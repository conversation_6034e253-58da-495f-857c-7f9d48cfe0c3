name: Refresh Worker - Deployment

on:
  push:
    branches: [master]
    paths:
      - infra/stacks/application/**/worker-service*.ts
      - infra/stacks/application/**/refresh-worker*.ts
      - infra/stacks/application/**/config.base.ts
      - infra/stacks/application/**/config.refresh-worker.ts
      - .github/workflows/refresh-worker-*.yml
      - infra/stacks/application/context/helpers/*
      - infra/stacks/application/enums.ts
      - api/carepatron.workers.refresh/**
  workflow_dispatch:

jobs:
  staging-diff:
    uses: Carepatron/github-shared-workflows/.github/workflows/cdk_diff_base.yml@master
    with:
        working-directory: ./infra/stacks/application
        stack-name: refresh-worker
        role-to-assume: arn:aws:iam::907270438193:role/github-actions-minimal-role
        aws-region: ap-southeast-2
        aws-environment: staging
        image-tag: ${{ github.sha }}
  production-diff:
    uses: Carepatron/github-shared-workflows/.github/workflows/cdk_diff_base.yml@master
    with:
        working-directory: ./infra/stacks/application
        stack-name: refresh-worker
        role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
        aws-region: ap-southeast-2
        aws-environment: production
        image-tag: ${{ github.sha }}
  staging:
      concurrency:
        group: ${{ github.workflow }}-staging
        cancel-in-progress: false
      needs:
          - staging-diff
          - production-diff
      uses: ./.github/workflows/cdk_build_deploy_base.yml
      with:
          aws-region: ap-southeast-2
          ecr-repository: carepatron-app/refresh-worker
          application-working-directory: ./api
          application-directory: carepatron.workers.refresh 
          cdk-working-directory: ./infra/stacks/application
          stack-name: refresh-worker
          ecr-role-to-assume: arn:aws:iam::907270438193:role/carepatron-app-ecr-deployment-role-staging
          deploy-role-to-assume: arn:aws:iam::907270438193:role/github-actions-minimal-role
          aws-environment: staging
          github-environment: Staging
          git-sha: ${{ github.sha }}
  production:
      needs:
          - staging
      uses: ./.github/workflows/cdk_publish_deploy_base.yml
      with: 
          staging-ecr-role-to-assume: arn:aws:iam::907270438193:role/carepatron-app-ecr-deployment-role-staging
          production-ecr-role-to-assume: arn:aws:iam::418502688622:role/carepatron-app-ecr-deployment-role-production
          deploy-role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
          staging-aws-region: ap-southeast-2
          production-aws-region: ap-southeast-2
          ecr-repository: carepatron-app/refresh-worker
          aws-environment: production
          working-directory: ./infra/stacks/application
          stack-name: refresh-worker
          aws-region: ap-southeast-2
          github-environment: Production
          image-tag: ${{ github.sha }}
        
