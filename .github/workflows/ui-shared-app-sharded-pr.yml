name: UI App - Shared Sharded Workflow

on:
  workflow_call:
    inputs:
      package_name:
        required: true
        type: string

jobs:
  checks:
    runs-on:
      group: "ci-runners-large"
    timeout-minutes: 15
    defaults:
      run:
        working-directory: ./ui
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - run: |
          git checkout -b master origin/master
          git checkout ${{ github.event.pull_request.head.sha }}

      - name: Cache Turbo Artifacts
        uses: actions/cache@v4
        with:
          path: ./ui/**/.turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: 📥 Monorepo install
        uses: ./.github/actions/yarn-install
        with:
          cwd: "./ui"

      - name: Checks
        run: yarn run ${{ inputs.package_name }}:check:ci

  test:
    needs: [checks]
    runs-on:
      group: "ci-runners-large"
    timeout-minutes: 15
    defaults:
      run:
        working-directory: ./ui

    strategy:
      fail-fast: false
      matrix:
        shard: [1, 2, 3, 4]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - run: |
          git checkout -b master origin/master
          git checkout ${{ github.event.pull_request.head.sha }}

      - name: Cache Turbo Artifacts
        uses: actions/cache@v4
        with:
          path: ./ui/**/.turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: 📥 Monorepo install
        uses: ./.github/actions/yarn-install
        with:
          cwd: "./ui"

      - name: Test Matrix
        env:
          FORCE_COLOR: "1"
        run: yarn workspace @carepatron/${{ inputs.package_name }} test:ci --shard=${{matrix.shard}}/4
