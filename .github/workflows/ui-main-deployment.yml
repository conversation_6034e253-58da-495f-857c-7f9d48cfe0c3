name: UI Main - Deployment

on:
  push:
    branches: [master]
    paths:
      - ui/packages/main/**
      - ui/packages/components/**
      - ui/packages/utilities/**
      - ui/packages/auth/**
      - ui/e2e/main/**
      - .github/workflows/ui-main-deployment.yml
  workflow_dispatch:

jobs:
  ui_deploy_staging:
    name: Deploy to staging
    environment: "Staging"
    runs-on: ubuntu-latest
    concurrency:
      group: ${{ github.workflow }}-staging
      cancel-in-progress: false
    defaults:
      run:
        working-directory: ./ui/packages/main
    permissions:
      id-token: write
      contents: write
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: 📥 Monorepo install
        uses: ./.github/actions/yarn-install
        with:
          cwd: "./ui"
      - name: Build staging
        run: CI=false yarn run build:main:staging
        env:
          REACT_APP__BUILD_NUMBER: ${{ github.RUN_ID }}.${{ github.RUN_NUMBER }}.${{ github.RUN_ATTEMPT }}
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Remove generated source maps
        run: |
          find ./build/static/js -type f -name "*.map" -delete

      - name: Main App Version Info
        run: cat ./build/version.json

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::907270438193:role/app-frontend-deployment-role
          role-session-name: DeploymentSession
      - name: Deploy to Staging
        working-directory: ui/packages/main/build
        run: aws s3 sync . s3://carepatron-staging-ui-assets --exclude 'index.html'
      - name: Deploy index to Staging
        working-directory: ui/packages/main/build
        run: aws s3 cp index.html s3://carepatron-staging-ui-assets

  e2e_staging:
    needs: ui_deploy_staging
    uses: ./.github/workflows/e2e_test_base.yml
    secrets:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  ui_deploy_production:
    name: Deploy to production
    environment: "Production"
    needs: e2e_staging
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./ui/packages/main
    permissions:
      id-token: write
      contents: write
      pull-requests: read
    steps:
      - uses: actions/checkout@v2

      - name: Increment Force Update Build Count
        id: force-update
        uses: carepatron/force-update-build-action@v1.2.1
        with:
          commit_sha: ${{ github.sha }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          owner: ${{ github.repository_owner }}
          repo: ${{ github.event.repository.name }}
          label: 'force-update'

      - uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: 📥 Monorepo install
        uses: ./.github/actions/yarn-install
        with:
          cwd: "./ui"
      - name: Build production
        run: CI=false yarn run build:main:production
        env:
          REACT_APP__BUILD_NUMBER: ${{ github.RUN_ID }}.${{ github.RUN_NUMBER }}.${{ github.RUN_ATTEMPT }}.${{ steps.force-update.outputs.force_update_build_count }}
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}

      - name: Remove generated source maps
        run: |
          find ./build/static/js -type f -name "*.map" -delete

      - name: Main App Version Info
        run: cat ./build/version.json

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::418502688622:role/app-frontend-deployment-role
          role-session-name: DeploymentSession
      - name: Deploy to production
        working-directory: ./ui/packages/main/build
        run: aws s3 sync . s3://carepatron-ui-production-assets --exclude 'index.html'
      - name: Deploy index to production
        working-directory: ./ui/packages/main/build
        run: aws s3 cp index.html s3://carepatron-ui-production-assets
