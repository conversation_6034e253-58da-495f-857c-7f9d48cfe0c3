name: 'CDK: Test carepatron-events'
on:
  pull_request:
    branches:
      - master
    paths:
      - infra/stacks/application/**/carepatron-events*.ts
      - infra/stacks/application/**/config.base.ts
      - infra/stacks/application/**/config.carepatron-events.ts
      - .github/workflows/cdk-carepatron-events-*.yml
jobs:
    run-cdk-tests:
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_test_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: carepatron-events
            role-to-assume: arn:aws:iam::907270438193:role/github-actions-minimal-role
            aws-region: ap-southeast-2
            aws-environment: staging