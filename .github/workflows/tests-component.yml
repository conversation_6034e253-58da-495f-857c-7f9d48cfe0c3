name: "Tests: All Components"

on:
  pull_request:
    branches:
      - master
    paths:
      - .github/workflows/api-deployment.yml
      - api/**
      - '!api/Notifications/**'
      - build/bootstrap/**
      - images/bootstrap/**
      - .github/workflows/tests-component.yml
      - api/Insurance/**
      - api/Messagings/**
      - api/carepatron.core/**
      - api/carepatron.infra.sql/**
      - api/carepatron.infra.sqs/**
      - api/carepatron.infra.telemetry/**
      - api/carepatron.workers/common/**
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  api-build:
    runs-on:  
      group: "ci-runners-large"
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref || github.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name || github.repository }}
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: 8.0.x
      - name: Setup FFmpeg
        uses: ./.github/workflows/setup-ffmpeg
      - name: Restore dependencies
        working-directory: ./api
        run: dotnet restore
      - name: Build
        working-directory: ./api
        run: dotnet build --no-restore --nologo -v q --property WarningLevel=0 /clp:ErrorsOnly
      - name: Unit Tests - Core
        working-directory: ./api
        run: dotnet test --no-build --verbosity normal --filter "(FullyQualifiedName!~tests.component) & (FullyQualifiedName!~Transcriptions.Worker.Tests) & (FullyQualifiedName!~ClaimMonitor.Worker.Tests) & (FullyQualifiedName!~PayerReplicator.Worker.Tests) & (FullyQualifiedName!~TemplateImporter.Worker.Tests) & (FullyQualifiedName!~Billing.Queue.Worker.Tests)" --logger trx --results-directory ./TestResults
      - name: Process trx reports with default
        if: always()
        uses: EnricoMi/publish-unit-test-result-action@v2   
        with:
          check_name: Unit Tests - Core
          files: './api/TestResults/*.trx'
          comment_mode: always
          github_token: ${{ secrets.GITHUB_TOKEN }}
          report_individual_runs: false
          ignore_runs: true

      - name: Upload build artifacts
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: api-build-${{ github.run_id }}
          path: |
            api/**/bin/**
            api/**/*.csproj
            api/obj/project.assets.json
            api/**/obj/project.assets.json
            !api/**/bin/**/ref/**
            !api/**/bin/**/runtimes/**
          retention-days: 1
          compression-level: 1

  component-tests:
    needs: api-build
    runs-on:
      group: "test-runners-large"
    timeout-minutes: 45
    env:
      ASPNETCORE_ENVIRONMENT: Development
      AWS_ACCESS_KEY_ID: AKIAIOSFODNN7EXAMPLE
      AWS_SECRET_ACCESS_KEY: wJalrXUtnFEMIGK7MDENGGbPxRfiCYEXAMPLEKEY
    defaults:
      run:
        working-directory: ./
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref || github.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name || github.repository }}

      - name: API Build and Setup
        uses: ./.github/workflows/api-setup-build
        with:
          enable-docker-cache: true
          install-ffmpeg: true
          setup-database: true
          use-build-artifacts: true
          build-artifact-name: api-build-${{ github.run_id }}

      - name: Run Component Tests
        working-directory: ./api/tests/tests.component
        run: dotnet test --no-build tests.component.csproj --logger trx --results-directory ../TestResults

      - name: Process trx reports
        if: always()
        uses: EnricoMi/publish-unit-test-result-action@v2
        with:
          check_name: Component Tests
          files: "**/TestResults/*.trx"
          comment_mode: always
          github_token: ${{ secrets.GITHUB_TOKEN }}
          report_individual_runs: false
          ignore_runs: true

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-component-${{ github.run_id }}
          path: "**/TestResults/*.trx"
          retention-days: 1

  worker-tests:
    needs: api-build
    runs-on:
      group: "test-runners-large"
    timeout-minutes: 60
    env:
      ASPNETCORE_ENVIRONMENT: Development
      AWS_ACCESS_KEY_ID: AKIAIOSFODNN7EXAMPLE
      AWS_SECRET_ACCESS_KEY: wJalrXUtnFEMIGK7MDENGGbPxRfiCYEXAMPLEKEY
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref || github.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name || github.repository }}

      - name: API Build and Setup
        uses: ./.github/workflows/api-setup-build
        with:
          enable-docker-cache: true
          install-ffmpeg: true
          setup-database: true
          use-build-artifacts: true
          build-artifact-name: api-build-${{ github.run_id }}

      # Queue Worker Tests
      - name: Run Queue Worker Tests
        working-directory: ./api/tests/tests.component.worker.queues
        run: dotnet test --no-build tests.component.worker.queues.csproj --logger trx --results-directory ../TestResults/QueueWorker

      # Transcription Worker Tests
      - name: Run Transcription Worker Tests
        working-directory: ./api/Documentation/Transcriptions.Worker.Tests
        run: dotnet test --no-build Transcriptions.Worker.Tests.csproj --logger trx --results-directory ../TestResults/TranscriptionWorker

      # Worker Tests
      - name: Run Worker Tests
        working-directory: ./api/tests/tests.component.worker
        run: dotnet test --no-build tests.component.worker.csproj --logger trx --results-directory ../TestResults/Worker

      # Dispatcher Tests
      - name: Run Dispatcher Tests
        working-directory: ./api/tests/tests.component.worker.job.dispatcher
        run: dotnet test --no-build tests.component.worker.job.dispatcher.csproj --logger trx --results-directory ../TestResults/Dispatcher

      # Worker Refresh Tests
      - name: Run Worker Refresh Tests
        working-directory: ./api/tests/tests.component.worker.refresh
        run: dotnet test --no-build tests.component.worker.refresh.csproj --logger trx --results-directory ../TestResults/WorkerRefresh

      # Payer Replicator Tests
      - name: Run Payer Replicator Tests
        working-directory: ./api/Insurance/Tests/PayerReplicator.Worker.Tests
        run: dotnet test --no-build PayerReplicator.Worker.Tests.csproj --logger trx --results-directory ../TestResults/PayerReplicator

      # Claim Monitor Tests
      - name: Run Claim Monitor Tests
        working-directory: ./api/Insurance/Tests/ClaimMonitor.Worker.Tests
        run: dotnet test --no-build ClaimMonitor.Worker.Tests.csproj --logger trx --results-directory ../TestResults/ClaimMonitor

      # Billing Queue Worker Tests
      - name: Run Billing Queue Worker Tests
        working-directory: ./api/Insurance/Tests/Billing.Queue.Worker.Tests
        run: dotnet test --no-build Billing.Queue.Worker.Tests.csproj --logger trx --results-directory ../TestResults/BillingQueueWorker

      - name: Process trx reports
        if: always()
        uses: EnricoMi/publish-unit-test-result-action@v2
        with:
          check_name: Worker Tests - All Suites
          files: "**/TestResults/**/*.trx"
          comment_mode: always
          github_token: ${{ secrets.GITHUB_TOKEN }}
          report_individual_runs: false
          ignore_runs: true

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-worker-${{ github.run_id }}
          path: "**/TestResults/**/*.trx"
          retention-days: 1

  test-report:
    needs: [component-tests, worker-tests]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Download all test results
        uses: actions/download-artifact@v4
        with:
          pattern: test-results-*-${{ github.run_id }}
          merge-multiple: true

      - name: Process consolidated test reports
        uses: EnricoMi/publish-unit-test-result-action@v2
        with:
          check_name: All Component Tests - Consolidated Report
          files: "**/*.trx"
          comment_mode: always
          github_token: ${{ secrets.GITHUB_TOKEN }}
          report_individual_runs: true
          ignore_runs: false
          check_run_annotations: all tests, skipped tests
