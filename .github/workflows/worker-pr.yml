name: Worker - Pull Request

on:
  pull_request:
    branches: [master]
    paths:
      - infra/stacks/application/**/worker-service*.ts
      - infra/stacks/application/**/reminder-worker*.ts
      - infra/stacks/application/**/config.base.ts
      - infra/stacks/application/**/config.reminder-worker.ts
      - .github/workflows/cdk-reminder-worker-*.yml
      - infra/stacks/application/context/helpers/*
      - infra/stacks/application/enums.ts
      - api/carepatron.worker/**
  workflow_dispatch:
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true
jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: 8.0.x
      - name: Setup FFmpeg
        uses: ./.github/workflows/setup-ffmpeg
      - name: Restore dependencies
        working-directory: ./api/carepatron.worker
        run: dotnet restore
      - name: Build
        working-directory: ./api/carepatron.worker
        run: dotnet build --no-restore
      - name: Run Unit Tests
        working-directory: ./api
        run: dotnet test --verbosity normal --filter "FullyQualifiedName!~tests.component&!Transcriptions.Worker.Tests" --logger trx
      - name: Process trx reports with default
        if: always()
        uses: im-open/process-dotnet-test-results@v3.0.0
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
  run-cdk-tests:
    uses: Carepatron/github-shared-workflows/.github/workflows/cdk_test_base.yml@master
    with:
      working-directory: ./infra/stacks/application
      stack-name: "reminder-worker worker-service"
      role-to-assume: arn:aws:iam::907270438193:role/github-actions-minimal-role
      aws-region: ap-southeast-2
      aws-environment: staging
