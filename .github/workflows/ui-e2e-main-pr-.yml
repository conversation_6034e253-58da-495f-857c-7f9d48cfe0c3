name: UI E2E Main - Pull Request

on:
  pull_request:
    branches: [master]
    paths:
      - "ui/e2e/main/**"
  workflow_dispatch:
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true
jobs:
  checks:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    defaults:
      run:
        working-directory: ./ui
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - run: |
          git checkout -b master origin/master
          git checkout ${{ github.event.pull_request.head.sha }}

      - name: Cache Turbo Artifacts
        uses: actions/cache@v4
        with:
          path: ./ui/**/.turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: 📥 Monorepo install
        uses: ./.github/actions/yarn-install
        with:
          cwd: "./ui"

      - name: Checks
        run: yarn run e2e-main:check:ci
