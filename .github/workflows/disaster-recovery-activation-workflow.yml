name: Disaster Recovery Activation Workflow
description: |
  This workflow is designed to activate the disaster recovery (DR) environment in the Ohio region.
on:
  workflow_dispatch:

jobs:

  diff-elasticache:
    uses: Carepatron/github-shared-workflows/.github/workflows/cdk_diff_base.yml@master
    with:
      working-directory: ./infra/stacks/application
      stack-name: --app 'npx ts-node --prefer-ts-exts bin/ohio-stacks.ts' elasticache
      role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
      aws-region: us-east-2
      aws-environment: ohio
      image-tag: latest

  diff-database:
    uses: Carepatron/github-shared-workflows/.github/workflows/cdk_diff_base.yml@master
    with:
      working-directory: ./infra/stacks/application
      stack-name: --app 'npx ts-node --prefer-ts-exts bin/ohio-stacks.ts' database
      role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
      aws-region: us-east-2
      aws-environment: ohio
      image-tag: latest

  diff-alb:
    uses: Carepatron/github-shared-workflows/.github/workflows/cdk_diff_base.yml@master
    with:
      working-directory: ./infra/stacks/application
      stack-name: --app 'npx ts-node --prefer-ts-exts bin/ohio-stacks.ts' ecs-load-balancer
      role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
      aws-region: us-east-2
      aws-environment: ohio
      image-tag: latest
  
  diff-api:
    uses: Carepatron/github-shared-workflows/.github/workflows/cdk_diff_base.yml@master
    with:
      working-directory: ./infra/stacks/application
      stack-name: --app 'npx ts-node --prefer-ts-exts bin/ohio-stacks.ts' monolith-api
      role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
      aws-region: us-east-2
      aws-environment: ohio
      image-tag: latest

  elasticache:
    name: deploy-elasticache
    needs: 
      - diff-alb
      - diff-api
      - diff-database
      - diff-elasticache
    runs-on: ubuntu-latest
    environment: Disaster-Recovery
    permissions:
      actions: write
      contents: read
      id-token: write
    env:
      AWS_ENVIRONMENT: ohio
      IMAGE_TAG: latest
    steps:
      - uses: actions/setup-node@v4
        with:
            node-version: 20
      - name: Configure Staging AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
            role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
            aws-region: us-east-2
      - name: Checkout rep
        uses: actions/checkout@v4
        with:
          ref: master
          repository: Carepatron/Carepatron-App
      - run: npm install -g aws-cdk
      - run: npm i
        working-directory: ./infra/stacks/application
      - name: Check creds
        run: aws sts get-caller-identity
      - name: Deploy
        working-directory: ./infra/stacks/application
        run: cdk deploy --app 'npx ts-node --prefer-ts-exts bin/ohio-stacks.ts' database --require-approval never

  database:
    name: deploy-database
    needs: 
      - diff-alb
      - diff-api
      - diff-database
      - diff-elasticache
    runs-on: ubuntu-latest
    environment: Disaster-Recovery
    permissions:
      actions: write
      contents: read
      id-token: write
    env:
      AWS_ENVIRONMENT: ohio
      IMAGE_TAG: latest
    steps:
      - uses: actions/setup-node@v4
        with:
            node-version: 20
      - name: Configure Staging AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
            role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
            aws-region: us-east-2
      - name: Checkout rep
        uses: actions/checkout@v4
        with:
          ref: master
          repository: Carepatron/Carepatron-App
      - run: npm install -g aws-cdk
      - run: npm i
        working-directory: ./infra/stacks/application
      - name: Check creds
        run: aws sts get-caller-identity
      - name: Deploy
        working-directory: ./infra/stacks/application
        run: cdk deploy --app 'npx ts-node --prefer-ts-exts bin/ohio-stacks.ts' database --require-approval never

  alb:
    name: deploy-alb
    needs: 
      - diff-alb
      - diff-api
      - diff-database
      - diff-elasticache
    runs-on: ubuntu-latest
    environment: Disaster-Recovery
    permissions:
      actions: write
      contents: read
      id-token: write
    env:
      AWS_ENVIRONMENT: ohio
      IMAGE_TAG: latest
    steps:
      - uses: actions/setup-node@v4
        with:
            node-version: 20
      - name: Configure Staging AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
            role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
            aws-region: us-east-2
      - name: Checkout rep
        uses: actions/checkout@v4
        with:
          ref: master
          repository: Carepatron/Carepatron-App
      - run: npm install -g aws-cdk
      - run: npm i
        working-directory: ./infra/stacks/application
      - name: Check creds
        run: aws sts get-caller-identity
      - name: Deploy
        working-directory: ./infra/stacks/application
        run: cdk deploy --app 'npx ts-node --prefer-ts-exts bin/ohio-stacks.ts' ecs-load-balancer --require-approval never
  
  api:
    needs: 
      - alb
      - database
      - elasticache
    name: deploy-api
    runs-on: ubuntu-latest
    environment: Disaster-Recovery
    permissions:
      actions: write
      contents: read
      id-token: write
    env:
      AWS_ENVIRONMENT: ohio
      IMAGE_TAG: latest
    steps:
      - uses: actions/setup-node@v4
        with:
            node-version: 20
      - name: Configure Staging AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
            role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
            aws-region: us-east-2
      - name: Checkout rep
        uses: actions/checkout@v4
        with:
          ref: master
          repository: Carepatron/Carepatron-App
      - run: npm install -g aws-cdk
      - run: npm i
        working-directory: ./infra/stacks/application
      - name: Check creds
        run: aws sts get-caller-identity
      - name: Deploy
        working-directory: ./infra/stacks/application
        run: cdk deploy --app 'npx ts-node --prefer-ts-exts bin/ohio-stacks.ts' monolith-api --require-approval never
