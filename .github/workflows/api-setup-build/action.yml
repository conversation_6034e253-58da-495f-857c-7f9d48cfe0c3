name: "Reusable API Build and Setup"
description: "Composite action for setting up API build environment with optional database setup"

inputs:
  enable-docker-cache:
    description: 'Enable Docker image caching'
    required: false
    default: 'true'
  install-ffmpeg:
    description: 'Install ffmpeg'
    required: false
    default: 'true'
  setup-database:
    description: 'Setup database with migrations and seed data'
    required: false
    default: 'false'
  use-build-artifacts:
    description: 'Use build artifacts instead of building from source'
    required: false
    default: 'false'
  build-artifact-name:
    description: 'Name of the build artifact to download'
    required: false
    default: ''

outputs:
  build-success:
    description: "API build success status"
    value: ${{ steps.build-api.outcome || steps.build-api-artifacts.outcome }}
  api-migrations-success:
    description: "API migrations success status"
    value: ${{ steps.api-migrations.outcome || steps.api-migrations-artifacts.outcome }}
  notification-migrations-success:
    description: "Notification migrations success status"
    value: ${{ steps.notification-migrations.outcome || steps.notification-migrations-artifacts.outcome }}

runs:
  using: "composite"
  steps:
    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: "8.x"
    
    - name: Setup FFmpeg
      if: inputs.install-ffmpeg == 'true'
      uses: ./.github/workflows/setup-ffmpeg

    - name: Install localhost cert
      run: sudo dotnet dev-certs https --trust --export-path /usr/local/share/ca-certificates/localhost.crt --no-password --format PEM
      shell: bash

    - name: Update certs
      run: sudo update-ca-certificates
      shell: bash

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
        
    - name: Cache Docker images
      if: inputs.enable-docker-cache == 'true'
      uses: actions/cache@v4
      with:
        path: /tmp/.docker-cache
        key: ${{ runner.os }}-docker-${{ hashFiles('build/bootstrap/docker-compose.yml', 'images/bootstrap/Dockerfile') }}
        restore-keys: |
          ${{ runner.os }}-docker-${{ hashFiles('build/bootstrap/docker-compose.yml', 'images/bootstrap/Dockerfile') }}

    - name: Load Docker cache
      if: inputs.enable-docker-cache == 'true'
      id: load-cache
      run: |
        if [ -f /tmp/.docker-cache/images.tar ]; then
          echo "Loading Docker images from cache..."
          docker load -i /tmp/.docker-cache/images.tar
          echo "Cache loaded successfully"
          docker images
          echo "cache-hit=true" >> $GITHUB_OUTPUT
        else
          echo "No Docker cache found"
          echo "cache-hit=false" >> $GITHUB_OUTPUT
        fi
      shell: bash

    - name: Build bootstrap image (cache miss)
      if: inputs.enable-docker-cache == 'true' && steps.load-cache.outputs.cache-hit == 'false'
      working-directory: ./build/bootstrap
      run: docker compose build bootstrap
      shell: bash
      env:
        COMPOSE_BAKE: true

    - name: Build bootstrap image (no cache)
      if: inputs.enable-docker-cache != 'true'
      working-directory: ./build/bootstrap
      run: docker compose build bootstrap
      shell: bash
      env:
        COMPOSE_BAKE: true

    - name: Bootstrap services
      working-directory: ./build/bootstrap
      run: docker compose up bootstrap
      shell: bash

    - name: Save Docker cache
      if: inputs.enable-docker-cache == 'true' && steps.load-cache.outputs.cache-hit == 'false'
      run: |
        mkdir -p /tmp/.docker-cache
        # Get list of images to save
        IMAGES=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "(bootstrap)" | head -10)
        if [ ! -z "$IMAGES" ]; then
          echo "Saving images: $IMAGES"
          docker save -o /tmp/.docker-cache/images.tar $IMAGES
          echo "Cache saved successfully"
        else
          echo "No images to cache"
        fi
      shell: bash
    - name: Download build artifacts
      if: inputs.use-build-artifacts == 'true'
      uses: actions/download-artifact@v4
      with:
        name: ${{ inputs.build-artifact-name }}
        path: ./api

    - name: Set build success (artifacts)
      id: build-api-artifacts
      if: inputs.use-build-artifacts == 'true'
      run: |
        echo "Using pre-built artifacts - no build required"
        echo "Artifacts downloaded successfully"
      shell: bash

    - name: Restore dependencies (from source)
      if: inputs.use-build-artifacts != 'true'
      working-directory: ./api
      run: dotnet restore
      shell: bash

    - name: Build from source
      id: build-api
      if: inputs.use-build-artifacts != 'true'
      working-directory: ./api
      run: dotnet build --no-restore --nologo -v q --property WarningLevel=0 /clp:ErrorsOnly
      shell: bash

    # Database setup section (conditional)
    - name: Install dotnet-ef tool
      if: inputs.setup-database == 'true'
      run: dotnet tool install --global dotnet-ef --version 8.0.1
      shell: bash

    - name: Wait for PostgreSQL to be ready
      if: inputs.setup-database == 'true'
      run: |
        echo "Waiting for PostgreSQL to be ready..."
        until docker exec carepatron-test-database pg_isready -U postgres -d local; do
          echo "PostgreSQL is not ready yet, waiting 5 seconds..."
          sleep 5
        done
        echo "PostgreSQL is ready!"
      shell: bash

    - name: Execute create database extensions
      if: inputs.setup-database == 'true'
      run: |
        echo "Executing database extensions..."
        docker exec -i carepatron-test-database psql -U postgres -d local -f /dev/stdin < ./api/carepatron.infra.sqlmigrations/Extensions/CreateExtensions.sql
      shell: bash
        
    - name: Run api migrations
      if: inputs.setup-database == 'true' && inputs.use-build-artifacts != 'true' && steps.build-api.outcome == 'success'
      id: api-migrations
      working-directory: ./api/carepatron.infra.sql
      run: dotnet ef database update --no-build -s ../carepatron.api --connection "User ID=postgres;Password=password;Server=localhost;Port=5434;Database=local;Pooling=true;Include Error Detail=true;"
      shell: bash

    - name: Run api migrations (with artifacts)
      if: inputs.setup-database == 'true' && inputs.use-build-artifacts == 'true'
      id: api-migrations-artifacts
      working-directory: ./api/carepatron.infra.sql
      run: |
        echo "Running migrations with pre-built artifacts..."
        dotnet ef database update --no-build -s ../carepatron.api --connection "User ID=postgres;Password=password;Server=localhost;Port=5434;Database=local;Pooling=true;Include Error Detail=true;"
      shell: bash

    - name: Run notification migrations
      if: inputs.setup-database == 'true' && inputs.use-build-artifacts != 'true' && steps.build-api.outcome == 'success'
      id: notification-migrations
      working-directory: ./api/Notifications/Notifications.Module
      run: dotnet ef database update --no-build -s ../Notifications.Api --connection "User ID=postgres;Password=password;Server=localhost;Port=5434;Database=local;Pooling=true;Include Error Detail=true;"
      shell: bash

    - name: Run notification migrations (with artifacts)
      if: inputs.setup-database == 'true' && inputs.use-build-artifacts == 'true'
      id: notification-migrations-artifacts
      working-directory: ./api/Notifications/Notifications.Module
      run: |
        echo "Running notification migrations with pre-built artifacts..."
        dotnet ef database update --no-build -s ../Notifications.Api --connection "User ID=postgres;Password=password;Server=localhost;Port=5434;Database=local;Pooling=true;Include Error Detail=true;"
      shell: bash
    
    - name: Execute remaining seed SQL files
      if: inputs.setup-database == 'true'
      run: |
        echo "Executing security SQL files (users)..."
        docker exec -i carepatron-test-database psql -U postgres -d local -f /dev/stdin < ./api/carepatron.infra.sqlmigrations/Security/Users.sql

        echo "Executing one-off scripts..."
        docker exec -i carepatron-test-database psql -U postgres -d local -f /dev/stdin < ./api/carepatron.infra.sqlmigrations/OneOffScripts/ICD_Codes_2024-25.sql
        
        echo "Executing seed data files..."
        for file in $(ls -1v ./api/carepatron.infra.sqlmigrations/SeedData/*.sql); do
          echo "Executing $file..."
          docker exec -i carepatron-test-database psql -U postgres -d local -f /dev/stdin < "$file"
        done
        
        echo "Executing test data files..."
        docker exec -i carepatron-test-database psql -U postgres -d local -f /dev/stdin < ./build/local/populate-data.sql
        
        echo "All remaining seed SQL files executed successfully."
      shell: bash
