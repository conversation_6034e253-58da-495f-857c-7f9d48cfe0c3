name: Deploy
on:
  workflow_call:
    inputs:
      staging-ecr-role-to-assume:
        required: true
        type: string
      production-ecr-role-to-assume:
        required: true
        type: string
      staging-aws-region:
        required: true
        type: string
      production-aws-region:
        required: true
        type: string
      ecr-repository:
        required: true
        type: string
      aws-environment:
        description: 'AWS environment to deploy to'
        required: true
        type: string
      image-tag: 
        description: 'Image tag to deploy'
        required: false
        type: string
      working-directory:
        description: 'Working directory of the cdk stack'
        required: true
        type: string
      stack-name:
        description: 'CDK stack name'
        required: true
        type: string
      deploy-role-to-assume:
        description: 'AWS deployment role ARN'
        required: true
        type: string
      aws-region:
        description: 'AWS region to deploy to'
        required: true
        type: string
      github-environment:
        description: 'Github deployment environment'
        required: false
        type: string
      npm-install-directory:
        description: 'Directory to run npm install relative to the working directory path'
        required: false
        type: string
jobs:
  deploy:
    name: deploy
    runs-on: ubuntu-latest
    environment: ${{ inputs.github-environment }}
    permissions:
      actions: write
      contents: read
      id-token: write
    env:
      AWS_ENVIRONMENT: ${{ inputs.aws-environment }}
    steps:
      - uses: actions/setup-node@v4
        with:
            node-version: 20
      # Pull from Staging
      - name: Configure Staging AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
            role-to-assume: ${{ inputs.staging-ecr-role-to-assume }}
            aws-region: ${{ inputs.staging-aws-region }}
      - name: Login to Staging Amazon ECR
        id: login-ecr-staging
        uses: aws-actions/amazon-ecr-login@v2
      - name: Pull from Staging ECR
        id: pull-image
        env:
            ECR_REGISTRY: ${{ steps.login-ecr-staging.outputs.registry }}
            ECR_REPOSITORY: ${{ inputs.ecr-repository }}
            IMAGE_TAG: ${{ inputs.image-tag }}
        run: |
            docker pull $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
      # Tag and Push to Production
      - name: Configure Production AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
            role-to-assume: ${{ inputs.production-ecr-role-to-assume }}
            aws-region: ${{ inputs.production-aws-region }}
      - name: Login to Production Amazon ECR
        id: login-ecr-production
        uses: aws-actions/amazon-ecr-login@v2
      - name: Push to Production ECR
        id: push-image
        env:
            STAGING_ECR_REGISTRY: ${{ steps.login-ecr-staging.outputs.registry }}
            PRODUCTION_ECR_REGISTRY: ${{ steps.login-ecr-production.outputs.registry }}
            ECR_REPOSITORY: ${{ inputs.ecr-repository }}
            IMAGE_TAG: ${{ inputs.image-tag }}
        run: |
            docker image tag $STAGING_ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $PRODUCTION_ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
            docker image tag $STAGING_ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $PRODUCTION_ECR_REGISTRY/$ECR_REPOSITORY:latest
            docker image push $PRODUCTION_ECR_REGISTRY/$ECR_REPOSITORY --all-tags  
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ inputs.deploy-role-to-assume }}
          aws-region: ${{ inputs.aws-region }}
      - name: Checkout rep
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.push.head.ref }}
          repository: ${{ github.event.push.head.repo.full_name }}
      - name: Set IMAGE_TAG if provided
        if: ${{ inputs.image-tag }}
        run: echo "IMAGE_TAG=${{ inputs.image-tag }}" >> $GITHUB_ENV
      - run: npm install -g aws-cdk
      - run: npm i
        working-directory: ${{ inputs.working-directory }}
      - name: npm install specified directory
        if: inputs.npm-install-directory != ''
        run: npm install -C ${{ inputs.npm-install-directory }}
      - name: Check creds
        run: aws sts get-caller-identity
      - name: Deploy ${{ inputs.stack-name }}
        working-directory: ${{ inputs.working-directory }}
        run: cdk deploy ${{ inputs.stack-name }} --require-approval never