name: 'CDK: Test tasks-queue'
on:
  pull_request:
    branches:
      - master
    paths:
      - infra/stacks/application/**/tasks-queue*.ts
      - infra/stacks/application/**/config.base.ts
      - infra/stacks/application/**/config.tasks-queue.ts
      - .github/workflows/cdk-tasks-queue-*.yml
jobs:
    run-cdk-tests:
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_test_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: tasks-queue
            role-to-assume: arn:aws:iam::907270438193:role/github-actions-minimal-role
            aws-region: ap-southeast-2
            aws-environment: staging