name: 'CDK: Test api-events'
on:
  pull_request:
    branches:
      - master
    paths:
      - infra/stacks/application/**/api-events*.ts
      - infra/stacks/application/**/config.base.ts
      - infra/stacks/application/**/config.api-events.ts
      - .github/workflows/cdk-api-events-*.yml
jobs:
    run-cdk-tests:
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_test_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: api-events
            role-to-assume: arn:aws:iam::907270438193:role/github-actions-minimal-role
            aws-region: ap-southeast-2
            aws-environment: staging