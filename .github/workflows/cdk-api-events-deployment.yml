name: 'CDK: Deploy api-events'
on:
  push:
    branches:
      - master
    paths:
      - infra/stacks/application/**/api-events*.ts
      - infra/stacks/application/**/config.base.ts
      - infra/stacks/application/**/config.api-events.ts
      - .github/workflows/cdk-api-events-*.yml
jobs:
    staging-diff:
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_diff_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: api-events
            role-to-assume: arn:aws:iam::907270438193:role/github-actions-minimal-role
            aws-region: ap-southeast-2
            aws-environment: staging
            image-tag: ${{ github.sha }}
    production-diff:
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_diff_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: api-events
            role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
            aws-region: ap-southeast-2
            aws-environment: production
            image-tag: ${{ github.sha }}
    dr-diff:
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_diff_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: --app 'npx ts-node --prefer-ts-exts bin/ohio-stacks.ts' api-events
            role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
            aws-region: us-east-2
            aws-environment: ohio
            image-tag: ${{ github.sha }}
    staging-deployment:
        needs: 
            - staging-diff
            - production-diff
            - dr-diff
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_deploy_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: api-events
            role-to-assume: arn:aws:iam::907270438193:role/github-actions-minimal-role
            aws-region: ap-southeast-2
            aws-environment: staging
            github-environment: Infra-Staging
            image-tag: ${{ github.sha }}
    production-deployment:
        needs: 
            - staging-deployment
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_deploy_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: api-events
            role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
            aws-region: ap-southeast-2
            aws-environment: production
            github-environment: Infra-Production
            image-tag: ${{ github.sha }}
    dr-deployment:
        needs: 
            - production-deployment
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_deploy_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: --app 'npx ts-node --prefer-ts-exts bin/ohio-stacks.ts' api-events
            role-to-assume: arn:aws:iam::418502688622:role/github-actions-minimal-role
            aws-region: us-east-2
            aws-environment: ohio
            github-environment: Infra-Production
            image-tag: ${{ github.sha }}