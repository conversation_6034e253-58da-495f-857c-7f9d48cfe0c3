name: 'CDK: Test documentation-transcriptions-queue'
on:
  pull_request:
    branches:
      - master
    paths:
      - infra/stacks/application/**/documentation-transcriptions-queue*.ts
      - infra/stacks/application/**/config.base.ts
      - infra/stacks/application/**/config.documentation-transcriptions-queue.ts
      - .github/workflows/cdk-documentation-transcriptions-queue-*.yml
jobs:
    run-cdk-tests:
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_test_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: documentation-transcriptions-queue
            role-to-assume: arn:aws:iam::907270438193:role/github-actions-minimal-role
            aws-region: ap-southeast-2
            aws-environment: staging