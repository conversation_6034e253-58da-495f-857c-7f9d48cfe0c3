name: UI Package - Shared Workflow

on:
  workflow_call:
    inputs:
      package_name:
        required: true
        type: string

jobs:
  test:
    runs-on:
      group: "ci-runners-large"
    timeout-minutes: 45
    env:
      PACKAGE_NAME: ${{ inputs.package_name }}
    defaults:
      run:
        working-directory: ./ui
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - run: |
          git checkout -b master origin/master
          git checkout ${{ github.event.pull_request.head.sha }}

      - name: Cache Turbo Artifacts
        uses: actions/cache@v4
        with:
          path: ./ui/**/.turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: 📥 Monorepo install
        uses: ./.github/actions/yarn-install
        with:
          cwd: "./ui"

      - name: Checks
        run: yarn run ${{ inputs.package_name }}:check:ci

      - name: Test Package
        env:
          FORCE_COLOR: "1"
        run: yarn package:test:ci
