name: "Setup FFmpeg Static Build"
description: "Composite action for installing FFmpeg static build with caching"

inputs:
  cache-version:
    description: 'Cache version for ffmpeg (increment to invalidate cache)'
    required: false
    default: 'v1'

outputs:
  ffmpeg-installed:
    description: "FFmpeg installation success status"
    value: ${{ steps.install-ffmpeg.outcome }}
  cache-hit:
    description: "Whether ffmpeg was restored from cache"
    value: ${{ steps.cache-ffmpeg.outputs.cache-hit }}

runs:
  using: "composite"
  steps:
    - name: Cache ffmpeg static build
      id: cache-ffmpeg
      uses: actions/cache@v4
      with:
        path: /tmp/ffmpeg-static
        key: ffmpeg-static-amd64-${{ runner.os }}-${{ inputs.cache-version }}
        restore-keys: |
          ffmpeg-static-amd64-${{ runner.os }}-

    - name: Download ffmpeg static build
      if: steps.cache-ffmpeg.outputs.cache-hit != 'true'
      run: |
        echo "Downloading ffmpeg static build..."
        mkdir -p /tmp/ffmpeg-static
        cd /tmp/ffmpeg-static
        wget --secure-protocol=TLSv1 -O ffmpeg.tar.xz https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz
        tar -xf ffmpeg.tar.xz
        # Move binaries to a predictable location
        cp ffmpeg-*-amd64-static/ffmpeg ./ffmpeg
        cp ffmpeg-*-amd64-static/ffprobe ./ffprobe
        chmod +x ./ffmpeg ./ffprobe
        echo "ffmpeg static build downloaded and cached"
      shell: bash

    - name: Install ffmpeg from cache
      id: install-ffmpeg
      run: |
        echo "Installing ffmpeg from cache..."
        sudo cp /tmp/ffmpeg-static/ffmpeg /usr/local/bin/
        sudo cp /tmp/ffmpeg-static/ffprobe /usr/local/bin/
        sudo chmod +x /usr/local/bin/ffmpeg /usr/local/bin/ffprobe
        ffmpeg -version
        echo "ffmpeg installed successfully from cache"
      shell: bash