name: "Notifications Tests: component"

on:
  pull_request:
    branches:
      - master
    paths:
      - .github/workflows/notifications-tests-component.yml
      - api/Notifications/**
      - api/Shared/**
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
jobs:
  component-tests:
    runs-on:
      group: "test-runners-large"
    timeout-minutes: 30
    env:
      ASPNETCORE_ENVIRONMENT: Development
      AWS_ACCESS_KEY_ID: AKIAIOSFODNN7EXAMPLE
      AWS_SECRET_ACCESS_KEY: wJalrXUtnFEMIGK7MDENGGbPxRfiCYEXAMPLEKEY
    defaults:
      run:
        working-directory: ./
    steps:
      - name: Checkout git
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{ github.event.pull_request.head.repo.full_name }}

      - name: API Build and Setup
        uses: ./.github/workflows/api-setup-build
        with:
          enable-docker-cache: true
          install-ffmpeg: false
          setup-database: true

      - name: Run notifications tests
        working-directory: ./api/Notifications/Notifications.Test.Integration
        run: dotnet test --no-build Notifications.Tests.Component.csproj --logger trx

      - name: Run notifications worker tests
        working-directory: ./api/Notifications/Notifications.Tests.Worker.Queue
        run: dotnet test --no-build Notifications.Tests.Worker.Queue.csproj --logger trx

      - name: Display test results
        if: always()
        uses: im-open/process-dotnet-test-results@v3.0.0
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
