name: 'CDK: Test tasks-dead-letter-queue'
on:
  pull_request:
    branches:
      - master
    paths:
      - infra/stacks/application/**/tasks-dead-letter-queue*.ts
      - infra/stacks/application/**/config.base.ts
      - infra/stacks/application/**/config.tasks-dead-letter-queue.ts
      - .github/workflows/cdk-tasks-dead-letter-queue-*.yml
jobs:
    run-cdk-tests:
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_test_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: tasks-dead-letter-queue
            role-to-assume: arn:aws:iam::907270438193:role/github-actions-minimal-role
            aws-region: ap-southeast-2
            aws-environment: staging