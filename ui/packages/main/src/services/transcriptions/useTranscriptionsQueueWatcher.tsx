import { useEffect, useMemo, useRef } from 'react';

import { logger } from '@carepatron/utilities';

import api from 'services/api';
import { useAppSelector } from 'store';
import { useLazyGetNoteTranscriptionsQuery } from 'store/api/notes/hooks';
import { selectCurrentProviderId } from 'store/api/providers/selectors';
import { useCompleteTranscriptionPartMutation } from 'store/api/transcriptions/hooks';
import { useFeatureFlag } from 'store/slices/features/hooks';

import { logQueueStatus, performComprehensiveMonitoring } from './queueMonitoring';
import { getTabSessionId, startTabCoordination, isTabAlive } from './sessionUtils';
import TranscriptionsQueue, { TranscriptionPartQueueMessage } from './transcriptionsQueue';
import { TranscriptionStateManager } from './transcriptionStateManager';

// Log category for all transcription queue related logs
const LOG_CATEGORY = 'ai-transcription-queue';

// Queue processing interval (ms)
const QUEUE_PROCESSING_INTERVAL = 3000;

// Orphan adoption check interval (ms) - check for orphaned items every 2 minutes
const RESUMPTION_CHECK_INTERVAL = 2 * 60 * 1000;

// Monitoring interval (ms) - comprehensive monitoring every 5 minutes
const MONITORING_INTERVAL = 5 * 60 * 1000;

// Initial delays for startup operations
const INITIAL_RESUMPTION_DELAY = 2000; // 2 seconds
const INITIAL_MONITORING_DELAY = 10000; // 10 seconds

/**
 * Hook that manages transcription queue processing with simple orphan adoption.
 * Each tab processes its own items and can adopt orphaned items from closed tabs.
 *
 * Key features:
 * - Tab isolation: Each tab only processes its own items during normal processing
 * - Simple orphan adoption: Random delays prevent simultaneous adoption by multiple tabs
 * - Heartbeat-based orphan detection: Uses tab heartbeats to reliably detect closed tabs
 * - Double-check mechanism: Verifies items are still orphaned before adopting them
 *
 * @returns sessionId - The session identifier for this tab
 */
const useTranscriptionsQueueWatcher = () => {
	const processingRef = useRef<boolean>(false);
	const queueRef = useRef(TranscriptionsQueue.getInstance());
	const coordinationCleanupRef = useRef<(() => void) | null>(null);

	// Get the session ID for this tab - memoized to be stable across re-renders
	const sessionId = useMemo(() => getTabSessionId(), []);

	const providerId = useAppSelector(selectCurrentProviderId);
	const { hasFeatureFlagged: hasAgentProviderGoogleSpeechToText } = useFeatureFlag(
		'agent-provider-google-speech-to-text'
	);
	const { hasFeatureFlagged: hasDeferredTranscriptionCompletion } = useFeatureFlag(
		'deferred-transcription-completion'
	);

	const [fetchTranscriptions] = useLazyGetNoteTranscriptionsQuery();
	const [completeTranscriptionParts] = useCompleteTranscriptionPartMutation();

	/**
	 * Mark a transcription as active (being processed)
	 */
	const markTranscriptionActive = (transcriptionId: string) => {
		TranscriptionStateManager.markTranscriptionActive(providerId, transcriptionId);
	};

	/**
	 * Mark a transcription as completed (no longer being processed)
	 */
	const markTranscriptionCompleted = (transcriptionId: string) => {
		TranscriptionStateManager.markTranscriptionCompleted(providerId, transcriptionId);
	};

	/**
	 * Simple orphan adoption with random delay to prevent simultaneous adoption
	 * Multiple tabs can attempt adoption, but random delays spread them out
	 */
	const adoptOrphanedItems = async () => {
		try {
			// Random delay 0-5 seconds to prevent simultaneous adoption by multiple tabs
			const delay = Math.random() * 5000;
			await new Promise((resolve) => setTimeout(resolve, delay));

			// Get all items from the queue
			const allItems = await queueRef.current.getAllItems();
			const orphanedItems: { key: IDBValidKey; value: TranscriptionPartQueueMessage }[] = [];

			// Find orphaned items (items from other sessions where the tab is no longer alive)
			for (const item of allItems) {
				if (item.value.sessionId !== sessionId) {
					const originalTabAlive = await isTabAlive(item.value.sessionId);
					if (!originalTabAlive) {
						orphanedItems.push(item);
					}
				}
			}

			if (orphanedItems.length === 0) {
				return;
			}

			logger.info('Starting adoption of orphaned items', {
				category: LOG_CATEGORY,
				sessionId,
				orphanedCount: orphanedItems.length,
			});

			const adoptedItems: typeof orphanedItems = [];

			// Adopt each orphaned item by changing its sessionId
			for (const orphanedItem of orphanedItems) {
				try {
					// Double-check the item is still orphaned (might have been adopted by another tab)
					const stillOrphaned = await isTabAlive(orphanedItem.value.sessionId);

					if (stillOrphaned) {
						// Original tab came back alive, skip this item
						continue;
					}

					// Mark transcription as active since we're adopting it
					markTranscriptionActive(orphanedItem.value.transcriptionId);

					// Remove old item and add updated one with current sessionId
					await queueRef.current.deleteByKey(orphanedItem.key);
					await queueRef.current.enqueue({
						audioChunk: orphanedItem.value.audioChunk,
						noteId: orphanedItem.value.noteId,
						contactId: orphanedItem.value.contactId,
						transcriptionId: orphanedItem.value.transcriptionId,
						metadata: orphanedItem.value.metadata,
					});

					adoptedItems.push(orphanedItem);

					logger.info('Successfully adopted orphaned item', {
						category: LOG_CATEGORY,
						sessionId,
						transcriptionId: orphanedItem.value.transcriptionId,
						partNumber: orphanedItem.value.metadata.partNumber,
						originalSessionId: orphanedItem.value.sessionId,
					});
				} catch (error) {
					logger.error('Failed to adopt orphaned item', {
						category: LOG_CATEGORY,
						sessionId,
						transcriptionId: orphanedItem.value.transcriptionId,
						partNumber: orphanedItem.value.metadata.partNumber,
						originalSessionId: orphanedItem.value.sessionId,
						error,
					});
				}
			}

			// Simple adoption statistics logging
			if (adoptedItems.length > 0) {
				logger.info('Adoption completed successfully', {
					category: LOG_CATEGORY,
					sessionId,
					totalAdoptedItems: adoptedItems.length,
					adoptedTranscriptions: adoptedItems.map((item) => ({
						transcriptionId: item.value.transcriptionId,
						partNumber: item.value.metadata.partNumber,
						originalSessionId: item.value.sessionId,
					})),
				});

				// Log queue status after adoption
				await logQueueStatus(() => queueRef.current.getAllItems(), sessionId, 'post_adoption');
			}
		} catch (error) {
			logger.error('Error during orphaned items adoption', {
				category: LOG_CATEGORY,
				sessionId,
				error,
			});
		}
	};

	/**
	 * Process audio chunk upload for transcription
	 */
	const processAudioChunkUpload = async (message: TranscriptionPartQueueMessage) => {
		if (!providerId || !message) {
			return;
		}

		const { transcriptionId, metadata, contactId, noteId } = message;

		// Mark transcription as active when we start processing
		markTranscriptionActive(transcriptionId);

		try {
			if (metadata.isLastPart) {
				// Only complete transcription if this item belongs to our session
				if (message.sessionId === sessionId) {
					await completeTranscriptionParts({
						transcriptionId,
					}).unwrap();

					// Mark transcription as completed since this was the last part
					markTranscriptionCompleted(transcriptionId);

					// Refetch transcriptions to get the latest status
					fetchTranscriptions({
						contactId,
						noteId,
					});

					logger.info('Completed transcription processing', {
						category: LOG_CATEGORY,
						transcriptionId,
						sessionId,
						partNumber: metadata.partNumber,
					});
				}
			} else {
				// Only upload transcription part if this item belongs to our session
				if (message.sessionId === sessionId) {
					await api.uploadTranscriptionPart({
						providerId,
						transcriptionId,
						partNumber: metadata.partNumber,
						startTime: metadata.startTime,
						endTime: metadata.endTime,
						isLastPart: metadata.isLastPart,
						dto: message.audioChunk,
						context: {
							useAgentProviderGoogleSpeechToText: hasAgentProviderGoogleSpeechToText,
							deferredTranscriptionCompletion: hasDeferredTranscriptionCompletion,
						},
					});
				}
			}
		} catch (error) {
			logger.error('Error processing transcription part - will retry', {
				category: LOG_CATEGORY,
				sessionId,
				transcriptionId,
				partNumber: metadata.partNumber,
				isLastPart: metadata.isLastPart,
				error,
			});

			// Don't mark transcription as completed on error - let it retry
			// The item will remain in the queue and be retried on next cycle
			throw error; // Re-throw to handle in processQueue
		}
	};

	/**
	 * Process the transcription queue with coordinated orphan handling
	 */
	const processQueue = async () => {
		if (processingRef.current) return;
		processingRef.current = true;

		try {
			const item = await queueRef.current.peekAndLockFront();

			if (item && item.value.sessionId === sessionId) {
				// Process our own items
				try {
					await processAudioChunkUpload(item.value);
					await queueRef.current.deleteByKey(item.key);

					logger.info('Successfully processed queue item', {
						category: LOG_CATEGORY,
						sessionId,
						transcriptionId: item.value.transcriptionId,
						partNumber: item.value.metadata.partNumber,
						isLastPart: item.value.metadata.isLastPart,
					});
				} catch (error) {
					logger.error('Error processing audio chunk - item will be retried', {
						category: LOG_CATEGORY,
						sessionId,
						transcriptionId: item.value.transcriptionId,
						partNumber: item.value.metadata.partNumber,
						error,
					});

					// Unlock the item for retry - don't delete it
					queueRef.current.unlockFront();
				}
			} else if (item) {
				// Item belongs to another session, unlock it for that session to process
				// The periodic adoption check will handle orphaned items
				queueRef.current.unlockFront();
			}
		} catch (error) {
			logger.error('Error accessing transcription queue', {
				category: LOG_CATEGORY,
				sessionId,
				error,
			});
		} finally {
			processingRef.current = false;
		}
	};

	useEffect(() => {
		// Start BroadcastChannel coordination for this tab
		const coordinationCleanup = startTabCoordination(sessionId);
		coordinationCleanupRef.current = coordinationCleanup;

		// Set up queue processing interval
		const queueIntervalId = setInterval(processQueue, QUEUE_PROCESSING_INTERVAL);

		// Set up periodic orphan adoption checks with random delays
		const adoptionIntervalId = setInterval(adoptOrphanedItems, RESUMPTION_CHECK_INTERVAL);

		// Set up periodic comprehensive monitoring
		const monitoringIntervalId = setInterval(
			() =>
				performComprehensiveMonitoring(() => queueRef.current.getAllItems(), sessionId, 'periodic_monitoring'),
			MONITORING_INTERVAL
		);

		// Run initial adoption check after a short delay to allow heartbeats to stabilize
		const initialAdoptionTimeout = setTimeout(adoptOrphanedItems, INITIAL_RESUMPTION_DELAY);

		// Run initial monitoring check
		const initialMonitoringTimeout = setTimeout(
			() => performComprehensiveMonitoring(() => queueRef.current.getAllItems(), sessionId, 'startup_monitoring'),
			INITIAL_MONITORING_DELAY
		);

		logger.info('Transcription queue watcher started', {
			category: LOG_CATEGORY,
			sessionId,
			queueProcessingInterval: QUEUE_PROCESSING_INTERVAL,
			adoptionCheckInterval: RESUMPTION_CHECK_INTERVAL,
			monitoringInterval: MONITORING_INTERVAL,
		});

		return () => {
			// Stop BroadcastChannel coordination
			if (coordinationCleanupRef.current) {
				coordinationCleanupRef.current();
				coordinationCleanupRef.current = null;
			}

			// Clear intervals and timeouts
			clearInterval(queueIntervalId);
			clearInterval(adoptionIntervalId);
			clearInterval(monitoringIntervalId);
			clearTimeout(initialAdoptionTimeout);
			clearTimeout(initialMonitoringTimeout);

			logger.info('Transcription queue watcher stopped', {
				category: LOG_CATEGORY,
				sessionId,
			});
		};
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	// Return sessionId for external use (e.g., when adding items to queue)
	return { sessionId };
};

export default useTranscriptionsQueueWatcher;
