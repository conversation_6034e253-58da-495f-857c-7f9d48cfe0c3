import { logger } from '@carepatron/utilities';

import { isTabAlive } from './sessionUtils';
import { TranscriptionPartQueueMessage } from './transcriptionsQueue';

// Log category for orphan detection
const LOG_CATEGORY = 'ai-transcription-orphan-detection';

export type OrphanDetectionResult = {
	isOrphaned: boolean;
	reason: string;
	gracePeriodRemaining?: number;
};

/**
 * Determine if a queue item is truly orphaned and can be safely adopted
 *
 * An item is considered orphaned if:
 * 1. The tab that created it is no longer alive (doesn't respond to ping)
 *
 * @param item - The queue item to check
 * @param currentSessionId - The session ID of the current tab
 * @returns Promise<OrphanDetectionResult> with adoption decision and reasoning
 */
export const isItemOrphaned = async (
	item: TranscriptionPartQueueMessage,
	currentSessionId: string
): Promise<OrphanDetectionResult> => {
	// Item belongs to current session - not orphaned
	if (item.sessionId === currentSessionId) {
		return {
			isOrphaned: false,
			reason: 'belongs_to_current_session',
		};
	}

	// Check if the original tab is still alive using BroadcastChannel ping
	const originalTabAlive = await isTabAlive(item.sessionId);

	if (originalTabAlive) {
		return {
			isOrphaned: false,
			reason: 'original_tab_still_alive',
		};
	}

	logger.info('Detected orphaned item', {
		category: LOG_CATEGORY,
		transcriptionId: item.transcriptionId,
		partNumber: item.metadata.partNumber,
		originalSessionId: item.sessionId,
		currentSessionId,
		originalTabAlive,
	});

	return {
		isOrphaned: true,
		reason: 'original_tab_dead',
	};
};

/**
 * Get all orphaned items from a list of queue items
 *
 * @param items - All queue items to check
 * @param currentSessionId - The session ID of the current tab
 * @returns Promise<Array> of orphaned items that can be adopted
 */
export const getOrphanedItems = async (
	items: { key: IDBValidKey; value: TranscriptionPartQueueMessage }[],
	currentSessionId: string
): Promise<{ key: IDBValidKey; value: TranscriptionPartQueueMessage; reason: string }[]> => {
	const orphanedItems: { key: IDBValidKey; value: TranscriptionPartQueueMessage; reason: string }[] = [];

	// Check each item sequentially to avoid overwhelming BroadcastChannel
	for (const item of items) {
		const detection = await isItemOrphaned(item.value, currentSessionId);

		if (detection.isOrphaned) {
			orphanedItems.push({
				key: item.key,
				value: item.value,
				reason: detection.reason,
			});
		}
	}

	if (orphanedItems.length > 0) {
		logger.info('Found orphaned items for adoption', {
			category: LOG_CATEGORY,
			currentSessionId,
			orphanedCount: orphanedItems.length,
			totalItemsChecked: items.length,
			orphanedItems: orphanedItems.map((item) => ({
				transcriptionId: item.value.transcriptionId,
				partNumber: item.value.metadata.partNumber,
				originalSessionId: item.value.sessionId,
				reason: item.reason,
			})),
		});
	}

	return orphanedItems;
};

/**
 * Check if any items in the queue need adoption
 * This is a convenience function that combines queue inspection with orphan detection
 *
 * @param getAllItems - Function to get all items from the queue
 * @param currentSessionId - The session ID of the current tab
 * @returns Promise resolving to array of orphaned items
 */
export const findOrphanedQueueItems = async (
	getAllItems: () => Promise<{ key: IDBValidKey; value: TranscriptionPartQueueMessage }[]>,
	currentSessionId: string
): Promise<{ key: IDBValidKey; value: TranscriptionPartQueueMessage; reason: string }[]> => {
	try {
		const allItems = await getAllItems();
		return await getOrphanedItems(allItems, currentSessionId);
	} catch (error) {
		logger.error('Failed to find orphaned queue items', {
			category: LOG_CATEGORY,
			currentSessionId,
			error,
		});
		return [];
	}
};

/**
 * Log adoption statistics for monitoring and debugging
 *
 * @param adoptedItems - Items that were successfully adopted
 * @param currentSessionId - The session ID of the current tab
 */
export const logAdoptionStats = (
	adoptedItems: { key: IDBValidKey; value: TranscriptionPartQueueMessage; reason: string }[],
	currentSessionId: string
): void => {
	if (adoptedItems.length === 0) {
		return;
	}

	// Group by transcription ID for better insights
	const transcriptionGroups = adoptedItems.reduce(
		(groups, item) => {
			const transcriptionId = item.value.transcriptionId;
			if (!groups[transcriptionId]) {
				groups[transcriptionId] = [];
			}
			groups[transcriptionId].push(item);
			return groups;
		},
		{} as Record<string, typeof adoptedItems>
	);

	logger.info('Adoption completed successfully', {
		category: LOG_CATEGORY,
		currentSessionId,
		totalAdoptedItems: adoptedItems.length,
		transcriptionsAffected: Object.keys(transcriptionGroups).length,
		adoptionDetails: Object.entries(transcriptionGroups).map(([transcriptionId, items]) => ({
			transcriptionId,
			itemCount: items.length,
			partNumbers: items.map((item) => item.value.metadata.partNumber).sort((a, b) => a - b),
			originalSessions: Array.from(new Set(items.map((item) => item.value.sessionId))),
		})),
	});
};
