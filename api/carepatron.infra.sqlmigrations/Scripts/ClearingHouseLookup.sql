﻿START TRANSACTION;

CREATE TABLE carepatron.clearing_house_id_lookups (
    identity bigint GENERATED BY DEFAULT AS IDENTITY,
    entity_id uuid NOT NULL,
    CONSTRAINT pk_clearing_house_id_lookups PRIMARY KEY (identity)
);

CREATE UNIQUE INDEX ix_clearing_house_id_lookups_entity_id ON carepatron.clearing_house_id_lookups (entity_id);

INSERT INTO "__EFMigrationsHistory" (migration_id, product_version)
VALUES ('20250612002637_ClearingHouseLookup', '8.0.1');

COMMIT;

