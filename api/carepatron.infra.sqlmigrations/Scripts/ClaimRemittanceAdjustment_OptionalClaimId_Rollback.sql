﻿START TRANSACTION;

UPDATE carepatron.insurance_claim_adjustments SET claim_id = '00000000-0000-0000-0000-000000000000' WHERE claim_id IS NULL;
ALTER TABLE carepatron.insurance_claim_adjustments ALTER COLUMN claim_id SET NOT NULL;
ALTER TABLE carepatron.insurance_claim_adjustments ALTER COLUMN claim_id SET DEFAULT '00000000-0000-0000-0000-000000000000';

DELETE FROM "__EFMigrationsHistory"
WHERE migration_id = '20250606105734_ClaimRemittanceAdjustment_OptionalClaimId';

COMMIT;

