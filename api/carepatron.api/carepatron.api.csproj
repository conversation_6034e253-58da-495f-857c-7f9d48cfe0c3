﻿<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
    <UserSecretsId>1b458a10-5072-4605-ad59-3355a98a9f38</UserSecretsId>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Amazon.AspNetCore.Identity.Cognito" Version="3.0.2" />
    <PackageReference Include="Amazon.Extensions.CognitoAuthentication" Version="2.5.2" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="AWSSDK.Core" Version="3.7.400.70" />
    <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="10.0.1" />
    <PackageReference Include="AWSSDK.DynamoDBv2" Version="3.7.303.12" />
    <PackageReference Include="EFCore.NamingConventions" Version="8.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.1" />
    <PackageReference Include="NSwag.AspNetCore" Version="14.4.0" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.8.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.8.0" />
    <PackageReference Include="Scrutor" Version="3.3.0" />
    <PackageReference Include="Sentry.AspNetCore" Version="4.9.0" />
    <PackageReference Include="Sentry.OpenTelemetry" Version="4.9.0" />
    <PackageReference Include="Sentry.Serilog" Version="4.9.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Stripe.net" Version="39.3.0" />
    <PackageReference Include="WebPush" Version="1.0.11" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.1">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="8.0.1" />
    <PackageReference Include="AWSSDK.SQS" Version="3.7.301.8" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AWSSDK.S3" Version="3.7.308.4" />
    <PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.300" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Agents\Modules\Agents.Module\Agents.Module.csproj" />
    <ProjectReference Include="..\carepatron.core\carepatron.core.csproj" />
    <ProjectReference Include="..\carepatron.infra.cognito\carepatron.infra.cognito.csproj" />
    <ProjectReference Include="..\carepatron.infra.chime\carepatron.infra.chime.csproj" />
    <ProjectReference Include="..\carepatron.infra.dynamo\carepatron.infra.dynamo.csproj" />
    <ProjectReference Include="..\carepatron.infra.eventbridge\carepatron.infra.eventbridge.csproj" />
    <ProjectReference Include="..\carepatron.infra.partnerstack\carepatron.infra.partnerstack.csproj" />
    <ProjectReference Include="..\carepatron.infra.posthog\carepatron.infra.posthog.csproj" />
    <ProjectReference Include="..\carepatron.infra.redis\carepatron.infra.redis.csproj" />
    <ProjectReference Include="..\carepatron.infra.s3\carepatron.infra.s3.csproj" />
    <ProjectReference Include="..\carepatron.infra.secrets\carepatron.infra.secrets.csproj" />
    <ProjectReference Include="..\carepatron.infra.ses\carepatron.infra.ses.csproj" />
    <ProjectReference Include="..\carepatron.infra.common\carepatron.infra.common.csproj" />
    <ProjectReference Include="..\carepatron.infra.stripe\carepatron.infra.stripe.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\carepatron.infra.telemetry\carepatron.infra.telemetry.csproj" />
    <ProjectReference Include="..\carepatron.infra.webpush\carepatron.infra.webpush.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\carepatron.infra.sql\carepatron.infra.sql.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\carepatron.infra.google\carepatron.infra.google.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\carepatron.infra.microsoft\carepatron.infra.microsoft.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\carepatron.infra.sqs\carepatron.infra.sqs.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\carepatron.infra.zoom\carepatron.infra.zoom.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
    <ProjectReference Include="..\carepatron.infra.lambda\carepatron.infra.lambda.csproj" />
    <ProjectReference Include="..\Insurance\Modules\ClaimMD.Module\ClaimMD.Module.csproj" />
    <ProjectReference Include="..\Insurance\Modules\Insurance.Module\Insurance.Module.csproj" />
    <ProjectReference Include="..\Messagings\Messaging.Aws\Messaging.Aws.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  
  <Target Name="GenerateOpenApi" AfterTargets="Build" Condition="'$(Configuration)' == 'Debug'">
    <MSBuild Projects="..\Utilities\OpenApi\OpenApi.csproj"
       Targets="GenerateOpenApi"
       Properties="Generate=true;Configuration=$(Configuration)"
       ContinueOnError="true" />
  </Target>
  
</Project>
