﻿using carepatron.api.Constants;
using carepatron.api.Contracts.Requests.Calls;
using carepatron.core.Application.Calls.Commands;
using carepatron.core.Application.Calls.Models;
using carepatron.core.Application.Calls.Queries;
using carepatron.core.Commands.Commands.Call;
using carepatron.core.Identity;
using carepatron.core.Models.Call;
using carepatron.core.Models.Common;
using carepatron.core.Models.Execution;
using carepatron.core.Queries.Queries.Call;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System;
using System.Threading.Tasks;

namespace carepatron.api.Controllers
{
    [ControllerProperties(CodeOwner.Communications)]
    [Route("api/calls")]
    public class CallsController : BaseController
    {
        private readonly IMediator mediator;

        public CallsController(
            IIdentityContextFactory identityContextFactory,
            IMediator mediator)
                : base(identityContextFactory)
        {
            this.mediator = mediator;
        }

        [HttpPost("")]
        public async Task<CallSummary> CreateCall([FromBody] CreateCallRequest request)
        {
            var command = new CreateCallCommand(IdentityContext, request.Title, request.InviteOnly, request.Attendees, request.MediaRegion, request.ProviderId, request.EnableEchoReduction);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [HttpDelete("{id}")]
        public async Task<Guid> EndCall(Guid id)
        {
            var command = new EndCallCommand(IdentityContext, id);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        // Anyone can access this URL
        [AllowAnonymous]
        [HttpPost("{id}/join")]
        public async Task<CallAttendee> JoinCall([FromRoute] Guid id, [FromQuery] Guid? attendeeId = null)
        {
            var command = new JoinCallCommand(IdentityContext, id, attendeeId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [AllowAnonymous]
        [HttpPost("{id}/initiate")]
        public async Task<Unit> InitiateJoinAttempt([FromRoute] Guid id)
        {
            var command = new InitiateJoinCallAttemptCommand(IdentityContext, id);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [AllowAnonymous]
        [HttpPost("{id}/reset")]
        public async Task<CallSummary> ResetCall([FromRoute] Guid id, [FromQuery] Guid attendeeId,
            [FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)] ResetCallRequest request)
        {
            var command = new ResetCallCommand(IdentityContext, id, attendeeId, request?.EnableEchoReduction);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [HttpGet]
        public async Task<CallSummary[]> GetCalls(int limit = 50, int offset = 0, bool includeNotStarted = false)
        {
            var query = new GetCallsQuery(IdentityContext, limit, offset, includeNotStarted);
            var result = await mediator.Send(query);
            return ExecutionResult(result);
        }

        // Anyone can access this URL
        [AllowAnonymous]
        [HttpGet("{id}")]
        public async Task<CallSummary> GetCall(Guid id)
        {
            var query = new GetCallQuery(IdentityContext, id);
            var result = await mediator.Send(query);
            return ExecutionResult(result);
        }

        [HttpPost("{id}/start")]
        public async Task<CallSummary> StartCall(Guid id, [FromBody] StartCallRequest request)
        {
            var command = new StartCallCommand(IdentityContext, id, request.MediaRegion, request.EnableEchoReduction);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        // Anyone can access this URL
        [AllowAnonymous]
        [HttpGet("{id}/url")]
        public async Task<IActionResult> GetCallUrl(
            Guid id,
            [FromQuery] bool redirect,
            [FromQuery] bool client,
            [FromQuery] Email email,
            [FromQuery] string type)
        {
            var query = new GetCallUrlQuery(IdentityContext, id, client, email, type);
            var result = await mediator.Send(query);

            result.ThrowIfNotSuccess();

            var url = result.Result;

            if (redirect)
            {
                return Redirect(url);
            }

            return Ok(url);
        }

        [AllowAnonymous]
        [HttpGet("{id}/attendees")]
        public async Task<CallParticipant[]> GetAttendees([FromRoute] Guid id, [FromQuery] string externalAttendeeId)
        {
            var query = new GetCallParticipantsQuery(IdentityContext, id, externalAttendeeId);
            var result = await mediator.Send(query);
            return ExecutionResult(result);
        }

        [AllowAnonymous]
        [HttpPost("{id}/attendees/{attendeeId}/activate")]
        public async Task<CallAttendee> ActivateAttendee([FromRoute] Guid id, [FromRoute] Guid attendeeId, [FromBody] ActivateAttendeeRequest request)
        {
            var command = new ActivateAttendeeCommand(id, attendeeId, request.Name, request.ExternalAttendeeId, request.IsActive);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [HttpPost("~/api/providers/{providerId}/calls/{id}/attendees/{attendeeId}/admit")]
        public async Task<CallAttendee> AdmitAttendee([FromRoute] Guid id, [FromRoute] Guid attendeeId)
        {
            var command = new AdmitAttendeeCommand(id, attendeeId, IdentityContext);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [HttpPost("~/api/providers/{providerId}/calls/{id}/attendees/{attendeeId}/deny")]
        public async Task<CallAttendee> DenyAttendee([FromRoute] Guid id, [FromRoute] Guid attendeeId)
        {
            var command = new DenyAttendeeCommand(id, attendeeId, IdentityContext);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [HttpPost("~/api/providers/{providerId}/calls/{id}/capture-media")]
        public async Task<Unit> CaptureMedia(Guid providerId, Guid id, [FromBody] CreateMediaPipelineRequest request)
        {
            var command = new CaptureMediaCommand(id, providerId, request.MediaRegion, request.MeetingOccurence, request.TranscriptionOptions, IdentityContext);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [HttpGet("~/api/providers/{providerId}/calls/{id}/meeting-info")]
        public async Task<MeetingInfo> GetMeetingInfo([FromRoute] Guid id, [FromQuery] Guid? taskId = null)
        {
            var command = new GetCallMeetingInfoQuery(IdentityContext, id, taskId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }
    }
}
