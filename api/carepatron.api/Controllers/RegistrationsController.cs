﻿using carepatron.api.Contracts.Requests.Registrations;
using carepatron.api.Mappers;
using carepatron.core.Application.Registrations.Commands;
using carepatron.core.Application.Registrations.Models;
using carepatron.core.Application.Registrations.Queries;
using carepatron.core.Identity;
using carepatron.core.Repositories.User;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System;
using System.Threading.Tasks;
using AutoMapper;
using carepatron.api.Constants;
using carepatron.core.Models.Media;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Users.Commands;
using Microsoft.AspNetCore.RateLimiting;

namespace carepatron.api.Controllers
{
    [ControllerProperties(CodeOwner.Workspace)]
    [Authorize]
    [Route("api/persons/registrations")]
    public class RegistrationsController : BaseController
    {
        private readonly IMediator mediator;
        private readonly IUserRepository userRepository;
        private readonly IMapper mapper;

        public RegistrationsController(
            IIdentityContextFactory identityContextFactory,
            IUserRepository userRepository,
            IMediator mediator,
            IMapper mapper)
            : base(identityContextFactory)
        {
            this.userRepository = userRepository;
            this.mediator = mediator;
            this.mapper = mapper;
        }

        // seems like this needs to be deprecrated too? thoughts?
        // Registration is NOT protected by auth
        [AllowAnonymous]
        [HttpPost("~/api/contacts/registrations")]
        public async Task<CreatedRegistration> RegisterContact([FromHeader(Name = "Accept-Language")] string locale,
            [FromBody] RegisterContactRequest request)
        {
            if (request.Contact == null)
                return new CreatedRegistration();

            var contact = request.Contact;
            var files = FileMapper.Map(request.Files);
            var rolesAccessibleBy = request.RolesAccessibleBy;

            var command = new RegisterClientCommand(
                IdentityContext,
                request.Token,
                request.ProviderId,
                contact.Id,
                contact.FirstName,
                contact.LastName,
                contact.MiddleNames,
                contact.Email,
                contact.PhoneNumber,
                contact.Gender,
                contact.BirthDate,
                contact.Address,
                contact.Languages ?? Array.Empty<ContactLanguage>(),
                contact.Settings,
                rolesAccessibleBy,
                files,
                request.Password,
                request.StripePaymentMethodId,
                request.AuthoriseCard,
                locale);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        // Registration is NOT protected by auth
        [AllowAnonymous]
        [HttpPost("~/api/contacts/registrations/payments")]
        public async Task<CreatePaymentPersonResult> RegisterPaymentPerson([FromBody] PaymentPersonRequest request)
        {
            var command = new CreatePaymentPersonCommand(IdentityContext, request.ContactId, request.Token, request.Email, request.FirstName, request.LastName, request.PhoneNumber);
            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        // Registration is NOT protected by auth
        [AllowAnonymous]
        [HttpGet]
        public async Task<ContactRegistrationDetails> GetRegistrationDetails([FromQuery] Guid contactId, [FromQuery] string token = null)
        {
            var query = new ContactRegistrationDetailsQuery(IdentityContext, contactId, token);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        // Registration is NOT protected by auth
        [AllowAnonymous]
        [HttpPost("forgotpassword")]
        public async Task OldForgotPassword([FromBody] ForgotPasswordRequest request)
        {
            await userRepository.ForgotPassword(request.Email, request.Language);
        }

        [EnableRateLimiting(RateLimitPolicy.ForgotPassword)]
        [AllowAnonymous]
        [HttpPost("~/api/forgot-password")]
        public async Task<Unit> ForgotPassword([FromBody] ForgotPasswordRequest request)
        {
            var command = new ForgotPasswordCommand(request.Email, request.Language);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }
        
        [EnableRateLimiting(RateLimitPolicy.ForgotPassword)]
        [HttpPost("~/api/forgot-password/reset")]
        [AllowAnonymous]
        public async Task<Unit> ResetPassword([FromBody] ResetPasswordRequest request)
        {
            var command = new ResetPasswordCommand(request.Email, request.Code, request.NewPassword);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [HttpPost("linkuser")]
        public async Task LinkUser()
        {
            var command = new LinkFederatedUserCommand(IdentityContext);

            await mediator.Send(command);
        }

        // Registration is NOT protected by auth
        [AllowAnonymous]
        [HttpGet("canuserlink")]
        public async Task<CanUserLinkResult> CanUserlink()
        {
            var query = new CanUserlinkQuery(IdentityContext.Email);
            var result = await mediator.Send(query);
            return ExecutionResult(result);
        }

        [AllowAnonymous]
        [HttpPost("~/api/register")]
        public async Task<RegisterConfirm> RegisterAccount([FromBody] RegisterAccountRequest request)
        {
            var command = new RegisterAccountCommand(request.Email, request.Password, request.Token, request.IsPortal, request.IsClient, request.Language);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [HttpPost("~/api/register/person")]
        public async Task<Guid> RegisterPerson([FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)] RegisterPersonRequest request, [FromHeader(Name = "Accept-Time-Zone")] string timeZone, [FromHeader(Name = "Accept-Language")] string locale)
        {
            var command = new RegisterPersonCommand(IdentityContext.Email,
                request?.Name,
                request?.TimeZone ?? timeZone,
                request?.Locale ?? locale.ParseAcceptLanguage());

            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [HttpPost("workspace")]
        public async Task<PersonRegisteredWorkspace> RegisterWorkspace(
            [FromBody] RegisteredWorkspaceRequest request,
            [FromHeader(Name = "Accept-Language")] string locale,
            [FromHeader(Name = "ps_partner_id")] string partnerKey,
            [FromHeader(Name = "ps_click_id")] string clickId)
        {
            var logo = mapper.Map<ProviderLogo>(request.Logo);
            var command = new RegisterWorkspaceCommand(IdentityContext.Email,
                request.Name,
                request.Profession,
                request.TeamSize,
                request.ExploreFeatures,
                request.ToolsUsed,
                request.CountryCode,
                request.TimeZone,
                request.ReferralCode,
                locale?.ParseAcceptLanguage(),
                request.CustomerReferralCode,
                request.FirstName,
                request.LastName,
                request.BusinessName,
                request.Website,
                request.BillingAddress,
                request.Locations,
                request.Services,
                request.StaffSchedules,
                logo,
                request.PrimaryColorHex,
                partnerKey,
                clickId,
                request.PhoneNumber);

            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }
    }
}