﻿using carepatron.api.Constants;
using carepatron.api.Contracts.Requests;
using carepatron.api.Contracts.Requests.Notes;
using carepatron.api.Mappers;
using carepatron.core.Application.AskAI.Commands;
using carepatron.core.Application.AskAI.Models;
using carepatron.core.Application.AskAI.Queries;
using carepatron.core.Application.Notes.Commands;
using carepatron.core.Application.Notes.Models;
using carepatron.core.Application.Notes.Queries;
using carepatron.core.Application.Sharing.Models;
using carepatron.core.Application.SmartChips.Models;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Identity;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Notes;
using carepatron.core.Models.Pagination;
using carepatron.core.Paging.Models;
using carepatron.core.Utilities;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.Practitioner
{
    /// <summary>
    /// Controller for managing notes for a specific provider and contact.
    /// </summary>
    [ControllerProperties(CodeOwner.NotesAndDocuments)]
    [Authorize]
    [Route("api/providers/{providerId}/contacts/{contactId}/notes")]
    public class ProviderNotesController : BaseController
    {
        private readonly IMediator mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="ProviderNotesController"/> class.
        /// </summary>
        /// <param name="identityContextFactory">The identity context factory.</param>
        /// <param name="mediator">The mediator.</param>
        public ProviderNotesController(
            IIdentityContextFactory identityContextFactory,
            IMediator mediator) : base(identityContextFactory)
        {
            this.mediator = mediator;
        }

        /// <summary>
        /// Fetches a note by its ID.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves a specific note using its unique identifier. 
        /// It returns a NoteDetail object that includes all the information about the note.
        /// If the note with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="id">The ID of the note.</param>
        /// <returns>The note details.</returns>
        /// <response code="200">Returns the note details.</response>
        /// <response code="404">If the note is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationViewForContact)]
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<NoteDetail> GetNoteById(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid id)
        {
            var result = await mediator.Send(new GetNoteByIdQuery(id));

            return ExecutionResult(result);
        }

        /// <summary>
        /// Fetches a list of notes for a specific contact.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves a list of notes associated with a specific contact. 
        /// The notes can be filtered by tags, and the number of notes returned can be limited and offset for pagination purposes.
        /// If no notes are found, an empty list is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="tags">The tags to filter by.</param>
        /// <param name="limit">The maximum number of notes to return.</param>
        /// <param name="offset">The number of notes to skip before starting to return notes.</param>
        /// <returns>A paginated list of notes.</returns>
        /// <response code="200">Returns the paginated list of notes.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationViewForContact)]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<PaginatedResult<NoteDetail>> GetNotes(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromQuery] Guid[] tags = null,
            [FromQuery] int limit = QueryConstants.DefaultLimit,
            [FromQuery] int offset = QueryConstants.DefaultOffset)
        {
            var pagination = new PaginationRequest(limit, offset);
            var query = new GetNotesAsProviderQuery(IdentityContext, contactId, tags, pagination);
            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Creates a new note for a specific contact.
        /// </summary>
        /// <remarks>
        /// This endpoint creates a new note for a specific contact. 
        /// The note details are provided in the request body. 
        /// The note includes information such as title, content, status, tags, and associated task.
        /// If the note is successfully created, the details of the note are returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="request">The request body containing the details of the note to be created.</param>
        /// <returns>The details of the newly created note.</returns>
        /// <response code="200">Returns the details of the newly created note.</response>
        /// <response code="400">If the request body is invalid.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<NoteDetail> CreateNote(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromBody] CreateNoteRequest request)
        {
            var files = FileMapper.Map(request.Attachments);

            var command = new CreateNoteCommand(IdentityContext,
                contactId,
                request.Title,
                request.Content,
                request.ContentJson,
                request.RolesAccessibleBy,
                files,
                request.Status,
                request.Tags,
                request.TaskId,
                request.OccurrenceDateTimeUtc,
                request.TimeZone,
                request.Diagnoses,
                request.SkipAttachments);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes a note by its ID.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a specific note using its unique identifier. 
        /// Once the note is deleted, it cannot be recovered. 
        /// If the note with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <returns>The ID of the deleted note.</returns>
        /// <response code="200">Returns the ID of the deleted note.</response>
        /// <response code="404">If the note is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpDelete("{noteId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Guid> DeleteNote(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId)
        {
            var command = new DeleteNoteCommand(
                IdentityContext,
                noteId);

            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Updates a note for a specific contact.
        /// </summary>
        /// <remarks>
        /// This endpoint updates a specific note using its unique identifier. 
        /// The new details of the note are provided in the request body. 
        /// The note details include information such as title, content, status, tags, and associated task.
        /// If the note with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note to be updated.</param>
        /// <param name="request">The request body containing the new details of the note.</param>
        /// <returns>The details of the updated note.</returns>
        /// <response code="200">Returns the details of the updated note.</response>
        /// <response code="400">If the request body is invalid.</response>
        /// <response code="404">If the note is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPut("{noteId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<NoteDetail> UpdateNote(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromBody] UpdateNoteRequest request)
        {
            var files = FileMapper.Map(request.Attachments);

            var command = new UpdateNoteCommand(
                IdentityContext,
                noteId,
                request.Title,
                request.Content,
                request.ContentJson,
                request.RolesAccessibleBy,
                files,
                request.Status,
                request.Tags,
                request.TaskId,
                request.OccurrenceDateTimeUtc,
                request.TimeZone,
                request.Diagnoses,
                request.SkipAttachments);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Shares a note with a contact.
        /// </summary>
        /// <remarks>
        /// This endpoint shares a specific note with a contact. 
        /// The details of the sharing, including the contact and the message, are provided in the request body. 
        /// If the note is successfully shared, the details of the contacts with whom the note has been shared are returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note to be shared.</param>
        /// <param name="request">The request body containing the details of the sharing.</param>
        /// <returns>An array of contacts with whom the note has been shared.</returns>
        /// <response code="200">Returns an array of contacts with whom the note has been shared.</response>
        /// <response code="400">If the request body is invalid.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost("{noteId}/sharing")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<SharedWith[]> ShareNoteWithContact(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromBody] ShareWithRequest request)
        {
            var command = new SaveSharedNotesCommand(IdentityContext, noteId, request.SharedWith, request.Message);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Updates the access role of a shared note for a specific contact.
        /// </summary>
        /// <remarks>
        /// This endpoint updates the access role of a shared note for a specific contact. 
        /// The new role is provided in the request body. 
        /// The roles can be 'read', 'write', or 'admin'. 
        /// If the note with the provided ID does not exist, or if the contact with the provided ID does not have access to the note, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="personId">The ID of the person (contact).</param>
        /// <param name="request">The request body containing the new role.</param>
        /// <returns>The updated access role of the shared note for the contact.</returns>
        /// <response code="200">Returns the updated access role of the shared note for the contact.</response>
        /// <response code="400">If the request body is invalid.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPut("{noteId}/sharing/{personId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<SharedWith> UpdateSharedNoteRoleAccess(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromRoute] Guid personId,
            [FromBody] UpdateSharedNoteAccessRequest request)
        {
            var command = new UpdateSharedNoteContactAccessCommand(IdentityContext, noteId, personId, request.Role);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes a shared note for a specific contact.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a shared note for a specific contact using the unique identifiers of the note and the contact. 
        /// Once the shared note is deleted, the contact will no longer have access to the note. 
        /// If the note with the provided ID does not exist, or if the contact with the provided ID does not have access to the note, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="personId">The ID of the person (contact).</param>
        /// <returns>The ID of the deleted shared note.</returns>
        /// <response code="200">Returns the ID of the deleted shared note.</response>
        /// <response code="404">If the shared note is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpDelete("{noteId}/sharing/{personId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Guid> DeleteSharedNoteContact(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromRoute] Guid personId)
        {
            var command = new DeleteSharedNoteContactCommand(IdentityContext, noteId, personId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Saves a form field for a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint saves a form field for a specific note. 
        /// The details of the form field, including the schema, type, and version, are provided in the request body. 
        /// If the form field is successfully saved, the details of the form field are returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="request">The request body containing the details of the form field to be saved.</param>
        /// <returns>The details of the saved form field.</returns>
        /// <response code="200">Returns the details of the saved form field.</response>
        /// <response code="400">If the request body is invalid.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost("{noteId}/form/fields")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<NoteFormField> SaveFormField(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromBody] SaveFormFieldRequest request)
        {
            var command = new SaveNoteFormFieldCommand(request.Id, noteId, request.Schema, request.Type, request.Version);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes a form field from a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a form field from a specific note using the unique identifiers of the note and the form field. 
        /// Once the form field is deleted, it cannot be recovered. 
        /// If the form field with the provided ID does not exist, or if the note with the provided ID does not contain the form field, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="id">The ID of the form field to be deleted.</param>
        /// <returns>The ID of the deleted form field.</returns>
        /// <response code="200">Returns the ID of the deleted form field.</response>
        /// <response code="404">If the form field is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpDelete("{noteId}/form/fields/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Guid> DeleteFormField(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid id,
            [FromRoute] Guid noteId)
        {
            var command = new DeleteNoteFormFieldCommand(IdentityContext, id, noteId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Saves a form response for a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint saves a form response for a specific note. 
        /// The details of the form response, including the form field ID and the response, are provided in the request body. 
        /// If the form response is successfully saved, the details of the form response are returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="request">The request body containing the details of the form response to be saved.</param>
        /// <returns>The details of the saved form response.</returns>
        /// <response code="200">Returns the details of the saved form response.</response>
        /// <response code="400">If the request body is invalid.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost("{noteId}/form/responses")]
        public async Task<NoteFormResponse> SaveFormResponse(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromBody] SaveNoteFormResponseRequest request)
        {
            var command = new SaveNoteFormResponseCommand(IdentityContext, request.Id, noteId, request.FormFieldId, request.Response);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Creates a summary for a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint creates a summary for a specific note. 
        /// The details of the summary, including the input content and whether to rephrase, are provided in the request body. 
        /// If the summary is successfully created, the details of the summary are returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="request">The request body containing the details of the summary to be created.</param>
        /// <returns>The details of the created summary.</returns>
        /// <response code="200">Returns the details of the created summary.</response>
        /// <response code="400">If the request body is invalid.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost("{noteId}/summaries")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<NoteSummaryResponse> CreateSummary(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromBody] CreateNoteSummaryRequest request)
        {
            var command = new CreateNoteSummaryCommand(IdentityContext, noteId, request.InputContent, request.Rephrase);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Fetches a list of summaries for a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint fetches a list of summaries for a specific note. 
        /// The summaries are returned in a paginated format, with a default limit and offset specified. 
        /// The limit and offset can be customized in the request parameters. 
        /// If the note with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="limit">The maximum number of summaries to return.</param>
        /// <param name="offset">The number of summaries to skip before starting to return summaries.</param>
        /// <returns>A paginated list of summaries.</returns>
        /// <response code="200">Returns the paginated list of summaries.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpGet("{noteId}/summaries")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<PaginatedResult<NoteSummary>> GetSummaries(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromQuery] int limit = QueryConstants.DefaultLimit,
            [FromQuery] int offset = QueryConstants.DefaultOffset)
        {
            var pagination = new PaginationRequest(limit, offset);
            var query = new GetNoteSummariesQuery(IdentityContext, noteId, pagination);
            var result = await mediator.Send(query);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Saves a signature form response for a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint saves a signature form response for a specific note. 
        /// The details of the signature form response, including the form field ID, the signature attachment, and the response, are provided in the request body. 
        /// If the signature form response is successfully saved, the details of the signature form response are returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="request">The request body containing the details of the signature form response to be saved.</param>
        /// <returns>The details of the saved signature form response.</returns>
        /// <response code="200">Returns the details of the saved signature form response.</response>
        /// <response code="400">If the request body is invalid.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost("{noteId}/form/responses/signature")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<NoteFormResponse> SaveSignatureFormResponse(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromBody] SaveSignatureResponseRequest request)
        {
            var command = new SaveNoteFormSignatureResponseCommand(IdentityContext, noteId, request);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Saves a signature for a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint saves a signature for a specific note. 
        /// The details of the signature, including the signature attachment and remarks, are provided in the request body. 
        /// If the signature is successfully saved, the details of the signature are returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="request">The request body containing the details of the signature to be saved.</param>
        /// <returns>The details of the saved signature.</returns>
        /// <response code="200">Returns the details of the saved signature.</response>
        /// <response code="400">If the request body is invalid.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost("{noteId}/signatures")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<NoteSignature> SaveSignature(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromBody] SaveNoteSignatureRequest request)
        {
            var command = new SaveNoteSignatureCommand(IdentityContext, providerId, noteId, request);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes a signature from a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a signature from a specific note using the unique identifiers of the note and the signature. 
        /// Once the signature is deleted, it cannot be recovered. 
        /// If the signature with the provided ID does not exist, or if the note with the provided ID does not contain the signature, a 404 status code is returned.
        /// </remarks>
        /// <param name="id">The ID of the signature.</param>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <returns>The ID of the deleted signature.</returns>
        /// <response code="200">Returns the ID of the deleted signature.</response>
        /// <response code="404">If the signature is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpDelete("{noteId}/signatures/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Guid> DeleteSignature(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid id,
            [FromRoute] Guid noteId)
        {
            var command = new DeleteNoteSignatureCommand(IdentityContext, id, providerId, noteId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Locks a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint locks a specific note, preventing any further modifications to it. 
        /// The note is identified by its unique ID. 
        /// If the note is successfully locked, the details of the locked note are returned. 
        /// If the note with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note to be locked.</param>
        /// <returns>The details of the locked note.</returns>
        /// <response code="200">If the note is successfully locked.</response>
        /// <response code="404">If the note is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost("{noteId}/lock")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<NoteDetail> LockNote(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId)
        {
            var command = new SetLockNoteCommand(IdentityContext, noteId, true);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Unlocks a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint unlocks a specific note, allowing further modifications to it. 
        /// The note is identified by its unique ID. 
        /// If the note is successfully unlocked, the details of the unlocked note are returned. 
        /// If the note with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note to be unlocked.</param>
        /// <returns>The details of the unlocked note.</returns>
        /// <response code="200">If the note is successfully unlocked.</response>
        /// <response code="404">If the note is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpDelete("{noteId}/lock")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<NoteDetail> UnlockNote(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId)
        {
            var command = new SetLockNoteCommand(IdentityContext, noteId, false);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes a form response from a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a form response from a specific note using the unique identifiers of the note and the form response. 
        /// Once the form response is deleted, it cannot be recovered. 
        /// If the form response with the provided ID does not exist, or if the note with the provided ID does not contain the form response, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="id">The ID of the form response.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <returns>The ID of the deleted form response.</returns>
        /// <response code="200">Returns the ID of the deleted form response.</response>
        /// <response code="404">If the form response is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpDelete("{noteId}/form/responses/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Guid> DeleteFormResponse(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid id,
            [FromRoute] Guid noteId)
        {
            var command = new DeleteNoteFormResponseCommand(id, noteId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes a signature form response from a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a signature form response from a specific note using the unique identifiers of the note and the signature form response. 
        /// Once the signature form response is deleted, it cannot be recovered. 
        /// If the signature form response with the provided ID does not exist, or if the note with the provided ID does not contain the signature form response, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="id">The ID of the signature form response.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <returns>The ID of the deleted signature form response.</returns>
        /// <response code="200">Returns the ID of the deleted signature form response.</response>
        /// <response code="404">If the signature form response is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpDelete("{noteId}/form/responses/signature/{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Guid> DeleteSignatureFormResponse(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid id,
            [FromRoute] Guid noteId)
        {
            var command = new DeleteNoteFormSignatureResponseCommand(id, noteId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Submits form responses for a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint submits form responses for a specific note. 
        /// The note is identified by its unique ID. 
        /// If the form responses are successfully submitted, the ID of the note is returned. 
        /// If the note with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <returns>The ID of the note for which responses were submitted.</returns>
        /// <response code="200">Returns the ID of the note for which responses were submitted.</response>
        /// <response code="404">If the note is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost("{noteId}/form/submit")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Guid> SubmitResponses(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId)
        {
            var command = new SubmitNoteFormResponsesCommand(IdentityContext, noteId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Adds attachments to a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint adds attachments to a specific note. 
        /// The note is identified by its unique ID. 
        /// The details of the attachments to be added are provided in the request body. 
        /// If the attachments are successfully added, the details of the added attachments are returned. 
        /// If the note with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="request">The request body containing the details of the attachments to be added.</param>
        /// <returns>The details of the added attachments.</returns>
        /// <response code="200">Returns the details of the added attachments.</response>
        /// <response code="400">If the request body is invalid.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPut("{noteId}/attachments")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<NoteAttachment[]> AddAttachments(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromBody] CreateNoteAttachmentsRequest request)
        {
            var noteAttachments = NoteUtilities.CreateNoteAttachments(request.Attachments);
            var command = new CreateNoteAttachmentsCommand(IdentityContext, noteId, noteAttachments.ToArray());
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes an attachment from a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes an attachment from a specific note using the unique identifiers of the note and the attachment. 
        /// Once the attachment is deleted, it cannot be recovered. 
        /// If the attachment with the provided ID does not exist, or if the note with the provided ID does not contain the attachment, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="fileId">The ID of the attachment.</param>
        /// <returns>The ID of the deleted attachment.</returns>
        /// <response code="200">Returns the ID of the deleted attachment.</response>
        /// <response code="404">If the attachment is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpDelete("{noteId}/attachments/{fileId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Guid> DeleteNoteAttachment(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromRoute] Guid fileId)
        {
            var command = new DeleteNoteAttachmentCommand(fileId, noteId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes a transcription from a note.
        /// </summary>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="transcriptionId">The ID of the transcription to delete.</param>
        /// <returns>A unit result indicating the operation result.</returns>
        /// <response code="200">Transcription deleted successfully.</response>
        /// <response code="404">Transcription not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpDelete("{noteId}/transcriptions/{transcriptionId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Unit> DeleteNoteTranscription(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromRoute] Guid transcriptionId)
        {
            var command = new DeleteNoteTranscriptionCommand(IdentityContext, noteId, transcriptionId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Retrieves an attachment from a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves an attachment from a specific note using the unique identifiers of the note and the attachment. 
        /// If the attachment with the provided ID exists and is associated with the note with the provided ID, the details of the attachment are returned. 
        /// If the attachment with the provided ID does not exist, or if the note with the provided ID does not contain the attachment, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="fileId">The ID of the attachment.</param>
        /// <returns>The details of the attachment.</returns>
        /// <response code="200">Returns the details of the attachment.</response>
        /// <response code="404">If the attachment is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpGet("{noteId}/attachments/{fileId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<NoteAttachment> GetNoteAttachment(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromRoute] Guid fileId)
        {
            var command = new GetNoteAttachmentQuery(fileId, noteId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Duplicates a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint duplicates a specific note. 
        /// The note is identified by its unique ID. 
        /// The flags `copyAttachments` and `copyTags` determine whether to copy attachments and tags respectively. 
        /// If the note is successfully duplicated, the details of the duplicated note are returned. 
        /// If the note with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="noteId">The ID of the note to be duplicated.</param>
        /// <param name="targetNoteId">The ID of the target note to be replaced. If not provided, a new note will be created.</param>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="copyAttachments">A flag indicating whether to copy attachments.</param>
        /// <param name="copyTags">A flag indicating whether to copy tags.</param>
        /// <returns>The details of the duplicated note.</returns>
        /// <response code="200">Returns the details of the duplicated note.</response>
        /// <response code="404">If the note is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost("{noteId}/duplicate")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<NoteDetail> DuplicateNote(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromQuery] Guid? targetNoteId = null,
            [FromQuery] bool copyAttachments = true,
            [FromQuery] bool copyTags = true)
        {
            var command = new DuplicateNoteCommand(IdentityContext, providerId, noteId, targetNoteId, copyAttachments, copyTags);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }


        /// <summary>
        /// Fetches the smart chips for provider, client, dxcode and appointments.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves the smart chips.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <returns>The smart chips.</returns>
        /// <response code="200">Returns the smart chips.</response>
        /// <response code="404">If the note or contact is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpGet("{noteId}/smartchips")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<IList<SmartChip>> GetNoteSmartChips(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromHeader(Name = "Accept-Time-Zone")] string timeZone,
            [FromHeader(Name = "Accept-Language")] string locale)
        {
            var command = new GetNoteSmartChipsQuery(IdentityContext, providerId, contactId, noteId, locale, timeZone);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Applies a template to a note.
        /// </summary>
        /// <remarks>
        /// This endpoint applies a specified template to a note. 
        /// The details of the template and the note are provided in the request body. 
        /// If the template is successfully applied, the details of the updated note are returned.
        /// If TranscriptionId is provided, TemplateId must also be provided.
        /// If TranscriptionId is null, either TemplateId or PublicTemplateId must be provided.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="request">The request containing the template details to be applied to the note.</param>
        /// <returns>The details of the updated note.</returns>
        /// <response code="200">Returns the details of the updated note if successful.</response>
        /// <response code="404">If the note or template is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost("apply-template")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<ActionResult<NoteDetail>> ApplyTemplateToNote(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromHeader(Name = "Accept-Time-Zone")] string timeZone,
            [FromHeader(Name = "Accept-Language")] string locale,
            [FromBody] ApplyTemplateToNoteRequest request)
        {
            if (request == null)
            {
                return BadRequest("Request body could not be deserialised");
            }
            
            var command = new ApplyTemplateToNoteCommand(IdentityContext, providerId, contactId, request.TemplateId, request.PublicTemplateId, request.ApplyTemplateOption ?? ApplyTemplateOption.Replace, request.TranscriptionId, null, locale, timeZone);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Applies a template to an existing note.
        /// </summary>
        /// <remarks>
        /// This endpoint applies a specified template to an existing note. 
        /// The details of the template and the note are provided in the request body. 
        /// If the template is successfully applied, the details of the updated note are returned.
        /// If TranscriptionId is provided, TemplateId must also be provided.
        /// If TranscriptionId is null, either TemplateId or PublicTemplateId must be provided.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note to which the template will be applied.</param>
        /// <param name="request">The request containing the template details to be applied to the note.</param>
        /// <returns>The details of the updated note.</returns>
        /// <response code="200">Returns the details of the updated note if successful.</response>
        /// <response code="404">If the note or template is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPut("{noteId}/apply-template")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<NoteDetail> ApplyTemplateToExistingNote(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromHeader(Name = "Accept-Time-Zone")] string timeZone,
            [FromHeader(Name = "Accept-Language")] string locale,
            [FromBody] ApplyTemplateToNoteRequest request)
        {
            var command = new ApplyTemplateToNoteCommand(IdentityContext, providerId, contactId, request.TemplateId, request.PublicTemplateId, request.ApplyTemplateOption ?? ApplyTemplateOption.Replace, request.TranscriptionId, noteId, locale, timeZone);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Applies an AI template to a specific note.
        /// </summary>
        /// <remarks>
        /// This endpoint applies a specified AI template to a note.
        /// The details of the template and the note are provided in the request body.
        /// If the template is successfully applied, the details of the updated note are returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="timeZone">The time zone to be used.</param>
        /// <param name="locale">The locale to be used.</param>
        /// <param name="request">The request containing the template details to be applied to the note.</param>
        /// <returns>The details of the updated note.</returns>
        /// <response code="200">Returns the details of the updated note if successful.</response>
        /// <response code="404">If the note or template is not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpPost("{noteId}/apply-ai-template")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<NoteDetail> ApplyAiTemplateToNote(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromHeader(Name = "Accept-Time-Zone")] string timeZone,
            [FromHeader(Name = "Accept-Language")] string locale,
            [FromBody] ApplyAiTemplateToNoteRequest request)
        {
            var command = new ApplyAiTemplateToNoteCommand(IdentityContext, providerId, contactId, request.TemplateId, request.TranscriptionId, noteId, request.ApplyTemplateOption ?? ApplyTemplateOption.Append, locale, timeZone);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Creates a new AI conversation.
        /// </summary>
        /// <param name="providerId">The provider ID.</param>
        /// <param name="noteId">The note ID</param>
        /// <param name="request">The request body</param>
        /// <returns>The created AI conversation.</returns>
        [HttpPost("{noteId}/ask-ai/conversations")]
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [Obsolete("Use Copilot Endpoints")]
        public async Task<CreateAiConversationResponse> CreateConversation([FromRoute] Guid providerId, [FromRoute] Guid noteId, [FromBody] AskAiRequest request)
        {
            request = request with
            {
                Contexts = request.Contexts != null
                    ? new List<AiMessageContextRequest>(request.Contexts) // Copy existing items
                    {
                        new AiMessageContextRequest
                        {
                            Entity = AiMessageContextEntityType.Note,
                            Type = AiMessageContextType.Reference,
                            Value = noteId.ToString()
                        }
                    }
                    : new List<AiMessageContextRequest> // If null, initialize with a new list
                    {
                        new AiMessageContextRequest
                        {
                            Entity = AiMessageContextEntityType.Note,
                            Type = AiMessageContextType.Reference,
                            Value = noteId.ToString()
                        }
                    }
            };

            var command = new CreateAiConversationCommand(IdentityContext, request.Text, request.Contexts, IdentityContext.ProviderPermissions.ProviderId, null);

            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Get AI conversations.
        /// </summary>
        /// <param name="providerId">The provider ID.</param>
        /// <param name="noteId">The note ID</param>
        /// <param name="pagination">The pagination token</param>
        /// <param name="limit">The pagination limit</param>
        /// <returns>AI conversations.</returns>
        [HttpGet("{noteId}/ask-ai/conversations")]
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [Obsolete("Use Copilot Endpoints")]
        public async Task<TokenisedPaginatedResult<AiConversation>> GetConversations(
            [FromRoute] Guid providerId,
            [FromRoute] Guid noteId,
            [FromQuery] string pagination = null,
            [FromQuery] int limit = QueryConstants.DefaultLimit)
        {
            var command = new GetAiConversationsQuery(IdentityContext, providerId, noteId, pagination, limit);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Get AI conversation.
        /// </summary>
        /// <param name="providerId">The provider ID.</param>
        /// <param name="conversationId">The ai conversaion ID</param>
        /// <param name="noteId">The note ID</param>
        /// <returns>The AI conversation.</returns>
        [HttpGet("{noteId}/ask-ai/conversations/{conversationId}")]
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [Obsolete("Use Copilot Endpoints")]
        public async Task<AiConversation> GetAiConversation([FromRoute] Guid providerId, [FromRoute] Guid conversationId, [FromRoute] Guid noteId)
        {
            var command = new GetAiConversationByIdQuery(conversationId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Get AI conversation.
        /// </summary>
        /// <param name="providerId">The provider ID.</param>
        /// <param name="conversationId">The ai conversaion ID</param>
        /// <param name="noteId">The note ID</param>
        /// <returns>The AI conversation.</returns>
        [HttpGet("{noteId}/copilot/conversations/{conversationId}")]
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        public async Task<AiConversation> GetCopilotConversation([FromRoute] Guid providerId, [FromRoute] Guid conversationId, [FromRoute] Guid noteId)
        {
            var command = new GetAiConversationByIdQuery(conversationId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes a copilot conversation from a note.
        /// </summary>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="contactId">The ID of the contact.</param>
        /// <param name="noteId">The ID of the note.</param>
        /// <param name="conversationId">The ID of the conversation to delete.</param>
        /// <returns>A unit result indicating the operation result.</returns>
        /// <response code="200">Conversation deleted successfully.</response>
        /// <response code="404">Conversation not found.</response>
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [HttpDelete("{noteId}/copilot/conversations/{conversationId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Unit> DeleteNoteAiConversation(
            [FromRoute] Guid providerId,
            [FromRoute] Guid contactId,
            [FromRoute] Guid noteId,
            [FromRoute] Guid conversationId)
        {
            var command = new DeleteNoteAiConversationCommand(IdentityContext, noteId, conversationId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Creates a new AI message.
        /// </summary>
        /// <param name="providerId">The provider ID.</param>
        /// <param name="noteId"></param>
        /// <param name="conversationId">The ai conversation ID</param>
        /// <param name="request">The request body</param>
        /// <returns>The created AI conversation message.</returns>
        [HttpPost("{noteId}/ask-ai/conversations/{conversationId}/messages")]
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        [Obsolete("Use Copilot Endpoints")]
        public async Task<AskAiResponse> AskAi([FromRoute] Guid providerId, [FromRoute] Guid noteId, [FromRoute] Guid conversationId, [FromBody] AskAiRequest request)
        {
            request = request with
            {
                Contexts = request.Contexts != null
                   ? new List<AiMessageContextRequest>(request.Contexts) // Copy existing items
                   {
                        new AiMessageContextRequest
                        {
                            Entity = AiMessageContextEntityType.Note,
                            Type = AiMessageContextType.Reference,
                            Value = noteId.ToString()
                        }
                   }
                   : new List<AiMessageContextRequest> // If null, initialize with a new list
                   {
                        new AiMessageContextRequest
                        {
                            Entity = AiMessageContextEntityType.Note,
                            Type = AiMessageContextType.Reference,
                            Value = noteId.ToString()
                        }
                   }
            };

            var command = new AskAiCommand(IdentityContext, conversationId, request.Text, request.Contexts);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Get AI messages.
        /// </summary>
        /// <param name="providerId">The provider ID.</param>
        /// <param name="conversationId">The ai conversation ID</param>
        /// <param name="pagination">The pagination token</param>
        /// <param name="limit">The pagination limit</param>
        /// <returns>the paginated messages.</returns>
        [HttpGet("{noteId}/ask-ai/conversations/{conversationId}/messages")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [Obsolete("Use Copilot Endpoints")]
        public async Task<TokenisedPaginatedResult<AiMessage>> GetMessages(
            [FromRoute] Guid providerId,
            [FromRoute] Guid conversationId,
            [FromQuery] string pagination = null,
            [FromQuery] int limit = QueryConstants.DefaultLimit)
        {
            var command = new GetAiMessagesQuery(conversationId, pagination, limit);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Get AI messages.
        /// </summary>
        /// <param name="providerId">The provider ID.</param>
        /// <param name="conversationId">The ai conversation ID</param>
        /// <param name="pagination">The pagination token</param>
        /// <param name="limit">The pagination limit</param>
        /// <returns>the paginated messages.</returns>
        [HttpGet("{noteId}/copilot/conversations/{conversationId}/messages")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<TokenisedPaginatedResult<AiMessage>> GetCopilotMessages(
            [FromRoute] Guid providerId,
            [FromRoute] Guid conversationId,
            [FromQuery] string pagination = null,
            [FromQuery] int limit = QueryConstants.DefaultLimit)
        {
            var command = new GetAiMessagesQuery(conversationId, pagination, limit);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }
        
        /// <summary>
        /// Get AI conversations.
        /// </summary>
        /// <param name="providerId">The provider ID.</param>
        /// <param name="noteId">The note ID</param>
        /// <param name="pagination">The pagination token</param>
        /// <param name="limit">The pagination limit</param>
        /// <returns>AI conversations.</returns>
        [HttpGet("{noteId}/copilot/conversations")]
        [Authorize(Policy = AuthPolicy.HasClientDocumentationEditForContact)]
        public async Task<TokenisedPaginatedResult<AiConversation>> GetCopilotConversations(
            [FromRoute] Guid providerId,
            [FromRoute] Guid noteId,
            [FromQuery] string pagination = null,
            [FromQuery] int limit = QueryConstants.DefaultLimit)
        {
            var command = new GetAiConversationsQuery(IdentityContext, providerId, noteId, pagination, limit);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }
    }
}