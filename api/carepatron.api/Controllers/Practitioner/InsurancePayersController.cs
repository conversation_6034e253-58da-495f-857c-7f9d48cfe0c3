using carepatron.api.Constants;
using carepatron.api.Contracts.Requests.Insurance;
using carepatron.core.Application.Insurance.Commands;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Insurance.Queries;
using carepatron.core.Identity;
using carepatron.core.Models.Pagination;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using carepatron.core.Models.Execution;

namespace carepatron.api.Controllers.Practitioner;

[ControllerProperties(CodeOwner.BillingAndPayments)]
[Route("api/providers/{providerId}/payers")]
public class InsurancePayersController(
    IIdentityContextFactory identityContextFactory,
    IMediator mediator
) : BaseController(identityContextFactory)
{
    [HttpGet]
    [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
    public async Task<PaginatedResult<ProviderInsurancePayer>> GetPayers(
        [FromRoute] Guid providerId,
        [FromQuery] string search = "",
        [FromQuery] int limit = QueryConstants.DefaultLimit,
        [FromQuery] int offset = QueryConstants.DefaultOffset)
    {
        var pagination = new PaginationRequest(limit, offset);
        var query = new GetProviderInsurancePayersQuery(IdentityContext, providerId, search?.Trim(), pagination);
        var result = await mediator.Send(query);
        return ExecutionResult(result);
    }

    [HttpGet("{id}")]
    [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
    public async Task<ProviderInsurancePayer> GetPayerById([FromRoute] Guid id)
    {
        var command = new GetProviderInsurancePayerByIdQuery(
            id,
            IdentityContext.ProviderPermissions.ProviderId
        );
        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpPost]
    [Authorize(Policy = AuthPolicy.HasWorkspaceEditAccess)]
    public async Task<ProviderInsurancePayer> Create([FromBody] SaveInsurancePayerRequest request)
    {
        var command = new CreateInsurancePayerCommand(
            IdentityContext.ProviderPermissions.ProviderId,
            request.PayerId,
            request.Name,
            request.PhoneNumber,
            request.CoverageType,
            request.OtherCoverageTypeName,
            request.Address
        );

        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpPut("{id}")]
    [Authorize(Policy = AuthPolicy.HasWorkspaceEditAccess)]
    public async Task<ProviderInsurancePayer> Update(Guid id, [FromBody] SaveInsurancePayerRequest request)
    {
        var command = new UpdateInsurancePayerCommand(
            IdentityContext.ProviderPermissions.ProviderId,
            id,
            request.PayerId,
            request.Name,
            request.PhoneNumber,
            request.CoverageType,
            request.OtherCoverageTypeName,
            request.Address
        );

        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = AuthPolicy.HasWorkspaceEditAccess)]
    public async Task<Unit> Delete(Guid id)
    {
        var command = new DeleteInsurancePayerCommand(IdentityContext.ProviderPermissions.ProviderId, id);
        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpGet("available")]
    [Authorize(Policy = AuthPolicy.HasWorkspaceEditAccess)]
    public async Task<PaginatedResult<AvailableInsurancePayer>> GetAvailablePayers(
        [FromRoute] Guid providerId,
        [FromQuery] string searchTerm = null,
        [FromQuery] string[] state = null,
        [FromQuery] int limit = QueryConstants.DefaultLimit,
        [FromQuery] int offset = QueryConstants.DefaultOffset)
    {
        var pagination = new PaginationRequest(limit, offset);
        var query = new GetAvailableInsurancePayersQuery(IdentityContext, providerId, searchTerm?.Trim(), state, pagination);
        var result = await mediator.Send(query);
        return ExecutionResult(result);
    }

    [HttpPost("import")]
    [Authorize(Policy = AuthPolicy.HasWorkspaceEditAccess)]
    public async Task<ProviderInsurancePayer[]> ImportPayers(
        [FromRoute] Guid providerId,
        [FromBody] ImportInsurancePayersRequest request)
    {
        var command = new ImportInsurancePayersCommand(providerId, request.Payers);
        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpPost("{id}/enrollments")]
    [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
    public async Task<EnrollmentTransactionStatus> CreateEnrollment(Guid id, [FromBody] CreateInsurancePayerEnrollmentRequest request)
    {
        var command = new CreateInsurancePayerEnrollmentCommand(
            IdentityContext,
            IdentityContext.ProviderPermissions.ProviderId,
            new SaveInsurancePayerEnrollmentRequest
            {
                ProviderInsurancePayerId = id,
                BillingProfileId = request.BillingProfileId,
                Transaction = request.Transaction
            }
        );
        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpGet("{id}/enrollments")]
    [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
    public async Task<CollectionResult<PayerEnrollment>> GetPayerEnrolments(
    Guid id)
    {
        var query = new GetPayerEnrollmentsQuery(IdentityContext.ProviderPermissions.ProviderId, id, IdentityContext);
        var result = await mediator.Send(query);
        return ExecutionResult(result);
    }

    [HttpPost("{id}/enrollments/{payerEnrollmentId}/{transactionType}/link")]
    [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
    public async Task<EnrollmentTransactionStatus> CreateEnrollmentLink(
        Guid id,
        [FromRoute] Guid payerEnrollmentId,
        [FromRoute] PayerTransactionType transactionType
    )
    {
        var command = new GetInsurancePayerEnrollmentLinkQuery(
            IdentityContext.ProviderPermissions.ProviderId,
            new GetInsurancePayerEnrollmentLinkRequest
            {
                InsurancePayerEnrollmentId = payerEnrollmentId,
                TransactionType = transactionType
            }
        );

        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }
}
