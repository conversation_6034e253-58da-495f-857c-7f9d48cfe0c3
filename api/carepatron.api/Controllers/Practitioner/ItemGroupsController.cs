﻿using carepatron.api.Constants;
using carepatron.api.Contracts.Requests.Items;
using carepatron.core.Application.Workspace.ServiceItems.Commands;
using carepatron.core.Application.Workspace.ServiceItems.Models;
using carepatron.core.Application.Workspace.ServiceItems.Queries;
using carepatron.core.Identity;
using carepatron.core.Models.Pagination;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.Practitioner
{
    [ControllerProperties(CodeOwner.TasksAndScheduling)]
    [Route("api/providers/{providerId}/itemGroups")]
    public class ItemGroupsController : BaseController
    {
        private readonly IMediator mediator;

        public ItemGroupsController(
             IIdentityContextFactory identityContextFactory,
             IMediator mediator)
             : base(identityContextFactory)
        {
            this.mediator = mediator;
        }

        [HttpPost]
        [Authorize(Policy = AuthPolicy.HasWorkspaceEditAccess)]
        public async Task<ProviderItemGroup> Create([FromBody] SaveProviderItemGroupRequest request)
        {
            var command = new CreateProviderItemGroupCommand(IdentityContext, request.Name, IdentityContext.ProviderPermissions.ProviderId, request.ItemIds, request.Order);

            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }


        [HttpPost("reorder")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceEditAccess)]
        public async Task<Unit> Reorder([FromRoute] Guid providerId, [FromBody] ProviderItemGroup[] groups)
        {
            var command = new ReorderProviderItemGroupsCommand(IdentityContext.ProviderPermissions.ProviderId, groups ?? Array.Empty<ProviderItemGroup>());

            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [HttpPut("{providerItemGroupId}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceEditAccess)]
        public async Task<ProviderItemGroup> Update([FromRoute] Guid providerItemGroupId, [FromBody] SaveProviderItemGroupRequest request)
        {
            var command = new UpdateProviderItemGroupCommand(IdentityContext, providerItemGroupId, request.Name, request.ItemIds);

            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [HttpDelete("{providerItemGroupId}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceEditAccess)]
        public async Task<Guid> Delete([FromRoute] Guid providerItemGroupId)
        {
            var command = new DeleteProviderItemGroupCommand(IdentityContext, providerItemGroupId);

            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        [HttpGet]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<PaginatedResult<ProviderItemGroup>> Get([FromRoute] Guid providerId, string searchTerm, int limit = 100, int offset = 0)
        {
            var query = new GetProviderItemGroupsQuery(IdentityContext, providerId, searchTerm, limit, offset);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }
        
        [HttpGet("{providerItemGroupId}/items")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<CollectionResult<ProviderItem>> GetItems([FromRoute] Guid providerId, [FromRoute] Guid providerItemGroupId, string searchTerm)
        {
            var query = new GetItemsByGroupQuery(providerId, providerItemGroupId, searchTerm);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        [HttpPost("items/{itemId}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceEditAccess)]
        public async Task<Unit> SaveItemToItemGroups([FromRoute] Guid providerId, [FromRoute] Guid itemId, [FromBody] SaveItemToItemGroupsRequest request)
        {
            var command = new SaveItemToItemGroupsCommand(providerId, itemId, request.ItemGroupsToCreate ?? [], request.ItemGroupsToDelete ?? []);

            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

    }
}