using System;
using System.Threading.Tasks;
using carepatron.api.Constants;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Identity;
using carepatron.core.Models.Pagination;
using carepatron.api.Contracts.Requests.Contacts;
using carepatron.core.Application.Contacts.Commands;
using carepatron.core.Application.Contacts.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace carepatron.api.Controllers.Practitioner
{
    [ControllerProperties(CodeOwner.Clients)]
    [Route("api/providers/{providerId}/contacts/imports")]
    public class ContactImportsController : BaseController
    {
        private readonly IMediator mediator;

        public ContactImportsController(IIdentityContextFactory identityContextFactory, IMediator mediator) : base(identityContextFactory)
        {
            this.mediator = mediator;
        }

        [HttpPost]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<ContactImportSummary> Import([FromRoute] Guid providerId, [FromBody] ContactImportRequest request)
        {
            var command = new ContactImportCommand(
                providerId,
                IdentityContext.PersonId,
                request.ImportSource,
                request.ImportFileId,
                request.FileName,
                request.FileExtension,
                request.FileSize,
                request.MappedColumns,
                request.DataSchema,
                request.Status,
                request.ImportType,
                request.IsContact);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        [HttpGet("summary")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<CollectionResult<ContactImportSummary>> GetContactImportSummaries([FromRoute] Guid providerId,
            [FromQuery] DateTime? fromDate,
            [FromQuery] DateTime? toDate,
            [FromQuery] ImportSummaryStatus[] status,
            [FromQuery] bool isContact = false)
        {
            var result = await mediator.Send(new GetContactImportSummariesQuery(providerId, fromDate, toDate, status ?? [], isContact));
            return ExecutionResult(result);
        }

        [HttpPut("summary/{id}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<ContactImportSummary> UpdateContactImportSummary([FromRoute] Guid providerId, [FromRoute] Guid id, [FromBody] UpdateContactImportSummaryRequest request)
        {
            var command = new UpdateContactImportSummaryCommand(providerId,
                IdentityContext.PersonId,
                id, 
                request.LastStatusSeenBy ?? Array.Empty<Guid>(),
                request.Status,
                request.MappedColumns ?? []);
            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        [HttpPost]
        [Route("mappings")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<ImportContactMappingsResponse> GetImportContactMappings([FromRoute] Guid providerId, [FromBody] GetImportContactMappingsRequest request)
        {
            var query = new GetImportContactMappingsQuery(providerId, request.FileId, request.FileName);
            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        [HttpPut]
        [Route("schemas")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<string> UpdateImportContactSchemas([FromRoute] Guid providerId, [FromBody] UpdateImportContactSchemasRequest request)
        {
            var command = new UpdateImportContactSchemasCommand(providerId,
                IdentityContext.PersonId,
                request.FileId,
                request.FileName,
                request.ImportOptions,
                request.DataSchema,
                request.LayoutSchema);
            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }
    }
}