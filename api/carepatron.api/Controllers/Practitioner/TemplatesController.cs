﻿using carepatron.api.Constants;
using carepatron.api.Contracts.Requests;
using carepatron.api.Contracts.Requests.Notes;
using carepatron.api.Contracts.Requests.Templates;
using carepatron.api.Extensions;
using carepatron.api.Mappers;
using carepatron.core.Application.Templates.Commands;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Application.Templates.Queries;
using carepatron.core.Identity;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Pagination;
using carepatron.core.Paging.Models;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace carepatron.api.Controllers
{
    /// <summary>
    /// Controller for managing templates.
    /// </summary>
    [ControllerProperties(CodeOwner.NotesAndDocuments)]
    [Route("api/providers/{providerId}/templates")]
    public class TemplatesController : BaseController
    {
        private readonly IMediator mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="TemplatesController"/> class.
        /// </summary>
        /// <param name="identityContextFactory">The identity context factory.</param>
        /// <param name="mediator">The mediator.</param>
        public TemplatesController(
            IIdentityContextFactory identityContextFactory,
            IMediator mediator)
            : base(identityContextFactory)
        {
            this.mediator = mediator;
        }

        /// <summary>
        /// Fetches a specific template by its ID.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves a specific template by its ID. 
        /// If the template is successfully retrieved, the template is returned. 
        /// If the template with the provided ID does not exist, a 404 status code is returned.
        /// If the requesting user has no access to the resource, a 403 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template.</param>
        /// <returns>The requested template.</returns>
        /// <response code="200">Returns the requested template.</response>
        [HttpGet("{id}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<Template> GetTemplate([FromRoute] Guid providerId, [FromRoute] Guid id)
        {
            var query = new GetTemplateQuery(IdentityContext, id);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Fetches a set of template collections.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves a collection of templates. 
        /// The maximum number of templates to return and the number of templates to skip before starting to return templates can be specified. 
        /// If the templates are successfully retrieved, the collection of templates is returned. 
        /// If the requesting user has no access to the resource, a 403 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="limit">The maximum number of templates to return.</param>
        /// <param name="offset">The number of templates to skip before starting to return templates.</param>
        /// <returns>A collection of templates.</returns>
        /// <response code="200">Returns the collection of templates.</response>
        [HttpGet("collections")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<PaginatedResult<TemplateCollection>> GetCollections([FromRoute] Guid providerId,
            [FromQuery] int limit = 100, [FromQuery] int offset = 0)
        {
            var query = new GetTemplateCollectionsQuery(IdentityContext, IdentityContext.ProviderPermissions.ProviderId,
                limit, offset);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Create a new template collection.
        /// </summary>
        /// <remarks>
        /// This endpoint creates a new template collection. 
        /// The details of the template collection to create are provided in the request. 
        /// If the template collection is successfully created, the created template collection is returned. 
        /// If the requesting user has no access to the resource, a 403 status code is returned.
        /// If the request is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="request">The details of the template collection to create.</param>
        /// <returns>The created template collection.</returns>
        /// <response code="200">Returns the newly created template collection.</response>
        [HttpPost("collections")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<TemplateCollection> CreateCollection([FromRoute] Guid providerId,
            [FromBody] SaveTemplateCollectionRequest request)
        {
            var command = new CreateTemplateCollectionCommand(IdentityContext,
                IdentityContext.ProviderPermissions.ProviderId, request.Title);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Update a specific template collection.
        /// </summary>
        /// <remarks>
        /// This endpoint updates a specific template collection with the provided details. 
        /// If the template collection is successfully updated, the updated template collection is returned. 
        /// If the template collection with the provided ID does not exist, a 404 status code is returned.
        /// If the requesting user has no access to the resource, a 403 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template collection to update.</param>
        /// <param name="request">The details of the template collection to update.</param>
        /// <returns>The updated template collection.</returns>
        /// <response code="200">Returns the updated template collection.</response>
        [HttpPut("collections/{id}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<TemplateCollection> UpdateCollection([FromRoute] Guid providerId, [FromRoute] Guid id,
            [FromBody] SaveTemplateCollectionRequest request)
        {
            var command = new UpdateTemplateCollectionCommand(IdentityContext, id, request.Title);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Delete a specific template collection.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a specific template collection. 
        /// If the template collection is successfully deleted, the ID of the deleted template collection is returned. 
        /// If the template collection with the provided ID does not exist, a 404 status code is returned.
        /// If the requesting user has no access to the resource, a 403 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template collection to delete.</param>
        /// <returns>The ID of the deleted template collection.</returns>
        /// <response code="200">Returns the ID of the deleted template collection.</response>
        [HttpDelete("collections/{id}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<Guid> DeleteCollection([FromRoute] Guid providerId, [FromRoute] Guid id)
        {
            var command = new DeleteTemplateCollectionCommand(IdentityContext, id);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Create a new template.
        /// </summary>
        /// <remarks>
        /// This endpoint creates a new template with the provided details. 
        /// If the template is successfully created, the created template is returned. 
        /// If the requesting user has no access to the resource, a 403 status code is returned.
        /// If the request is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="request">The details of the template to create.</param>
        /// <returns>The created template.</returns>
        /// <response code="200">Returns the newly created template.</response>
        [HttpPost]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<Template>> CreateTemplate([FromRoute] Guid providerId, [FromBody] SaveTemplateRequest request)
        {
            if (request == null)
            {
                return BadRequest("Request body could not be deserialised");
            }
            
            var command = new CreateTemplateCommand(IdentityContext,
                request.TemplateCollectionId,
                request.Title,
                request.Content,
                request.ContentJson,
                request.Description,
                request.CopiedFromTemplateId,
                request.Tags,
                request.ShareToCommunity,
                request.Type,
                request.Collection,
                request.FolderId);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Duplicate a specific template.
        /// </summary>
        /// <remarks>
        /// This endpoint duplicates a specific template. 
        /// The ID of the template to duplicate and a flag indicating whether to copy tags are provided in the request. 
        /// If the template is successfully duplicated, the duplicated template is returned. 
        /// If the template with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template to duplicate.</param>
        /// <param name="copyTags">A flag indicating whether to copy tags.</param>
        /// <returns>The duplicated template.</returns>
        /// <response code="200">Returns the duplicated template.</response>
        /// <response code="404">If the template is not found.</response>
        [HttpPost("{id}/duplicate")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Template> DuplicateTemplate([FromRoute] Guid providerId, [FromRoute] Guid id,
            [FromQuery] bool copyTags = true)
        {
            var command = new DuplicateTemplateCommand(IdentityContext, id, copyTags);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Copies a public template to a private template.
        /// </summary>
        /// <remarks>
        /// This endpoint copies a specified public template to a private template for a provider. 
        /// The details of the public template to be copied are provided in the request body. 
        /// If the copy operation is successful, the details of the new private template are returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="request">The request containing the details of the public template to be copied.</param>
        /// <returns>The details of the new private template.</returns>
        /// <response code="200">Returns the details of the new private template if successful.</response>
        /// <response code="404">If the public template is not found.</response>
        [HttpPost("duplicate")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Template> CopyPublicTemplateToTemplate([FromRoute] Guid providerId,
            [FromBody] CreateTemplateFromPublicTemplateRequest request)
        {
            var command =
                new CreateTemplateFromPublicTemplateCommand(IdentityContext, providerId, request.PublicTemplateId);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Imports a template from a file.
        /// </summary>
        /// <remarks>
        /// This endpoint allows importing a template using a file ID. 
        /// The file ID is provided in the request body, and the imported template is returned upon success. 
        /// If the file is not found, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="request">The request containing the file ID for the template to import.</param>
        /// <returns>The imported template.</returns>
        /// <response code="200">Returns the imported template id.</response>
        /// <response code="404">If the file is not found.</response>
        [HttpPost("imports")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<TemplateImport> ImportTemplate([FromRoute] Guid providerId,
            [FromBody] ImportTemplateRequest request)
        {
            var command = new ImportTemplateCommand(IdentityContext, providerId, request.FileId, request.FileName);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Retrieves a paginated list of template imports for a specific provider.
        /// </summary>
        /// <remarks>
        /// This endpoint fetches a paginated list of template imports based on the provided parameters.
        /// The results can be filtered by the creator's ID and paginated using a tokenized pagination approach.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="pagination">The pagination token for retrieving the next set of results (optional).</param>
        /// <param name="limit">The maximum number of results to return (default is defined in QueryConstants).</param>
        /// <param name="createdByPersonId">The ID of the person who created the template imports (optional).</param>
        /// <returns>A tokenized paginated result containing the template imports.</returns>
        /// <response code="200">Returns the paginated list of template imports.</response>
        /// <response code="404">If no template imports are found.</response>
        [HttpGet("imports")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<TokenisedPaginatedResult<TemplateImport>> GetTemplateImports(
            [FromRoute] Guid providerId,
            [FromQuery] string pagination = null,
            [FromQuery] int limit = QueryConstants.DefaultLimit,
            [FromQuery] Guid? createdByPersonId = null)
        {
            // Create a query object to fetch template imports with the specified parameters.
            var query = new GetTemplateImportsQueryQuery(providerId, createdByPersonId, pagination, limit);

            // Send the query to the mediator for processing and retrieve the result.
            var result = await mediator.Send(query);

            // Return the execution result, which includes the paginated list of template imports.
            return ExecutionResult(result);
        }

        /// <summary>
        /// Fetches the details of a specific template import by its ID.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves the details of a template import using the provided template import ID. 
        /// If the template import is successfully retrieved, the details are returned. 
        /// If the template import with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="templateImportId">The ID of the template import to retrieve.</param>
        /// <returns>The details of the requested template import.</returns>
        /// <response code="200">Returns the details of the requested template import.</response>
        /// <response code="404">If the template import is not found.</response>
        [HttpGet("imports/{templateImportId}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<TemplateImport> GetImportTemplate([FromRoute] Guid providerId, [FromRoute] Guid templateImportId)
        {
            var query = new GetTemplateImportQuery(IdentityContext, templateImportId);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Retrieves the status of template imports for a specific provider.
        /// </summary>
        /// <remarks>
        /// This endpoint fetches the current status of all template imports associated with the specified provider.
        /// It provides an overview of ongoing, completed, or failed imports.
        /// If no imports are found for the provider, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The unique identifier of the provider.</param>
        /// <returns>The status of template imports for the provider.</returns>
        /// <response code="200">Returns the status of template imports.</response>
        /// <response code="404">If no template imports are found for the provider.</response>
        [HttpGet("imports/status")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<GetTemplateImportsStatusResponse> GetImportTemplatesStatus([FromRoute] Guid providerId)
        {
            var query = new GetTemplateImportsStatusQuery(IdentityContext, providerId);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes a specific template import by its ID.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a template import identified by the provided template import ID.
        /// If the deletion is successful, a status indicating the result of the operation is returned.
        /// If the template import with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="templateImportId">The ID of the template import to delete.</param>
        /// <returns>A status indicating the result of the operation.</returns>
        /// <response code="200">Indicates that the template import was successfully deleted.</response>
        /// <response code="404">If the template import is not found.</response>
        [HttpDelete("imports/{templateImportId}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Unit> DeleteImportTemplate([FromRoute] Guid providerId, [FromRoute] Guid templateImportId)
        {
            var command = new DeleteTemplateImportCommand(IdentityContext, templateImportId);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Cancels a specific template import by its ID.
        /// </summary>
        /// <remarks>
        /// This endpoint allows canceling an ongoing template import operation. 
        /// If the cancellation is successful, a status indicating the result of the operation is returned. 
        /// If the template import with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="templateImportId">The ID of the template import to cancel.</param>
        /// <returns>A status indicating the result of the operation.</returns>
        /// <response code="200">Indicates that the template import was successfully canceled.</response>
        /// <response code="404">If the template import is not found.</response>
        [HttpPost("imports/{templateImportId}/cancel")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<Unit> CancelImportTemplate([FromRoute] Guid providerId, [FromRoute] Guid templateImportId)
        {
            var command = new CancelTemplateImportCommand(IdentityContext, templateImportId);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Retries a previously failed template import operation.
        /// </summary>
        /// <remarks>
        /// This endpoint allows retrying a template import that previously failed. 
        /// The ID of the template import to retry is provided in the route parameters. 
        /// If the retry operation is successful, the details of the retried template import are returned. 
        /// If the template import with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="templateImportId">The ID of the template import to retry.</param>
        /// <returns>The retried template import details.</returns>
        /// <response code="200">Returns the retried template import details.</response>
        /// <response code="404">If the template import is not found.</response>
        [HttpPost("imports/{templateImportId}/retry")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<TemplateImport> RetryImportTemplate([FromRoute] Guid providerId, [FromRoute] Guid templateImportId)
        {
            // Create a command to retry the template import with the specified ID.
            var command = new RetryImportTemplateCommand(IdentityContext, templateImportId);

            // Send the command to the mediator for processing and retrieve the result.
            var result = await mediator.Send(command);

            // Return the execution result, which includes the retried template import details.
            return ExecutionResult(result);
        }

        /// <summary>
        /// Update a specific template.
        /// </summary>
        /// <remarks>
        /// This endpoint updates a specific template with the provided details. 
        /// If the template is successfully updated, the updated template is returned. 
        /// If the template with the provided ID does not exist, a 404 status code is returned.
        /// If the requesting user has no access to the resource, a 403 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template to update.</param>
        /// <param name="request">The details of the template to update.</param>
        /// <returns>The updated template.</returns>
        /// <response code="200">Returns the updated template.</response>
        /// <response code="404">If the template is not found.</response>
        /// <response code="403">If the requesting user has no access to the template.</response>
        [HttpPut("{id}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<Template> UpdateTemplate([FromRoute] Guid providerId, [FromRoute] Guid id,
            [FromBody] SaveTemplateRequest request)
        {
            var command = new UpdateTemplateCommand(IdentityContext, id, request);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Delete a specific template.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a specific template. 
        /// If the template is successfully deleted, the ID of the deleted template is returned. 
        /// If the template with the provided ID does not exist, a 404 status code is returned.
        /// If the requesting user has no access to the resource, a 403 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template to delete.</param>
        /// <returns>The ID of the deleted template.</returns>
        /// <response code="200">Returns the ID of the deleted template.</response>
        /// <response code="404">If the template is not found.</response>
        /// <response code="403">If the requesting user has no access to the template.</response>
        [HttpDelete("{id}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<Guid> DeleteTemplate([FromRoute] Guid providerId, [FromRoute] Guid id)
        {
            var command = new DeleteTemplateCommand(IdentityContext, id);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Fetches a set of templates.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves a collection of templates. 
        /// The collection of templates to return, the search term to filter templates, the tags to filter templates, 
        /// the maximum number of templates to return, and the number of templates to skip before starting to return templates can be specified. 
        /// If the templates are successfully retrieved, the collection of templates is returned. 
        /// If the requesting user has no access to the resource, a 403 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="collection">The collection of templates to return.</param>
        /// <param name="searchTerm">The search term to filter templates.</param>
        /// <param name="tags">The tags to filter templates.</param>
        /// <param name="limit">The maximum number of templates to return.</param>
        /// <param name="offset">The number of templates to skip before starting to return templates.</param>
        /// <param name="hasAiPrompts"></param>
        /// <returns>A collection of templates.</returns>
        /// <response code="200">Returns the collection of templates.</response>
        [HttpGet]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<PaginatedResult<Template>> GetTemplates(
            [FromRoute] Guid providerId,
            [FromQuery] string[] collection = null,
            [FromQuery] string searchTerm = null,
            [FromQuery] Guid[] tags = null,
            [FromQuery] int limit = 20,
            [FromQuery] int offset = 0,
            [FromQuery] bool? hasAiPrompts = null)
        {
            var query = new GetTemplatesQuery(IdentityContext.ProviderPermissions.ProviderId,
                searchTerm,
                tags ?? Array.Empty<Guid>(),
                collection ?? Array.Empty<string>(),
                limit,
                offset,
                hasAiPrompts);
            var result = await mediator.Send(query);
            return ExecutionResult(result);
        }

        [HttpGet("v2")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<PaginatedResult<SimpleTemplate>> GetTemplatesV2(
            [FromRoute] Guid providerId,
            [FromQuery] string[] collection = null,
            [FromQuery] string searchTerm = null,
            [FromQuery] Guid[] tags = null,
            [FromQuery] int limit = 20,
            [FromQuery] int offset = 0,
            [FromQuery] bool? hasAiPrompts = null,
            [FromQuery] bool? favoritesOnly = null,
            [FromQuery] UnifiedSearchSortBy? sortBy = UnifiedSearchSortBy.Title,
            [FromQuery] Guid? templatesFolderId = null)
        {
            var query = new GetTemplatesV2Query(IdentityContext,
                searchTerm,
                tags ?? Array.Empty<Guid>(),
                collection ?? Array.Empty<string>(),
                sortBy,
                limit,
                offset,
                hasAiPrompts,
                favoritesOnly,
                templatesFolderId);
            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Save templates used by a person.
        /// </summary>
        /// <remarks>
        /// This endpoint saves the details of the templates used by a person. 
        /// The details of the templates used by the person are provided in the request. 
        /// If the templates are successfully saved, the details of the templates used by the person are returned. 
        /// If the requesting user has no access to the resource, a 403 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="request">The details of the templates used by the person.</param>
        /// <returns>The details of the templates used by the person.</returns>
        /// <response code="200">Returns the details of the templates used by the person.</response>
        [HttpPost("me")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<TemplatesUsedByPerson> SaveTemplatesUsedByPerson([FromRoute] Guid providerId,
            [FromBody] SaveTemplatesUsedByPersonRequest request)
        {
            var command = new SaveTemplatesUsedByPersonCommand(IdentityContext, request.PersonId,
                IdentityContext.ProviderPermissions.ProviderId, request.AddRecentlyUsed, request.AddFavourites);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Get templates used by a person.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves the templates used by a person. 
        /// The ID of the provider is provided in the request. 
        /// If the templates are successfully retrieved, the templates used by the person are returned. 
        /// If the requesting user has no access to the resource, a 403 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <returns>The templates used by the person.</returns>
        /// <response code="200">Returns the templates used by the person.</response>
        [HttpGet("me")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<TemplatesUsedByPersonResponse> GetTemplatesUsedByPerson([FromRoute] Guid providerId)
        {
            var query = new GetTemplatesUsedByPersonQuery(IdentityContext,
                IdentityContext.ProviderPermissions.ProviderId);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Publish a specific template.
        /// </summary>
        /// <remarks>
        /// This endpoint publishes a specific template. 
        /// The ID of the template to publish and the details of the template to publish are provided in the request. 
        /// If the template is successfully published, the published template is returned. 
        /// If the template with the provided ID does not exist, a 404 status code is returned.
        /// If the request is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template to publish.</param>
        /// <param name="request">The details of the template to publish.</param>
        /// <returns>The published template.</returns>
        /// <response code="200">Returns the published template.</response>
        /// <response code="404">If the template is not found.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPost("{id}/publish")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<PublicTemplate> PublishTemplate([FromRoute] Guid providerId, [FromRoute] Guid id,
            [FromBody] PublishTemplateRequest request)
        {
            var command = new PublishTemplateCommand(IdentityContext,
                id,
                request.Collection,
                request.Professions ?? Array.Empty<string>(),
                request.Tags ?? Array.Empty<string>(),
                request.Description);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Add attachments to a specific template.
        /// </summary>
        /// <remarks>
        /// This endpoint adds attachments to a specific template. 
        /// The ID of the template to add attachments to and the details of the attachments to add are provided in the request. 
        /// If the attachments are successfully added, a status indicating the result of the operation is returned. 
        /// If the request is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template to add attachments to.</param>
        /// <param name="request">The details of the attachments to add.</param>
        /// <returns>A status indicating the result of the operation.</returns>
        /// <response code="200">Returns a status indicating the result of the operation.</response>
        /// <response code="400">If the request is invalid.</response>
        [HttpPut("{id}/attachments")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<IActionResult> AddAttachments([FromRoute] Guid providerId, [FromRoute] Guid id,
            [FromBody] CreateTemplateAttachmentsRequest request)
        {
            var attachments = TemplateAttachmentMapper.Map(request.Attachments);
            var command = new CreateTemplateAttachmentsCommand(IdentityContext, id, attachments.ToArray());
            var result = await mediator.Send(command);

            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Delete a specific attachment from a template.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a specific attachment from a template. 
        /// The ID of the template and the ID of the attachment to delete are provided in the request. 
        /// If the attachment is successfully deleted, a status indicating the result of the operation is returned. 
        /// If the template or the attachment with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template.</param>
        /// <param name="fileId">The ID of the attachment to delete.</param>
        /// <returns>A status indicating the result of the operation.</returns>
        /// <response code="200">Returns a status indicating the result of the operation.</response>
        /// <response code="404">If the template or the attachment is not found.</response>
        [HttpDelete("{id}/attachments/{fileId}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<IActionResult> DeleteAttachment([FromRoute] Guid providerId, [FromRoute] Guid id,
            [FromRoute] Guid fileId)
        {
            var command = new DeleteTemplateAttachmentCommand(IdentityContext, fileId, id);
            var result = await mediator.Send(command);

            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Fetches a specific attachment from a template.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves a specific attachment from a template. 
        /// The ID of the template and the ID of the attachment to retrieve are provided in the request. 
        /// If the attachment is successfully retrieved, the attachment is returned. 
        /// If the template or the attachment with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template.</param>
        /// <param name="fileId">The ID of the attachment to get.</param>
        /// <returns>The requested attachment.</returns>
        /// <response code="200">Returns the requested attachment.</response>
        /// <response code="404">If the template or the attachment is not found.</response>
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [HttpGet("{id}/attachments/{fileId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<IActionResult> GetTemplateAttachment([FromRoute] Guid providerId, [FromRoute] Guid id,
            [FromRoute] Guid fileId)
        {
            var command = new GetTemplateAttachmentQuery(IdentityContext, fileId, id);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Download a specific attachment from a template.
        /// </summary>
        /// <remarks>
        /// This endpoint downloads a specific attachment from a template. 
        /// The ID of the template and the ID of the attachment to download are provided in the request. 
        /// If the attachment is successfully downloaded, the attachment for download is returned. 
        /// If the template or the attachment with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template.</param>
        /// <param name="fileId">The ID of the attachment to download.</param>
        /// <returns>The requested attachment for download.</returns>
        /// <response code="200">Returns the requested attachment for download.</response>
        /// <response code="404">If the template or the attachment is not found.</response>
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [HttpGet("{id}/attachments/{fileId}/download")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<IActionResult> DownloadTemplateAttachment([FromRoute] Guid providerId, [FromRoute] Guid id,
            [FromRoute] Guid fileId)
        {
            var command = new GetTemplateAttachmentQuery(IdentityContext, fileId, id, IsDownload: true);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Save a form field for a specific template.
        /// </summary>
        /// <remarks>
        /// This endpoint saves a form field for a specific template. 
        /// The ID of the template and the details of the form field to save are provided in the request. 
        /// If the form field is successfully saved, the saved form field is returned. 
        /// If the request is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template.</param>
        /// <param name="request">The details of the form field to save.</param>
        /// <returns>The saved form field.</returns>
        /// <response code="200">Returns the saved form field.</response>
        /// <response code="400">If the request is invalid.</response>
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [HttpPost("{id}/form/fields")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<TemplateFormField> SaveFormField([FromRoute] Guid providerId, [FromRoute] Guid id,
            [FromBody] SaveFormFieldRequest request)
        {
            var command = new SaveTemplateFormFieldCommand(IdentityContext, request.Id, id, request.Schema,
                request.Type, request.Version);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Delete a specific form field from a template.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a specific form field from a template. 
        /// The ID of the form field to delete is provided in the request. 
        /// If the form field is successfully deleted, a status indicating the result of the operation is returned. 
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="formFieldId">The ID of the form field to delete.</param>
        /// <returns>A status indicating the result of the operation.</returns>
        /// <response code="200">Returns a status indicating the result of the operation.</response>
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [HttpDelete("{id}/form/fields/{formFieldId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteFormField([FromRoute] Guid providerId, [FromRoute] Guid formFieldId)
        {
            var command = new DeleteTemplateFormFieldCommand(IdentityContext, formFieldId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Fetches a specific AI prompt by its ID.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves a specific AI prompt by its ID.
        /// If the AI prompt is successfully retrieved, the AI prompt is returned.
        /// If the AI prompt with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="aiPromptId">The ID of the AI prompt.</param>
        /// <returns>The requested AI prompt.</returns>
        /// <response code="200">Returns the requested AI prompt.</response>
        /// <response code="404">If the AI prompt is not found.</response>
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [HttpGet("{id}/ai-prompts/{aiPromptId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<TemplateAiPrompt> GetAiPrompt([FromRoute] Guid providerId, [FromRoute] Guid aiPromptId)
        {
            var command = new GetTemplateAiPromptQuery(IdentityContext, providerId, aiPromptId);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Saves a new AI prompt for a specific template.
        /// </summary>
        /// <remarks>
        /// This endpoint saves a new AI prompt for a specific template.
        /// The details of the AI prompt to save are provided in the request.
        /// If the AI prompt is successfully saved, the saved AI prompt is returned.
        /// If the request is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The ID of the template.</param>
        /// <param name="request">The details of the AI prompt to save.</param>
        /// <returns>The saved AI prompt.</returns>
        /// <response code="200">Returns the saved AI prompt.</response>
        /// <response code="400">If the request is invalid.</response>
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [HttpPost("{id}/ai-prompts")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ValidationError))]
        public async Task<TemplateAiPrompt> SaveAiPrompt([FromRoute] Guid providerId, [FromRoute] Guid id,
            [FromBody] SaveTemplateAiPromptRequest request)
        {
            var command = new SaveTemplateAiPromptCommand(IdentityContext, request.Id, id, request.Schema, request.Type,
                request.Version);
            var result = await mediator.Send(command);
            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes a specific AI prompt from a template.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes a specific AI prompt from a template.
        /// The ID of the AI prompt to delete is provided in the request.
        /// If the AI prompt is successfully deleted, a status indicating the result of the operation is returned.
        /// </remarks>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="aiPromptId">The ID of the AI prompt to delete.</param>
        /// <returns>A status indicating the result of the operation.</returns>
        /// <response code="200">Returns a status indicating the result of the operation.</response>
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [HttpDelete("{id}/ai-prompts/{aiPromptId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteAiPrompt([FromRoute] Guid providerId, [FromRoute] Guid aiPromptId)
        {
            var command = new DeleteTemplateAiPromptCommand(IdentityContext, aiPromptId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Retrieves a paginated list of templates including default intakes for a provider.
        /// </summary>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="limit">The maximum number of records to return. Default is 20.</param>
        /// <param name="offset">The number of records to skip. Default is 0.</param>
        /// <returns>A paginated list of intake templates.</returns>
        [HttpGet("intakes")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<PaginatedResult<IntakeTemplate>> GetIntakeTemplates(
            [FromRoute] Guid providerId,
            [FromQuery] int limit = 20,
            [FromQuery] int offset = 0)
        {
            var query = new GetIntakeTemplatesQuery(IdentityContext, limit, offset);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Creates a default intake template for a specific provider and template id.
        /// </summary>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The unique identifier of the template.</param>
        /// <returns>The created default intake template.</returns>
        [HttpPost("{id}/default-intakes")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<DefaultIntakeTemplate> CreateDefaultIntakeTemplate(
            [FromRoute] Guid providerId,
            [FromRoute] Guid id)
        {
            var query = new CreateDefaultIntakeTemplateCommand(IdentityContext, id);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Deletes a default intake template for a specific provider and template id.
        /// </summary>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="id">The unique identifier of the template.</param>
        /// <returns>The unique identifier of the deleted default intake template.</returns>
        [HttpDelete("{id}/default-intakes")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<Guid> DeleteDefaultIntakeTemplate(
            [FromRoute] Guid providerId,
            [FromRoute] Guid id)
        {
            var query = new DeleteDefaultIntakeTemplateCommand(IdentityContext, id);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Retrieves the sharing configuration for a specific template.
        /// </summary>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="templateId">The ID of the template.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the template sharing configuration.</returns>
        /// <response code="200">Returns the template sharing configuration.</response>
        [HttpGet("{templateId}/sharing-config")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<TemplateSharingConfig> GetTemplateSharingConfig([FromRoute] Guid providerId,
            [FromRoute] Guid templateId)
        {
            var query = new GetTemplateSharingConfigQuery(IdentityContext, templateId);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Creates a new sharing configuration for a specific template.
        /// </summary>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="templateId">The ID of the template.</param>
        /// <param name="request">The request body containing the details of the sharing configuration to create.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the created template sharing configuration.</returns>
        /// <response code="200">Returns the created template sharing configuration.</response>
        [HttpPost("{templateId}/sharing-config")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<TemplateSharingConfig> CreateTemplateSharingConfig([FromRoute] Guid providerId,
            [FromRoute] Guid templateId, [FromBody] SaveTemplateSharingConfigRequest request)
        {
            var query = new CreateTemplateSharingConfigCommand(IdentityContext, templateId, request.VerificationOption, request.UseWorkspaceBranding, request.ShowPoweredBy);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Updates an existing sharing configuration for a specific template.
        /// </summary>
        /// <param name="providerId">The ID of the provider.</param>
        /// <param name="templateId">The ID of the template.</param>
        /// <param name="id">The ID of the sharing configuration to update.</param>
        /// <param name="request">The request body containing the updated details of the sharing configuration.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the updated template sharing configuration.</returns>
        /// <response code="200">Returns the updated template sharing configuration.</response>
        [HttpPut("{templateId}/sharing-config/{id}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<TemplateSharingConfig> UpdateTemplateSharingConfig([FromRoute] Guid providerId,
            [FromRoute] Guid templateId, [FromRoute] string id, [FromBody] SaveTemplateSharingConfigRequest request)
        {
            var query = new UpdateTemplateSharingConfigCommand(IdentityContext, id, templateId,
                request.VerificationOption, request.UseWorkspaceBranding, request.ShowPoweredBy);

            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Sets a specific template as a favorite.
        /// </summary>
        /// <param name="id">The ID of the template to set as favorite.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a unit value.</returns>
        /// <response code="200">Indicates that the template was successfully set as favorite.</response>
        [HttpPost("me/favorites/{id}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<Unit> SetTemplateAsFavorite(Guid id)
        {
            var command = new SetTemplateAsFavoriteCommand(IdentityContext, id);
        
            var result = await mediator.Send(command);
        
            return ExecutionResult(result);
        }
        
        /// <summary>
        /// Unsets a specific template as a favorite.
        /// </summary>
        /// <param name="id">The ID of the template to unset as favorite.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a unit value.</returns>
        /// <response code="200">Indicates that the template was successfully unset as favorite.</response>
        [HttpDelete("me/favorites/{id}")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<Unit> UnsetTemplateAsFavorite(Guid id)
        {
            var command = new UnsetTemplateAsFavoriteCommand(IdentityContext, id);
        
            var result = await mediator.Send(command);
        
            return ExecutionResult(result);
        }
    }
}