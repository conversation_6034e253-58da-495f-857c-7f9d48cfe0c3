using carepatron.api.Constants;
using carepatron.api.Contracts.Requests.Insurance;
using carepatron.core.Application.Insurance.Commands;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Insurance.Queries;
using carepatron.core.Identity;
using carepatron.core.Models.Pagination;
using carepatron.core.Utilities;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.IO;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.Practitioner;

[ControllerProperties(CodeOwner.BillingAndPayments)]
[Route("api/providers/{providerId}/claims")]
public class ProviderClaimsController(
    IIdentityContextFactory identityContextFactory,
    IMediator mediator
) : BaseController(identityContextFactory)
{
    [HttpGet()]
    [Authorize(Policy = AuthPolicy.HasBillingViewAccess)]
    public async Task<PaginatedResult<InsuranceClaimListEntry>> List(
        [FromQuery] GetProviderPaginatedInsuranceClaimsRequest request)
    {
        var query = new GetProviderInsuranceClaimsQuery(
          IdentityContext.ProviderPermissions.ProviderId,
          IdentityContext,
          request.ContactIds,
          request.StaffIds,
          request.PayerIds,
          request.FromDate,
          request.ToDate,
          request.Status,
          request.SearchTerm,
          request.Offset,
          request.Limit,
          SortUtilities.Transform(request.Sort)
      );
        var result = await mediator.Send(query);

        return ExecutionResult(result);
    }

    [HttpGet("export")]
    [Authorize(Policy = AuthPolicy.HasBillingViewAccess)]
    public async Task<ActionResult> Export(
        [FromQuery] DateOnly? fromDate,
        [FromQuery] DateOnly? toDate,
        [FromQuery] ClaimStatus[] status,
        [FromQuery] Guid[] staff,
        [FromQuery] string[] payer,
        [FromQuery] bool includeLineItems = false
    )
    {
        var query = new ExportClaimsQuery(IdentityContext.ProviderPermissions.ProviderId,
            IdentityContext,
            fromDate,
            toDate,
            status,
            staff,
            payer,
            includeLineItems);
        var result = await mediator.Send(query);

        var filename = "claims";

        if (fromDate.HasValue)
        {
            filename += $"_{fromDate.Value:yyyy-MM-dd}";
        }

        if (toDate.HasValue)
        {
            filename += $"_{toDate.Value:yyyy-MM-dd}";
        }

        return File(new MemoryStream(result.Result), "text/csv", $"{filename}.csv");
    }

    [HttpGet("{id}")]
    [Authorize(Policy = AuthPolicy.HasBillingViewForClaim)]
    public async Task<InsuranceClaim> Get(Guid id)
    {
        var command = new GetInsuranceClaimQuery(id, IdentityContext.ProviderPermissions.ProviderId);
        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpGet("~/api/providers/{providerId}/contacts/{contactId}/claims")]
    [Authorize(Policy = AuthPolicy.HasBillingViewForContact)]
    public async Task<PaginatedResult<InsuranceClaim>> ContactList(
        [FromRoute] Guid providerId,
        [FromRoute] Guid contactId,
        [FromQuery] GetPaginatedClaimsRequest request)
    {
        var pagination = new PaginationRequest(request.Limit, request.Offset);
        var query = new GetInsuranceClaimsQuery(
            providerId,
            contactId,
            pagination,
            request.Status,
            request.FromDate,
            request.ToDate,
            request.TaskIds ?? [],
            null,
            request.SearchTerm
        );
        var result = await mediator.Send(query);
        return ExecutionResult(result);
    }

    [HttpDelete("{id}")]
    [Authorize(Policy = AuthPolicy.HasBillingEditForClaim)]
    public async Task<Guid> Delete(Guid id)
    {
        var command = new DeleteUSProfessionalClaimCommand(id, IdentityContext.ProviderPermissions.ProviderId, IdentityContext);
        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpPut("{id}/status")]
    [Authorize(Policy = AuthPolicy.HasBillingEditForClaim)]
    public async Task<Unit> UpdateStatus([FromRoute] Guid id, [FromBody] UpdateClaimStatusRequest request)
    {
        var command = new UpdateInsuranceClaimStatusCommand(
            id,
            IdentityContext.ProviderPermissions.ProviderId,
            request.Status,
            IdentityContext
        );
        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpGet("{claimId}/errors")]
    [Authorize(Policy = AuthPolicy.HasBillingViewForClaim)]
    public async Task<InsuranceClaimError[]> GetHistory(
        [FromRoute] Guid providerId,
        [FromRoute] Guid claimId
    )
    {
        var query = new GetInsuranceClaimErrorsQuery(providerId, claimId);
        var result = await mediator.Send(query);
        return ExecutionResult(result);
    }
}
