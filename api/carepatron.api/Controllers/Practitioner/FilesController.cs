using carepatron.api.Constants;
using carepatron.api.Contracts.Requests.Files;
using carepatron.core.Application.Files.Commands;
using carepatron.core.Application.Files.Models;
using carepatron.core.Identity;
using carepatron.core.Models.Media;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.Practitioner
{
    [ControllerProperties(CodeOwner.Clients)]
    [Route("api/providers/{providerId}/files")]
    public class FilesController : BaseController
    {
        private readonly IMediator mediator;

        public FilesController(IIdentityContextFactory identityContextFactory, IMediator mediator) : base(identityContextFactory)
        {
            this.mediator = mediator;
        }

        [HttpPost("upload/initialize")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<InitializeMultipartUploadResult> Initialize([FromBody(EmptyBodyBehavior = EmptyBodyBehavior.Allow)] UploadInitializeRequest request)
        {
            // For backwards compatibility, remove this line once FE passes the body
            request ??= new UploadInitializeRequest();

            var command = new InitializeUploadCommand(IdentityContext, request.FileLocationType, request.ContentType);
            var response = await mediator.Send(command);
            return ExecutionResult(response);
        }

        [HttpPut("upload/parts")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<PartUploadResult> UploadPart([FromQuery] Guid fileId, [FromQuery] string uploadId, [FromQuery] int partNumber, [FromQuery] bool isLastPart, [FromForm] UploadPartRequest request, [FromQuery] FileLocationType fileLocationType = FileLocationType.ClientImport)
        {
            var command = new UploadPartCommand(fileId, uploadId, partNumber, isLastPart, request.File, fileLocationType);
            var response = await mediator.Send(command);
            return ExecutionResult(response);
        }

        [HttpPost("upload/complete")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<CompleteUploadResult> Complete([FromBody] CompleteUploadRequest request)
        {
            var command = new CompleteUploadCommand(request.FileId, request.UploadId, request.FileLocationType);
            var response = await mediator.Send(command);
            return ExecutionResult(response);
        }

        [HttpPost("upload/abort")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<Unit> Abort([FromBody] AbortUploadRequest request)
        {
            var command = new AbortUploadCommand(request.FileId, request.UploadId, request.FileLocationType);
            var response = await mediator.Send(command);
            return ExecutionResult(response);
        }

        [HttpGet("{id}/url")]
        [Authorize(Policy = AuthPolicy.HasWorkspaceAccess)]
        public async Task<string> GeneratePresignedUrl([FromRoute] Guid id,
            [FromQuery] string fileName = null,
            [FromQuery] bool download = false,
            [FromQuery] FileLocationType fileLocationType = FileLocationType.Files)
        {
            var command = new GeneratePresignedUrlCommand(IdentityContext, id, fileName, download, fileLocationType);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

    }
}