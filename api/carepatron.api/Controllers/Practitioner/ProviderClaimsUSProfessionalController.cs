using carepatron.api.Constants;
using carepatron.api.Contracts.Requests.Insurance;
using carepatron.core.Application.History.Models;
using carepatron.core.Application.History.Queries;
using carepatron.core.Application.Insurance.Commands;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Insurance.Queries;
using carepatron.core.Identity;
using carepatron.core.Models.Pagination;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.Practitioner;

[ControllerProperties(CodeOwner.BillingAndPayments)]
[Route("api/providers/{providerId}/contacts/{contactId}/claims")]
public class InsuranceClaimsUSProfessionalController(
    IIdentityContextFactory identityContextFactory,
    IMediator mediator
) : BaseController(identityContextFactory)
{
    [HttpGet("{id}/us-professional")]
    [Authorize(Policy = AuthPolicy.HasBillingViewForClaim)]
    public async Task<InsuranceClaimUSProfessional> GetById([FromRoute] Guid id)
    {
        var command = new GetUSProfessionalClaimByIdQuery(
            id,
            IdentityContext.ProviderPermissions.ProviderId
        );
        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpPost("us-professional")]
    [Authorize(Policy = AuthPolicy.HasBillingEditForContact)]
    public async Task<InsuranceClaimUSProfessional> Create([FromBody] SaveUSProfessionalClaimRequest request)
    {
        var providerId = IdentityContext.ProviderPermissions.ProviderId;
        var command = new CreateUSProfessionalClaimCommand(
            providerId,
            request.Status,
            request.SubmissionMethod,
            request.ResubmissionCode,
            request.OriginalReferenceNumber,
            request.PatientsAccountNumber,
            request.PriorAuthorizationNumber,
            request.AmountPaid,
            request.Lab,
            request.LabCharges,
            request.AdditionalClaimInformation,
            request.Client?.ToModel(providerId),
            request.Incident?.ToModel(providerId),
            request.ServiceFacility?.ToModel(providerId),
            request.ServiceLines?.Select((s, index) => s.ToModel(providerId, index + 1)).ToArray(),
            request.ReferringProviders?.Select(s => s.ToModel(providerId)).ToArray(),
            request.RenderingProviders?.Select((s, index) => s.ToModel(providerId, index + 1)).ToArray(),
            request.DiagnosticCodes?.Select(s => s.ToModel(providerId)).ToArray(),
            request.BillingDetail?.ToModel(providerId),
            request.ContactInsurancePolicy?.ToModel(providerId),
            IdentityContext
        );

        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpPut("{id}/us-professional")]
    [Authorize(Policy = AuthPolicy.HasBillingEditForClaim)]
    public async Task<InsuranceClaimUSProfessional> Update(Guid id, [FromBody] SaveUSProfessionalClaimRequest request)
    {
        var providerId = IdentityContext.ProviderPermissions.ProviderId;
        var command = new UpdateUSProfessionalClaimCommand(
            id,
            providerId,
            request.Status,
            request.SubmissionMethod,
            request.ResubmissionCode,
            request.OriginalReferenceNumber,
            request.PatientsAccountNumber,
            request.PriorAuthorizationNumber,
            request.AmountPaid,
            request.Lab,
            request.LabCharges,
            request.AdditionalClaimInformation,
            request.Client?.ToModel(providerId),
            request.Incident?.ToModel(providerId),
            request.ServiceFacility?.ToModel(providerId),
            request.ServiceLines.FilterNulls().Select((s, index) => s.ToModel(providerId, index + 1)).ToArray(),
            request.ReferringProviders.FilterNulls().Select(s => s.ToModel(providerId)).ToArray(),
            request.RenderingProviders.FilterNulls().Select((s, index) => s.ToModel(providerId, index + 1)).ToArray(),
            request.DiagnosticCodes.FilterNulls().Select(s => s.ToModel(providerId)).ToArray(),
            request.BillingDetail?.ToModel(providerId),
            request.ContactInsurancePolicy?.ToModel(providerId),
            IdentityContext
        );

        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpPost("{id}/us-professional/submit")]
    [Authorize(Policy = AuthPolicy.HasBillingEditForClaim)]
    public async Task<Unit> SubmitClaim(
        [FromRoute] Guid id,
        [FromBody] SubmitUSProfessionalClaimRequest request
    )
    {
        var command = new SubmitUSProfessionalClaimCommand(
            id,
            IdentityContext.ProviderPermissions.ProviderId,
            request?.SubmissionMethod ?? ClaimSubmissionMethod.Manual,
            IdentityContext
        );
        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpPost("{id}/us-professional/validate")]
    [Authorize(Policy = AuthPolicy.HasBillingEditForClaim)]
    public async Task<Unit> ValidateClaim([FromRoute] Guid id)
    {
        var command = new ValidateUSProfessionalClaimCommand(
            id,
            IdentityContext.ProviderPermissions.ProviderId,
            IdentityContext
        );
        var result = await mediator.Send(command);
        return ExecutionResult(result);
    }

    [HttpPost("{id}/us-professional/export")]
    [Authorize(Policy = AuthPolicy.HasBillingViewForClaim)]
    public async Task<ExportPrintUSProfessionalClaimResponse> Export([FromRoute] Guid id, [FromQuery] string key)
    {
        var command = new ExportUSProfessionalClaimCommand(
            id,
            key,
            IdentityContext
        );

        var response = await mediator.Send(command);
        return ExecutionResult(response);
    }

    [HttpPost("{id}/us-professional/print")]
    [Authorize(Policy = AuthPolicy.HasBillingViewForClaim)]
    public async Task<ExportPrintUSProfessionalClaimResponse> Print([FromRoute] Guid id, [FromQuery] string key)
    {
        var command = new PrintUSProfessionalClaimCommand(
            id,
            key,
            IdentityContext
        );

        var response = await mediator.Send(command);
        return ExecutionResult(response);
    }

    [HttpGet("{id}/us-professional/history")]
    [Authorize(Policy = AuthPolicy.HasBillingViewForClaim)]
    public async Task<PaginatedResult<EntityHistory>> GetHistory(
        [FromRoute] Guid providerId,
        [FromRoute] Guid id,
        [FromQuery] PaginationRequest request
    )
    {
        var query = new GetEntityHistoryQuery(
            providerId,
            request,
            EntityType.USProfessionalInsuranceClaim,
            id,
            null
        );
        var result = await mediator.Send(query);
        return ExecutionResult(result);
    }
}