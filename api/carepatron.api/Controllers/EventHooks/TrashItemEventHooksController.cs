using System.Threading.Tasks;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Trash.Commands;
using carepatron.core.Identity;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.Workspace)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class TrashItemEventHooksController : ControllerBase, IHasEventBindings
{
    private readonly IMediator mediator;

    public TrashItemEventHooksController(IMediator mediator)
    {
        this.mediator = mediator;
    }
    
    [EventBridgeTarget("trash-items/MergeTrashItems", nameof(ContactMergedEvent))]
    public async Task<IActionResult> MergeTrashItems([FromBody] ReceivedEvent<ContactMergedEvent> @event)
    {
        var command = new MergeTrashItemsCommand(@event.Detail.ProviderId, @event.Detail.Entity.Id, @event.Detail.ArchivedContactIds);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }
}