﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Authorization.Commands;
using carepatron.core.Application.Files.Commands;
using carepatron.core.Application.Files.Events;
using carepatron.core.Application.Notes.Commands;
using carepatron.core.Authorization.Constants;
using carepatron.core.Authorization.Models;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks
{
    /// <summary>
    /// Controller for managing file event hooks.
    /// </summary>
    [ControllerProperties(CodeOwner.NotesAndDocuments)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class FileEventHooksController : ControllerBase, IHasEventBindings
    {
        private readonly IMediator mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="FileEventHooksController"/> class.
        /// </summary>
        /// <param name="mediator">Mediator instance used to send or publish messages.</param>
        public FileEventHooksController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        /// <summary>
        /// Setup file access for a shared file.
        /// </summary>
        /// <remarks>
        /// This endpoint sets up file access for a shared file. 
        /// The details of the shared file are provided in the event body. 
        /// If the file access is successfully set up, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the shared file.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("files/SetupFileAccess", nameof(FileSharedEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SetupFileAccess([FromBody] ReceivedEvent<FileSharedEvent> @event)
        {
            var command = new SetupFileAccessCommand(@event.Detail.ProviderId, @event.Detail.Initiator.PersonId, @event.Detail.Entity.Id, @event.Detail.SharedWithPersonId, @event.Detail.Role, @event.Detail.SendMessage, @event.Detail.Message);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Setup file access for a note file.
        /// </summary>
        /// <remarks>
        /// This endpoint sets up file access for a note file. 
        /// The details of the note file are provided in the event body. 
        /// If the file access is successfully set up, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the note file.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("files/SetupNoteFileAccess", nameof(NoteFileAttachedEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SetupNoteFileAccess([FromBody] ReceivedEvent<NoteFileAttachedEvent> @event)
        {
            var command = new SetupNoteFileAccessCommand(@event.Detail.ProviderId, @event.Detail.Entity.Id);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Delete user access token for a file.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes the user access token for a specific file. 
        /// The details of the file are provided in the event body. 
        /// If the user access token is successfully deleted, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the file.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("files/DeleteUserAccessToken", nameof(FileStoppedSharingEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteUserAccessToken([FromBody] ReceivedEvent<FileStoppedSharingEvent> @event)
        {
            var command = new DeleteAccessTokenCommand(@event.Detail.Entity.Id, core.Application.Security.Models.TokenResource.File, @event.Detail.PersonId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Delete user's file policy.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes the file policy for a specific user. 
        /// The details of the file are provided in the event body. 
        /// If the file policy is successfully deleted, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the file.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("files/DeleteUsersFilePolicy", nameof(FileStoppedSharingEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteUsersFilePolicy([FromBody] ReceivedEvent<FileStoppedSharingEvent> @event)
        {
            var spec = new PolicyQuerySpecification(PolicyScopeValues.Files)
                .AddResource(@event.Detail.Entity.ResourceName)
                .AddIdentity(@event.Detail.PersonId.ToString());

            var command = new DeleteResourcePolicyCommand(spec);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Delete access tokens for a deleted file.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes the access tokens for a specific file. 
        /// The details of the file are provided in the event body. 
        /// If the access tokens are successfully deleted, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the deleted file.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("files/DeleteAccessTokens", nameof(FileDeletedEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteAccessTokens([FromBody] ReceivedEvent<FileDeletedEvent> @event)
        {
            var command = new DeleteAccessTokenCommand(@event.Detail.Entity.Id, core.Application.Security.Models.TokenResource.File);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Delete file policies for a deleted file.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes the file policies for a specific file. 
        /// The details of the file are provided in the event body. 
        /// If the file policies are successfully deleted, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the deleted file.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("files/DeleteFilePolicies", nameof(FileDeletedEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteFilePolicies([FromBody] ReceivedEvent<FileDeletedEvent> @event)
        {
            var spec = new PolicyQuerySpecification(PolicyScopeValues.Files)
                .AddResource(@event.Detail.Entity.ResourceName);

            var command = new DeleteResourcePolicyCommand(spec);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}
