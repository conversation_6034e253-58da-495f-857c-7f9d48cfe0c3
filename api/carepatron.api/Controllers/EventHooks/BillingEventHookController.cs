using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Repositories.SQS;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using carepatron.core.Application.Payments.Messages;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.BillingAndPayments)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class BillingEventHooksController(
  ISqsRepository sqsRepository
) : ControllerBase, IHasEventBindings
{
    [EventBridgeTarget("billing/MergeContact", nameof(ContactMergedEvent))]
    public async Task<IActionResult> MergeContact([FromBody] ReceivedEvent<ContactMergedEvent> @event)
    {
        await sqsRepository.SendMessage(
            QueueType.Task,
            new EventMessage(
                EventType.MergeContactForBilling,
                new EventData<MergeContactForBillingMessage>(
                    new MergeContactForBillingMessage(
                        @event.Detail.Entity.ProviderId,
                        @event.Detail.Entity.Id,
                        @event.Detail.ArchivedContactIds
                    )
                ),
                PaymentsMessageTopics.ContactMerged(@event.Detail.Entity.ProviderId),
                null
            )
        );
        return Ok();
    }
}
