﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Invoices.Commands;
using carepatron.core.Application.Invoices.Events;
using carepatron.core.Application.ServiceReceipts.Commands;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.BillingAndPayments)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class InvoiceEventHooksController : ControllerBase, IHasEventBindings
    {
        private readonly IMediator mediator;

        public InvoiceEventHooksController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        /// <summary>
        /// Voids all unpaid invoices for a contact when the contact is deleted
        /// </summary>
        /// <param name="event"></param>
        /// <returns></returns>
        [EventBridgeTarget("invoice/DeleteContactVoidInvoices", nameof(ContactDeletedEvent))]
        public async Task<IActionResult> DeleteContactVoidInvoices([FromBody] ReceivedEvent<ContactDeletedEvent> @event)
        {
            if (@event.Detail.Entity.Id == Guid.Empty)
                return Ok();

            var command = new VoidUnpaidInvoicesCommand(@event.Detail.ProviderId, @event.Detail.Entity.Id);

            var result = await mediator.Send(command);

            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Voids all unpaid invoices for a contact when the contact is trashed
        /// </summary>
        /// <param name="event"></param>
        /// <returns></returns>
        [EventBridgeTarget("invoice/TrashContactVoidInvoices", nameof(ContactTrashedEvent))]
        public async Task<IActionResult> TrashContactVoidInvoices([FromBody] ReceivedEvent<ContactTrashedEvent> @event)
        {
            if (@event.Detail.Entity.Id == Guid.Empty)
                return Ok();

            var command = new VoidUnpaidInvoicesCommand(@event.Detail.ProviderId, @event.Detail.Entity.Id);

            var result = await mediator.Send(command);

            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Creates a job to generate a superbill receipt when an invoice is paid
        /// </summary>
        /// <param name="event"></param>
        /// <returns></returns>
        [EventBridgeTarget("invoice/CreateGenerateReceiptJob", "InvoicePaid")]
        public async Task<IActionResult> CreateGenerateReceiptJob([FromBody] ReceivedEvent<InvoicePaidEventDetail> @event)
        {
            if (@event.Detail.ContactId == Guid.Empty)
                return Ok();

            var command = new CreateGenerateReceiptJobCommand(@event.Detail.ContactId);

            var result = await mediator.Send(command);

            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Cancels a stripe payment intent when an invoice is voided
        /// </summary>
        /// <param name="event"></param>
        /// <returns></returns>
        [EventBridgeTarget("invoice/CancelStripePaymentIntent", "InvoiceVoided")]
        public async Task<IActionResult> CancelStripePaymentIntent([FromBody] ReceivedEvent<InvoiceVoidedEventDetail> @event)
        {
            if (string.IsNullOrEmpty(@event.Detail.StripePaymentIntentId))
                return Ok();

            var command = new CancelStripePaymentIntentCommand(@event.Detail.StripePaymentIntentId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}