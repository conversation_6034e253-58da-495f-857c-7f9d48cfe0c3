﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Booking.Events;
using carepatron.core.Application.Calendar.Commands;
using carepatron.core.Application.Calendar.Events;
using carepatron.core.Application.Tasks.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.Communications)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class ConnectedCalendarsHooksController : ControllerBase, IHasEventBindings
    {
        private readonly IMediator mediator;

        public ConnectedCalendarsHooksController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        [EventBridgeTarget("calendar/SyncBookedTask", "BookingCreated", targetGroup: "2")]
        public async Task<IActionResult> SyncBookedTask([FromBody] ReceivedEvent<BookingCreatedEventDetail> @event)
        {
            var command = new SyncTaskCommand(@event.Detail.ProviderId, @event.Detail.TaskId, new Guid[] { @event.Detail.StaffId });
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calendar/SyncRebookedTask", "TaskRescheduledEvent")]
        public async Task<IActionResult> SyncRebookedTask([FromBody] ReceivedEvent<TaskRescheduledEventDetail> @event)
        {
            var command = new SyncTaskCommand(@event.Detail.ProviderId, @event.Detail.TaskId, @event.Detail.StaffIds);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calendar/SyncCreatedTask", nameof(TaskCreatedEvent))]
        public async Task<IActionResult> SyncCreatedTask([FromBody] ReceivedEvent<TaskCreatedEvent> @event)
        {
            var command = new SyncTaskCommand(@event.Detail.Entity.ProviderId,
                @event.Detail.Entity.TaskId,
                StaffIdsToAdd: @event.Detail.Entity.StaffIds);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calendar/SyncUpdatedTaskEvent", nameof(TaskUpdatedEvent))]
        public async Task<IActionResult> SyncUpdatedTaskEvent([FromBody] ReceivedEvent<TaskUpdatedEvent> @event)
        {
            var command = new SyncTaskCommand(@event.Detail.Entity.ProviderId,
                @event.Detail.Entity.TaskId,
                StaffIdsToAdd: @event.Detail.NewStaffIds,
                StaffIdsToUpdate: @event.Detail.UpdateStaffIds,
                StaffIdsToDelete: @event.Detail.DeleteStaffIds);

            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calendar/SyncDeletedTask", nameof(TaskDeletedEvent))]
        public async Task<IActionResult> SyncDeletedTask([FromBody] ReceivedEvent<TaskDeletedEvent> @event)
        {
            var command = new SyncTaskCommand(@event.Detail.Entity.ProviderId,
                @event.Detail.Entity.TaskId,
                StaffIdsToDelete: @event.Detail.Entity.StaffIds);

            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calendar/SyncClientConfirmed", "ClientEventConfirmed")]
        public async Task<IActionResult> SyncClientConfirmed([FromBody] ReceivedEvent<ClientEventConfirmedEventDetail> @event)
        {
            var command = new SyncTaskCommand(@event.Detail.ProviderId,
                @event.Detail.TaskId,
                StaffIdsToUpdate: @event.Detail.StaffIds);

            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calendar/SyncDeleteExternalTask", nameof(ExternalTaskDeletedEvent))]
        public async Task<IActionResult> DeleteExternalEvent([FromBody] ReceivedEvent<ExternalTaskDeletedEvent> @event)
        {
            var command = new SyncTaskCommand(@event.Detail.Entity.ProviderId,
                @event.Detail.Entity.TaskId.GetValueOrDefault(),
                StaffIdsToDelete: [@event.Detail.Entity.PersonId],
                CalendarId: @event.Detail.Entity.ExternalCalendarId,
                EventId: @event.Detail.Entity.ExternalId,
                TaskType: @event.Detail.Entity.TaskType);

            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calendar/SyncTrashedTask", nameof(TaskTrashedEvent))]
        public async Task<IActionResult> SyncTrashedTask([FromBody] ReceivedEvent<TaskTrashedEvent> @event)
        {
            var command = new SyncTaskCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.TaskId, StaffIdsToDelete: @event.Detail.Entity.StaffIds);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calendar/SyncRestoredTask", nameof(TaskRestoredEvent))]
        public async Task<IActionResult> SyncRestoredTask([FromBody] ReceivedEvent<TaskRestoredEvent> @event)
        {
            var command = new SyncTaskCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.TaskId, StaffIdsToAdd: @event.Detail.Entity.StaffIds);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [Obsolete("To be removed. External calendar subscription has been moved to queue worker")]
        [EventBridgeTarget("calendar/Subscribe", nameof(CalendarSubscriptionCreatedEvent), disabled: true)]
        public async Task<IActionResult> Subscribe([FromBody] ReceivedEvent<CalendarSubscriptionCreatedEvent> @event)
        {
            return Ok();
        }

        [Obsolete("Updating Subscription Resource is moved to Queue Worker")]
        [EventBridgeTarget("calendar/UpdateSubscriptionResource", nameof(CalendarSubscribedEvent), disabled: true)]
        public async Task<IActionResult> UpdateCalendarSubscriptionResource([FromBody] ReceivedEvent<CalendarSubscribedEvent> @event)
        {
            var command = new UpdateCalendarSubscriptionResourceCommand(@event.Detail.CalendarSubscriptionId, @event.Detail.ResourceId, @event.Detail.ExpirationDateTimeUtc);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [Obsolete("Removal of Subscription Resource is moved to Queue Worker")]
        [EventBridgeTarget("calendar/RemoveSubscriptionResource", nameof(CalendarUnsubscribedEvent), disabled: true)]
        public async Task<IActionResult> RemoveCalendarSubscriptionResource([FromBody] ReceivedEvent<CalendarUnsubscribedEvent> @event)
        {
            var command = new UpdateCalendarSubscriptionResourceCommand(@event.Detail.Entity.CalendarSubscriptionId, null, null);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calendar/Unsubscribe", nameof(CalendarSubscriptionDeletedEvent))]
        public async Task<IActionResult> Unsubscribe([FromBody] ReceivedEvent<CalendarSubscriptionDeletedEvent> @event)
        {
            var command = new UnsubscribeFromCalendarCommand(@event.Detail.Entity, @event.Detail.ProductCode, @event.Detail.AccessTokenInfo);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [Obsolete("To be removed. External calendar unsubscription has been moved to queue worker.")]
        [EventBridgeTarget("calendar/SubscribeUnsubscribeCalendar", nameof(CalendarSubscriptionUpdatedEvent), disabled: true)]
        public async Task<IActionResult> SubscribeUnsubscribeCalendar([FromBody] ReceivedEvent<CalendarSubscriptionUpdatedEvent> @event)
        {
            return Ok();
        }

        [EventBridgeTarget("calendar/CalendarSyncCompleted", nameof(CalendarInitialSyncCompletedEvent))]
        public async Task<IActionResult> BroadcastCalendarInitialSyncCompleted([FromBody] ReceivedEvent<CalendarInitialSyncCompletedEvent> @event)
        {
            var command = new BroadcastCalendarInitialSyncCompletedCommand(@event.Detail.CalendarSubscriptionId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}
