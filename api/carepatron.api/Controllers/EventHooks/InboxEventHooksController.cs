﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core;
using carepatron.core.Application.Communications.Inbox.Commands;
using carepatron.core.Application.Communications.Inbox.Events;
using carepatron.core.Application.ConnectedApps.Events;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Staff.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.Communications)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class InboxEventHooksController : ControllerBase, IHasEventBindings
    {
        private readonly IMediator mediator;

        public InboxEventHooksController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        [EventBridgeTarget("inbox/SyncCreatedConnectedApp", nameof(ConnectedAppCreatedEvent))]
        public async Task<IActionResult> SyncCreatedConnectedApp([FromBody] ReceivedEvent<ConnectedAppCreatedEvent> @event)
        {
            @event.Detail.Entity.Deconstruct(out var evtConnectedAppId,
                out var evtProviderId,
                out var evtPersonId,
                out var evtSettings);

            var providerId = evtProviderId ?? Guid.Empty;
            var personId = evtPersonId ?? Guid.Empty;

            if (providerId == Guid.Empty || personId == Guid.Empty)
                return Ok();

            var command = new SyncInboxSettingsCommand(evtConnectedAppId, providerId, personId, evtSettings);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/SyncUpdatedConnectedApp", nameof(ConnectedAppUpdatedEvent))]
        public async Task<IActionResult> SyncUpdatedConnectedApp([FromBody] ReceivedEvent<ConnectedAppUpdatedEvent> @event)
        {
            @event.Detail.Entity.Deconstruct(out var evtConnectedAppId,
                out var evtProviderId,
                out var evtPersonId,
                out var evtSettings);

            var providerId = evtProviderId ?? Guid.Empty;
            var personId = evtPersonId ?? Guid.Empty;

            if (providerId == Guid.Empty || personId == Guid.Empty)
                return Ok();

            var command = new SyncInboxSettingsCommand(evtConnectedAppId, providerId, personId, evtSettings);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/SyncDeletedConnectedApp", nameof(ConnectedAppDeletedEvent))]
        public async Task<IActionResult> SyncDeletedConnectedApp([FromBody] ReceivedEvent<ConnectedAppDeletedEvent> @event)
        {
            @event.Detail.Entity.Deconstruct(out var evtConnectedAppId,
                out var evtProviderId,
                out var evtPersonId,
                out var evtSettings);

            var providerId = evtProviderId ?? Guid.Empty;
            var personId = evtPersonId ?? Guid.Empty;

            if (providerId == Guid.Empty || personId == Guid.Empty)
                return Ok();

            var command = new SyncInboxSettingsCommand(evtConnectedAppId, providerId, personId, evtSettings);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/SetupCreatedInboxAuthPolicy", nameof(InboxCreatedEvent))]
        public async Task<IActionResult> SetupCreatedInboxAuthPolicy([FromBody] ReceivedEvent<InboxCreatedEvent> @event)
        {
            @event.Detail.Entity.Deconstruct(out var evtProviderId,
                out var evtPersonId,
                out var evtId,
                out var evtName,
                out var evtStaffs);

            var command = new SetupInboxAuthPolicyCommand(evtProviderId, evtPersonId, evtId, evtStaffs);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/SetupUpdateInboxAuthPolicy", nameof(InboxUpdatedEvent))]
        public async Task<IActionResult> SetupUpdateInboxAuthPolicy([FromBody] ReceivedEvent<InboxUpdatedEvent> @event)
        {
            @event.Detail.Entity.Deconstruct(out var evtProviderId,
                out var evtPersonId,
                out var evtId,
                out var evtName,
                out var evtStaffs);

            var command = new SetupInboxAuthPolicyCommand(evtProviderId, evtPersonId, evtId, evtStaffs);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/SyncAddedInboxContact", nameof(ContactCreatedEvent))]
        public async Task<IActionResult> SyncAddedInboxContact([FromBody] ReceivedEvent<ContactCreatedEvent> @event)
        {
            var evtDetail = @event.Detail;

            var command = new SyncInboxContactCommand(evtDetail.ProviderId, evtDetail.Entity.Id, [], evtDetail.Initiator?.PersonId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/SyncUpdatedInboxContact", nameof(ContactUpdatedEvent))]
        public async Task<IActionResult> SyncUpdatedInboxContact([FromBody] ReceivedEvent<ContactUpdatedEvent> @event)
        {
            var evtDetail = @event.Detail;

            var command = new SyncInboxContactCommand(evtDetail.ProviderId, evtDetail.Entity.Id, [], evtDetail.Initiator?.PersonId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/SyncMergedInboxContact", nameof(ContactMergedEvent))]
        public async Task<IActionResult> SyncUpdatedInboxContact([FromBody] ReceivedEvent<ContactMergedEvent> @event)
        {
            var evtDetail = @event.Detail;

            var command = new SyncInboxContactCommand(evtDetail.ProviderId, evtDetail.Entity.Id, evtDetail.ArchivedContactIds, null);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/SyncTrashedInboxContact", nameof(ContactTrashedEvent))]
        public async Task<IActionResult> SyncTrashedInboxContact([FromBody] ReceivedEvent<ContactTrashedEvent> @event)
        {
            var evtDetail = @event.Detail;

            var command = new SyncInboxContactCommand(evtDetail.ProviderId, Guid.Empty, [evtDetail.Entity.Id], null);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/SyncRestoredInboxContact", nameof(ContactRestoredEvent))]
        public async Task<IActionResult> SyncRestoredInboxContact([FromBody] ReceivedEvent<ContactRestoredEvent> @event)
        {
            var evtDetail = @event.Detail;

            var command = new SyncInboxContactCommand(evtDetail.ProviderId, evtDetail.Entity.Id, [], null);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/CleanupAttachmentFiles", nameof(InboxMessageAttachmentDeletedEvent))]
        public async Task<IActionResult> CleanupAttachmentFiles([FromBody] ReceivedEvent<InboxMessageAttachmentDeletedEvent> @event)
        {
            var evtDetail = @event.Detail;

            var command = new CleanUpS3MessageAttachmentFilesCommand(evtDetail.InboxId, evtDetail.FileKeys);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/GrantAddedStaffsAuthPolicy", nameof(InboxStaffAddedEvent))]
        public async Task<IActionResult> GrantAddedStaffsAuthPolicy([FromBody] ReceivedEvent<InboxStaffAddedEvent> @event)
        {
            @event.Detail.Entity.Deconstruct(out var evtProviderId,
                out var evtId,
                out var evtName,
                out var evtStaffs);

            var command = new SetupInboxAuthPolicyCommand(evtProviderId, Guid.Empty, evtId, evtStaffs, false);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/RevokeRemovedStaffsAuthPolicy", nameof(InboxStaffDeletedEvent))]
        public async Task<IActionResult> RevokeRemovedStaffsAuthPolicy([FromBody] ReceivedEvent<InboxStaffDeletedEvent> @event)
        {
            @event.Detail.Entity.Deconstruct(out var evtProviderId,
                out var evtId,
                out var evtName,
                out var evtStaffs);

            var command = new SetupInboxAuthPolicyCommand(evtProviderId, Guid.Empty, evtId, evtStaffs, true);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/SyncDeletedInboxAccount", nameof(InboxAccountDeletedEvent))]
        public async Task<IActionResult> SyncDeletedInboxAccount([FromBody] ReceivedEvent<InboxAccountDeletedEvent> @event)
        {
            @event.Detail.Entity.Deconstruct(out var evtProviderId,
                out var evtInboxId,
                out var evtInboxAccountId,
                out var evtExternalAccountId,
                out var evtExternalSource);

            var command = new SyncDeletedInboxAccountCommand(evtProviderId, evtInboxId, evtInboxAccountId, evtExternalAccountId, evtExternalSource);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/GrantStaffMemberAuthPolicy", nameof(StaffMemberInvited))]
        public async Task<IActionResult> GrantStaffMemberAuthPolicy([FromBody] ReceivedEvent<StaffMemberInvited> @event)
        {
            @event.Detail.Entity.Deconstruct(out var evtPersonId,
                out var evtProviderId,
                out var email);

            var command = new SetupStaffInboxAuthPolicyCommand(evtProviderId, evtPersonId, false);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/RevokeStaffMemberAuthPolicy", nameof(StaffDeleted))]
        public async Task<IActionResult> RevokeStaffMemberAuthPolicy([FromBody] ReceivedEvent<StaffDeleted> @event)
        {
            @event.Detail.Entity.Deconstruct(out var evtPersonId,
                out var evtProviderId,
                out var email);

            var command = new SetupStaffInboxAuthPolicyCommand(evtProviderId, evtPersonId, true);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/RevokeInboxAuthPolicy", nameof(InboxDeleted))]
        public async Task<IActionResult> RevokeInboxAuthPolicy([FromBody] ReceivedEvent<InboxDeleted> @event)
        {
            var eventDetail = @event.Detail;

            var command = new RevokeAccessDeletedInboxPolicyCommand(eventDetail.ProviderId, eventDetail.InboxId, eventDetail.Staffs);

            var result = await mediator.Send(command);

            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("inbox/RemoveInboxAccount", nameof(InboxDeleted))]
        public async Task<IActionResult> RemoveInboxAccount([FromBody] ReceivedEvent<InboxDeleted> @event)
        {
            var eventDetail = @event.Detail;

            var command = new RemoveInboxAccountCommand(eventDetail.ProviderId, eventDetail.InboxId, eventDetail.InboxAccounts);

            var result = await mediator.Send(command);

            return this.EvalExecutionResult(result);
        }
    }
}
