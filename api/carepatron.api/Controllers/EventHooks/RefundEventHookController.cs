using System.Threading.Tasks;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Refunds.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.BillingAndPayments)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class RefundEventHookController(IMediator mediator) : ControllerBase, IHasEventBindings
{
    [EventBridgeTarget("refunds/sendNotificationCreated", nameof(RefundCreated))]
    public async Task<IActionResult> SendRefundCreatedNotification([FromBody] ReceivedEvent<RefundCreated> @event)
    {
        var cmd = new RefundCreatedEventNotificationCommand(@event.Detail.Entity);
        var result = await mediator.Send(cmd);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("refunds/sendNotificationUpdated", nameof(RefundUpdated))]
    public async Task<IActionResult> SendRefundUpdatedNotification([FromBody] ReceivedEvent<RefundUpdated> @event)
    {
        var cmd = new RefundUpdatedEventNotificationCommand(@event.Detail.Entity);
        var result = await mediator.Send(cmd);
        return this.EvalExecutionResult(result);
    }
}