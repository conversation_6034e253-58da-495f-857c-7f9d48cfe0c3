using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Billables.Commands;
using carepatron.core.Application.Invoices.Events;
using carepatron.core.Application.Payments.Events;
using carepatron.core.Application.Tasks.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.BillingAndPayments)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class BillableEventHookController(IMediator mediator) : ControllerBase, IHasEventBindings
{
    [EventBridgeTarget("billables/CreateTaskBillable", nameof(TaskCreatedEvent))]
    public async Task<IActionResult> CreateTaskBillable([FromBody] ReceivedEvent<TaskCreatedEvent> @event, CancellationToken cancellationToken)
    {
        var command = new SyncBillableTaskCommand(@event.Detail.Entity.TaskId, @event.Detail.Entity.ProviderId, @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command, cancellationToken);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("billables/UpdateTaskBillable", nameof(TaskUpdatedEvent))]
    public async Task<IActionResult> UpdateTaskBillable([FromBody] ReceivedEvent<TaskUpdatedEvent> @event)
    {
        var command = new SyncBillableTaskCommand(@event.Detail.Entity.TaskId, @event.Detail.Entity.ProviderId, @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }


    [EventBridgeTarget("billables/TrashTaskBillable", nameof(TaskTrashedEvent))]
    public async Task<IActionResult> TrashTaskBillable([FromBody] ReceivedEvent<TaskTrashedEvent> @event)
    {
        var command = new SyncBillableTaskCommand(@event.Detail.Entity.TaskId, @event.Detail.Entity.ProviderId, @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("billables/RestoreTaskBillable", nameof(TaskRestoredEvent))]
    public async Task<IActionResult> RestoreTaskBillable([FromBody] ReceivedEvent<TaskRestoredEvent> @event)
    {
        var command = new SyncBillableTaskCommand(@event.Detail.Entity.TaskId, @event.Detail.Entity.ProviderId, @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("billables/DeleteTaskBillable", nameof(TaskDeletedEvent))]
    public async Task<IActionResult> DeleteTaskBillable([FromBody] ReceivedEvent<TaskDeletedEvent> @event)
    {
        var command = new SyncBillableTaskCommand(@event.Detail.Entity.TaskId, @event.Detail.Entity.ProviderId, @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }


    [EventBridgeTarget("billables/CreateInvoiceBillable", nameof(InvoiceCreatedEvent))]
    public async Task<IActionResult> CreateInvoiceBillable([FromBody] ReceivedEvent<InvoiceCreatedEvent> @event)
    {
        var command = new SyncInvoiceBillableCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.Id, IsBillingV2: @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }


    [EventBridgeTarget("billables/UpdateInvoiceBillable", nameof(InvoiceUpdatedEvent))]
    public async Task<IActionResult> UpdateInvoiceBillable([FromBody] ReceivedEvent<InvoiceUpdatedEvent> @event)
    {
        var removedBillableItemIds = GetRemovedBillableItemIds(@event.Detail.Previous?.LineItems, @event.Detail.Entity.LineItems);
        var command = new SyncInvoiceBillableCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.Id, removedBillableItemIds, @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("billables/DeleteInvoiceBillable", nameof(InvoiceDeletedEvent))]
    public async Task<IActionResult> DeleteInvoiceBillable([FromBody] ReceivedEvent<InvoiceDeletedEvent> @event)
    {
        var command = new SyncInvoiceBillableCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.Id, IsBillingV2: @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("billables/TrashInvoiceBillable", nameof(InvoiceTrashedEvent))]
    public async Task<IActionResult> TrashInvoiceBillable([FromBody] ReceivedEvent<InvoiceTrashedEvent> @event)
    {
        var command = new SyncInvoiceBillableCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.Id, IsBillingV2: @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("billables/RestoreInvoiceBillable", nameof(InvoiceRestoredEvent))]
    public async Task<IActionResult> RestoreInvoiceBillable([FromBody] ReceivedEvent<InvoiceRestoredEvent> @event)
    {
        var command = new SyncInvoiceBillableCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.Id, IsBillingV2: @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("billables/VoidInvoiceBillable", nameof(InvoiceVoidedEvent))]
    public async Task<IActionResult> VoidInvoiceBillable([FromBody] ReceivedEvent<InvoiceVoidedEvent> @event)
    {
        var command = new SyncInvoiceBillableCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.Id, IsBillingV2: @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("billables/RecalculateOnPayment", nameof(PaymentCreatedEvent))]
    public async Task<IActionResult> RecalculateOnPayment([FromBody] ReceivedEvent<PaymentCreatedEvent> @event)
    {
        var command = new RecalculateBillableCommand(@event.Detail.Entity.ProviderId, null, @event.Detail.Entity.Id, IsBillingV2: @event.Detail.Entity.IsBillingV2);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    private Guid[] GetRemovedBillableItemIds(InvoiceLineItemEventModel[]? previousLineItems, InvoiceLineItemEventModel[] currentLineItems)
    {
        if (previousLineItems == null)
        {
            return Array.Empty<Guid>();
        }

        return previousLineItems
            .Where(x => x.BillableItemId.HasValue && !currentLineItems.Any(y => y.BillableItemId == x.BillableItemId))
            .Select(x => x.BillableItemId.Value)
            .ToArray();
    }
}
