using System.Threading.Tasks;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.FeatureModules.Commands;
using carepatron.core.Application.FeatureModules.Models;
using carepatron.core.Application.Tasks.Commands;
using carepatron.core.Application.Tasks.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.Workspace)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class FeatureModuleEventHookController : ControllerBase, IHasEventBindings
{
    private readonly IMediator mediator;

    public FeatureModuleEventHookController(IMediator mediator)
    {
        this.mediator = mediator;
    }

    [EventBridgeTarget("feature-modules/AddTasksUsage", nameof(TaskCreatedEvent), targetGroup: "2")]
    public async Task<IActionResult> ClientEventCancelled([FromBody] ReceivedEvent<TaskCreatedEvent> @event)
    {
        var command =
            new AddFeatureModuleUsageCommand(@event.Detail.Entity.ProviderId, FeatureModuleType.Workflows_Tasks, 1);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }
}