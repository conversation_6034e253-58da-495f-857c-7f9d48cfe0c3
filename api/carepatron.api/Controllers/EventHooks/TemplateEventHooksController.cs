using System.Threading.Tasks;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Folders.Events;
using carepatron.core.Application.Templates.Commands;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.NotesAndDocuments)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class TemplateEventHooksController(IMediator mediator) : ControllerBase, IHasEventBindings
{
    [EventBridgeTarget("templates/DeleteTemplates", nameof(FolderDeletedEvent))]
    public async Task<IActionResult> DeleteTemplates([FromBody] ReceivedEvent<FolderDeletedEvent> @event)
    {
        var command = new DeleteTemplatesCommand(@event.Detail.ProviderId, @event.Detail.Entity.Id, @event.Detail.Entity.IsSoftDeleted, @event.Detail.Initiator);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }
}