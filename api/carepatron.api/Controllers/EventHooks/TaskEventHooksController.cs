﻿using System;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Booking.Commands;
using carepatron.core.Application.Booking.Events;
using carepatron.core.Application.Calendar.Commands;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Tasks.Commands;
using carepatron.core.Application.Tasks.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.TasksAndScheduling)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class TaskEventHooksController : ControllerBase, IHasEventBindings
    {
        private readonly IMediator mediator;

        public TaskEventHooksController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        [EventBridgeTarget("tasks/ClientEventCancelled", "ClientEventCancelled")]
        public async Task<IActionResult> ClientEventCancelled([FromBody] ReceivedEvent<ClientEventCancelledEventDetail> @event)
        {
            var command = new ClientEventCancelledCommand(@event.Detail.TaskId, @event.Detail.TaskContactId, @event.Detail.DontSendNotification);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("tasks/SendBookingConfirmationEmail", "TaskRescheduledEvent")]
        public async Task<IActionResult> SendBookingConfirmationEmail([FromBody] ReceivedEvent<TaskRescheduledEventDetail> @event)
        {
            var command = new SendBookingConfirmationEmailCommand(@event.Detail.ProviderId, @event.Detail.TaskId, @event.Detail.StaffIds.FirstOrDefault(), @event.Detail.ContactIds, false);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [Obsolete("moved to connected calendars hook 'calendar/SyncDeleteExternalTask'")]
        [EventBridgeTarget("tasks/DeleteExternalEvent", "ExternalTaskDeletedEvent", disabled: true)]
        public async Task<IActionResult> DeleteExternalEvent([FromBody] ReceivedEvent<ExternalTaskDeletedEvent> @event)
        {
            var command = new DeleteExternalEventCommand(@event.Detail.Entity.ProviderId,
                @event.Detail.Entity.PersonId,
                @event.Detail.Entity.ExternalCalendarId,
                @event.Detail.Entity.ExternalId,
                @event.Detail.Entity.TaskId
            );
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("tasks/RestoreContactTasks", nameof(ContactRestoredEvent))]
        public async Task<IActionResult> RestoreTasks([FromBody] ReceivedEvent<ContactRestoredEvent> @event)
        {
            var command = new RestoreContactTasksCommand(@event.Detail.ProviderId, @event.Detail.Entity.Id);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }


        [EventBridgeTarget("tasks/DeleteContactTasks", nameof(ContactTrashedEvent))]
        public async Task<IActionResult> DeleteTasks([FromBody] ReceivedEvent<ContactDeletedEvent> @event)
        {
            var command = new DeleteContactTasksCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.Id);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("tasks/MergeTasks", nameof(ContactMergedEvent))]
        public async Task<IActionResult> MergeTasks([FromBody] ReceivedEvent<ContactMergedEvent> @event)
        {
            var command = new MergeTasksCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.Id, @event.Detail.ArchivedContactIds);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("tasks/BroadcastCreatedTask", nameof(TaskCreatedEvent), targetGroup: "2")]
        public async Task<IActionResult> BroadcastCreatedTask([FromBody] ReceivedEvent<TaskCreatedEvent> @event)
        {
            var command = new BroadcastCreatedTaskCommand(@event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("tasks/BroadcastUpdatedTask", nameof(TaskUpdatedEvent))]
        public async Task<IActionResult> BroadcastUpdatedTask([FromBody] ReceivedEvent<TaskUpdatedEvent> @event)
        {
            var command = new BroadcastUpdatedTaskCommand(@event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("tasks/BroadcastTrashedTask", nameof(TaskTrashedEvent))]
        public async Task<IActionResult> BroadcastTrashedTask([FromBody] ReceivedEvent<TaskTrashedEvent> @event)
        {
            var command = new BroadcastDeletedTaskCommand(@event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("tasks/BroadcastDeletedTask", nameof(ExternalTaskDeletedEvent))]
        public async Task<IActionResult> BroadcastDeletedTask([FromBody] ReceivedEvent<ExternalTaskDeletedEvent> @event)
        {
            var taskId = @event.Detail.Entity.TaskId;
            // this likely from google calendar sync process where an external event is deleted to re-create it
            if (!taskId.HasValue || @event.Detail.Entity.SkipUserNotification) return Ok();

            var command = new BroadcastDeletedTaskCommand(taskId.Value);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("tasks/BroadcastRestoredTask", nameof(TaskRestoredEvent))]
        public async Task<IActionResult> BroadcastRestoredTask([FromBody] ReceivedEvent<TaskRestoredEvent> @event)
        {
            var command = new BroadcastRestoredTaskCommand(@event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("tasks/BroadcastBookingCreated", "BookingCreated", targetGroup: "2")]
        public async Task<IActionResult> BroadcastBookingCreated([FromBody] ReceivedEvent<BookingCreatedEventDetail> @event)
        {
            var command = new BroadcastCreatedTaskCommand(@event.Detail.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}
