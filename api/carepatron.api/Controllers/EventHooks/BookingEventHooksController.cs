﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Booking.Commands;
using carepatron.core.Application.Booking.Events;
using carepatron.core.Application.Payments.Commands;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.TasksAndScheduling)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class BookingEventHooksController : ControllerBase, IHasEventBindings
    {
        private readonly IMediator mediator;

        public BookingEventHooksController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        [EventBridgeTarget("booking/SendBookingConfirmationEmail", "BookingCreated")]
        public async Task<IActionResult> SendBookingConfirmationEmail([FromBody] ReceivedEvent<BookingCreatedEventDetail> @event)
        {
            var command = new SendBookingConfirmationEmailCommand(@event.Detail.ProviderId, @event.Detail.TaskId, @event.Detail.StaffId, [@event.Detail.ContactId], true);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("booking/SavePaymentMethod", "BookingCreated")]
        public async Task<IActionResult> SavePaymentMethod([FromBody] ReceivedEvent<BookingCreatedEventDetail> @event)
        {
            if (!@event.Detail.SavePaymentMethod || string.IsNullOrEmpty(@event.Detail.PaymentMethodId))
                return Ok();

            var command = new SaveAuthorizedPaymentMethodCommand(
                @event.Detail.ProviderId,
                @event.Detail.ContactId,
                @event.Detail.PaymentMethodId);

            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("booking/SavePayment", "BookingCreated")]
        public async Task<IActionResult> SavePayment([FromBody] ReceivedEvent<BookingCreatedEventDetail> @event)
        {
            if (string.IsNullOrEmpty(@event.Detail.PaymentIntentId))
                return Ok();

            var command = new SaveBookingPaymentCommand(
                ProviderId: @event.Detail.ProviderId,
                PaymentIntentId: @event.Detail.PaymentIntentId,
                ContactId: @event.Detail.ContactId,
                BillToId: @event.Detail.BillToId ?? @event.Detail.ContactId,
                TaskId: @event.Detail.TaskId);

            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}
