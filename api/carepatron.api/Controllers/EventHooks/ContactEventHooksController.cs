﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Authorization.Messaging;
using carepatron.core.Application.Contacts.Commands;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Notes.Commands;
using carepatron.core.Application.Registrations.Events;
using carepatron.core.Authorization.Models;
using carepatron.core.Authorization.ResourceNames;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Repositories.SQS;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.Communications)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class ContactEventHooksController(IMediator mediator, ISqsRepository sqsRepository) : ControllerBase, IHasEventBindings
    {

        [EventBridgeTarget("contact/LinkContacts", nameof(PersonRegisteredEvent))]
        public async Task<IActionResult> LinkContacts([FromBody] ReceivedEvent<PersonRegisteredEvent> @event)
        {
            var command = new LinkPersonContactsCommand(@event.Detail.Entity.Id);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("contact/UpdateContactPolicies", nameof(ContactPersonChangedEvent))]
        public async Task<IActionResult> UpdateContactPolicies([FromBody] ReceivedEvent<ContactPersonChangedEvent> @event)
        {
            var command = new SetupContactPersonPoliciesCommand(@event.Detail.Entity.Id, @event.Detail.Entity.PersonId, @event.Detail.PreviousPersonId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("contact/TransferSharedNotesAccess", nameof(ClientIntakeCompleteEvent))]
        public async Task<IActionResult> TransferSharedNotesAccess([FromBody] ReceivedEvent<ClientIntakeCompleteEvent> @event)
        {
            var command = new TransferSharedNotesAccessCommand(@event.Detail.EnrolmentToken, @event.Detail.Entity.PersonId.Value, @event.Detail.PreviousPersonId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("contact/RemovePolicies", nameof(ContactDeletedEvent))]
        public async Task<IActionResult> RemovePolicies([FromBody] ReceivedEvent<ContactDeletedEvent> @event)
        {
            var contact = new ResourceNameBuilder().BuildContact(@event.Detail.Entity.ProviderId, @event.Detail.Entity.Id);
            var spec = new PolicyQuerySpecification(null)
                .AddResource(contact)
                .WithResourceMatchingBehaviour(ResourceMatchingBehaviour.Contains);

            var deleteMessage = new DeleteAuthorizationPolicyMessage(spec);
            await sqsRepository.SendMessage(QueueType.Task, new EventMessage(EventType.DeleteAuthorizationPolicy,
                new EventData<DeleteAuthorizationPolicyMessage>(deleteMessage),
                $"policies-{@event.Detail.ProviderId}",
                null));

            return Ok();
        }

        [EventBridgeTarget("contact/ContactCreatedDetectDuplicates", nameof(ContactCreatedEvent))]
        public async Task<IActionResult> CreatedContactDetectDuplicates([FromBody] ReceivedEvent<ContactCreatedEvent> @event)
        {
            var command = new DetectDuplicateContactsCommand(@event.Detail.Entity.ProviderId, @event.Detail.Initiator, @event.Detail.Entity);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("contact/ContactUpdatedDetectDuplicates", nameof(ContactUpdatedEvent))]
        public async Task<IActionResult> ContactUpdatedDetectDuplicates([FromBody] ReceivedEvent<ContactUpdatedEvent> @event)
        {
            var command = new DetectDuplicateContactsCommand(@event.Detail.Entity.ProviderId, @event.Detail.Initiator, @event.Detail.Entity, @event.Detail.Previous);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }


        [EventBridgeTarget("contact/ContactUpdatedCleanupDuplicates", nameof(ContactUpdatedEvent))]
        public async Task<IActionResult> ContactUpdatedCleanupDuplicates([FromBody] ReceivedEvent<ContactUpdatedEvent> @event)
        {
            var command = new CleanupDuplicateContactsCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity, PreviousContact: @event.Detail.Previous);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("contact/ContactTrashedCleanupDuplicates", nameof(ContactTrashedEvent))]
        public async Task<IActionResult> ContactTrashedCleanupDuplicates([FromBody] ReceivedEvent<ContactTrashedEvent> @event)
        {
            var command = new CleanupDuplicateContactsCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("contact/ContactRestoredDetectDuplicates", nameof(ContactRestoredEvent))]
        public async Task<IActionResult> ContactRestoredDetectDuplicates([FromBody] ReceivedEvent<ContactRestoredEvent> @event)
        {
            var command = new DetectDuplicateContactsCommand(@event.Detail.Entity.ProviderId, @event.Detail.Initiator, @event.Detail.Entity);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }


        [EventBridgeTarget("contact/ContactRestoredCleanupDuplicates", nameof(ContactRestoredEvent))]
        public async Task<IActionResult> ContactRestoredCleanupDuplicates([FromBody] ReceivedEvent<ContactRestoredEvent> @event)
        {
            var command = new CleanupDuplicateContactsCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("contact/UnlinkExternalContact", nameof(ContactUpdatedEvent))]
        public async Task<IActionResult> UnlinkExternalContact([FromBody] ReceivedEvent<ContactUpdatedEvent> @event)
        {
            var command = new UnlinkExternalContactCommand(@event.Detail.Entity.Id, false);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("contact/UnlinkTrashedContact", nameof(ContactTrashedEvent), targetGroup: "2")]
        public async Task<IActionResult> UnlinkTrashedContact([FromBody] ReceivedEvent<ContactTrashedEvent> @event)
        {
            var command = new UnlinkExternalContactCommand(@event.Detail.Entity.Id, true);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}
