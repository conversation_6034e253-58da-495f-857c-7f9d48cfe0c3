﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Calls.Commands;
using carepatron.core.Application.Calls.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.Communications)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class CallEventHooksController : ControllerBase, IHasEventBindings
    {
        private readonly IMediator mediator;

        public CallEventHooksController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        [EventBridgeTarget("calls/NotifyAttendeeJoinCallAttempted", nameof(CallAttendeeJoinAttemptedEvent))]
        public async Task<IActionResult> NotifyAttendeeJoinCallAttempted([FromBody] ReceivedEvent<CallAttendeeJoinAttemptedEvent> @event)
        {
            var command = new NotifyAttendeeJoinCallAttemptedCommand(@event.Detail.Entity.CallId, @event.Detail.Entity.ProviderId, @event.Detail.Entity.HostPersonId, @event.Detail.Entity.CallAttendeeName, @event.Detail.Entity.JoinedDateTime);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calls/NotifyHostCallAttendeeAskedToJoin", nameof(CallAttendeeAskedToJoinEvent))]
        public async Task<IActionResult> NotifyHostCallAttendeeAskedToJoin([FromBody] ReceivedEvent<CallAttendeeAskedToJoinEvent> @event)
        {
            var command = new NotifyHostCallAttendeeAskedToJoinCommand(@event.Detail.Entity.CallId, @event.Detail.Entity.ProviderId, @event.Detail.Entity.HostPersonId, @event.Detail.Entity.CallAttendeeName, @event.Detail.Entity.JoinedDateTime);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calls/NotifyAttendeeAskToJoinAdmitted", nameof(CallAttendeeAskToJoinAdmittedEvent))]
        public async Task<IActionResult> NotifyAttendeeAskToJoinAdmitted([FromBody] ReceivedEvent<CallAttendeeAskToJoinAdmittedEvent> @event)
        {
            var command = new NotifyAttendeeAskToJoinAdmittedCommand(
                @event.Detail.Entity.CallId,
                @event.Detail.Entity.ProviderId,
                @event.Detail.Entity.PersonId,
                @event.Detail.Entity.CallAttendeeId,
                @event.Detail.Entity.CallAttendeeName,
                @event.Detail.Entity.JoinedDateTime
            );

            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("calls/NotifyAttendeeAskToJoinDenied", nameof(CallAttendeeAskToJoinDeniedEvent))]
        public async Task<IActionResult> NotifyAttendeeAskToJoinDenied([FromBody] ReceivedEvent<CallAttendeeAskToJoinDeniedEvent> @event)
        {
            var command = new NotifyAttendeeAskToJoinDeniedCommand(
                @event.Detail.Entity.CallId,
                @event.Detail.Entity.ProviderId,
                @event.Detail.Entity.PersonId,
                @event.Detail.Entity.CallAttendeeId,
                @event.Detail.Entity.CallAttendeeName,
                @event.Detail.Entity.JoinedDateTime
            );

            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}
