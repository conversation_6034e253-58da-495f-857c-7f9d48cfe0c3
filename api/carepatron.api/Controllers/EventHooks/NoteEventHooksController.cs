﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Authorization.Commands;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Notes.Commands;
using carepatron.core.Application.Notes.Events;
using carepatron.core.Authorization.Constants;
using carepatron.core.Authorization.Models;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks
{
    /// <summary>
    /// Controller for managing note event hooks.
    /// </summary>
    [ControllerProperties(CodeOwner.NotesAndDocuments)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class NoteEventHooksController : ControllerBase, IHasEventBindings
    {
        private readonly IMediator mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="NoteEventHooksController"/> class.
        /// </summary>
        /// <param name="mediator">Mediator instance used to send or publish messages.</param>
        public NoteEventHooksController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        /// <summary>
        /// Setup note access for a shared note.
        /// </summary>
        /// <remarks>
        /// This endpoint sets up note access for a shared note. 
        /// The details of the shared note are provided in the event body. 
        /// If the note access is successfully set up, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the shared note.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("notes/SetupNoteAccess", nameof(NoteSharedEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SetupNoteAccess([FromBody] ReceivedEvent<NoteSharedEvent> @event)
        {
            var command = new SetupNoteAccessCommand(@event.Detail.ProviderId, @event.Detail.Initiator.PersonId, @event.Detail.Entity.Id, @event.Detail.SharedWithPersonId, @event.Detail.Role, @event.Detail.SendMessage, @event.Detail.Message);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Delete user access token for a note.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes the user access token for a specific note. 
        /// The details of the note are provided in the event body. 
        /// If the user access token is successfully deleted, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the note.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("notes/DeleteUserAccessToken", nameof(NoteStoppedSharingEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteUserAccessToken([FromBody] ReceivedEvent<NoteStoppedSharingEvent> @event)
        {
            var command = new DeleteAccessTokenCommand(@event.Detail.Entity.Id, core.Application.Security.Models.TokenResource.Note, @event.Detail.PersonId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Delete user's note policy.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes the note policy for a specific user. 
        /// The details of the note are provided in the event body. 
        /// If the note policy is successfully deleted, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the note.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("notes/DeleteUsersNotePolicy", nameof(NoteStoppedSharingEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteUsersNotePolicy([FromBody] ReceivedEvent<NoteStoppedSharingEvent> @event)
        {
            var spec = new PolicyQuerySpecification(PolicyScopeValues.Notes)
                .AddResource(@event.Detail.Entity.ResourceName)
                .AddIdentity(@event.Detail.PersonId.ToString());

            var command = new DeleteResourcePolicyCommand(spec);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Delete access tokens for a deleted note.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes the access tokens for a specific note. 
        /// The details of the note are provided in the event body. 
        /// If the access tokens are successfully deleted, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the deleted note.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("notes/DeleteAccessTokens", nameof(NoteDeletedEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteAccessTokens([FromBody] ReceivedEvent<NoteDeletedEvent> @event)
        {
            var command = new DeleteAccessTokenCommand(@event.Detail.Entity.Id, core.Application.Security.Models.TokenResource.Note);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Delete note policies for a deleted note.
        /// </summary>
        /// <remarks>
        /// This endpoint deletes the note policies for a specific note. 
        /// The details of the note are provided in the event body. 
        /// If the note policies are successfully deleted, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the deleted note.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("notes/DeleteNotePolicies", nameof(NoteDeletedEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteNotePolicies([FromBody] ReceivedEvent<NoteDeletedEvent> @event)
        {
            var spec = new PolicyQuerySpecification(PolicyScopeValues.Notes)
                .AddResource(@event.Detail.Entity.ResourceName);

            var command = new DeleteResourcePolicyCommand(spec);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Send an email when a note is unlocked.
        /// </summary>
        /// <remarks>
        /// This endpoint sends an email when a note is unlocked. 
        /// The details of the unlocked note are provided in the event body. 
        /// If the email is successfully sent, the result of the operation is returned. 
        /// If the event body is invalid, a 400 status code is returned.
        /// </remarks>
        /// <param name="event">The event containing the details of the unlocked note.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("notes/SendUnlockedNoteEmail", nameof(NoteUnlockedEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SendUnlockedNoteEmail([FromBody] ReceivedEvent<NoteUnlockedEvent> @event)
        {
            var command = new SendUnlockedNoteEmailCommand(@event.Detail.Entity.Id, @event.Detail.Initiator.PersonId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Send an email when a note is submitted and updates Contact information if note contains client information fields.
        /// </summary>
        /// <param name="event">The event containing the details of the submitted note.</param>
        /// <returns>A result indicating whether the operation was successful.</returns>
        /// <response code="200">Returns the result of the operation.</response>
        [EventBridgeTarget("notes/NoteFormResponsesSubmitted", nameof(NoteFormResponsesSubmittedEvent))]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> NoteFormResponsesSubmitted([FromBody] ReceivedEvent<NoteFormResponsesSubmittedEvent> @event)
        {
            var command = new NoteFormResponsesSubmittedCommand(@event.Detail.Entity.Id, @event.Detail.Initiator.PersonId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("notes/RestoreContactNotes", nameof(ContactRestoredEvent))]
        public async Task<IActionResult> RestoreNotes([FromBody] ReceivedEvent<ContactRestoredEvent> @event)
        {
            var command = new RestoreContactNotesCommand(@event.Detail.ProviderId, @event.Detail.Entity.Id);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }


        [EventBridgeTarget("notes/DeleteContactNotes", nameof(ContactTrashedEvent))]
        public async Task<IActionResult> DeleteNotes([FromBody] ReceivedEvent<ContactDeletedEvent> @event)
        {
            var command = new DeleteContactNotesCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.Id);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("notes/MergeNotes", nameof(ContactMergedEvent))]
        public async Task<IActionResult> MergeNotes([FromBody] ReceivedEvent<ContactMergedEvent> @event)
        {
            var command = new MergeNotesCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.Id, @event.Detail.ArchivedContactIds);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}
