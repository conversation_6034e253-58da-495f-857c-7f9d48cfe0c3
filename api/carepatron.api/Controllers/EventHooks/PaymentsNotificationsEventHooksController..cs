using System.Threading.Tasks;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Workspace.Billing.Commands;
using carepatron.core.Application.Workspace.Billing.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.BillingAndPayments)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class PaymentsNotificationsEventHooksController(IMediator mediator) : ControllerBase, IHasEventBindings
{

    [EventBridgeTarget("notifications/payments/ChargesDisabled", nameof(PaymentAccountStatusUpdatedEvent))]
    public async Task<IActionResult> SendChargesDisabledNotification([FromBody] ReceivedEvent<PaymentAccountStatusUpdatedEvent> @event)
    {
        if (@event.Detail.Entity.ChargesEnabled == false && @event.Detail.Previous.ChargesEnabled)
        {
            var command = new SendPaymentsDisabledNotificationCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.PaymentProvider);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
        return Ok();
    }
}