﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Communications.Chats.Commands;
using carepatron.core.Application.Communications.Inbox.Events;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Application.Staff.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.Communications)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class ChatsEventHooksController(IMediator mediator) : ControllerBase, IHasEventBindings
{
    private readonly IMediator mediator = mediator;

    [EventBridgeTarget("chats/CleanupStaffChatConversations", nameof(StaffDeleted))]
    public async Task<IActionResult> CleanUpStaffChatConversations([FromBody] ReceivedEvent<StaffDeleted> @event)
    {
        var command = new CleanUpChatConversationsCommand(@event.Detail.Entity.ProviderId,
            @event.Detail.Entity.PersonId.ToString(),
            AccountType.Staff);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("chats/NotifyChatGroupCreated", nameof(ChatGroupCreatedEvent))]
    public async Task<IActionResult> NotifyChatGroupCreated([FromBody] ReceivedEvent<ChatGroupCreatedEvent> @event)
    {
        var command = new NotifyChatGroupCreatedCommand(@event.Detail.ProviderId,
            @event.Detail.ChatGroup,
            @event.Detail.EventInitiator.PersonId);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("chats/NotifyChatGroupDeleted", nameof(ChatGroupDeletedEvent))]
    public async Task<IActionResult> NotifyChatGroupDeleted([FromBody] ReceivedEvent<ChatGroupDeletedEvent> @event)
    {
        var command = new NotifyChatGroupDeletedCommand(@event.Detail.ProviderId,
            @event.Detail.ChatGroup,
            @event.Detail.EventInitiator.PersonId);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("chats/NotifyChatParticipantsAdded", nameof(ChatGroupParticipantsAddedEvent))]
    public async Task<IActionResult> NotifyChatGroupParticipantsAdded([FromBody] ReceivedEvent<ChatGroupParticipantsAddedEvent> @event)
    {
        var command = new NotifyChatGroupParticipantsAddedCommand(@event.Detail.ProviderId,
            @event.Detail.ChatGroup,
            @event.Detail.Participants,
            @event.Detail.EventInitiator.PersonId);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("chats/NotifyChatParticipantsRemoved", nameof(ChatGroupParticipantsRemovedEvent))]
    public async Task<IActionResult> NotifyChatGroupParticipantsRemoved([FromBody] ReceivedEvent<ChatGroupParticipantsRemovedEvent> @event)
    {
        var command = new NotifyChatGroupParticipantsRemovedCommand(@event.Detail.ProviderId,
            @event.Detail.ChatGroup,
            @event.Detail.Participants,
            @event.Detail.EventInitiator.PersonId);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("chats/NotifyChatGroupUpdated", nameof(ChatGroupUpdatedEvent))]
    public async Task<IActionResult> NotifyChatGroupUpdated([FromBody] ReceivedEvent<ChatGroupUpdatedEvent> @event)
    {
        var command = new NotifyChatGroupUpdatedCommand(@event.Detail.ProviderId,
            @event.Detail.ChatGroup,
            @event.Detail.Participants,
            @event.Detail.EventInitiator.PersonId);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("chats/NotifyChatMessageReceived", nameof(ChatMessageReceivedEvent))]
    public async Task<IActionResult> NotifyChatMessageReceived([FromBody] ReceivedEvent<ChatMessageReceivedEvent> @event)
    {
        var command = new NotifyChatMessageReceivedCommand(@event.Detail.ProviderId,
            @event.Detail.ConversationId,
            @event.Detail.Id,
            @event.Detail.EventInitiator.PersonId);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("chats/NotifyChatMessageEdited", nameof(ChatMessageEditedEvent))]
    public async Task<IActionResult> NotifyChatMessageEdited([FromBody] ReceivedEvent<ChatMessageEditedEvent> @event)
    {
        var command = new NotifyChatMessageEditedCommand(@event.Detail.ProviderId,
            @event.Detail.ConversationId,
            @event.Detail.Id,
            @event.Detail.EventInitiator.PersonId);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("chats/NotifyChatMessageDeleted", nameof(ChatMessageDeletedEvent))]
    public async Task<IActionResult> NotifyChatMessageDeleted([FromBody] ReceivedEvent<ChatMessageDeletedEvent> @event)
    {
        var command = new NotifyChatMessageDeletedCommand(@event.Detail.ProviderId,
            @event.Detail.ConversationId,
            @event.Detail.Id,
            @event.Detail.EventInitiator.PersonId);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }
}
