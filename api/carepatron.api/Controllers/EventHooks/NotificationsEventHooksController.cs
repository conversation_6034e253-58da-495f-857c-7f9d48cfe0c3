﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Billing.Events;
using carepatron.core.Application.Booking.Events;
using carepatron.core.Application.Communications.Inbox.Events;
using carepatron.core.Application.Communications.Notifications.Commands;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Files.Events;
using carepatron.core.Application.Notes.Events;
using carepatron.core.Application.PublicForms.Events;
using carepatron.core.Application.Referrals.Events;
using carepatron.core.Application.Staff.Events;
using carepatron.core.Application.Tasks.Events;
using carepatron.core.Application.Templates.Events;
using carepatron.core.Models.Execution;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.Communications)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class NotificationsEventHooksController(IMediator mediator) : ControllerBase, IHasEventBindings
{
    private readonly IMediator mediator = mediator;

    [EventBridgeTarget("notifications/SendBookingCreated", "BookingCreated")]
    public async Task<IActionResult> SendBookingCreatedNotification([FromBody] ReceivedEvent<BookingCreatedEventDetail> @event)
    {
        var command = new SendBookingCreatedNotificationCommand(@event.Detail.ProviderId, @event.Detail.TaskId, @event.Detail.ContactId);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendClientFileUploaded", nameof(FileUploadedEvent))]
    public async Task<IActionResult> SendClientFileUploadedNotification([FromBody] ReceivedEvent<FileUploadedEvent> @event)
    {
        // Handle sentry error CU: CP-19970 - https://app.clickup.com/t/**********/CP-19970
        // The person id is not always available in the event detail, i.e if the event is emitted through the RegisterClientCommandHandler
        var personId = @event.Detail.Initiator?.PersonId;
        if (personId.IsNullOrEmpty()) return this.EvalExecutionResult((ExecutionResult<Unit>)Unit.Value);

        var command = new SendClientFileUploadedNotificationCommand(@event.Detail.ProviderId, @event.Detail.Entity.Id, @event.Detail.Entity.ContactId, personId.Value);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendClientEventCancelled", "ClientEventCancelled")]
    public async Task<IActionResult> SendClientEventCancelledNotification([FromBody] ReceivedEvent<ClientEventCancelledEventDetail> @event)
    {
        var command = new SendClientEventCancelledNotificationCommand(@event.Detail.ProviderId,
            @event.Detail.TaskId,
            @event.Detail.TaskContactId,
            @event.Detail.TaskTitle,
            @event.Detail.StaffIds,
            @event.Detail.StartDate,
            @event.Detail.EndDate,
            @event.Detail.RRule,
            @event.Detail.Reason,
            @event.Detail.InitiatedByPersonId);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendUpdatedTask", nameof(TaskUpdatedEvent))]
    public async Task<IActionResult> SendUpdatedTaskNotification([FromBody] ReceivedEvent<TaskUpdatedEvent> @event)
    {
        // Rely on the logic of the emitter for the new staff ids
        var command = new SendTaskAssignedNotificationCommand(@event.Detail.Entity.TaskId,
            @event.Detail.Entity.ProviderId,
            @event.Detail.Entity.Title,
            @event.Detail.Entity.LastUpdatedByPersonId,
            @event.Detail.Entity.StartDate,
            @event.Detail.Entity.EndDate,
            @event.Detail.Entity.RRule,
            @event.Detail.NewStaffIds);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendCreatedTask", nameof(TaskCreatedEvent))]
    public async Task<IActionResult> SendCreatedTaskNotification([FromBody] ReceivedEvent<TaskCreatedEvent> @event)
    {
        var command = new SendTaskAssignedNotificationCommand(@event.Detail.Entity.TaskId,
            @event.Detail.Entity.ProviderId,
            @event.Detail.Entity.Title,
            @event.Detail.Entity.LastUpdatedByPersonId,
            @event.Detail.Entity.StartDate,
            @event.Detail.Entity.EndDate,
            @event.Detail.Entity.RRule,
            @event.Detail.Entity.StaffIds);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendFormResponsesSubmitted", nameof(NoteFormResponsesSubmittedEvent))]
    public async Task<IActionResult> SendFormResponsesSubmitted([FromBody] ReceivedEvent<NoteFormResponsesSubmittedEvent> @event)
    {
        var evt = @event.Detail;
        var command = new SendNoteFormSubmittedCommandCommand(evt.Entity.Id, evt.Entity.ProviderId, evt.Initiator.PersonId, evt.Entity.ContactId, evt.Entity.NoteTitle);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }
    
    [EventBridgeTarget("notifications/SendPublicFormSubmitted", nameof(PublicFormResponsesSubmittedEvent))]
    public async Task<IActionResult> SendPublicFormResponsesSubmittedNotification([FromBody] ReceivedEvent<PublicFormResponsesSubmittedEvent> @event)
    {
        var evt = @event.Detail;
        var command = new SendPublicFormResponsesSubmittedNotificationCommand(evt.Entity.Id, evt.Entity.ProviderId, evt.Initiator.PersonId, evt.Entity.ContactId, evt.Entity.NoteTitle, evt.AdminsPersonIds);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }
    
    [EventBridgeTarget("notifications/TemplateImportCompleted", nameof(TemplateImportCompletedEvent))]
    public async Task<IActionResult> SendTemplateImportCompletedNotification([FromBody] ReceivedEvent<TemplateImportCompletedEvent> @event)
    {
        var evt = @event.Detail;
        var command = new SendTemplateImportCompletedNotificationCommand(evt.Entity.Id, evt.Entity.TemplateId, evt.Entity.FileId, evt.Entity.ProviderId, evt.Initiator.PersonId, evt.Entity.TemplateTitle);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/TemplateImportFailed", nameof(TemplateImportFailedEvent))]
    public async Task<IActionResult> SendTemplateImportFailedNotification([FromBody] ReceivedEvent<TemplateImportFailedEvent> @event)
    {
        var evt = @event.Detail;
        var command = new SendTemplateImportFailedNotificationCommand(evt.Entity.Id, evt.Entity.FileId, evt.Entity.FileName, evt.Entity.ProviderId, evt.Initiator.PersonId);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendClientEventRescheduled", "TaskRescheduledEvent")]
    public async Task<IActionResult> SendClientEventRescheduledNotification([FromBody] ReceivedEvent<TaskRescheduledEventDetail> @event)
    {
        var initiatedByPersonId = @event.Detail.RescheduledByPersonId;

        if (initiatedByPersonId.IsNullOrEmpty()) return this.EvalExecutionResult((ExecutionResult<Unit>)Unit.Value);

        var command = new SendClientEventRescheduledNotificationCommand(@event.Detail.ProviderId,
            @event.Detail.TaskId,
            initiatedByPersonId.Value,
            @event.Detail.Title,
            @event.Detail.StaffIds,
            @event.Detail.NewTime.FromDate,
            @event.Detail.NewTime.ToDate,
            @event.Detail.RRule);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendClientEventConfirmed", "ClientEventConfirmed")]
    public async Task<IActionResult> SendClientEventConfirmedNotification([FromBody] ReceivedEvent<ClientEventConfirmedEventDetail> @event)
    {
        var initiatedByPersonId = @event.Detail.InitiatedByPersonId;

        // If initiated by person id is null or the note is not firstPublished
        if (initiatedByPersonId.IsNullOrEmpty()) return this.EvalExecutionResult((ExecutionResult<Unit>)Unit.Value);

        var command = new SendClientEventConfirmedNotificationCommand(@event.Detail.ProviderId,
            @event.Detail.TaskId,
            @event.Detail.TaskContactId,
            @event.Detail.IsConfirmed,
            @event.Detail.Title,
            @event.Detail.StaffIds,
            @event.Detail.StartDate,
            @event.Detail.EndDate,
            @event.Detail.RRule,
            initiatedByPersonId.Value);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    // Subscribe to the same NoteUpdatedEvent but check for isFirstPublish = true
    // A note is always created on a draft state
    // But we don't want to send notifications on a notes creation in draft state
    [EventBridgeTarget("notifications/SendClientAddedNote", nameof(NoteUpdatedEvent))]
    public async Task<IActionResult> SendClientAddedNoteNotification([FromBody] ReceivedEvent<NoteUpdatedEvent> @event)
    {
        var initiatedByPersonId = @event.Detail.Initiator?.PersonId;

        // If initiated by person id is null or the note is not firstPublished
        if (initiatedByPersonId.IsNullOrEmpty()) return this.EvalExecutionResult((ExecutionResult<Unit>)Unit.Value);

        var command = new SendClientAddedNoteNotificationCommand(@event.Detail.ProviderId,
            @event.Detail.Entity.Id,
            @event.Detail.Entity.ContactId,
            initiatedByPersonId.Value,
            @event.Detail.Entity.NoteTitle,
            @event.Detail.Entity.Status,
            @event.Detail.isFirstPublish);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    // Subscribe to the same NoteUpdatedEvent but check for isFirstPublish = false
    [EventBridgeTarget("notifications/SendClientUpdatedNote", nameof(NoteUpdatedEvent))]
    public async Task<IActionResult> SendClientUpdatedNoteNotification([FromBody] ReceivedEvent<NoteUpdatedEvent> @event)
    {
        var initiatedByPersonId = @event.Detail.Initiator?.PersonId;

        // If initiated by person id is null and the note is firstPublished
        if (initiatedByPersonId.IsNullOrEmpty()) return this.EvalExecutionResult((ExecutionResult<Unit>)Unit.Value);

        var command = new SendClientUpdatedNoteNotificationCommand(@event.Detail.ProviderId,
            @event.Detail.Entity.Id,
            @event.Detail.Entity.ContactId,
            initiatedByPersonId.Value,
            @event.Detail.Entity.NoteTitle,
            @event.Detail.Entity.Status,
            @event.Detail.isFirstPublish);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendReferralJoined", nameof(ProviderReferralJoinedEvent))]
    public async Task<IActionResult> SendReferralJoinedNotification([FromBody] ReceivedEvent<ProviderReferralJoinedEvent> @event)
    {
        var command = new SendReferralJoinedNotificationCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.StaffPersonId, @event.Detail.Entity.ReferredProviderName, @event.Detail.Entity.ReferredEmail, @event.Detail.ReferredPersonId);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }
    
    [EventBridgeTarget("notifications/SendSubPaymentFailed", nameof(SubscriptionPaymentFailedEvent))]
    public async Task<IActionResult> SendSubscriptionPaymentFailedNotification([FromBody] ReceivedEvent<SubscriptionPaymentFailedEvent> @event)
    {
        var command = new SendSubscriptionPaymentFailedNotificationCommand(@event.Detail.Entity.ProviderId,
            @event.Detail.Entity.BillingAccountType,
            @event.Detail.Entity.BillingAccountStatus,
            @event.Detail.Previous.BillingAccountStatus);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendStaffContactsAssigned", nameof(StaffContactsAssigned))]
    public async Task<IActionResult> SendStaffContactAssignedNotification([FromBody] ReceivedEvent<StaffContactsAssigned> @event)
    {
        var initiatedByPersonId = @event.Detail.Initiator?.PersonId;

        // If initiated by person id is null and the note is firstPublished
        if (initiatedByPersonId.IsNullOrEmpty()) return this.EvalExecutionResult((ExecutionResult<Unit>)Unit.Value);

        var command = new SendStaffContactAssignmentNotificationCommand(@event.Detail.Entity.ProviderId,
            @event.Detail.Entity.PersonId,
            @event.Detail.NewContactIds,
            initiatedByPersonId.Value);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendContactStaffAssigned", nameof(ContactStaffAssigned))]
    public async Task<IActionResult> SendContactStaffAssignedNotification([FromBody] ReceivedEvent<ContactStaffAssigned> @event)
    {
        var initiatedByPersonId = @event.Detail.Initiator?.PersonId;

        // If initiated by person id is null and the note is firstPublished
        if (initiatedByPersonId.IsNullOrEmpty()) return this.EvalExecutionResult((ExecutionResult<Unit>)Unit.Value);

        var command = new SendStaffContactAssignmentNotificationCommand(@event.Detail.Entity.ProviderId,
            @event.Detail.StaffPersonId,
            [@event.Detail.Entity.Id],
            initiatedByPersonId.Value);

        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendStaffNewInboxAssigned", nameof(InboxCreatedEvent))]
    public async Task<IActionResult> SendStaffInboxAssignedFromCreateInboxNotification([FromBody] ReceivedEvent<InboxCreatedEvent> @event)
    {
        var initiatedByPersonId = @event.Detail.Initiator?.PersonId;
        if (initiatedByPersonId.IsNullOrEmpty()) return this.EvalExecutionResult((ExecutionResult<Unit>)Unit.Value);

        var command = new SendStaffInboxAssignedNotificationCommand(
            @event.Detail.Entity.ProviderId,
            @event.Detail.Entity.Id,
            @event.Detail.Entity.Name,
            initiatedByPersonId.Value,
            @event.Detail.Entity.Staffs
        );
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendStaffInboxAssigned", nameof(InboxStaffAddedEvent))]
    public async Task<IActionResult> SendStaffInboxAssignedNotification([FromBody] ReceivedEvent<InboxStaffAddedEvent> @event)
    {
        var initiatedByPersonId = @event.Detail.Initiator?.PersonId;
        if (initiatedByPersonId.IsNullOrEmpty()) return this.EvalExecutionResult((ExecutionResult<Unit>)Unit.Value);

        var command = new SendStaffInboxAssignedNotificationCommand(
            @event.Detail.Entity.ProviderId,
            @event.Detail.Entity.Id,
            @event.Detail.Entity.Name,
            initiatedByPersonId.Value,
            @event.Detail.Entity.Staffs
        );
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendStaffInboxUnssigned", nameof(InboxStaffDeletedEvent))]
    public async Task<IActionResult> SendStaffInboxUnassignedNotification(
        [FromBody] ReceivedEvent<InboxStaffDeletedEvent> @event
    )
    {
        var initiatedByPersonId = @event.Detail.Initiator?.PersonId;
        if (initiatedByPersonId.IsNullOrEmpty())
            return this.EvalExecutionResult((ExecutionResult<Unit>)Unit.Value);

        var command = new SendStaffInboxUnassignedNotificationCommand(
            @event.Detail.Entity.ProviderId,
            @event.Detail.Entity.Id,
            @event.Detail.Entity.Name,
            initiatedByPersonId.Value,
            @event.Detail.Entity.Staffs
        );
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("notifications/SendStaffInboxDeleted", nameof(InboxDeleted))]
    public async Task<IActionResult> SendStaffInboxDeletedNotification(
        [FromBody] ReceivedEvent<InboxDeleted> @event
    )
    {
        var initiatedByPersonId = @event.Detail.InitiatedByPersonId;
        if (initiatedByPersonId.IsEmpty())
            return this.EvalExecutionResult((ExecutionResult<Unit>)Unit.Value);

        var command = new SendStaffInboxUnassignedNotificationCommand(
            @event.Detail.ProviderId,
            @event.Detail.InboxId,
            @event.Detail.InboxName,
            initiatedByPersonId,
            @event.Detail.Staffs
        );
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }
}
