using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using carepatron.core.Application.Billing.Events;
using MediatR;
using carepatron.core.Application.FeatureModules.Commands;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.BillingAndPayments)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class BillingAccountEventHooksController(
    IMediator mediator
) : ControllerBase, IHasEventBindings
{
    [EventBridgeTarget("billingAccount/UpdateFeatureModules", nameof(SubscriptionUpdatedEvent))]
    public async Task<IActionResult> UpdateFeatureModules([FromBody] ReceivedEvent<SubscriptionUpdatedEvent> @event)
    {
        await mediator.Send(new UpdateFeatureModulesCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.BillingAccountType, @event.Detail.Previous.BillingAccountType));
        return Ok();
    }
}
