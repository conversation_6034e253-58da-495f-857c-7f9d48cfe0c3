using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Billing.Commands;
using carepatron.core.Application.Registrations.Events;
using carepatron.core.Models.Billing;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using carepatron.core.Application.Partnership.Commands;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.Workspace)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class WorkspaceEventHooksController(
    IMediator mediator
) : ControllerBase, IHasEventBindings
{
    [EventBridgeTarget("workspace/CreateStripeAccount", nameof(WorkspaceCreatedEvent))]
    public async Task<IActionResult> CreateStripeAccount([FromBody] ReceivedEvent<WorkspaceCreatedEvent> @event)
    {
        // If request has a promo code or referral code, we create a subscription for the user
        // if not, we only create a Stripe customer for the user and skip subscriptions
        if (!string.IsNullOrEmpty(@event.Detail.CustomerReferralCode)
            || !string.IsNullOrEmpty(@event.Detail.ReferralCode)
            || @event.Detail.IsInitialTrial)
        {
            // If request has a referral code, then setup a trial subscription for the user
            // This subscription will bypass the email verification check
            var command = new SetupStripeAccountCommand(
                null,
                @event.Detail.Entity.Id,
                @event.Detail.Initiator.PersonId,
                BillingAccountType.Advanced,
                @event.Detail.ReferralCode,
                @event.Detail.CustomerReferralCode,
                BillingPeriod.Monthly,
                false,
                @event.Detail.IsInitialTrial
            );
            var result = await mediator.Send(command);

            // Transform some error codes to avoid errors, DLQs or retries. 
            switch (result?.Error?.Code)
            {
                // skip creating stripe accounts if the promo code is invalid.
                case Errors.PromotionCodeInvalidCode: return Ok(result.Error);
                // if the account already exists there's nothing we need to do
                case Errors.ProviderHasExistingBillingAccountErrorCode: return Ok(result.Error);
            }

            return this.EvalExecutionResult(result);
        }
        else
        {
            var result = await mediator.Send(new CreateStripeBillingAccountCommand(@event.Detail.Entity.Id, @event.Detail.Initiator.PersonId));
            return this.EvalExecutionResult(result);
        }
    }

    [EventBridgeTarget("workspace/CreatePartnerStackCustomer", nameof(WorkspaceCreatedEvent))]
    public async Task<IActionResult> CreatePartnerStackCustomer([FromBody] ReceivedEvent<WorkspaceCreatedEvent> @event)
    {
        var command = new CreatePartnerStackCustomerCommand(@event.Detail.Entity.Id, @event.Detail.Initiator.PersonId, @event.Detail.ClickId);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }
}