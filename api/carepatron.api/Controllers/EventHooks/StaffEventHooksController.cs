﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Authorization.Commands;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Staff.Commands;
using carepatron.core.Application.Staff.Events;
using carepatron.core.Authorization.Models;
using carepatron.core.Authorization.ResourceNames;
using carepatron.core.Repositories.SQS;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using carepatron.core.Application.Booking.Events;
using carepatron.core.Application.Tasks.Events;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.Workspace)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class StaffEventHooksController(IMediator mediator, ISqsRepository sqsRepository) : ControllerBase, IHasEventBindings
    {

        [EventBridgeTarget("staff/AssignContactPolicy", nameof(ContactStaffAssigned))]
        public async Task<IActionResult> AssignContactPolicy([FromBody] ReceivedEvent<ContactStaffAssigned> @event)
        {
            var command = new SetupStaffContactPoliciesCommand(
                @event.Detail.ProviderId,
                @event.Detail.StaffPersonId,
                new[] { @event.Detail.Entity.Id },
                null,
                null);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/AssignContactPolicies", nameof(StaffContactsAssigned))]
        public async Task<IActionResult> AssignContactPolicies([FromBody] ReceivedEvent<StaffContactsAssigned> @event)
        {
            var command = new SetupStaffContactPoliciesCommand(
                @event.Detail.ProviderId,
                @event.Detail.Entity.PersonId,
                @event.Detail.ContactIds,
                null,
                null);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/UpdateContactPolicies", nameof(StaffPermissionsChanged))]
        public async Task<IActionResult> UpdateContactPolicies([FromBody] ReceivedEvent<StaffPermissionsChanged> @event)
        {
            var command = new SetupStaffContactPoliciesCommand(
                @event.Detail.ProviderId, @event.Detail.Entity.PersonId, null, @event.Detail.Entity, @event.Detail.PreviousPermissions);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/SetupContactPolicies", nameof(StaffMemberInvited))]
        public async Task<IActionResult> SetupContactPolicies([FromBody] ReceivedEvent<StaffMemberInvited> @event)
        {
            var command = new SetupStaffContactPoliciesCommand(@event.Detail.ProviderId, @event.Detail.Entity.PersonId, null, null, null);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/DeleteContactPolicy", nameof(ContactStaffRemoved))]
        public async Task<IActionResult> DeleteContactPolicy([FromBody] ReceivedEvent<ContactStaffRemoved> @event)
        {
            var command = new DeleteStaffContactPoliciesCommand(@event.Detail.ProviderId, @event.Detail.StaffPersonId, [@event.Detail.Entity.Id]);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/DeleteContactPolicies", nameof(StaffContactsUnassigned))]
        public async Task<IActionResult> DeleteContactPolicies([FromBody] ReceivedEvent<StaffContactsUnassigned> @event)
        {
            var command = new DeleteStaffContactPoliciesCommand(@event.Detail.ProviderId, @event.Detail.Entity.PersonId, @event.Detail.ContactIds);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/DeletePolicies", nameof(StaffDeleted))]
        public async Task<IActionResult> DeletePolicies([FromBody] ReceivedEvent<StaffDeleted> @event)
        {
            var resource = new ResourceNameBuilder().Provider(@event.Detail.ProviderId).Build();

            var spec = new PolicyQuerySpecification(null)
                .AddResource(resource)
                .AddIdentity(@event.Detail.Entity.PersonId.ToString())
                .WithResourceMatchingBehaviour(ResourceMatchingBehaviour.Contains);

            var command = new DeleteResourcePolicyCommand(spec);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Schedule a job to update billed users when a staff member is invited
        /// </summary>
        /// <param name="event"></param>
        /// <returns></returns>
        [EventBridgeTarget("staff/UpdateBilledUsersInvited", nameof(StaffMemberInvited))]
        public async Task<IActionResult> UpdateBilledUsersInvitedStaff([FromBody] ReceivedEvent<StaffMemberInvited> @event)
        {
            var command = new ScheduleUpdateBilledUserCommand(@event.Detail.ProviderId, @event.Detail.Entity);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        /// <summary>
        /// Schedule a job to update billed users when a staff member is deleted
        /// </summary>
        /// <param name="event"></param>
        /// <returns></returns>
        [EventBridgeTarget("staff/UpdateBilledUsersDeleted", nameof(StaffDeleted))]
        public async Task<IActionResult> UpdateBilledUsersDeletedStaff([FromBody] ReceivedEvent<StaffDeleted> @event)
        {
            var command = new ScheduleUpdateBilledUserCommand(@event.Detail.ProviderId, @event.Detail.Entity);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/AutoAssignStaffToContact", nameof(TaskCreatedEvent))]
        public async Task<IActionResult> AutoAssignStaffToContact([FromBody] ReceivedEvent<TaskCreatedEvent> @event)
        {
            var command = new AutoAssignStaffToContactCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/AutoAssignStaffToContactFromBooking", "BookingCreated", targetGroup: "2")]
        public async Task<IActionResult> AutoAssignStaffToContactFromBooking([FromBody] ReceivedEvent<BookingCreatedEventDetail> @event)
        {
            var command = new AutoAssignStaffToContactCommand(@event.Detail.ProviderId, @event.Detail.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/DeclineExistingTasks", nameof(TaskCreatedEvent), targetGroup: "2")]
        public async Task<IActionResult> DeclineExistingTasksForCreatedTask([FromBody] ReceivedEvent<TaskCreatedEvent> @event)
        {
            var command = new DeclineExistingTasksCommand(@event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/DeclineNewTask", nameof(TaskCreatedEvent), targetGroup: "2")]
        public async Task<IActionResult> DeclineNewTaskForCreatedTask([FromBody] ReceivedEvent<TaskCreatedEvent> @event)
        {
            var command = new DeclineNewTaskCommand(@event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/DeclineExistingTasksForUpdatedTask", nameof(TaskUpdatedEvent), targetGroup: "2")]
        public async Task<IActionResult> DeclineExistingTasksForUpdatedTask([FromBody] ReceivedEvent<TaskUpdatedEvent> @event)
        {
            var command = new DeclineExistingTasksCommand(@event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("staff/DeclineNewTaskForUpdatedTask", nameof(TaskUpdatedEvent), targetGroup: "2")]
        public async Task<IActionResult> DeclineNewTaskForUpdatedTask([FromBody] ReceivedEvent<TaskUpdatedEvent> @event)
        {
            var command = new DeclineNewTaskCommand(@event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}
