using System.Linq;
using System.Threading.Tasks;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Billables.Commands;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Insurance.Commands;
using carepatron.core.Application.Insurance.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.BillingAndPayments)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class InsuranceClaimsEventHookController(IMediator mediator) : ControllerBase, IHasEventBindings
{
    [EventBridgeTarget("insurance/CreateClaimBillables", nameof(USProfessionalClaimCreatedEvent), disabled: true)]
    public IActionResult CreateClaimBillables([FromBody] ReceivedEvent<USProfessionalClaimCreatedEvent> @event)
    {
        return Ok();
    }

    [EventBridgeTarget("insurance/UpdateClaimBillables", nameof(USProfessionalClaimUpdatedEvent), disabled: true)]
    public IActionResult UpdateClaimBillables([FromBody] ReceivedEvent<USProfessionalClaimUpdatedEvent> @event)
    {
        return Ok();
    }

    [EventBridgeTarget("insurance/OnCreateSyncBillables", nameof(USProfessionalClaimCreatedEvent))]
    public async Task<IActionResult> OnCreateSyncBillables([FromBody] ReceivedEvent<USProfessionalClaimCreatedEvent> @event)
    {
        var command = new SyncClaimBillablesCommand(
            @event.Detail.Entity.ProviderId,
            @event.Detail.Entity.Id,
            []
        );
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("insurance/OnUpdateSyncBillables", nameof(USProfessionalClaimUpdatedEvent))]
    public async Task<IActionResult> OnUpdateSyncBillables([FromBody] ReceivedEvent<USProfessionalClaimUpdatedEvent> @event)
    {
        var currentItems = @event.Detail.Entity?.ServiceLines.EmptyIfNull().Select(x => x.BillableItemId).Where(x => x.HasValue).ToArray();
        var removedItems = @event.Detail.Previous?.ServiceLines.EmptyIfNull()
            .Select(s => s.BillableItemId).Where(x => x.HasValue && !currentItems.Contains(x.Value)).Select(x => x.Value).ToArray();

        var command = new SyncClaimBillablesCommand(
            @event.Detail.Entity.ProviderId,
            @event.Detail.Entity.Id,
            removedItems
        );
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("insurance/OnTrashSyncBillables", nameof(USProfessionalClaimTrashedEvent))]
    public async Task<IActionResult> OnTrashSyncBillables([FromBody] ReceivedEvent<USProfessionalClaimTrashedEvent> @event)
    {
        var command = new SyncClaimBillablesCommand(
            @event.Detail.Entity.ProviderId,
            @event.Detail.Entity.Id,
            []
        );
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("insurance/OnRestoreSyncBillables", nameof(USProfessionalClaimRestoredEvent))]
    public async Task<IActionResult> OnRestoreSyncBillables([FromBody] ReceivedEvent<USProfessionalClaimRestoredEvent> @event)
    {
        var command = new SyncClaimBillablesCommand(
            @event.Detail.Entity.ProviderId,
            @event.Detail.Entity.Id,
            []
        );
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }

    [EventBridgeTarget("insurance/OnTrashedContact", nameof(ContactTrashedEvent), targetGroup: "2")]
    public async Task<IActionResult> TrashContactCloseDraftClaim([FromBody] ReceivedEvent<ContactTrashedEvent> @event)
    {
        var command = new CloseDraftClaimsForContactCommand(
            @event.Detail.Entity.ProviderId,
            @event.Detail.Entity.Id
        );
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }
}
