﻿using System.Threading.Tasks;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Contacts.Commands;
using carepatron.core.Application.Contacts.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.Clients)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class ContactImportEventHooksController(IMediator mediator) : ControllerBase, IHasEventBindings
    {
        [EventBridgeTarget("contact-import/PreprocessDoneNotify", nameof(PreprocessImportContactCompletedEvent))]
        public async Task<IActionResult> SendPreprocessImportContactCompletedNotification([FromBody] ReceivedEvent<PreprocessImportContactCompletedEvent> @event)
        {
            var command = new SendPreprocessImportContactCompletedNotificationCommand(@event.Detail.Entity.ContactImportSummaryId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}