﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Registrations.Commands;
using carepatron.core.Application.Registrations.Events;
using carepatron.core.Application.Users.Commands;
using carepatron.core.Application.Users.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.Workspace)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class PersonEventHooksController : ControllerBase, IHasEventBindings
    {
        private readonly IMediator mediator;

        public PersonEventHooksController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        [EventBridgeTarget("person/SendEmailVerification", "EmailChangeRequest")]
        public async Task<IActionResult> SendEmailVerification([FromBody] ReceivedEvent<EmailChangeRequestEventDetail> @event)
        {
            var command = new EmailChangeSendVerificationCommand(@event.Detail.PersonId, @event.Detail.NewEmail, @event.Detail.Token);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("person/UpdatePersonEmail", "PersonEmailChanged")]
        public async Task<IActionResult> UpdatePersonEmail([FromBody] ReceivedEvent<PersonEmailChangedDetail> @event)
        {
            var command = new UpdatePersonEmailCommand(@event.Detail.PersonId, @event.Detail.NewEmail);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("person/CleanUpOldCognitoAccount", "PersonEmailChanged")]
        public async Task<IActionResult> CleanUpOldCognitoAccount([FromBody] ReceivedEvent<PersonEmailChangedDetail> @event)
        {
            var command = new CleanUpOldCognitoAccountCommand(@event.Detail.PreviousEmail, @event.Detail.NewEmail);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("person/UpdateRegisteredClientSub", "ClientRegistered")]
        public async Task<IActionResult> UpdateClientPersonSub([FromBody] ReceivedEvent<ClientRegisteredEventDetail> @event)
        {
            var command = new UpdateRegisteredClientSubCommand(@event.Detail.PersonId, @event.Detail.Sub);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("person/ClearPersonSub", nameof(CognitoAccountDeletedEvent))]
        public async Task<IActionResult> ClearPersonSub([FromBody] ReceivedEvent<CognitoAccountDeletedEvent> @event)
        {
            var command = new ClearPersonSubCommand(@event.Detail.PersonId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("person/partner-stack/CreateCustomer", nameof(PersonRegisteredEvent), Disabled = true)]
        public Task<IActionResult> CreatePartnerStackCustomer([FromBody] ReceivedEvent<PersonRegisteredEvent> @event)
        {
            return Task.FromResult<IActionResult>(Ok());
        }
    }
}