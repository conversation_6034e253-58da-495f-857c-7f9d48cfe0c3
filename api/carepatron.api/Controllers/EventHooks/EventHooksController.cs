﻿using System;
using System.Threading.Tasks;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Partnership.Commands;
using carepatron.core.Application.Workspace.Ownership.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Serilog;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.Platform)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class EventHooksController : ControllerBase, IHasEventBindings
{
    private readonly IMediator mediator;

    public EventHooksController(IMediator mediator)
    {
        this.mediator = mediator;
    }

    [EventBridgeTarget(path: "ping", matchesDetailType: "test-event")]
    public async Task<IActionResult> Ping([FromBody] ReceivedEvent<object> @event)
    {
        Log.Information("Event hooks ping received. {event}", JsonConvert.SerializeObject(@event));
        return Ok();
    }

    [EventBridgeTarget(path: "UpdatePartnerStackCustomer", matchesDetailType: "WorkspaceOwnershipTransferredEventDetail")]
    public async Task<IActionResult> UpdatePartnerStackCustomer([FromBody] ReceivedEvent<WorkspaceOwnershipTransferredEventDetail> @event)
    {
        var command = new UpdatePartnerStackCustomerCommand(@event.Detail.ProviderId, @event.Detail.OldOwnerId, @event.Detail.NewOwnerId);
        var result = await mediator.Send(command);
        return this.EvalExecutionResult(result);
    }
}