﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Booking.Events;
using carepatron.core.Application.Reminders.Commands;
using carepatron.core.Application.Tasks.Commands;
using carepatron.core.Application.Tasks.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using carepatron.core.Application.Reminders.Events;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.TasksAndScheduling)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class ReminderHooksController : ControllerBase, IHasEventBindings
    {
        private readonly IMediator mediator;

        public ReminderHooksController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        [EventBridgeTarget("reminders/ClientEventCancelled", "ClientEventCancelled")]
        public async Task<IActionResult> ClientEventCancelled([FromBody] ReceivedEvent<ClientEventCancelledEventDetail> @event)
        {
            var command = new ClientEventCancelledDeleteReminderCommand(@event.Detail.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("reminders/ClientEventConfirmed", "ClientEventConfirmed")]
        public async Task<IActionResult> ClientEventConfirmed([FromBody] ReceivedEvent<ClientEventConfirmedEventDetail> @event)
        {
            var command = new ClientEventConfirmedCreateReminderCommand(@event.Detail.ProviderId, @event.Detail.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("reminders/CreateBookingReminder", "BookingCreated", targetGroup: "2")]
        public async Task<IActionResult> CreateBookingReminder([FromBody] ReceivedEvent<BookingCreatedEventDetail> @event)
        {
            var command = new CreateTaskReminderCommand(@event.Detail.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("reminders/UpdateRebookedReminder", "TaskRescheduledEvent")]
        public async Task<IActionResult> UpdateRebookedReminder([FromBody] ReceivedEvent<TaskRescheduledEventDetail> @event)
        {
            // todo - Might be nice if we could use the same UpdateTaskReminderCommand for this.
            var command = new UpdateRebookedReminderCommand(@event.Detail.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("reminders/CreateTaskReminder", nameof(TaskCreatedEvent))]
        public async Task<IActionResult> CreateTaskReminder([FromBody] ReceivedEvent<TaskCreatedEvent> @event)
        {
            var command = new CreateTaskReminderCommand(@event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("reminders/UpdateTaskReminder", nameof(TaskUpdatedEvent))]
        public async Task<IActionResult> UpdateTaskReminder([FromBody] ReceivedEvent<TaskUpdatedEvent> @event)
        {
            var command = new UpdateTaskReminderCommand(@event.Detail.Previous, @event.Detail.Entity);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("reminders/DeleteTaskReminder", nameof(TaskDeletedEvent))]
        public async Task<IActionResult> DeleteTaskReminder([FromBody] ReceivedEvent<TaskDeletedEvent> @event)
        {
            var command = new DeleteTaskRemindersCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }


        [EventBridgeTarget("reminders/TaskTrashedDeleteReminder", nameof(TaskTrashedEvent))]
        public async Task<IActionResult> TaskTrashedDeleteReminder([FromBody] ReceivedEvent<TaskTrashedEvent> @event)
        {
            var command = new DeleteTaskRemindersCommand(@event.Detail.Entity.ProviderId, @event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("reminders/TaskRestoredCreateReminder", nameof(TaskRestoredEvent))]
        public async Task<IActionResult> TaskRestoredCreateReminder([FromBody] ReceivedEvent<TaskRestoredEvent> @event)
        {
            var command = new CreateTaskReminderCommand(@event.Detail.Entity.TaskId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("reminders/CreateTasksReminder", nameof(ReminderSettingCreatedEvent))]
        public async Task<IActionResult> CreateTasksReminder([FromBody] ReceivedEvent<ReminderSettingCreatedEvent> @event)
        {
            var command = new CreateTasksReminderCommand(@event.Detail.Entity.ReminderSettingId,
                @event.Detail.Entity.ProviderId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}
