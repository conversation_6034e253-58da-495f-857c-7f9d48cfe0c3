﻿using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Relationships.Commands;
using carepatron.core.Application.Relationships.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace carepatron.api.Controllers.EventHooks
{
    [ControllerProperties(CodeOwner.Clients)]
    [ApiExplorerSettings(IgnoreApi = true)]
    [Route("api/eventhooks")]
    [Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
    public class RelationshipEventHooksController : ControllerBase, IHasEventBindings
    {
        private readonly IMediator mediator;

        public RelationshipEventHooksController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        [EventBridgeTarget("relationships/CreatePolicies", nameof(RelationshipAccessGrantedEvent))]
        public async Task<IActionResult> CreatePolicies([FromBody] ReceivedEvent<RelationshipAccessGrantedEvent> @event)
        {
            var command = new CreateRelationshipAccessPoliciesCommand(@event.Detail.Entity.ContactId, @event.Detail.Entity.ToContactId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("relationships/DeletePolicies", nameof(RelationshipAccessRemovedEvent))]
        public async Task<IActionResult> DeletePolicies([FromBody] ReceivedEvent<RelationshipAccessRemovedEvent> @event)
        {
            var command = new DeleteRelationshipAccessPoliciesCommand(@event.Detail.Entity.ContactId, @event.Detail.Entity.ToContactId);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }

        [EventBridgeTarget("relationships/UpdatePolicies", nameof(ContactPersonChangedEvent))]
        public async Task<IActionResult> UpdatePolicies([FromBody] ReceivedEvent<ContactPersonChangedEvent> @event)
        {
            var command = new UpdateRelationshipAccessPoliciesCommand(@event.Detail.PreviousPersonId, @event.Detail.Entity.PersonId, @event.Detail.Entity.Id);
            var result = await mediator.Send(command);
            return this.EvalExecutionResult(result);
        }
    }
}
