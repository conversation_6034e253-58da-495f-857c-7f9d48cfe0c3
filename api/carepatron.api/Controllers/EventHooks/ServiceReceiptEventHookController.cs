using System.Threading;
using System.Threading.Tasks;
using carepatron.api.Extensions;
using carepatron.api.Infrastructure.EventHooks;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.core.Application.ServiceReceipts.Commands;
using carepatron.core.Application.ServiceReceipts.Events;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace carepatron.api.Controllers.EventHooks;

[ControllerProperties(CodeOwner.BillingAndPayments)]
[ApiExplorerSettings(IgnoreApi = true)]
[Route("api/eventhooks")]
[Authorize(AuthenticationSchemes = ApiKeyAuthConstants.AuthenticationScheme)]
public class ServiceReceiptEventHookController(IMediator mediator) : ControllerBase, IHasEventBindings
{
    /// <summary>
    ///  Send notification when service receipt is generated but failed to send.
    /// </summary>
    /// <param name="event"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    [EventBridgeTarget("serviceReceipt/SendRequiresReview", nameof(ServiceReceiptRequiresReviewEvent))]
    public async Task<IActionResult> SendRequiresReview([FromBody] ReceivedEvent<ServiceReceiptRequiresReviewEvent> @event, CancellationToken cancellationToken)
    {
        var command = new SendServiceReceiptRequiresReviewNotificationCommand(@event.Detail.Entity.Id, @event.Detail.Entity.ProviderId, @event.Detail.Entity.ContactId, @event.Detail.Entity.Number, @event.Detail.Entity.ClientName);
        var result = await mediator.Send(command, cancellationToken);
        return this.EvalExecutionResult(result);
    }
}