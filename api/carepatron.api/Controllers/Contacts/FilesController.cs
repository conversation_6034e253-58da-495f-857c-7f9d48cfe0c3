﻿using carepatron.api.Constants;
using carepatron.api.Contracts.Requests.Contacts;
using carepatron.api.Contracts.Requests.Files;
using carepatron.api.Mappers;
using carepatron.core.Application.Contacts.Commands;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Files.Commands;
using carepatron.core.Application.Files.Queries;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Commands.Commands.Files;
using carepatron.core.Commands.Handlers;
using carepatron.core.Identity;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Media;
using carepatron.core.Models.Pagination;
using carepatron.core.Queries.Handlers;
using carepatron.core.Queries.Queries.Files;
using carepatron.core.Utilities;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using carepatron.core.Application.Files.Models;

namespace carepatron.api.Controllers.Contacts
{
    /// <summary>
    /// Controller for managing files associated with contacts.
    /// </summary>
    [ControllerProperties(CodeOwner.NotesAndDocuments)]
    [Route(ApiUrlPaths.ContactsFiles)]
    public class FilesController : BaseController
    { 
        private readonly IMediator mediator;

        /// <summary>
        /// Initializes a new instance of the <see cref="FilesController"/> class.
        /// </summary>
        /// <param name="identityContextFactory">The identity context factory.</param>
        /// <param name="mediator">The mediator.</param>
        public FilesController(
            IIdentityContextFactory identityContextFactory, 
            IMediator mediator)
            : base(identityContextFactory)
        {
            this.mediator = mediator; 
        }

        // Todo
        // These are currently only exposed/used for registration - we should move these endpoints/actions
        // closer to the other registration code
        // THIS IS NOT PROTECTED BY AUTH
        [AllowAnonymous]
        [HttpPost("~/api/files/direct")]
        public async Task<SimpleFile[]> UploadDirectFiles([FromForm] UploadDirectFilesRequest request)
        {
            var command = new UploadDirectFilesCommand(IdentityContext, request.Token, request.Files.ToArray());

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Fetches a summary of the contacts to be imported.
        /// </summary>
        /// <remarks>
        /// This endpoint provides a summary of the contacts to be imported from a provided file. 
        /// The file is parsed and a summary of the contacts is returned. 
        /// If the provider ID is not provided, the first 5 contacts from the file are returned as a summary. 
        /// If the provider ID is provided, a query is executed to get a summary of the contacts for the specified provider.
        /// </remarks>
        /// <param name="request">The request to import contacts.</param>
        /// <returns>A summary of the contacts to be imported.</returns>
        /// <response code="200">Returns the contact import summary if successful.</response>
        [HttpPost("~/api/files/contactimport/summary")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ContactImportFileResult> ContactImportSummary([FromForm] ImportContactSummaryRequest request)
        {
            if (request.ProviderId == default)
            {
                return SpreadsheetUtilities.ParseToContactImportFileResult(request.File, 5);
            }

            var query = new GetImportContactsSummaryQuery(IdentityContext, request.ProviderId, request.File);
            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }
        

        // this can be deprecated and use the same endpoint in contact files controller
        [HttpPost("~/api/files/contactimport/full")]
        public async Task<Guid> ContactImportFull([FromForm] ImportContactRequest request)
        {            
            DataSchema dataSchema = !string.IsNullOrEmpty(request.DataSchemaJson) ? JsonConvert.DeserializeObject<DataSchema>(request.DataSchemaJson) : null;
            var command = new ImportContactsCommand(IdentityContext,
                request.ProviderId,
                request.File,
                JsonConvert.DeserializeObject<List<ImportContactsOption>>(request.MappedColumnsJson),
                dataSchema);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        /// <summary>
        /// Generate a presigned URL for a file.
        /// </summary>
        /// <remarks>
        /// This endpoint generates a presigned URL for a specific file. 
        /// The file is identified by its unique ID. 
        /// The `fileName` parameter can be used to specify a name for the file. 
        /// If the `download` parameter is set to true, the file will be downloaded when the presigned URL is accessed. 
        /// If the file with the provided ID does not exist, a 404 status code is returned.
        /// </remarks>
        /// <param name="id">The ID of the file.</param>
        /// <param name="fileName">The name of the file.</param>
        /// <param name="download">Whether the file should be downloaded.</param>
        /// <returns>A presigned URL for the file.</returns>
        /// <response code="200">Returns the presigned URL if successful.</response>
        /// <response code="404">If the file is not found.</response>
        [Authorize(Policy = AuthPolicy.FilesView)]
        [HttpGet("~/api/files/{id}/url")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ValidationError))]
        public async Task<string> GeneratePresignedUrl([FromRoute] Guid id, [FromQuery] string fileName = null, [FromQuery] bool download = false)
        {
            var command = new GeneratePresignedUrlCommand(IdentityContext, id, fileName, download, FileLocationType.Files);

            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }
    }
}
