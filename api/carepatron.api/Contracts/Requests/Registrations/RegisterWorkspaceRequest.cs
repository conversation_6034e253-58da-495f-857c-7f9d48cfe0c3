﻿using carepatron.api.Contracts.Requests.Providers;
using carepatron.core.Application.Registrations.Models;
using carepatron.core.Application.Workspace.Models;
using carepatron.core.Models.Common;

namespace carepatron.api.Contracts.Requests.Registrations
{
    public class RegisteredWorkspaceRequest
    {
        public string Name { get; set; }
        public string Profession { get; set; }
        public string TeamSize { get; set; }
        public string[] ExploreFeatures { get; set; }
        public string ToolsUsed { get; set; }
        public string CountryCode { get; set; }
        public string PhoneNumber { get; set; }

        public string TimeZone { get; set; }

        // Referral code is similar to <PERSON>e's promotion code.
        // Can be entered thru subscription settings screen or
        // thru register with a "ref_code"
        public string ReferralCode { get; set; }

        // Customer referral code is a unique code by a provider
        // that they can use to share the app and gain rewards.
        // These are stored in "provider_referral_codes" table
        public string CustomerReferralCode { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string BusinessName { get; set; }
        public string Website { get; set; }
        public Address BillingAddress { get; set; }
        public Address[] Locations { get; set; }
        public DefaultService[] Services { get; set; }
        public ProvisioningStaffSchedule[] StaffSchedules { get; set; }
        public SaveLogoRequest Logo { get; set; }
        public string PrimaryColorHex { get; set; }
    }
}