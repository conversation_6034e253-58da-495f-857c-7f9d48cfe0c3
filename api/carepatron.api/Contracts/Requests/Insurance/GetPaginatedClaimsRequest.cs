using System;
using carepatron.core.Application.Insurance.Models;

namespace carepatron.api.Contracts.Requests.Insurance;

public class GetPaginatedClaimsRequest
{
    public int Limit { get; set; } = QueryConstants.DefaultLimit;
    public int Offset { get; set; } = QueryConstants.DefaultOffset;
    public string SearchTerm { get; set; }
    public ClaimStatus[] Status { get; set; }
    public DateTime? FromDate { get; set; }
    public Guid[] TaskIds { get; set; }
    public DateTime? ToDate { get; set; }
}
