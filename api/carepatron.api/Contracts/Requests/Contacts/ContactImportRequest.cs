using System;
using System.Collections.Generic;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Schema.Models;

namespace carepatron.api.Contracts.Requests.Contacts
{
    public class ContactImportRequest
    {
        public ExternalContactImportSource ImportSource { get; set; }

        public Guid ImportFileId { get; set; }

        public string FileName { get; set; }

        public string FileExtension { get; set; }

        public long FileSize { get; set; }

        public List<ImportContactsOption> MappedColumns { set; get; }
        
        public DataSchema DataSchema { get; set; }
        
        public ImportSummaryStatus? Status { get; set; }
        
        public ImportType? ImportType { get; set; }

        public bool IsContact { get; set; }
    }
}
