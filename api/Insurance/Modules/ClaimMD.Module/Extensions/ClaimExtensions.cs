using System;
using System.Collections.Generic;
using System.Linq;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Constants;
using carepatron.core.Extensions;
using carepatron.core.Models.Media;
using ClaimMD.Module.Models.Http.Dtos;

namespace ClaimMD.Module.Extensions;

public static class ClaimExtensions
{
    public static InsuranceClaimRemittance ToClaimRemittance(
        this ERAClaimDto claim,
        Guid providerId,
        Media media,
        InsuranceRemittanceAdvice remittanceAdvice,
        Guid? claimId = null
    )
    {
        var id = Guid.NewGuid();
        return new InsuranceClaimRemittance
        {
            Id = id,
            ClaimId = claimId,
            ProviderId = providerId,
            RemittanceAdvice = remittanceAdvice,
            TotalAmount = Convert.ToDecimal(claim.TotalCharge),
            MediaId = media.Id,
            TotalPaid = Convert.ToDecimal(claim.TotalPaid),
            RemarkCodes = claim.GetRemarkCodes(),
        };
    }

    public static InsuranceClaimRemittanceItem ToClaimRemittanceItem(
        this ERAClaimChargeDto charge,
        ClaimServiceLine serviceLine,
        Guid claimRemittanceId,
        Guid providerId,
        Guid? claimId
    )
    {
        var id = Guid.NewGuid();
        var serviceLineId = serviceLine?.Id;
        return new InsuranceClaimRemittanceItem
        {
            Id = id,
            ProviderId = providerId,
            InsuranceClaimRemittanceId = claimRemittanceId,
            Date = charge.FromDate.ToDateOnly("yyyyMMdd"),
            Code = charge.ProcedureCode,
            Units = Convert.ToDecimal(charge.Units),
            CurrencyCode = CurrencyCodes.USD,
            Amount = Convert.ToDecimal(charge.Charge),
            Allowed = Convert.ToDecimal(charge.Allowed),
            Paid = Convert.ToDecimal(charge.Paid),
            Description = serviceLine?.Description,
            ServiceLineId = serviceLineId,
            Adjustments = charge.Adjustments?.Select(s => s.ToClaimAdjustment(
                id,
                providerId,
                claimId,
                serviceLineId
            )).ToArray() ?? []
        };
    }

    public static PaymentAllocation ToPaymentAllocation(
        this ERAClaimChargeDto charge,
        ClaimServiceLine serviceLine,
        Guid providerId,
        Guid paymentId,
        Guid contactId
    )
    {
        var id = Guid.NewGuid();
        var serviceLineId = serviceLine?.Id;
        if (serviceLine?.BillableItemId is null) return null;

        return new PaymentAllocation
        {
            Id = id,
            ProviderId = providerId,
            BillableItemId = serviceLine.BillableItemId.Value,
            PaymentId = paymentId,
            ContactId = contactId,
            Amount = Convert.ToDecimal(charge.Paid),
            ClaimLineId = serviceLineId,
            InvoiceLineItemId = null
        };
    }

    public static InsuranceClaimAdjustment ToClaimAdjustment(
        this ChargeAdjustmentDto adjustment,
        Guid remittanceItemId,
        Guid providerId,
        Guid? claimId = null,
        Guid? serviceLineId = null
    )
    {
        return new InsuranceClaimAdjustment
        {
            ClaimId = claimId,
            ProviderId = providerId,
            RemittanceItemId = remittanceItemId,
            GroupCode = adjustment.Group,
            ReasonCode = adjustment.Code,
            CurrencyCode = CurrencyCodes.USD,
            Amount = Convert.ToDecimal(adjustment.Amount),
            ServiceLineId = serviceLineId
        };
    }

    public static string[] GetRemarkCodes(this ERAClaimDto claim)
    {
        if (claim == null) return [];

        var remarkCodes = new List<string>();
        remarkCodes.AddIfNotNullOrEmpty(claim.RemarkCode1);
        remarkCodes.AddIfNotNullOrEmpty(claim.RemarkCode2);
        remarkCodes.AddIfNotNullOrEmpty(claim.RemarkCode3);
        remarkCodes.AddIfNotNullOrEmpty(claim.RemarkCode4);
        remarkCodes.AddIfNotNullOrEmpty(claim.RemarkCode5);
        remarkCodes.AddIfNotNullOrEmpty(claim.RemarkCode6);
        remarkCodes.AddIfNotNullOrEmpty(claim.RemarkCode7);
        remarkCodes.AddIfNotNullOrEmpty(claim.RemarkCode8);

        return [.. remarkCodes];
    }
}
