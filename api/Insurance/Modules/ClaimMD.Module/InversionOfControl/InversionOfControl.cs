using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using carepatron.core.Application.Insurance.Services.ClearingHouse;
using carepatron.core.Application.Insurance.Models;
using ClaimMD.Module.Configuration;
using ClaimMD.Module.Interfaces;
using ClaimMD.Module.Mappers;
using ClaimMD.Module.Services;

namespace ClaimMD.Module;

public static class InversionOfControl
{
    public static IServiceCollection RegisterClaimMDInfra(
        this IServiceCollection services,
        IConfiguration configuration
    )
    {
        services.AddHttpClient<IClaimMdHttpClient, ClaimMdHttpClient>(
            (sp, client) =>
            {
                var config = sp.GetRequiredService<ClaimMdConfiguration>();
                client.BaseAddress = new Uri(config.BaseApiUrl);
                client.DefaultRequestHeaders.Add("Accept", "application/json");
            }
        );

        // Add AutoMapper profiles
        services.AddAutoMapper(typeof(ClearingHouseEligibilityProfile));
        services.AddAutoMapper(typeof(ClearingHousePayersProfile));

        // Add ClaimMD configuration
        var config = configuration.GetSection("ClaimMdConfiguration").Get<ClaimMdConfiguration>();
        services.AddSingleton(config);

        // Add ClaimMD services
        services.AddKeyedScoped<IClearingHousePayersService, ClaimMdPayersService>(
            ClearingHouseType.ManagedClaimMd
        );
        services.AddKeyedScoped<IClearingHouseEligibilityService, ClaimMdEligibilityService>(
            ClearingHouseType.ManagedClaimMd
        );
        services.AddKeyedScoped<IClearingHouseClaimService, ClaimMdClaimService>(
            ClearingHouseType.ManagedClaimMd
        );
        services.AddScoped<IClearingHouseServiceResolver, ClearingHouseServiceResolver>();

        return services;
    }
}
