using System;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Insurance.Services;
using carepatron.core.Application.Payments.Models;
using ClaimMD.Module.Models;
using ClaimMD.Module.Models.Http.Dtos;
using Serilog;
using static ClaimMD.Module.Constants;

namespace ClaimMD.Module.Utilities;

public static class ClaimMdEraStatusHelper
{
    public static ResolvedClaimStatus ResolveClaimStatus(
        ERAClaimDto eraClaim,
        InsuranceClaimUSProfessional internalClaim,
        PaymentAllocation[] claimAllocations,
        IInsuranceService insuranceService
    )
    {
        var claimStatus = internalClaim.Status;
        var eraClaimStatusCode = eraClaim.StatusCode;
        var statusReason = GetStatusReason(eraClaimStatusCode);
        if (string.IsNullOrEmpty(statusReason))
        {
            Log.Warning(
                "No status reason found for ERA claim status code: {StatusCode}",
                eraClaim.StatusCode
            );
        }

        switch (eraClaimStatusCode)
        {
            case ERAClaimStatusCode.ProcessedAsPrimary:
            case ERAClaimStatusCode.ProcessedAsSecondary:
            case ERAClaimStatusCode.ProcessedAsTertiary:
            case ERAClaimStatusCode.ApprovedAsAmended:
            case ERAClaimStatusCode.ApprovedAsSubmitted:
            case ERAClaimStatusCode.ProcessedAsPrimaryForwarded:
            case ERAClaimStatusCode.ProcessedAsSecondaryForwarded:
            case ERAClaimStatusCode.ProcessedAsTertiaryForwarded:
            case ERAClaimStatusCode.Reviewed:
            case ERAClaimStatusCode.Repriced:
            case ERAClaimStatusCode.Audited:
            case ERAClaimStatusCode.ProcessedAsConditional:
                if (Convert.ToDecimal(eraClaim.TotalPaid) != 0)
                {
                    claimStatus = insuranceService.GetClaimStatusByPayments(
                        internalClaim,
                        claimAllocations
                    );
                }
                else
                {
                    // If the total paid is 0, we consider it as accepted
                    claimStatus = ClaimStatus.Accepted;
                }
                break;
            case ERAClaimStatusCode.Pended:
            case ERAClaimStatusCode.PendingUnderInvestigation:
            case ERAClaimStatusCode.ReceivedButNotInProcess:
            case ERAClaimStatusCode.SuspendedInvestigationWithField:
            case ERAClaimStatusCode.PredeterminationPricingOnly:
            case ERAClaimStatusCode.DocumentationClaimNoPayment:
                claimStatus = ClaimStatus.Accepted;
                break;
            case ERAClaimStatusCode.CancelledDueToInactivity:
            case ERAClaimStatusCode.Closed:
                claimStatus = ClaimStatus.Closed;
                break;
            case ERAClaimStatusCode.RejectedDuplicateClaim:
            case ERAClaimStatusCode.RejectedPleaseResubmit:
            case ERAClaimStatusCode.Suspended:
            case ERAClaimStatusCode.SuspendedIncompleteClaim:
            case ERAClaimStatusCode.SuspendedReturnWithMaterial:
            case ERAClaimStatusCode.SuspendedReviewPending:
            case ERAClaimStatusCode.SuspendedProductRegistration:
            case ERAClaimStatusCode.NotOurClaimForwarded:
            case ERAClaimStatusCode.TransferredToProperCarrier:
            case ERAClaimStatusCode.NotOurClaimUnableToForward:
                claimStatus = ClaimStatus.Rejected;
                break;
            case ERAClaimStatusCode.Denied:
                claimStatus = ClaimStatus.Denied;
                break;
            case ERAClaimStatusCode.ReversalOfPreviousPayment:
            case ERAClaimStatusCode.Additional:
            case ERAClaimStatusCode.Appealed:
            case ERAClaimStatusCode.WeeklyCertification:
            case ERAClaimStatusCode.Open:
            case ERAClaimStatusCode.Initial:
            case ERAClaimStatusCode.Reaudited:
            case ERAClaimStatusCode.Reissue:
            case ERAClaimStatusCode.ReopenedAndClosed:
            case ERAClaimStatusCode.Redetermination:
            case ERAClaimStatusCode.Reopened:
                claimStatus = internalClaim.Status;
                break;
            default:
                Log.Warning("Unable to resolve status for claim");
                claimStatus = internalClaim.Status;
                break;
        }

        return new ResolvedClaimStatus(claimStatus, statusReason);
    }

    private static string GetStatusReason(string statusCode)
    {
        if (string.IsNullOrEmpty(statusCode))
        {
            return null;
        }

        return ERAClaimStatusCode.Descriptions.TryGetValue(statusCode, out string value)
          ? value
          : null;
    }
}
