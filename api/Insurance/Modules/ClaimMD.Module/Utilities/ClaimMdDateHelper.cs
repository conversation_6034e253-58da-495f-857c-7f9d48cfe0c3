using System;
using System.Globalization;

namespace ClaimMD.Module.Utilities;

public static class ClaimMdDateHelper
{
    public static readonly string ClaimMdTimeZoneId = "America/Denver";
    public static class Formats
    {
        public const string CompactISODate = "yyyyMMdd";
        public const string ISO8601Date = "yyyy-MM-dd";

        /// <summary>
        /// Claim MD uses a 12 hour format with AM/PM 
        /// Times in this format are in Mountain Time (MST / MDT)
        /// </summary>
        public const string DateTime12hAmPm = "yyyy-MM-dd hh:mm:sstt";
    }


    /// <summary>
    /// Parses a claim MD full date string to a UTC time. 
    /// Expected format: "yyyy-MM-dd hh:mm:sstt" (e.g. "2023-10-01 10:30:00pm")
    /// </summary>
    /// <param name="dateTimeString"></param>
    /// <returns></returns>
    public static DateTime MountainDateTimeToUtc(string dateTimeString)
    {
        DateTime dateTime = DateTime.ParseExact(
            dateTimeString,
            Formats.DateTime12hAmPm,
            CultureInfo.InvariantCulture,
            DateTimeStyles.None
        );
        return MountainDateTimeToUtc(dateTime);
    }

    /// <summary>
    /// Converts a DateOnly in Mountain Time to a UTC DateTime.
    /// </summary>
    public static DateTime MountainDateOnlyToUtc(DateOnly date)
    {
        var localTime = new DateTime(date, new TimeOnly(0, 0), DateTimeKind.Unspecified);
        return MountainDateTimeToUtc(localTime);
    }


    /// <summary>
    /// Converts a DateTime in Mountain Time to a UTC DateTime.
    /// </summary>
    public static DateTime MountainDateTimeToUtc(DateTime mountainDateTime)
    {
        var mountainTimeZone = TimeZoneInfo.FindSystemTimeZoneById(ClaimMdTimeZoneId);
        return TimeZoneInfo.ConvertTimeToUtc(mountainDateTime, mountainTimeZone);
    }
}
