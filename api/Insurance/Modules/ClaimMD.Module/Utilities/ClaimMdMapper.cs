using System;
using System.Collections.Generic;
using System.Linq;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Constants;
using carepatron.core.Extensions;
using carepatron.core.Models.Common;
using carepatron.core.Models.Execution;
using carepatron.core.Utilities;
using ClaimMD.Module.Models.Http.Dtos;
using Serilog;

namespace ClaimMD.Module.Utilities;

public static class ClaimMdMapper
{
    public static List<UploadedClaimDto> MapClaimDetailRequest(InsuranceClaimUSProfessional[] claims)
    {
        var result = new List<UploadedClaimDto>();

        foreach (var claim in claims)
        {
            var claimDetail = new UploadedClaimDto
            {
                AcceptAssign = "Y",
                BalanceDue = claim.BalanceDue.ToString("0.00"),
                ClaimForm = "1500",
                PCN = claim.ClientControlNumber,
                RemoteClaimId = Base36GuidEncoder.Encode(claim.Id)
            };

            if (!string.IsNullOrEmpty(claim.OriginalReferenceNumber))
            {
                claimDetail.ResubmissionCode = claim.ResubmissionCode;
                claimDetail.OriginalReferenceNumber = claim.OriginalReferenceNumber;
            }

            if (claim.Client is not null)
            {
                claimDetail.PatientFirstName = claim.Client.FirstName;
                claimDetail.PatientLastName = claim.Client.LastName;
                claimDetail.PatientSex = ToSexString(claim.Client.Sex);
                claimDetail.PatientDateOfBirth = claim.Client.DateOfBirth.HasValue
                    ? claim.Client.DateOfBirth.Value.ToString("yyyy-MM-dd")
                    : string.Empty;

                if (claim.Client.Address is not null)
                {
                    claimDetail.PatientAddress1 = claim.Client.Address?.StreetAddress;
                    claimDetail.PatientCity = claim.Client.Address?.City;
                    claimDetail.PatientState = AddressUtilities.GetUSState(
                        claim.Client.Address?.State
                    );
                    claimDetail.PatientZip = claim.Client.Address?.ZipCode;
                }
            }

            if (claim.Incident is not null)
            {
                claimDetail.AutoAccident = ToYesNo(claim.Incident.IsConditionRelatedToAutoAccident);
                claimDetail.EmploymentRelated = ToYesNo(
                    claim.Incident.IsConditionRelatedToEmployment
                );
            }

            if (claim.BillingDetail is not null)
            {
                claimDetail.BillingState = AddressUtilities.GetUSState(
                    claim.BillingDetail.Address.State
                );
                claimDetail.BillingNpi = claim.BillingDetail.NationalProviderId;
                claimDetail.BillingPhone = claim.BillingDetail.PhoneNumber?.Replace(
                    claim.BillingDetail.PhoneCountryCode,
                    string.Empty
                );
                claimDetail.BillTaxId = claim.BillingDetail.TaxNumber;
                claimDetail.BillingTaxIdType = GetTaxType(claim.BillingDetail.TaxNumberType);
                claimDetail.ProviderTaxonomy = claim.BillingDetail.TaxonomyCode;

                if (claim.BillingDetail.Address is not null)
                {
                    claimDetail.BillingName = claim.BillingDetail.Name;
                    claimDetail.BillingAddress1 = claim.BillingDetail.Address.StreetAddress;
                    claimDetail.BillingCity = claim.BillingDetail.Address.City;
                    claimDetail.BillingZip = claim.BillingDetail.Address.ZipCode;
                }
            }

            if (claim.ContactInsurancePolicy is not null)
            {
                claimDetail.PayerId = claim.ContactInsurancePolicy.PayerNumber;
                claimDetail.PolicyHolderGroup = claim.ContactInsurancePolicy.PolicyHolderGroupId;
                claimDetail.PolicyHolderFirstName = claim
                    .ContactInsurancePolicy
                    .PolicyHolderFirstName;
                claimDetail.PolicyHolderLastName = claim
                    .ContactInsurancePolicy
                    .PolicyHolderLastName;
                claimDetail.PolicyHolderSex = ToSexString(
                    claim.ContactInsurancePolicy.PolicyHolderSex
                );
                claimDetail.InsuranceNumber = claim.ContactInsurancePolicy.PolicyHolderMemberId;
                claimDetail.PatientRelationship = GetClientRelationship(
                    claim.ContactInsurancePolicy.PolicyHolderRelationshipType
                );
                claimDetail.PolicyHolderDateOfBirth = claim
                    .ContactInsurancePolicy
                    .PolicyHolderDateOfBirth
                    .HasValue
                    ? claim.ContactInsurancePolicy.PolicyHolderDateOfBirth.Value.ToString(
                          "yyyy-MM-dd"
                      )
                    : string.Empty;

                if (claim.ContactInsurancePolicy.PolicyHolderAddress is not null)
                {
                    claimDetail.PolicyHolderAddress1 = claim
                        .ContactInsurancePolicy
                        .PolicyHolderAddress
                        .StreetAddress;
                    claimDetail.PolicyHolderCity = claim
                        .ContactInsurancePolicy
                        .PolicyHolderAddress
                        .City;
                    claimDetail.PolicyHolderState = AddressUtilities.GetUSState(
                        claim.ContactInsurancePolicy.PolicyHolderAddress.State
                    );
                    claimDetail.PolicyHolderZip = claim
                        .ContactInsurancePolicy
                        .PolicyHolderAddress
                        .ZipCode;
                }
            }

            var diagnosticCodesMap = new Dictionary<string, string>();
            var letterMap = new List<string>
            {
                "A",
                "B",
                "C",
                "D",
                "E",
                "F",
                "G",
                "H",
                "I",
                "J",
                "K",
                "L"
            };
            if (claim.DiagnosticCodes is not null && claim.DiagnosticCodes.Length > 0)
            {
                for (var i = 0; i < claim.DiagnosticCodes.Length; i++)
                {
                    diagnosticCodesMap.Add(claim.DiagnosticCodes[i].Code, letterMap[i]);
                }

                var diagnosisItems = diagnosticCodesMap.ToList();
                if (diagnosisItems.Count > 0)
                    claimDetail.Diagnosis1 = diagnosisItems[0].Key;
                if (diagnosisItems.Count > 1)
                    claimDetail.Diagnosis2 = diagnosisItems[1].Key;
                if (diagnosisItems.Count > 2)
                    claimDetail.Diagnosis3 = diagnosisItems[2].Key;
                if (diagnosisItems.Count > 3)
                    claimDetail.Diagnosis4 = diagnosisItems[3].Key;
                if (diagnosisItems.Count > 4)
                    claimDetail.Diagnosis5 = diagnosisItems[4].Key;
                if (diagnosisItems.Count > 5)
                    claimDetail.Diagnosis6 = diagnosisItems[5].Key;
                if (diagnosisItems.Count > 6)
                    claimDetail.Diagnosis7 = diagnosisItems[6].Key;
                if (diagnosisItems.Count > 7)
                    claimDetail.Diagnosis8 = diagnosisItems[7].Key;
                if (diagnosisItems.Count > 8)
                    claimDetail.Diagnosis9 = diagnosisItems[8].Key;
                if (diagnosisItems.Count > 9)
                    claimDetail.Diagnosis10 = diagnosisItems[9].Key;
                if (diagnosisItems.Count > 10)
                    claimDetail.Diagnosis11 = diagnosisItems[10].Key;
                if (diagnosisItems.Count > 11)
                    claimDetail.Diagnosis12 = diagnosisItems[11].Key;
            }

            var charges = new List<ClaimDetailChargeDto>();
            if (claim.ServiceLines is not null && claim.ServiceLines.Length > 0)
            {
                foreach (var serviceLine in claim.ServiceLines)
                {
                    var diagnosisItems = diagnosticCodesMap
                        .ToList()
                        .Where(x => serviceLine.DiagnosticCodeReferences.Contains(x.Key))
                        .ToList();
                    charges.Add(
                        new ClaimDetailChargeDto
                        {
                            Charge = serviceLine.Amount.ToString("0.00"),
                            FromDate = serviceLine.Date.ToString("yyyy-MM-dd"),
                            ThruDate = serviceLine.Date.ToString("yyyy-MM-dd"),
                            ChargeRecordType = "UN",
                            PlaceOfService = serviceLine.POSCode,
                            ProcedureCode = serviceLine.Code,
                            Units = serviceLine.Units.ToString(),
                            DiagnosisReference = string.Join(
                                "",
                                diagnosisItems.Select(x => x.Value)
                            ),
                            RemoteChargeId = Base36GuidEncoder.Encode(serviceLine.Id)
                        }
                    );
                }
            }
            claimDetail.Charges = charges;
            claimDetail.TotalCharge = claim.Amount.ToString("0.00");

            if (claim.ReferringProviders is not null && claim.ReferringProviders.Length > 0)
            {
                claimDetail.ReferrerFirstName = claim.ReferringProviders[0].FirstName;
                claimDetail.ReferrerLastName = claim.ReferringProviders[0].LastName;
                claimDetail.ReferrerMiddleName = claim.ReferringProviders[0].MiddleName;
                claimDetail.ReferrerNpi = claim.ReferringProviders[0].NationalProviderId;
            }

            if (claim.RenderingProviders is not null && claim.RenderingProviders.Length > 0)
            {
                claimDetail.ProviderFirstName = claim.RenderingProviders[0].FirstName;
                claimDetail.ProviderLastName = claim.RenderingProviders[0].LastName;
                claimDetail.ProviderMiddleName = claim.RenderingProviders[0].MiddleName;
                claimDetail.ProviderNpi = claim.RenderingProviders[0].NationalProviderId;
            }

            result.Add(claimDetail);
        }

        return result;
    }

    public static string ToSexString(Sex? sex)
    {
        return sex switch
        {
            Sex.Male => "M",
            Sex.Female => "F",
            _ => throw new ExecutionException(new ValidationError(
                Errors.ClearingHouseGeneralErrorCode,
                Errors.InvalidSexDetails,
                ValidationType.BadRequest
            ))
        };
    }

    public static string GetClientRelationship(InsurancePolicyHolderType type)
    {
        return type switch
        {
            InsurancePolicyHolderType.Client => "18",
            InsurancePolicyHolderType.Spouse => "01",
            InsurancePolicyHolderType.Parent => "19",
            InsurancePolicyHolderType.Other => "20",
            _ => throw new ExecutionException(new ValidationError(
                Errors.ClearingHouseGeneralErrorCode,
                Errors.InvalidPolicyHolderRelationshipDetails,
                ValidationType.BadRequest
            ))
        };
    }

    public static string ToYesNo(bool? value)
    {
        if (value is null)
            return "N";
        return value.Value ? "Y" : "N";
    }

    public static string GetTaxType(string type)
    {
        return type.ToLower() switch
        {
            "ssn" => "S",
            "ein" => "E",
            _ => throw new ExecutionException(new ValidationError(
                Errors.ClearingHouseGeneralErrorCode,
                Errors.InvalidTaxTypeDetail,
                ValidationType.BadRequest
            ))
        };
    }

    public static InsuranceClaimClearingHouseMetadata MapMetadata<T>(T src) where T : BaseClaim
    {
        if (src is null) return null;

        if (!Base36GuidEncoder.TryParse(src.RemoteClaimId, out var insuranceClaimId))
        {
            Log
                .ForContext("RemoteClaimId", src.RemoteClaimId)
                .Warning("RemoteClaimId not found in event data");
            throw new InvalidOperationException("RemoteClaimId not found in event data");
        }

        return new InsuranceClaimClearingHouseMetadata
        {
            Id = Guid.NewGuid(),
            PayerClaimId = src.ClaimMdId != src.SenderICN ? src.SenderICN : null,
            InsuranceClaimId = insuranceClaimId,
            PayerId = src.PayerId,
            ClearingHouse = ClearingHouseType.ManagedClaimMd,
            ClearingHouseClaimId = src.ClaimMdId,
            CreatedDateTimeUtc = DateTime.UtcNow
        };
    }

    public static InsuranceClaimError[] MapClaimErrors<T>(T src, Guid providerId) where T : BaseClaim
    {
        if (src?.Messages is null) return [];

        var errors = src?.Messages?.Where(s => s.Status == Constants.ClaimStatusRejected) ?? [];
        if (!errors.Any()) return [];

        var insuranceClaimId = !string.IsNullOrEmpty(src.RemoteClaimId) ? Base36GuidEncoder.Decode(src.RemoteClaimId) : Guid.Empty;

        //todo - temp to get a feel for errors encountered.
        Log.ForContext("Claim", new
        {
            src.RemoteClaimId,
            src.ClaimId
        }, true)
            .ForContext("ClaimErrors", errors, true)
            .Information("Claim contains error messages");

        return errors.Select(s =>
        {
            var field = ReverseMapFieldName(s.Fields);
            return new InsuranceClaimError
            {
                Id = Guid.NewGuid(),
                ProviderId = providerId,
                Field = field,
                Message = s.Content,
                Status = ClaimErrorStatus.Active,
                InsuranceClaimId = insuranceClaimId
            };
        }).ToArray();
    }

    private static string ReverseMapFieldName(string fields)
    {
        return string.Empty;
        // todo.
        // No point setting anything here until we've got a full reverse map
        // The field will need to match to a UI field so populating it with 
        // the ClaimMD field values will just muddy the water.
        var fieldName = string.Empty;
        var field = fields.Split(',');
        if (field.Length == 1)
        {
            fieldName = field[0];
        }
    }

    /// <summary>
    /// Maps the given decimal to a Claim.MD friendly decimal string.
    /// </summary>
    public static string MapToDecimalString(decimal? d) => (d ?? 0M).ToString("F2");

}
