using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Abstractions;
using carepatron.core.Application.ClearingHouse.Models;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Constants;
using carepatron.core.Repositories.ClearingHouse;
using carepatron.core.Repositories.Insurance;
using carepatron.core.Utilities;
using ClaimMD.Module.Exceptions;
using ClaimMD.Module.Interfaces;
using ClaimMD.Module.Models.Http.Dtos;
using ClaimMD.Module.Models.Http.Requests;
using ClaimMD.Module.Models.Http.Responses;
using ClaimMD.Module.Queues;
using Messaging;
using Messaging.Handling;
using Messaging.Models;
using Messaging.Services;
using Serilog;
using Serilog.Context;

using static carepatron.core.Constants.LogProperties;

namespace ClaimMD.Module.Messages.Handlers;

[MessageHandler(nameof(ClaimMdProcessERAMessage))]
public class ClaimMdProcessERAMessageHandler(
    IUnitOfWork unitOfWork,
    IClaimMdHttpClient claimMdHttpClient,
    IMessageSender<ClaimMdMessageQueue> messageSender,
    IContactRepository contactRepository,
    IInsuranceClaimClientRepository insuranceClaimClientRepository,
    IInsuranceRemittanceAdvicesRepository insuranceRemittanceAdvicesRepository,
    IInsurancePayerRepository providerInsurancePayerRepository,
    IClearingHouseLogRepository clearingHouseLogRepository,
    IClearingHouseIdLookupRepository clearingHouseIdLookupRepository
) : MessageHandlerBase<ClaimMdProcessERAMessage>
{
    private const ClearingHouseType ClearingHouse = ClearingHouseType.ManagedClaimMd;
    private const ClearingHouseLogType ClearingHouseLog = ClearingHouseLogType.Era;
    private const string CurrencyCode = CurrencyCodes.USD;

    protected override async Task HandleMessage(ClaimMdProcessERAMessage message, CancellationToken cancellationToken)
    {
        using (LogContext.PushProperty(nameof(CodeOwner), CodeOwner.BillingAndPayments))
        {
            unitOfWork.UseUnitOfWork();

            await ProcessMessage(message, cancellationToken);
            await unitOfWork.SaveUnitOfWork(cancellationToken);
        }
    }

    private async Task ProcessMessage(ClaimMdProcessERAMessage message, CancellationToken cancellationToken)
    {
        var eraId = message?.ERA.EraId;

        using (LogContext.PushProperty(Constants.LogProperties.EraId, eraId))
        {
            var eraDetails = await RetrieveEraDetail(eraId);

            if (eraDetails is null)
            {
                throw new ClaimMdEraException(message: "Failed to retrieve ERA details", eraId: eraId);
            }

            await ProcessEraDetails(eraDetails);

            await clearingHouseLogRepository.UpdateStatus(
                eraId,
                ClearingHouse,
                ClearingHouseLog,
                ClearingHouseLogStatus.Processed,
                cancellationToken);
        }
    }

    private async Task ProcessEraDetails(ERADetailsResponse eraDetails)
    {
        var contactClaimGroups = await GroupContacts(eraDetails);
        var messages = new List<ClaimMDProcessContactERAMessage>();
        var remittanceAdviceList = new List<InsuranceRemittanceAdvice>();

        foreach (var (contactId, claims) in contactClaimGroups)
        {
            using (LogContext.PushProperty(ContactId, contactId))
            {
                var providerId = await RetrieveProviderId(contactId, eraDetails.EraId);

                using (LogContext.PushProperty(ProviderId, providerId))
                {
                    var remittanceAdvice = remittanceAdviceList.SingleOrDefault(x =>
                        x.ProviderId == providerId &&
                        x.NPINumber == eraDetails.ProviderNpi &&
                        x.TaxId == eraDetails.ProviderTaxId);

                    if (remittanceAdvice is null)
                    {
                        remittanceAdvice = await RetrieveOrCreateRemittanceAdvice(providerId, eraDetails);

                        // Add to local list so we don't need to deal with dirty reads on uncommited UoW if remittance advice was created
                        remittanceAdviceList.Add(remittanceAdvice);
                    }

                    messages.Add(CreateClaimMDProcessContactERAMessage(
                        contactId: contactId,
                        providerId: providerId,
                        insuranceRemittanceAdviceId: remittanceAdvice.Id,
                        eraDetails,
                        claims));
                }
            }
        }

        await DispatchContactERAMessages(messages);
    }

    private async Task<Dictionary<Guid, ERAClaimDto[]>> GroupContacts(ERADetailsResponse eraDetails)
    {
        var contactClaims = new Dictionary<Guid, ERAClaimDto[]>();

        var pcnGroups = eraDetails.Claims?
             .GroupBy(claim => claim.PCN)
             .ToDictionary(group => group.Key, group => group.ToArray()) ?? new Dictionary<string, ERAClaimDto[]>();

        var contactIds = await clearingHouseIdLookupRepository.Lookup(
            pcnGroups.Keys.ToArray(),
            CancellationToken.None);

        foreach (var pcnGroup in pcnGroups)
        {
            if (contactIds.TryGetValue(pcnGroup.Key, out var contactId))
            {
                contactClaims[contactId] = pcnGroup.Value;
            }
            else if (long.TryParse(pcnGroup.Key, out var pcn))
            {
                // if its a long and we cant match. We have a problem.
                Log.ForContext("PCN", pcnGroup.Key).Warning("Claim PCN does not map to a valid contact Id");
                throw new ClaimMdEraException("Claim PCN does not map to a valid contact Id", eraId: eraDetails.EraId);
            }
            else
            {
                Log.ForContext("PCN", pcnGroup.Key).Information("Claim PCN is not numeric. Skipping.");
                //if this isn't a long, its prob come from a different system and not for us to handle;
                continue;
            }
        }

        return contactClaims;
    }

    private async Task<Guid> RetrieveProviderId(Guid contactId, int eraId)
    {
        var contact = await contactRepository.Get(contactId, includeSoftDeleted: true);

        if (contact is not null)
        {
            return contact.ProviderId;
        }

        var providerId = await insuranceClaimClientRepository.GetProviderIdByContact(contactId);

        if (providerId is not null)
        {
            return providerId.Value;
        }

        throw new ClaimMdEraException("Unable to retrieve provider ID for contact", eraId: eraId);
    }

    private async Task<InsuranceRemittanceAdvice> RetrieveOrCreateRemittanceAdvice(Guid providerId, ERADetailsResponse eraDetails)
    {
        var remittanceAdvice = await insuranceRemittanceAdvicesRepository.Get(
            providerId,
            npiNumber: eraDetails.ProviderNpi,
            taxId: eraDetails.ProviderTaxId);

        return remittanceAdvice ?? await CreateRemittanceAdvice(eraDetails, providerId);
    }

    private async Task<InsuranceRemittanceAdvice> CreateRemittanceAdvice(ERADetailsResponse era, Guid providerId)
    {
        using (LogContext.PushProperty("EraPaidDate", era.PaidDate))
        using (LogContext.PushProperty("EraPaidAmount", era.PaidAmount))
        {
            if (!DateOnly.TryParse(era.PaidDate, out var paymentDate))
            {
                throw new ClaimMdEraException("Failed to create remittance advice. Unable to parse ERA payment date", era.EraId);
            }

            if (!decimal.TryParse(era.PaidAmount, out var paidAmount))
            {
                throw new ClaimMdEraException("Failed to create remittance advice. Unable to parse ERA paid amount", era.EraId);
            }

            var payerId = await RetrievePayer(providerId, era.PayerId);

            var remittanceAdvice = new InsuranceRemittanceAdvice
            {
                Id = Guid.NewGuid(),
                BillingProfileId = null, // No longer required
                ClearingHouse = ClearingHouse,
                CreatedDateTimeUtc = DateTime.UtcNow,
                CurrencyCode = CurrencyCode,
                ExternalId = era.EraId.ToString(),
                NPINumber = era.ProviderNpi,
                PaidAmount = paidAmount,
                PayerId = payerId,
                PayerName = era.PayerName,
                PayerNumber = era.PayerId,
                PaymentDate = paymentDate,
                PaymentMethod = era.PaymentMethod,
                PaymentReference = era.CheckNumber,
                ProviderId = providerId,
                ReceivedDateTimeUtc = DateTime.UtcNow,
                TaxId = era.ProviderTaxId
            };

            await insuranceRemittanceAdvicesRepository.Create(remittanceAdvice);

            return remittanceAdvice;
        }
    }

    private async Task<Guid?> RetrievePayer(Guid providerId, string payerNumber)
    {
        var payer = await providerInsurancePayerRepository.GetByPayerNumber(
            providerId,
            payerNumber,
            ClearingHouse);

        if (payer is not null)
        {
            return payer.Id;
        }

        Log.Warning("No provider payer found for ERA payer number {payerNumber}", payerNumber);

        return null;
    }

    private ClaimMDProcessContactERAMessage CreateClaimMDProcessContactERAMessage(
        Guid contactId,
        Guid providerId,
        Guid insuranceRemittanceAdviceId,
        ERADetailsResponse eraDetails,
        ERAClaimDto[] claims)
    {
        var message = new ClaimMDProcessContactERAMessage
        {
            ContactId = contactId,
            ProviderId = providerId,
            InsuranceRemittanceAdviceId = insuranceRemittanceAdviceId,
            ERADetails = ERADetail.Create(eraDetails, claims)
        };

        return message;
    }

    private async Task DispatchContactERAMessages(IList<ClaimMDProcessContactERAMessage> messages)
    {
        var requests = messages.Select(era => new SendRequest(era)).ToArray();

        var results = await messageSender.SendBatch(requests);

        if (results.Any(s => !s.IsSuccess))
        {
            throw new ApplicationException("Failed to send all Contact ERA messages");
        }
    }

    private async Task<ERADetailsResponse> RetrieveEraDetail(string eraId)
    {
        var request = new ERADetailsRequest
        {
            EraId = eraId
        };

        var response = await claimMdHttpClient.GetERADetails(request);

        return response;
    }
}