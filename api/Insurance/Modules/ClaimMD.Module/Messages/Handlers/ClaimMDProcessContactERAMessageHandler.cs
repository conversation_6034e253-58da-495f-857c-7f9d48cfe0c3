using carepatron.core.Abstractions;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Common;
using carepatron.core.Constants;
using carepatron.core.Application.Insurance.Extensions;
using carepatron.core.Extensions;
using carepatron.core.Repositories.Insurance;
using ClaimMD.Module.Extensions;
using Messaging;
using Messaging.Handling;
using Serilog;
using Serilog.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Repositories.Billables;
using Payment = carepatron.core.Application.Payments.Models.Payment;
using carepatron.core.Application.History.Models;
using carepatron.core.Repositories.History;
using carepatron.core.Repositories.Payments;
using ClaimMD.Module.Interfaces;
using ClaimMD.Module.Models.Http.Requests;
using carepatron.core.Repositories.Files;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Insurance.Services;
using carepatron.core.Models.Media;
using ClaimMD.Module.Models.Http.Responses;
using ClaimMD.Module.Models.Http.Dtos;
using ClaimMD.Module.Utilities;
using carepatron.core.Utilities;
using carepatron.core.Repositories.ClearingHouse;

namespace ClaimMD.Module.Messages.Handlers;

[MessageHandler(nameof(ClaimMDProcessContactERAMessage))]
public class ClaimMDProcessContactERAMessageHandler(
    IClaimMdHttpClient claimMdHttpClient,
    IContactRepository contactRepository,
    IUnitOfWork unitOfWork,
    IInsuranceRemittancePaymentsRepository insuranceRemittancePaymentsRepository,
    IInsuranceRemittanceAdvicesRepository insuranceRemittanceAdvicesRepository,
    IInsuranceClaimsRepository insuranceClaimsRepository,
    IClearingHouseIdLookupRepository clearingHouseIdLookupRepository,
    IInsuranceClaimUSProfessionalsRepository insuranceClaimUSProfessionalsRepository,
    IInsuranceService insuranceService,
    IPaymentRepository paymentRepository,
    IEntityHistoryRepository entityHistoryRepository,
    IMediaRepository mediaRepository,
    IFileStorageRepository fileStorageRepository,
    IBillableRepository billableRepository,
    IInsuranceClaimRemittancesRepository insuranceClaimRemittancesRepository,
    IInsuranceClaimErrorsRepository insuranceClaimErrorsRepository,
    IInsurancePayerRepository insurancePayerRepository
) : MessageHandlerBase<ClaimMDProcessContactERAMessage>
{
    private const ClearingHouseType ClearingHouse = ClearingHouseType.ManagedClaimMd;

    protected override async Task HandleMessage(ClaimMDProcessContactERAMessage message, CancellationToken cancellationToken)
    {
        LogContext.PushProperty(nameof(CodeOwner), CodeOwner.BillingAndPayments);
        unitOfWork.UseUnitOfWork();
        await ProcessMessage(message, cancellationToken);
    }

    private async Task ProcessMessage(ClaimMDProcessContactERAMessage message, CancellationToken cancellationToken)
    {
        // TODO
        // - Handling unmatched claims
        // - Mapping claim status denials
        // - Mapping/updating status reasons
        // - Correct payment calculations
        // - Sending Notifications

        ArgumentNullException.ThrowIfNull(message, nameof(message));

        var eraDetails = message.ERADetails;
        if (eraDetails is null)
        {
            Log.Error("Cannot process ERA. No ERADetails provided");
            return;
        }

        using (LogContext.PushProperty("ClaimMDProcessContactERAMessage", new
        {
            eraDetails.EraId,
            eraDetails.CheckNumber,
            message.ContactId,
            message.ProviderId,
            message.InsuranceRemittanceAdviceId
        }))
        {
            var contactId = message.ContactId;
            if (contactId == Guid.Empty)
            {
                Log.Error("Cannot process ERA: Empty ContactId");
                return;
            }

            var providerId = message.ProviderId;
            if (providerId == Guid.Empty)
            {
                Log.Error("Cannot process ERA: Empty ProviderId");
                return;
            }

            var claims = eraDetails.Claims;
            if (claims is null || claims.Length == 0)
            {
                Log.Error("Cannot process ERA: Contains no claims");
                return;
            }

            var insurancePayment = await insuranceRemittancePaymentsRepository.GetByReference(message.ProviderId, eraDetails.CheckNumber, cancellationToken);
            if (insurancePayment is not null)
            {
                Log.Information("Remittance payment already exists");
                return;
            }

            var pcn = await clearingHouseIdLookupRepository.Get(contactId, cancellationToken);
            if (pcn is null)
            {
                throw new InvalidOperationException("Cannot process ERA: No PCN found for contact");
            }

            Log.Information("Processing ERA");

            var remittanceAdvice = await insuranceRemittanceAdvicesRepository.GetById(message.InsuranceRemittanceAdviceId, providerId, cancellationToken);
            if (remittanceAdvice is null)
            {
                throw new InvalidOperationException("Remittance advice not found");
            }

            var contact = await contactRepository.Get(contactId, true);
            if (contact is null)
            {
                // the contact cannot be found, 
                // but we can progress with the specified contact id, assuming it was deleted.
                // TBC:  a merged contact could cause a bit of havok here...
                Log.Warning("Contact {ContactId} not found", contactId);
            }

            var media = await GetPdfAndUploadS3(eraDetails, pcn.Value, contactId, providerId, remittanceAdvice.Id);
            var payer = await RetrievePayer(providerId, eraDetails, cancellationToken);
            var payment = CreatePayment(eraDetails, contactId, providerId, payer, cancellationToken);

            // Create insurance remittance payment (links payment and remittance advice)
            var newInsurancePayment = new InsuranceRemittancePayment
            {
                Id = Guid.NewGuid(),
                ProviderId = providerId,
                Payment = payment,
                InsuranceRemittanceAdvice = remittanceAdvice
            };

            // Get all claim charge ids. these map to claim service line ids.
            var serviceItemIds = claims
                .SelectMany(c => c.Charges.Select(s => Base36GuidEncoder.Decode(s.ChgId)))
                .Distinct()
                .ToArray();

            var insuranceClaims = await insuranceClaimUSProfessionalsRepository.GetByContactLineItemIds(
                contactId,
                providerId,
                serviceItemIds,
                cancellationToken
            );

            var claimRemittances = new List<InsuranceClaimRemittance>();
            var allocations = new List<PaymentAllocation>();
            var entityHistories = new List<EntityHistory>();
            var claimStatusUpdates = new List<InsuranceClaimStatusUpdateRequest>();
            var pendingNotifications = new List<Func<Task>>();

            // Process each claim in the ERA
            // Create claim remittance record, history record and update claim status
            foreach (var claim in claims)
            {
                using (LogContext.PushProperty("ERAClaim", new
                {
                    claim.PCN,
                    claim.PayerICN,
                    claim.TotalCharge,
                    claim.TotalPaid,
                }))
                {
                    Log.Information("Processing claim");

                    var matchingClaim = FindMatchingInsuranceClaim(insuranceClaims, claim, contactId);

                    var claimRemittanceItems = new List<InsuranceClaimRemittanceItem>();
                    var claimPaymentAllocationHistoryReferences = new List<PaymentAllocationHistoryReference>();
                    var claimAllocations = new List<PaymentAllocation>();

                    var claimRemittance = claim.ToClaimRemittance(providerId, media, remittanceAdvice, matchingClaim?.Id);
                    claimRemittances.Add(claimRemittance);
                    if (claimRemittance.TotalPaid == 0)
                    {
                        Log.ForContext("claimRemittance", new
                        {
                            claimRemittance.Id,
                            claimRemittance.TotalPaid,
                            claimRemittance.TotalAmount,
                        }, true)
                        .Warning("Claim remittance has no payments");
                    }

                    foreach (var charge in claim.Charges)
                    {
                        var serviceLine = matchingClaim?.ServiceLines.SingleOrDefault(s => s.Id == Base36GuidEncoder.Decode(charge.ChgId));
                        if (serviceLine is null)
                        {
                            Log.ForContext("charge", new
                            {
                                ChgId = Base36GuidEncoder.Decode(charge.ChgId),
                                charge.ProcedureCode,
                            }, true)
                            .Warning("No claim service line matched");
                        }
                        var item = charge.ToClaimRemittanceItem(serviceLine, claimRemittance.Id, providerId, matchingClaim?.Id);
                        claimRemittanceItems.Add(item);

                        if (serviceLine is not null)
                        {
                            // if we matched the service line, add a payment allocation as well.
                            var claimAllocation = charge.ToPaymentAllocation(serviceLine, providerId, payment.Id, contactId);

                            if (claimAllocation is not null)
                            {
                                claimAllocations.Add(claimAllocation);
                                allocations.Add(claimAllocation);
                                claimPaymentAllocationHistoryReferences.Add(new PaymentAllocationHistoryReference(
                                    claimAllocation.Amount,
                                    serviceLine.Code,
                                    serviceLine.Description
                                ));
                            }
                        }
                    }

                    claimRemittance.Items = claimRemittanceItems.ToArray();

                    if (matchingClaim is not null)
                    {
                        var claimStatus = ClaimMdEraStatusHelper.ResolveClaimStatus(
                            claim,
                            matchingClaim,
                            claimAllocations.ToArray(),
                            insuranceService
                        );
                        
                        if (claimStatus.Status != matchingClaim.Status)
                        {
                            matchingClaim.Status = claimStatus.Status;
                            claimStatusUpdates.Add(new InsuranceClaimStatusUpdateRequest(
                                matchingClaim.Id,
                                claimStatus.Status,
                                claimStatus.StatusReason
                            ));

                            if (claimStatus.Status is ClaimStatus.Rejected or ClaimStatus.Denied)
                            {
                                pendingNotifications.Add(() => insuranceService.SendClaimResponseNotification(
                                    providerId,
                                    matchingClaim,
                                    payer?.Name ?? eraDetails.PayerName,
                                    payer?.PayerId ?? eraDetails.PayerId,
                                    payer?.Id));
                            }
                        }

                        if (claimRemittance.TotalPaid != 0)
                        {
                            var history = matchingClaim.ToClaimElectronicPaymentHistory([], payment, claimPaymentAllocationHistoryReferences.ToArray(), claimRemittance);
                            entityHistories.Add(history);

                            pendingNotifications.Add(() => insuranceService.SendClaimPaymentNotification(
                                providerId,
                                matchingClaim,
                                payment,
                                payerName: payer?.Name ?? eraDetails.PayerName,
                                payerNumber: payer?.PayerId ?? eraDetails.PayerId,
                                payer?.Id));
                        }
                        else
                        {
                            var history = matchingClaim.ToClaimERAReceivedHistory([], claimRemittance, remittanceAdvice);
                            entityHistories.Add(history);
                        }
                    }
                }
            }

            // Bulk create claim remittances
            await insuranceClaimRemittancesRepository.BulkCreate([.. claimRemittances], cancellationToken);

            // create payment 
            var claimIds = insuranceClaims.Select(s => s.Id).ToArray();
            await paymentRepository.CreateInsurancePayment(payment, claimIds);

            // Create insurance remittance payment
            await insuranceRemittancePaymentsRepository.Create(newInsurancePayment, cancellationToken);

            // Create allocations
            if (allocations.Count > 0)
            {
                await paymentRepository.CreatePaymentAllocations(allocations);
                //todo - double check if this needs UoW to be saved first.
                await billableRepository.RecalculateBillableByItemIds(allocations.Select(a => a.BillableItemId).ToArray());
            }

            // Bulk update claim statuses
            if (claimStatusUpdates.Count > 0)
            {
                await insuranceClaimsRepository.UpdateStatus([.. claimStatusUpdates], cancellationToken);
            }

            // Bulk create claim histories
            if (entityHistories.Count > 0)
            {
                await entityHistoryRepository.BulkCreate([.. entityHistories], cancellationToken);
            }

            await unitOfWork.SaveUnitOfWork(cancellationToken);

            // Send notifications
            foreach (var pendingNotification in pendingNotifications)
            {
                await pendingNotification();
            }
        }
    }

    private static InsuranceClaimUSProfessional FindMatchingInsuranceClaim(InsuranceClaimUSProfessional[] insuranceClaims, ERAClaimDto claim, Guid contactId)
    {
        var chargeIds = claim.Charges.Select(s => Base36GuidEncoder.Decode(s.ChgId)).ToList();

        var matchingClaims = insuranceClaims
            .Where(
                x => x.ContactId == contactId &&
                x.ServiceLines.Any(s => chargeIds.Contains(s.Id)))
            .ToArray();

        if (matchingClaims.Length == 1)
        {
            return matchingClaims.Single();
        }

        // todo - match by PCN or Payer_ICN ? 
        Log
            .ForContext("ChargeIds", new
            {
                Ids = claim.Charges.Select(s => new { ChgId = Base36GuidEncoder.Decode(s.ChgId), s.ProcedureCode }),
            }, true)
            .ForContext("Matches", new
            {
                Ids = matchingClaims.Select(c => c.Id)
            }, true)
            .Warning("{Count} matching claims found", matchingClaims.Length);

        if (matchingClaims.Length == 0)
        {
            // No matching claims found, log the warning and continue to allow an unallocated payment to be processed.
            return null;
        }

        throw new InvalidOperationException($"Cannot process ERA. Multiple matching claims found");
    }

    private async Task<ProviderInsurancePayer> RetrievePayer(Guid providerId, ERADetail eraDetails, CancellationToken cancellationToken)
    {
        var payer = await insurancePayerRepository.GetByPayerNumber(providerId, eraDetails.PayerId, ClearingHouse, cancellationToken);

        if (payer is null)
        {
            Log.ForContext("PayerDetails", new
            {
                eraDetails.PayerId,
                eraDetails.PayerName,
            }, true)
                .Warning("No matching payer found");
        }

        return payer;
    }

    private Payment CreatePayment(ERADetail eraDetails, Guid contactId, Guid providerId, ProviderInsurancePayer payer, CancellationToken cancellationToken)
    {
        // Create payment for the contact that will contain all payments for each claim
        var rawAmount = eraDetails.Claims.Sum(s => Convert.ToDecimal(s.TotalPaid));
        var stripeAmount = CurrencyHandler.Get(CurrencyCodes.USD).ToStripeAmount(rawAmount);
        var paymentDate = ClaimMdDateHelper.MountainDateOnlyToUtc(eraDetails.PaidDate);

        var payment = new Payment
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            ContactId = contactId,
            Type = PaymentTypes.Insurance,
            PayerType = PayerType.Insurance,
            PaymentProvider = PaymentProviders.ClaimMD,
            Amount = stripeAmount,
            ChargeAmount = rawAmount,
            TransferAmount = rawAmount,
            InsurancePayerId = payer?.Id,
            PayerName = eraDetails.PayerName,
            Reference = eraDetails.CheckNumber,
            Fee = 0,
            IsClientChargedFee = false,
            CurrencyCode = CurrencyCodes.USD,
            PaymentDate = paymentDate,
            PayoutDateUtc = null,
            IsBillingV2 = true
        };

        return payment;
    }

    private async Task<Media> GetPdfAndUploadS3(ERADetail eraDetails, long pcn, Guid contactId, Guid providerId, Guid remittanceAdviceId)
    {
        // Grab the ERA details PDF from ClaimMD
        var eraDetailsPdfResponse = await claimMdHttpClient.GetERADetailsFile(new ERADetailsPDFRequest
        {
            EraId = eraDetails.EraId.ToString(),
            PCN = pcn.ToString()
        });

        if (eraDetailsPdfResponse?.Data is null)
        {
            throw new InvalidOperationException("Failed to retrieve ERA details PDF from ClaimMD");
        }

        // Upload the ERA details PDF to internal blob storage
        var uploadableFile = new UploadableFile
        {
            FileName = $"{eraDetails.EraId}.pdf",
            FileKey = $"providers/{providerId}/contacts/{contactId}/remittances/{remittanceAdviceId}.pdf",
            ContentType = "application/pdf",
            Bytes = Convert.FromBase64String(eraDetailsPdfResponse.Data.Single())
        };

        var files = await fileStorageRepository.Upload(
            FileLocationType.Files,
            [uploadableFile]
        );

        if (files is null || files.Length != 1)
        {
            throw new InvalidOperationException("Failed to upload ERA details PDF to internal storage");
        }

        var file = files.Single();

        // Create a media record for the uploaded file
        var media = new Media
        {
            Id = Guid.NewGuid(),
            FileName = uploadableFile.FileName,
            Url = uploadableFile.FileKey,
            FileExtensions = file.FileExtension,
            ContentType = file.ContentType,
            FileSize = file.FileSize,
            MediaType = MediaTypes.RemittanceAdvice
        };

        return await mediaRepository.Save(media);
    }
}
