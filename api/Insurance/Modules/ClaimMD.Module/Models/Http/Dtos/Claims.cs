using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace ClaimMD.Module.Models.Http.Dtos;

public abstract class BaseClaim
{
    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonProperty("claimmd_id")]
    public string ClaimMdId { get; set; }

    [JsonProperty("claimid")]
    public string ClaimId { get; set; }

    [JsonProperty("remote_claimid")]
    public string RemoteClaimId { get; set; }

    [JsonProperty("batchid")]
    public string BatchId { get; set; }

    [JsonProperty("sender_name")]
    public string SenderName { get; set; }

    [JsonProperty("messages")]
    public List<ClaimMessageDto> Messages { get; set; }

    [JsonProperty("fileid")]
    public string FileId { get; set; }

    [JsonProperty("ins_number")]
    public string InsuranceNumber { get; set; }

    [JsonProperty("bill_taxid")]
    public string BillTaxId { get; set; }

    [JsonProperty("bill_npi")]
    public string BillingNpi { get; set; }

    [JsonProperty("pcn")]
    public string PCN { get; set; }

    [JsonProperty("total_charge")]
    public string TotalCharge { get; set; }

    [JsonProperty("payerid")]
    public string PayerId { get; set; }

    [JsonProperty("senderid")]
    public string SenderId { get; set; }

    [JsonProperty("sender_icn")]
    public string SenderICN { get; set; }

    public string GetExternalId()
    {
        // Since ClaimMdId can potentially repeat in status updates, we need to use the latest response id instead
        var lastMessage =
            (
                Messages?.Count > 0
                    ? Messages.OrderByDescending(m => m.ResponseId).FirstOrDefault()
                    : null
            ) ?? throw new InvalidOperationException("No messages found in claim");
        return lastMessage.ResponseId.ToString();
    }
}

public class ClaimDto : BaseClaim
{
    [JsonProperty("fdos")]
    public string FDOS { get; set; }

    [JsonProperty("filename")]
    public string FileName { get; set; }
}

public class ClaimDetailDto : BaseClaim
{
    [JsonProperty("bill_addr_1")]
    public string BillingAddress1 { get; set; }

    [JsonProperty("bill_city")]
    public string BillingCity { get; set; }

    [JsonProperty("bill_name")]
    public string BillingName { get; set; }

    [JsonProperty("bill_phone")]
    public string BillingPhone { get; set; }

    [JsonProperty("bill_state")]
    public string BillingState { get; set; }

    [JsonProperty("bill_zip")]
    public string BillingZip { get; set; }

    [JsonProperty("charge")]
    public List<ClaimDetailChargeDto> Charges { get; set; }

    [JsonProperty("claim_form")]
    public string ClaimForm { get; set; }

    [JsonProperty("diag_1")]
    public string Diagnosis1 { get; set; }

    [JsonProperty("diag_2")]
    public string Diagnosis2 { get; set; }

    [JsonProperty("diag_3")]
    public string Diagnosis3 { get; set; }

    [JsonProperty("diag_4")]
    public string Diagnosis4 { get; set; }

    [JsonProperty("diag_5")]
    public string Diagnosis5 { get; set; }

    [JsonProperty("diag_6")]
    public string Diagnosis6 { get; set; }

    [JsonProperty("diag_7")]
    public string Diagnosis7 { get; set; }

    [JsonProperty("diag_8")]
    public string Diagnosis8 { get; set; }

    [JsonProperty("diag_9")]
    public string Diagnosis9 { get; set; }

    [JsonProperty("diag_10")]
    public string Diagnosis10 { get; set; }

    [JsonProperty("diag_11")]
    public string Diagnosis11 { get; set; }

    [JsonProperty("diag_12")]
    public string Diagnosis12 { get; set; }

    [JsonProperty("employment_related")]
    public string EmploymentRelated { get; set; }

    [JsonProperty("ins_addr_1")]
    public string PolicyHolderAddress1 { get; set; }

    [JsonProperty("ins_city")]
    public string PolicyHolderCity { get; set; }

    [JsonProperty("ins_state")]
    public string PolicyHolderState { get; set; }

    [JsonProperty("ins_zip")]
    public string PolicyHolderZip { get; set; }

    [JsonProperty("ins_dob")]
    public string PolicyHolderDateOfBirth { get; set; }

    [JsonProperty("ins_group")]
    public string PolicyHolderGroup { get; set; }

    [JsonProperty("ins_sex")]
    public string PolicyHolderSex { get; set; }

    [JsonProperty("pat_addr_1")]
    public string PatientAddress1 { get; set; }

    [JsonProperty("pat_city")]
    public string PatientCity { get; set; }

    [JsonProperty("pat_dob")]
    public string PatientDateOfBirth { get; set; }

    [JsonProperty("pat_name_f")]
    public string PatientFirstName { get; set; }

    [JsonProperty("pat_name_l")]
    public string PatientLastName { get; set; }

    [JsonProperty("pat_rel")]
    public string PatientRelationship { get; set; }

    [JsonProperty("pat_sex")]
    public string PatientSex { get; set; }

    [JsonProperty("pat_state")]
    public string PatientState { get; set; }

    [JsonProperty("pat_zip")]
    public string PatientZip { get; set; }

    [JsonProperty("prov_name_f")]
    public string ProviderFirstName { get; set; }

    [JsonProperty("prov_name_l")]
    public string ProviderLastName { get; set; }

    [JsonProperty("prov_name_m")]
    public string ProviderMiddleName { get; set; }

    [JsonProperty("prov_npi")]
    public string ProviderNpi { get; set; }

    [JsonProperty("prov_taxonomy")]
    public string ProviderTaxonomy { get; set; }

    [JsonProperty("ref_name_f")]
    public string ReferrerFirstName { get; set; }

    [JsonProperty("ref_name_l")]
    public string ReferrerLastName { get; set; }

    [JsonProperty("ref_name_m")]
    public string ReferrerMiddleName { get; set; }

    [JsonProperty("ref_npi")]
    public string ReferrerNpi { get; set; }

    [JsonProperty("remote_fileid")]
    public string RemoteFileId { get; set; }
}

public class StatusUpdateClaimDto : BaseClaim
{
    [JsonProperty("response_time")]
    public string ResponseTime { get; set; }
}

public class UploadedClaimDto : ClaimDetailDto
{
    [JsonProperty("accept_assign")]
    public string AcceptAssign { get; set; }

    [JsonProperty("auto_accident")]
    public string AutoAccident { get; set; }

    [JsonProperty("balance_due")]
    public string BalanceDue { get; set; }

    [JsonProperty("bill_taxid_type")]
    public string BillingTaxIdType { get; set; }

    [JsonProperty("ins_name_f")]
    public string PolicyHolderFirstName { get; set; }

    [JsonProperty("ins_name_l")]
    public string PolicyHolderLastName { get; set; }

    [JsonProperty("ins_name_m")]
    public string PolicyHolderMiddleName { get; set; }

    [JsonProperty("frequency_code")]
    public string ResubmissionCode { get; set; }

    [JsonProperty("icn_dcn_1")]
    public string OriginalReferenceNumber { get; set; }
}

public class ClaimMessageDto
{
    [JsonProperty("fields")]
    public string Fields { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonProperty("mesgid")]
    public string MesgId { get; set; }

    [JsonProperty("message")]
    public string Content { get; set; }

    [JsonProperty("responseid")]
    public long ResponseId { get; set; }
}

public class ClaimDetailChargeDto
{
    [JsonProperty("charge")]
    public string Charge { get; set; }

    [JsonProperty("from_date")]
    public string FromDate { get; set; }

    [JsonProperty("thru_date")]
    public string ThruDate { get; set; }

    [JsonProperty("units")]
    public string Units { get; set; }

    [JsonProperty("proc_code")]
    public string ProcedureCode { get; set; }

    [JsonProperty("charge_record_type")]
    public string ChargeRecordType { get; set; }

    [JsonProperty("diag_ref")]
    public string DiagnosisReference { get; set; }

    [JsonProperty("place_of_service")]
    public string PlaceOfService { get; set; }

    [JsonProperty("remote_chgid")]
    public string RemoteChargeId { get; set; }
}
