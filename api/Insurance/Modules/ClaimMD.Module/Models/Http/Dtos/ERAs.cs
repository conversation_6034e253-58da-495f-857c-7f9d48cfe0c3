using System;
using System.Collections.Generic;
using carepatron.core.Utilities;
using Newtonsoft.Json;

namespace ClaimMD.Module.Models.Http.Dtos;

public class ERAClaimDto
{
    [JsonProperty("pcn")]
    public string PCN { get; set; }
    [JsonProperty("pat_name_l")]
    public string PatientLastName { get; set; }

    [JsonProperty("pat_name_m")]
    public string PatientMiddleName { get; set; }

    [JsonProperty("pat_name_f")]
    public string PatientFirstName { get; set; }

    [JsonProperty("thru_dos")]
    public string ThruDOS { get; set; }

    [JsonProperty("from_dos")]
    public string FromDOS { get; set; }

    [JsonProperty("place_of_service")]
    public string PlaceOfService { get; set; }

    [JsonProperty("status_code")]
    public string StatusCode { get; set; }

    [JsonProperty("claim_received_date")]
    public string ClaimReceivedDate { get; set; }
    [JsonProperty("patient_responsibility")]
    public string PatientResponsibility { get; set; }

    [JsonProperty("total_charge")]
    public string TotalCharge { get; set; }

    [JsonProperty("total_paid")]
    public string TotalPaid { get; set; }
    [JsonProperty("ins_number")]
    public string InsuranceNumber { get; set; }

    [JsonProperty("ins_name_f")]
    public string PolicyHolderFirstName { get; set; }

    [JsonProperty("ins_name_l")]
    public string PolicyHolderLastName { get; set; }

    [JsonProperty("ins_name_m")]
    public string PolicyHolderMiddleName { get; set; }

    [JsonProperty("plan_type")]
    public string PlanType { get; set; }

    [JsonProperty("filing_code")]
    public string FilingCode { get; set; }

    [JsonProperty("crossover_id")]
    public string CrossoverId { get; set; }

    [JsonProperty("crossover_carrier")]
    public string CrossoverCarrier { get; set; }

    [JsonProperty("frequency_code")]
    public string FrequencyCode { get; set; }

    [JsonProperty("payer_icn")]
    public string PayerICN { get; set; }

    [JsonProperty("charge")]
    public List<ERAClaimChargeDto> Charges { get; set; }

    [JsonProperty("prov_npi")]
    public string ProviderNpi { get; set; }

    [JsonProperty("remark_code_1")]
    public string RemarkCode1 { get; set; }

    [JsonProperty("remark_code_2")]
    public string RemarkCode2 { get; set; }

    [JsonProperty("remark_code_3")]
    public string RemarkCode3 { get; set; }

    [JsonProperty("remark_code_4")]
    public string RemarkCode4 { get; set; }

    [JsonProperty("remark_code_5")]
    public string RemarkCode5 { get; set; }

    [JsonProperty("remark_code_6")]
    public string RemarkCode6 { get; set; }

    [JsonProperty("remark_code_7")]
    public string RemarkCode7 { get; set; }

    [JsonProperty("remark_code_8")]
    public string RemarkCode8 { get; set; }
}

public class ERAClaimChargeDto
{
    [JsonProperty("charge")]
    public string Charge { get; set; }

    [JsonProperty("from_dos")]
    public string FromDate { get; set; }

    [JsonProperty("thru_dos")]
    public string ThruDate { get; set; }

    [JsonProperty("units")]
    public string Units { get; set; }

    [JsonProperty("proc_code")]
    public string ProcedureCode { get; set; }

    [JsonProperty("mod1")]
    public string Mod1 { get; set; }

    [JsonProperty("mod2")]
    public string Mod2 { get; set; }

    [JsonProperty("mod3")]
    public string Mod3 { get; set; }

    [JsonProperty("mod4")]
    public string Mod4 { get; set; }

    [JsonProperty("adjustment")]
    public List<ChargeAdjustmentDto> Adjustments { get; set; }

    [JsonProperty("chgid")]
    public string ChgId { get; set; }

    [JsonProperty("pos")]
    public string Pos { get; set; }

    [JsonProperty("paid")]
    public string Paid { get; set; }

    [JsonProperty("allowed")]
    public string Allowed { get; set; }
}

public class ChargeAdjustmentDto
{
    [JsonProperty("amount")]
    public string Amount { get; set; }

    [JsonProperty("code")]
    public string Code { get; set; }

    [JsonProperty("group")]
    public string Group { get; set; }
}
