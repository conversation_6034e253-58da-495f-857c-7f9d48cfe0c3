using System;
using System.Collections.Generic;
using ClaimMD.Module.Models.Http.Dtos;
using ClaimMD.Module.Utilities;
using Newtonsoft.Json;

namespace ClaimMD.Module.Models.Http.Responses;

public class ERADetailsResponse : BaseResponse
{
    [JsonProperty("prov_account")]
    public string ProviderAccount { get; set; }

    [JsonProperty("prov_routing")]
    public string ProviderRouting { get; set; }

    [JsonProperty("prov_name")]
    public string ProviderName { get; set; }

    [JsonProperty("prov_taxid")]
    public string ProviderTaxId { get; set; }

    [JsonProperty("prov_npi")]
    public string ProviderNpi { get; set; }

    [JsonProperty("prov_addr_1")]
    public string ProviderAddr1 { get; set; }

    [JsonProperty("prov_city")]
    public string ProviderCity { get; set; }

    [JsonProperty("prov_state")]
    public string ProviderState { get; set; }

    [JsonProperty("prov_zip")]
    public string ProviderZip { get; set; }

    [JsonProperty("payer_account")]
    public string PayerAccount { get; set; }

    [JsonProperty("payerid")]
    public string PayerId { get; set; }

    [JsonProperty("payer_name")]
    public string PayerName { get; set; }

    [JsonProperty("payer_companyid")]
    public string PayerCompanyId { get; set; }

    [JsonProperty("payer_routing")]
    public string PayerRouting { get; set; }

    [JsonProperty("payer_addr_1")]
    public string PayerAddr1 { get; set; }

    [JsonProperty("payer_city")]
    public string PayerCity { get; set; }

    [JsonProperty("payer_state")]
    public string PayerState { get; set; }

    [JsonProperty("payer_zip")]
    public string PayerZip { get; set; }

    [JsonProperty("claim")]
    public List<ERAClaimDto> Claims { get; set; }

    [JsonProperty("check_number")]
    public string CheckNumber { get; set; }

    [JsonProperty("eraid")]
    public int EraId { get; set; }

    [JsonProperty("paid_date")]
    public string PaidDate { get; set; }

    [JsonProperty("payment_method")]
    public string PaymentMethod { get; set; }

    [JsonProperty("paid_amount")]
    public string PaidAmount { get; set; }

    [JsonProperty("payment_format")]
    public string PaymentFormat { get; set; }

    [JsonProperty("eft_sender_id")]
    public string EftSenderId { get; set; }
}

public class ERADetailsPDFResponse : BaseResponse
{
    [JsonProperty("prov_npi")]
    public string ProviderNpi { get; set; }

    [JsonProperty("paid_date")]
    public string PaidDate { get; set; }

    [JsonProperty("payer_name")]
    public string PayerName { get; set; }

    [JsonProperty("payerid")]
    public string PayerId { get; set; }

    [JsonProperty("paid_amount")]
    public string PaidAmount { get; set; }

    [JsonProperty("eraid")]
    public int EraId { get; set; }

    [JsonProperty("check_number")]
    public string CheckNumber { get; set; }

    [JsonProperty("prov_name")]
    public string ProviderName { get; set; }

    [JsonProperty("data")]
    public string[] Data { get; set; }
}

public record ERADetail(
    long EraId,
    string PayerId,
    string PayerName,
    string CheckNumber,
    DateOnly PaidDate,
    decimal PaidAmount,
    string PaymentMethod,
    ERAClaimDto[] Claims
)
{
    public static ERADetail Create(ERADetailsResponse eraDetails, ERAClaimDto[] claims = null)
    {
        return new ERADetail(
            eraDetails.EraId,
            eraDetails.PayerId,
            eraDetails.PayerName,
            eraDetails.CheckNumber,
            DateOnly.ParseExact(eraDetails.PaidDate, ClaimMdDateHelper.Formats.ISO8601Date),
            decimal.Parse(eraDetails.PaidAmount),
            eraDetails.PaymentMethod,
            claims ?? eraDetails.Claims.ToArray()
        );
    }
}
