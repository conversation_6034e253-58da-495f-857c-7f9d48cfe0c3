using System;
using System.Linq;
using System.Text.Json;
using System.Threading;
using Amazon.SQS;
using Amazon.SQS.Model;
using carepatron.core.Events.Handlers;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.Repositories.Files;
using ClaimMD.Module.Interfaces;
using Messaging;
using Messaging.Aws.Sqs;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Notifications.Sdk.Client.Abstract;
using tests.common.Extensions;
using tests.common.Fixtures;
using tests.common.Mocks;

namespace Billing.Queue.Worker.Tests.Setup;

public class BillingWorkerQueueTestFixture : BaseWorkerFixture
{
    public AwsSqsMock SqsRepository => TestScope.ServiceProvider.GetRequiredService<AwsSqsMock>();

    public BillingWorkerQueueTestFixture()
    {
        CreateTestServer(
            Program.CreateHostBuilder([]),
            services =>
            {
                services.AddScopedMock<IAmazonSQS, AwsSqsMock>();
                services.AddScopedMock<IIntegrationEventPublisher, EventPublisherMock>();
                services.AddScopedMock<IEventHandler, Mock<IEventHandler>>();
                services.AddScopedMock<INotificationsService, NotificationsServiceMock>();
                services.AddScopedMock<IClaimMdHttpClient, ClaimMdHttpClientMock>();
                services.AddScopedMock<IFileStorageRepository, FileStorageRepositoryMock>();
            }
        );
    }

    public ReceiveMessageResponse QueueMessage<T>(T payload)
        where T : IMessagePayload
    {
        var awsSqsMock = TestScope.ServiceProvider.GetRequiredService<AwsSqsMock>();
        var envelope = new Envelope(payload);
        var receivedMessageResponse = new ReceiveMessageResponse
        {
            Messages =
            [
                new Message
                {
                    Body = JsonSerializer.Serialize(envelope, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    }),
                    MessageId = Guid.NewGuid().ToString(),
                    ReceiptHandle = Guid.NewGuid().ToString(),
                }
            ]
        };
        awsSqsMock.ReceivedMessages.AddRange(receivedMessageResponse.Messages);
        return receivedMessageResponse;
    }

    public void VerifyMessageDeleted(Message message, Times? times = null)
    {
        SqsRepository.Verify(x => x.DeleteMessageAsync(It.Is<DeleteMessageRequest>(
           x => x.ReceiptHandle == message.ReceiptHandle), It.IsAny<CancellationToken>()),
           times ?? Times.Once());
    }
    
    public void DeleteMessage(ReceiveMessageResponse receiveMessageResponse)
        => DeleteMessages(receiveMessageResponse.Messages.Select(x => x.ReceiptHandle).ToArray());
    
    public void DeleteMessages(params string[] receiptHandles)
    {
        foreach (var receiptHandle in receiptHandles)
        {
            var message = SqsRepository.ReceivedMessages.FirstOrDefault(m => m.ReceiptHandle == receiptHandle);

            if (message != null)
            {
                SqsRepository.ReceivedMessages.Remove(message);
            }
        }
    }
}