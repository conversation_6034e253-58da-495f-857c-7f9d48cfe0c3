using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using tests.common.Fixtures;
using Xunit;
using carepatron.core.Constants;
using Billing.Queue.Worker.Tests.Setup;
using System.Threading.Tasks;
using System.Threading;
using Bogus;
using carepatron.core.Application.Billables.Models;
using carepatron.core.Application.ClearingHouse.Models;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Extensions;
using carepatron.infra.sql.Models.BillableItem;
using carepatron.infra.sql.Models.ClearingHouse;
using carepatron.infra.sql.Models.Insurance;
using ClaimMD.Module.Messages;
using ClaimMD.Module.Models.Http.Requests;
using ClaimMD.Module.Models.Http.Responses;
using ClaimMD.Module.Utilities;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Moq;
using Newtonsoft.Json;
using tests.common.Data.Fakers;
using tests.common.Data.Fakers.ClaimMD;
using tests.common.Data.Mappers;
using tests.common.Data.Mappers.Insurance;
using tests.common.Mocks;
using carepatron.core.Utilities;

namespace Billing.Queue.Worker.Tests.MessageHandlers;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class ClaimMdProcessERAMessageTests(BillingWorkerQueueTestFixture fixture)
 : WorkerComponentTestBase<BillingWorkerQueueTestFixture>(fixture)
{
    [Fact]
    public async Task InvokesHandler_WithValidEra_ShouldCreateRemittanceAdviceAndContactClaimMessages()
    {
        var (claims, era, eraDetailsResponse) = await CreateEra(uniqueContacts: 2);

        ResolveService<ClaimMdHttpClientMock>()
            .Setup(s => s.GetERADetails(It.Is<ERADetailsRequest>(request => request.EraId == era.EraId)))
            .ReturnsAsync(eraDetailsResponse);

        var messageResponse = Fixture.QueueMessage(new ClaimMdProcessERAMessage
        {
            ERA = era
        });

        await Fixture.Run(CancellationToken.None);

        var dbRemittanceAdvice = await RetrieveRemittanceAdvice(era);
        dbRemittanceAdvice.Should().NotBeNull();

        var dbClearingHouseLog = await RetrieveClearingHouseLog(era.EraId);
        dbClearingHouseLog.Should().NotBeNull();
        dbClearingHouseLog.Status.Should().Be(ClearingHouseLogStatus.Processed);

        AssertClaimMDProcessContactERAMessagesSent(era.EraId, claims.Select(x => x.ContactId).Distinct().ToArray());

        Fixture.VerifyMessageDeleted(messageResponse.Messages.Single());
    }

    [Fact]
    public async Task InvokesHandler_WithProcessingError_ShouldNotCreateRemittanceOrContactClaimMessages()
    {
        var (claims, era, _) = await CreateEra(uniqueContacts: 2);

        ResolveService<ClaimMdHttpClientMock>()
            .Setup(s => s.GetERADetails(It.Is<ERADetailsRequest>(request => request.EraId == era.EraId)))
            .ReturnsAsync(default(ERADetailsResponse));

        var messageResponse = Fixture.QueueMessage(new ClaimMdProcessERAMessage
        {
            ERA = era
        });

        await Fixture.Run(CancellationToken.None);

        var dbRemittanceAdvice = await RetrieveRemittanceAdvice(era);
        dbRemittanceAdvice.Should().BeNull();

        var dbClearingHouseLog = await RetrieveClearingHouseLog(era.EraId);
        dbClearingHouseLog.Should().NotBeNull();
        dbClearingHouseLog.Status.Should().Be(ClearingHouseLogStatus.Pending);

        AssertClaimMDProcessContactERAMessagesNotSent(era.EraId, claims.Select(x => x.ContactId).Distinct().ToArray());

        Fixture.VerifyMessageDeleted(messageResponse.Messages.Single(), Times.Never());
    }

    [Fact]
    public async Task InvokesHandler_WithUnmatchedPCN_ShouldNotCreateRemittanceOrContactClaimMessages()
    {
        // set up multiple claims and contacts
        // if any fail, we stop processing the ERA.
        var (claims, era, eraDetailsResponse) = await CreateEra(uniqueContacts: 2);

        // Create a random PCN that won't match a contact or claim contact
        eraDetailsResponse.Claims.First().PCN = new Faker().Random.Long(1).ToString();

        ResolveService<ClaimMdHttpClientMock>()
            .Setup(s => s.GetERADetails(It.Is<ERADetailsRequest>(request => request.EraId == era.EraId)))
            .ReturnsAsync(eraDetailsResponse);

        var messageResponse = Fixture.QueueMessage(new ClaimMdProcessERAMessage
        {
            ERA = era
        });

        await Fixture.Run(CancellationToken.None);

        var dbRemittanceAdvice = await RetrieveRemittanceAdvice(era);
        dbRemittanceAdvice.Should().BeNull();

        var dbClearingHouseLog = await RetrieveClearingHouseLog(era.EraId);
        dbClearingHouseLog.Should().NotBeNull();
        dbClearingHouseLog.Status.Should().Be(ClearingHouseLogStatus.Pending);

        AssertClaimMDProcessContactERAMessagesNotSent(era.EraId, claims.Select(x => x.ContactId).Distinct().ToArray());

        Fixture.VerifyMessageDeleted(messageResponse.Messages.Single(), Times.Never());
    }

    private async Task<(InsuranceClaimUSProfessional[] claims, ERAListItemDto era, ERADetailsResponse eraDetailsResponse)> CreateEra(int uniqueContacts = 2)
    {
        var claims = await CreateInsuranceClaimUSProfessional(ProviderId, uniqueContacts);
        var era = new ERAListItemDtoFaker().FromClaim(claims.First()).Generate();
        var eraDetailsResponse = new ERADetailsResponseFaker()
            .FromClaim(claims)
            .RuleFor(x => x.EraId, int.Parse(era.EraId))
            .RuleFor(x => x.CheckNumber, era.CheckNumber)
            .Generate();

        await CreateClearingHouseLog(era);

        return (claims, era, eraDetailsResponse);
    }

    private Task<InsuranceRemittanceAdviceDataModel> RetrieveRemittanceAdvice(ERAListItemDto era)
        => DataContext.InsuranceRemittanceAdvices
            .AsNoTracking()
            .SingleOrDefaultAsync(x =>
                x.ProviderId == ProviderId &&
                x.ExternalId == era.EraId &&
                x.NPINumber == era.ProviderNpi &&
                x.TaxId == era.ProviderTaxId &&
                x.PaymentReference == era.CheckNumber);

    private void AssertClaimMDProcessContactERAMessagesSent(string eraId, Guid[] contactIds)
        => AssertClaimMDProcessContactERAMessagesSent(RetrieveSentMessages(), eraId, contactIds);

    private void AssertClaimMDProcessContactERAMessagesSent(ClaimMDProcessContactERAMessage[] messages, string eraId, Guid[] contactIds)
    {
        foreach (var uniqueContactId in contactIds)
        {
            messages.Should().ContainSingle(message =>
                message.ERADetails.EraId.ToString().Equals(eraId) &&
                message.ContactId == uniqueContactId);
        }
    }

    private void AssertClaimMDProcessContactERAMessagesNotSent(string eraId, Guid[] contactIds)
        => AssertClaimMDProcessContactERAMessagesNotSent(RetrieveSentMessages(), eraId, contactIds);

    private void AssertClaimMDProcessContactERAMessagesNotSent(ClaimMDProcessContactERAMessage[] messages, string eraId, Guid[] contactIds)
    {
        foreach (var uniqueContactId in contactIds)
        {
            messages.Should().NotContain(message =>
                message.ERADetails.EraId.ToString().Equals(eraId) &&
                message.ContactId == uniqueContactId);
        }
    }

    private async Task CreateClearingHouseLog(ERAListItemDto era)
    {
        var clearingHouseLog = ClearingHouseLog.Create(
            ClearingHouseType.ManagedClaimMd,
            ClearingHouseLogStatus.Pending,
            ClearingHouseLogType.Era,
            JsonDocument.Parse(JsonConvert.SerializeObject(era)),
            era.EraId
        );

        await DataContext.ClearingHouseLogs.AddAsync(clearingHouseLog.ToDataModel());
        await DataContext.SaveChangesAsync();
    }

    private Task<ClearingHouseLogDataModel> RetrieveClearingHouseLog(string eraId)
        => DataContext.ClearingHouseLogs
            .AsNoTracking()
            .SingleOrDefaultAsync(x =>
                x.ExternalId == eraId &&
                x.ClearingHouse == ClearingHouseType.ManagedClaimMd &&
                x.Type == ClearingHouseLogType.Era);

    private ClaimMDProcessContactERAMessage[] RetrieveSentMessages()
    {
        return Fixture.SqsRepository.ReceivedMessages
            .Select(message =>
            {
                var json = JsonDocument.Parse(message.Body).RootElement;
                var messageType = json.GetProperty("messageType").GetString();

                if (messageType != nameof(ClaimMDProcessContactERAMessage))
                {
                    return null;
                }

                var payloadJson = json.GetProperty("payload").ToString();

                return JsonConvert.DeserializeObject<ClaimMDProcessContactERAMessage>(payloadJson);
            })
            .Where(message => message != null).ToArray();
    }

    private async Task<InsuranceClaimUSProfessional[]> CreateInsuranceClaimUSProfessional(Guid providerId, int uniqueContacts = 2)
    {
        var payer = new ProviderInsurancePayerFaker(providerId).Generate();
        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(providerId, staffPerson).Generate();

        DataContext.AddRange(
            payer.ToDataModel(),
            staffPerson.ToDataModel(),
            staffMember.ToDataModel());

        var claims = Enumerable.Range(0, uniqueContacts).Select(_ =>
        {
            var contact = new ContactFaker(providerId).RuleFor(x => x.MiddleNames, x => x.Person.LastName).Generate();
            var memberContact = new ContactFaker(providerId).RuleFor(x => x.MiddleNames, x => x.Person.LastName).Generate();

            DataContext.Contacts.AddRange([
                contact.ToDataModel(),
                memberContact.ToDataModel()
            ]);

            var policy = new ContactInsurancePolicyFaker(providerId, memberContact.Id)
                .RuleFor(x => x.Payer, payer)
                .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
                {
                    Type = InsurancePolicyHolderType.Client,
                    Contact = memberContact.ToPolicyHolder(),
                    RelationshipType = "Client"
                }).Generate();

            DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

            var defaultBillingProfile = new ProviderBillingProfileFaker(providerId)
                .RuleFor(x => x.IsDefault, true)
                .Generate();

            DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

            var billableItem = CreateBillableTestData(contact, providerId);

            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(providerId, contact.Id, billableItem.Id).Generate()
            };

            var detail = new USProfessionalClaimFaker(providerId)
                .WithBillingDetail(providerId, defaultBillingProfile)
                .WithClient(providerId, contact)
                .WithContactInsurancePolicy(providerId, contact.Id, policy)
                .WithDiagnosticCodes(providerId)
                .WithFacility(providerId)
                .WithIncident(providerId)
                .WithReferringProviders(providerId)
                .WithRenderingProviders(providerId, staffMember)
                .WithServiceLines([.. serviceLines])
                .WithInsurancePayer(payer)
                .Generate();

            var claim = detail.ToClaimHeaderDataModel();
            var lookup = detail.ToClearingHouseIdLookupDataModel();

            DataContext.ClearingHouseIdLookups.Add(lookup);
            DataContext.InsuranceClaims.Add(claim);
            DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());

            return detail;

        }).ToArray();

        await DataContext.SaveChangesAsync();

        return claims;
    }

    private BillableItem CreateBillableTestData(Contact contact, Guid providerId, Person staff = null)
    {
        var unchargedBillableItem = new BillableItemFaker(providerId, contact.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.Price, 100)
            .RuleFor(x => x.ServiceId, (Guid?)null)
            .Generate();

        var unchargedBillable = new BillableDataModel
        {
            Id = Guid.NewGuid(),
            TaskId = null,
            ContactId = contact.Id,
            ProviderId = providerId,
            TotalOwed = 100,
            Date = DateTime.UtcNow.WithoutSecsAndMs(),
            Type = BillableType.Invoice,
            BillableItems = [unchargedBillableItem.ToDataModel()],
        };

        DataContext.AddRangeAsync(unchargedBillable);

        return unchargedBillableItem;
    }
}
