using System.Text.Json;
using carepatron.core.Application.ClearingHouse.Models;
using carepatron.core.Application.Insurance.Models;
using ClaimMD.Module.Interfaces;
using ClaimMD.Module.Messages;
using tests.common.Data.Fakers.ClaimMD;
using Moq;
using Xunit;
using ClaimMD.Module.Messages.Handlers;
using ClaimMD.Module.Queues;
using Messaging.Models;
using Messaging.Services;
using carepatron.core.Repositories.ClearingHouse;
using carepatron.core.Repositories.Insurance;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Abstractions;
using ClaimMD.Module.Exceptions;
using ClaimMD.Module.Models.Http.Dtos;
using ClaimMD.Module.Models.Http.Requests;
using ClaimMD.Module.Models.Http.Responses;
using FluentAssertions;
using tests.common.Data.Fakers;
using tests.common.Data.Fakers.Insurance;
using Contact = carepatron.core.Application.Contacts.Models.Contact;
using carepatron.core.Utilities;
using Bogus;

namespace ClaimMD.Module.Tests.Messages.Handlers;

public class ClaimMdProcessERAMessageHandlerTests
{
    private readonly Mock<IClaimMdHttpClient> claimMdHttpClientMock = new();
    private readonly Mock<IMessageSender<ClaimMdMessageQueue>> messageSenderMock = new();
    private readonly Mock<IContactRepository> contactRepositoryMock = new();
    private readonly Mock<IInsuranceClaimClientRepository> insuranceClaimClientRepositoryMock = new();
    private readonly Mock<IInsuranceRemittanceAdvicesRepository> insuranceRemittanceAdvicesRepositoryMock = new();
    private readonly Mock<IInsurancePayerRepository> providerInsurancePayerRepositoryMock = new();
    private readonly Mock<IClearingHouseLogRepository> clearingHouseLogRepositoryMock = new();
    private readonly Mock<IClearingHouseIdLookupRepository> clearingHouseIdLookupRepositoryMock = new();

    private readonly ClaimMdProcessERAMessageHandler sut;

    public ClaimMdProcessERAMessageHandlerTests()
    {
        sut = new ClaimMdProcessERAMessageHandler(
            Mock.Of<IUnitOfWork>(),
            claimMdHttpClientMock.Object,
            messageSenderMock.Object,
            contactRepositoryMock.Object,
            insuranceClaimClientRepositoryMock.Object,
            insuranceRemittanceAdvicesRepositoryMock.Object,
            providerInsurancePayerRepositoryMock.Object,
            clearingHouseLogRepositoryMock.Object,
            clearingHouseIdLookupRepositoryMock.Object
        );
    }

    [Fact]
    public async Task Invoke_WithValidMessage_ShouldProcessMessage()
    {
        var (_, message, contacts, _, _) = SetupMockDefaults();

        await InvokeHandler(message);

        AssertClearingHouseLogIsProcessed(message);
        AssertContactMessagesSent(contacts);
    }

    [Fact]
    public async Task Invoke_WithValidPcn_ShouldResolveProviderIdFromContact()
    {
        var (_, message, contacts, _, _) = SetupMockDefaults();
        var contact = contacts.First();

        await InvokeHandler(message);

        contactRepositoryMock.Verify(x => x.Get(contact.Id, true), Times.Once);
        insuranceClaimClientRepositoryMock.Verify(x => x.GetProviderIdByContact(contact.Id), Times.Never);

        AssertClearingHouseLogIsProcessed(message);
        AssertContactMessagesSent(contacts);
    }

    [Fact]
    public async Task Invoke_WithValidPcn_ShouldResolveProviderIdFromClaimContactAsFallback()
    {
        var (_, message, contacts, _, _) = SetupMockDefaults();
        var contact = contacts.First();

        contactRepositoryMock
            .Setup(x => x.Get(contact.Id, true))
            .ReturnsAsync(default(Contact));

        await InvokeHandler(message);

        contactRepositoryMock.Verify(x => x.Get(contact.Id, true), Times.Once);
        insuranceClaimClientRepositoryMock.Verify(x => x.GetProviderIdByContact(contact.Id), Times.Once);

        AssertClearingHouseLogIsProcessed(message);
        AssertContactMessagesSent(contacts);
    }

    [Fact]
    public async Task Invoke_WithMissingRemittanceAdvice_ShouldCreateRemittanceAdviceOncePerProvider()
    {
        var (provider1Id, message, provider1Contacts, provider1Claims, m1) = SetupMockDefaults(3);
        var (provider2Id, _, provider2Contacts, provider2Claims, m2) = SetupMockDefaults(1);

        var pcnMapping = m1.Concat(m2).ToDictionary(x => x.Key, x => x.Value);
        clearingHouseIdLookupRepositoryMock
            .Setup(x => x.Lookup(pcnMapping.Keys.ToArray(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(pcnMapping);

        var era = new ERADetailsResponseFaker(provider1Claims.Concat(provider2Claims).ToArray())
            .RuleFor(x => x.ProviderNpi, message.ERA.ProviderNpi)
            .RuleFor(x => x.ProviderTaxId, message.ERA.ProviderTaxId)
            .Generate();

        claimMdHttpClientMock
            .Setup(x => x.GetERADetails(It.IsAny<ERADetailsRequest>()))
            .ReturnsAsync(era);

        insuranceRemittanceAdvicesRepositoryMock
            .Setup(x => x.Get(provider1Id, message.ERA.ProviderNpi, message.ERA.ProviderTaxId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(default(InsuranceRemittanceAdvice));

        insuranceRemittanceAdvicesRepositoryMock
            .Setup(x => x.Get(provider2Id, message.ERA.ProviderNpi, message.ERA.ProviderTaxId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(default(InsuranceRemittanceAdvice));

        await InvokeHandler(message);

        // Only one request to get remittance advice should occur for provider + npi + tax id regardless of contact count
        insuranceRemittanceAdvicesRepositoryMock.Verify(x => x.Get(
            provider1Id,
            message.ERA.ProviderNpi,
            message.ERA.ProviderTaxId,
            It.IsAny<CancellationToken>()), Times.Once);

        insuranceRemittanceAdvicesRepositoryMock.Verify(x => x.Get(
            provider2Id,
            message.ERA.ProviderNpi,
            message.ERA.ProviderTaxId,
            It.IsAny<CancellationToken>()), Times.Once);

        // multiple contacts should not trigger multiple remittance creation requests for each provider
        insuranceRemittanceAdvicesRepositoryMock.Verify(x => x.Create(It.Is<InsuranceRemittanceAdvice>(remittanceAdvice =>
            remittanceAdvice.ProviderId == provider1Id &&
            remittanceAdvice.NPINumber == message.ERA.ProviderNpi &&
            remittanceAdvice.TaxId == message.ERA.ProviderTaxId
        )), Times.Once);

        insuranceRemittanceAdvicesRepositoryMock.Verify(x => x.Create(It.Is<InsuranceRemittanceAdvice>(remittanceAdvice =>
            remittanceAdvice.ProviderId == provider2Id &&
            remittanceAdvice.NPINumber == message.ERA.ProviderNpi &&
            remittanceAdvice.TaxId == message.ERA.ProviderTaxId
        )), Times.Once);

        AssertClearingHouseLogIsProcessed(message);
        AssertContactMessagesSent(provider1Contacts.Concat(provider2Contacts).ToArray());
    }

    [Fact]
    public async Task Invoke_WithMissingEraDetails_ShouldThrow()
    {
        var (_, message, _, _, _) = SetupMockDefaults();

        claimMdHttpClientMock
            .Setup(x => x.GetERADetails(It.IsAny<ERADetailsRequest>()))
            .ReturnsAsync(default(ERADetailsResponse));

        var action = async () => await InvokeHandler(message);

        await action.Should().ThrowAsync<ClaimMdEraException>();

        AssertClearingHouseLogIsNotProcessed(message);
        AssertNoContactMessagesSent();
    }

    [Fact]
    public async Task Invoke_WhenUnableToResolveProviderId_ShouldThrow()
    {
        var (_, message, contacts, _, _) = SetupMockDefaults(1);
        var contact = contacts.Single();

        contactRepositoryMock
            .Setup(x => x.Get(contact.Id, true))
            .ReturnsAsync(default(Contact));

        insuranceClaimClientRepositoryMock
            .Setup(x => x.GetProviderIdByContact(contact.Id))
            .ReturnsAsync(default(Guid?));

        var action = async () => await InvokeHandler(message);

        await action.Should().ThrowAsync<ClaimMdEraException>();

        contactRepositoryMock.Verify(x => x.Get(contact.Id, true), Times.Once);
        insuranceClaimClientRepositoryMock.Verify(x => x.GetProviderIdByContact(contact.Id), Times.Once);

        AssertClearingHouseLogIsNotProcessed(message);
        AssertNoContactMessagesSent();
    }

    [Fact]
    public async Task Invoke_WithInvalidPcn_ShouldSkipContact()
    {
        var (_, message, _, claims, pcnMapping) = SetupMockDefaults(2);
        var validClaim = claims.First();
        var invalidClaim = claims.Last();

        // remove the previously valid PCN created in the setup
        pcnMapping.Remove(invalidClaim.PCN);
        invalidClaim.PCN = "invalid-pcn";
        // add invalid pcn back to keys for lookup
        var pcnKeys = pcnMapping.Keys.Concat(new[] { invalidClaim.PCN }).ToArray();

        clearingHouseIdLookupRepositoryMock
            .Setup(x => x.Lookup(pcnKeys, It.IsAny<CancellationToken>()))
            .ReturnsAsync(pcnMapping);

        var era = new ERADetailsResponseFaker(claims)
            .RuleFor(x => x.ProviderNpi, message.ERA.ProviderNpi)
            .RuleFor(x => x.ProviderTaxId, message.ERA.ProviderTaxId)
            .Generate();

        claimMdHttpClientMock
            .Setup(x => x.GetERADetails(It.IsAny<ERADetailsRequest>()))
            .ReturnsAsync(era);

        await InvokeHandler(message);

        AssertClearingHouseLogIsProcessed(message);
        AssertContactMessagesSent(validClaim.PCN);
    }

    [Fact]
    public async Task Invoke_WithInvalidPaidAmount_ShouldThrow()
    {
        var (providerId, message, _, claims, _) = SetupMockDefaults(1);

        var era = new ERADetailsResponseFaker(claims)
            .RuleFor(x => x.ProviderNpi, message.ERA.ProviderNpi)
            .RuleFor(x => x.ProviderTaxId, message.ERA.ProviderTaxId)
            .RuleFor(x => x.PaidAmount, "invalid-amount")
            .Generate();

        claimMdHttpClientMock
            .Setup(x => x.GetERADetails(It.IsAny<ERADetailsRequest>()))
            .ReturnsAsync(era);

        insuranceRemittanceAdvicesRepositoryMock
            .Setup(x => x.Get(providerId, message.ERA.ProviderNpi, message.ERA.ProviderTaxId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(default(InsuranceRemittanceAdvice));

        var action = async () => await InvokeHandler(message);

        await action.Should().ThrowAsync<ClaimMdEraException>();

        AssertClearingHouseLogIsNotProcessed(message);
        AssertNoContactMessagesSent();
    }

    [Fact]
    public async Task Invoke_WithInvalidPaidDate_ShouldSThrow()
    {
        var (providerId, message, _, claims, _) = SetupMockDefaults(1);

        var era = new ERADetailsResponseFaker(claims)
            .RuleFor(x => x.ProviderNpi, message.ERA.ProviderNpi)
            .RuleFor(x => x.ProviderTaxId, message.ERA.ProviderTaxId)
            .RuleFor(x => x.PaidDate, "invalid-date")
            .Generate();

        claimMdHttpClientMock
            .Setup(x => x.GetERADetails(It.IsAny<ERADetailsRequest>()))
            .ReturnsAsync(era);

        insuranceRemittanceAdvicesRepositoryMock
            .Setup(x => x.Get(providerId, message.ERA.ProviderNpi, message.ERA.ProviderTaxId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(default(InsuranceRemittanceAdvice));

        var action = async () => await InvokeHandler(message);

        await action.Should().ThrowAsync<ClaimMdEraException>();

        AssertClearingHouseLogIsNotProcessed(message);
        AssertNoContactMessagesSent();
    }

    private Task InvokeHandler(ClaimMdProcessERAMessage message)
        => sut.Invoke(JsonSerializer.Serialize(message), new MessageAttributes("123", "TestMessage"), JsonSerializerOptions.Default, CancellationToken.None);

    private void AssertClearingHouseLogIsProcessed(ClaimMdProcessERAMessage message)
        => AssertClearingHouseLogIsProcessed(message.ERA.EraId, Times.Once());

    private void AssertClearingHouseLogIsNotProcessed(ClaimMdProcessERAMessage message)
        => AssertClearingHouseLogIsProcessed(message.ERA.EraId, Times.Never());

    private void AssertClearingHouseLogIsProcessed(string eraId, Times times)
        => clearingHouseLogRepositoryMock.Verify(x => x.UpdateStatus(
            eraId,
            ClearingHouseType.ManagedClaimMd,
            ClearingHouseLogType.Era,
            ClearingHouseLogStatus.Processed,
            It.IsAny<CancellationToken>()), times);

    private void AssertContactMessagesSent(params Contact[] contacts)
        => AssertContactMessagesSent(contacts.Select(contact => contact.Id.ToString()).ToArray());

    private void AssertContactMessagesSent(params string[] pcns)
        => messageSenderMock.Verify(x => x.SendBatch(It.Is<SendRequest[]>(requests => requests.Length == pcns.Length)), Times.Once);

    private void AssertNoContactMessagesSent()
        => messageSenderMock.Verify(x => x.SendBatch(It.IsAny<SendRequest[]>()), Times.Never);

    private (Guid providerId, ClaimMdProcessERAMessage message, Contact[] contacts, ERAClaimDto[] claims, Dictionary<string, Guid> mapping) SetupMockDefaults(int uniqueContactsCount = 2)
    {
        var providerId = Guid.NewGuid();
        var contacts = new List<Contact>();
        var claims = new List<ERAClaimDto>();
        var mapping = new Dictionary<string, Guid>();

        for (var i = 0; i < uniqueContactsCount; i++)
        {
            var pcn = new Faker().Random.Long(1).ToString();

            var contact = new ContactFaker()
                .RuleFor(x => x.ProviderId, providerId)
                .Generate();

            var claim = new ERAClaimDtoFaker()
                .RuleFor(x => x.PCN, pcn)
                .Generate();

            contacts.Add(contact);
            claims.Add(claim);
            mapping.Add(pcn, contact.Id);
            contactRepositoryMock
                .Setup(x => x.Get(contact.Id, true))
                .ReturnsAsync(contact);

            insuranceClaimClientRepositoryMock
                .Setup(x => x.GetProviderIdByContact(contact.Id))
                .ReturnsAsync(providerId);
        }

        clearingHouseIdLookupRepositoryMock
            .Setup(x => x.Lookup(mapping.Keys.ToArray(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(mapping);

        var era = new ERADetailsResponseFaker(claims.ToArray()).Generate();

        var remittanceAdvice = new InsuranceRemittanceAdviceFaker(providerId)
            .RuleFor(x => x.NPINumber, era.ProviderNpi)
            .RuleFor(x => x.TaxId, era.ProviderTaxId)
            .Generate();

        insuranceRemittanceAdvicesRepositoryMock
            .Setup(x => x.Get(providerId, era.ProviderNpi, era.ProviderTaxId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(remittanceAdvice);

        var message = new ClaimMdProcessERAMessage
        {
            ERA = new ERAListItemDtoFaker()
                .RuleFor(x => x.ProviderNpi, era.ProviderNpi)
                .RuleFor(x => x.ProviderTaxId, era.ProviderTaxId)
                .Generate()
        };

        claimMdHttpClientMock
            .Setup(x => x.GetERADetails(It.IsAny<ERADetailsRequest>()))
            .ReturnsAsync(era);

        return (providerId, message, contacts.ToArray(), claims.ToArray(), mapping);
    }
}