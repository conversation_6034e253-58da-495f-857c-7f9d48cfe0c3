using System.Net;
using carepatron.core.Constants;
using ClaimMD.Module.Configuration;
using ClaimMD.Module.Services;
using FluentAssertions;
using Moq;
using Moq.Protected;
using Newtonsoft.Json;
using tests.common.Extensions;
using tests.common.Data.Fakers.ClaimMD;
using Xunit;
using ClaimMD.Module.Utilities;
using ClaimMD.Module.Models.Http.Requests;
using carepatron.core.Extensions;
using tests.common.Data.Fakers;
using carepatron.core.Utilities;

namespace ClaimMD.Module.Tests.Services;


[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class ClaimMdHttpClientTests
{
    private readonly HttpClient httpClient;
    private readonly Mock<IHttpClientFactory> httpClientFactoryMock;
    private readonly ClaimMdConfiguration claimMdConfiguration;
    private readonly Mock<HttpMessageHandler> httpMessageHandlerMock;

    
    public ClaimMdHttpClientTests()
    {
        claimMdConfiguration = new ClaimMdConfiguration()
        {
            ApiKey = "api-key",
            BaseApiUrl = "https://api.claimmd.com"
        };
        httpMessageHandlerMock = new Mock<HttpMessageHandler>();
        httpClient = new HttpClient(httpMessageHandlerMock.Object)
        {
            BaseAddress = new Uri(claimMdConfiguration.BaseApiUrl)
        };
        httpClientFactoryMock = new Mock<IHttpClientFactory>();
        httpClientFactoryMock.Setup(_ => _.CreateClient(It.IsAny<string>()))
            .Returns(() => httpClient);
    }

    [Fact]
    public async Task GetClaimStatusUpdates_ShouldSubmitCorrectPayload()
    {
        SetupHttpMessageHandler(JsonConvert.SerializeObject(new ClaimStatusUpdateResponseFaker().Generate()));
        var request = new ClaimStatusUpdateRequestFaker(Guid.NewGuid()).Generate();
        var payload = CreatePostData(new Dictionary<string, string>
        {
            { "ClaimID", request.ClaimId },
            { "ResponseID", request.ResponseId.ToString() }
        });
        var result = await new ClaimMdHttpClient(httpClient, claimMdConfiguration)
            .GetResponseRequest(request);

        result.Should().NotBeNull();

        var content = await new FormUrlEncodedContent(payload).ReadAsStringAsync();
        AssertCorrectArgumentsSubmitted("/services/response/", content);
    }

    [Fact]
    public async Task GetEligibility_ShouldSubmitCorrectPayload()
    {
        SetupHttpMessageHandler(JsonConvert.SerializeObject(new EligibilityRequestFaker().Generate()));
        var request = new EligibilityRequestFaker().Generate();
        var payload = CreatePostData(new Dictionary<string, string>
        {
            { "payerid", request.PayerId },
            { "pat_rel", request.PatientRelationship },
            { "ins_name_l", request.PolicyHolderLastName },
            { "ins_name_f", request.PolicyHolderFirstName },
            { "ins_name_m", request.PolicyHolderMiddleName },
            { "fdos", request.FDOS },
            { "prov_npi", request.ProviderNpi?.Replace("-", string.Empty) },
            { "service_code", string.Join(',', request.ServiceCodes ?? []) },
            { "proc_code", request.ProcCode },
            { "ins_number", request.InsuranceNumber },
            { "ins_dob", request.PolicyHolderDateOfBirth },
            { "ins_sex", ClaimMdMapper.ToSexString(request.PolicyHolderSex) },
            { "pat_name_l", request.PatientLastName },
            { "pat_name_f", request.PatientFirstName },
            { "pat_name_m", request.PatientMiddleName },
            { "pat_dob", request.PatientDateOfBirth },
            { "pat_sex", ClaimMdMapper.ToSexString(request.PatientSex) },
            { "prov_name_l", request.ProviderLastName },
            { "prov_name_f", request.ProviderFirstName },
            { "prov_taxonomy", request.ProviderTaxonomy },
            { "prov_taxid", request.ProviderTaxId?.Replace("-", string.Empty) },
            { "prov_taxid_type", request.ProviderTaxIdType },
            { "prov_addr_1", request.ProviderAddressLine1 },
            { "prov_city", request.ProviderCity },
            { "prov_state", request.ProviderState },
            { "prov_zip", request.ProviderZip }
        });
        var result = await new ClaimMdHttpClient(httpClient, claimMdConfiguration)
            .GetEligibilityRequest(request);

        result.Should().NotBeNull();

        var content = await new FormUrlEncodedContent(payload).ReadAsStringAsync();
        AssertCorrectArgumentsSubmitted("/services/eligdata/", content);
    }

    [Fact]
    public async Task GetERADetails_ShouldSubmitCorrectPayload()
    {
        SetupHttpMessageHandler(JsonConvert.SerializeObject(new ERADetailsResponseFaker().Generate()));
        var request = new ERADetailsRequestFaker().Generate();
        var payload = CreatePostData(new Dictionary<string, string>
        {
            { "eraid", request.EraId }
        });
        var result = await new ClaimMdHttpClient(httpClient, claimMdConfiguration)
            .GetERADetails(request);

        result.Should().NotBeNull();

        var content = await new FormUrlEncodedContent(payload).ReadAsStringAsync();
        AssertCorrectArgumentsSubmitted("/services/eradata/", content);
    }

    [Fact]
    public async Task GetERAList_ShouldSubmitCorrectPayload()
    { 
        SetupHttpMessageHandler(JsonConvert.SerializeObject(new ERAListResponseFaker().Generate()));
        var request = new ERAListRequestFaker().Generate();
        var payload = CreatePostData(new Dictionary<string, string>
        {
            { "ERAID", request.EraId.ToString() }
        });
        var result = await new ClaimMdHttpClient(httpClient, claimMdConfiguration)
            .GetERAList(request);

        result.Should().NotBeNull();

        var content = await new FormUrlEncodedContent(payload).ReadAsStringAsync();
        AssertCorrectArgumentsSubmitted("/services/eralist/", content);
    }

    [Fact]
    public async Task GetPayersList_ShouldSubmitCorrectPayload()
    {
        SetupHttpMessageHandler(JsonConvert.SerializeObject(new PayersListResponseFaker().Generate()));
        var request = new PayersListRequestFaker().Generate();
        var payload = CreatePostData(new Dictionary<string, string>
        {
            { "payerid", request.PayerId },
            { "payer_name", request.PayerName }
        });
        var result = await new ClaimMdHttpClient(httpClient, claimMdConfiguration)
            .GetPayersList(request);

        result.Should().NotBeNull();

        var content = await new FormUrlEncodedContent(payload).ReadAsStringAsync();
        AssertCorrectArgumentsSubmitted("/services/payerlist/", content);
    }

    [Fact]
    public async Task GetProviderEnrollmentStatus_ShouldSubmitCorrectPayload()
    {
        SetupHttpMessageHandler(JsonConvert.SerializeObject(new ProviderEnrollmentResponseFaker().Generate()));
        var request = new ProviderEnrollmentRequestFaker().Generate();
        var payload = CreatePostData(new Dictionary<string, string>
        {
            { "payerid", request.PayerId },
            { "enroll_type", request.EnrollmentType },
            { "prov_taxid", request.ProviderTaxId.CleanString() },
            { "prov_npi", request.ProviderNpi.CleanString() },
            { "prov_name_l", request.ProviderLastName },
            { "prov_name_f", request.ProviderFirstName },
            { "prov_name_m", request.ProviderMiddleName },
            { "prov_id", request.ClaimMdProviderId },
            { "prov_addr_1", request.ProviderAddressLine1 },
            { "prov_addr_2", request.ProviderAddressLine2 },
            { "prov_city", request.ProviderCity },
            { "prov_state", request.ProviderState },
            { "prov_zip", request.ProviderZip },
            { "contact", request.ContactName },
            { "contact_phone", request.ContactPhone },
            { "contact_email", request.ContactEmail },
            { "contact_fax", request.ContactFax },
            { "contact_title", request.ContactTitle }
        });
        var result = await new ClaimMdHttpClient(httpClient, claimMdConfiguration)
            .ProviderEnrollmentRequest(request);

        result.Should().NotBeNull();

        var content = await new FormUrlEncodedContent(payload).ReadAsStringAsync();
        AssertCorrectArgumentsSubmitted("/services/enroll/", content);
    }

    [Fact]
    public async Task UploadClaims_ShouldSubmitCorrectPayload()
    {
        SetupHttpMessageHandler(JsonConvert.SerializeObject(new ClaimUploadResponseFaker().Generate()));
        var serviceLines = new ClaimServiceLineFaker(Guid.NewGuid()).Generate(2);
        var claims = new USProfessionalClaimFaker(Guid.NewGuid())
            .WithServiceLines([.. serviceLines])
            .WithResubmissionCode("7")
            .Generate();
        var request = new ClaimUploadRequestFaker(Guid.NewGuid(), [claims]).Generate();
        var result = await new ClaimMdHttpClient(httpClient, claimMdConfiguration)
            .UploadBatchFiles(request);

        result.Should().NotBeNull();

        var mappedClaims = ClaimMdMapper.MapClaimDetailRequest([.. request.Claims]);
        var claimRequest = new ClaimUploadFileData
        {
            FileId = request.FileId,
            Claims = mappedClaims
        };
        var postData = new MultipartFormDataContent
        {
            { new StringContent(claimMdConfiguration.ApiKey), "AccountKey" },
            { new StringContent(JsonConvert.SerializeObject(claimRequest)), "File" },
            { new StringContent(request.FileName), "Filename" }
        };
        httpMessageHandlerMock.Protected().Verify(
            "SendAsync",
            Times.Once(),
            ItExpr.Is<HttpRequestMessage>(req => 
            ValidateClaimUploadRequest(
                req,
                "/services/upload/",
                claimRequest,
                request.FileName,
                claimMdConfiguration.ApiKey,
                request
            ).GetAwaiter().GetResult()
            ),
            ItExpr.IsAny<CancellationToken>()
        );
    }

    private void SetupHttpMessageHandler(string response)
    {
        httpMessageHandlerMock
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync((HttpRequestMessage request, CancellationToken token) =>
                new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(response)
                });
    }

    private void AssertCorrectArgumentsSubmitted(string url, string content)
    {
        httpMessageHandlerMock.Protected().Verify(
            "SendAsync",
            Times.Once(),
            ItExpr.Is<HttpRequestMessage>(req =>
                req.Method == HttpMethod.Post &&
                req.RequestUri != null && 
                req.RequestUri.AbsolutePath == url &&
                req.Content != null &&
                req.Content.ReadAsStringAsync().Result == content
            ),
            ItExpr.IsAny<CancellationToken>()
        );
    }

    private static List<KeyValuePair<string, string>> CreatePostData(Dictionary<string, string> parameters)
    {
        var postData = new List<KeyValuePair<string, string>>
        {
            new("AccountKey", "api-key")
        };

        foreach (var param in parameters)
        {
            postData.Add(new KeyValuePair<string, string>(param.Key, param.Value));
        }

        return postData;
    }

    private static async Task<bool> ValidateClaimUploadRequest(
        HttpRequestMessage request,
        string url,
        ClaimUploadFileData expectedClaimRequest,
        string expectedFileName,
        string expectedApiKey,
        ClaimUploadRequest actualRequest
    )
    {
        if (request.Method != HttpMethod.Post)
            return false;

        if (request.RequestUri == null || request.RequestUri.AbsolutePath != url)
            return false;

        if (request.Content == null || request.Content is not MultipartFormDataContent content)
            return false;

        var contentDict = await (request.Content as MultipartFormDataContent).ToDictionary();

        if (!contentDict.ContainsKey("AccountKey") || !contentDict.ContainsKey("File") || !contentDict.ContainsKey("Filename"))
            return false;

        if (contentDict["AccountKey"] != expectedApiKey)
            return false;

        if (contentDict["Filename"] != expectedFileName)
            return false;

        var actualClaimRequestJson = contentDict["File"];
        var expectedClaimRequestJson = JsonConvert.SerializeObject(expectedClaimRequest);
        
        if (actualClaimRequestJson != expectedClaimRequestJson)
            return false;

        var uploadedClaims = actualRequest.Claims;
        var parsedClaims = expectedClaimRequest.Claims;

        // Ensure claim service lines are submitted with RemoteChargeId as the service line ID
        var claimLineIds = uploadedClaims.SelectMany(c => c.ServiceLines.Select(s => Base36GuidEncoder.Encode(s.Id))).ToList();
        var chargeIds = parsedClaims.SelectMany(c => c.Charges.Select(ch => ch.RemoteChargeId)).ToList();
        if (claimLineIds.Count != chargeIds.Count) return false;
        foreach (var claimLineId in claimLineIds)
        {
            if (!chargeIds.Contains(claimLineId)) return false;
        }
        return true;
    }

}
