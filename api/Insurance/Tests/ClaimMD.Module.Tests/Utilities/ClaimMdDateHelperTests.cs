using System;
using Xunit;
using ClaimMD.Module.Utilities;

namespace ClaimMD.Module.Tests.Utilities
{
    public class ClaimMdDateHelperTests
    {
        [Fact]
        public void MountainDateTimeToUtc_String_ParsesAndConvertsCorrectly()
        {
            // 2023-10-01 10:30:00 PM Mountain Time
            string input = "2023-10-01 10:30:00PM";
            // 10:30 PM MDT is 04:30 AM UTC next day (MDT is UTC-6)
            var expectedUtc = new DateTime(2023, 10, 2, 4, 30, 0, DateTimeKind.Utc);

            var result = ClaimMdDateHelper.MountainDateTimeToUtc(input);

            Assert.Equal(expectedUtc, result);
            Assert.Equal(DateTimeKind.Utc, result.Kind);
        }

        [Fact]
        public void MountainDateOnlyToUtc_ConvertsMidnightMountainToUtc()
        {
            // 2023-10-01 midnight Mountain Time
            var dateOnly = new DateOnly(2023, 10, 1);
            // Midnight MDT is 6:00 AM UTC (MDT is UTC-6)
            var expectedUtc = new DateTime(2023, 10, 1, 6, 0, 0, DateTimeKind.Utc);

            var result = ClaimMdDateHelper.MountainDateOnlyToUtc(dateOnly);

            Assert.Equal(expectedUtc, result);
            Assert.Equal(DateTimeKind.Utc, result.Kind);
        }

        [Fact]
        public void MountainDateTimeToUtc_ConvertsMountainTimeToUtc()
        {
            // 2023-01-01 12:00:00 Mountain Time (MST is UTC-7)
            var mountainTime = new DateTime(2023, 1, 1, 12, 0, 0, DateTimeKind.Unspecified);
            var expectedUtc = new DateTime(2023, 1, 1, 19, 0, 0, DateTimeKind.Utc);

            var result = ClaimMdDateHelper.MountainDateTimeToUtc(mountainTime);

            Assert.Equal(expectedUtc, result);
            Assert.Equal(DateTimeKind.Utc, result.Kind);
        }

        [Fact]
        public void MountainDateTimeToUtc_String_ThrowsOnInvalidFormat()
        {
            string invalidInput = "2023/10/01 10:30:00PM";
            Assert.Throws<FormatException>(() =>
                ClaimMdDateHelper.MountainDateTimeToUtc(invalidInput)
            );
        }
    }
}