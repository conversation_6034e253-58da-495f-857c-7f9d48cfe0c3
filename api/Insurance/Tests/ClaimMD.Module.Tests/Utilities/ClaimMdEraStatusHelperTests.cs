using System;
using Xunit;
using ClaimMD.Module.Utilities;
using static ClaimMD.Module.Constants;
using carepatron.core.Application.Insurance.Models;
using tests.common.Data.Fakers.ClaimMD;
using tests.common.Data.Fakers;
using Moq;
using carepatron.core.Application.Insurance.Services;
using carepatron.core.Application.Payments.Models;
using FluentAssertions;

namespace ClaimMD.Module.Tests.Utilities;

public class ClaimMdEraStatusHelperTests
{
    private Mock<IInsuranceService> _insuranceServiceMock;

    public ClaimMdEraStatusHelperTests()
    {
        _insuranceServiceMock = new Mock<IInsuranceService>();
        _insuranceServiceMock
            .Setup(x => x.GetClaimStatusByPayments(It.IsAny<InsuranceClaimUSProfessional>(), It.IsAny<PaymentAllocation[]>()))
            .Returns(ClaimStatus.Paid);
    }

    [Theory]
    [InlineData("1", "0.00", ClaimStatus.Accepted)]
    [InlineData("2", "100.00", ClaimStatus.Paid)]
    [InlineData("3", "50.00", ClaimStatus.Paid)]
    [InlineData("4", "0.00", ClaimStatus.Denied)]
    [InlineData("5", "0.00", ClaimStatus.Accepted)]
    [InlineData("6", "100.00", ClaimStatus.Paid)]
    [InlineData("7", "0.00", ClaimStatus.Accepted)]
    [InlineData("8", "0.00", ClaimStatus.Closed)]
    [InlineData("9", "0.00", ClaimStatus.Accepted)]
    [InlineData("10", "0.00", ClaimStatus.Accepted)]
    [InlineData("11", "0.00", ClaimStatus.Rejected)]
    [InlineData("12", "0.00", ClaimStatus.Rejected)]
    [InlineData("13", "0.00", ClaimStatus.Rejected)]
    [InlineData("14", "0.00", ClaimStatus.Rejected)]
    [InlineData("15", "0.00", ClaimStatus.Accepted)]
    [InlineData("16", "0.00", ClaimStatus.Rejected)]
    [InlineData("17", "0.00", ClaimStatus.Rejected)]
    [InlineData("18", "0.00", ClaimStatus.Rejected)]
    [InlineData("19", "0.00", ClaimStatus.Accepted)]
    [InlineData("20", "0.00", ClaimStatus.Accepted)]
    [InlineData("21", "100.00", ClaimStatus.Paid)]
    [InlineData("22", "0.00", ClaimStatus.Submitted)]
    [InlineData("23", "0.00", ClaimStatus.Rejected)]
    [InlineData("24", "0.00", ClaimStatus.Rejected)]
    [InlineData("25", "0.00", ClaimStatus.Accepted)]
    [InlineData("26", "0.00", ClaimStatus.Accepted)]
    [InlineData("27", "0.00", ClaimStatus.Accepted)]
    [InlineData("28", "0.00", ClaimStatus.Accepted)]
    [InlineData("29", "100.00", ClaimStatus.Paid)]
    [InlineData("30", "0.00", ClaimStatus.Accepted)]
    [InlineData("31", "0.00", ClaimStatus.Rejected)]
    [InlineData("AD", "0.00", ClaimStatus.Submitted)]
    [InlineData("AP", "0.00", ClaimStatus.Submitted)]
    [InlineData("CC", "0.00", ClaimStatus.Submitted)]
    [InlineData("CL", "0.00", ClaimStatus.Closed)]
    [InlineData("CP", "0.00", ClaimStatus.Submitted)]
    [InlineData("I", "0.00", ClaimStatus.Submitted)]
    [InlineData("RA", "0.00", ClaimStatus.Submitted)]
    [InlineData("RB", "0.00", ClaimStatus.Submitted)]
    [InlineData("RC", "0.00", ClaimStatus.Submitted)]
    [InlineData("RD", "0.00", ClaimStatus.Submitted)]
    [InlineData("RO", "0.00", ClaimStatus.Submitted)]
    [InlineData(null, "0.00", ClaimStatus.Submitted)]
    public void ResolveClaimStatus_ShouldGetCorrectClaimStatus(
        string? eraClaimStatusCode,
        string totalPaid,
        ClaimStatus claimStatus
    )
    {
        var providerId = Guid.NewGuid();
        var eraClaim = new ERAClaimDtoFaker()
            .RuleFor(x => x.StatusCode, eraClaimStatusCode)
            .RuleFor(x => x.TotalPaid, totalPaid)
            .Generate();
        var internalClaim = new USProfessionalClaimFaker(providerId)
            .RuleFor(x => x.Status, ClaimStatus.Submitted)
            .Generate();

        var result = ClaimMdEraStatusHelper.ResolveClaimStatus(
            eraClaim,
            internalClaim,
            [],
            _insuranceServiceMock.Object
        );

        result.Should().NotBeNull();
        result.Status.Should().Be(claimStatus);
        if (string.IsNullOrEmpty(eraClaimStatusCode))
        {
            eraClaimStatusCode.Should().BeNullOrEmpty();
        }
        else
        {
            result.StatusReason.Should().Be(ERAClaimStatusCode.Descriptions[eraClaimStatusCode]);
        }
    }
}
