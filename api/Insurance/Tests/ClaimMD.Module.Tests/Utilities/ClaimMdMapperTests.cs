using Bogus;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Utilities;
using ClaimMD.Module.Models.Http.Dtos;
using ClaimMD.Module.Utilities;
using FluentAssertions;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using Xunit;

namespace ClaimMD.Module.Tests.Utilities;

public class ClaimMdMapperTests
{
    [Fact]
    public void MapClaimDetailRequest_ShouldMapCorrectly()
    {
        // Arrange
        var claim = GenerateClaim();

        // Act
        var result = ClaimMdMapper.MapClaimDetailRequest([claim]);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(1);
        var mappedClaim = result[0];
        ValidateClaim(mappedClaim, claim);
    }
    
    [Fact]
    public void MapClaimDetailRequest_ShouldSetResubmissionCodeAndOriginalReferenceIfClaimHasValues()
    {
        // Arrange
        var faker = new Faker();
        var claim = GenerateClaim();
        claim.OriginalReferenceNumber = faker.Random.AlphaNumeric(10);
        claim.ResubmissionCode = "7";

        // Act
        var result = ClaimMdMapper.MapClaimDetailRequest([claim]);

        // Assert
        result.Should().NotBeNull();
        result.Count.Should().Be(1);
        var mappedClaim = result[0];
        ValidateClaim(mappedClaim, claim);
    }

    private void ValidateClaim(UploadedClaimDto mappedClaim, InsuranceClaimUSProfessional claim)
    {
        // Claim details
        mappedClaim.AcceptAssign.Should().Be("Y");
        mappedClaim.BalanceDue.Should().Be(claim.BalanceDue.ToString("0.00"));
        mappedClaim.ClaimForm.Should().Be("1500");
        mappedClaim.PCN.Should().Be(claim.ClientControlNumber);
        mappedClaim.RemoteClaimId.Should().Be(Base36GuidEncoder.Encode(claim.Id));
        mappedClaim.TotalCharge.Should().Be(claim.Amount.ToString("0.00"));

        if (!string.IsNullOrEmpty(claim.OriginalReferenceNumber))
        {
            mappedClaim.OriginalReferenceNumber.Should().Be(claim.OriginalReferenceNumber);
            mappedClaim.ResubmissionCode.Should().Be(claim.ResubmissionCode);
        }
        else
        {
            mappedClaim.OriginalReferenceNumber.Should().BeEmpty();
            mappedClaim.ResubmissionCode.Should().BeEmpty();
        }

        // Patient info
        mappedClaim.PatientFirstName.Should().Be(claim.Client.FirstName);
        mappedClaim.PatientLastName.Should().Be(claim.Client.LastName);
        mappedClaim.PatientSex.Should().Be(ClaimMdMapper.ToSexString(claim.Client.Sex));
        mappedClaim.PatientDateOfBirth.Should().Be(
            claim.Client.DateOfBirth.HasValue ? claim.Client.DateOfBirth.Value.ToString("yyyy-MM-dd") : string.Empty
        );
        mappedClaim.PatientAddress1.Should().Be(claim.Client.Address?.StreetAddress);
        mappedClaim.PatientCity.Should().Be(claim.Client.Address?.City);
        mappedClaim.PatientState.Should().Be(AddressUtilities.GetUSState(claim.Client.Address?.State));
        mappedClaim.PatientZip.Should().Be(claim.Client.Address?.ZipCode);
        // Incident info
        mappedClaim.AutoAccident.Should().Be(ClaimMdMapper.ToYesNo(claim.Incident.IsConditionRelatedToAutoAccident));
        mappedClaim.EmploymentRelated.Should().Be(ClaimMdMapper.ToYesNo(claim.Incident.IsConditionRelatedToEmployment));
        // Billing info
        mappedClaim.BillingState.Should().Be(AddressUtilities.GetUSState(claim.BillingDetail.Address.State));
        mappedClaim.BillingNpi.Should().Be(claim.BillingDetail.NationalProviderId);
        mappedClaim.BillingPhone.Should().Be(claim.BillingDetail.PhoneNumber?.Replace(claim.BillingDetail.PhoneCountryCode, string.Empty));
        mappedClaim.BillTaxId.Should().Be(claim.BillingDetail.TaxNumber);
        mappedClaim.BillingTaxIdType.Should().Be(ClaimMdMapper.GetTaxType(claim.BillingDetail.TaxNumberType));
        mappedClaim.ProviderTaxonomy.Should().Be(claim.BillingDetail.TaxonomyCode);
        mappedClaim.BillingName.Should().Be(claim.BillingDetail.Name);
        mappedClaim.BillingAddress1.Should().Be(claim.BillingDetail.Address.StreetAddress);
        mappedClaim.BillingCity.Should().Be(claim.BillingDetail.Address.City);
        mappedClaim.BillingZip.Should().Be(claim.BillingDetail.Address.ZipCode);
        // Insurance info
        mappedClaim.PayerId.Should().Be(claim.ContactInsurancePolicy.PayerNumber);
        mappedClaim.PolicyHolderGroup.Should().Be(claim.ContactInsurancePolicy.PolicyHolderGroupId);
        mappedClaim.PolicyHolderFirstName.Should().Be(claim.ContactInsurancePolicy.PolicyHolderFirstName);
        mappedClaim.PolicyHolderLastName.Should().Be(claim.ContactInsurancePolicy.PolicyHolderLastName);
        mappedClaim.PolicyHolderSex.Should().Be(ClaimMdMapper.ToSexString(claim.ContactInsurancePolicy.PolicyHolderSex));
        mappedClaim.InsuranceNumber.Should().Be(claim.ContactInsurancePolicy.PolicyHolderMemberId);
        mappedClaim.PatientRelationship.Should().Be(ClaimMdMapper.GetClientRelationship(claim.ContactInsurancePolicy.PolicyHolderRelationshipType));
        mappedClaim.PolicyHolderDateOfBirth.Should().Be(
            claim.ContactInsurancePolicy.PolicyHolderDateOfBirth.HasValue
                ? claim.ContactInsurancePolicy.PolicyHolderDateOfBirth.Value.ToString("yyyy-MM-dd")
                : string.Empty
        );
        mappedClaim.PolicyHolderAddress1.Should().Be(claim.ContactInsurancePolicy.PolicyHolderAddress.StreetAddress);
        mappedClaim.PolicyHolderCity.Should().Be(claim.ContactInsurancePolicy.PolicyHolderAddress.City);
        mappedClaim.PolicyHolderState.Should().Be(AddressUtilities.GetUSState(claim.ContactInsurancePolicy.PolicyHolderAddress.State));
        mappedClaim.PolicyHolderZip.Should().Be(claim.ContactInsurancePolicy.PolicyHolderAddress.ZipCode);
        // Diagnosis codes
        for (var i = 0; i < claim.DiagnosticCodes.Length; i++)
        {
            var code = claim.DiagnosticCodes[i].Code;
            switch (i)
            {
                case 0:
                    mappedClaim.Diagnosis1.Should().Be(code);
                    break;
                case 1:
                    mappedClaim.Diagnosis2.Should().Be(code);
                    break;
                case 2:
                    mappedClaim.Diagnosis3.Should().Be(code);
                    break;
                case 3:
                    mappedClaim.Diagnosis4.Should().Be(code);
                    break;
                case 4:
                    mappedClaim.Diagnosis5.Should().Be(code);
                    break;
                case 5:
                    mappedClaim.Diagnosis6.Should().Be(code);
                    break;
                case 6:
                    mappedClaim.Diagnosis7.Should().Be(code);
                    break;
                case 7:
                    mappedClaim.Diagnosis8.Should().Be(code);
                    break;
                case 8:
                    mappedClaim.Diagnosis9.Should().Be(code);
                    break;
                case 9:
                    mappedClaim.Diagnosis10.Should().Be(code);
                    break;
                case 10:
                    mappedClaim.Diagnosis11.Should().Be(code);
                    break;
                case 11:
                    mappedClaim.Diagnosis12.Should().Be(code);
                    break;
            }
        }
        // Service lines
        for (int i = 0; i < claim.ServiceLines.Length; i++)
        {
            var expected = claim.ServiceLines[i];
            var actual = mappedClaim.Charges[i];

            actual.Charge.Should().Be(expected.Amount.ToString("0.00"));
            actual.FromDate.Should().Be(expected.Date.ToString("yyyy-MM-dd"));
            actual.ThruDate.Should().Be(expected.Date.ToString("yyyy-MM-dd"));
            actual.ChargeRecordType.Should().Be("UN");
            actual.PlaceOfService.Should().Be(expected.POSCode);
            actual.ProcedureCode.Should().Be(expected.Code);
            actual.Units.Should().Be(expected.Units.ToString());
            actual.RemoteChargeId.Should().Be(Base36GuidEncoder.Encode(expected.Id));

            // DiagnosisReference – using the same logic as mapping
            var expectedDiagnosisRef = string.Join(
                "",
                claim.DiagnosticCodes
                    .Select((diag, index) => new { diag.Code, Letter = ((char)('A' + index)).ToString() })
                    .Where(x => expected.DiagnosticCodeReferences.Contains(x.Code))
                    .Select(x => x.Letter)
            );
            actual.DiagnosisReference.Should().Be(expectedDiagnosisRef);
        }
        // Referring provider info
        mappedClaim.ReferrerFirstName.Should().Be(claim.ReferringProviders[0].FirstName);
        mappedClaim.ReferrerLastName.Should().Be(claim.ReferringProviders[0].LastName);
        mappedClaim.ReferrerMiddleName.Should().Be(claim.ReferringProviders[0].MiddleName);
        mappedClaim.ReferrerNpi.Should().Be(claim.ReferringProviders[0].NationalProviderId);
        // Rendering provider info
        mappedClaim.ProviderFirstName.Should().Be(claim.RenderingProviders[0].FirstName);
        mappedClaim.ProviderLastName.Should().Be(claim.RenderingProviders[0].LastName);
        mappedClaim.ProviderMiddleName.Should().Be(claim.RenderingProviders[0].MiddleName);
        mappedClaim.ProviderNpi.Should().Be(claim.RenderingProviders[0].NationalProviderId);
    }

    private static InsuranceClaimUSProfessional GenerateClaim()
    {
        var faker = new Faker();
        var providerId = Guid.NewGuid();
        var contact = new ContactFaker(providerId).Generate();
        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(providerId, staffPerson).Generate();
        var providerInsurancePayer = new ProviderInsurancePayerFaker(providerId).Generate();
        var payer = new InsurancePayerFaker()
            .RuleFor(x => x.Name, providerInsurancePayer.Name)
            .RuleFor(x => x.PayerId, providerInsurancePayer.PayerId)
            .Generate();
        var policy = new ContactInsurancePolicyFaker(providerId, contact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = contact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();

        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(providerId, contact.Id).Generate()
        };
        var defaultBillingProfile = new ProviderBillingProfileFaker(providerId).Generate();
        defaultBillingProfile.IsDefault = true;
        var claim = new USProfessionalClaimFaker(providerId)
            .WithBillingDetail(providerId, defaultBillingProfile)
            .WithClient(providerId, contact)
            .WithContactInsurancePolicy(providerId, contact.Id, policy)
            .WithDiagnosticCodes(providerId)
            .WithFacility(providerId)
            .WithIncident(providerId)
            .WithReferringProviders(providerId)
            .WithServiceLines([.. serviceLines])
            .WithRenderingProviders(providerId, staffMember)
            .Generate();
        return claim;
    }
}