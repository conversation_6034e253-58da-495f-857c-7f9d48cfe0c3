using System.Text.Json;
using Amazon.SQS.Model;
using Bogus;
using carepatron.core.Application.ClearingHouse.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Constants;
using carepatron.infra.sql.Models.ClearingHouse;
using ClaimMD.Module;
using ClaimMD.Module.Messages;
using ClaimMD.Module.Models.Http.Dtos;
using ClaimMD.Module.Models.Http.Requests;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Moq;
using Newtonsoft.Json;
using tests.common.Data.Fakers.ClaimMD;
using tests.common.Fixtures;
using tests.common.Mocks;
using tests.component.worker.insurance.Setup;
using Xunit;

namespace ClaimMonitor.Worker.Tests;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class ClaimMdClaimMonitorTests(ClaimMonitorWorkerFixture fixture) : WorkerComponentTestBase<ClaimMonitorWorkerFixture>(fixture)
{
    [Fact]
    public async Task ClaimResponse_ShouldQueueClaimStatusMessageForProcessing()
    {
        var faker = new Faker();
        var responseId = faker.Random.Int(1000000, 10000000);
        var contactId = Guid.NewGuid();
        await SetupClaimMdSettings(responseId);

        // todo, get a realis responses in here.
        var messages1 = new ClaimMessageDtoFaker()
            .RuleFor(x => x.Status, "A")
            .Generate(1);
        var claim1Id = Guid.NewGuid();
        var claim1 = new StatusUpdateClaimDtoFaker(claimId: claim1Id)
            .RuleFor(x => x.Status, "A")
            .RuleFor(x => x.SenderId, Constants.ClaimMdSenderId)
            .RuleFor(x => x.Messages, f => messages1)
            .Generate();
        var messages2 = new ClaimMessageDtoFaker()
            .RuleFor(x => x.Status, "R")
            .Generate(1);
        var claim2Id = Guid.NewGuid();
        var claim2 = new StatusUpdateClaimDtoFaker(claimId: claim2Id)
            .RuleFor(x => x.Status, "R")
            .RuleFor(x => x.SenderId, Constants.ClaimMdSenderId)
            .RuleFor(x => x.Messages, f => messages2)
            .Generate();
        var claims = new[] { claim1, claim2 };
        var claimMdResponse = new ClaimStatusUpdateResponseFaker(claims)
            .RuleFor(x => x.LastResponseId, responseId)
            .Generate();

        var httpClientMock = ResolveService<ClaimMdHttpClientMock>();
        httpClientMock.Setup(s => s.GetResponseRequest(It.IsAny<ClaimStatusUpdateRequest>()))
            .ReturnsAsync(claimMdResponse);

        // Act
        await Fixture.Run<ClaimMdClaimMonitorWorkerExecution>();

        Fixture.AwsSqsClient.ReceivedMessages.Any(s => ValidateMessage(s, claims)).Should().BeTrue();

        var dbSettings = await DataContext.ClearingHouseClaimMdSettings
            .AsNoTracking()
            .FirstAsync(x => x.ProviderId == null);
        dbSettings.LastResponseId.Should().Be(claimMdResponse.LastResponseId);

        var externalIds = claims.Select(x => x.GetExternalId()).ToArray();
        var dbResponseLogs = await DataContext.ClearingHouseLogs
            .AsNoTracking()
            .Where(x => externalIds.Contains(x.ExternalId))
            .ToArrayAsync();
        dbResponseLogs.Should().NotBeEmpty().And.AllSatisfy(x =>
        {
            x.Status.Should().Be(ClearingHouseLogStatus.Pending);
            x.ClearingHouse.Should().Be(ClearingHouseType.ManagedClaimMd);
            x.Type.Should().Be(ClearingHouseLogType.Claim);
            x.RawResponse.Should().NotBeNull();
        });
    }

    private async Task SetupClaimMdSettings(int lastResponseId)
    {
        var defaultSettings = await DataContext.ClearingHouseClaimMdSettings
            .FirstOrDefaultAsync(x => x.ProviderId == null);
        if (defaultSettings is not null)
        {
            defaultSettings.LastResponseId = lastResponseId;
            await DataContext.SaveChangesAsync();
            return;
        }

        DataContext.ClearingHouseClaimMdSettings.Add(new ClearingHouseClaimMdSettingsDataModel
        {
            Id = Guid.NewGuid(),
            ProviderId = null,
            LastResponseId = lastResponseId,
            LastEraId = 0,
        });
        await DataContext.SaveChangesAsync();
    }

    private bool ValidateMessage(Message message, StatusUpdateClaimDto[] claims)
    {
        var json = JsonDocument.Parse(message.Body).RootElement;
        var messageType = json.GetProperty("messageType").GetString();
        if (messageType != typeof(ClaimMdClaimStatusMessage).Name) return false;
        var payloadJson = json.GetProperty("payload").ToString();
        var claimMessage = JsonConvert.DeserializeObject<ClaimMdClaimStatusMessage>(payloadJson);
        if (claimMessage is null) return false;
        var claim = claims.FirstOrDefault(x => x.ClaimId == claimMessage.ClaimUpdate.ClaimId);
        return claim is not null;
    }
}
