using System.Diagnostics;
using System.Text.Json;
using carepatron.core.Abstractions;
using carepatron.core.Application.ClearingHouse.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Repositories.ClearingHouse;
using carepatron.workers.common;
using ClaimMD.Module.Interfaces;
using ClaimMD.Module.Messages;
using ClaimMD.Module.Models.Http.Requests;
using ClaimMD.Module.Queues;
using Messaging.Models;
using Messaging.Services;
using Newtonsoft.Json;
using Serilog;
using Serilog.Context;

namespace ClaimMonitor.Worker;

public class ClaimMdClaimMonitorWorkerExecution(
    IClearingHouseClaimMdSettingsRepository clearingHouseClaimMdSettingsRepository,
    IClearingHouseLogRepository clearingHouseLogRepository,
    IClaimMdHttpClient claimMdHttpClient,
    IMessageSender<ClaimMdMessageQueue> messagePublisher,
    IUnitOfWork unitOfWork
) : IWorkerExecution
{
    public async Task Execute(CancellationToken stoppingToken)
    {
        using var activity = Telemetry.ClaimMonitorActivitySource.StartActivity(
            name: "Claim MD Claim Monitor",
            kind: ActivityKind.Internal
        );

        // Get all claim md settings (for now this is just one setting until providers can onboard their own ClaimMD accounts)
        // @TODO: for multiple accounts
        // - processing in batches to avoid pulling all settings into memory at once
        // - only processing 'active' providers who have submitted claims.
        var settings = await clearingHouseClaimMdSettingsRepository.Get();
        if (settings is null || settings.Length == 0)
        {
            throw new InvalidOperationException("No ClaimMD settings found.");
        }

        foreach (var setting in settings)
        {
            if (stoppingToken.IsCancellationRequested)
            {
                Log.Information("Cancellation requested, stopping Claim MD monitor.");
                break;
            }
            using (LogContext.PushProperty("Settings", new
            {
                setting.Id,
                setting.ProviderId,
                setting.LastResponseId,
                setting.LastEraId
            }, true))
            {
                try
                {
                    // Fetch claim updates from the clearing house
                    Log.Information("Processing claim responses");
                    await ProcessClaimResponses(setting, stoppingToken);
                    Log.Information("Processing claim responses complete");
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error processing claim updates");
                }
            }
        }
    }

    private async Task ProcessClaimResponses(ClearingHouseClaimMdSettings settings, CancellationToken cancellationToken)
    {
        unitOfWork.UseUnitOfWork();

        var httpReq = new ClaimStatusUpdateRequest { ResponseId = settings.LastResponseId };
        var claimResponses = await claimMdHttpClient.GetResponseRequest(httpReq);

        if (claimResponses?.Claims is null || claimResponses.Claims.Count == 0)
        {
            Log.Information("No claim status updates found");
            return;
        }
        
        // Save all responses first
        await clearingHouseLogRepository.BulkUpsert(
            ClearingHouseType.ManagedClaimMd,
            ClearingHouseLogType.Claim,
            claimResponses.Claims.Select(claim => ClearingHouseLog.Create(
                ClearingHouseType.ManagedClaimMd,
                ClearingHouseLogStatus.Pending,
                ClearingHouseLogType.Claim,
                JsonDocument.Parse(JsonConvert.SerializeObject(claim)),
                claim.GetExternalId()
            )).ToArray(),
            cancellationToken
        );
        
        // Ensure clearinghouse logs are saved before dispatching messages
        Log.Information("Saving clearinghouse logs");
        
        await unitOfWork.SaveUnitOfWork(cancellationToken);
        
        unitOfWork.UseUnitOfWork();

        Log.Information("Dispatching claim status messages");
        
        // Process each claim status update
        var messages = claimResponses.Claims.Select(claim => new SendRequest(
            new ClaimMdClaimStatusMessage { ClaimUpdate = claim },
            deduplicationId: claim.GetExternalId(),
            messageGroupId: claim.ClaimMdId
        )).ToArray();
        var results = await messagePublisher.SendBatch(messages);

        if (results.Any(s => !s.IsSuccess))
        {
            throw new ApplicationException("Failed to send claim messages");
        }

        await clearingHouseClaimMdSettingsRepository.UpdateLastResponseId(
            settings.ProviderId,
            claimResponses.LastResponseId,
            cancellationToken
        );

        await unitOfWork.SaveUnitOfWork(cancellationToken);
    }
}
