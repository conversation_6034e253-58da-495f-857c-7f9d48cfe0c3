﻿using Agents.Module;
using Agents.Module.AgentRunners;
using Agents.Module.Agents;
using Agents.Module.Exceptions;
using Agents.Sdk.Types;
using carepatron.core.Abstractions;
using carepatron.core.Application.FeatureModules.Abstractions;
using carepatron.core.Application.FeatureModules.Models;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Notes.Models;
using carepatron.core.Application.Transcriptions.Models;
using carepatron.core.Common;
using carepatron.core.Constants;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Models.Notes;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Models.Storage;
using carepatron.core.Repositories.Notes;
using carepatron.core.Repositories.SQS;
using carepatron.core.Services;
using FileUtilities;
using MediatR;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Context;
using Shared.Module.Execution;
using Shared.Module.Pipeline.Abstractions;
using System.Diagnostics;
using System.Text.RegularExpressions;
using TranscriptionType = carepatron.core.Application.Transcriptions.Models.TranscriptionType;

namespace Transcriptions.Worker.Module.Application.Commands;

public class TranscribeAudioFilesCommand
{
    public record Request(
        Guid ProviderId,
        Guid InitiatorPersonId,
        string FolderPath,
        StorageProvider StorageProvider,
        string AudioFileMimeType,
        string AudioFileFormat,
        Guid[] ClientIds,
        TranscriptionType TranscriptionType,
        FileLocationType FileLocationType,
        Guid[] NoteIds,
        Guid? TaskId = null,
        Dictionary<string, string> LogMetadata = null,
        string LastProcessedFile = null,
        Guid? TranscriptionId = null,
        int PartNumber = 0,
        string StartTime = null,
        int ScheduleRunCount = 0,
        int FileCountToProcess = 12,
        AgentProvider AgentProvider = AgentProvider.Google) : ICommand<Unit>;

    public class Handler(
        ILogger<Handler> logger,
        ITranscriptionRepository transcriptionRepository,
        IAudioProcessingService audioProcessingService,
        IAgentClient agentClient,
        ISqsRepository sqsRepository,
        IDateTimeProvider dateTimeProvider,
        INoteRepository noteRepository,
        IFeatureService featureService,
        INoteTranscriptionRepository noteTranscriptionRepository,
        FileStorageServiceFactory fileStorageServiceFactory,
        IFeatureModuleService featureModuleService) : ICommandHandler<Request, Unit>
    {
        private const int MaxScheduledRunCountWithoutFiles = 1;

        public async Task<ExecutionResult<Unit>> Handle(Request request, CancellationToken cancellationToken)
        {
            var stopwatch = Stopwatch.StartNew();

            var startTime = string.IsNullOrEmpty(request.StartTime) ? "00:00:00" : request.StartTime;

            using (LogContext.PushProperty("TranscriptionStartTime", startTime))
            using (LogContext.PushProperty("Metadata", request.LogMetadata ?? new Dictionary<string, string>(),
                       destructureObjects: true))
            using (LogContext.PushProperty("FolderPath", request.FolderPath))
            using (LogContext.PushProperty("ProviderId", request.ProviderId))
            using (LogContext.PushProperty("TranscriptionId", request.TranscriptionId))
            using (LogContext.PushProperty("PartNumber", request.PartNumber))
            using (LogContext.PushProperty("TranscriptionType", request.TranscriptionType))
            using (LogContext.PushProperty("FileCountToProcess", request.FileCountToProcess))
            using (LogContext.PushProperty("StorageProvider", request.StorageProvider))
            using (LogContext.PushProperty("InitiatorPersonId", request.InitiatorPersonId))
            using (LogContext.PushProperty("NoteIds", request.NoteIds))
            {
                Transcription transcription = null;

                try
                {
                    if (!request.TranscriptionId.HasValue)
                    {
                        await HandleNewTranscription(request);
                    }
                    else
                    {
                        transcription = await transcriptionRepository.GetById(request.TranscriptionId.Value);
                        if (string.IsNullOrEmpty(request.LastProcessedFile))
                        {
                            await HandleExistingTranscriptionWithoutProcessedFile(request, transcription, startTime);
                        }
                        else
                        {
                            await HandleExistingTranscriptionWithProcessedFile(request, transcription, startTime);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "TranscriptionProcess: Error occurred processing call {FolderPath} transcription", request.FolderPath);

                    if (transcription == null) throw;

                    transcription.RetryCount++;

                    if (transcription.RetryCount >= 5)
                    {
                        transcription.Status = TranscriptionStatus.Failed;
                        await transcriptionRepository.Update(transcription);

                        LogContext.PushProperty("TranscriptionId", transcription.Id);
                        LogContext.PushProperty("FailedReason", "Transcription max retry count reached.");
                        Log.Error("TranscriptionProcess: Error processing transcription");

                        return Unit.Value;
                    }

                    await transcriptionRepository.Update(transcription);

                    // retry the transcription immediately and skip the visibility timeout of SQS
                    await SendNextSchedule(request, request.LastProcessedFile, request.PartNumber, request.StartTime,
                        request.ScheduleRunCount + 1, true);
                }
                finally
                {
                    Log.ForContext("ElapsedMilliseconds", stopwatch.ElapsedMilliseconds)
                        .Information("TranscriptionProcess: Transcription process completed");
                }
            }

            return Unit.Value;
        }

        private async Task HandleNewTranscription(Request request)
        {
            var initializeTranscriptionData = await InitializeTranscription(request);

            await SendNextSchedule(initializeTranscriptionData, null, 0, null, 0, false);
        }

        private async Task<string> GetAgentModel(Guid providerId)
        {
            var useAgentModelGeminiFlash25Preview = await featureService.IsEnabled(FeatureFlags.UseGeminiFlash25PreviewForTranscription, providerId);
            var model = useAgentModelGeminiFlash25Preview ? AgentModels.MultiModal.Google.GeminiFlash25_Preview : AgentModels.MultiModal.Google.GeminiFlash20_001;
            return model;
        }

        private async Task HandleExistingTranscriptionWithoutProcessedFile(Request request, Transcription transcription,
            string startTime)
        {
            var (files, hasMoreBatches) = await GetFilesAfterKey(transcription.Path, request.FileCountToProcess,
                request.StorageProvider, request.FileLocationType);

            if (request.ScheduleRunCount >= MaxScheduledRunCountWithoutFiles && !files.Any())
            {
                await CompleteTranscription(request.TranscriptionId.Value, request.NoteIds, request.InitiatorPersonId,
                    request.ProviderId);
                return;
            }

            if (!files.Any())
            {
                await SendNextSchedule(request, null, 0, null, request.ScheduleRunCount + 1, hasMoreBatches);
                return;
            }

            await ProcessFiles(request, transcription, request.FolderPath, files, 0, startTime, hasMoreBatches);
        }

        private async Task HandleExistingTranscriptionWithProcessedFile(Request request, Transcription transcription,
            string startTime)
        {
            var (files, hasMoreBatches) = await GetFilesAfterKey(transcription.Path, request.FileCountToProcess,
                request.StorageProvider, request.FileLocationType, request.LastProcessedFile);

            if (files.Any())
            {
                await ProcessFiles(request, transcription, request.FolderPath, files, request.PartNumber, startTime,
                    hasMoreBatches);
            }
            else
            {
                await CompleteTranscription(transcription.Id, request.NoteIds, request.InitiatorPersonId,
                    request.ProviderId);
            }
        }

        private async Task ProcessFiles(Request request, Transcription transcription, string folderPath,
            string[] fileKeys, int partNumber, string startTime, bool hasMoreBatches)
        {
            var fileStreams = await GetFileStreams(fileKeys, request.StorageProvider, request.FileLocationType);

            byte[] audioBytes = default;
            TimeSpan duration;
            string newStartTime = null;

            var result =
                await audioProcessingService.ConvertStreamsToBytesWithDuration(fileStreams, request.AudioFileFormat);

            // dispose all streams
            fileStreams.ToList().ForEach(stream => stream.Dispose());

            // If the Duration is Zero, the file might be corrupted. Skip the file on this scenario
            if (result.Duration == TimeSpan.Zero)
            {
                await SendNextSchedule(request, fileKeys.Last(), partNumber + 1, startTime, 0, hasMoreBatches);
                return;
            }

            audioBytes = result.AudioBytes;
            duration = result.Duration;

            newStartTime = CalculateNewStartTime(startTime, duration);

            if (TimeSpan.Parse(newStartTime) > TimeSpan.FromHours(2))
            {
                transcription.Status = TranscriptionStatus.Failed;
                transcription.FailedReason = TranscriptionFailedReason.InvalidFileDuration;
                await transcriptionRepository.Update(transcription);

                LogContext.PushProperty("TranscriptionId", transcription.Id);
                LogContext.PushProperty("FailedReason", transcription.FailedReason.ToString());
                Log.Error("TranscriptionProcess: Error processing transcription");

                return;
            }

            var model = await GetAgentModel(request.ProviderId);

            var response = await agentClient.Run<TranscriptionAgent.TimestampOutputResponse>(
                new TranscriptionAgent(Agents.Module.Agents.TranscriptionType.RecordedTranscription, request.AgentProvider, model),
                TranscriptionAgent.TimestampOutputSchema,
                new UserInput(Role.User, new TextUserInputPart(TranscriptionAgent.AudioTranscriptionUserPrompt)),
                new UserInput(Role.User,
                    new InlineDataUserInputPart(Convert.ToBase64String(audioBytes), request.AudioFileMimeType)));

            var totalTokens = response.InputTokens + response.OutputTokens;

            await featureModuleService.AddUsage(request.ProviderId, FeatureModuleType.AI_Tokens, totalTokens, dateTimeProvider.GetDateTimeUtc());

            if (response.Error is OutputSchemaException)
            {
                Log.Warning("TranscriptionProcess: Json schema deserialization failed. Retrying using Json agent");
                response = await agentClient.Run<TranscriptionAgent.TimestampOutputResponse>(
                    new TranscriptionJsonQualityAssuranceAgent(model),
                    TranscriptionAgent.TimestampOutputSchema,
                    new UserInput(Role.User, new TextUserInputPart(response.ResponseText)));
                Log.Information("TranscriptionProcess: Json agent successfully processed the response");
            }


            // A non-silent transcription exists
            if (response?.Output?.Transcripts is { Length: > 0 })
            {
                var transcripts = response.Output.Transcripts
                    .Select(x =>
                    {
                        var isInaudible = x.IsInaudible || string.IsNullOrWhiteSpace(x.Content) ||
                                          Regex.IsMatch(x.Content, @"^\s*[Pp]*\s*$|^([\p{L}\p{M}]+)(?:\1){2,}$");

                        var transcript = new Transcript
                        {
                            Id = Guid.NewGuid(),
                            Content = isInaudible ? "" : x.Content,
                            Speaker = x.Speaker,
                            StartTime = x.StartTime.Add(TimeSpan.Parse(startTime)).ToString(@"hh\:mm\:ss"),
                            EndTime = x.EndTime.Add(TimeSpan.Parse(startTime)).ToString(@"hh\:mm\:ss"),
                            PartNumber = partNumber,
                            IsInaudible = isInaudible
                        };

                        return transcript;
                    })
                    .ToList();

                await SaveTranscripts(transcription.Id, transcripts);
            }
            else
            {
                Log.Warning(
                    "TranscriptionProcess: Transcription is empty for {TranscriptionId} with Folder path {FolderPath} and keys {fileKeys}",
                    transcription.Id, folderPath, fileKeys);
            }

            await SendNextSchedule(request, fileKeys.Last(), partNumber + 1, newStartTime, 0, hasMoreBatches);
        }

        private async Task<Request> InitializeTranscription(Request request)
        {
            var now = dateTimeProvider.GetDateTimeUtc();

            var notes = request.ClientIds
                .Distinct()
                .Select(x =>
                    new Note
                    {
                        Id = Guid.NewGuid(),
                        ContactId = x,
                        ProviderId = request.ProviderId,
                        CreatedByPersonId = request.InitiatorPersonId,
                        LastUpdatedByPersonId = request.InitiatorPersonId,
                        Status = NoteStatus.Draft,
                        CreatedDateTimeUtc = now,
                        LastUpdatedDateTimeUtc = now,
                        TaskId = request.TaskId
                    })
                .ToList();

            var transcription = new Transcription
            {
                Id = Guid.NewGuid(),
                Path = request.FolderPath,
                CreatedByPersonId = request.InitiatorPersonId,
                Type = request.TranscriptionType,
                Status = TranscriptionStatus.PendingCompletion,
                StorageProvider = request.StorageProvider,
                Vendor = TranscriptionProvider.GoogleVertexAI,
                ProviderId = request.ProviderId,
                CreatedDateTimeUtc = now,
                UpdatedDateTimeUtc = now,
                Transcripts = new List<Transcript>()
            };

            var noteTranscriptions = notes
                .Select(x => new NoteTranscription
                {
                    NoteId = x.Id,
                    TranscriptionId = transcription.Id,
                })
                .ToList();

            await noteRepository.Create(notes);
            await transcriptionRepository.Create(transcription);

            foreach (var noteTranscription in noteTranscriptions)
            {
                await noteTranscriptionRepository.Upsert(noteTranscription);
            }

            request = request with
            {
                NoteIds = notes.Select(x => x.Id).ToArray(),
                TranscriptionId = transcription.Id
            };

            return request;
        }

        private async Task SendNextSchedule(Request request,
            string lastProcessedFile, int partNumber, string startTime, int scheduleRunCount, bool scheduleImmediately)
        {
            var eventData = new EventData<Request>(request with
            {
                LastProcessedFile = lastProcessedFile,
                TranscriptionId = request.TranscriptionId ?? Guid.Empty,
                PartNumber = partNumber,
                StartTime = startTime,
                ScheduleRunCount = scheduleRunCount
            });

            var delayInSeconds =
                scheduleImmediately || request.TranscriptionType == TranscriptionType.RecordedTranscription ? 0 : 60;

            // 60 second delayed message
            await sqsRepository.SendMessage(
                QueueType.Transcription,
                new EventMessage(EventType.TranscribeAudioFiles, eventData, null, delayInSeconds)
            );

            Log.Information("TranscriptionProcess: SendNextSchedule: Schedule created {TranscriptionId}, {FolderPath}",
                eventData.Data.TranscriptionId, request.FolderPath);
        }

        /// <summary>
        /// Takes an existing start time and adds the duration of the audio to it
        /// </summary>
        /// <param name="startTime">Represented as 00:00:00 (hh:mm:ss)</param>
        /// <param name="duration">TimeSpan on the audio</param>
        /// <returns>New start time (hh:mm:ss)</returns>
        public string CalculateNewStartTime(string startTime, TimeSpan duration)
        {
            return TimeSpan.Parse(startTime ?? "00:00:00").Add(duration).ToString(@"hh\:mm\:ss");
        }

        private async Task<(string[], bool)> GetFilesAfterKey(string path, int fileCountToProcess,
            StorageProvider storageProvider, FileLocationType fileLocationType, string lastProcessedFileKey = null)
        {
            var twoBatchesCount = fileCountToProcess * 2;
            var fileKeys = await fileStorageServiceFactory(storageProvider)
                .ListPathFiles(path, twoBatchesCount, lastProcessedFileKey, fileLocationType, false);
            var hasMoreBatches = fileKeys.Length >= twoBatchesCount;

            var files = fileKeys.Order().Take(fileCountToProcess).ToArray();

            Log.ForContext("Files", files);

            return (files, hasMoreBatches);
        }

        private async Task CompleteTranscription(Guid transcriptionId, Guid[] noteIds,
            Guid personId, Guid providerId)
        {
            var transcription = await transcriptionRepository.GetById(transcriptionId);
            transcription.Status = TranscriptionStatus.PendingCompletion;
            await transcriptionRepository.Update(transcription);

            await sqsRepository.SendMessage(
                QueueType.Transcription,
                new EventMessage(EventType.TranscriptionPendingCompletion,
                    new EventData<TranscriptionPendingCompletionCommand.Request>(
                        new TranscriptionPendingCompletionCommand.Request(transcription.Id, noteIds, personId, providerId))
                ));
        }

        private async Task SaveTranscripts(Guid transcriptionId, List<Transcript> transcriptsToAdd)
        {
            var transcription = await transcriptionRepository.GetById(transcriptionId);

            var transcripts = new List<Transcript>();

            if (transcription is null || !transcriptsToAdd.Any()) return;

            // add existing transcripts
            if (transcription.Transcripts != null && transcription.Transcripts.Any())
            {
                transcripts.AddRange(transcription.Transcripts);
            }

            // add new transcripts
            transcripts.AddRange(transcriptsToAdd);

            // sort transcripts by part number and start time
            if (transcripts.Count > 1)
            {
                transcripts = transcripts.OrderBy(x => x.PartNumber).ThenBy(x => x.StartTime).ToList();
            }

            // update transcription with new transcripts
            transcription.Transcripts = transcripts;

            if (!transcription.HasInaudibleAudio)
            {
                transcription.HasInaudibleAudio = transcripts.Any(x => x.IsInaudible);
            }

            await transcriptionRepository.Update(transcription);
        }

        private Task<Stream[]> GetFileStreams(string[] fileKeys, StorageProvider storageProvider,
            FileLocationType fileLocationType)
        {
            return fileStorageServiceFactory(storageProvider).GetObjectStreams(fileKeys, fileLocationType);
        }
    }
}
