using System.Text;
using Markdig;
using Markdig.Syntax;
using Markdig.Syntax.Inlines;
using StepWise.Prose.Model;
using Markdig.Extensions.Tables; // For handling tables
using Markdig.Extensions.Footnotes;
using Markdig.Helpers;
using Markdig.Renderers;

namespace RichText.Module;

public static class RichTextUtility
{
    public static Node MarkdownToDocument(string markdown, Schema? _schema = null)
    {
        var schema = _schema ?? RichTextSchema.Schema;
        var nodes = MarkdownToNodes(markdown, schema);

        return schema.Nodes["doc"].Create(null, nodes);
    }

    public static List<Node> MarkdownToNodes(string markdown, Schema? schema = null)
    {
        var pipeline = new MarkdownPipelineBuilder()
            .UseAdvancedExtensions() // Enable advanced extensions (tables, footnotes, etc.)
            .Build();
        var document = Markdown.Parse(markdown, pipeline);

        var docContent = new List<Node>();
        foreach (var block in document)
        {
            var node = ParseBlock(block, schema ?? RichTextSchema.Schema);
            if (node != null)
            {
                docContent.Add(node);
            }
        }

        return docContent;
    }

    public static string? MarkdownToPlainText(string markdown)
    {
        // Create a pipeline that renders the markdown to plain text
        var pipeline = new MarkdownPipelineBuilder()
            .Build();

        // Convert the Markdown to plain text
        return Markdown.ToPlainText(markdown, pipeline)?.Trim();
    }

    private static Node? ParseBlock(Block block, Schema schema)
    {
        Func<Node?> unkonwnhandler = () =>
        {
            var content = GetBlockContent(block);
            if (string.IsNullOrEmpty(content))
            {
                return null;
            }

            return schema.Nodes["paragraph"].Create(null, new List<Node> { schema.Text(content) });
        };

        return block switch
        {
            ParagraphBlock paragraph => ParseParagraph(paragraph, schema),
            HeadingBlock heading => ParseHeading(heading, schema),
            ListBlock list => ParseList(list, schema),
            ListItemBlock listItem => ParseListItem(listItem, schema),
            QuoteBlock quote => ParseQuote(quote, schema),
            ThematicBreakBlock => schema.Nodes["horizontalRule"].Create(),
            CodeBlock code => schema.Nodes["paragraph"].Create(null, new List<Node> { schema.Text(GetBlockContent(code)) }),
            Table table => ParseTable(table, schema),
            Footnote footnote => ParseFootNote(footnote, schema),
            HtmlBlock htmlBlock => ParseHtmlBlock(htmlBlock, schema), // Added from my previous example
            _ => unkonwnhandler()
        };
    }

    private static Node ParseParagraph(ParagraphBlock paragraph, Schema schema)
    {
        return schema.Nodes["paragraph"].Create(null, ParseInlines(paragraph.Inline, schema));
    }

    private static Node ParseHeading(HeadingBlock heading, Schema schema)
    {
        var attrs = new Attrs { { "level", heading.Level } };
        return schema.Nodes["heading"].Create(attrs, ParseInlines(heading.Inline, schema));
    }

    private static Node ParseList(ListBlock list, Schema schema)
    {
        var listContent = new List<Node>();
        foreach (var item in list)
        {
            if (item is ListItemBlock listItem)
            {
                var listItemNode = ParseListItem(listItem, schema);
                if (listItemNode != null)
                {
                    listContent.Add(listItemNode);
                }
            }
        }

        var nodeName = list.IsOrdered ? "orderedList" : "bulletList";
        Attrs? attrs = null;
        if (list.IsOrdered && list.OrderedStart != "1")
        {
            if (int.TryParse(list.OrderedStart, out int startValue)) // Safer: Use TryParse
            {
                attrs = new Attrs { { "order", startValue } };
            }
        }
        return schema.Nodes[nodeName].Create(attrs, listContent);
    }

    // --- ParseListItem ---
    // Updated to handle flattening of a first-child ListBlock.
    private static Node ParseListItem(ListItemBlock listItem, Schema schema)
    {
        var listItemContent = new List<Node>();
        bool firstBlockProcessed = false;

        // Check if the first block exists and is a ListBlock
        if (listItem.FirstOrDefault() is ListBlock firstListBlock)
        {
            // --- Flattening Logic ---
            // Iterate through the items (ListItemBlocks) of the nested list
            foreach (var innerItem in firstListBlock)
            {
                if (innerItem is ListItemBlock nestedListItem)
                {
                    // Iterate through the actual content blocks of the nested list item
                    foreach (var contentBlock in nestedListItem)
                    {
                        // Parse the content block (e.g., ParagraphBlock)
                        var node = ParseBlock(contentBlock, schema);
                        if (node != null)
                        {
                            // Add the *content* node directly to the parent list item's content
                            listItemContent.Add(node);
                        }
                    }
                }
                // Handle cases where an item in a ListBlock isn't a ListItemBlock?
                // Markdig structure usually prevents this.
            }
            firstBlockProcessed = true; // Mark the first block as processed
        }

        // Process the remaining blocks in the original listItem
        // If the first block was processed (flattened), skip it (index starts at 1).
        // Otherwise, process all blocks (index starts at 0).
        foreach (var block in listItem.Skip(firstBlockProcessed ? 1 : 0))
        {
            // Parse normally
            var node = ParseBlock(block, schema);
            if (node != null)
            {
                listItemContent.Add(node);
            }
        }

        // Ensure listItem is not empty if required by schema (optional, depends on schema strictness)
        // if (listItemContent.Count == 0 && schema.Nodes["listItem"].ContentMatch.Required) // Example check
        // {
        //    // Add a default empty paragraph if the schema requires content
        //    listItemContent.Add(schema.Nodes["paragraph"].Create());
        // }

        // Create the final listItem node with the potentially flattened content
        return schema.Nodes["listItem"].Create(null, listItemContent);
    }

    private static Node ParseQuote(QuoteBlock quote, Schema schema)
    {
        return schema.Nodes["blockquote"].Create(null, quote.Select(block => ParseBlock(block, schema)).Where(node => node != null).ToList()!);
    }

    private static Node ParseCodeBlock(CodeBlock codeBlock, Schema schema)
    {
        var code = new StringBuilder();
        if (codeBlock is FencedCodeBlock fencedCodeBlock)
        {
            AppendCodeLines(fencedCodeBlock.Lines.Lines, code); // Corrected call
        }
        else if (codeBlock is CodeBlock indentedCodeBlock) // Keep consistent naming
        {
            AppendCodeLines(indentedCodeBlock.Lines.Lines, code); // Corrected call
        }

        Attrs? attrs = null;
        if (codeBlock is FencedCodeBlock fcb && !string.IsNullOrEmpty(fcb.Info))
        {
            var language = fcb.Info.Replace("language-", ""); // Your improved language handling
            attrs = new Attrs { { "language", language } };
        }

        // todo support codeBlock
        // return schema.Nodes["codeBlock"].Create(attrs, new List<Node> { schema.Text(code.ToString()) });
        return schema.Nodes["paragraph"].Create(attrs, new List<Node> { schema.Text(code.ToString()) });
    }

    // Corrected helper function: takes StringLine[]
    private static void AppendCodeLines(StringLine[]? lines, StringBuilder sb)
    {
        if (lines != null)
        {
            foreach (var line in lines) // Iterate directly over StringLine
            {
                sb.Append(line.ToString()); // each is StringLine, not a group
                sb.AppendLine(); // Add newline after *each* line
            }
        }
    }

    private static Node ParseTable(Table table, Schema schema)
    {
        var tableContent = new List<Node>();

        foreach (var tableRow in table)
        {
            if (tableRow is TableRow row)
            {
                var rowContent = new List<Node>();
                foreach (var tableCell in row)
                {
                    if (tableCell is TableCell cell)
                    {
                        var cellContent = new List<Node>();
                        foreach (var block in cell)
                        {
                            var parsedBlock = ParseBlock(block, schema);
                            if (parsedBlock != null)
                            {
                                cellContent.Add(parsedBlock);
                            }

                        }
                        var cellNode = schema.Nodes[row.IsHeader ? "tableHeader" : "tableCell"].Create(null, cellContent);
                        rowContent.Add(cellNode);
                    }
                }
                var tableRowNode = schema.Nodes["tableRow"].Create(null, rowContent);
                tableContent.Add(tableRowNode);
            }
        }
        return schema.Nodes["table"].Create(null, tableContent);
    }

    private static Node ParseFootNote(Footnote footnote, Schema schema)
    {
        var footnoteContent = footnote.Select(block => ParseBlock(block, schema)).Where(node => node != null).Cast<Node>().ToList();
        var attrs = new Attrs { { "id", footnote.Order.ToString() } };
        return schema.Nodes["blockquote"].Create(attrs, footnoteContent); // Use blockquote or a custom node
    }

    // **NEW**: Parses HTML blocks.
    private static Node ParseHtmlBlock(HtmlBlock htmlBlock, Schema schema)
    {
        var html = htmlBlock.Lines.ToString();  // Get the raw HTML content.  .Lines.ToString() concatenates all lines, handling newlines.
        return schema.Nodes["htmlBlock"].Create(new Attrs { { "html", html } }, null); // Store as an attribute

    }

    private static List<Node> ParseInlines(ContainerInline? inlines, Schema schema)
    {
        var inlineContent = new List<Node>();
        if (inlines == null)
        {
            return inlineContent;
        }

        // Track HTML marks state
        var activeHtmlMarks = new List<Mark>();

        foreach (var inline in inlines)
        {
            Node? inlineNode = null;
            switch (inline)
            {
                case LiteralInline literal:
                    var literalText = literal.Content.ToString();
                    if (!string.IsNullOrEmpty(literalText))
                    {
                        var textNode = schema.Text(literalText);
                        // Apply active HTML marks if any
                        if (activeHtmlMarks.Count > 0)
                        {
                            textNode = textNode.Mark(activeHtmlMarks.ToList());
                        }
                        inlineNode = textNode;
                    }
                    break;

                case EmphasisInline emphasis:
                    var marks = new List<Mark>();
                    if (emphasis.DelimiterChar == '*' || emphasis.DelimiterChar == '_')
                    {
                        marks.Add(emphasis.DelimiterCount == 2 ? schema.Marks["bold"].Create() : schema.Marks["italic"].Create());
                    }
                    // Handle emphasis levels (bold/italic). Markdig's EmphasisInline provides IsDouble for bold.
                    else if (emphasis.DelimiterChar == '~')
                    {
                        if (emphasis.DelimiterCount == 2)
                        {
                            marks.Add(schema.Marks["strike"].Create());
                        }
                    }
                    var emphasisContent = ParseInlines(emphasis, schema);
                    inlineContent.AddRange(AddMarksToNodes(emphasisContent, marks));
                    inlineNode = null;
                    break;

                // Corrected FootnoteLink handling: Separate case *before* LinkInline
                case FootnoteLink footnoteLink:
                    inlineContent.Add(ParseFootNoteLink(footnoteLink, schema));
                    inlineNode = null; // Already added to inlineContent
                    break;

                case LinkInline link:  // Now handles only *non-footnote* links
                                       // Image handling
                    if (link.IsImage)
                    {
                        inlineNode = schema.Nodes["image"].Create(new Attrs
                    {
                        { "src", link.Url },
                        { "alt", link.Title ?? GetImageAltText(link) }, // Use helper for alt text
                        { "title", link.Title ?? "" }
                    });
                        inlineContent.Add(inlineNode);
                    }
                    else
                    {
                        var linkMarks = new List<Mark>
                    {
                        schema.Marks["link"].Create(new Attrs
                        {
                            { "href", link.Url },
                            { "title", link.Title ?? "" }
                        })
                    };
                        var linkContent = ParseInlines(link, schema);
                        inlineContent.AddRange(AddMarksToNodes(linkContent, linkMarks));
                        inlineNode = null;
                    }
                    break;

                case CodeInline code:
                    // TODO: Handle code inline.  For now, treat as plain text.
                    // var codeMarks = new List<Mark> { schema.Marks["bold"].Create() };
                    var codeText = code.Content.ToString();
                    if (!string.IsNullOrEmpty(codeText))
                    {
                        inlineNode = schema.Text(codeText);
                    }
                    // inlineContent.Add(inlineNode.Mark(codeMarks));
                    break;

                case LineBreakInline lineBreak:
                    inlineNode = lineBreak.IsHard ? schema.Nodes["hardBreak"].Create() : schema.Text("\n");
                    inlineContent.Add(inlineNode);
                    break;

                case AutolinkInline autolink:
                    var autolinkMarks = new List<Mark>
                    {
                        schema.Marks["link"].Create(new Attrs
                        {
                            { "href", autolink.Url },
                            { "title", "" }
                        })
                    };
                    var autolinkContent = new List<Node> { schema.Text(autolink.Url) }; // Use the URL as the text.
                    inlineContent.AddRange(AddMarksToNodes(autolinkContent, autolinkMarks));
                    inlineNode = null;
                    break;

                case HtmlInline htmlInline:
                    // Parse basic HTML tags and convert them to appropriate marks
                    var htmlTag = htmlInline.Tag.ToLower().Trim();

                    // Handle opening tags - add marks to active list
                    if (htmlTag == "<u>")
                    {
                        activeHtmlMarks.Add(schema.Marks["underline"].Create());
                        inlineNode = null;
                    }
                    else if (htmlTag == "<b>" || htmlTag == "<strong>")
                    {
                        activeHtmlMarks.Add(schema.Marks["bold"].Create());
                        inlineNode = null;
                    }
                    else if (htmlTag == "<i>" || htmlTag == "<em>")
                    {
                        activeHtmlMarks.Add(schema.Marks["italic"].Create());
                        inlineNode = null;
                    }
                    else if (htmlTag.StartsWith("<a "))
                    {
                        // Extract href attribute
                        var href = ExtractHrefFromAnchorTag(htmlInline.Tag);
                        if (!string.IsNullOrEmpty(href))
                        {
                            var linkMark = schema.Marks["link"].Create(new Attrs { { "href", href } });
                            activeHtmlMarks.Add(linkMark);
                            inlineNode = null;
                        }
                        else
                        {
                            // Fallback to plain text if href extraction fails
                            var fallbackText = htmlInline.Tag;
                            if (!string.IsNullOrEmpty(fallbackText))
                            {
                                inlineNode = schema.Text(fallbackText);
                            }
                        }
                    }
                    // Handle closing tags - remove marks from active list
                    else if (htmlTag == "</u>")
                    {
                        activeHtmlMarks.RemoveAll(m => m.Type.Name == "underline");
                        inlineNode = null;
                    }
                    else if (htmlTag == "</b>" || htmlTag == "</strong>")
                    {
                        activeHtmlMarks.RemoveAll(m => m.Type.Name == "bold");
                        inlineNode = null;
                    }
                    else if (htmlTag == "</i>" || htmlTag == "</em>")
                    {
                        activeHtmlMarks.RemoveAll(m => m.Type.Name == "italic");
                        inlineNode = null;
                    }
                    else if (htmlTag == "</a>")
                    {
                        activeHtmlMarks.RemoveAll(m => m.Type.Name == "link");
                        inlineNode = null;
                    }
                    else if (htmlTag == "<br>" || htmlTag == "<br/>")
                    {
                        // Self-closing line break
                        inlineNode = schema.Nodes["hardBreak"].Create();
                    }
                    else
                    {
                        // For unrecognized HTML tags, treat as plain text
                        var htmlText = htmlInline.Tag;
                        if (!string.IsNullOrEmpty(htmlText))
                        {
                            inlineNode = schema.Text(htmlText);
                        }
                    }
                    break;

                default:
                    if (inlineNode != null)
                    {
                        var nodeContent = inlineNode.Content.ToString();
                        if (!string.IsNullOrEmpty(nodeContent))
                        {
                            inlineNode = schema.Text(nodeContent);
                            inlineContent.Add(inlineNode);
                        }
                    }
                    break;
            }
            if (inlineNode != null)
            {
                inlineContent.Add(inlineNode);
            }
        }
        return inlineContent;
    }

    private static Node ParseFootNoteLink(FootnoteLink footnoteLink, Schema schema)
    {
        var footnote = footnoteLink.Footnote;
        if (footnote == null)
        {
            return schema.Text("Invalid Footnote");
        }

        var attrs = new Attrs
        {
            { "ref", footnote.Order.ToString() },
            { "href", $"#footnote-{footnote.Order}" }
        };
        // Create a text node and mark it as a link
        return schema.Text($"[{footnote.Order}]").Mark(new List<Mark> { schema.Marks["link"].Create(attrs) });
    }

    private static List<Node> AddMarksToNodes(List<Node> nodes, List<Mark> marks)
    {
        return marks == null || marks.Count == 0
            ? nodes
            : nodes.Select(node => node.Mark(marks)).ToList();
    }
    // Helper function from my previous response
    private static string GetImageAltText(LinkInline link)
    {
        if (link.IsImage && link.FirstChild is LiteralInline literalInline)
        {
            return literalInline.Content.ToString();
        }
        return "";
    }

    private static string GetBlockContent(Block block)
    {
        using (var writer = new StringWriter())
        {
            var renderer = new HtmlRenderer(writer);
            renderer.Render(block);
            var content = writer.ToString();

            return string.Empty;
        }
    }

    private static string ExtractContentFromHtmlTag(string html)
    {
        // Extract content between the opening and closing tags
        var match = System.Text.RegularExpressions.Regex.Match(html, @">([^<]+)<");
        return match.Success ? match.Groups[1].Value.Trim() : "";
    }

    private static string ExtractHrefFromAnchorTag(string html)
    {
        // Extract href attribute value from an anchor tag
        var match = System.Text.RegularExpressions.Regex.Match(html, @"href\s*=\s*[""']?([^""' >]+)");
        return match.Success ? match.Groups[1].Value : "";
    }
}
