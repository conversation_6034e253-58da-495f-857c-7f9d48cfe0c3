using FluentAssertions;

namespace RichText.Module.Tests;

public class RichTextUtilityTests
{
  [Fact]
  public void MarkdownToDocument_Test()
  {
    var doc = RichTextUtility.MarkdownToDocument(markdown);

    var docJson = doc.ToJSON().ToJson();

    var snapshot = File.ReadAllText(Path.Join("Resources", "MarkdownToProseMirrorSnapshot.json"));

    docJson.Replace("\n", "").Replace(" ", "").Should().BeEquivalentTo(snapshot.Replace("\n", "").Replace(" ", ""));
  }

  [Fact]
  public void MarkdownToPlainText_Test()
  {
    var text = RichTextUtility.MarkdownToPlainText(simpleMarkdown);

    text.Should().BeEquivalentTo(simpleMarkdownSnapshot);
  }

  [Fact]
  public void MarkdownToNodes_Should_Parse_All_Inline_Node_Types()
  {
    // Markdown string containing all inline types handled in ParseInlines
    var markdown = @"This is plain text, **bold**, *italic*, ~~strike~~, `code`, [link](https://example.com), ![alt](img.png), <u>html</u>,
[autolink](mailto:<EMAIL>), <a href='x'>htmlinline</a>,
Line1  
Line2

Here is a footnote.[^1]

[^1]: Footnote text.";

    var actualJson = RichTextUtility.MarkdownToDocument(markdown).ToJSON().ToJson();

    // TODO: Replace this with the actual expected JSON after first run
    actualJson.Replace("\n", "").Replace(" ", "").Should().BeEquivalentTo(expectedJsonForAllNodes.Replace("\n", "").Replace(" ", ""));
  }

  private string simpleMarkdown = @"# SOAP Note
**Date:** 2024-10-26
**Patient:** Jane Doe
**DOB:** 1988-03-15
**MRN:** 1234567
---
**S (Subjective):**
*   Patient reports a persistent, dull headache for the past 3 days, rated 4/10 on a pain scale.";

  private string simpleMarkdownSnapshot = "SOAP Note\nDate: 2024-10-26\nPatient: Jane Doe\nDOB: 1988-03-15\nMRN: 1234567\nS (Subjective):\nPatient reports a persistent, dull headache for the past 3 days, rated 4/10 on a pain scale.";

  private string markdown = @"
Okay, here's a fictitious SOAP note, formatted concisely with Markdown:

---

### SOAP Note

**Date:** 2024-10-26

**Patient:** Jane Doe

**DOB:** 1988-03-15

**MRN:** 1234567

---

**S (Subjective):**

*   Patient reports a persistent, dull headache for the past 3 days, rated 4/10 on a pain scale.
*   No fever, nausea, or vomiting.
*   States headache is worse in the evenings.
*   Reports increased stress at work recently.
* Denies any visual changes or weakness.
* No recent illness, injury or trauma.

---

**O (Objective):**

*   **Vitals:** BP 120/78 mmHg, HR 72 bpm, RR 16/min, Temp 98.6°F (oral).
*   **General Appearance:** Alert, oriented x3, appears well-nourished, no acute distress.
*   **Neurological:** Cranial nerves II-XII grossly intact.  Reflexes 2+ and symmetric. Muscle strength 5/5 in all extremities. Sensation intact. Gait normal.
* No neck stiffness.

---

**A (Assessment):**

*   R/O Tension-type headache.
*   Consider stress as a contributing factor.
*   Rule out more serious causes (low suspicion based on presentation).

---

**P (Plan):**

*   Recommend OTC analgesics (Ibuprofen 400mg PO q6h PRN headache, not to exceed 2400mg/day).
*   Advised patient on stress reduction techniques (deep breathing, mindfulness).
*   Follow-up in 1 week if symptoms persist or worsen.
    *   Instructed patient to seek immediate medical attention if experiencing:
    * Severe headache/stiffness
    * Changes in vission
    * Weakness or numbness
    * Fever
    * Vomiting
* Rule out the use of OTC medication.

---
**Provider:** John Smith, MD

---
Key improvements & explanations:

*   **Markdown Formatting:**  Uses headings (`#`, `##`, `###`), bullet points (`*`), and bold text for clarity and readability.
*   **Conciseness:**  Uses standard abbreviations (e.g., BP, HR, PRN) and avoids unnecessary jargon.  Gets straight to the point in each section.
*   **Complete but Focused Subjective:**  Includes the chief complaint, relevant history (onset, duration, character, aggravating/alleviating factors), and pertinent negatives (ruling out red flags).
*   **Targeted Objective:**  Includes relevant vital signs and a focused neurological exam.  Uses objective terms (e.g., ""grossly intact,"" ""2+ and symmetric"").
*   **Clear Assessment:** Provides a differential diagnosis (rule out) and considers potential contributing factors.
*   **Specific Plan:**  Includes medication recommendations (name, dose, route, frequency, max dose), non-pharmacological advice, and a clear follow-up plan with specific ""red flag"" instructions.
* **Provider info:** Includes provider information.
*   **R/O format:** ""Rule Out"" uses the R/O format.
*   **Medical Record Number(MRN):** Included for completeness.

This is a numbered list
1. hello there
2. Yes sir ree

This example provides a good balance of detail and brevity, making it a practical and efficient SOAP note. Remember that the specific content will vary depending on the patient's condition.";

  string expectedJsonForAllNodes = @"{
  ""type"": ""doc"",
  ""content"": [
    {
      ""type"": ""paragraph"",
      ""content"": [
        { ""type"": ""text"", ""text"": ""This is plain text, "" },
        { ""type"": ""text"", ""text"": ""bold"", ""marks"": [{ ""type"": ""bold"" }] },
        { ""type"": ""text"", ""text"": "", "" },
        { ""type"": ""text"", ""text"": ""italic"", ""marks"": [{ ""type"": ""italic"" }] },
        { ""type"": ""text"", ""text"": "", "" },
        { ""type"": ""text"", ""text"": ""strike"", ""marks"": [{ ""type"": ""strike"" }] },
        { ""type"": ""text"", ""text"": "", code, "" },
        {
          ""type"": ""text"",
          ""text"": ""link"",
          ""marks"": [
            {
              ""type"": ""link"",
              ""attrs"": { ""href"": ""https://example.com"", ""title"": """" }
            }
          ]
        },
        { ""type"": ""text"", ""text"": "", "" },
        {
          ""type"": ""image"",
          ""attrs"": { ""src"": ""img.png"", ""alt"": """", ""title"": """" }
        },
        {
          ""type"": ""image"",
          ""attrs"": { ""src"": ""img.png"", ""alt"": """", ""title"": """" }
        },
        { ""type"": ""text"", ""text"": "", "" },
        { ""type"": ""text"", ""text"": ""html"", ""marks"": [{ ""type"": ""underline"" }] },
        { ""type"": ""text"", ""text"": "",\n\n"" },
        {
          ""type"": ""text"",
          ""text"": ""autolink"",
          ""marks"": [
            {
              ""type"": ""link"",
              ""attrs"": { ""href"": ""mailto:<EMAIL>"", ""title"": """" }
            }
          ]
        },
        { ""type"": ""text"", ""text"": "", "" },
        {
          ""type"": ""text"",
          ""text"": ""htmlinline"",
          ""marks"": [{ ""type"": ""link"", ""attrs"": { ""href"": ""x"", ""title"": null } }]
        },
        { ""type"": ""text"", ""text"": "",\n\nLine1"" },
        { ""type"": ""hardBreak"" },
        { ""type"": ""hardBreak"" },
        { ""type"": ""text"", ""text"": ""Line2"" }
      ]
    },
    {
      ""type"": ""paragraph"",
      ""content"": [
        { ""type"": ""text"", ""text"": ""Here is a footnote."" },
        {
          ""type"": ""text"",
          ""text"": ""[1]"",
          ""marks"": [
            {
              ""type"": ""link"",
              ""attrs"": { ""href"": ""#footnote-1"", ""title"": null }
            }
          ]
        }
      ]
    }
  ]
}";

}