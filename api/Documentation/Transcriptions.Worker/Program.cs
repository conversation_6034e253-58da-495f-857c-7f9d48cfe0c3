﻿using Agents.Module.Infrastructure.InversionOfControl;
using carepatron.core.Application.Schema.Services;
using carepatron.core.Common;
using carepatron.core.Configuration;
using carepatron.core.Paging;
using carepatron.core.Services;
using carepatron.infra.common.InversionOfControl;
using carepatron.infra.common.Serialization;
using carepatron.infra.eventbridge.InversionOfControl;
using carepatron.infra.google.InversionOfControl;
using carepatron.infra.lambda;
using carepatron.infra.posthog.InversionOfControl;
using carepatron.infra.s3.InversionOfControl;
using carepatron.infra.sql.InversionOfControl;
using carepatron.infra.sqs.InversionOfControl;
using carepatron.infra.telemetry;
using carepatron.infra.telemetry.Extensions;
using carepatron.workers.common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Notifications.Module;
using Serilog;
using Shared.Module;
using System;
using TemplateImporter.Worker.Module;
using Transcriptions.Worker.Module;

namespace Documentation.Transcriptions.Worker;

public class Program
{
    public static void Main(string[] args)
    {
        Log.Logger = new LoggerConfiguration()
            .ConfigureBootstrapLogger();
        AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(ErrorHandling.LogUnhandledException);

        try
        {
            CreateHostBuilder(args).Build().Run();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Host terminating. Fatal unhandled exception");
            Log.CloseAndFlush();
            throw;
        }
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseDefaultServiceProvider(options =>
            {
                // todo
                // Would be nice to enable this but muddy boundaries between
                // modules require a lot of unnecessary registrations.
                options.ValidateOnBuild = false;
            })
            .UseSerilog((hostingContext, services, loggerConfiguration) =>
            {
                loggerConfiguration.ConfigureLogger(hostingContext.Configuration);
            })
            .ConfigureSentryLoggingWithOtel()
            .ConfigureServices((hostContext, services) =>
            {
                IConfiguration configuration = hostContext.Configuration;

                JsonConvert.DefaultSettings = JsonSerializationSettings.GetJsonSerializerSettings;

                services
                    .AddMemoryCache()
                    // Add telemetry and monitoring
                    .AddWorkerOpenTelemetryWithSentry(hostContext.HostingEnvironment.ApplicationName)

                    // Register shared and worker-specific modules
                    .RegisterServices<SharedModule>()
                    .RegisterServices<TranscriptionsWorkerModule>()
                    .RegisterServices<TemplateImporterWorkerModule>()

                    // Core and infrastructure registrations
                    .RegisterInfra()
                    .RegisterSQLInfra(configuration)
                    .RegisterS3Infra(configuration)
                    .RegisterGoogleCloudStorage(configuration)
                    .RegisterEventBridgeInfra(configuration)
                    .RegisterSQSInfra(configuration) // Worker-specific SQS infra
                    .RegisterAgentServices(configuration)
                    .RegisterLambdaInfra(configuration)
                    .RegisterPostHogInfra(configuration)

                    // AI Templates configuration
                    .AddSingleton<IAiTemplatesConfiguration>(
                        configuration.GetSection("AiTemplatesConfiguration").Get<AiTemplatesConfiguration>()
                    )

                    // AI Templates client
                    .AddSingleton<TemplateImportUtilsClientConfiguration>(
                        configuration.GetSection("TemplateImportUtilsClient").Get<TemplateImportUtilsClientConfiguration>()
                    )

                    // Scoped services
                    .AddScoped<ISchemaService, SchemaService>()
                    .AddScoped<IWorkerExecution, WorkerExecution>()
                    .AddScoped<ITokenisedPaginator, TokenisedPaginator>()

                    // Transient worker services
                    .AddTransient<Worker>()

                    // Singleton services
                    .AddSingleton<IDateTimeProvider, DateTimeProvider>()

                    // Hosted services: Ensure multiple worker instances are created
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>())
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>())
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>())
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>())
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>())
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>())
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>())
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>())
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>());
                
                

                // services.RegisterMessageHandlersByAttribute();

                // not required in the worker; causes DI issues due to StripeWebhook dependency on mediatr.
                services.RemoveAll<IStripeWebhookService>();
            });
}