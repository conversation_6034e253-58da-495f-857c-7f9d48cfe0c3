﻿<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>dotnet-carepatron.workers.queue-071C58CF-ED81-4E69-90DD-126726FDAA5E</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <_WebToolingArtifacts Remove="Properties\launchSettings.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.6" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="EFCore.NamingConventions" Version="8.0.3" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.8.1" />
    <PackageReference Include="Scrutor" Version="3.3.0" />
    <PackageReference Include="AWS.Logger.AspNetCore" Version="3.2.0" />
    <PackageReference Include="AWSSDK.SQS" Version="3.7.301.8" />
    <PackageReference Include="Sentry.OpenTelemetry" Version="4.9.0" />
    <PackageReference Include="Sentry.Serilog" Version="4.9.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Agents\Modules\Agents.Module\Agents.Module.csproj" />
    <ProjectReference Include="..\..\carepatron.core\carepatron.core.csproj" />
    <ProjectReference Include="..\..\carepatron.infra.eventbridge\carepatron.infra.eventbridge.csproj" />
    <ProjectReference Include="..\..\carepatron.infra.google\carepatron.infra.google.csproj" />
    <ProjectReference Include="..\..\carepatron.infra.lambda\carepatron.infra.lambda.csproj" />
	<ProjectReference Include="..\..\carepatron.infra.posthog\carepatron.infra.posthog.csproj" />
    <ProjectReference Include="..\..\carepatron.infra.s3\carepatron.infra.s3.csproj" />
    <ProjectReference Include="..\..\carepatron.infra.sql\carepatron.infra.sql.csproj" />
    <ProjectReference Include="..\..\carepatron.infra.sqs\carepatron.infra.sqs.csproj" />
    <ProjectReference Include="..\..\carepatron.infra.telemetry\carepatron.infra.telemetry.csproj" />
    <ProjectReference Include="..\..\carepatron.workers.common\carepatron.workers.common.csproj" />
    <ProjectReference Include="..\TemplateImporter.Worker.Module\TemplateImporter.Worker.Module.csproj" />
    <ProjectReference Include="..\Transcriptions.Worker.Module\Transcriptions.Worker.Module.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Properties\launchSettings.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
  </ItemGroup>
</Project>
