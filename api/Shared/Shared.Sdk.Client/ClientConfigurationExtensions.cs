﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Polly;
using Polly.Extensions.Http;
using Polly.Timeout;
using Shared.Sdk.Client.Authorization;
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using System.Net;

namespace Shared.Sdk.Client
{
    public static class ClientConfigurationExtensions
    {
        public static IServiceCollection AddClient<TClient, TClientImplementation>(this IServiceCollection services, IConfiguration configuration)
            where TClient : class
            where TClientImplementation : class, TClient
        {
            return services.ConfigureServices<TClient, TClientImplementation>(configuration.GetSection(typeof(TClientImplementation).Name).Get<ClientConfiguration>());
        }

        public static IServiceCollection AddClient<TClient, TClientImplementation>(this IServiceCollection services, Func<ClientConfiguration> configurationAction)
            where TClient : class
            where TClientImplementation : class, TClient
        {
            var clientConfiguration = configurationAction();

            return services.ConfigureServices<TClient, TClientImplementation>(clientConfiguration);
        }

        public static IServiceCollection AddClient<TClient, TClientImplementation>(this IServiceCollection services, Action<ClientConfiguration> configurationAction)
            where TClient : class
            where TClientImplementation : class, TClient
        {
            var clientConfiguration = new ClientConfiguration();

            configurationAction?.Invoke(clientConfiguration);

            return services.ConfigureServices<TClient, TClientImplementation>(clientConfiguration);
        }

        private static IServiceCollection ConfigureServices<TClient, TClientImplementation>(this IServiceCollection services, ClientConfiguration clientConfiguration)
            where TClient : class
            where TClientImplementation : class, TClient
        {
            // Ensure memory cache is added to the service collection
            services.AddMemoryCache();

            var configurationKey = $"{typeof(TClientImplementation).Name}_Configuration";

            services.AddKeyedSingleton(configurationKey, clientConfiguration);

            services.AddHttpClient<IAuthorizationService, CognitoAuthorizationService>((serviceProvider, httpClient) =>
            {
                var clientConfiguration = serviceProvider.GetKeyedService<ClientConfiguration>(configurationKey);

                httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Content-Type", "application/json");
                httpClient.BaseAddress = new Uri(clientConfiguration.Issuer);
            });

            services.AddHttpClient<TClient, TClientImplementation>((serviceProvider, httpClient) =>
            {
                var clientConfiguration = serviceProvider.GetKeyedService<ClientConfiguration>(configurationKey);

                httpClient.BaseAddress = new Uri(clientConfiguration.BaseUrl);

            }).AddHttpMessageHandler((serviceProvider) =>
            {
                var clientConfiguration = serviceProvider.GetKeyedService<ClientConfiguration>(configurationKey);

                return new AccessTokenRetrievalHandler(serviceProvider.GetRequiredService<IAuthorizationService>(), clientConfiguration);
            })
            .AddPolicyHandler((serviceProvider, request) =>
            {
                var clientConfiguration = serviceProvider.GetKeyedService<ClientConfiguration>(configurationKey);

                HashSet<HttpStatusCode> retryableStatusCodes = new HashSet<HttpStatusCode>
                {
                    HttpStatusCode.ServiceUnavailable,
                    HttpStatusCode.GatewayTimeout,
                    HttpStatusCode.TooManyRequests,
                    HttpStatusCode.BadGateway
                };

                return HttpPolicyExtensions
                                    .HandleTransientHttpError()
                                    .Or<TimeoutRejectedException>() // thrown by Polly's TimeoutPolicy if the inner execution times out
                                    .OrResult(response => retryableStatusCodes.Contains(response.StatusCode))
                                    .WaitAndRetryAsync(clientConfiguration.RetryTimes, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
            }).AddPolicyHandler((serviceProvider, request) =>
            {
                var clientConfiguration = serviceProvider.GetKeyedService<ClientConfiguration>(configurationKey);

                return HttpPolicyExtensions
                                    .HandleTransientHttpError()
                                    .CircuitBreakerAsync(clientConfiguration.RetryTimes, TimeSpan.FromSeconds(clientConfiguration.Timeout));
            });

            return services;
        }
    }
}
