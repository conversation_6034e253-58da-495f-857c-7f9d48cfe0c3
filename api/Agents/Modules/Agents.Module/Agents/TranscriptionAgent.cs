using Agents.Module.JsonConverters;
using Agents.Sdk.Types;
using Newtonsoft.Json;
using static Agents.Sdk.Types.AgentModels;

namespace Agents.Module.Agents;

public enum TranscriptionType
{
  LiveDictation,
  LiveTranscription,
  RecordedTranscription,
  LiveVideoCallTranscription
}

public class TranscriptionAgent : Agent
{
  public TranscriptionAgent(TranscriptionType transcriptionType, AgentProvider provider, string model) : base(
      "Audio Transcription Agent",
      provider,
      model,
      transcriptionType
          is TranscriptionType.LiveTranscription
          or TranscriptionType.RecordedTranscription
          or TranscriptionType.LiveVideoCallTranscription
              ? AudioTranscription : AudioDictation,
          new AgentSettings(new ModelSettings(Temperature: 0.4f, MaxOutputTokens: 8192), new AgentAudioSettings(true)))
  { }

  public static string AudioTranscription = @"
Your task is to accurately transcribe the provided audio file. Follow these steps carefully:

Step 1 - Analyze the audio
- Count the distinct number of speakers who clearly speak
- Listen attentively to what they are saying

Step 2 - Transcribe the audio:
- Listen to the audio file attentively.
- Transcribe only the spoken words.
- If the speech is inaudible or you think it sounds robotic or extremely muffled, then output the content with nothing.
- Separate the speakers.
- Precisely measure the StartTime and EndTime of when the person spoke formatted in ""mm:ss.fff"".
- If there's an inaudible audio, set the IsInaudible property to true, otherwise set it to false.

Step 3 - Quality Assurance:
- Listen back to the audio and review your transcription.
- Fix any repeated or nonsensical text (e.g., """"PPPP..."""", """"p p p p p p..."""", """"पीपीपीपीपी...""""). You should make the content of these empty unless there are valid words in the string. Remember it could be in any language.
- Fix any missing or incorrect punctuation and brackets (e.g., missing braces or quotes) in the JSON output
- Remove extra/unnecessary characters (e.g., trailing HTML tags) in the JSON output
- Preserve as much meaningful content as possible (i.e. keep legitimate transcription text)
- Return only valid JSON (ensure all keys and values make sense and syntax is correct)
- Do not add extraneous commentary or explanation in your output. Output the corrected JSON only
- Ensure the final output can be parsed by standard JSON parsers without error.
- Do not include markdown, code-block formatting, or any additional text—only the corrected JSON object or JSON array.

Important: Do not invent or add any content that is not clearly spoken in the audio. If you are unsure about any part of the speech, it's better to output empty content than to guess.
  ";

  public static string AudioDictation = @"
You are an expert medical transcriptionist tasked with accurately transcribing the following audio. Your primary focus is on maintaining high accuracy, especially regarding medical terminology, patient details, and clinical descriptions. Follow these steps carefully:

Step 1 - Transcription:
- Listen to the audio attentively, focusing only on the main voices and ignoring background noises.
- Transcribe the medical audio with high accuracy, ensuring all medical terms, patient information, and clinical descriptions are correctly captured.
- If there is no speech or nothing to transcribe, respond with an empty text.
- Ensure the transcription is clear, precise, and retains as much of the original speech as possible.
- Do not include any background noise, irrelevant speech, or partial words cut off at the beginning or end of the audio.
- If you're unsure about a word or phrase, especially medical terms, indicate this with [unclear] rather than guessing.

Step 2 - Transcription Refinement:
- Remove filler words such as ""um,"" ""uh,"" ""er,"" etc.
- Convert verbal indications of punctuation into their corresponding marks:
  * ""Full stop"" or ""period"" → .
  * ""Comma"" → ,
  * ""New paragraph"" → [Start a new paragraph]
  * ""Question mark"" → ?
  * ""Exclamation point"" → !
  * ""Colon"" → :
  * ""Semicolon"" → ;
  * ""Dash"" → -
  * ""Open parenthesis"" → (
  * ""Close parenthesis"" → )
- Ensure proper capitalization and formatting of medical terms, drug names, and anatomical references.

Step 3 - Specialized Conversions:
- Convert NATO phonetic alphabet words into their corresponding letters. For example:
  * ""Echo Foxtrot X-ray"" becomes ""EFX""
  * ""Charlie Tango"" becomes ""CT""
- NATO phonetic alphabet: Alfa, Bravo, Charlie, Delta, Echo, Foxtrot, Golf, Hotel, India, Juliett, Kilo, Lima, Mike, November, Oscar, Papa, Quebec, Romeo, Sierra, Tango, Uniform, Victor, Whiskey, X-ray, Yankee, Zulu.
- If you encounter medical abbreviations or acronyms, transcribe them as spoken. Do not expand them unless explicitly stated in the audio.

Step 4 - Formatting:
- If there is output from the previous step, proceed with this step. Otherwise, skip this step and output an empty JSON object {}.
- Format the transcription in JSON with the following keys: ""speaker"", ""startTime"", ""endTime"", and ""content"".
- Ensure that ""startTime"" and ""endTime"" are precise to the milliseconds and formatted in ""mm:ss.fff"".

Step 5 - Final Review:
- Review your transcription for accuracy, paying special attention to medical terminology, patient information, and clinical descriptions.
- Ensure that the transcription flows logically and maintains the context of the medical discussion.
- Format the transcription with appropriate paragraphs for readability.

Important: Do not invent or add any content that is not clearly spoken in the audio. If you are unsure about any part of the speech, it's better to omit it than to guess.
";

  public static string TimestampOutputSchema = @"{
  ""type"": ""object"",
  ""properties"": {
    ""transcripts"": {
      ""type"": ""array"",
      ""items"": {
        ""type"": ""object"",
        ""properties"": {
          ""IsInaudible"": {
            ""type"": ""boolean""
          },
          ""StartTime"": {
            ""type"": ""string""
          },
          ""EndTime"": {
            ""type"": ""string""
          },
          ""Content"": {
            ""type"": ""string""
          }
        },
        ""required"": [""IsInaudible"", ""StartTime"", ""EndTime"", ""Content""]
      }
    }
  },
  ""required"": [""transcripts""]
}";

  public static string AudioTranscriptionUserPrompt = @"
The audio file may have people speaking throughout, or only in parts of it, or none of it.
Capture all of the clearly spoken words.
";

  public class TimestampOutputResponse
  {
    public TimestampTranscript[] Transcripts { get; set; } = Array.Empty<TimestampTranscript>();
  }
}

public class TimestampTranscript
{
  public string? Speaker { get; set; }

  [JsonConverter(typeof(TimeStampConverter))]
  public TimeSpan StartTime { get; set; }

  [JsonConverter(typeof(TimeStampConverter))]
  public TimeSpan EndTime { get; set; }

  public string? Content { get; set; }
  public bool IsInaudible { get; set; }
}