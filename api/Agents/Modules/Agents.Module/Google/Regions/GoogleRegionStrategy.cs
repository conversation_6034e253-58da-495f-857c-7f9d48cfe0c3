namespace Agents.Module.Google.Regions;

using Sdk.Types;
using Module.Services;
using ITimer = Services.ITimer;
using System.Collections.Concurrent;

public interface IGoogleRegionStrategy
{
    void MarkRegionAsFailed(string region);
    bool IsRegionFailed(string region);
    GoogleEndpoint RoundRobinRegion();
}

public abstract class GoogleRegionStrategy(string projectId, string publisher, string model, TimerFactory timerFactory, int timerExpiration = 60000)
    : IGoogleRegionStrategy
{
    private readonly ConcurrentDictionary<string, ITimer> failedRegions = new();
    public abstract IReadOnlyCollection<string> ProvisionedRegions { get; }
    public abstract IReadOnlyCollection<string> GeneralRegions { get; }

    public void MarkRegionAsFailed(string region)
    {
        if (failedRegions.TryGetValue(region, out var timer))
        {
            timer.Stop();
            timer.Start();
        }
        else
        {
            timer = timerFactory(timerExpiration);
            timer.Elapsed += (sender, e) =>
            {
                if (failedRegions.ContainsKey(region))
                {
                    failedRegions.Remove(region, out _);
                }
            };
            timer.AutoReset = false;
            timer.Start();

            failedRegions.TryAdd(region, timer);
        }
    }

    public bool IsRegionFailed(string region) => failedRegions.ContainsKey(region);

    public GoogleEndpoint RoundRobinRegion()
    {
        Random random = new Random();
        int attempts = 0;
        int maxAttempts = GeneralRegions.Count + ProvisionedRegions.Count;

        // Try provisioned regions first
        foreach (var provisionedRegion in ProvisionedRegions)
        {
            if (!IsRegionFailed(provisionedRegion))
            {
                return new GoogleEndpoint(projectId, publisher, provisionedRegion, model);
            }
        }

        // If all provisioned regions failed, fallback to other regions
        string region;
        do
        {
            IReadOnlyList<string> generalRegionsList = GeneralRegions.ToList();
            region = generalRegionsList[random.Next(generalRegionsList.Count)];

            attempts++;
            if (attempts >= maxAttempts)
            {
                throw new InvalidOperationException("No available regions found.");
            }
        } while (IsRegionFailed(region));

        return new GoogleEndpoint(projectId, publisher, region, model);
    }
}

public class DefaultGoogleRegionStrategy : GoogleRegionStrategy
{
    public DefaultGoogleRegionStrategy(string projectId, string publisher, string model, TimerFactory timerFactory, int timerExpiration)
        : base(projectId, publisher, model, timerFactory, timerExpiration) { }

    public override IReadOnlyCollection<string> ProvisionedRegions => Array.Empty<string>();

    public override IReadOnlyCollection<string> GeneralRegions => new[] {
        // Australia
        "australia-southeast1",

        // United States
        "us-central1",
        "us-east1",
        "us-east4",
        "us-east5",
        "us-south1",
        "us-west1",
        "us-west4",

        // Asia-Pacific
        "asia-east1",
        "asia-east2",
        "asia-northeast1",
        "asia-northeast3",
        "asia-south1",
        "asia-southeast1",
    };
}

public class GeminiFlash2GoogleRegionStrategy : GoogleRegionStrategy
{
    public GeminiFlash2GoogleRegionStrategy(string projectId, string publisher, TimerFactory timerFactory, int timerExpiration)
        : base(projectId, publisher, AgentModels.MultiModal.Google.GeminiFlash2, timerFactory, timerExpiration) { }

    public override IReadOnlyCollection<string> ProvisionedRegions => new[] {
        // Australia
        "australia-southeast1",
    };

    public override IReadOnlyCollection<string> GeneralRegions => new[] {
        // United States
        "us-central1",
        "us-east1",
        "us-east4",
        "us-east5",
        "us-south1",
        "us-west1",
        "us-west4",

        // Asia-Pacific
        "asia-east1",
        "asia-east2",
        "asia-northeast1",
        "asia-northeast3",
        "asia-south1",
        "asia-southeast1",
    };
}

public class GeminiPro2GoogleRegionStrategy : GoogleRegionStrategy
{
    public GeminiPro2GoogleRegionStrategy(string projectId, string publisher, TimerFactory timerFactory, int timerExpiration)
        : base(projectId, publisher, AgentModels.MultiModal.Google.GeminiPro2, timerFactory, timerExpiration) { }

    public override IReadOnlyCollection<string> ProvisionedRegions => new[] {
        // Australia
        "australia-southeast1",
    };

    public override IReadOnlyCollection<string> GeneralRegions => new[] {
        // United States
        "us-central1",
        "us-east1",
        "us-east4",
        "us-east5",
        "us-south1",
        "us-west1",
        "us-west4",

        // Asia-Pacific
        "asia-east1",
        "asia-east2",
        "asia-northeast1",
        "asia-northeast3",
        "asia-south1",
        "asia-southeast1",
    };
}

public class GeminiFlash20_001GoogleRegionStrategy : GoogleRegionStrategy
{
    public GeminiFlash20_001GoogleRegionStrategy(string projectId, string publisher, TimerFactory timerFactory, int timerExpiration)
        : base(projectId, publisher, AgentModels.MultiModal.Google.GeminiFlash20_001, timerFactory, timerExpiration) { }

    public override IReadOnlyCollection<string> ProvisionedRegions => new[] {
        // Oregon
        "us-west1",
    };

    public override IReadOnlyCollection<string> GeneralRegions => new[] {
        // United States
        "us-central1",
        "us-east1",
        "us-east4",
        "us-east5",
        "us-south1",
        "us-west1",
        "us-west4",

        // Asia-Pacific
        "asia-east1",
        "asia-east2",
        "asia-northeast1",
        "asia-northeast3",
        "asia-south1",
        "asia-southeast1",
    };
}

public class GeminiFlash25_PreviewGoogleRegionStrategy : GoogleRegionStrategy
{
    public GeminiFlash25_PreviewGoogleRegionStrategy(string projectId, string publisher, TimerFactory timerFactory, int timerExpiration)
        : base(projectId, publisher, AgentModels.MultiModal.Google.GeminiFlash25_Preview, timerFactory, timerExpiration) { }

    public override IReadOnlyCollection<string> ProvisionedRegions => new[] {
        "us-central1",
    };

    public override IReadOnlyCollection<string> GeneralRegions => new[] {
        // United States
        "us-central1",
        "us-east1",
        "us-east4",
        "us-east5",
        "us-south1",
        "us-west1",
        "us-west4",
        // Asia-Pacific
        "asia-east1",
        "asia-east2",
        "asia-northeast1",
        "asia-northeast3",
        "asia-south1",
        "asia-southeast1",
    };
}