using System.Collections.Concurrent;
using Agents.Module.Infrastructure.Configuration;
using Agents.Module.Services;
using Agents.Sdk.Types;

namespace Agents.Module.Google.Regions;

public interface IGoogleRegionStrategyHelper
{
    void MarkRegionAsFailed(string model, string region);
    GoogleEndpoint GetEndpoint(string model);
}

public class GoogleRegionStrategyHelper(GoogleRegionStrategyFactory googleRegionStrategyFactory) : IGoogleRegionStrategyHelper
{
    ConcurrentDictionary<string, IGoogleRegionStrategy> strategies = new();
    private object lockObject = new();

    public void MarkRegionAsFailed(string model, string region)
    {
        // thread safe when failures are added to dictionary within the strategy
        lock (lockObject)
            strategies.GetOrAdd(model, googleRegionStrategyFactory.CreateStrategy).MarkRegionAsFailed(region);
    }

    public GoogleEndpoint GetEndpoint(string model)
        => strategies.GetOrAdd(model, googleRegionStrategyFactory.CreateStrategy).RoundRobinRegion();
}

public class GoogleRegionStrategyFactory(GoogleAgentConfiguration configuration, TimerFactory timerFactory, int timerExpiration)
{
    public IGoogleRegionStrategy CreateStrategy(string model)
    {
        return model switch
        {
            AgentModels.MultiModal.Google.GeminiFlash2 => new GeminiFlash2GoogleRegionStrategy(configuration.ProjectId, configuration.Publisher, timerFactory, timerExpiration),
            AgentModels.MultiModal.Google.GeminiPro2 => new GeminiPro2GoogleRegionStrategy(configuration.ProjectId, configuration.Publisher, timerFactory, timerExpiration),
            AgentModels.MultiModal.Google.GeminiFlash20_001 => new GeminiFlash20_001GoogleRegionStrategy(configuration.ProjectId, configuration.Publisher, timerFactory, timerExpiration),
            AgentModels.MultiModal.Google.GeminiFlash25_Preview => new GeminiFlash25_PreviewGoogleRegionStrategy(configuration.ProjectId, configuration.Publisher, timerFactory, timerExpiration),
            _ => new DefaultGoogleRegionStrategy(configuration.ProjectId, configuration.Publisher, model, timerFactory, timerExpiration),
        };
    }
}