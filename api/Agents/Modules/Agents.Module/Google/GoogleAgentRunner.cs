using Agents.Module.AgentRunners;
using Agents.Module.Exceptions;
using Agents.Module.Google.Regions;
using Agents.Sdk.Types;
using Google;
using Google.Cloud.AIPlatform.V1Beta1;
using Google.Protobuf;
using Google.Protobuf.Collections;
using Grpc.Core;
using Newtonsoft.Json;
using Polly;
using Polly.Retry;
using Serilog;
using System.Net;
using System.Text.RegularExpressions;
using static Google.Cloud.AIPlatform.V1Beta1.SafetySetting.Types;

namespace Agents.Module.Google;

public class GoogleAgentRunner(
    IGoogleAIApiService service,
    IGoogleRegionStrategyHelper regionStrategyHelper) : IAgentRunner
{
    private static string modelContextKey = "Model";
    private static string regionContextKey = "Region";

    private static RepeatedField<SafetySetting> SafetySettingsPredict => new RepeatedField<SafetySetting>{
        new SafetySetting
        {
            Category = HarmCategory.HateSpeech, Threshold = HarmBlockThreshold.BlockNone
        },
        new SafetySetting
        {
            Category = HarmCategory.DangerousContent, Threshold = HarmBlockThreshold.BlockNone
        },
        new SafetySetting
        {
            Category = HarmCategory.SexuallyExplicit, Threshold = HarmBlockThreshold.BlockNone
        },
        new SafetySetting
        {
            Category = HarmCategory.Harassment, Threshold = HarmBlockThreshold.BlockNone
        },
    };

    private static HttpStatusCode[] retryStatusCodes = {
        HttpStatusCode.TooManyRequests,
        HttpStatusCode.InternalServerError,
        HttpStatusCode.ServiceUnavailable,
        HttpStatusCode.GatewayTimeout,
        HttpStatusCode.RequestTimeout,
        HttpStatusCode.BadGateway
    };

    private static StatusCode[] retryRpcStatusCodes = {
        StatusCode.ResourceExhausted,
        StatusCode.Unavailable,
        StatusCode.DeadlineExceeded,
        StatusCode.Aborted,
        StatusCode.Internal,
        StatusCode.Unknown,
        StatusCode.Cancelled,

    };

    private Action<Exception, int, Polly.Context> retryAction = (exception, retryCount, context) =>
    {
        context.TryGetValue(modelContextKey, out var modelContext);
        context.TryGetValue(regionContextKey, out var regiontContex);

        var model = modelContext.ToString();
        var region = regiontContex.ToString();

        if (model != null && region != null)
        {
            regionStrategyHelper.MarkRegionAsFailed(model, region);
        }

        Log.Warning(exception, "Retry {RetryCount} Google Agent for model {model} and region {region}", retryCount, model, region);
    };

    private AsyncRetryPolicy RetryPolicy() => Policy
            .Handle<GoogleApiException>(ex => retryStatusCodes.Contains(ex.HttpStatusCode))
            .RetryAsync(2, retryAction);

    private AsyncRetryPolicy RetryPolicyRpc() => Policy
            .Handle<RpcException>(ex => retryRpcStatusCodes.Contains(ex.StatusCode))
            .RetryAsync(2, retryAction);

    public Task<AgentResponse<string?>> RunAgent(Agent agent, params UserInput[] messages)
    {
        return RunAgent<string>(agent, null, messages);
    }

    public async Task<AgentResponse<T?>> RunAgent<T>(Agent agent, string? outputSchema = null, params UserInput[] messages) where T : class
    {
        var agentResponse = new AgentResponse<T?>();
        var responseText = string.Empty;

        try
        {
            if (agent == null)
            {
                Log.Error("Agent object is null.");
            }

            if (agent.Settings == null || agent.Settings.ModelSettings == null)
            {
                Log.Error("Agent settings or model settings are null.");
            }

            var endpoint = regionStrategyHelper.GetEndpoint(agent.Model);

            if (endpoint == null)
            {
                Log.Error("Endpoint is null for model: {Model}", agent.Model);
            }

            // Initialize content request
            var request = new GenerateContentRequest
            {
                Model = endpoint.Endpoint,
                GenerationConfig = new GenerationConfig
                {
                    MaxOutputTokens = agent.Settings.ModelSettings.MaxOutputTokens,
                    Temperature = agent.Settings.ModelSettings.Temperature,
                    ResponseSchema = !string.IsNullOrWhiteSpace(outputSchema)
                        ? JsonConvert.DeserializeObject<OpenApiSchema>(outputSchema)
                        : null,
                    ResponseMimeType = !string.IsNullOrWhiteSpace(outputSchema)
                        ? "application/json"
                        : "text/plain",
                    AudioTimestamp = agent.Settings.AgentAudioSettings?.IncludeTimestamps ?? false
                },
                SafetySettings = { SafetySettingsPredict },
                SystemInstruction = new Content
                {
                    Parts = { new Part { Text = agent.Instructions } }
                },
            };

            if (request.GenerationConfig.ResponseSchema == null && !string.IsNullOrWhiteSpace(outputSchema))
            {
                Log.Warning("Response schema deserialization returned null. Output schema: {OutputSchema}", outputSchema);
            }

            // Loop through the messages and add them to the Contents list
            foreach (var message in messages)
            {
                if (message == null)
                {
                    Log.Warning("A message in the messages array is null.");
                    continue;
                }

                var content = new Content
                {
                    Role = message.Role.ToString()
                };

                foreach (var part in message.Parts)
                {
                    if (part == null)
                    {
                        Log.Warning("A part in the message parts array is null.");
                        continue;
                    }

                    content.Parts.Add(part.Type switch
                    {
                        UserInputPartType.InlineData => new Part
                        {
                            InlineData = new Blob
                            {
                                Data = ByteString.FromBase64(part.InlineData),
                                MimeType = part.MimeType
                            }
                        },
                        UserInputPartType.FileData => new Part
                        {
                            FileData = new FileData
                            {
                                FileUri = part.FileUri,
                                MimeType = part.MimeType
                            }
                        },
                        _ => new Part { Text = part.Text }
                    });
                }

                if (content.Parts.Count == 0)
                {
                    Log.Warning("Message content parts are empty for role: {Role}", message.Role);
                }

                request.Contents.Add(content);
            }

            var response = await RetryPolicy()
                .ExecuteAsync((ctx) =>
                {
                    var endpoint = regionStrategyHelper.GetEndpoint(agent.Model);

                    if (endpoint == null)
                    {
                        Log.Error("Endpoint is null during request execution for model: {Model}", agent.Model);
                    }

                    request.Model = endpoint.Endpoint;

                    ctx[modelContextKey] = endpoint.Model;
                    ctx[regionContextKey] = endpoint.Location;

                    return service.Execute(request, endpoint);
                }, new Polly.Context { { modelContextKey, string.Empty }, { regionContextKey, string.Empty } });

            if (response == null || response.Candidates == null || response.Candidates.Count == 0)
            {
                Log.Warning("Response or response candidates are null or empty.");
                return new AgentResponse<T?>
                {
                    ResponseText = string.Empty,
                    InputTokens = response?.UsageMetadata?.PromptTokenCount ?? 0,
                    OutputTokens = response?.UsageMetadata?.CandidatesTokenCount ?? 0,
                };
            }

            responseText = response.Candidates[0]?.Content.Parts?[0]?.Text ?? string.Empty;

            if (string.IsNullOrWhiteSpace(responseText))
            {
                Log.Warning("Response text is null or empty.");
            }

            agentResponse = new AgentResponse<T?>
            {
                ResponseText = responseText,
                InputTokens = response?.UsageMetadata?.PromptTokenCount ?? 0,
                OutputTokens = response?.UsageMetadata?.CandidatesTokenCount ?? 0,
            };

            agentResponse.Output = outputSchema == null
                ? responseText as T
                : JsonConvert.DeserializeObject<T>(responseText);

            return agentResponse;
        }
        catch (JsonException ex)
        {
            // Replace all letters with "#" to remove any sensitive information except P or p
            string redactedText = Regex.Replace(responseText, "(?![Pp])[a-zA-Z]", "#");
            Log.Warning(ex, $"Error deserializing response from model: {redactedText}");
            agentResponse.Error = new OutputSchemaException(responseText);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "An unknown Exception happened while running the agent.");
            throw;
        }

        return agentResponse;
    }

    public async IAsyncEnumerable<string> RunAgentStream(Agent agent, params UserInput[] messages)
    {
        var endpoint = regionStrategyHelper.GetEndpoint(agent.Model);

        // Initialize content request
        var generateContentRequest = new GenerateContentRequest
        {
            Model = endpoint.Endpoint,
            GenerationConfig = new GenerationConfig
            {
                MaxOutputTokens = agent.Settings.ModelSettings.MaxOutputTokens,
                Temperature = agent.Settings.ModelSettings.Temperature,
                // not yet supported by the API
                // AudioTimestamp = agent.Settings.AgentAudioSettings?.IncludeTimestamps
            },
            SafetySettings = { SafetySettingsPredict },
            SystemInstruction = new Content
            {
                Parts = { new Part { Text = agent.Instructions } }
            }
        };

        // Loop through the messages and add them to the Contents list
        foreach (var message in messages)
        {
            var content = new Content
            {
                Role = message.Role.ToString()
            };

            foreach (var part in message.Parts)
            {
                content.Parts.Add(part.Type switch
                {
                    UserInputPartType.InlineData => new Part
                    {
                        InlineData = new Blob
                        {
                            Data = ByteString.FromBase64(part.InlineData),
                            MimeType = part.MimeType
                        }
                    },
                    UserInputPartType.FileData => new Part
                    {
                        FileData = new FileData
                        {
                            FileUri = part.FileUri,
                            MimeType = part.MimeType
                        }
                    },
                    _ => new Part { Text = part.Text }
                });
            }

            generateContentRequest.Contents.Add(content);
        };

        var (enumerator, movedNext, disposeStream) = await RetryPolicyRpc()
            .ExecuteAsync((ctx) =>
            {
                var endpoint = regionStrategyHelper.GetEndpoint(agent.Model);

                ctx[modelContextKey] = endpoint.Model;
                ctx[regionContextKey] = endpoint.Location;

                return service.ExecuteStream(generateContentRequest, endpoint);
            }, new Polly.Context { { modelContextKey, string.Empty }, { regionContextKey, string.Empty } });

        if (movedNext)
        {
            do
            {
                yield return enumerator.Current.Candidates[0]?.Content.Parts[0]?.Text ?? string.Empty;
            }
            while (await enumerator.MoveNextAsync());

            await disposeStream();
        }
    }
}