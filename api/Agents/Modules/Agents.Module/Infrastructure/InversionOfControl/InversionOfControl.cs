using Agents.Module.AgentRunners;
using Agents.Module.Google;
using Agents.Module.Google.Regions;
using Agents.Module.Infrastructure.Configuration;
using Agents.Module.Services;
using Agents.Sdk.Types;
using Agents.Sdk.Types.Kernel;
using Google.Apis.Auth.OAuth2;
using Google.Cloud.AIPlatform.V1Beta1;
using Google.Cloud.Speech.V1;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;

namespace Agents.Module.Infrastructure.InversionOfControl;

public static class InversionOfControl
{
    public static IServiceCollection RegisterAgentServices(this IServiceCollection services,
        IConfiguration configuration)
    {
        var googleAppConfiguration = configuration.GetSection(nameof(GoogleAgentConfiguration)).Get<GoogleAgentConfiguration>();

        if (googleAppConfiguration == null) throw new InvalidOperationException("GoogleAgentConfiguration not found in appsettings.json");

        var credential = GoogleCredential.FromJson(JsonConvert.SerializeObject(new
        {
            type = googleAppConfiguration.Type,
            project_id = googleAppConfiguration.ProjectId,
            private_key_id = googleAppConfiguration.PrivateKeyId,
            private_key = googleAppConfiguration.PrivateKey,
            client_email = googleAppConfiguration.ClientEmail,
            client_id = googleAppConfiguration.ClientId,
            auth_uri = googleAppConfiguration.AuthUri,
            token_uri = googleAppConfiguration.TokenUri,
            auth_provider_x509_cert_url = googleAppConfiguration.AuthProviderX509CertUrl,
            client_x509_cert_url = googleAppConfiguration.ClientX509CertUrl,
            universe_domain = googleAppConfiguration.UniverseDomain
        })).CreateScoped(PredictionServiceClient.DefaultScopes);

        services
            .AddSingleton(googleAppConfiguration)
            .AddScoped<GoogleCredential>(_ => credential)
            .AddScoped<IGoogleAIApiService, GoogleAIApiService>()
            .AddScoped<AgentClient>()
            .AddScoped<IAgentClient, AgentClient>()
            .AddScoped<GoogleAgentRunner>()
            .AddScoped<GoogleSpeechToTextAgentRunner>()
            .AddScoped((Func<IServiceProvider, AgentRunnerFactory>)(serviceProvider =>
                (provider, model) =>
                {
                    if (provider == AgentProvider.Google)
                    {
                        // agent runner for Google models
                        if (AgentModels.GoogleModels.Contains(model))
                        {
                            return serviceProvider.GetRequiredService<GoogleAgentRunner>();
                        }
                    }
                    else if (provider == AgentProvider.GoogleSpeechToText)
                    {
                        return serviceProvider.GetRequiredService<GoogleSpeechToTextAgentRunner>();
                    }
                    else if (provider == AgentProvider.AWS)
                    {
                        // temporaly default to google
                        return serviceProvider.GetRequiredService<GoogleAgentRunner>();
                    }

                    // default to google
                    throw new ArgumentException("Model not supported", nameof(model));
                }))
            .AddScoped((Func<IServiceProvider, PredictionServiceFactory>)(serviceProvider =>
                (location) =>
                {
                    return new PredictionServiceClientBuilder
                    {
                        Endpoint = $"{location}-aiplatform.googleapis.com",
                        Credential = credential
                    }.Build();
                }))
            .AddSingleton(_ =>
            {
                return new SpeechClientBuilder
                {
                    Endpoint = "speech.googleapis.com",
                    Credential = credential
                }.Build();
            })
            .AddScoped<TimerFactory>(provider => (interval) => new RealTimer(interval))
            .AddScoped(provider => new GoogleRegionStrategyFactory(provider.GetRequiredService<GoogleAgentConfiguration>(), provider.GetRequiredService<TimerFactory>(), 60000))
            .AddSingleton<IGoogleRegionStrategyHelper>(provider => new GoogleRegionStrategyHelper(provider.GetRequiredService<GoogleRegionStrategyFactory>()))
            .AddScoped<IGoogleBearerTokenProvider, GoogleBearerTokenProvider>()
            .RegisterKernel();

        return services;
    }

    private static void RegisterKernel(this IServiceCollection services)
    {
        services
            .AddTransient(serviceProvider =>
            {
                var model = AgentModels.MultiModal.Google.GeminiFlash20_001;
                var endpoint = serviceProvider.GetRequiredService<IGoogleRegionStrategyHelper>().GetEndpoint(model);

                return Kernel.CreateBuilder()
                    .AddVertexAIGeminiChatCompletion(
                        model,
                        () => serviceProvider.GetRequiredService<IGoogleBearerTokenProvider>().GetBearerToken(),
                        endpoint.Location,
                        endpoint.ProjectId,
                        Microsoft.SemanticKernel.Connectors.Google.VertexAIVersion.V1_Beta);
            })
            .AddTransient(serviceProvider =>
            {
                var factory = new AutonomousAgentFactory(serviceProvider.GetRequiredService<IKernelBuilder>(), serviceProvider);

                return factory;
            })
            .AddTransient((Func<IServiceProvider, ChatCompletionServiceFactory>)(serviceProvider =>
                    (agent) =>
                    {
                        var chatCompletionService = agent.Kernel.GetRequiredService<IChatCompletionService>();

                        return chatCompletionService;
                    }));
    }
}