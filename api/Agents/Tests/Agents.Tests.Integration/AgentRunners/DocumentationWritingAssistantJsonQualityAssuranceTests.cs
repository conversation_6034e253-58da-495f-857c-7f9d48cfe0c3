﻿using Agents.Module;
using Agents.Module.AgentRunners;
using Agents.Module.Agents;
using Agents.Module.Infrastructure.InversionOfControl;
using Agents.Module.Services;
using FluentAssertions;
using Google.Protobuf.WellKnownTypes;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Reflection;
using static Agents.Module.Agents.DocumentationWritingAssistant;

namespace Agents.Tests.Integration.AgentRunners;

public class DocumentationWritingAssistantJsonQualityAssuranceTests
{
    private readonly ServiceProvider serviceProvider;

    public DocumentationWritingAssistantJsonQualityAssuranceTests()
    {
        serviceProvider = new ServiceCollection()
            .RegisterAgentServices(new ConfigurationBuilder()
                .SetBasePath(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? string.Empty)
                .AddJsonFile("appsettings.Test.json")
                .Build())
            .AddScoped<TimerFactory>(provider => (interval) => new MockTimer(interval))
            .BuildServiceProvider();
    }

    [Theory(Skip = "This is for manual testing only")]
    [InlineData("{\"items\": [{\"PromptId\": \"123e4567-e89b-12d3-a456-426614174000\", \"Output\": \"Some output\"]}", "{\"items\": [{\"PromptId\": \"123e4567-e89b-12d3-a456-426614174000\", \"Output\": \"Some output\"}]}")]
    [InlineData("{\"items\": [{\"PromptId\": \"123e4567-e89b-12d3-a456-426614174000\", \"Output\": \"Unmatched quote}]}", "{\"items\": [{\"PromptId\": \"123e4567-e89b-12d3-a456-426614174000\", \"Output\": \"Unmatched quote\"}]}")]
    [InlineData("{\"items\": [{\"PromptId\": \"123e4567-e89b-12d3-a456-426614174000\", \"Output\": Missing closing quote}]}", "{\"items\": [{\"PromptId\": \"123e4567-e89b-12d3-a456-426614174000\", \"Output\": \"Missing closing quote\"}]}")]
    [InlineData("{\"items\": [{\"PromptId\": \"123e4567-e89b-12d3-a456-426614174000\" \"Output\": \"Missing comma\"}]}", "{\"items\": [{\"PromptId\": \"123e4567-e89b-12d3-a456-426614174000\", \"Output\": \"Missing comma\"}]}")]
    [InlineData("{\"items\": {\"PromptId\": \"123e4567-e89b-12d3-a456-426614174000\", \"Output\": \"Invalid array structure\"}}", "{\"items\": [{\"PromptId\": \"123e4567-e89b-12d3-a456-426614174000\", \"Output\": \"Invalid array structure\"}]}")]
    public async Task DocumentationJsonQualityAssuranceAgent_Tests(string brokenJson, string expectedJson)
    {
        using (var scope = serviceProvider.CreateScope())
        {
            var client = scope.ServiceProvider.GetRequiredService<IAgentClient>();

            var response = await client.Run<DocumentationAssistantResponse>(
                new DocumentationWritingAssistantJsonQualityAssurance(),
                DocumentationWritingAssistant.DocumentationPromptResponseSchema,
                new UserInput(Role.User, new TextUserInputPart(brokenJson)));

            var expectedResponse = JsonConvert.DeserializeObject<DocumentationAssistantResponse>(expectedJson);

            response.Output.Should().BeEquivalentTo(expectedResponse);
        }
    }

    [Fact(Skip = "This is for manual testing only")]
    public async Task DocumentationJsonQualityAssuranceAgent_InvalidPromptId_ReturnsError()
    {
        using (var scope = serviceProvider.CreateScope())
        {
            var client = scope.ServiceProvider.GetRequiredService<IAgentClient>();

            // Input JSON with an invalid PromptId (not a valid GUID)
            var brokenJson = "{\"items\": [{\"PromptId\": \"INVALID_GUID\", \"Output\": \"Some output\"}]}";

            var response = await client.Run<DocumentationAssistantResponse>(
                new DocumentationWritingAssistantJsonQualityAssurance(),
                DocumentationWritingAssistant.DocumentationPromptResponseSchema,
                new UserInput(Role.User, new TextUserInputPart(brokenJson)));

            // Assert: Output should be empty or contain an error message, depending on implementation
            // Adjust the assertion below to match your actual error handling
            response.Output.Items.Should().BeNullOrEmpty("because the PromptId is invalid");
            // Optionally, if your implementation returns an error message:
            // response.ResponseText.Should().Contain("invalid prompt id", StringComparison.OrdinalIgnoreCase);
        }
    }
}
