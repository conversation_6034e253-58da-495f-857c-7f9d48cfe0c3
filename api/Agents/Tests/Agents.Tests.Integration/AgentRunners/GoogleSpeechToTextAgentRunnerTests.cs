﻿using Agents.Module;
using Agents.Module.AgentRunners;
using Agents.Module.Agents;
using Agents.Module.Infrastructure.InversionOfControl;
using Agents.Module.Services;
using Agents.Sdk.Types;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace Agents.Tests.Integration.AgentRunners;

public class GoogleSpeechToTextAgentRunnerTests
{
    private readonly ServiceProvider serviceProvider;
    private string audioFilePath = Path.Combine("Resources", "SampleRecording.mp3");

    public GoogleSpeechToTextAgentRunnerTests()
    {
        serviceProvider = new ServiceCollection()
            .RegisterAgentServices(new ConfigurationBuilder()
                .SetBasePath(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? string.Empty)
                .AddJsonFile("appsettings.Test.json")
                .Build())
            .AddScoped<TimerFactory>(provider => (interval) => new MockTimer(interval))
            .BuildServiceProvider();
    }


    [Fact(Skip = "This is for manual testing only")]
    public async Task TranscriptionAgent_Test()
    {
        using (var scope = serviceProvider.CreateScope())
        {
            var client = scope.ServiceProvider.GetRequiredService<IAgentClient>();

            var response = await client.Run<TranscriptionAgent.TimestampOutputResponse>(
                new TranscriptionAgent(TranscriptionType.RecordedTranscription, AgentProvider.GoogleSpeechToText, AgentModels.MultiModal.Google.GeminiFlash25_Preview),
                TranscriptionAgent.TimestampOutputSchema,
                new UserInput(Role.User, new TextUserInputPart(TranscriptionAgent.AudioTranscriptionUserPrompt)),
                new UserInput(Role.User, new InlineDataUserInputPart(ConvertAudioToBase64(), "audio/mp3")));

            response?.Output?.Transcripts.Should().NotBeEmpty();
        }
    }

    private string ConvertAudioToBase64()
    {
        byte[] audioBytes = File.ReadAllBytes(audioFilePath);
        string base64String = Convert.ToBase64String(audioBytes);
        return base64String;
    }
}
