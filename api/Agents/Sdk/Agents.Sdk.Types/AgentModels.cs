using System;
using System.Reflection;

namespace Agents.Sdk.Types;

public static class AgentModels
{
    public static string[] GoogleModels = new[]
    {
        MultiModal.Google.GeminiPro,
        MultiModal.Google.GeminiFlash2,
        MultiModal.Google.GeminiPro2,
        MultiModal.Google.GeminiFlash20_001,
        MultiModal.Google.GeminiFlash25_Preview
    };

    public class MultiModal
    {
        public static class Google
        {
            public const string GeminiPro = "gemini-1.5-pro-001";

            public const string GeminiFlash2 = "gemini-1.5-flash-002";

            public const string GeminiPro2 = "gemini-1.5-pro-002";

            public const string GeminiFlash20_001 = "gemini-2.0-flash-001";

            public const string GeminiFlashThinking = "gemini-2.0-flash-thinking-exp-01-21";

            public const string GeminiFlash25_Preview = "gemini-2.5-flash-preview-04-17";
        }
    }

    public class Text
    {
        public static class Google
        {
        }
    }
}

public enum AgentProvider
{
    AWS,
    Google,
    GoogleSpeechToText,
}