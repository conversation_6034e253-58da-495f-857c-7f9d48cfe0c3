﻿using Agents.Module.Infrastructure.InversionOfControl;
using Agents.Sdk.Types.Kernel;
using carepatron.core.Abstractions;
using carepatron.core.Application.AIAgents;
using carepatron.core.Application.AIAgents.Plugins;
using carepatron.core.Application.AiAssistant.Services;
using carepatron.core.Application.AskAI.Services;
using carepatron.core.Application.Automation.Services;
using carepatron.core.Application.Booking.Service;
using carepatron.core.Application.Calendar.EventHandlers;
using carepatron.core.Application.Calendar.Services;
using carepatron.core.Application.Communications.Inbox.Abstractions;
using carepatron.core.Application.Communications.Inbox.Decorators;
using carepatron.core.Application.Communications.Inbox.Services;
using carepatron.core.Application.Communications.Notifications.Builders;
using carepatron.core.Application.Communications.Notifications.Builders.Arguments;
using carepatron.core.Application.Communications.Notifications.Resolvers;
using carepatron.core.Application.Communications.Notifications.Services;
using carepatron.core.Application.Communications.Notifications.Strategy;
using carepatron.core.Application.Communications.Notifications.Strategy.Scheduling;
using carepatron.core.Application.ConnectedApps.Services;
using carepatron.core.Application.Contacts.Services;
using carepatron.core.Application.Exports.Abstractions;
using carepatron.core.Application.Exports.Factories;
using carepatron.core.Application.Insurance.Services;
using carepatron.core.Application.Payments.Risk;
using carepatron.core.Application.Reminders.Services;
using carepatron.core.Application.Schema.Services;
using carepatron.core.Application.ServiceReceipts.Services;
using carepatron.core.Application.SmartChips.Abstractions;
using carepatron.core.Application.SmartChips.Services;
using carepatron.core.Application.Staff.Abstractions;
using carepatron.core.Application.Staff.Services;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Application.Tasks.Factory;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Tasks.Services;
using carepatron.core.Application.Trash.Services;
using carepatron.core.Common;
using carepatron.core.Configuration;
using carepatron.core.Events;
using carepatron.core.Events.Extensions;
using carepatron.core.Events.Handlers;
using carepatron.core.Infrastructure;
using carepatron.core.Models.Call;
using carepatron.core.Models.Storage;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Risk;
using carepatron.core.Risk.RuleProviders;
using carepatron.core.Services;
using carepatron.infra.chime;
using carepatron.infra.chime.InversionOfControl;
using carepatron.infra.cognito.InversionOfControl;
using carepatron.infra.common.InversionOfControl;
using carepatron.infra.common.Serialization;
using carepatron.infra.eventbridge.InversionOfControl;
using carepatron.infra.google.InversionOfControl;
using carepatron.infra.google.Services;
using carepatron.infra.importer.pms.InversionOfControl;
using carepatron.infra.microsoft.InversionOfControl;
using carepatron.infra.posthog.InversionOfControl;
using carepatron.infra.s3.InversionOfControl;
using carepatron.infra.s3.Services;
using carepatron.infra.ses.InversionOfControl;
using carepatron.infra.sql.InversionOfControl;
using carepatron.infra.sqs.InversionOfControl;
using carepatron.infra.stripe.InversionOfControl;
using carepatron.infra.telemetry;
using carepatron.infra.telemetry.Extensions;
using carepatron.infra.zoom;
using carepatron.infra.zoom.InversionOfControl;
using carepatron.workers.common;
using FileUtilities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Notifications.Sdk.Client;
using Serilog;
using System;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Factories;
using carepatron.core.Repositories.Templates;

namespace carepatron.workers.queue;

public class Program
{
    public static void Main(string[] args)
    {
        Log.Logger = new LoggerConfiguration()
            .ConfigureBootstrapLogger();
        AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(ErrorHandling.LogUnhandledException);

        try
        {
            CreateHostBuilder(args).Build().Run();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Host terminating. Fatal unhandled exception");
            Log.CloseAndFlush();
            throw;
        }
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseDefaultServiceProvider(options =>
            {
                // todo
                // Would be nice to enable this but muddy boundaries between
                // modules require a lot of unnecessary registrations.
                options.ValidateOnBuild = false;
            })
            .UseSerilog((hostingContext, services, loggerConfiguration) =>
            {
                loggerConfiguration.ConfigureLogger(hostingContext.Configuration);
            })
            .ConfigureSentryLoggingWithOtel()
            .ConfigureServices((hostContext, services) =>
            {
                IConfiguration configuration = hostContext.Configuration;

                JsonConvert.DefaultSettings = JsonSerializationSettings.GetJsonSerializerSettings;

                services
                    .AddWorkerOpenTelemetryWithSentry(hostContext.HostingEnvironment.ApplicationName)
                    .AddLocalization()
                    .AddMemoryCache()
                    .RegisterCore(configuration)
                    .RegisterInfra()
                    .RegisterSQLInfra(configuration)
                    .RegisterSQSInfra(configuration)
                    .RegisterGoogleInfra(configuration)
                    .RegisterMicrosoftInfra(configuration)
                    .RegisterZoomInfra(configuration)
                    .RegisterStripeInfra(configuration)
                    .RegisterSESInfra(configuration)
                    .RegisterImporterPmsInfra(configuration)
                    .RegisterS3Infra(configuration)
                    .RegisterAgentServices(configuration)
                    .RegisterCognitoInfra(configuration)
                    .RegisterZoomInfra(configuration)
                    .RegisterEventBridgeInfra(configuration)
                    .RegisterPostHogInfra(configuration)
                    .RegisterGoogleCloudStorage(configuration)
                    .RegisterChimeInfra(configuration)
                    .AddScoped<IServiceReceiptService, ServiceReceiptService>()
                    .AddScoped<IConnectedAppService, ConnectedAppService>()
                    .AddScoped<IScheduledEventService, ScheduledEventService>()
                    .AddScoped<IUriProvider, UriProvider>()
                    .AddSingleton<IDateTimeProvider, DateTimeProvider>()
                    .AddScoped<IReminderService, ReminderService>()
                    .AddScoped<IConnectedCalendarService, ConnectedCalendarService>()
                    .AddScoped<IEventHandlerDelegator, EventHandlerDelegator>()
                    .AddScoped<IImportContactsService, ImportContactsService>()
                    .AddScoped<IExportContactService, ExportContactService>()
                    .AddScoped<IRiskAssessment<PaymentRiskRequest>, FastPaymentRiskAssessment>()
                    .AddScoped<IRiskAssessment<StripeTransferRiskAssessmentRequest>, StripeTransferRiskAssessment>()
                    .AddScoped<IRiskAssessmentService, RiskAssessmentService>()
                    .AddScoped<IAudioProcessingService, AudioProcessingService>()
                    .AddScoped<IRiskAssessmentProvider, RiskAssessmentProvider>()
                    .AddScoped<core.Application.Contacts.Abstractions.IContactService, ContactService>()
                    .AddScoped<ITrashService, TrashService>()
                    .AddScoped<IInboxMessageService, InboxMessageService>()
                    .AddScoped<IInboxService, InboxService>()
                    .AddScoped<IExecutionStrategyFactory, ExecutionStrategyFactory>()
                    .AddScoped<IExportEventHandlerFactory, ExportEventHandlerFactory>()
                    .AddScoped<ITaskService, TaskService>()
                    .AddScoped<ICalendarService, CalendarService>()
                    .AddScoped<IOutlookSyncService, OutlookInboxSyncService>()
                    .AddScoped<SyncGoogleExternalEventsProcessor>()
                    .AddScoped<SyncGoogleInternalEventsProcessor>()
                    .AddScoped<GoogleCalendarSyncService>()
                    .AddScoped<IConnectedCalendarRepositoryFactory, ConnectedCalendarRepositoryFactory>()
                    .AddScoped<IConnectedAppCalendarServiceFactory, ConnectedAppCalendarServiceFactory>()
                    .AddScoped<IImportContactPreprocessorFactory, ImportContactPreprocessorFactory>()
                    .AddScoped<IConnectedAppCalendarService, GoogleConnectedAppCalendarService>()
                    .AddScoped<ICallService, CallService>()
                    .AddScoped<IStaffScheduleService, StaffScheduleService>()
                    .AddScoped<IBookingService, BookingService>()
                    .AddScoped<IInsuranceService, InsuranceService>()
                    .AddScoped<ISchemaService, SchemaService>()
                    .AddScoped<IChatHistoryReducerService, ChatHistoryReducerService>()
                    .AddScoped<IContextRetrievalService, ContextRetrievalService>()
                    .AddScoped<IAssistantService, AiAgentAssistantService>()
                    .AddScoped<IAiContextConverter, AiContextConverter>()
                    .AddScoped<IAppointmentNotificationEmailService, AppointmentNotificationEmailService>()
                    .AddScoped<INotificationTemplateService, NotificationTemplateService>()
                    .AddScoped<INotificationTemplateBuilder, NotificationTemplateBuilder>()
                    .AddScoped<INotificationTemplateContentBuilderStrategy, AppointmentUpcomingReminderTemplateContentDeliveryStrategy>()
                    .AddScoped<INotificationTemplateContentBuilderStrategy, AppointmentConfirmedTemplateContentDeliveryStrategy>()
                    .AddScoped<INotificationTemplateContentBuilderStrategy<DefaultTemplateArgument, PreviewStrategyArguments>, DefaultTemplateContentStrategy>()
                    .AddScoped<ITaskStatusService, TaskStatusService>()
                    .AddScoped<IImportContactMappingService, ImportContactMappingService>()
                    .AddScoped<ITempTemplateProfessionCsvRepository, TempTemplateProfessionCsvRepository>()
                    // Smart chip resolvers
                    .AddScoped<ISmartChipResolverService, SmartChipResolverService>()
                    .Scan(scan => scan
                        .FromAssemblyOf<SmartChipResolverService>()
                        .AddClasses(classes => classes.AssignableTo<ISmartChipResolverStrategy>())
                        .AsImplementedInterfaces()
                        .WithScopedLifetime())
                    .Scan(scan =>
                    {
                        var assembly = scan.FromAssemblyOf<IEventHandlerDelegator>();

                        assembly.AddClasses(x => x.AssignableTo(typeof(IEventHandler<>)))
                            .AsSelf()
                            .AsImplementedInterfaces()
                            .WithScopedLifetime();

                        assembly.AddClasses(x => x.AssignableTo(typeof(IExportHandler)))
                            .AsSelf()
                            .AsImplementedInterfaces()
                            .WithScopedLifetime();

                    })
                    .AddSingleton<IAiTemplatesConfiguration>(configuration.GetSection("AiTemplatesConfiguration").Get<AiTemplatesConfiguration>())
                    // worker
                    .AddScoped<IWorkerExecution, WorkerExecution>()
                    //ensure new worker created for each hosted service
                    .AddTransient<Worker>()
                    // 3 instances of worker
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>())
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>())
                    .AddSingleton<IHostedService>(sp => sp.GetRequiredService<Worker>());

                services.AddScoped((Func<IServiceProvider, FileStorageServiceFactory>)(serviceProvider => provider =>
                {
                    switch (provider)
                    {
                        case StorageProvider.Google:
                            return serviceProvider.GetRequiredService<GoogleFileStorageService>();
                        case StorageProvider.AWS:
                        default:
                            return serviceProvider.GetRequiredService<AwsFileStorageService>();
                    }
                }));

                services.Decorate<IConnectedInboxRepository, ConnectedInboxErrorHandlingDecorator>();

                services.AddScoped((Func<IServiceProvider, CallProviderFactory>)(serviceProvider => (CallProvider provider) =>
                {
                    switch (provider)
                    {
                        case CallProvider.Zoom:
                            return serviceProvider.GetRequiredService<ZoomCallProvider>();

                        case CallProvider.AWS:
                        default:
                            return serviceProvider.GetRequiredService<ChimeCallProvider>();
                    }
                }));

                services.Decorate<IConnectedCalendarRepository, ConnectedCalendarLoggingDecorator>();
                services.Decorate<IConnectedInboxRepository, ConnectedInboxLoggingDecorator>();

                services.AddNotificationService<PersonActorResolver>(configuration);

                services.AddScoped((Func<IServiceProvider, CalendarSyncServiceFactory>)(serviceProvider => (ExternalEventType externalEventType) =>
                {
                    return externalEventType switch
                    {
                        ExternalEventType.Google => serviceProvider.GetRequiredService<GoogleCalendarSyncService>(),
                        ExternalEventType.Microsoft => throw new NotImplementedException(),
                        _ => throw new NotSupportedException($"ExternalEventType {externalEventType} is not supported")
                    };
                }));

                services.RegisterMessageHandlersByAttribute();

                // not required in the worker; causes DI issues due to StripeWebhook dependency on mediatr.
                services.RemoveAll<IStripeWebhookService>();

                ConfigureAutonomousAgents(services);
            });

    public static void ConfigureAutonomousAgents(IServiceCollection services)
    {
        services
            // agents
            .AddAutonomousAgent<ChatHistoryAutonomousAgent>();

        services
            .AddTransientPlugin<BookingManagementPlugin>()
            .AddTransientPlugin<BookingSchedulingPlugin>();


        // Inbox Agent
        services
            .AddAutonomousAgent<InboxMessageAutomationAutonomousAgent>()
                .WithPlugin<BookingSchedulingPlugin>()
                .WithPlugin<BookingManagementPlugin>();
    }
}