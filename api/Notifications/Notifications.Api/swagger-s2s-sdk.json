{"x-generator": "NSwag v14.0.7.0 (NJsonSchema v11.0.0.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "Notification Internal APIs", "version": "1.0.0"}, "paths": {"/temp/notfound": {"get": {"operationId": "GetTempNotfound", "responses": {"200": {"description": ""}}}}, "/temp/forbidden": {"get": {"operationId": "GetTempForbidden", "responses": {"200": {"description": ""}}}}, "/temp/conflict": {"get": {"operationId": "GetTempConflict", "responses": {"200": {"description": ""}}}}, "/temp/domainerror": {"get": {"operationId": "GetTempDomainerror", "responses": {"200": {"description": ""}}}}, "/api/notifications/notify": {"post": {"tags": ["Internal Apis"], "operationId": "PostApiNotificationsNotify", "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendRealTimeNotificationRequest"}}}, "required": true, "x-position": 1}, "responses": {"404": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "409": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/api/notifications/send": {"post": {"tags": ["Internal Apis"], "operationId": "PostApiNotificationsSend", "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendNotificationRequest"}}}, "required": true, "x-position": 1}, "responses": {"404": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "409": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/api/notifications/email/send": {"post": {"tags": ["Internal Apis"], "operationId": "PostApiNotificationsEmailSend", "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailRequest"}}}, "required": true, "x-position": 1}, "responses": {"404": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "409": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/api/notifications/sms/send": {"post": {"tags": ["Internal Apis"], "operationId": "PostApiNotificationsSmsSend", "responses": {"404": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "409": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}}}}, "/api/notifications/providers/{groupId}/templates/{templateId}": {"get": {"tags": ["Internal Apis"], "operationId": "GetApiNotificationsProvidersTemplates", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "templateId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}, {"name": "isSystem", "in": "query", "required": true, "schema": {"type": "boolean"}, "x-position": 3}], "responses": {"404": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "409": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateSummaryResponse"}}}}}}}, "/api/notifications/providers/{groupId}/templates/render": {"post": {"tags": ["Internal Apis"], "operationId": "PostApiNotificationsProvidersTemplatesRender", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RenderNotificationTemplateByContextRequest"}}}, "required": true, "x-position": 2}, "responses": {"404": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "409": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RenderNotificationTemplateResponse"}}}}}}}, "/api/notifications/providers/{groupId}/templates/{templateId}/render": {"post": {"tags": ["Internal Apis"], "operationId": "PostApiNotificationsProvidersTemplatesRender2", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "templateId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}], "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateRequest"}}}, "required": true, "x-position": 3}, "responses": {"404": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "409": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RenderNotificationTemplateResponse"}}}}}}}, "/api/notifications/providers/{groupId}/templates/{templateId}/preview": {"post": {"tags": ["Internal Apis"], "operationId": "PostApiNotificationsProvidersTemplatesPreview", "parameters": [{"name": "groupId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 1}, {"name": "templateId", "in": "path", "required": true, "schema": {"type": "string", "format": "guid"}, "x-position": 2}], "requestBody": {"x-name": "request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateRequest"}}}, "required": true, "x-position": 3}, "responses": {"404": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "500": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "409": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "403": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Problem"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationTemplateResponse"}}}}}}}}, "components": {"schemas": {"Problem": {"type": "object", "additionalProperties": false, "properties": {"status": {"$ref": "#/components/schemas/ProblemStatus"}, "type": {"type": "string"}, "title": {"type": "string"}, "detail": {"type": "string"}, "data": {"type": "object", "additionalProperties": {}}}}, "ProblemStatus": {"type": "integer", "description": "", "x-enumNames": ["BadRequest", "Forbidden", "NotFound", "Conflict", "ValidationError", "InternalServer"], "enum": [400, 403, 404, 409, 422, 500]}, "UserNotificationSettingResponse": {"type": "object", "additionalProperties": false, "properties": {"providerId": {"type": "string", "format": "guid"}, "personId": {"type": "string", "format": "guid"}, "preferences": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationPreferenceResponse"}}}}, "NotificationPreferenceResponse": {"type": "object", "additionalProperties": false, "properties": {"category": {"type": "string"}, "channels": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationChannelResponse"}}}}, "NotificationChannelResponse": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "enabled": {"type": "boolean"}}}, "UpdateUserNotificationSettingRequest": {"type": "object", "additionalProperties": false, "properties": {"providerId": {"type": "string", "format": "guid"}, "personId": {"type": "string", "format": "guid"}, "preferences": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateNotificationPreferenceRequest"}}}}, "UpdateNotificationPreferenceRequest": {"type": "object", "additionalProperties": false, "properties": {"category": {"type": "string"}, "channels": {"type": "array", "items": {"$ref": "#/components/schemas/UpdateNotificationChannelRequest"}}}}, "UpdateNotificationChannelRequest": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "enabled": {"type": "boolean"}}}, "SendRealTimeNotificationRequest": {"type": "object", "additionalProperties": false, "properties": {"recipients": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationRecipientsRequest"}}, "notificationType": {"type": "string"}, "payload": {"nullable": true}}}, "NotificationRecipientsRequest": {"type": "object", "additionalProperties": false, "properties": {"groupId": {"type": "string", "format": "guid"}, "personIds": {"type": "array", "items": {"type": "string", "format": "guid"}}}}, "SendNotificationRequest": {"type": "object", "additionalProperties": false, "properties": {"recipients": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationRecipientsRequest"}}, "notificationCategory": {"$ref": "#/components/schemas/NotificationCategoryRequest"}, "notificationType": {"type": "string"}, "parameters": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationParameterRequest"}}, "actorId": {"type": "string"}, "actorProfile": {"$ref": "#/components/schemas/NotificationActorProfileRequest"}, "version": {"type": "string"}}}, "NotificationCategoryRequest": {"type": "integer", "description": "", "x-enumNames": ["SCHEDULING", "BILLING_PAYMENT", "CLIENT_DOCUMENT", "WORKSPACE", "COMMUNICATIONS", "CONTACT_IMPORT"], "enum": [0, 1, 2, 3, 4, 5]}, "NotificationParameterRequest": {"type": "object", "additionalProperties": false, "properties": {"key": {"type": "string"}, "value": {"type": "string"}}}, "NotificationActorProfileRequest": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "profilePhotoId": {"type": "string", "format": "guid", "nullable": true}}}, "InAppNotificationFilterTypes": {"type": "integer", "description": "", "x-enumNames": ["All", "Unread", "Read", "Ignored"], "enum": [0, 1, 2, 3]}, "PollingInAppNotificationResponse": {"type": "object", "additionalProperties": false, "properties": {"count": {"type": "integer", "format": "int32"}}}, "EmailRequest": {"type": "object", "additionalProperties": false, "properties": {"from": {"$ref": "#/components/schemas/EmailAddress"}, "to": {"type": "array", "items": {"$ref": "#/components/schemas/EmailAddress"}}, "subject": {"type": "string"}, "body": {"type": "string"}, "isHtml": {"type": "boolean"}, "cc": {"type": "array", "items": {"$ref": "#/components/schemas/EmailAddress"}}, "bcc": {"type": "array", "items": {"$ref": "#/components/schemas/EmailAddress"}}, "replyTo": {"type": "array", "items": {"$ref": "#/components/schemas/EmailAddress"}}, "attachments": {"type": "array", "items": {"$ref": "#/components/schemas/AttachmentRequest"}}, "metadata": {"type": "object", "additionalProperties": {"type": "string"}}}}, "EmailAddress": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "address": {"type": "string"}}}, "AttachmentRequest": {"type": "object", "additionalProperties": false, "properties": {"fileName": {"type": "string"}, "content": {"type": "string", "format": "byte"}}}, "NotificationTemplateSummaryResponse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "groupId": {"type": "string", "format": "guid"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "version": {"type": "string"}, "layoutName": {"type": "string"}, "isEnabled": {"type": "boolean"}, "isSystem": {"type": "boolean"}, "context": {"$ref": "#/components/schemas/NotificationTemplateContext"}, "workflows": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationTemplateWorkflow"}}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "createdByPersonId": {"type": "string", "format": "guid", "nullable": true}, "updatedByPersonId": {"type": "string", "format": "guid", "nullable": true}, "localisations": {"type": "array", "items": {"type": "string"}}, "defaultLocale": {"type": "string"}}}, "NotificationTemplateContext": {"type": "object", "additionalProperties": false, "properties": {"notificationType": {"type": "string"}, "notificationCategory": {"type": "string"}, "recommendedSchemas": {"type": "array", "items": {"type": "string"}}}}, "NotificationTemplateWorkflow": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string"}}}, "NotificationTemplateResponse": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "format": "guid"}, "groupId": {"type": "string", "format": "guid"}, "name": {"type": "string"}, "description": {"type": "string"}, "type": {"type": "string"}, "content": {"type": "string"}, "version": {"type": "string"}, "layoutName": {"type": "string"}, "isEnabled": {"type": "boolean"}, "isSystem": {"type": "boolean"}, "context": {"$ref": "#/components/schemas/NotificationTemplateContext"}, "components": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationTemplateComponent"}}, "workflows": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationTemplateWorkflow"}}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "createdByPersonId": {"type": "string", "format": "guid", "nullable": true}, "updatedByPersonId": {"type": "string", "format": "guid", "nullable": true}, "defaultLocale": {"type": "string"}, "localisations": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationTemplateLocalisation"}}}, "defaultLocalisations": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationTemplateLocalisation"}}}}}, "NotificationTemplateComponent": {"type": "object", "additionalProperties": false, "properties": {"key": {"type": "string"}, "groupKey": {"type": "string"}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationTemplateComponentConfiguration"}}}}, "NotificationTemplateComponentConfiguration": {"type": "object", "additionalProperties": false, "properties": {"key": {"type": "string"}, "value": {}, "type": {"type": "string"}, "isEditable": {"type": "boolean"}}}, "NotificationTemplateLocalisation": {"type": "object", "additionalProperties": false, "properties": {"componentKey": {"type": "string"}, "propertyKey": {"type": "string"}, "localisedValue": {"type": "string"}}}, "UpsertNotificationTemplateRequest": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "version": {"type": "string"}, "isEnabled": {"type": "boolean"}, "isSystem": {"type": "boolean"}, "components": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationTemplateComponent"}}, "defaultLocale": {"type": "string"}, "localisations": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationTemplateLocalisation"}}}}}, "RenderNotificationTemplateResponse": {"type": "object", "additionalProperties": false, "properties": {"content": {"type": "string"}, "subject": {"type": "string"}}}, "RenderNotificationTemplateByContextRequest": {"type": "object", "additionalProperties": false, "properties": {"notificationType": {"type": "string"}, "category": {"$ref": "#/components/schemas/NotificationCategoryRequest"}, "type": {"type": "string"}, "templateParams": {"type": "object", "additionalProperties": {}}, "locale": {"type": "string"}}}, "NotificationTemplateRequest": {"type": "object", "additionalProperties": false, "properties": {"templateParams": {"type": "object", "additionalProperties": {}}, "isSystem": {"type": "boolean"}, "locale": {"type": "string"}}}}}}