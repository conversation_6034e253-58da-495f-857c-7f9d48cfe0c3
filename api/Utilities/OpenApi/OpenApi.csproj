﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        
        <!-- Do not generate OpenAPI by default -->
        <Generate>false</Generate>
        
    </PropertyGroup>


    <ItemGroup>
        <PackageReference Include="NSwag.CodeGeneration" Version="14.4.0" />
        <PackageReference Include="NSwag.CodeGeneration.CSharp" Version="14.4.0" />
        <PackageReference Include="NSwag.CodeGeneration.TypeScript" Version="14.4.0" />
        <PackageReference Include="NSwag.MSBuild" Version="14.4.0">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>
    
    <Target Name="GenerateOpenApi"  Condition="'$(Generate)' == 'true'" DependsOnTargets="Build">
        <PropertyGroup>
            <ApiProjectPath>../../carepatron.api/carepatron.api.csproj</ApiProjectPath>
            <OutputPath>../../../openapi/carepatron-api-v1.json</OutputPath>
        </PropertyGroup>

        <!-- Ensure the output directory exists -->
        <MakeDir Directories="../../../openapi" />

        <!-- Generate the OpenAPI spec -->
        <Exec
            EnvironmentVariables="ASPNETCORE_ENVIRONMENT=Development"
            Command="$(NSwagExe_Net80) aspnetcore2openapi /nobuild:true /project:$(ApiProjectPath) /documentName:v1 /output:$(OutputPath)"
            ContinueOnError="true" />

        <Message Text="OpenAPI file generated at: $(OutputPath)" Importance="high" />
    </Target>
    
</Project>
