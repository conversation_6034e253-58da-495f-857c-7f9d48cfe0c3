﻿using carepatron.core.Application.Communications.Inbox.Extensions;
using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Application.Communications.Inbox.Processor;
using carepatron.core.Extensions;
using carepatron.infra.google.Extensions;
using carepatron.infra.google.Models;
using carepatron.infra.google.Models.Enums;
using Google.Apis.Gmail.v1.Data;
using Google.Apis.Oauth2.v2.Data;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace carepatron.infra.google.Mappers
{
    public interface IMessageMapper
    {
        InboxMessage[] Map(Guid providerId, Guid inboxId, IEnumerable<Message> messages, ListSendAsResponse sendAsInfo, Userinfo userInfo, DateTime syncTimestamp);

        InboxMessage Map(Guid providerId, Guid inboxId, Message message, ListSendAsResponse sendAsInfo, Userinfo userInfo, DateTime syncTimestamp);

        Message CreateMessage(SenderInfo senderInfo, Recipient recipients, string messageSubject, string messageBody, string emailRfcMessageId, MessageType messageType, MessageStatus messageStatus,
            Message messageToReplyTo = null, AttachmentStream[] attachments = null);
    }

    public class MessageMapper([FromKeyedServices(MessageBodyProcessorType.Incoming)] IMessageBodyProcessor incomingMessageProcessor,
        [FromKeyedServices(MessageBodyProcessorType.Outgoing)] IMessageBodyProcessor outgoingMessageProcessor) : IMessageMapper
    {
        private readonly IMessageBodyProcessor incomingMessageBodyProcessor = incomingMessageProcessor;
        private readonly IMessageBodyProcessor outgoingMessageProcessor = outgoingMessageProcessor;

        // This regex is used to split the header value using comma as delimiter ignoring those commas within quotes
        private readonly Regex commaSplitter = new(",(?=(?:[^\"]*\"[^\"]*\")*(?![^\"]*\"))");

        // This regex is used to split the recipient into displayname and account id groups
        private readonly Regex recipientSplitter = new("^(?<DisplayName>.*)\\s*<(?<AccountId>.*)>$");

        public InboxMessage[] Map(Guid providerId, Guid inboxId, IEnumerable<Message> messages, ListSendAsResponse sendAsInfo, Userinfo userInfo, DateTime syncTimestamp)
        {
            // Do a coalesce operation to handle null messages
            messages ??= [];

            return messages
                .Where(x => IsValid(x))
                .Select(x => Map(providerId, inboxId, x, sendAsInfo, userInfo, syncTimestamp))
                .ToArray();
        }

        public InboxMessage Map(Guid providerId, Guid inboxId, Message message, ListSendAsResponse sendAsInfo, Userinfo userInfo, DateTime syncTimestamp)
        {
            // Populate data from message payload
            var inboxMessage = new InboxMessage()
            {
                Id = Guid.NewGuid(),
                InboxId = inboxId,
                ExternalSource = ExternalSource.Gmail,
                ExternalSourceMessageId = message.Id,
                DownloadTimestamp = syncTimestamp,
                MessagePreview = message.Snippet,
                Recipients = new Recipient(),
                ExternalConversationId = message.ThreadId,
                IsRead = (message.LabelIds ?? []).Contains("SENT", StringComparer.InvariantCultureIgnoreCase), // If message has SENT label, mark the message as read by default (this logic may need to be refactored if there is a need to store a label id property)
                Status = MessageStatus.Default,
                StatusChangedAt = syncTimestamp
            };

            var messageHeaders = message.GetPayloadHeaders();

            var primarySendAs = sendAsInfo.SendAs.FirstOrDefault(x => x.IsPrimary == true);
            var toAccountId = !primarySendAs?.SendAsEmail.IsNullOrWhiteSpace() ?? false ? primarySendAs.SendAsEmail : userInfo.Email;
            var toDisplayName = !primarySendAs?.DisplayName.IsNullOrWhiteSpace() ?? false ? primarySendAs.DisplayName : userInfo.Name;
            inboxMessage.To = !toDisplayName.IsNullOrWhiteSpace()
                ? new RecipientDetail(toDisplayName, toAccountId)
                : new RecipientDetail(toAccountId);

            inboxMessage.EmailRfcMessageId = message.Payload.Headers.GetHeaderValue(MessageContentHeaders.MessageId);

            inboxMessage.CreatedAt = (message.InternalDate.HasValue)
                ? DateTimeOffset.FromUnixTimeMilliseconds(message.InternalDate.Value).UtcDateTime
                : DateTimeOffset.Parse(messageHeaders[nameof(MessageHeader.Date)]).UtcDateTime;

            // Still need to leave this First() call, as we are not sure if there will be multiple from addresses
            inboxMessage.From = GetRecipientDetails(messageHeaders[nameof(MessageHeader.From)]).First();

            // Only map the to header if it is not "undisclosed-recipients:;"
            // Gmail has a quirk where it uses "undisclosed-recipients:;" for messages with blank To field but with BCC recipients
            // To omit this, we check if the To header is present and not equal to "undisclosed-recipients:;"
            // This is so as to not create a to recipient / conversation participant with undisclosed recipient that is reflected in the FE
            if (messageHeaders.TryGetValue(nameof(MessageHeader.To), out string toValue)
                && toValue.Trim() != "undisclosed-recipients:;")
            {
                inboxMessage.Recipients.To = GetRecipientDetails(toValue);
            }

            if (messageHeaders.TryGetValue(nameof(MessageHeader.Cc), out string ccValue)) inboxMessage.Recipients.Cc = GetRecipientDetails(ccValue);
            if (messageHeaders.TryGetValue(nameof(MessageHeader.Bcc), out string bccValue)) inboxMessage.Recipients.Bcc = GetRecipientDetails(bccValue);

            inboxMessage.MessageSubject = messageHeaders.ContainsKey(nameof(MessageHeader.Subject)) 
                ? messageHeaders[nameof(MessageHeader.Subject)] ??= string.Empty
                : string.Empty;

            var messageParts = message.Payload.Parts ?? new List<MessagePart>()
            {
                message.Payload
            };

            var (htmlTextBody, plainTextBody) = GetBody(messageParts);

            var attachments = GetAttachments(inboxMessage.Id, messageParts, syncTimestamp);

            inboxMessage.MessageType = MessageType.PlainText;
            inboxMessage.MessageBody = plainTextBody;

            // Do another strip to ensure that html is safe and clear of incomprehensible content
            inboxMessage.MessageBodyPlainText = plainTextBody.StripHtmlTags();

            if (!htmlTextBody.IsNullOrWhiteSpace())
            {
                inboxMessage.MessageType = MessageType.Html;

                var (processedHtmlBody, processedAttachments) = ProcessMessageBodyAndAttachments(htmlTextBody, attachments);

                // Set the message body to the processed value with embedded attachments
                inboxMessage.MessageBody = processedHtmlBody;

                // Set the attachments to the processed value with is embedded populated
                attachments = processedAttachments;
            }

            inboxMessage.MessageAttachments = attachments;
            inboxMessage.Conversation = inboxMessage.CreateConversation(providerId, Guid.NewGuid(), syncTimestamp);

            return inboxMessage;
        }

        public Message CreateMessage(SenderInfo sender,
            Recipient recipients,
            string messageSubject,
            string messageBody,
            string emailRfcMessageId,
            MessageType messageType,
            MessageStatus messageStatus,
            Message messageToReplyTo = null,
            AttachmentStream[] attachments = null)
        {
            var messageBuilder = new MessageBuilder()
                .WithFrom(sender)
                .WithRecipients(recipients)
                .WithSubject(messageSubject)
                .WithBody(messageBody)
                .WithAttachments(attachments);

            messageBuilder = (messageType == MessageType.Html)
                ? messageBuilder.AsHtml()
                : messageBuilder;

            // If message is a reply, ensure to add the InReplyTo and References headers
            if (!emailRfcMessageId.IsNullOrEmpty())
            {
                var replyMessageHeaders = new Dictionary<string, string>()
                {
                    { MessageContentHeaders.InReplyTo, emailRfcMessageId },
                    { MessageContentHeaders.References, emailRfcMessageId }
                };

                messageBuilder = messageBuilder.WithHeaders(replyMessageHeaders);
            }

            // Build mail message
            var mailMessage = messageBuilder.Build();

            if (mailMessage.IsBodyHtml)
            {
                // Process outgoing message body
                var builder = new MessageBodyProcessorPayloadBuilder(mailMessage.Body);

                mailMessage.Attachments
                    .Zip(attachments ?? [],
                        (mailAttachment, streamAttachment) => new { mailAttachment, streamAttachment })
                    .ToList()
                    .ForEach(x =>
                    {
                        builder.WithAttachmentContent(x.mailAttachment.ContentId, x.streamAttachment.Id, x.streamAttachment.Source);
                    });

                var procesorResponse = outgoingMessageProcessor.Process(builder.Build());

                mailMessage.Body = procesorResponse.MessageBody;
            }

            return new Message()
            { 
                Raw = mailMessage.ToMimeMessage().ToEncodedRawMessage(),
                ThreadId = messageToReplyTo?.ThreadId,
                LabelIds = (messageStatus.ToGmailLabel().IsNullOrEmpty())
                    ? [MessageExtensions.GmailLabelSent]
                    : [MessageExtensions.GmailLabelSent, messageStatus.ToGmailLabel()],
            };
        }

        private RecipientDetail[] GetRecipientDetails(string headerValue)
        {
            // Commasplitter splits the header value based on comma delimiter disregarding commas within quotes
            // i.e input -> Test User <<EMAIL>>, <EMAIL>, "Test User 1, Test User 2" <<EMAIL>>
            // i.e output -> [Test User <<EMAIL>>, <EMAIL>, "Test User 1, Test User 2" <<EMAIL>>]
            var recipients = commaSplitter.Split(headerValue)
                .Select(x =>
                {
                    var match = recipientSplitter.Match(x);

                    // If regex match fails, then we can assume that the recipient is an email address only
                    // i.e <EMAIL>
                    if (!match.Success) return new RecipientDetail(x.CleanForMessageRecipient());

                    // If regex match success, then we can assume that the recipient has both display name and account id
                    // i.e Test User <<EMAIL>>
                    var displayName = match.Groups["DisplayName"].Value.CleanForMessageRecipient();
                    var accountId = match.Groups["AccountId"].Value.CleanForMessageRecipient();

                    if (displayName.IsNullOrWhiteSpace()) return new RecipientDetail(accountId);

                    return new RecipientDetail(displayName, accountId);
                })
                .ToArray();

            return recipients;
        }

        private (string htmlTextBody, string plainTextBody) GetBody(IList<MessagePart> messageParts)
        {
            return GetBodyParts(messageParts, "", "");
        }

        private (string htmlTextBody, string plainTextBody) GetBodyParts(IList<MessagePart> parts, string htmlText, string plainText)
        {
            string htmlBody = htmlText;
            string plainBody = plainText;

            if (parts == null)
            {
                return (htmlBody, plainBody);
            }
            else
            {
                foreach (var part in parts)
                {
                    if (part.Parts == null)
                    {
                        if (part.Body?.Data != null)
                        {
                            // Only fetch for plain / html mimetype for now. This can be extended for attachements later on
                            var decodedBodyData = part.Body.Data.DecodeBase64String();
                            if (part.MimeType == "text/html") htmlBody += decodedBodyData;
                            if (part.MimeType == "text/plain") plainBody += decodedBodyData;
                        }
                    }
                    else
                    {
                        return GetBodyParts(part.Parts, htmlBody, plainBody);
                    }
                }

                return (htmlBody, plainBody);
            }
        }

        private InboxMessageAttachment[] GetAttachments(Guid inboxMessageId, IList<MessagePart> parts, DateTime syncTimestamp)
        {
            var attachments = GetAttachmentParts(parts, inboxMessageId, syncTimestamp, []);
            return [.. attachments];
        }

        private List<InboxMessageAttachment> GetAttachmentParts(IList<MessagePart> parts,
            Guid inboxMessageId,
            DateTime syncTimestamp,
            List<InboxMessageAttachment> processedAttachments)
        {
            var attachments = processedAttachments ?? [];

            if (parts == null)
            {
                return attachments;
            }
            else
            {
                foreach (var part in parts)
                {
                    if (part.Parts == null)
                    {
                        if (IsAttachmentPart(part))
                        {
                            var messageAttachment = new InboxMessageAttachment()
                            {
                                Id = Guid.NewGuid(),
                                MediaId = Guid.NewGuid(),
                                ExternalFileId = part.Body.AttachmentId,
                                InboxMessageId = inboxMessageId,
                                FileName = part.Filename,
                                FileExtensions = part.Filename.GetAttachmentExtension(),
                                FileSize = part.Body.Size ?? 0,
                                ContentType = part.MimeType,
                                CreatedAt = syncTimestamp,
                                ContentId = part.Headers.GetHeaderValue(MessageContentHeaders.ContentId).Trim('<', '>')
                            };

                            attachments.Add(messageAttachment);
                        }
                    }
                    else
                    {
                        attachments = GetAttachmentParts(part.Parts, inboxMessageId, syncTimestamp, attachments);
                    }
                }

                return attachments;
            }
        }

        private bool IsAttachmentPart(MessagePart part)
        {
            return !part.Filename.IsNullOrWhiteSpace() &&
                part.Body != null &&
                !part.Body.AttachmentId.IsNullOrWhiteSpace();
        }

        private (string processedHtmlBody, InboxMessageAttachment[] processedAttachments) ProcessMessageBodyAndAttachments(string htmlBody, InboxMessageAttachment[] attachments)
        {
            var builder = new MessageBodyProcessorPayloadBuilder(htmlBody);

            var attachmentsContentIds = attachments
                .Where(x => !x.ContentId.IsNullOrWhiteSpace())
                .ToArray();

            foreach (var attachment in attachmentsContentIds)
            {
                builder.WithAttachmentContent(attachment.ContentId, attachment.Id);
            };

            var response = incomingMessageBodyProcessor.Process(builder.Build());

            attachments.ToList()
                .ForEach(x => x.IsEmbedded = response.EmbeddedAttachmentIds.Contains(x.Id));

            return (response.MessageBody, attachments.Where(x => !response.EmbeddedAttachmentIdsToRemove.Contains(x.Id)).ToArray());
        }

        private bool IsValid(Message message)
        {
            if (message?.Payload?.Headers is null) return false;

            var messageHeaders = message.GetPayloadHeaders();

            return messageHeaders.ContainsKey(nameof(MessageHeader.From));
        }
    }
}