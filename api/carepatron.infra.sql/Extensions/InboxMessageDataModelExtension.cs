﻿using carepatron.core.Application.Communications.Inbox.Builders;
using carepatron.core.Application.Communications.Inbox.Extensions;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Extensions;
using carepatron.infra.sql.Models.Inbox;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.infra.sql.Extensions
{
    public static class InboxMessageDataModelExtension
    {
        // Filter by all inboxes of a provider
        public static IQueryable<InboxMessageDataModel> FilterByProviderInboxes(this IQueryable<InboxMessageDataModel> query, Guid providerId)
        {
            // Add a filter here for the inbox permission settings in the future
            return query.Where(message => message.Inbox.ProviderId == providerId);
        }

        // Filter by contact id based on participants
        public static IQueryable<InboxMessageDataModel> FilterByContact(this IQueryable<InboxMessageDataModel> query, IQueryable<InboxContactDataModel> inboxContactQuery, Guid contactId)
        {
            return query
                .Where(message => message.InboxConversation.InboxConversationParticipants
                    .Any(participant => inboxContactQuery
                        .Where(inboxContact => inboxContact.ContactId == contactId)
                        .Where(inboxContact => inboxContact.AccountId.ToLower() == participant.AccountId.ToLower()).Any()));
        }

        public static IQueryable<InboxMessageDataModel> FilterByInboxes(this IQueryable<InboxMessageDataModel> query, params Guid[] inboxIds)
        {
            inboxIds ??= [];

            // If no inbox ids are provided, return an empty query
            if (inboxIds.IsNullOrEmpty()) return query.Where(_ => false);

            return inboxIds.Length == 1
                ? query.Where(message => message.InboxId == inboxIds[0])
                : query.Where(x => x.InboxId.HasValue && inboxIds.Contains(x.InboxId.Value));
        }

        public static IQueryable<InboxMessageDataModel> FilterByConversations(this IQueryable<InboxMessageDataModel> query, params Guid[] conversationIds)
        {
            conversationIds ??= [];

            // If no conversation ids are provided, return an empty query
            if (conversationIds.IsNullOrEmpty()) return query.Where(_ => false);

            return conversationIds.Length == 1
                ? query.Where(message => message.ConversationId.HasValue && message.ConversationId == conversationIds[0])
                : query.Where(message => message.ConversationId.HasValue && conversationIds.Contains(message.ConversationId.Value));
        }

        public static IQueryable<InboxMessageDataModel> FilterConversationByStatus(this IQueryable<InboxMessageDataModel> query, MessageStatus status)
        {
            return status switch
            {
                // Fetch conversation with draft records (new drafts / replies)
                MessageStatus.Draft => query.FilterDraft(),

                // Fetch conversation with sent items excluding drafts
                MessageStatus.Sent => query.FilterSent().ExcludeByStatus(MessageStatus.Draft),

                MessageStatus.Scheduled => query.FilterScheduled().ExcludeByStatus(MessageStatus.Draft),

                // Inbox (Default) / Archive / Deleted folders
                _ => query.FilterByStatus(status)
            };
        }

        public static IQueryable<InboxMessageDataModel> FilterConversationByReadStatus(this IQueryable<InboxMessageDataModel> query, ConversationFiltering filterBy)
        {
            return filterBy switch
            {
                ConversationFiltering.Read => query.FilterRead(),
                ConversationFiltering.Unread => query.FilterUnread(),
                _ => query
            };
        }

        public static IQueryable<InboxMessageDataModel> FilterConversationByStatuses(this IQueryable<InboxMessageDataModel> query, MessageStatus[] statuses) => query.Where(m => statuses.Contains(m.Status));

        public static IQueryable<InboxMessageDataModel> FilterMessageByStatus(this IQueryable<InboxMessageDataModel> query, MessageStatus status)
        {
            return status switch
            {
                // Fetch all messages including drafts (new drafts / replies)
                MessageStatus.Draft => query.IncludeDraft(),

                // Fetch all messages excluding drafts for Sent and Scheduled folders
                var _ when status == MessageStatus.Sent || status == MessageStatus.Scheduled => query.ExcludeByStatus(MessageStatus.Draft),

                // Inbox (Default) / Archive / Deleted folders
                _ => query.FilterByStatus(status)
            };
        }

        // Filter unread messages
        public static IQueryable<InboxMessageDataModel> FilterUnread(this IQueryable<InboxMessageDataModel> query)
        {
            return query.Where(message => !message.IsRead);
        }

        // Filter read messages
        public static IQueryable<InboxMessageDataModel> FilterRead(this IQueryable<InboxMessageDataModel> query)
        {
            return query.Where(message => message.IsRead);
        }

        // Filter by conversation id
        public static IQueryable<InboxMessageDataModel> FilterByConversationIds(this IQueryable<InboxMessageDataModel> query, params Guid[] conversationIds)
        {
            conversationIds ??= [];

            query = query.IncludeWithConversationIdAndNewDrafts();

            if (conversationIds.IsNullOrEmpty()) return query;

            var stringifiedConversationIds = conversationIds.Select(x => x.ToString()).ToArray();

            return conversationIds.Length == 1
                ? query.Where(message => message.ConversationId == conversationIds[0] || (message.Status == MessageStatus.Draft && message.ExternalConversationId == stringifiedConversationIds[0]))
                : query.Where(message => conversationIds.Contains(message.ConversationId.Value)
                    || (message.Status == MessageStatus.Draft && stringifiedConversationIds.Contains(message.ExternalConversationId)));
        }

        public static IQueryable<InboxMessageDataModel> AllMessagesExceptOtherParticipantDrafts(this IQueryable<InboxMessageDataModel> query, string conversationViewOfAccountId)
        {
            return query.Where(message =>
                message.Status != MessageStatus.Draft || // Not a draft
                message.FromAccountId == conversationViewOfAccountId   // OR it's a draft but from the sender
            );
        }

        public static IQueryable<InboxMessageDataModel> FilterByProviderParticipant(this IQueryable<InboxMessageDataModel> query, Guid providerId)
        {
            return query.Where(message =>
                message.InboxConversation.InboxConversationParticipants.Any(participant => participant.ProviderId == providerId)
            );
        }

        // Exclude by conversation id
        public static IQueryable<InboxMessageDataModel> ExcludeByConversationIds(this IQueryable<InboxMessageDataModel> query, params Guid[] conversationIds)
        {
            conversationIds ??= [];

            query = query.IncludeWithConversationIdAndNewDrafts();

            if (conversationIds.IsNullOrEmpty()) return query;

            return conversationIds.Length == 1
                ? query.Where(message => message.ConversationId != conversationIds[0])
                : query.Where(message => !conversationIds.Contains(message.ConversationId.Value));
        }

        // Check if person id has seen the inbox message
        public static IQueryable<InboxMessageDataModel> IsNotSeenByPerson(this IQueryable<InboxMessageDataModel> query, Guid personId)
        {
            return query.Where(x => !x.InboxMessageSeenBys.Any(x => x.PersonId == personId));
        }

        public static IQueryable<InboxMessageDataModel> FilterBySearchQuery(this IQueryable<InboxMessageDataModel> query,
            string[] accounts,
            string searchTerm)
        {
            return query
                .Where(x => EF.Functions.ILike(x.FromAccountId, $"%{searchTerm}%") // Search by from display name
                    || EF.Functions.ILike(x.FromDisplayName, $"%{searchTerm}%")
                    || accounts
                        .Any(inboxContact => inboxContact == x.FromAccountId.ToLower()) // Search by contact first and last name
                    || EF.Functions.ToTsVector("english", x.MessageSubject).SetWeight('A')
                        .Concat(EF.Functions.ToTsVector("english", x.MessageBodyPlainText).SetWeight('B')).Matches(searchTerm)); // Search by ts vector
        }

        // Filter By Sender
        public static IQueryable<InboxMessageDataModel> FilterBySender(this IQueryable<InboxMessageDataModel> query, string fromAccountId)
        {
            return query.Where(x => x.FromAccountId.ToLower() == fromAccountId.ToLower());
        }

        // Filter By External Source Message Id
        public static IQueryable<InboxMessageDataModel> FilterByExternalSourceMessageIds(this IQueryable<InboxMessageDataModel> query, params string[] externalSourceMessageIds)
        {
            externalSourceMessageIds ??= [];

            if (externalSourceMessageIds.IsNullOrEmpty()) return query.Where(_ => false);

            if (externalSourceMessageIds.Length == 0) return query.Where(x => x.ExternalSourceMessageId == externalSourceMessageIds[0]);

            return query.Where(x => externalSourceMessageIds.Contains(x.ExternalSourceMessageId));
        }

        public static async Task<Dictionary<string, ConversationSummary>> GetConversationSummaryByConversation(this IQueryable<IGrouping<string, InboxMessageDataModel>> conversationsQuery)
        {
            return await conversationsQuery
                .Select(x => new
                {
                    // ConversationKey could be the conversationId or the externalConversationId based on modern / classic modes
                    ConversationKey = x.Key,

                    // For new composed messages (draft status & w/o parent message) count as 1 draft, otherwise count the drafts under the message record
                    DraftCount = x.Sum(message => message.Status == MessageStatus.Draft ? 1 : message.InboxMessageDrafts.Count),

                    // For new composed messages (draft status & w/o parent message) count as 1 draft if failed, otherwise count the drafts with failed sending status under the message record
                    DraftFailedCount = x.Sum(message => message.Status == MessageStatus.Draft && message.LastSendStatus == MessageSendStatus.Failed ? 1 : message.InboxMessageDrafts.Count(x => x.LastSendStatus == MessageSendStatus.Failed)),

                    HasScheduled = x.Any(message => message.ScheduledAt != null)
                })
                .ToDictionaryAsync(x => x.ConversationKey, val => new ConversationSummary(val.DraftCount, val.DraftFailedCount, val.HasScheduled));
        }

        public static IQueryable<InboxMessageDataModel> FilterByStatus(this IQueryable<InboxMessageDataModel> query, MessageStatus status)
        {
            return query.Where(message => message.Status == status);
        }

        public static IQueryable<IGrouping<string, InboxMessageDataModel>> ApplyPagination(this IQueryable<IGrouping<string, InboxMessageDataModel>> query, ConversationSorting sortBy, DateTime paginationSortingDateTime)
        {
            return (sortBy.IsSortByNewestRecord())
                ? query.Where(g => g.OrderByDescending(x => x.CreatedAt).First().CreatedAt < paginationSortingDateTime)
                : query.Where(g => g.OrderByDescending(x => x.CreatedAt).First().CreatedAt > paginationSortingDateTime);
        }

        public static IQueryable<InboxMessageDataModel> ApplyConversationSorting(this IQueryable<IGrouping<string, InboxMessageDataModel>> query, ConversationSorting sortBy, string[] accounts)
        {
            // Do default order to create an IOrderedQueryable for chaining
            var orderedQuery = query.OrderBy(x => 0);

            if (sortBy.IsSortByUnreplied()) orderedQuery = orderedQuery.SortByUnreplied(accounts);

            orderedQuery = sortBy.IsSortByNewestRecord()
                ? orderedQuery.SortByNewest()
                : orderedQuery.SortByOldest();

            return orderedQuery.SelectLatestMessage();
        }

        public static IQueryable<InboxMessageDataModel> ApplyPagination(this IQueryable<InboxMessageDataModel> query, ConversationSorting sortBy, DateTime paginationSortingDateTime)
        {
            return (sortBy.IsSortByNewestRecord())
                ? query.Where(x => x.CreatedAt < paginationSortingDateTime)
                : query.Where(x => x.CreatedAt > paginationSortingDateTime);
        }

        public static IQueryable<InboxMessageDataModel> ApplyConversationSorting(this IQueryable<InboxMessageDataModel> query, ConversationSorting sortBy, string[] accounts)
        {
            // Do default order to create an IOrderedQueryable for chaining
            var orderedQuery = query.OrderBy(x => 0);

            if (sortBy.IsSortByUnreplied()) orderedQuery = orderedQuery.SortByUnreplied(accounts);
            
            return sortBy.IsSortByNewestRecord()
                ? orderedQuery.SortByNewest()
                : orderedQuery.SortByOldest();
        }

        public static IQueryable<InboxMessageDataModel> FilterByInboxMessageId(this IQueryable<InboxMessageDataModel> query, Guid inboxMessageId)
        {
            return query.Where(x => x.Id == inboxMessageId);
        }

        public static IQueryable<InboxMessageDataModel> FilterBySendStatus(this IQueryable<InboxMessageDataModel> query, MessageSendStatus sendStatus)
        {
            return query.Where(x => x.LastSendStatus == sendStatus);
        }

        public static IQueryable<IGrouping<string, InboxMessageDataModel>> GroupByConversationId(this IQueryable<InboxMessageDataModel> query)
        {
            // Conversation Id is null for New Draft messages, instead of grouping them by Conversation Id, we group them by the draft's External Conversation Id
            return query
                .IncludeWithConversationIdAndNewDrafts()
                .GroupBy(message => message.ConversationId == null ? message.ExternalConversationId : message.ConversationId.ToString());
        }

        public static IQueryable<InboxMessageDataModel> ExcludeByStatus(this IQueryable<InboxMessageDataModel> query, MessageStatus status)
        {
            return query.Where(message => message.Status != status);
        }

        public static IQueryable<InboxMessageDataModel> ExcludeByStatuses(this IQueryable<InboxMessageDataModel> query,
            params MessageStatus[] messageStatuses)
        {
            messageStatuses ??= [];

            if (messageStatuses.IsNullOrEmpty()) return query;

            if (messageStatuses.Length == 1) return query.ExcludeByStatus(messageStatuses[0]);

            return query.Where(x => !messageStatuses.Contains(x.Status));
        }

        public static IQueryable<InboxMessageDataModel> HasAttachments(this IQueryable<InboxMessageDataModel> query)
        {
            return query.Where(message => message.InboxMessageAttachments.Any());
        }

        public static IQueryable<InboxMessageDataModel> FilterByExternalSources(this IQueryable<InboxMessageDataModel> query,
            params ExternalSource[] externalSources)
        {
            externalSources ??= [];

            if (externalSources.IsNullOrEmpty()) return query;

            if (externalSources.Length == 1) return query.Where(x => x.ExternalSource != externalSources[0]);

            return query.Where(x => externalSources.Contains(x.ExternalSource));
        }

        private static IQueryable<InboxMessageDataModel> FilterSent(this IQueryable<InboxMessageDataModel> query)
        {
            // A message is considered sent if its sender (from) is one of the user's inbox accounts
            return query.Where(message => message.Inbox.InboxAccounts.Any(account => account.ExternalAccountId.ToLower() == message.FromAccountId.ToLower()))
                .Where(message => message.ScheduledAt == null); // Exclude scheduled messages
        }

        private static IQueryable<InboxMessageDataModel> FilterScheduled(this IQueryable<InboxMessageDataModel> query)
        {
            // A message is considered scheduled if its scheduled at is not null
            return query.Where(message => message.ScheduledAt != null);
        }

        // Include messages with conversation id and new drafts for backwards and forwards compatibility
        private static IQueryable<InboxMessageDataModel> IncludeWithConversationIdAndNewDrafts(this IQueryable<InboxMessageDataModel> query)
        {
            // This is an additional filter before grouping messages under a conversation / external conversation id
            // For backwards compatibility, we need to exclude messages without conversation id from previous release (old data)
            // For new composed draft records (w/o conversation id), we need to include them in the group
            return query.Where(message => (message.ConversationId != null)
                || (message.Status == MessageStatus.Draft
                    && message.DraftForInboxMessageId == null
                    && message.ExternalConversationId.Length > 0));
        }

        private static IQueryable<InboxMessageDataModel> FilterDraft(this IQueryable<InboxMessageDataModel> query)
        {
            // Fetch messages having a draft record related to it (reply scenario)
            // Fetch new composed draft records w/o a parent inbox message record (new message scenario)
            return query.Where(message => message.InboxMessageDrafts.Any()
                || (message.Status == MessageStatus.Draft && message.DraftForInboxMessageId == null && message.ExternalConversationId.Length > 0));
        }

        private static IQueryable<InboxMessageDataModel> IncludeDraft(this IQueryable<InboxMessageDataModel> query)
        {
            // Fetch all messages except the draft status as it will be under a message record (reply scenario)
            // Fetch new composed draft records w/o a parent inbox message record (new message scenario)
            return query.Where(message => message.Status != MessageStatus.Draft
                    || (message.Status == MessageStatus.Draft && message.DraftForInboxMessageId == null && message.ExternalConversationId.Length > 0));
        }

        private static IOrderedQueryable<InboxMessageDataModel> SortByUnreplied(this IOrderedQueryable<InboxMessageDataModel> query, string[] accounts)
        {
            // If the latest message is from the user's inbox account it is considered replied
            // This orders by 0 (false) and 1 (true) to sort unreplied messages first
            return query.ThenBy(x => accounts.Any(account => account == x.FromAccountId));
        }

        private static IOrderedQueryable<IGrouping<string, InboxMessageDataModel>> SortByUnreplied(this IOrderedQueryable<IGrouping<string, InboxMessageDataModel>> query, string[] accounts)
        {
            // If the latest message is from the user's inbox account it is considered replied
            // This orders by 0 (false) and 1 (true) to sort unreplied messages first
            return query.ThenBy(g => accounts.Any(account => account == g.OrderByDescending(x => x.CreatedAt).First().FromAccountId));
        }

        private static IOrderedQueryable<InboxMessageDataModel> SortByNewest(this IOrderedQueryable<InboxMessageDataModel> query)
        {
            return query.ThenByDescending(x => x.CreatedAt);
        }

        private static IOrderedQueryable<InboxMessageDataModel> SortByOldest(this IOrderedQueryable<InboxMessageDataModel> query)
        {
            return query.ThenBy(x => x.CreatedAt);
        }

        private static IOrderedQueryable<IGrouping<string, InboxMessageDataModel>> SortByNewest(this IOrderedQueryable<IGrouping<string, InboxMessageDataModel>> query)
        {
            return query.ThenByDescending(g => g.OrderByDescending(x => x.CreatedAt).First().CreatedAt);
        }

        private static IOrderedQueryable<IGrouping<string, InboxMessageDataModel>> SortByOldest(this IOrderedQueryable<IGrouping<string, InboxMessageDataModel>> query)
        {
            return query.ThenBy(g => g.OrderByDescending(x => x.CreatedAt).First().CreatedAt);
        }

        private static IQueryable<InboxMessageDataModel> SelectLatestMessage(this IOrderedQueryable<IGrouping<string, InboxMessageDataModel>> query)
        {
            return query.Select(g => g.OrderByDescending(x => x.CreatedAt).First());
        }
    }
}
    