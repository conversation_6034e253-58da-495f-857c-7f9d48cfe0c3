﻿using carepatron.core.Abstractions;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Models.Common;
using carepatron.core.Models.Reminders;
using carepatron.infra.sql.Converters;
using carepatron.infra.sql.Extensions;
using carepatron.infra.sql.Functions;
using carepatron.infra.sql.Models.Attachments;
using carepatron.infra.sql.Models.Authorization;
using carepatron.infra.sql.Models.Automation;
using carepatron.infra.sql.Models.Billing;
using carepatron.infra.sql.Models.Booking;
using carepatron.infra.sql.Models.Call;
using carepatron.infra.sql.Models.ConnectedApp;
using carepatron.infra.sql.Models.Contact;
using carepatron.infra.sql.Models.File;
using carepatron.infra.sql.Models.ICDCode;
using carepatron.infra.sql.Models.Inbox;
using carepatron.infra.sql.Models.Insurance;
using carepatron.infra.sql.Models.Invoices;
using carepatron.infra.sql.Models.Items;
using carepatron.infra.sql.Models.Media;
using carepatron.infra.sql.Models.Note;
using carepatron.infra.sql.Models.Onboarding;
using carepatron.infra.sql.Models.Permissions;
using carepatron.infra.sql.Models.Person;
using carepatron.infra.sql.Models.Provider;
using carepatron.infra.sql.Models.PushSubscription;
using carepatron.infra.sql.Models.Referrals;
using carepatron.infra.sql.Models.Registration;
using carepatron.infra.sql.Models.Reminders;
using carepatron.infra.sql.Models.Risk;
using carepatron.infra.sql.Models.Schema;
using carepatron.infra.sql.Models.Security;
using carepatron.infra.sql.Models.ServiceReceipts;
using carepatron.infra.sql.Models.Signatures;
using carepatron.infra.sql.Models.Staff;
using carepatron.infra.sql.Models.Subscription;
using carepatron.infra.sql.Models.Tags;
using carepatron.infra.sql.Models.Tasks;
using carepatron.infra.sql.Models.Templates;
using carepatron.infra.sql.Models.Transcriptions;
using carepatron.infra.sql.SeedData;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Query;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Npgsql.EntityFrameworkCore.PostgreSQL.Query;
using Npgsql.EntityFrameworkCore.PostgreSQL.Query.Expressions;
using Serilog;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using carepatron.infra.sql.Models.BillableItem;
using carepatron.infra.sql.Models.Trash;
using carepatron.core.Application.Refunds.Models;
using carepatron.infra.sql.Models.Exports;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using carepatron.core.Application.TaxRates.Models;
using carepatron.infra.sql.Models.OptionSets;
using carepatron.infra.sql.Models.FeatureModules;
using carepatron.infra.sql.Models.Payment;
using carepatron.infra.sql.Models.AskAI;
using carepatron.infra.sql.Models.Folders;
using carepatron.infra.sql.Models.Temp;
using carepatron.infra.sql.Models.History;
using carepatron.infra.sql.Models.ClearingHouse;
using carepatron.infra.sql.Models.History.Configuration;

namespace carepatron.infra.sql
{
    public class DataContext : DbContext, IUnitOfWork
    {
        private bool usingUnitOfWork = false;
        private readonly NpgsqlSqlExpressionFactory sqlExpressionFactory;

        public DataContext(DbContextOptions<DataContext> options)
            : base(options)
        {
            sqlExpressionFactory = (NpgsqlSqlExpressionFactory)this.GetService<ISqlExpressionFactory>();
        }

        // very useful when Benchmarking EF
        // protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        // {
        //     optionsBuilder.LogTo(x => Debug.WriteLine(x));
        //     base.OnConfiguring(optionsBuilder);
        // }

        /// <summary>
        /// Warning! This isn't a typical unit of work.
        ///
        /// It was a hacky "temporary" addition to allow opting in to more atomic operations across repositories
        /// through SaveChanges instead of introducing full transactions while we transitioned to Mediatr pipelines.
        ///
        /// It sets a flag in the DB Context that means direct SaveChanges are ignored.
        /// and SaveChanges are only invoked if using SaveUnitOfWork.
        /// By default this is triggered in the mediatr pipeline behaviour, but can also be explicitly called.
        /// </summary>
        public void UseUnitOfWork()
        {
            ChangeTracker.Clear();
            usingUnitOfWork = true;
            Log.Debug("Enabling unit of work: {DBContextHashCode}", this.GetHashCode());
        }

        public async Task<int> SaveUnitOfWork(CancellationToken cancellationToken = default)
        {
            if (!usingUnitOfWork) throw new InvalidOperationException();

            Log.Debug("Calling SaveChanges through SaveUnitOfWork: {DBContextHashCode}", this.GetHashCode());
            var result = await DoSaveChangesAsync(cancellationToken);

            ChangeTracker.Clear();

            return result;
        }

        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            if (usingUnitOfWork)
            {
                //if opted into use unit of work savechanges will be handled through SaveUnitOfWork();
                Log.Debug("Running in unit of work. Skipping save changes {DBContextHashCode}", this.GetHashCode());
                return Task.FromResult(0);
            }

            Log.Debug("Save changes {DBContextHashCode}", this.GetHashCode());
            return DoSaveChangesAsync(cancellationToken);
        }

        private async Task<int> DoSaveChangesAsync(CancellationToken cancellationToken)
        {
            foreach (var entry in ChangeTracker.Entries())
            {
                // Soft-delete entities
                if (entry.State == EntityState.Deleted
                    && entry.Entity is ISoftDelete entity
                    && (entity is not IHardDelete hardDelete || !hardDelete.HardDelete))
                {
                    entry.State = EntityState.Modified;
                    entity.DeletedAtUtc = DateTime.UtcNow;
                }
            }
            return await base.SaveChangesAsync(cancellationToken);
        }

        // Persons

        public virtual DbSet<PersonDataModel> Persons { get; set; }

        public virtual DbSet<RiskViolationDataModel> RiskViolations { get; set; }

        // Personal Preferences
        public virtual DbSet<PersonalSettingsDataModel> PersonalSettings { get; set; }

        public virtual DbSet<PersonalPreferencesDataModel> PersonalPreferences { get; set; }

        // Providers

        public virtual DbSet<ProviderDataModel> Providers { get; set; }

        public virtual DbSet<ProviderStaffDataModel> ProviderStaff { get; set; }

        public virtual DbSet<ProviderStaffDefaultLocationDataModel> ProviderStaffDefaultLocations { get; set; }

        public virtual DbSet<ProviderBillingSettingsDataModel> ProviderBillingSettings { get; set; }

        public virtual DbSet<ProviderTaxRate> TaxRates { get; set; }

        public virtual DbSet<CountryCurrencyDataModel> CountryCurrency { get; set; }

        public virtual DbSet<ProviderLogoDataModel> ProviderLogo { get; set; }

        public virtual DbSet<ProviderLocationDataModel> ProviderLocations { get; set; }

        public virtual DbSet<ProviderContactFieldSettingsDataModel> ProviderContactFieldSettings { get; set; }

        public virtual DbSet<ProviderOnlineBookingOptionsDataModel> ProviderOnlineBookingOptions { get; set; }

        public virtual DbSet<ProviderItemGroupDataModel> ProviderItemGroups { get; set; }

        public virtual DbSet<ProviderItemGroupItemDataModel> ProviderItemGroupItems { get; set; }

        public virtual DbSet<ContactFileTagDataModel> ContactFileTags { get; set; }

        public virtual DbSet<ProviderWorkspacePreferenceDataModel> ProviderWorkspacePreferences { get; set; }

        // Media

        public virtual DbSet<MediaDataModel> Medias { get; set; }

        // PushSubscriptions

        public virtual DbSet<PushSubscriptionDataModel> PushSubscriptions { get; set; }

        // Calls

        public virtual DbSet<CallDataModel> Calls { get; set; }

        public virtual DbSet<CallAttendeeDataModel> CallAttendees { get; set; }

        // Contacts

        public virtual DbSet<ContactDataModel> Contacts { get; set; }

        public virtual DbSet<ContactSettingsDataModel> ContactSettings { get; set; }

        public virtual DbSet<ContactRelationshipDataModel> ContactRelationships { get; set; }

        public virtual DbSet<ContactAddressDataModel> ContactAddresses { get; set; }

        public virtual DbSet<ContactEmailDataModel> ContactEmails { get; set; }

        public virtual DbSet<ContactPhoneDataModel> ContactPhones { get; set; }

        public virtual DbSet<ContactLanguageDataModel> ContactLanguages { get; set; }

        public virtual DbSet<ContactAssignedStaffDataModel> ContactAssignedStaff { get; set; }

        public virtual DbSet<ClientEnrolmentDataModel> ClientEnrolments { get; set; }

        public virtual DbSet<ClientEnrolmentTemplateDataModel> ClientEnrolmentTemplates { get; set; }

        public virtual DbSet<ClientEnrolmentNoteDataModel> ClientEnrolmentNotes { get; set; }

        public virtual DbSet<ContactAutomationSettingDataModel> ContactAutomationSettings { get; set; }

        public virtual DbSet<ContactImportSummaryDataModel> ContactImportSummaries { get; set; }
        public virtual DbSet<ContactExportSummaryDataModel> ContactExportSummaries { get; set; }

        public virtual DbSet<ContactCreditAdjustmentDataModel> ContactCreditAdjustments { get; set; }

        public virtual DbSet<DuplicateContactDataModel> DuplicateContacts { get; set; }
        
        public virtual DbSet<ExternalContactDataModel> ExternalContacts { get; set; }

        // Files

        public virtual DbSet<ContactFileDataModel> ContactFiles { get; set; }

        public virtual DbSet<ContactFileAccessTypeDataModel> ContactFileAccessTypes { get; set; }

        public virtual DbSet<SharedFileDataModel> SharedFiles { get; set; }

        // Notes

        public virtual DbSet<ContactNoteDataModel> ContactNotes { get; set; }

        public virtual DbSet<NoteFormResponseDataModel> NoteFormResponses { get; set; }

        public virtual DbSet<ContactNoteAccessTypeDataModel> ContactNoteAccessTypes { get; set; }

        public virtual DbSet<SharedNoteDataModel> SharedNotes { get; set; }

        public virtual DbSet<NoteFormFieldDataModel> NoteFormFields { get; set; }

        public virtual DbSet<ContactNoteAttachmentDataModel> ContactNoteAttachments { get; set; }

        public virtual DbSet<FormResponseAttachmentDataModel> FormResponseAttachments { get; set; }

        public virtual DbSet<NoteSummaryDataModel> NoteSummaries { get; set; }

        public virtual DbSet<NoteSignatureDataModel> NoteSignatures { get; set; }

        public virtual DbSet<SignatureDataModel> Signatures { get; set; }

        public virtual DbSet<TranscriptionDataModel> Transcriptions { get; set; }

        public virtual DbSet<NoteTranscriptionDataModel> NoteTranscriptions { get; set; }

        // Tags

        public virtual DbSet<TagDataModel> Tags { get; set; }

        public virtual DbSet<ContactTagDataModel> ContactTags { get; set; }

        public virtual DbSet<ContactNoteTagDataModel> ContactNoteTags { get; set; }

        // Tasks

        public virtual DbSet<TaskDataModel> Tasks { get; set; }

        public virtual DbSet<InvoiceTaskDataModel> InvoiceTasks { get; set; }

        public virtual DbSet<TaskContactDataModel> TaskContacts { get; set; }

        public virtual DbSet<TaskStaffDataModel> TaskStaff { get; set; }

        public virtual DbSet<InsuranceClaimTaskDataModel> InsuranceClaimTasks { get; set; }
        
        public virtual DbSet<TaskExternalContactDataModel> TaskExternalContacts { get; set; }
        
        public virtual DbSet<TaskAttendeeStatusDataModel> TaskAttendeeStatuses { get; set; }

        // Services

        public virtual DbSet<ProviderItemDataModel> Items { get; set; }
        public virtual DbSet<ProviderStaffItemDataModel> StaffItems { get; set; }
        public virtual DbSet<ProviderItemLocationDataModel> ItemLocations { get; set; }
        public virtual DbSet<ItemScheduleDataModel> ItemSchedules { get; set; }
        public virtual DbSet<ItemScheduleTimeBlockDataModel> ItemScheduleTimeBlocks { get; set; }

        // Reminders

        public virtual DbSet<ReminderSettingsDataModel> ReminderSettings { get; set; }

        public virtual DbSet<ReminderJobDataModel> ReminderJobs { get; set; }

        // Invoices
        public virtual DbSet<InvoiceDataModel> Invoices { get; set; }


        public virtual DbSet<BillableItemDataModel> BillableItems { get; set; }
        public virtual DbSet<BillableDataModel> Billables { get; set; }
        public virtual DbSet<InvoiceTemplateSettingDataModel> InvoiceTemplateSettings { get; set; }

        public virtual DbSet<InvoiceLineItemDataModel> InvoiceLineItems { get; set; }

        public virtual DbSet<PaymentDataModel> Payments { get; set; }
        public virtual DbSet<PaymentAllocationDataModel> PaymentAllocations { get; set; }

        public virtual DbSet<InvoicePaymentDataModel> InvoicePayments { get; set; }

        public virtual DbSet<InvoiceStaffDataModel> InvoiceStaff { get; set; }

        public virtual DbSet<PaymentIntentDataModel> PaymentIntents { get; set; }

        public virtual DbSet<PaymentMethodDataModel> PaymentMethods { get; set; }

        public virtual DbSet<PaymentMethodAuthorizationDataModel> PaymentMethodAuthorizations { get; set; }

        public virtual DbSet<TemplateDataModel> Templates { get; set; }
        
        public virtual DbSet<TempTemplateDataModel> TempTemplates { get; set; }
        
        public virtual DbSet<TemplatePersonFavoriteDataModel> TemplatePersonFavorites { get; set; }
        
        public virtual DbSet<TemplateSharingConfigDataModel> TemplateSharingConfigs { get; set; }
        public virtual DbSet<TemplateFolderDataModel> TemplateFolders { get; set; }
        public virtual DbSet<FolderDataModel> Folders { get; set; }
        public virtual DbSet<FolderPersonFavoriteDataModel> FolderPersonFavorites { get; set; }
        public virtual DbSet<DefaultIntakeTemplateDataModel> DefaultIntakeTemplates { get; set; }

        public virtual DbSet<TemplateFormFieldDataModel> TemplateFormFields { get; set; }
        public virtual DbSet<TemplateAiPromptDataModel> TemplateAiPrompts { get; set; }

        public virtual DbSet<PublicTemplateDataModel> PublicTemplates { get; set; }
        public virtual DbSet<TemplateImportDataModel> TemplateImports { get; set; }

        public virtual DbSet<PublicTemplateFormFieldDataModel> PublicTemplateFormFields { get; set; }
        public virtual DbSet<PublicTemplateAiPromptDataModel> PublicTemplateAiPrompts { get; set; }

        public virtual DbSet<TemplateTagDataModel> TemplateTags { get; set; }

        public virtual DbSet<PublicTemplateTagDataModel> PublicTemplateTags { get; set; }

        public virtual DbSet<PublicTemplateProfessionDataModel> PublicTemplateProfessions { get; set; }

        public virtual DbSet<TemplateCollectionDataModel> TemplateCollections { get; set; }

        public virtual DbSet<TemplatesUsedByPersonDataModel> TemplatesUsedByPersons { get; set; }

        public virtual DbSet<PublicTemplateAttachmentDataModel> PublicTemplateAttachments { get; set; }

        public virtual DbSet<TemplateAttachmentDataModel> TemplateAttachments { get; set; }


        // Service receipts

        public virtual DbSet<ServiceReceiptDataModel> ServiceReceipts { get; set; }
        public virtual DbSet<ServiceReceiptLineItemDataModel> ServiceReceiptLineItems { get; set; }
        public virtual DbSet<ServiceReceiptStaffDataModel> ServiceReceiptStaff { get; set; }
        public virtual DbSet<ServiceReceiptInvoiceDataModel> ServiceReceiptInvoices { get; set; }

        // connected apps

        public virtual DbSet<ConnectedAppDataModel> ConnectedApps { get; set; }

        public virtual DbSet<ConnectedAppProductDataModel> ConnectedAppProducts { get; set; }

        public virtual DbSet<CalendarSubscriptionDataModel> CalendarSubscriptions { get; set; }

        // Billing
        public virtual DbSet<BillingAccountDataModel> BillingAccounts { get; set; }

        public virtual DbSet<UsageDataModel> Usage { get; set; }

        // Onboarding
        public virtual DbSet<OnboardingDataModel> Onboarding { get; set; }

        // Permissions
        public virtual DbSet<ProviderPermissionsDataModel> ProviderPermissions { get; set; }

        public virtual DbSet<PublicTokenDataModel> PublicTokens { get; set; }

        // ICD Code
        public virtual DbSet<ICDCodeDataModel> ICDCodes { get; set; }

        // Scheduled Event
        public virtual DbSet<ScheduledEventDataModel> ScheduledEvents { get; set; }

        // staff schedules
        public virtual DbSet<StaffScheduleDataModel> StaffSchedules { get; set; }

        public virtual DbSet<StaffScheduleServiceDataModel> StaffScheduleServices { get; set; }

        public virtual DbSet<StaffScheduleTimeBlockDataModel> StaffScheduleTimeBlocks { get; set; }

        public virtual DbSet<StaffScheduleTimeBlockLocationDataModel> StaffScheduleTimeBlockLocations { get; set; }

        public virtual DbSet<StaffScheduleOverrideDataModel> StaffScheduleOverrides { get; set; }

        public virtual DbSet<StaffScheduleOverrideTimeBlockDataModel> StaffScheduleOverrideTimeBlocks { get; set; }

        public virtual DbSet<StaffScheduleOverrideLocationDataModel> StaffScheduleOverrideLocations { get; set; }

        public virtual DbSet<StaffScheduleOverrideServiceDataModel> StaffScheduleOverrideServices { get; set; }


        public virtual DbSet<BookingIntentDataModel> BookingIntents { get; set; }
        public virtual DbSet<AuthPolicyStatementDataModel> AuthPolicyStatements { get; set; }
        public virtual DbSet<RegisteredWorkspaceDataModel> RegisteredWorkspaces { get; set; }

        public virtual DbSet<EmailVerificationDataModel> EmailVerifications { get; set; }

        //schemas
        public virtual DbSet<DataSchemaDataModel> DataSchemas { get; set; }

        public virtual DbSet<LayoutSchemaDataModel> LayoutSchemas { get; set; }

        //inbox
        public virtual DbSet<InboxDataModel> Inboxes { get; set; }
        public virtual DbSet<InboxAccountDataModel> InboxAccounts { get; set; }
        public virtual DbSet<InboxMessageDataModel> InboxMessages { get; set; }
        public virtual DbSet<InboxMessageSeenByDataModel> InboxMessageSeenBy { get; set; }
        public virtual DbSet<InboxMessageAttachmentDataModel> InboxMessageAttachments { get; set; }
        public virtual DbSet<InboxContactDataModel> InboxContacts { get; set; }
        public virtual DbSet<InboxConversationDataModel> InboxConversations { get; set; }
        public virtual DbSet<InboxConversationParticipantDataModel> InboxConversationParticipants { get; set; }
        public virtual DbSet<InboxConversationThreadDataModel> InboxConversationThreads { get; set; }

        public virtual DbSet<InboxStaffRoleDataModel> InboxStaffRoles { get; set; }

        public virtual DbSet<InboxReplyFormatDataModel> InboxReplyFormats { get; set; }

        public virtual DbSet<InboxTrackedSenderDataModel> InboxTrackedSenders { get; set; }

        public virtual DbSet<InboxMessageBulkTransactionDataModel> InboxMessageBulkTransactions { get; set; }

        public virtual DbSet<SubscriptionDiscountDataModel> SubscriptionDiscounts { get; set; }
        public virtual DbSet<ProviderReferralCodeDataModel> ProviderReferralCodes { get; set; }
        public virtual DbSet<ProviderReferralDataModel> ProviderReferrals { get; set; }

        // Insurance
        public virtual DbSet<ProviderInsurancePayerDataModel> ProviderInsurancePayers { get; set; }
        public virtual DbSet<ProviderBillingProfileDataModel> ProviderBillingProfiles { get; set; }
        public virtual DbSet<ProviderBillingProfileStaffDataModel> ProviderBillingProfileStaff { get; set; }
        public virtual DbSet<ProviderBillingProfilePayerDataModel> ProviderBillingProfilePayers { get; set; }
        public virtual DbSet<InsuranceServiceCoverageDataModel> InsuranceServiceCoverages { get; set; }
        public virtual DbSet<ContactInsurancePolicyDataModel> ContactInsurancePolicies { get; set; }
        public virtual DbSet<ContactInsuranceSettingsDataModel> ContactInsuranceSettings { get; set; }
        public virtual DbSet<InsuranceClaimDataModel> InsuranceClaims { get; set; }
        public virtual DbSet<InsuranceClaimPaymentDataModel> InsuranceClaimPayments { get; set; }
        public virtual DbSet<InsuranceClaimUSProfessionalDataModel> InsuranceClaimsUSProfessional { get; set; }
        public virtual DbSet<InsuranceClaimDiagnosticCodeDataModel> InsuranceClaimDiagnosticCodes { get; set; }
        public virtual DbSet<InsuranceClaimReferringProviderDataModel> InsuranceClaimReferringProviders { get; set; }
        public virtual DbSet<InsuranceClaimRenderingProviderDataModel> InsuranceClaimRenderringProviders { get; set; }
        public virtual DbSet<InsuranceClaimServiceLineDataModel> InsuranceClaimServiceLines { get; set; }
        public virtual DbSet<InsuranceClaimBillingDetailDataModel> InsuranceClaimBillingDetails { get; set; }
        public virtual DbSet<InsuranceClaimIncidentDataModel> InsuranceClaimIncidents { get; set; }
        public virtual DbSet<InsuranceClaimContactInsurancePolicyDataModel> InsuranceClaimContactInsurancePolicies { get; set; }
        public virtual DbSet<InsuranceClaimClientDataModel> InsuranceClaimClients { get; set; }
        public virtual DbSet<InsuranceClaimFacilityDataModel> InsuranceClaimFacilities { get; set; }
        public virtual DbSet<OptionSetDataModel> OptionSets { get; set; }
        public virtual DbSet<OptionSetValueCountryDataModel> OptionSetValueCountries { get; set; }
        public virtual DbSet<ContactCreditSummaryDataModel> ContactCreditSummaries { get; set; }
        public virtual DbSet<InsurancePayerDataModel> InsurancePayers { get; set; }
        public virtual DbSet<InsurancePayerEnrollmentDataModel> InsurancePayerEnrollments { get; set; }
        public virtual DbSet<InsurancePayerEnrollmentTransactionDataModel> InsurancePayerEnrollmentTransactions { get; set; }
        public virtual DbSet<InsuranceClaimErrorDataModel> InsuranceClaimErrors { get; set; }
        public virtual DbSet<InsuranceClaimClearingHouseMetadataDataModel> InsuranceClaimClearingHouseMetadata { get; set; }
        public virtual DbSet<InsuranceRemittanceAdviceDataModel> InsuranceRemittanceAdvices { get; set; }
        public virtual DbSet<InsuranceClaimRemittanceDataModel> InsuranceClaimRemittances { get; set; }
        public virtual DbSet<InsuranceClaimRemittanceItemDataModel> InsuranceClaimRemittanceItems { get; set; }
        public virtual DbSet<InsuranceClaimAdjustmentDataModel> InsuranceClaimAdjustments { get; set; }
        public virtual DbSet<InsuranceRemittancePaymentDataModel> InsuranceRemittancePayments { get; set; }

        // Clearing house
        public virtual DbSet<ClearingHouseClaimMdSettingsDataModel> ClearingHouseClaimMdSettings { get; set; }
        public virtual DbSet<ClearingHouseLogDataModel> ClearingHouseLogs { get; set; }
        public virtual DbSet<ClearingHouseIdLookupDataModel> ClearingHouseIdLookups { get; set; }

        // Entity history
        public virtual DbSet<EntityHistoryDataModel> EntityHistory { get; set; }
        public virtual DbSet<EntityHistoryActorDataModel> EntityHistoryActors { get; set; }
        public virtual DbSet<EntityHistoryContactDataModel> EntityHistoryContacts { get; set; }
       
        // export
        public virtual DbSet<ExportDataModel> Exports { get; set; }

        public virtual DbSet<TrashItemDataModel> TrashItems { get; set; }
        public virtual DbSet<TrashItemMetadataDataModel> TrashItemMetadatas { get; set; }

 
        public virtual DbSet<Refund> Refunds { get; set; }

        public virtual DbSet<RefundHistory> RefundHistories { get; set; }

        //Ask AI
        public virtual DbSet<AiConversationDataModel> AiConversations { get; set; }

        public virtual DbSet<AiMessageDataModel> AiMessages { get; set; }

        public virtual DbSet<AiMessageContextDataModel> AiMessageContexts { get; set; }

        public virtual DbSet<NoteAiConversationDataModel> NoteAiConversations { get; set; }

        public virtual DbSet<ProviderFeatureModuleDataModel> ProviderFeatureModules { get; set; }
        public virtual DbSet<ProviderFeatureModuleUsageDataModel> ProviderFeatureModuleUsages { get; set; }

        // Temp Tables

        public virtual DbSet<TempEmbeddedTemplateDataModel> TempEmbeddedTemplates { get; set; }

        public virtual DbSet<TempEmbeddedPublicTemplateDataModel> TempEmbeddedPublicTemplates { get; set; }

        public virtual DbSet<TempEmbeddedContactNoteDataModel> TempEmbeddedContactNotes { get; set; }
        public virtual DbSet<TempAnnotatedFilesDataModel> TempAnnotatedFiles { get; set; }
        public virtual DbSet<TempPublicTemplateProfessionDataModel> TempPublicTemplateProfessions { get; set; }

        protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
        {
            configurationBuilder
                .Properties<Email>()
                .HaveConversion<EmailValueConverter>();

            configurationBuilder
                .Properties<RRule>()
                .HaveConversion<RRuleValueConverter>();
        }

        public EntityEntry<TEntity> HardDelete<TEntity>(TEntity entity) where TEntity : class, IHardDelete
        {
            entity.HardDelete = true;
            return base.Remove<TEntity>(entity);
        }

        public void HardDeleteRange<TEntity>(params TEntity[] entities) where TEntity : class, IHardDelete
        {
            foreach (var entity in entities)
            {
                entity.HardDelete = true;
            }
            base.RemoveRange(entities);
        }

        public void SoftDeleteRange<TEntity>(IEnumerable<TEntity> entities) where TEntity : class, ISoftDelete
        {
            foreach (var entity in entities)
            {
                entity.DeletedAtUtc = DateTime.UtcNow;
                var entry = this.Entry<TEntity>(entity);
                entry.State = EntityState.Modified;
            }
        }

        public EntityEntry<TEntity> SoftDelete<TEntity>(TEntity entity) where TEntity : class, ISoftDelete
        {
            entity.DeletedAtUtc = DateTime.UtcNow;
            var entry = this.Entry<TEntity>(entity);
            entry.State = EntityState.Modified;
            return entry;
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            builder.HasDefaultSchema("carepatron");

            builder.HasDbFunction(typeof(SqlFunctions)
                .GetMethod(nameof(SqlFunctions.ArrayContains),
                    [typeof(string[]), typeof(string[])]))
                .HasTranslation(args => sqlExpressionFactory.MakePostgresBinary(PgExpressionType.Contains,
                    args[0], args[1]));

            builder.HasDbFunction(typeof(SqlFunctions)
                .GetMethod(nameof(SqlFunctions.ArrayOverlaps),
                    [typeof(string[]), typeof(string[])]))
                .HasTranslation(args => sqlExpressionFactory.MakePostgresBinary(PgExpressionType.Overlaps,
                    args[0], args[1]));

            // Person
            builder.Entity<PersonDataModel>()
                .HasIndex(x => x.Email)
                .IsUnique();

            builder.Entity<RiskViolationDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<RiskViolationDataModel>()
                .Property(x => x.Metadata)
                .HasColumnType("jsonb");

            // Provider
            builder.Entity<ProviderStaffDataModel>()
                .HasKey(x => new { x.ProviderId, x.PersonId });

            builder.Entity<ProviderStaffDataModel>()
                .HasIndex(x => new { x.PersonId, x.ProviderId });

            builder.Entity<ProviderBillingSettingsDataModel>()
                .HasKey(x => new { x.ProviderId, x.Id });

            builder.Entity<ProviderLogoDataModel>()
                .HasKey(x => new { x.ProviderId, x.Id });

            builder.Entity<CountryCurrencyDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<CountryCurrencyDataModel>()
                .HasData(CurrenciesSeedData.Currencies);

            builder.Entity<ProviderBillingSettingsDataModel>()
                .HasOne(x => x.Provider);

            builder.Entity<ProviderBillingSettingsDataModel>()
                .Property(x => x.Address)
                .HasColumnType("jsonb");

            builder.Entity<ProviderBillingSettingsDataModel>()
                .HasIndex(x => x.StripeAccountId)
                .IsUnique();

            builder.Entity<ProviderLogoDataModel>()
                .HasOne(x => x.Provider)
                .WithOne(x => x.Logo);

            builder.Entity<ProviderLocationDataModel>()
                .HasKey(x => new { x.Id, x.ProviderId });

            builder.Entity<ProviderLocationDataModel>()
                .HasOne(x => x.Provider)
                .WithMany(x => x.Locations);

            builder.Entity<ContactFileTagDataModel>()
                .HasKey(c => new { c.FileId, c.TagId });

            builder.Entity<ContactFileTagDataModel>()
                .HasOne(p => p.File)
                .WithMany()
                .HasForeignKey(p => p.FileId);

            builder.Entity<ContactFileTagDataModel>()
                .HasOne(p => p.Tag)
                .WithMany()
                .HasForeignKey(p => p.TagId);

            builder.Entity<ProviderItemGroupItemDataModel>()
                .HasKey(p => new { p.ProviderItemGroupId, p.ProviderItemId });

            builder.Entity<ProviderItemGroupItemDataModel>()
                .HasOne(p => p.Group)
                .WithMany(p => p.GroupItems)
                .HasForeignKey(p => p.ProviderItemGroupId);

            builder.Entity<ProviderItemGroupItemDataModel>()
                .HasOne(p => p.Item)
                .WithMany(p => p.GroupItems)
                .HasForeignKey(p => p.ProviderItemId);

            builder.Entity<ProviderItemGroupDataModel>()
                .HasMany(p => p.GroupItems)
                .WithOne(p => p.Group)
                .HasForeignKey(p => p.ProviderItemGroupId);

            builder.Entity<ProviderItemGroupDataModel>()
                .HasIndex(x => x.ProviderId);

            // Push subscriptions
            builder.Entity<PushSubscriptionDataModel>()
                .HasKey(x => new { x.PersonId, x.DeviceId });

            // Calls
            builder.Entity<CallDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<CallAttendeeDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<CallAttendeeDataModel>()
                .Property(x => x.Id)
                .HasDefaultValueSql("md5(random()::text || clock_timestamp()::text)::uuid");

            builder.Entity<CallAttendeeDataModel>()
                .HasIndex(x => new { x.PersonId, x.CallId });

            builder.Entity<CallDataModel>()
                .Property(x => x.ExternalMetadataJson)
                .HasColumnType("jsonb");

            builder.Entity<CallDataModel>()
                .HasOne(x => x.Provider)
                .WithMany()
                .OnDelete(DeleteBehavior.SetNull);

            /*
             * Contacts
             */

            // keys
            builder.Entity<ContactDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<ContactDataModel>()
                .HasOne(x => x.Person);

            builder.Entity<ContactDataModel>()
                .HasOne(x => x.Provider);

            builder.Entity<ContactDataModel>()
                .HasMany(x => x.Tags)
                .WithOne(x => x.Contact);

            // fields
            builder.Entity<ContactDataModel>()
                .Property(x => x.Fields)
                .HasColumnType("jsonb");

            builder.Entity<ContactDataModel>()
                .Property(x => x.Sex)
                .HasColumnType("jsonb")
                .HasConversion<OptionSetValueConverter>();

            builder.Entity<ContactDataModel>()
                .Property(x => x.PreferredLanguage)
                .HasColumnType("jsonb")
                .HasConversion<OptionSetValueConverter>();

            // indexes
            builder.Entity<ContactDataModel>()
                .HasIndex(x => new { x.ProviderId, x.FirstName })
                .IncludeProperties(x => new
                {
                    x.Id,
                    x.PersonId,
                    x.IsClient,
                    x.IsArchived,
                    x.LastName,
                    x.MiddleNames,
                    x.BusinessName,
                    x.Email,
                    x.PhoneNumber,
                    x.Gender,
                    x.IdentificationNumber,
                    x.BirthDate,
                    x.ProfilePhotoId,
                    x.CreatedDateTimeUtc,
                    x.UpdatedDateTimeUtc
                });

            builder.Entity<ContactDataModel>()
                .HasIndex(x => x.PersonId);

            builder.Entity<ContactDataModel>()
                .HasIndex(x => x.Fields)
                .HasMethod("gin")
                .HasOperators("jsonb_ops");

            builder.Entity<ContactDataModel>().Property(x => x.Fields)
               .HasConversion(x => JsonConvert.SerializeObject(x, new JsonSerializerSettings
               {
                   ContractResolver = new DefaultContractResolver
                   {
                       NamingStrategy = new CamelCaseNamingStrategy
                       {
                           ProcessDictionaryKeys = false
                       }
                   }
               }), x => JsonConvert.DeserializeObject<Dictionary<string, object>>(x));

            builder.Entity<ContactDataModel>()
                .HasIndex(x => new { x.Email, x.ProviderId });

            builder.Entity<ContactDataModel>()
                .HasIndex(x => x.DeletedAtUtc);

            //Global filter
            builder.Entity<ContactDataModel>()
                .HasQueryFilter(x => !x.DeletedAtUtc.HasValue);

            builder.Entity<ContactDataModel>()
                .Navigation(c => c.Settings)
                .AutoInclude();


            /*
             * Contact Assigned Staff
             */

            builder.Entity<ContactAssignedStaffDataModel>()
                .HasOne(x => x.Contact)
                .WithMany(x => x.AssignedStaff);

            builder.Entity<ContactAssignedStaffDataModel>()
                .HasOne(x => x.Person);

            builder.Entity<ContactAssignedStaffDataModel>()
                .HasKey(x => new { x.ContactId, x.PersonId });

            /*
             * Contact Relationships
             */

            // keys
            builder.Entity<ContactRelationshipDataModel>()
                .HasKey(x => new { x.ContactId, x.ToContactId });

            builder.Entity<ContactRelationshipDataModel>()
                .HasOne(x => x.Contact);

            builder.Entity<ContactRelationshipDataModel>()
                .HasOne(x => x.ToContact);

            // indexes
            builder.Entity<ContactRelationshipDataModel>()
                .HasIndex(x => new { x.ToContactId, x.ContactId })
                .IsUnique()
                .IncludeProperties(x => new
                {
                    x.EmergencyContact,
                    x.ProviderId,
                    x.RelationshipAccessType,
                    x.RelationshipType,
                    x.ReverseRelationshipType,
                    x.CreatedDateTimeUtc,
                    x.UpdatedDateTimeUtc
                });

            /*
             * Contact Addresses
             */

            // keys
            builder.Entity<ContactAddressDataModel>()
                .Property(x => x.Id)
                .HasDefaultValueSql("md5(random()::text || clock_timestamp()::text)::uuid");

            builder.Entity<ContactAddressDataModel>()
                .HasOne(x => x.Provider)
                .WithMany()
                .HasForeignKey(x => x.ProviderId)
                .OnDelete(DeleteBehavior.Cascade);


            // indexes
            builder.Entity<ContactAddressDataModel>()
                .HasIndex(x => new { x.ProviderId, x.ContactId });

            builder.Entity<ContactAddressDataModel>()
                .OwnsOne(r => r.AddressDetails, address => address.ConfigureAddress());


            /*
             * Contact Files
             */

            // keys
            builder.Entity<ContactFileDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<ContactFileDataModel>()
                .HasOne(x => x.Contact)
                .WithMany()
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<ContactFileDataModel>()
                .HasMany(x => x.Tags)
                .WithOne(x => x.File);

            // indexes
            builder.Entity<ContactFileDataModel>()
                .HasIndex(x => new { x.ContactId, x.CreatedDateTimeUtc })
                .IncludeProperties(x => new { x.Id, x.NoteId, x.ProviderId, x.FileName, x.FileSize, x.FileExtension, x.ContentType, x.CreatedByPersonId, x.LastUpdatedByPersonId, x.LastUpdatedDateTimeUtc, x.Url });

            builder.Entity<ContactFileDataModel>()
                .HasIndex(x => new { x.ProviderId, x.CreatedDateTimeUtc })
                .IncludeProperties(x => new { x.Id, x.NoteId, x.ContactId, x.FileName, x.FileSize, x.FileExtension, x.ContentType, x.CreatedByPersonId, x.LastUpdatedByPersonId, x.LastUpdatedDateTimeUtc, x.Url });

            builder.Entity<ContactFileDataModel>()
                .HasIndex(x => x.NoteId)
                .IncludeProperties(x => new { x.Id, x.ContactId, x.CreatedDateTimeUtc, x.FileName, x.FileSize, x.FileExtension, x.ContentType, x.CreatedByPersonId, x.LastUpdatedByPersonId, x.LastUpdatedDateTimeUtc, x.Url });

            builder.Entity<SharedFileDataModel>()
                .HasKey(x => new { x.PersonId, x.FileId });

            builder.Entity<SharedFileDataModel>()
                .HasOne(p => p.Person)
                .WithMany(p => p.SharedFiles)
                .HasForeignKey(p => p.PersonId);

            builder.Entity<SharedFileDataModel>()
                .HasOne(p => p.File)
                .WithMany(p => p.SharedFiles)
                .HasForeignKey(p => p.FileId);

            builder.Entity<SharedFileDataModel>()
                .HasIndex(x => x.PersonId);

            /*
             * Contact File Access Type
             */

            // keys
            builder.Entity<ContactFileAccessTypeDataModel>()
                .HasKey(x => new { x.FileId, x.RelationshipAccessType });

            /*
             * Contact Notes
             */

            // keys
            builder.Entity<ContactNoteDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<ContactNoteDataModel>()
                .HasOne(x => x.Task)
                .WithMany(x => x.Notes)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<ContactNoteDataModel>()
                .HasOne(x => x.ClientEnrolmentNote)
                .WithOne(x => x.Note);

            builder.Entity<ContactNoteDataModel>()
               .Property(e => e.ContentJson)
               .HasConversion<ContentJsonValueConverter>();

            builder.Entity<NoteFormFieldDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<NoteFormFieldDataModel>()
                .Property(x => x.Schema)
                .HasColumnType("jsonb");

            builder.Entity<NoteFormFieldDataModel>()
                .Property(e => e.Schema)
                .HasConversion<JObjectValueConverter>();

            builder.Entity<NoteFormFieldDataModel>()
                .HasOne(p => p.Note)
                .WithMany(p => p.FormFields)
                .HasForeignKey(p => p.NoteId);

            builder.Entity<NoteFormFieldDataModel>()
                .HasMany(p => p.Responses)
                .WithOne(p => p.FormField)
                .HasForeignKey(p => p.FormFieldId);

            builder.Entity<NoteSignatureDataModel>()
                .HasKey(x => new { x.NoteId, x.SignatureId });

            builder.Entity<NoteSignatureDataModel>()
                .HasOne(p => p.Note)
                .WithMany(p => p.NoteSignatures)
                .HasForeignKey(p => p.NoteId);

            builder.Entity<SignatureDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<NoteSummaryDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<NoteSummaryDataModel>()
                .HasOne(x => x.Note);

            builder.Entity<NoteFormResponseDataModel>()
               .HasKey(x => new { x.Id, x.NoteId });

            builder.Entity<NoteFormResponseDataModel>()
              .HasOne(x => x.FormField)
              .WithMany(x => x.Responses)
              .HasForeignKey(x => x.FormFieldId);

            builder.Entity<NoteFormResponseDataModel>()
                .Property(x => x.Response)
                .HasColumnType("jsonb");

            builder.Entity<NoteFormResponseDataModel>()
                .Property(e => e.Response)
                .HasConversion<JObjectValueConverter>();

            builder.Entity<ContactNoteDataModel>()
                .HasMany(i => i.SharedNotes)
                .WithOne(pi => pi.Note)
                .HasForeignKey(pi => pi.NoteId);

            // indexes
            builder.Entity<ContactNoteDataModel>()
                .HasIndex(x => new { x.ContactId, x.CreatedDateTimeUtc })
                .IncludeProperties(x => new { x.Id, x.ProviderId, x.CreatedByPersonId, x.LastUpdatedByPersonId, x.LastUpdatedDateTimeUtc });

            builder.Entity<ContactNoteDataModel>()
                .HasIndex(x => new { x.ProviderId, x.CreatedDateTimeUtc })
                .IncludeProperties(x => new { x.Id, x.ContactId, x.CreatedByPersonId, x.LastUpdatedByPersonId, x.LastUpdatedDateTimeUtc });

            builder.Entity<ContactNoteDataModel>()
                .HasMany(x => x.Tags)
                .WithOne(x => x.Note);

            builder.Entity<ContactNoteDataModel>()
                .HasMany(i => i.SharedNotes)
                .WithOne(pi => pi.Note)
                .HasForeignKey(pi => pi.NoteId);

            builder.Entity<ContactNoteDataModel>()
                .HasMany(i => i.FormFields)
                .WithOne(pi => pi.Note)
                .HasForeignKey(pi => pi.NoteId);

            builder.Entity<ContactNoteDataModel>()
                .HasMany(i => i.NoteSignatures)
                .WithOne(pi => pi.Note)
                .HasForeignKey(pi => pi.NoteId);

            builder.Entity<ContactNoteDataModel>()
                .Property(x => x.Diagnoses)
                .HasColumnType("jsonb");

            builder.Entity<SharedNoteDataModel>()
                .HasKey(x => new { x.PersonId, x.NoteId });

            builder.Entity<SharedNoteDataModel>()
                .HasOne(p => p.Person)
                .WithMany(p => p.SharedNotes)
                .HasForeignKey(p => p.PersonId);

            builder.Entity<SharedNoteDataModel>()
                .HasOne(p => p.Note)
                .WithMany(p => p.SharedNotes)
                .HasForeignKey(p => p.NoteId);

            builder.Entity<SharedNoteDataModel>()
                .HasIndex(x => x.PersonId);

            // note forms

            builder.Entity<NoteFormFieldDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<NoteFormFieldDataModel>()
                .Property(x => x.Schema)
                .HasColumnType("jsonb");

            builder.Entity<NoteFormFieldDataModel>()
                .Property(e => e.Schema)
                .HasConversion<JObjectValueConverter>();

            builder.Entity<TemplateAttachmentDataModel>()
                .HasKey(x => new { x.TemplateId, x.FileId });

            builder.Entity<ContactNoteAttachmentDataModel>()
                .HasKey(x => new { x.NoteId, x.FileId });

            builder.Entity<FormResponseAttachmentDataModel>()
                .HasKey(x => new { x.FormResponseId, x.FileId });

            builder.Entity<PublicTemplateAttachmentDataModel>()
                .HasKey(x => new { x.PublicTemplateId, x.FileId });

            /*
             * Contact Note Access Type
             */

            // keys
            builder.Entity<ContactNoteAccessTypeDataModel>()
                .HasKey(x => new { x.NoteId, x.RelationshipAccessType });

            /*
             * Tags
             */

            builder.Entity<TagDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<ContactTagDataModel>()
               .HasKey(x => new { x.ContactId, x.TagId });

            builder.Entity<ContactNoteTagDataModel>()
                .HasKey(x => new { x.NoteId, x.TagId });

            builder.Entity<TagDataModel>()
                .HasIndex(x => new { x.ProviderId, x.Type });

            /*
             * Services
             */

            builder.Entity<ProviderItemDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<ProviderItemDataModel>()
                .HasIndex(x => x.ProviderId);

            builder.Entity<ProviderItemDataModel>()
                .HasMany(i => i.GroupItems)
                .WithOne(pi => pi.Item)
                .HasForeignKey(pi => pi.ProviderItemId);

            /*
             * Staff Services
             */

            builder.Entity<ProviderStaffItemDataModel>()
                .HasKey(x => new { x.ProviderId, x.PersonId, x.ItemId });

            builder.Entity<ProviderStaffItemDataModel>()
                .HasOne(x => x.Staff)
                .WithMany(s => s.Services)
                .HasForeignKey(x => new { x.ProviderId, x.PersonId });

            /*
             * Tasks
             */

            // keys
            builder.Entity<TaskDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<TaskContactDataModel>()
                .HasKey(x => new { x.TaskId, x.ContactId });

            builder.Entity<TaskContactDataModel>()
                .Property(x => x.TaskContactStatus)
                .HasDefaultValue(TaskContactStatus.Confirmed);

            builder.Entity<TaskDataModel>()
                .HasMany(x => x.Billables)
                .WithOne(y => y.Task)
                .HasForeignKey(x => x.TaskId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<TaskContactDataModel>()
                .Property(x => x.Attending)
                .HasDefaultValue(true);

            builder.Entity<TaskStaffDataModel>()
                .HasKey(x => new { x.TaskId, x.PersonId });

            builder.Entity<TaskDataModel>()
                .HasMany(x => x.Files)
                .WithOne(x => x.Task)
                .HasForeignKey(x => x.TaskId);

            builder.Entity<TaskDataModel>()
                .HasMany(x => x.Contacts)
                .WithOne(x => x.Task)
                .HasForeignKey(x => x.TaskId);

            builder.Entity<TaskDataModel>()
                .HasIndex(x => new { x.ParentId, x.StartDate })
                .HasFilter("parent_id IS NOT NULL");

            builder.Entity<TaskDataModel>()
                .HasIndex(x => new { x.ProviderId, x.StartDate, x.EndDate, x.OccurrenceEndDate });

            builder.Entity<TaskDataModel>()
                .HasIndex(x => x.ExternalCalendarId);

            builder.Entity<TaskDataModel>()
                .Property(x => x.ContactReminderConfigs)
                .HasColumnType("jsonb");

            builder.Entity<TaskDataModel>()
                .Property(x => x.ItemsSnapshot)
                .HasColumnType("jsonb");

            builder.Entity<TaskDataModel>()
                .Property(x => x.LocationsSnapshot)
                .HasColumnType("jsonb");

            builder.Entity<TaskDataModel>()
                .Property(x => x.IsBillingV2)
                .HasDefaultValue(false);

            builder.Entity<TaskDataModel>()
            .HasQueryFilter(x => !x.DeletedAtUtc.HasValue);

            // reminders

            builder.Entity<ReminderSettingsDataModel>()
                .HasKey(x => new { x.Id });

            builder.Entity<ReminderSettingsDataModel>()
                .HasOne(x => x.Provider)
                .WithMany(x => x.ReminderSettings);

            builder.Entity<ReminderSettingsDataModel>()
                .Property(x => x.DeliveryType)
                .HasDefaultValue(ReminderDeliveryType.Email);

            builder.Entity<ReminderJobDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<ReminderJobDataModel>()
                .HasOne(x => x.ReminderSettings)
                .WithMany(x => x.ReminderJobs)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<ReminderJobDataModel>()
                .HasIndex(x => x.SendDateTimeUtc);

            /*
            * Templates
            */

            

            builder.Entity<TemplateFormFieldDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<TemplateFormFieldDataModel>()
                .Property(x => x.Schema)
                .HasColumnType("jsonb");

            builder.Entity<TemplateFormFieldDataModel>()
                .Property(e => e.Schema)
                .HasConversion<JObjectValueConverter>();

            builder.Entity<TemplateFormFieldDataModel>()
                .HasOne(p => p.Template)
                .WithMany(p => p.FormFields)
                .HasForeignKey(p => p.TemplateId);

            builder.Entity<TemplatesUsedByPersonDataModel>()
                .HasKey(x => new { x.PersonId, x.ProviderId });

            builder.Entity<PublicTemplateFormFieldDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<PublicTemplateFormFieldDataModel>()
                .Property(x => x.Schema)
                .HasColumnType("jsonb");

            builder.Entity<PublicTemplateFormFieldDataModel>()
                .Property(e => e.Schema)
                .HasConversion<JObjectValueConverter>();

            builder.Entity<PublicTemplateFormFieldDataModel>()
                .HasOne(p => p.PublicTemplate)
                .WithMany(p => p.FormFields)
                .HasForeignKey(p => p.PublicTemplateId);

            



            builder.Entity<PublicTemplateTagDataModel>()
                .HasKey(x => new { x.PublicTemplateId, x.Title });

            builder.Entity<PublicTemplateTagDataModel>().HasIndex(x => x.Title);

            // Client enrolment

            builder.Entity<ClientEnrolmentDataModel>().HasKey(x => x.Token);

            builder.Entity<ClientEnrolmentTemplateDataModel>().HasKey(x => new { x.Token, x.TemplateId });

            builder.Entity<ClientEnrolmentTemplateDataModel>().HasOne(x => x.ClientEnrolment).WithMany(x => x.Templates).HasForeignKey(x => x.Token);

            builder.Entity<ClientEnrolmentNoteDataModel>().HasKey(x => new { x.EnrolmentToken, x.NoteId });

            builder.Entity<ClientEnrolmentNoteDataModel>().HasOne(x => x.ClientEnrolment).WithMany(x => x.Notes).HasForeignKey(x => x.EnrolmentToken);

            // Billing

            builder.Entity<BillingAccountDataModel>().HasKey(x => x.Id);

            builder.Entity<BillingAccountDataModel>()
                .HasOne<ProviderDataModel>(x => x.Provider);

            builder.Entity<BillingAccountDataModel>()
                .HasIndex(x => x.StripeSubscriptionId);

            builder.Entity<UsageDataModel>().HasNoKey().ToView(null);

            // Provider Contact Field Settings

            builder.Entity<ProviderContactFieldSettingsDataModel>()
                .HasKey(x => x.ProviderId);

            builder.Entity<ProviderContactFieldSettingsDataModel>()
                .HasOne<ProviderDataModel>(x => x.Provider)
                .WithOne()
                .HasForeignKey<ProviderContactFieldSettingsDataModel>(x => x.ProviderId);

            builder.Entity<ProviderContactFieldSettingsDataModel>()
                .Property(x => x.Categories)
                .HasColumnType("jsonb");

            // Onboarding
            builder.Entity<OnboardingDataModel>()
                .HasKey(x => x.Id);

            builder.Entity<OnboardingDataModel>()
                .HasOne(x => x.Person)
                .WithMany();

            builder.Entity<OnboardingDataModel>()
                .Property(x => x.Steps)
                .HasColumnType("jsonb");
            
            builder.Entity<OnboardingDataModel>()
                .Property(x => x.Setup)
                .HasColumnType("jsonb");


            // Provider Permissions

            builder.Entity<ProviderPermissionsDataModel>()
                .HasKey(x => new { x.ProviderId, x.PersonId });

            // icd code
            builder.Entity<ICDCodeDataModel>()
                .HasKey(x => x.Code);

            builder.Entity<ICDCodeDataModel>()
                .HasIndex(x => x.SearchVector)
                .HasMethod("GIN");

            // contact automation setting
            builder.Entity<ContactAutomationSettingDataModel>()
                .HasKey(x => new { x.ProviderId, x.ContactId });

            builder.ApplyConfigurationsFromAssembly(typeof(DataContext).Assembly);
        }
    }
}
