using System;
using System.Linq;
using AutoMapper;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Application.Refunds.Models;
using carepatron.core.Models.Payments.Models;
using carepatron.infra.sql.Models.BillableItem;
using carepatron.infra.sql.Models.Contact;
using carepatron.infra.sql.Models.Invoices;
using carepatron.infra.sql.Models.Payment;

namespace carepatron.infra.sql.Mappers
{
    public class PaymentProfile : Profile
    {
        public PaymentProfile()
        {
            CreateMap<Payment, PaymentDataModel>();
            CreateMap<PaymentDataModel, Payment>()
                .Include<PaymentDataModel, PaymentListEntry>()
                .ForMember(
                    x => x.InvoiceId,
                    opt =>
                    {
                        opt.PreCondition(x => x.InvoicePayment != null);
                        opt.MapFrom(x => x.InvoicePayment.InvoiceId);
                    }
                )
                .ForMember(
                    x => x.UnallocatedAmount,
                    opt =>
                        opt.MapFrom(
                            x =>
                                x.IsBillingV2 == true
                                    ? (
                                        (
                                            x.IsClientChargedFee
                                                ? x.ChargeAmount - x.Fee
                                                : x.ChargeAmount
                                        )
                                        - x.Allocations.Sum(x => x.Amount)
                                        - x.CreditAdjustments.Sum(x => x.Amount)
                                        - x.Refunds
                                            .Where(x => x.ReducePaymentAllocation)
                                            .Sum(x => x.Amount)
                                    )
                                    : 0
                        )
                );

            CreateMap<PaymentDataModel, PaymentListEntry>()
                .ForMember(x => x.Contact, opt => opt.MapFrom(x => x.Contact != null ? new ContactReference()
                {
                    Id = x.ContactId,
                    FirstName = x.Contact.FirstName,
                    LastName = x.Contact.LastName,
                } : null))
                .ForMember(x => x.PayerId, opt => opt.MapFrom(x => x.InsurancePayerId));


            CreateMap<PaymentIntent, PaymentIntentDataModel>().ReverseMap();
            CreateMap<PaymentIntentDataModel, PaymentIntent>();

            CreateMap<PaymentMethod, PaymentMethodDataModel>();

            // Notes on using ProjectTo() with EF navigation properties:
            // - AutoMapper's LINQ projection will build the SQL to load related entities in a single query,
            //   no need for explicit eager loading with Include().
            // - Filters on collection properties must be specified in the map,
            //   filters specified in Include() will have no effect.
            // - Use ExplicitExpansion to only load these properties when needed.
            // Reference: https://docs.automapper.org/en/stable/Queryable-Extensions.html#preventing-lazy-loading-select-n-1-problems

            CreateMap<PaymentMethodDataModel, PaymentMethod>()
                .ForMember(
                    x => x.PaymentMethodAuthorizations,
                    opt =>
                    {
                        opt.MapFrom(x => x.PaymentMethodAuthorizations);
                        opt.ExplicitExpansion();
                    }
                );

            CreateMap<PaymentMethodAuthorizationDataModel, PaymentMethodAuthorization>()
                .ForMember(x => x.Contact, opt => opt.MapFrom(x => x.Contact))
                .ForMember(x => x.Provider, opt => opt.MapFrom(x => x.Contact.Provider))
                .ForMember(x => x.Authorized, opt => opt.MapFrom(x => true))
                .ReverseMap();

            // Mapping for unauthorised contacts
            CreateMap<ContactDataModel, PaymentMethodAuthorization>()
                .ForMember(x => x.Contact, opt => opt.MapFrom(x => x))
                .ForMember(x => x.Provider, opt => opt.MapFrom(x => x.Provider))
                .ForMember(x => x.Authorized, opt => opt.MapFrom(x => false));

            CreateMap<InvoiceDataModel, InvoiceReference>()
                .ForMember(x => x.InvoiceId, opt => opt.MapFrom(x => x.Id))
                .ForMember(x => x.InvoiceStatus, opt => opt.MapFrom(x => x.Status))
                .ForMember(x => x.IssueDate, opt => opt.MapFrom(x => x.IssueDate))
                .ForMember(x => x.InvoiceNumber, opt => opt.MapFrom(x => x.Number))
                .ForMember(x => x.IssuedCredits, opt => opt.MapFrom(x => x.IssuedCredits));

            CreateMap<Refund, RefundReference>();

            CreateMap<PaymentAllocationDataModel, PaymentAllocation>().ReverseMap();

            CreateMap<PaymentDataModel, PaymentReference>().ReverseMap();

            CreateMap<PaymentDataModel, ContactPayment>()
                .ForMember(x => x.Amount, opt => opt.MapFrom(x => x.ChargeAmount))
                .ForMember(
                    x => x.DisplayAmount,
                    opt =>
                        opt.MapFrom(
                            x => x.IsClientChargedFee ? x.ChargeAmount - x.Fee : x.ChargeAmount
                        )
                )
                .ForMember(
                    x => x.Invoices,
                    opt =>
                        opt.MapFrom(
                            x =>
                                x.Allocations
                                    .Where(
                                        a =>
                                            a.InvoiceLineItemId != null && a.InvoiceLineItem != null
                                    )
                                    .Select(a => a.InvoiceLineItem.Invoice)
                                    .DistinctBy(x => x.Id)
                        )
                )
                .ForMember(x => x.Refunds, opt => opt.MapFrom(x => x.Refunds))
                .ForMember(x => x.IssuedCredits, opt => opt.MapFrom(x => x.CreditAdjustments))
                .ForMember(
                    x => x.UnallocatedAmount,
                    opt =>
                        opt.MapFrom(
                            x =>
                                x.IsBillingV2 == true
                                    ? (
                                        (
                                            x.IsClientChargedFee
                                                ? x.ChargeAmount - x.Fee
                                                : x.ChargeAmount
                                        )
                                        - x.Allocations.Sum(x => x.Amount)
                                        - x.CreditAdjustments.Sum(x => x.Amount)
                                        - x.Refunds
                                            .Where(x => x.ReducePaymentAllocation)
                                            .Sum(x => x.Amount)
                                    )
                                    : 0
                        )
                );

            CreateMap<PaymentDataModel, PaymentDetail>()
                .ForMember(x => x.PaymentMethod, opt => opt.MapFrom(x => x.Type))
                .ForMember(x => x.IssuedCredits, opt => opt.MapFrom(x => x.CreditAdjustments))
                .ForMember(x => x.Refunds, opt => opt.MapFrom(x => x.Refunds));

            CreateMap<PaymentAllocationDataModel, PaymentAllocationDetail>()
                .ForMember(
                    x => x.Invoice,
                    opt =>
                        opt.MapFrom(
                            x => x.InvoiceLineItem != null ? x.InvoiceLineItem.Invoice : null
                        )
                )
                .ForMember(
                    x => x.Claim,
                    opt => opt.MapFrom(x => x.ClaimLine != null ? x.ClaimLine.InsuranceClaim : null))
                .ForMember(
                    x => x.Description,
                    opt =>
                        opt.MapFrom(
                            x =>
                                x.Payment.PayerType == PayerType.Insurance
                                    ? x.BillableItem.Description
                                    : x.InvoiceLineItem != null
                                        ? x.InvoiceLineItem.Description
                                        : null
                        )
                )
                .ForMember(
                    x => x.Detail,
                    opt =>
                        opt.MapFrom(
                            x =>
                                x.Payment.PayerType == PayerType.Insurance
                                    ? x.BillableItem.Detail
                                    : x.InvoiceLineItem != null
                                        ? x.InvoiceLineItem.Detail
                                        : null
                        )
                )
                .ForMember(
                    x => x.Paid,
                    opt =>
                        opt.MapFrom(
                            x =>
                                x.Payment.PayerType == PayerType.Insurance
                                    ? x.BillableItem.InsurancePaid
                                    : x.InvoiceLineItem != null
                                        ? x.InvoiceLineItem.Allocations.Sum(x => x.Amount)
                                        : 0
                        )
                )
                .ForMember(
                    x => x.Amount,
                    opt =>
                        opt.MapFrom(
                            x =>
                                x.Payment.PayerType == PayerType.Insurance
                                    ? x.BillableItem.InsuranceAmount
                                    : x.InvoiceLineItem != null
                                        ? x.InvoiceLineItem.Price
                                          * x.InvoiceLineItem.Units
                                          * (1 + x.InvoiceLineItem.SalesTaxRate / 100)
                                        : 0l
                        )
                )
                .ForMember(x => x.Allocated, opt => opt.MapFrom(x => x.Amount))
                .ForMember(
                    x => x.Date,
                    opt =>
                        opt.MapFrom(
                            x =>
                                x.Payment.PayerType == PayerType.Insurance
                                    ? x.BillableItem.Date
                                    : x.InvoiceLineItem != null
                                        ? x.InvoiceLineItem.Date
                                        : null
                        )
                )
                .ReverseMap();

            CreateMap<InvoiceLineItemDataModel, InvoiceUnallocatedItem>()
                .ForMember(x => x.BillableItemId, opt => opt.MapFrom(x => x.BillableItemId))
                .ForMember(x => x.InvoiceLineItemId, opt => opt.MapFrom(x => x.Id))
                .ForMember(x => x.Date, opt => opt.MapFrom(x => x.Date))
                .ForMember(x => x.CurrencyCode, opt => opt.MapFrom(x => x.CurrencyCode))
                .ForMember(
                    x => x.Amount,
                    opt => opt.MapFrom(x => x.Price * x.Units * (1 + x.SalesTaxRate / 100))
                )
                .ForMember(x => x.Description, opt => opt.MapFrom(x => x.Description))
                .ForMember(x => x.Detail, opt => opt.MapFrom(x => x.Detail))
                .ForMember(
                    x => x.Paid,
                    opt =>
                        opt.MapFrom(
                            x => x.Allocations != null ? x.Allocations.Sum(x => x.Amount) : 0
                        )
                )
                .ReverseMap();

            CreateMap<BillableItemDataModel, InsuranceUnallocatedItem>()
                .ForMember(x => x.BillableItemId, opt => opt.MapFrom(x => x.Id))
                .ForMember(x => x.Date, opt => opt.MapFrom(x => x.Date))
                .ForMember(x => x.Description, opt => opt.MapFrom(x => x.Description))
                .ForMember(x => x.Detail, opt => opt.MapFrom(x => x.Detail))
                .ForMember(x => x.Amount, opt => opt.MapFrom(x => x.InsuranceAmount))
                .ForMember(x => x.Paid, opt => opt.MapFrom(x => x.InsurancePaid))
                .ReverseMap();

            CreateMap<PaymentAllocationDetail, PaymentAllocation>()
                .ForMember(x => x.Amount, opt => opt.MapFrom(x => x.Allocated))
                .ReverseMap();
        }
    }
}
