using AutoMapper;
using carepatron.core.Models.Invoices;
using carepatron.infra.sql.Models.Contact;
using carepatron.infra.sql.Models.Invoices;
using carepatron.infra.sql.Models.Person;
using System.Linq;
using carepatron.core.Application.Invoices.Extensions;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.TaxRates.Models;
using carepatron.core.Application.Workspace.Billing.Models;
using carepatron.core.Extensions;

namespace carepatron.infra.sql.Mappers
{
    public class InvoiceProfile : Profile
    {
        public InvoiceProfile()
        {
            CreateMap<SimpleInvoice, InvoiceDataModel>();
            CreateMap<InvoiceDataModel, SimpleInvoice>()
                .ForMember(x => x.TaskIds, opt => opt.MapFrom(x => x.TaskInvoices.Select(x => x.TaskId)));

            CreateMap<Invoice, InvoiceDataModel>()
                .ForMember(x => x.InvoiceLineItems, opt => opt.MapFrom(y => y.LineItems))
                .ForMember(x => x.ContactDetail, opt => opt.MapFrom(y => y.ContactDetail))
                .ForMember(x => x.BillToDetail, opt => opt.MapFrom(y => y.BillToDetail))
                .ForMember(x => x.ProviderDetail, opt => opt.MapFrom(y => y.ProviderDetail))
                .ForMember(x => x.StaffDetail, opt => opt.MapFrom(y => y.StaffDetail))
                .ForMember(x => x.LogoId, opt => opt.MapFrom(x => x.Theme != null ? x.Theme.LogoId : null))
                .ForMember(x => x.Layout, opt => opt.MapFrom(x => x.Theme != null ? x.Theme.Layout : InvoiceLayout.Simple))
                .ForMember(x => x.ColorHex, opt => opt.MapFrom(x => x.Theme != null ? x.Theme.ColorHex : null))
                .ForMember(x => x.ShowCodes, opt => opt.MapFrom(x => x.Theme == null || x.Theme.ShowCodes))
                .ForMember(x => x.ShowLineItemTax, opt => opt.MapFrom(x => x.Theme == null || x.Theme.ShowLineItemTax))
                .ForMember(x => x.ShowUnits, opt => opt.MapFrom(x => x.Theme == null || x.Theme.ShowUnits))
                .ForMember(x => x.TaskInvoices, opt => opt.MapFrom(i => i.TaskId.HasValue
                    ? i.TaskIds.Append(i.TaskId.Value).Distinct().Select(x => new InvoiceTaskDataModel(i.Id, x))
                    : i.TaskIds.Distinct().Select(x => new InvoiceTaskDataModel(i.Id, x))));

            CreateMap<InvoiceDataModel, Invoice>()
                .Include<InvoiceDataModel, InvoiceListEntry>()
                .ForMember(x => x.LineItems, opt => opt.MapFrom(y => y.InvoiceLineItems.OrderBy(x => x.OrderIndex).ThenBy(x => x.Id)))
                .ForMember(x => x.ServiceReceiptIds, opt => opt.MapFrom(y => y.ServiceReceipts.Select(sr => sr.ServiceReceiptId)))
                .ForMember(x => x.ContactDetail, opt => opt.MapFrom(y => y.ContactDetail))
                .ForMember(x => x.BillToDetail, opt => opt.MapFrom(y => y.BillToDetail))
                .ForMember(x => x.ProviderDetail, opt => opt.MapFrom(y => y.ProviderDetail))
                .ForMember(x => x.StaffDetail, opt => opt.MapFrom(y => y.StaffDetail))
                .ForMember(x => x.TaskIds, opt => opt.MapFrom(x => x.TaskInvoices.Select(x => x.TaskId)))
                .ForMember(x => x.StaffIds, opt => opt.MapFrom(x => x.Staff.Select(y => y.PersonId)))
                .ForMember(x => x.AmountPaid, opt => opt.MapFrom(x => x.InvoiceLineItems
                    .Where(li => li.Allocations != null)
                    .Sum(li => li.Allocations.Sum(a => a.Amount))));

            CreateMap<InvoiceDataModel, SimpleInvoice>()
                .ForMember(x => x.Theme, opt => opt.MapFrom(y => new InvoiceTheme()
                {
                    LogoId = y.LogoId,
                    Layout = y.Layout,
                    ColorHex = y.ColorHex,
                    ShowCodes = y.ShowCodes,
                    ShowLineItemTax = y.ShowLineItemTax,
                    ShowUnits = y.ShowUnits
                }))
                .ForMember(x => x.TaskIds, opt => opt.MapFrom(x => x.TaskInvoices.Select(x => x.TaskId)));

            CreateMap<InvoiceDataModel, InvoiceListEntry>()
                .ForMember(x => x.Contact, opt => opt.MapFrom(y => y.Contact))
                .ForMember(x => x.BillTo, opt => opt.MapFrom(y => y.BillTo))
                .ForMember(x => x.Staff, opt => opt.MapFrom(y => y.Staff.Select(staff => staff.Person)))
                .ForMember(x => x.Payments, opt => opt.MapFrom(y => y.InvoicePayment.Select(ivp => ivp.Payment)))
                .ForMember(x => x.Theme, opt => opt.MapFrom(y => new InvoiceTheme()
                {
                    LogoId = y.LogoId,
                    Layout = y.Layout,
                    ColorHex = y.ColorHex,
                    ShowCodes = y.ShowCodes,
                    ShowLineItemTax = y.ShowLineItemTax,
                    ShowUnits = y.ShowUnits
                }))
                .ForMember(x => x.TaskIds, opt => opt.MapFrom(x => x.TaskInvoices.Select(x => x.TaskId)))
                .AfterMap((_, entry) => entry.CalculateTotals());

            CreateMap<InvoiceLineItem, InvoiceLineItemDataModel>()
                .ForMember(x => x.TaxRates, opt => opt.MapFrom(y => y.TaxRates))
                .ForMember(x => x.Allocations, opt => opt.Ignore()) // ignore creating allocations from invoice line items.
                .ReverseMap();

            CreateMap<ProviderTaxRate, ItemTaxRate>()
                .ForMember(x => x.TaxRateId, opt => opt.MapFrom(y => y.Id))
                .ReverseMap();

            CreateMap<ContactDataModel, InvoiceListEntryContact>()
                .ForMember(x => x.Name, opt => opt.MapFrom(y => y.FullName));

            CreateMap<PersonDataModel, InvoiceListEntryStaff>();
            CreateMap<PaymentDataModel, InvoiceListEntryPayment>();
            CreateMap<InvoiceDataModel, TaskContactInvoice>()
                .ForMember(x => x.TotalAmount, opt => opt.MapFrom(p => p.TaxPrice + p.TaxExclusivePrice));
        }
    }
}