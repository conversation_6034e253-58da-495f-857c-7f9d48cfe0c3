﻿using System;
using System.Collections.Generic;
using AutoMapper;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Models.Tags;
using carepatron.core.Utilities;
using carepatron.infra.sql.Models.Attachments;
using carepatron.infra.sql.Models.Media;
using carepatron.infra.sql.Models.Templates;
using System.Linq;
using carepatron.core.Application.Folders.Models;
using carepatron.infra.sql.Models.Folders;

namespace carepatron.infra.sql.Mappers
{
    public class TemplateProfile : Profile
    {
        public TemplateProfile()
        {
            CreateMap<Template, TemplateDataModel>()
                .ForMember(x => x.ContentJsonb, opt => opt.MapFrom(x => x.ContentJson));

            CreateMap<TemplateDataModel, Template>()
                .ForMember(x => x.ContentJson,
                    opt => opt.MapFrom(x => JObjectUtilities.GetContentJson(x.ContentJsonb, x.ContentJson)))
                .ForMember(x => x.Collection, opt => opt.MapFrom(x => x.TemplateCollection.Title))
                .ForMember(x => x.IsIntakeDefault, opt => opt.MapFrom(x => x.DefaultIntakeTemplate != null))
                .ForMember(x => x.Form, opt => opt.MapFrom(x => x.FormFields))
                .ForMember(x => x.FolderId, opt => opt.MapFrom(x => x.TemplateFolder.FolderId))
                .ForMember(x => x.IsFavorite, opt => opt.MapFrom((src, dest, destMember, context) =>
                {
                    if (context.TryGetItems(out var items) && items.TryGetValue("currentLoggedInPersonId", out var personId) && personId is not null)
                    {
                        return src.FavoriteByPersons.Any(y => y.TemplateId == src.Id && y.PersonId == (Guid)personId);
                    }

                    return false;
                }));
            

            CreateMap<TemplateDataModel, TemplateMeta>()
                .ForMember(x => x.FolderId, opt => opt.MapFrom(x => x.TemplateFolder.FolderId));
            
            CreateMap<PublicTemplateDataModel, PublicTemplateMeta>();

            CreateMap<TemplateTagDataModel, Tag>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Tag.Id))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Tag.Title))
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Tag.Type))
                .ForMember(dest => dest.ColorHex, opt => opt.MapFrom(src => src.Tag.ColorHex))
                .ForMember(dest => dest.ProviderId, opt => opt.MapFrom(src => src.Tag.ProviderId))
                .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.Tag.IsActive))
                .ReverseMap();
            
            CreateMap<TemplateDataModel, SimpleTemplate>()
                .ForMember(x => x.FolderId, opt => opt.MapFrom(x => x.TemplateFolder.FolderId))
                .ForMember(x => x.Collection, opt => opt.MapFrom(x => x.TemplateCollection.Title))
                .ForMember(x => x.IsIntakeDefault, opt => opt.MapFrom(x => x.DefaultIntakeTemplate != null));

            CreateMap<ICollection<TemplateFormFieldDataModel>, TemplateForm>()
                .ForPath(x => x.Fields, opt => opt.MapFrom(x => x.Where(y => !y.Deleted).ToDictionary(y => y.Id, y => y)));

            CreateMap<TemplateDataModel, IntakeTemplate>()
                .ForMember(x => x.Id, opt => opt.MapFrom(x => x.Id))
                .ForMember(x => x.HasForms, opt => opt.MapFrom(y => y.FormFields.Count != 0))
                .ForMember(x => x.IsIntakeDefault, opt => opt.MapFrom(x => x.DefaultIntakeTemplate != null))
                .ForMember(x => x.ProviderId, opt => opt.MapFrom(x => x.ProviderId))
                .ForMember(x => x.Collection, opt => opt.MapFrom(x => x.TemplateCollection.Title))
                .ForMember(x => x.Title, opt => opt.MapFrom(x => x.Title));

            CreateMap<DefaultIntakeTemplate, DefaultIntakeTemplateDataModel>()
                .ForMember(x => x.TemplateId, opt => opt.MapFrom(x => x.Id))
                .ForMember(x => x.CreatedByPersonId, opt => opt.MapFrom(x => x.CreatedByPersonId))
                .ForMember(x => x.ProviderId, opt => opt.MapFrom(x => x.ProviderId))
                .ForMember(x => x.CreatedDateTimeUtc, opt => opt.MapFrom(x => x.CreatedDateTimeUtc));

            CreateMap<DefaultIntakeTemplateDataModel, DefaultIntakeTemplate>()
                .ForMember(x => x.Id, opt => opt.MapFrom(x => x.TemplateId))
                .ForMember(x => x.CreatedByPersonId, opt => opt.MapFrom(x => x.CreatedByPersonId))
                .ForMember(x => x.ProviderId, opt => opt.MapFrom(x => x.ProviderId))
                .ForMember(x => x.CreatedDateTimeUtc, opt => opt.MapFrom(x => x.CreatedDateTimeUtc));

            CreateMap<PublicTemplate, PublicTemplateDataModel>()
                .ForMember(x => x.ContentJsonb, opt => opt.MapFrom(x => x.ContentJson))
                .ForMember(x => x.AiPrompts, opt => opt.MapFrom(x => x.AiPrompts))
                .ForMember(x => x.HasAiPrompts, opt => opt.MapFrom(x => x.HasAiPrompts))
                .ForMember(x => x.FormFields, opt => opt.Ignore())
                .ForMember(x => x.Professions, opt => opt.MapFrom(x => x.Professions.Select(profession => new PublicTemplateProfessionDataModel(x.Id, profession))));

            CreateMap<PublicTemplateDataModel, PublicTemplate>()
                .ForMember(x => x.Form, opt => opt.MapFrom(x => x))
                .ForMember(x => x.AiPrompts, opt => opt.MapFrom(x => x.AiPrompts))
                .ForMember(x => x.HasAiPrompts, opt => opt.MapFrom(x => x.HasAiPrompts))
                .ForMember(x => x.Collection, opt => opt.MapFrom(x => x.Collection))
                .ForMember(x => x.IsPublic, opt => opt.MapFrom(x => true))
                .ForMember(x => x.ContentJson, opt => opt.MapFrom(x => JObjectUtilities.GetContentJson(x.ContentJsonb, x.ContentJson)))
                .ForMember(x => x.Professions, opt => opt.MapFrom(x => x.Professions.Select(y => y.Profession).ToArray()));

            CreateMap<PublicTemplateTagDataModel, SimpleTag>()
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Title))
                .ReverseMap();

            CreateMap<Author, TemplateAuthor>().ReverseMap();
            CreateMap<TemplateCollection, TemplateCollectionDataModel>().ReverseMap();
            CreateMap<TemplatesUsedByPerson, TemplatesUsedByPersonDataModel>().ReverseMap();

            CreateMap<TemplateAttachment, TemplateAttachmentDataModel>()
                .ForMember(x => x.CreatedByPerson, opt => opt.Ignore())
                .ForMember(x => x.CreatedByPersonId, opt => opt.MapFrom(d => d.CreatedByPerson.Id))
                .ForMember(x => x.FileId, opt => opt.MapFrom(d => d.Id));

            CreateMap<PublicTemplateAttachment, PublicTemplateAttachmentDataModel>()
                .ForMember(x => x.CreatedByPerson, opt => opt.Ignore())
                .ForMember(x => x.CreatedByPersonId, opt => opt.MapFrom(d => d.CreatedByPerson.Id))
                .ForMember(x => x.FileId, opt => opt.MapFrom(d => d.Id));

            CreateMap<TemplateSharingConfig, TemplateSharingConfigDataModel>()
                .ReverseMap();

            CreateMap<TemplateFormFieldDataModel, TemplateFormField>()
                .ReverseMap();

            CreateMap<TemplateImportDataModel, TemplateImport>()
                .ReverseMap();

            CreateMap<PublicTemplateFormFieldDataModel, PublicTemplateFormField>()
                .ReverseMap();

            CreateMap<TemplateDataModel, TemplateForm>()
                .ForMember(x => x.Fields, opt => opt.MapFrom(d => d.FormFields.Where(ff => !ff.Deleted).ToDictionary(ff => ff.Id)));

            CreateMap<PublicTemplateDataModel, PublicTemplateForm>()
                .ForMember(x => x.Fields, opt => opt.MapFrom(d => d.FormFields.ToDictionary(ff => ff.Id)));

            CreateMap<TemplateAttachmentDataModel, TemplateAttachment>()
                .ForMember(x => x.Id, opt => opt.MapFrom(d => d.FileId))
                .ForMember(x => x.FileExtension, opt => opt.MapFrom(d => d.File.FileExtensions))
                .ForMember(x => x.FileName, opt => opt.MapFrom(d => d.File.FileName))
                .ForMember(x => x.FileSize, opt => opt.MapFrom(d => d.File.FileSize))
                .ForMember(x => x.ContentType, opt => opt.MapFrom(d => d.File.ContentType))
                .ForMember(x => x.MediaType, opt => opt.MapFrom(d => d.File.MediaType))
                .ForMember(x => x.Url, opt => opt.MapFrom(d => d.File.Url));

            CreateMap<PublicTemplateAttachmentDataModel, PublicTemplateAttachment>()
                .ForMember(x => x.Id, opt => opt.MapFrom(d => d.FileId))
                .ForMember(x => x.FileExtension, opt => opt.MapFrom(d => d.File.FileExtensions))
                .ForMember(x => x.FileName, opt => opt.MapFrom(d => d.File.FileName))
                .ForMember(x => x.FileSize, opt => opt.MapFrom(d => d.File.FileSize))
                .ForMember(x => x.ContentType, opt => opt.MapFrom(d => d.File.ContentType))
                .ForMember(x => x.MediaType, opt => opt.MapFrom(d => d.File.MediaType))
                .ForMember(x => x.Url, opt => opt.MapFrom(d => d.File.Url));

            CreateMap<TemplateDataModel, ConsentTemplate>();
            CreateMap<TemplateAiPromptDataModel, TemplateAiPrompt>().ReverseMap();
            CreateMap<PublicTemplateAiPromptDataModel, PublicTemplateAiPrompt>().ReverseMap();
            
            CreateMap<TemplateFolderDataModel, TemplateFolder>()
                .ForMember(x => x.ProviderId, opt => opt.MapFrom(x => x.Template.ProviderId))
                .ForMember(x => x.Title, opt => opt.MapFrom(x => x.Folder.Title))
                .ReverseMap();
            
            CreateMap<TempTemplateDataModel, TempTemplate>()
                .ReverseMap();
            
            CreateMap<TempPublicTemplateProfessionDataModel, TempPublicTemplateProfession>()
                .ReverseMap();
        }
    }
}