using System;
using System.Collections.Generic;

namespace carepatron.infra.sql.Models.Insurance;

public class InsuranceClaimAdjustmentDataModel
{
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public Guid? ClaimId { get; set; }
    public Guid? RemittanceItemId { get; set; }
    public string GroupCode { get; set; }
    public string ReasonCode { get; set; }
    public string CurrencyCode { get; set; }
    public decimal Amount { get; set; }
    public Guid? ServiceLineId { get; set; }
    public virtual InsuranceClaimDataModel Claim { get; set; }
    public virtual InsuranceClaimRemittanceItemDataModel RemittanceItem { get; set; }
}
