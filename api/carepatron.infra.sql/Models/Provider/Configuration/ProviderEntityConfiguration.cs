using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace carepatron.infra.sql.Models.Provider.Configuration;

public class ProviderEntityConfiguration : IEntityTypeConfiguration<ProviderDataModel>
{
    public void Configure(EntityTypeBuilder<ProviderDataModel> builder)
    {
        builder.Property(x => x.LastAccessedUtc)
            .HasDefaultValueSql("NOW() AT TIME ZONE 'UTC'");

        builder
            .HasMany(x => x.TaxRates)
            .WithOne()
            .HasForeignKey(x => x.ProviderId);
    }
}
