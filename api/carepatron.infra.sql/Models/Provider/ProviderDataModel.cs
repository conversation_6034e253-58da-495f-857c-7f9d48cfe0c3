﻿using carepatron.core.Application.Contacts.Models;
using carepatron.infra.sql.Models.Billing;
using carepatron.infra.sql.Models.Insurance;
using carepatron.infra.sql.Models.Referrals;
using carepatron.infra.sql.Models.Registration;
using carepatron.infra.sql.Models.Reminders;
using System;
using System.Collections.Generic;
using carepatron.core.Application.TaxRates.Models;

namespace carepatron.infra.sql.Models.Provider;

public class ProviderDataModel
{
    public Guid Id { get; set; }

    public string Name { get; set; }

    public string PhoneNumber { get; set; }

    public string Address { get; set; }

    public string CountryCode { get; set; }

    public string NationalProviderId { get; set; }

    public string Website { get; set; }

    public string PrimaryColorHex { get; set; }

    public DateTime CreatedDateTimeUtc { get; set; }

    public DateTime UpdatedDateTimeUtc { get; set; }

    public DateTime LastAccessedUtc { get; set; }

    public DateTime? DeletedAtUtc { get; set; }

    public Guid? DeletedByPersonId { get; set; }

    public string DeletedReason { get; set; }

    public string PartnerStackPartnerKey  { get; set; }

    // reference models

    public virtual ProviderLogoDataModel Logo { get; set; }

    public virtual RegisteredWorkspaceDataModel RegisteredWorkspace { get; set; }

    public virtual ICollection<ProviderLocationDataModel> Locations { get; set; }

    public virtual ICollection<ReminderSettingsDataModel> ReminderSettings { get; set; }

    public virtual ICollection<ProviderBillingSettingsDataModel> BillingSettings { get; set; }

    public virtual ICollection<ProviderTaxRate> TaxRates { get; set; }

    public virtual ICollection<ProviderReferralCodeDataModel> ProviderReferralCodes { get; set; }

    public virtual ICollection<ProviderReferralDataModel> ProviderReferrals { get; set; }

    public virtual ICollection<ProviderInsurancePayerDataModel> ProviderInsurancePayers { get; set; }

    public virtual ICollection<ProviderBillingProfileDataModel> ProviderBillingProfiles { get; set; }
}
