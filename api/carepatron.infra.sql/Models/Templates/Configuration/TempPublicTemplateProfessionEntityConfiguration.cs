using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace carepatron.infra.sql.Models.Templates.Configuration;

internal class TempPublicTemplateProfessionEntityConfiguration : IEntityTypeConfiguration<TempPublicTemplateProfessionDataModel>
{
    public void Configure(EntityTypeBuilder<TempPublicTemplateProfessionDataModel> builder)
    {
        builder.HasKey(x => new { x.PublicTemplateId, x.Profession });
        builder.HasIndex(x => x.Profession);
    }
}