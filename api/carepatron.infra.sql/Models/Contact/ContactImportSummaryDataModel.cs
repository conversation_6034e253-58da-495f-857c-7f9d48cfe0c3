using System;
using carepatron.core.Application.Contacts.Models;
using carepatron.infra.sql.Models.Person;
using carepatron.infra.sql.Models.Provider;

namespace carepatron.infra.sql.Models.Contact
{
    public class ContactImportSummaryDataModel
    {
        public Guid Id { get; set; }
        public Guid ProviderId { get; set; }
        public Guid FileId { get; set; }
        public string OriginalFileName { get; set; }
        public string FileName { get; set; }
        public long FileSize { get; set; }
        public string FileExtension { get; set; }
        public Guid? SchemaFileId { get; set; }
        public ImportSummaryStatus Status { get; set; }
        public ImportType ImportType { get; set; }
        public bool IsContact { get; set; }
        public Guid[] LastStatusSeenBy { get; set; }
        public Guid CreatedByPersonId { get; set; }
        public DateTime CreatedDateTimeUtc { get; set; }
        public DateTime? UpdatedDateTime { get; set; }
        public DateTime? CompletedDateTimeUtc { get; set; }

        public virtual ProviderDataModel Provider { get; set; }
        public virtual PersonDataModel CreatedByPerson { get; set; }
    }
}
