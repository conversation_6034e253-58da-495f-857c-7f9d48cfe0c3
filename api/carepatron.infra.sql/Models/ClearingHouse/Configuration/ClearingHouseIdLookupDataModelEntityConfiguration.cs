using carepatron.infra.sql.Models.ClearingHouse;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace carepatron.infra.sql.Models.History.Configuration;

public class ClearingHouseIdLookupDataModelEntityConfiguration
    : IEntityTypeConfiguration<ClearingHouseIdLookupDataModel>
{
    public void Configure(EntityTypeBuilder<ClearingHouseIdLookupDataModel> builder)
    {
        builder.HasKey(x => x.Identity);
        builder.Property(x => x.Identity)
            .ValueGeneratedOnAdd()
            .HasColumnType("bigint");

        builder.HasIndex(x => x.EntityId)
            .IsUnique();
    }
}
