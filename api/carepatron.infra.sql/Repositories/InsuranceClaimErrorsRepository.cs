using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Repositories.Insurance;
using carepatron.infra.sql.Models.Insurance;
using carepatron.infra.sql.Utils;
using Microsoft.EntityFrameworkCore;

namespace carepatron.infra.sql.Repositories.Insurance;

public class InsuranceClaimErrorsRepository(DataContext dataContext, IMapper mapper) : IInsuranceClaimErrorsRepository
{
    public async Task ClearForClaims(Guid[] insuranceClaimIds, Guid providerId, CancellationToken cancellationToken = default)
    {
        var errors = await dataContext.InsuranceClaimErrors
            .Where(x => insuranceClaimIds.Contains(x.InsuranceClaimId) && x.ProviderId == providerId)
            .ToListAsync(cancellationToken);
        if (errors.Count == 0) return;
        errors.ForEach(item => item.Status = ClaimErrorStatus.Inactive);
        await dataContext.SaveChangesAsync(cancellationToken);
    }

    public async Task<InsuranceClaimError[]> GetByClaimId(Guid insuranceClaimId, Guid providerId, CancellationToken cancellationToken = default)
    {
        var errors = await dataContext.InsuranceClaimErrors
            .AsNoTracking()
            .Where(x => x.InsuranceClaimId == insuranceClaimId && x.ProviderId == providerId && x.Status == ClaimErrorStatus.Active)
            .ProjectTo<InsuranceClaimError>(mapper.ConfigurationProvider)
            .ToArrayAsync(cancellationToken);
        if (errors is null) return [];
        return [.. errors];
    }

    public async Task Upsert(Guid insuranceClaimId, Guid providerId, InsuranceClaimError[] claimErrors, CancellationToken cancellationToken = default)
    {
        var dbErrors = await dataContext.InsuranceClaimErrors
            .Where(x => x.InsuranceClaimId == insuranceClaimId && x.ProviderId == providerId)
            .ToListAsync(cancellationToken);

        EntityUtilities.UpdateList(
            dataContext,
            dbErrors,
            claimErrors,
            (existing, request) =>
                // @TODO: Ideally we should check for field instead of message.
                // For now, we check if message is same AND status is different, then
                // we can add the error as a new record
                existing.Message?.ToLower() == request.Message?.ToLower()
                && existing.Status != request.Status
                && existing.ProviderId == request.ProviderId
                && existing.InsuranceClaimId == request.InsuranceClaimId,
            mapper.Map<InsuranceClaimErrorDataModel>,
            (request, updated, existing) =>
            {
                updated.Id = existing.Id;
                updated.ProviderId = providerId;
                updated.InsuranceClaimId = insuranceClaimId;
            },
            x => x.Status = ClaimErrorStatus.Inactive
        );

        await dataContext.SaveChangesAsync(cancellationToken);
    }
}
