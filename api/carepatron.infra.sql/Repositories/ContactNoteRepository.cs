﻿using AutoMapper;
using carepatron.core.Abstractions;
using carepatron.core.Application.ClientPortal.Models;
using carepatron.core.Application.ClientResources.Models;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Notes.Models;
using carepatron.core.Application.Sharing.Models;
using carepatron.core.Authorization.Models;
using carepatron.core.Authorization.ResourceNames;
using carepatron.core.Extensions;
using carepatron.core.Models.Common;
using carepatron.core.Models.Notes;
using carepatron.core.Models.Pagination;
using carepatron.core.Repositories.Files;
using carepatron.core.Repositories.Notes;
using carepatron.core.Utilities;
using carepatron.infra.sql.Mappers;
using carepatron.infra.sql.Models.ICDCode;
using carepatron.infra.sql.Models.Note;
using carepatron.infra.sql.Models.Temp;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Npgsql;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using carepatron.core.Models.Diagnoses;
using SharedNote = carepatron.core.Application.Notes.Models.SharedNote;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Trash.Models;
using carepatron.core.Application.Users.Models;
using Serilog;

namespace carepatron.infra.sql.Repositories
{
    public class ContactNoteRepository : INoteRepository, IActivityCheck
    {
        private readonly DataContext dataContext;
        private readonly IMapper mapper;
        private readonly IFileStorageRepository fileStorageRepository;

        public ContactNoteRepository(DataContext dataContext, IMapper mapper, IFileStorageRepository fileStorageRepository)
        {
            this.dataContext = dataContext;
            this.mapper = mapper;
            this.fileStorageRepository = fileStorageRepository;
        }

        public async Task<Note> Create(Note note)
        {
            var model = mapper.Map<ContactNoteDataModel>(note);

            model.Tags = note.Tags
                .EmptyIfNull()
                .Select(x => new ContactNoteTagDataModel(note.Id, x.Id))
                .PreferExisingItems(model.Tags, x => x.TagId)
                .ToList();

            dataContext.Add(model);

            await dataContext.SaveChangesAsync();

            return note;
        }

        public async Task<IList<Note>> Create(IList<Note> notes)
        {
            var model = mapper.Map<IList<ContactNoteDataModel>>(notes);
            var tags = notes.ToDictionary(x => x.Id, x => x.Tags);

            foreach (var note in model)
            {
                note.Tags = tags[note.Id]
                    .EmptyIfNull()
                    .Select(x => new ContactNoteTagDataModel(note.Id, x.Id))
                    .PreferExisingItems(note.Tags, x => x.TagId)
                    .ToList();
            }

            await dataContext.AddRangeAsync(model);
            await dataContext.SaveChangesAsync();

            return notes;
        }

        public async Task<IList<ICDCode>> GetClientPreviousNoteDiagnoses(Guid contactId, DateTime createdDateTimeUtc)
        {
            var model = await dataContext
                .ContactNotes
                .OrderByDescending(x => x.CreatedDateTimeUtc)
                .FirstOrDefaultAsync(x => x.ContactId == contactId &&
                x.CreatedDateTimeUtc < createdDateTimeUtc);

            var diagnoses = mapper.Map<IList<ICDCode>>(model?.Diagnoses);

            return diagnoses;
        }

        public async Task Delete(Guid id)
        {
            var model = await dataContext.ContactNotes.Where(x => x.Id == id).FirstOrDefaultAsync();

            dataContext.SoftDelete(model);

            await dataContext.SaveChangesAsync();
        }

        public async Task<NoteDetail> Get(Guid id, bool includeSoftDeleted)
        {
            var query = dataContext.ContactNotes.AsQueryable();
            if (includeSoftDeleted)
            {
                query = query.IgnoreQueryFilters();
            }

            var model = await query
                .Where(x => x.Id == id)
                .FirstOrDefaultAsync();

            var note = mapper.Map<NoteDetail>(model);

            return note;
        }

        public async Task<NoteDetail> Get(Guid id, Guid? providerId = null)
        {
            var query = dataContext.ContactNotes
                .Include(x => x.RolesAccessibleBy)
                .Include(x => x.Files)
                .ThenInclude(x => x.Tags)
                .ThenInclude(x => x.Tag)
                .Include(x => x.FileAttachments)
                .Include(x => x.NoteSignatures)
                .Include(x => x.CreatedByPerson)
                    .ThenInclude(x => x.ProfilePhoto)
                .Include(x => x.SharedNotes)
                .Include(x => x.Task)
                .Include(x => x.Tags)
                .ThenInclude(x => x.Tag)
                .Where(x => x.Id == id);

            if (providerId != null)
            {
                query = query.Where(x => x.ProviderId == providerId);
            }

            query = IncludeFormFields(query);

            var model = await query
                .AsSplitQuery()
                .FirstOrDefaultAsync();

            var note = mapper.Map<NoteDetail>(model);

            if (note != null)
            {
                NoteUtilities.UnionAttachments(note, fileStorageRepository);
            }

            return note;
        }

        public async Task<IList<NoteDetail>> Get(Guid[] ids, Guid? providerId)
        {
            var query = dataContext.ContactNotes
                .Include(x => x.RolesAccessibleBy)
                .Include(x => x.Files)
                .ThenInclude(x => x.Tags)
                .ThenInclude(x => x.Tag)
                .Include(x => x.FileAttachments)
                .Include(x => x.NoteSignatures)
                .Include(x => x.CreatedByPerson)
                .ThenInclude(x => x.ProfilePhoto)
                .Include(x => x.SharedNotes)
                .Include(x => x.Task)
                .Include(x => x.Tags)
                .ThenInclude(x => x.Tag)
                .Where(x => ids.Contains(x.Id));

            if (providerId.HasValue)
            {
                query = query.Where(x => x.ProviderId == providerId);
            }

            query = IncludeFormFields(query);

            var model = await query
                .AsSplitQuery()
                .ToListAsync();

            var notes = mapper.Map<IList<NoteDetail>>(model);

            if (notes.Any())
            {
                foreach (var note in notes)
                {
                    NoteUtilities.UnionAttachments(note, fileStorageRepository);
                }
            }

            return notes;
        }

        public async Task<NoteMeta> GetMeta(Guid id, Guid? providerId = null)
        {
            var query = dataContext.ContactNotes
                .AsNoTracking()
                .Where(x => x.Id == id);

            if (providerId != null)
            {
                query = query.Where(x => x.ProviderId == providerId);
            }

            return await query
                .ProjectTo<NoteMeta>(mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();
        }

        public async Task<ClientPortalNote> GetClientPortalNote(Guid id)
        {
            var query = dataContext.ContactNotes.Include(x => x.Files)
                .Include(x => x.FileAttachments)
                .Include(x => x.NoteSignatures)
                .Include(x => x.CreatedByPerson)
                .Include(x => x.LockedByPerson)
                .Include(x => x.Provider)
                .Include(x => x.Contact)
                .Include(x => x.Tags).ThenInclude(x => x.Tag)
                .Where(x => x.Id == id);

            query = IncludeFormFields(query);

            var model = await query
                 .AsSplitQuery()
                 .FirstOrDefaultAsync();

            var note = mapper.Map<ClientPortalNote>(model);

            if (note != null)
            {
                NoteUtilities.UnionAttachments(note, fileStorageRepository);
            }

            return note;
        }

        public async Task<IntakeNote> GetIntakeNote(Guid id)
        {
            var query = dataContext.ContactNotes.Include(x => x.Files)
                .Include(x => x.FileAttachments)
                .Include(x => x.NoteSignatures)
                .Include(x => x.CreatedByPerson)
                .Include(x => x.LockedByPerson)
                .Include(x => x.Provider)
                .Include(x => x.ClientEnrolmentNote)
                .Include(x => x.Contact)
                .Include(x => x.Tags).ThenInclude(x => x.Tag)
                .Where(x => x.Id == id);

            query = IncludeFormFields(query);

            var model = await query
                .AsSplitQuery()
                .FirstOrDefaultAsync();

            var note = mapper.Map<IntakeNote>(model);

            if (note != null)
            {
                NoteUtilities.UnionAttachments(note, fileStorageRepository);
            }

            return note;
        }

        public async Task<PaginatedResult<NoteDetail>> Get(Guid contactId, Guid personId, RelationshipAccessType currentPersonRole, Guid[] tags, PaginationRequest pagination)
        {
            var query = dataContext.ContactNotes
                .Where(x => x.ContactId == contactId)
                .Where(x =>
                    (x.RolesAccessibleBy.Any(x => x.RelationshipAccessType == currentPersonRole)
                    && x.Status == NoteStatus.Published)
                    || x.CreatedByPersonId == personId);

            query = ApplyTagFilter(query, tags);

            query = IncludeFormFields(query);

            var models = await query
                .Include(x => x.RolesAccessibleBy)
                .Include(x => x.Files)
                .Include(x => x.FileAttachments)
                .Include(x => x.NoteSignatures)
                .Include(x => x.CreatedByPerson)
                .Include(x => x.Task)
                .Include(x => x.Tags).ThenInclude(x => x.Tag)
                .OrderByDescending(x => x.CreatedDateTimeUtc)
                .Skip(pagination.Offset)
                .Take(pagination.Limit + 1)
                .AsSplitQuery()
                .ToListAsync();

            var notes = mapper.Map<NoteDetail[]>(models.Take(pagination.Limit));

            if (notes != null)
            {
                foreach (var note in notes)
                {
                    NoteUtilities.UnionAttachments(note, fileStorageRepository);
                }
            }

            return new PaginatedResult<NoteDetail>(notes, new Pagination(notes.Length, pagination.Offset, models.Count > notes.Length));
        }

        public async Task<Note[]> GetTaskNotes(Guid providerId, Guid[] taskIds, Guid[] assignedStaff)
        {
            var query = dataContext.ContactNotes
                .Include(x => x.Task)
                .Where(x => x.ProviderId == providerId && x.TaskId != null && taskIds.Contains(x.TaskId.Value));

            if (assignedStaff?.Any() == true)
            {
                query = query.Where(x => x.Contact.AssignedStaff.Any(s => assignedStaff.Contains(s.PersonId)));
            }

            var models = await query
                .Include(x => x.RolesAccessibleBy)
                .Include(x => x.Files)
                .Include(x => x.FileAttachments)
                .Include(x => x.NoteSignatures)
                .Include(x => x.CreatedByPerson)
                .Include(x => x.SharedNotes)
                .Include(x => x.Task)
                .Include(x => x.Tags).ThenInclude(x => x.Tag)
                .OrderByDescending(x => x.CreatedDateTimeUtc)
                .AsSplitQuery()
                .ToListAsync();

            var notes = mapper.Map<NoteDetail[]>(models);

            if (notes != null)
            {
                foreach (var note in notes)
                {
                    NoteUtilities.UnionAttachments(note, fileStorageRepository);
                }
            }

            return notes;
        }

        public async Task<PaginatedResult<NoteDetail>> GetByProviderId(Guid providerId, RelationshipAccessType currentPersonRole, Guid[] tags, PaginationRequest pagination)
        {
            var query = dataContext.ContactNotes
                .Where(x => x.ProviderId == providerId)
                .Where(x => x.RolesAccessibleBy.Any(x => x.RelationshipAccessType == currentPersonRole));

            query = ApplyTagFilter(query, tags);

            query = IncludeFormFields(query);

            var models = await query
                .Include(x => x.RolesAccessibleBy)
                .Include(x => x.Files)
                .Include(x => x.FileAttachments)
                .Include(x => x.NoteSignatures)
                .Include(x => x.CreatedByPerson)
                .Include(x => x.Task)
                .Include(x => x.Tags).ThenInclude(x => x.Tag)
                .OrderByDescending(x => x.CreatedDateTimeUtc)
                .Skip(pagination.Offset)
                .Take(pagination.Limit + 1)
                .AsSplitQuery()
                .ToListAsync();

            var notes = mapper.Map<NoteDetail[]>(models.Take(pagination.Limit));

            if (notes != null)
            {
                foreach (var note in notes)
                {
                    NoteUtilities.UnionAttachments(note, fileStorageRepository);
                }
            }

            return new PaginatedResult<NoteDetail>(notes, new Pagination(notes.Length, pagination.Offset, models.Count > notes.Length));
        }

        [Obsolete]
        public async Task<PaginatedResult<NoteDetail>> GetByContactId(Guid contactId, Guid personId, Guid[] tags, PaginationRequest pagination)
        {
            var query = dataContext.ContactNotes
                .Where(x => x.ContactId == contactId)
                .Where(x => x.CreatedByPersonId == personId || x.Status == NoteStatus.Published);

            query = ApplyTagFilter(query, tags);

            query = IncludeFormFields(query);

            var models = await query
                 .Include(x => x.Files)
                 .Include(x => x.FileAttachments)
                 .Include(x => x.NoteSignatures)
                 .Include(x => x.CreatedByPerson)
                 .Include(x => x.Task)
                 .Include(x => x.Tags).ThenInclude(x => x.Tag)
                 .OrderByDescending(x => x.CreatedDateTimeUtc)
                 .Skip(pagination.Offset)
                 .Take(pagination.Limit + 1)
                 .AsSplitQuery()
                 .ToListAsync();

            var notes = mapper.Map<NoteDetail[]>(models.Take(pagination.Limit));

            if (notes != null)
            {
                foreach (var note in notes)
                {
                    NoteUtilities.UnionAttachments(note, fileStorageRepository);
                }
            }

            return new PaginatedResult<NoteDetail>(notes, new Pagination(notes.Length, pagination.Offset, models.Count > notes.Length));
        }

        public async Task<NoteDetail[]> GetByContactId(Guid contactId,
            Guid currentPersonId,
            Guid[] staffIds,
            Guid[] tags,
            DateTime? occurrenceDateTimeUtcFrom,
            DateTime? occurrenceDateTimeUtcTo,
            string title,
            ResourceTypes[] resourceTypes,
            TokenisedPaginationRequest pagination)
        {
            bool hasStaffFilter = staffIds.Length > 0;

            var query = dataContext.ContactNotes
                .Where(x => x.ContactId == contactId)
                .Where(x => x.Status == NoteStatus.Published || (x.CreatedByPersonId == currentPersonId && x.Status == NoteStatus.Draft));

            if (staffIds.Length > 0)
                query = query.Where(x => staffIds.Contains(x.CreatedByPersonId));

            if (occurrenceDateTimeUtcFrom.HasValue)
                query = query.Where(x => x.OccurrenceDateTimeUtc >= occurrenceDateTimeUtcFrom.Value);

            if (occurrenceDateTimeUtcTo.HasValue)
                query = query.Where(x => x.OccurrenceDateTimeUtc <= occurrenceDateTimeUtcTo.Value);

            if (!string.IsNullOrWhiteSpace(title))
                query = query.Where(x => EF.Functions.ILike(x.Title, $"%{title}%"));

            if (pagination.SortingDateTimeUtc.HasValue)
                query = query.Where(x => x.OccurrenceDateTimeUtc < pagination.SortingDateTimeUtc.Value);

            if (resourceTypes.Length > 0)
            {
                HashSet<ResourceTypes> resourceTypesHashset = resourceTypes.ToHashSet();

                var hasNoteType = resourceTypesHashset.Contains(ResourceTypes.Note);
                var hasFileType = resourceTypesHashset.Contains(ResourceTypes.File);
                var hasFormType = resourceTypesHashset.Contains(ResourceTypes.Form);
                var hasDiagnosisType = resourceTypesHashset.Contains(ResourceTypes.Diagnosis);

                if (!hasNoteType)
                    query = query.Where(x =>
                    (hasFileType && x.Files.Any(f => !f.IsEmbedded) || x.FileAttachments.Any(f => !f.IsEmbedded)) ||
                        (hasFormType && x.FormFields.Any()) ||
                        (hasDiagnosisType && x.Diagnoses.Any()));
            }

            query = ApplyTagFilter(query, tags, title);
            query = IncludeFormFields(query);

            var entities = await query
                .Include(x => x.Files)
                .Include(x => x.FileAttachments)
                .Include(x => x.NoteSignatures)
                .Include(x => x.SharedNotes)
                .Include(x => x.CreatedByPerson)
                .Include(x => x.RolesAccessibleBy)
                .Include(x => x.Task)
                .Include(x => x.FormFields).ThenInclude(x => x.Responses)
                .Include(x => x.Tags).ThenInclude(x => x.Tag)
                .AsSplitQuery()
                .OrderByDescending(x => x.OccurrenceDateTimeUtc)
                .Take(pagination.Limit + 1)
                .ToListAsync();

            var notes = mapper.Map<NoteDetail[]>(entities);

            if (notes != null)
            {
                foreach (var note in notes)
                {
                    NoteUtilities.UnionAttachments(note, fileStorageRepository);
                }
            }

            return notes;
        }

        public async Task<ClientPortalNote[]> GetClientNotes(Guid[] contactIds, Guid? personId, Guid? providerId, TokenisedPaginationRequest pagination)
        {
            IQueryable<ContactNoteDataModel> query = dataContext.ContactNotes;

            if (contactIds?.Any() == true)
            {
                query = query.Where(x => contactIds.Contains(x.ContactId));
            }
            else if (personId.HasValue)
            {
                query = query.Where(n => n.Contact.PersonId == personId);
            }

            if (personId.HasValue)
            {
                // where the note has been shared with the person.
                query = query.Where(x => x.SharedNotes.Any(s => s.PersonId == personId));
            }

            if (pagination != null && pagination.SortingDateTimeUtc.HasValue)
            {
                query = query.Where(x => x.OccurrenceDateTimeUtc < pagination.SortingDateTimeUtc);
            }

            if (providerId.HasValue)
            {
                query = query.Where(x => x.ProviderId == providerId.Value);
            }

            query = query = IncludeFormFields(query);

            query = query
                .Include(x => x.RolesAccessibleBy)
                .Include(x => x.Files)
                .Include(x => x.FileAttachments)
                .Include(x => x.NoteSignatures)
                .Include(x => x.SharedNotes)
                .Include(x => x.CreatedByPerson)
                .Include(x => x.Task)
                .OrderByDescending(x => x.OccurrenceDateTimeUtc);

            if (pagination != null)
            {
                query = query.Take(pagination.Limit + 1);
            }

            var models = await query
                .AsSplitQuery()
                .ToListAsync();

            var notes = mapper.Map<ClientPortalNote[]>(models);

            if (notes != null)
            {
                foreach (var note in notes)
                {
                    NoteUtilities.UnionAttachments(note, fileStorageRepository);
                }
            }

            return notes ?? Array.Empty<ClientPortalNote>();
        }

        public async Task<PaginatedResult<NoteDetail>> GetByContactIds(IDictionary<Guid, RelationshipAccessType> idAndAccessPairs, Guid personId, Guid[] tags, PaginationRequest pagination)
        {
            var paramModels = idAndAccessPairs.Select(x => new ContactAccessTempDataModel(x.Key, x.Value));

            var json = JsonConvert.SerializeObject(paramModels);

            var parameter = new NpgsqlParameter("@contact_access", NpgsqlTypes.NpgsqlDbType.Jsonb);
            parameter.Value = JsonConvert.SerializeObject(paramModels);

            const string sql = @"
select distinct cn.*
from carepatron.contact_notes cn
join jsonb_array_elements(@contact_access) as arr
    on (arr ->> 'contact_id')::uuid = cn.contact_id
join carepatron.contact_note_access_types cat
    on cat.note_id = cn.id and cat.relationship_access_type = (arr ->> 'relationship_access_type')::integer";

            var query = dataContext.ContactNotes
                .FromSqlRaw(sql, parameter)
                .Where(x => x.CreatedByPersonId == personId || x.Status == NoteStatus.Published);

            query = ApplyTagFilter(query, tags);

            query = IncludeFormFields(query);

            var models = await query
                .Include(x => x.RolesAccessibleBy)
                .Include(x => x.Files)
                .Include(x => x.FileAttachments)
                .Include(x => x.NoteSignatures)
                .Include(x => x.CreatedByPerson)
                .Include(x => x.Task)
                .Include(x => x.FormFields).ThenInclude(x => x.Responses)
                .Include(x => x.Tags).ThenInclude(x => x.Tag)
                .OrderByDescending(x => x.CreatedDateTimeUtc)
                .Skip(pagination.Offset)
                .Take(pagination.Limit + 1)
                .AsSplitQuery()
                .ToListAsync();

            var notes = mapper.Map<NoteDetail[]>(models.Take(pagination.Limit));

            if (notes != null)
            {
                foreach (var note in notes)
                {
                    NoteUtilities.UnionAttachments(note, fileStorageRepository);
                }
            }

            return new PaginatedResult<NoteDetail>(notes, new Pagination(notes.Length, pagination.Offset, models.Count > notes.Length));
        }

        public async Task<Note> Update(Note note, bool includeSoftDeleted = false)
        {
            var query = dataContext.ContactNotes.Where(x => x.Id == note.Id);

            if (includeSoftDeleted)
            {
                query = query.IgnoreQueryFilters();
            }

            var model = await query.FirstOrDefaultAsync();

            if (model == null)
            {
                Log.Error($"Note with id {note.Id} not found.");
                return null;
            }

            model.Title = note.Title;
            model.Content = note.Content;
            model.ContentJson = note.ContentJson;
            model.LastUpdatedByPersonId = note.LastUpdatedByPersonId;
            model.LastUpdatedDateTimeUtc = DateTime.UtcNow;
            model.Status = note.Status;
            model.Tags = note.Tags
                .EmptyIfNull()
                .Select(x => new ContactNoteTagDataModel(note.Id, x.Id))
                .PreferExisingItems(model.Tags, x => x.TagId)
                .ToList();

            model.RolesAccessibleBy.Clear();
            model.RolesAccessibleBy = ContactNoteMapper.Map(note.Id, note.RolesAccessibleBy);
            model.Diagnoses = note.Diagnoses
                .EmptyIfNull()
                .Select(x => new ICDCodeDataModel()
                {
                    Code = x.Code,
                    Description = x.Description
                })
                .ToArray();
            model.TaskId = note.TaskId;
            model.OccurrenceDateTimeUtc = note.OccurrenceDateTimeUtc;
            model.IsLocked = note.IsLocked;
            model.LockedByPersonId = note.LockedByPersonId;
            model.LockedDateTimeUtc = note.LockedDateTimeUtc;

            await dataContext.SaveChangesAsync();

            return note;
        }

        public async Task<SharedWith[]> SaveSharedNotes(Guid noteId, IList<SharedNote> sharedNotes)
        {
            var sharedByPerson = await dataContext.SharedNotes
                .Where(x => x.NoteId == noteId)
                .ToDictionaryAsync(_ => _.PersonId);

            var results = new List<SharedNoteDataModel>();
            foreach (var item in sharedNotes)
            {
                if (sharedByPerson.TryGetValue(item.PersonId, out var existingValue))
                {
                    existingValue.Role = item.Role;
                    existingValue.ProviderId = item.ProviderId;
                    results.Add(existingValue);
                }
                else
                {
                    var dataModel = mapper.Map<SharedNoteDataModel>(item);
                    dataContext.SharedNotes.Add(dataModel);
                    results.Add(dataModel);
                }
            }

            await dataContext.SaveChangesAsync();
            return mapper.Map<SharedWith[]>(results);
        }

        public async Task<SharedWith> UpdateSharedNoteRole(Guid noteId, Guid personId, ShareAccessLevel noteAccessLevel)
        {
            var sharedNote = await dataContext.SharedNotes
                .Where(x => x.PersonId == personId && x.NoteId == noteId)
                .FirstOrDefaultAsync();

            if (sharedNote == null)
            {
                return null;
            }

            sharedNote.Role = noteAccessLevel;

            await dataContext.SaveChangesAsync();

            return mapper.Map<SharedWith>(sharedNote);
        }

        public async Task RemoveSharedNoteByPersonId(Guid noteId, Guid personId)
        {
            var sharedNote = await dataContext.SharedNotes
                .Where(x => x.PersonId == personId && x.NoteId == noteId)
                .FirstOrDefaultAsync();

            if (sharedNote == null)
            {
                return;
            }

            dataContext.SharedNotes.Remove(sharedNote);

            await dataContext.SaveChangesAsync();
        }

        private static IQueryable<ContactNoteDataModel> ApplyTagFilter(IQueryable<ContactNoteDataModel> query, Guid[] tags, string titleFilter = null)
        {
            if (tags != null && tags.Any())
            {
                bool hasTitleFilter = !string.IsNullOrEmpty(titleFilter);
                return query.Where(x => x.Tags.Any(t => tags.Contains(t.TagId) || (hasTitleFilter && EF.Functions.ILike(t.Tag.Title, $"%{titleFilter}%"))));
            }

            return query;
        }

        public IQueryable<ContactNoteDataModel> IncludeFormFields(IQueryable<ContactNoteDataModel> query)
        {
            query = query
                .Include(x => x.FormFields
                    .Where(field => !field.Deleted))
                .ThenInclude(x => x.Responses);

            return query;
        }

        public async Task<NoteDetail[]> GetNotes(DateRange dateRange, Guid[] noteIds, Guid? lastNoteId, int limit)
        {
            IQueryable<ContactNoteDataModel> query = dataContext.ContactNotes;

            if (lastNoteId.HasValue)
            {
                query = query.Where(x => x.Id.CompareTo(lastNoteId.Value) > 0);
            }

            if (dateRange != null)
            {
                query = query.Where(n => n.CreatedDateTimeUtc >= dateRange.FromDate && n.CreatedDateTimeUtc <= dateRange.ToDate);
            }

            if (noteIds?.Any() == true)
            {
                query = query.Where(n => noteIds.Contains(n.Id));
            }

            var models = await query
                .Include(x => x.RolesAccessibleBy)
                .Include(x => x.Files)
                .Include(x => x.CreatedByPerson)
                .Include(x => x.Task)
                .Include(x => x.Tags).ThenInclude(x => x.Tag)
                .OrderBy(x => x.Id)
                .Take(limit)
                .AsSplitQuery()
                .ToListAsync();

            return mapper.Map<NoteDetail[]>(models);
        }

        public async Task<ResourceName> GetResourceName(Guid id)
        {
            var values = await dataContext.ContactNotes
                .AsNoTracking()
                .Select(c => new
                {
                    ProviderId = c.ProviderId,
                    ContactId = c.ContactId,
                    Id = c.Id
                })
                .FirstOrDefaultAsync(c => c.Id == id);

            if (values == null)
                return null;

            return new ResourceNameBuilder()
                .BuildNote(values.ProviderId, values.ContactId, values.Id);
        }

        public async Task<bool> IsActive(Guid providerId, DateRange dateRange)
        {
            if (providerId == Guid.Empty) return false;
            var count = await dataContext.ContactNotes
                .Where(x => x.ProviderId == providerId && x.CreatedDateTimeUtc >= dateRange.FromDate && x.CreatedDateTimeUtc <= dateRange.ToDate)
                .CountAsync();

            return count > 0;
        }

        public async Task<IEnumerable<SharedWith>> GetSharedWith(Guid noteId, DateTime? createdDateTime = null)
        {
            var query = dataContext.SharedNotes
                .Where(x => x.NoteId == noteId);

            if (createdDateTime.HasValue)
            {
                query.Where(x => x.CreatedDateTimeUtc >= createdDateTime.Value);
            }

            var models = await query.ToListAsync();

            return mapper.Map<IEnumerable<SharedWith>>(models);
        }

        public async Task DeleteByContactId(Guid providerId, Guid contactId)
        {
            await dataContext.ContactNotes
                .IgnoreQueryFilters()
                .Where(x => x.ProviderId == providerId && x.ContactId == contactId)
                .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.DeletedAtUtc, DateTime.UtcNow));

        }

        public async Task Restore(Guid providerId, Guid id)
        {
            var model = await dataContext.ContactNotes
                .IgnoreQueryFilters()
                .Where(x => x.ProviderId == providerId && x.Id == id)
                .FirstOrDefaultAsync();

            if (model == null)
                return;

            model.DeletedAtUtc = null;
            await dataContext.SaveChangesAsync();
        }

        public async Task HardDelete(Guid providerId, Guid id)
        {
            var model = await dataContext.ContactNotes
                .IgnoreQueryFilters()
                .Where(x => x.ProviderId == providerId && x.Id == id)
                .FirstOrDefaultAsync();

            if (model == null)
                return;

            foreach (var file in model.Files)
            {
                dataContext.Remove(file);
            }

            dataContext.HardDelete(model);
            await dataContext.SaveChangesAsync();
        }

        public async Task RestoreByContactId(Guid providerId, Guid contactId)
        {
            var trashNoteIds = dataContext.TrashItems.Where(x => x.FromContactId == contactId && x.Type == TrashType.Note)
                .Select(x => x.EntityId).ToArray();

            await dataContext.ContactNotes
                .IgnoreQueryFilters()
                .Where(x => x.ProviderId == providerId && x.ContactId == contactId && x.DeletedAtUtc.HasValue && !trashNoteIds.Contains(x.Id))
                .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.DeletedAtUtc, (DateTime?)null));
        }
        
        

        public async Task<Guid[]> MergeNotes(Guid providerId, Guid destinationContactId, Guid[] sourceContactIds)
        {
            var noteIds = await dataContext
                .ContactNotes
                .IgnoreQueryFilters()
                .Where(x => x.ProviderId == providerId && 
                            sourceContactIds.Contains(x.ContactId) && 
                            x.ContactId != destinationContactId)
                .Select(x => x.Id)
                .ToArrayAsync();

            if (noteIds.Any())
            {
                await dataContext.ContactNotes
                    .Where(x => x.ProviderId == providerId && 
                                sourceContactIds.Contains(x.ContactId) && 
                                x.ContactId != destinationContactId)
                    .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.ContactId, destinationContactId));
            }

            return noteIds;
        }
        
        public async Task<SharedWith[]> MergeSharedNotes(Guid destinationPersonId, Guid[] sourcePersonIds, Guid providerId)
        {
            var sharedWiths = await dataContext
                .SharedNotes
                .Where(x => sourcePersonIds.Contains(x.PersonId) && x.ProviderId == providerId)
                .ToArrayAsync();

            if (sharedWiths.Any())
            {
                // Find entries that would cause duplicates
                var duplicateEntries = await dataContext.SharedNotes
                    .Where(x => x.PersonId == destinationPersonId && sharedWiths.Select(s => s.NoteId).Contains(x.NoteId) && x.ProviderId == providerId)
                    .Select(x => x.NoteId)
                    .ToListAsync();

                // Update only the entries that do not cause duplicates
                await dataContext.SharedNotes
                    .Where(x => sourcePersonIds.Contains(x.PersonId) && !duplicateEntries.Contains(x.NoteId) && x.ProviderId == providerId)
                    .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.PersonId, destinationPersonId));
            }

            return mapper.Map<SharedWith[]>(sharedWiths);
        }

        public async Task<NoteReference[]> GetTaskContactNotes(Guid providerId, Guid taskId, Guid contactId)
        {
            var entities = await dataContext.ContactNotes
                .Where(x => x.ProviderId == providerId &&
                            x.TaskId.HasValue && x.TaskId.Value == taskId &&
                            x.ContactId == contactId)
                .ToArrayAsync();
            
            return mapper.Map<NoteReference[]>(entities);
        }
    }
}