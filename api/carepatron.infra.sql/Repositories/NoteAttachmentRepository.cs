﻿using AutoMapper;
using carepatron.core.Application.Notes.Models;
using carepatron.core.Repositories.Notes;
using carepatron.infra.sql.Models.Attachments;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.infra.sql.Repositories
{
    public class NoteAttachmentRepository : INoteAttachmentRepository
    {
        private readonly DataContext dataContext;
        private readonly IMapper mapper;

        public NoteAttachmentRepository(DataContext dataContext, IMapper mapper)
        {
            this.dataContext = dataContext;
            this.mapper = mapper;
        }

        public async Task<NoteAttachment> CreateNoteAttachment(NoteAttachment noteAttachments)
        {
            var result = await CreateNoteAttachments(new [] { noteAttachments });

            return result.FirstOrDefault();
        }

        public async Task<NoteAttachment[]> CreateNoteAttachments(NoteAttachment[] noteAttachments)
        {
            var models = mapper.Map<ContactNoteAttachmentDataModel[]>(noteAttachments);

            foreach (var model in models)
            {
                var trackedFile = await dataContext.Medias
                    .FirstOrDefaultAsync(m => m.Id == model.FileId);

                if (trackedFile != null)
                {
                    model.File = trackedFile;
                }
            }

            dataContext.AddRange(models);

            await dataContext.SaveChangesAsync();

            return noteAttachments;
        }

        public async Task DeleteNoteAttachment(Guid fileId, Guid noteId)
        {
            var model = await dataContext.ContactNoteAttachments
                .Where(x => x.FileId == fileId && x.NoteId == noteId).FirstOrDefaultAsync();

            if (model is null)
                return;
            
            dataContext.Remove(model);
            
            await dataContext.SaveChangesAsync();
        }

        public async Task DeleteNoteAttachments(Guid noteId, IEnumerable<Guid> fileIds)
        {
            var models = await dataContext.ContactNoteAttachments
                .Where(x => fileIds.Contains(x.FileId) && x.NoteId == noteId).ToListAsync();

            dataContext.RemoveRange(models);

            await dataContext.SaveChangesAsync();
        }

        public async Task<NoteAttachment> GetNoteAttachment(Guid fileId, Guid noteId)
        {
            var model = await dataContext.ContactNoteAttachments
                .Where(x => x.FileId == fileId && x.NoteId == noteId).FirstOrDefaultAsync();

            return mapper.Map<NoteAttachment>(model);
        }

        public async Task<NoteAttachment[]> GetNoteAttachments(Guid noteId)
        {
            var models = await dataContext.ContactNoteAttachments
                .Where(x => x.NoteId == noteId).ToListAsync();

            return mapper.Map<NoteAttachment[]>(models);
        }
    }
}
