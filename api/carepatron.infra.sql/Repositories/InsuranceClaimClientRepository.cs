using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Repositories.Insurance;
using Microsoft.EntityFrameworkCore;

namespace carepatron.infra.sql.Repositories;

public class InsuranceClaimClientRepository(DataContext dataContext) : IInsuranceClaimClientRepository
{
    public async Task<Guid?> GetProviderIdByContact(Guid contactId)
    {
        return await dataContext.InsuranceClaimClients
            .AsNoTracking()
            .Where(x => x.ContactId == contactId)
            .Select(x => x.ProviderId as Guid?)
            .FirstOrDefaultAsync();
    }
}