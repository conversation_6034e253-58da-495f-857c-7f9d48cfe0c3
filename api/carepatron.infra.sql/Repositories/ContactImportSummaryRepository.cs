using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Models.Pagination;
using carepatron.core.Application.Contacts.Abstractions;
using Microsoft.EntityFrameworkCore;
using carepatron.infra.sql.Models.Contact;

namespace carepatron.infra.sql.Repositories
{
    public class ContactImportSummaryRepository : IContactImportSummaryRepository
    {
        private readonly DataContext dataContext;
        private readonly IMapper mapper;

        public ContactImportSummaryRepository(DataContext dataContext, IMapper mapper)
        {
            this.dataContext = dataContext;
            this.mapper = mapper;
        }
        
        public async Task Create(ContactImportSummary contactImportSummary)
        {
            var model = mapper.Map<ContactImportSummaryDataModel>(contactImportSummary);
            dataContext.Add(model);
            await dataContext.SaveChangesAsync();
        }

        public async Task<ContactImportSummary> GetById(Guid id)
        {
            var dataModel = await dataContext.ContactImportSummaries.FirstOrDefaultAsync(i => i.Id == id);
            return mapper.Map<ContactImportSummary>(dataModel);
        }

        public async Task Update(ContactImportSummary contactImportSummary)
        {
            var dataModel = await dataContext.ContactImportSummaries.FirstOrDefaultAsync(i => i.Id == contactImportSummary.Id);
            dataModel.FileId = contactImportSummary.FileId;
            dataModel.FileName = contactImportSummary.FileName;
            dataModel.FileExtension = contactImportSummary.FileExtension;
            dataModel.SchemaFileId = contactImportSummary.SchemaFileId;
            dataModel.Status = contactImportSummary.Status;
            dataModel.LastStatusSeenBy = contactImportSummary.LastStatusSeenBy;
            dataModel.UpdatedDateTime = DateTime.UtcNow;
            dataModel.CompletedDateTimeUtc = contactImportSummary.CompletedDateTimeUtc;

            await dataContext.SaveChangesAsync();
        }

        public async Task<CollectionResult<ContactImportSummary>> Get(Guid providerId,
            DateTime? fromDate,
            DateTime? toDate,
            ImportSummaryStatus[] statuses,
            bool isContact)
        {
            var query = dataContext.ContactImportSummaries.Where(i => i.ProviderId == providerId && i.IsContact == isContact);

            if (fromDate.HasValue)
            {
                query = query.Where(i => i.CreatedDateTimeUtc >= fromDate);
            }

            if (toDate.HasValue)
            {
                query = query.Where(i => i.CreatedDateTimeUtc <= toDate);
            }

            if (statuses != null && statuses.Length > 0)
            {
                query = query.Where(i => statuses.Contains(i.Status));
            }

            var importSummaryModels = await query
                .ToArrayAsync();

            var summaries = mapper.Map<ContactImportSummary[]>(importSummaryModels);

            return new CollectionResult<ContactImportSummary>(summaries);
        }
    }
}
