using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Constants;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Pagination;
using carepatron.core.Repositories.Insurance;
using carepatron.infra.sql.Models.Insurance;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.infra.sql.Repositories;

public class ProviderInsurancePayerRepository(
    DataContext dataContext,
    IMapper mapper
) : IInsurancePayerRepository
{
    public async Task<ProviderInsurancePayer> Create(ProviderInsurancePayer providerInsurancePayer, CancellationToken cancellationToken = default)
    {
        var model = mapper.Map<ProviderInsurancePayerDataModel>(providerInsurancePayer);
        model.CreatedDateTimeUtc = DateTime.UtcNow;
        dataContext.Add(model);

        await dataContext.SaveChangesAsync(cancellationToken);
        return mapper.Map<ProviderInsurancePayer>(model);
    }

    public async Task Delete(Guid providerId, Guid id)
    {
        var model = await dataContext.ProviderInsurancePayers
            .Where(x => x.ProviderId == providerId && x.Id == id)
            .FirstOrDefaultAsync();

        if (model == null) return;

        model.DeletedAtUtc = DateTime.UtcNow;
        await dataContext.SaveChangesAsync();
    }

    public async Task<PaginatedResult<AvailableInsurancePayer>> GetAvailableInsurancePayers(Guid providerId, string searchTerm, string[] states,
        PaginationRequest paginationRequest)
    {
        var query = dataContext.InsurancePayers.Where(x => x.Professional != PayerTransactionAvailability.False
                                                           || x.ERA != PayerTransactionAvailability.False
                                                           || x.Eligibility != PayerTransactionAvailability.False
        );
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(x => (EF.Functions.ILike(x.Name, "%" + searchTerm + "%") || EF.Functions.ILike(x.PayerId, "%" + searchTerm + "%")));
        }

        if (states != null && states.Any())
        {
            query = query.Where(x => (x.States.Any(s => states.Contains(s))));
        }

        var availablePayerQuery = query
            .AsNoTracking()
            .LeftJoin(dataContext.ProviderInsurancePayers.Where(x => x.ProviderId == providerId),
                x => new { x.PayerId, x.ClearingHouse },
                y => new { y.PayerId, y.ClearingHouse },
                (clearingHousePayer, providerPayer) => new { Payer = clearingHousePayer, AddedToWorkspaces = providerPayer != null })
            .Select(x => new AvailableInsurancePayer
            {
                PayerId = x.Payer.PayerId,
                ClearingHouse = x.Payer.ClearingHouse,
                Name = x.Payer.Name,
                PayerType = x.Payer.PayerType,
                States = x.Payer.States,
                PhoneNumber = x.Payer.PhoneNumber,
                Address = x.Payer.Address,
                Eligibility = x.Payer.Eligibility,
                Professional = x.Payer.Professional,
                ERA = x.Payer.ERA,
                Attachment = x.Payer.Attachment,
                AddedToWorkspace = x.AddedToWorkspaces
            });

        var total = availablePayerQuery.Count();
        availablePayerQuery = availablePayerQuery
            .OrderBy(x => x.Name)
            .ThenBy(x => x.PayerId);

        if (paginationRequest?.Offset != null)
        {
            availablePayerQuery = availablePayerQuery.Skip(paginationRequest.Offset);
        }

        if (paginationRequest?.Limit != null)
        {
            availablePayerQuery = availablePayerQuery.Take(paginationRequest.Limit);
        }

        var result = await availablePayerQuery.ToArrayAsync();
        var paginatedResult = PaginatedResult<AvailableInsurancePayer>.FromResult(result, paginationRequest);
        paginatedResult.TotalCount = total;
        return paginatedResult;
    }

    public async Task<InsurancePayer[]> GetClearingHousePayers(Guid providerId, ClearingHousePayer[] payers, CancellationToken cancellationToken = default)
    {
        if (payers == null || payers.Length == 0) return [];

        var predicate = PredicateBuilder.New<InsurancePayerDataModel>(false);
        predicate = payers.Aggregate(predicate, (current, payer) => current.Or(x => x.PayerId == payer.PayerId && x.ClearingHouse == payer.ClearingHouse));

        var query = dataContext.InsurancePayers
            .AsNoTracking()
            .Where(predicate)
            .LeftJoin(dataContext.ProviderInsurancePayers.Where(x => x.ProviderId == providerId),
                x => new { x.PayerId, x.ClearingHouse },
                y => new { y.PayerId, y.ClearingHouse },
                (x, y) => new { x, y })
            .Where(arg => arg.y == null) // where the payer has not been added
            .Select(arg => arg.x)
            .ProjectTo<InsurancePayer>(mapper.ConfigurationProvider);

        var result = await query.ToArrayAsync(cancellationToken);
        return result;
    }

    public async Task<Dictionary<string, ProviderInsurancePayer>> GetById(Guid requestProviderId, ClearingHouseType clearingHouse, string[] payerIds)
    {
        var query = dataContext.ProviderInsurancePayers
            .Where(x => x.ProviderId == requestProviderId && payerIds.Contains(x.PayerId) && x.ClearingHouse == clearingHouse)
            .AsNoTracking()
            .ProjectTo<ProviderInsurancePayer>(mapper.ConfigurationProvider);
        return await query
            .ToDictionaryAsync(x => x.PayerId);
    }


    public async Task<ProviderInsurancePayer[]> ImportPayers(ProviderInsurancePayer[] createdPayers, ProviderInsurancePayer[] updatedPayer, CancellationToken cancellationToken)
    {
        var mappedModels = mapper.Map<ProviderInsurancePayerDataModel[]>(createdPayers);
        await dataContext.ProviderInsurancePayers.AddRangeAsync(mappedModels);
        foreach (var updated in updatedPayer)
        {
            var payer = await dataContext.ProviderInsurancePayers.FindAsync([updated.Id], cancellationToken);
            if (payer != null)
            {
                payer.ClearingHouse = updated.ClearingHouse;
                payer.Name = updated.Name;
                payer.CoverageType = updated.CoverageType;
                payer.OtherCoverageTypeName = updated.OtherCoverageTypeName;
                dataContext.ProviderInsurancePayers.Update(payer);
            }
        }

        await dataContext.SaveChangesAsync(cancellationToken);
        var response = mapper.Map<ProviderInsurancePayer[]>(mappedModels);
        return response;
    }

    public Task UpdateClearingHousePayer(InsurancePayer clearingHousePayer)
    {
        var model = dataContext.InsurancePayers.Find(clearingHousePayer.PayerId);
        model.Name = clearingHousePayer.Name;
        model.PayerType = clearingHousePayer.PayerType;
        model.Professional = clearingHousePayer.Professional;
        model.Attachment = clearingHousePayer.Attachment;
        model.Eligibility = clearingHousePayer.Eligibility;
        model.ERA = clearingHousePayer.ERA;
        model.States = clearingHousePayer.States;
        model.PhoneNumber = clearingHousePayer.PhoneNumber;
        model.Address = clearingHousePayer.Address;
        dataContext.InsurancePayers.Update(model);
        return dataContext.SaveChangesAsync();
    }

    public Task CreateClearingHousePayer(InsurancePayer clearingHousePayer)
    {
        var model = mapper.Map<InsurancePayerDataModel>(clearingHousePayer);
        dataContext.InsurancePayers.Add(model);
        return dataContext.SaveChangesAsync();
    }

    public async Task<PaginatedResult<ProviderInsurancePayer>> Get(Guid providerId, string search, PaginationRequest paginationRequest)
    {
        var query = dataContext.ProviderInsurancePayers
            .Where(x => x.ProviderId == providerId);

        if (!string.IsNullOrWhiteSpace(search))
        {
            var searchQuery = search.ToLower();
            query = query.Where(x => x.Name.ToLower().Contains(searchQuery) || x.PayerId.ToLower().Contains(searchQuery));
        }

        var total = await query.CountAsync();

        query = query
            .OrderBy(x => x.Name)
            .ThenBy(x => x.PayerId)
            .ThenBy(x => x.Id);

        if (paginationRequest?.Offset != null)
        {
            query = query.Skip(paginationRequest.Offset);
        }

        if (paginationRequest?.Limit != null)
        {
            query = query.Take(paginationRequest.Limit);
        }

        var result = await query.ToArrayAsync();

        var mappedResult = mapper.Map<ProviderInsurancePayer[]>(result);
        var paginatedResult = PaginatedResult<ProviderInsurancePayer>.FromResult(mappedResult, paginationRequest);
        paginatedResult.TotalCount = total;
        return paginatedResult;
    }

    public async Task<ProviderInsurancePayer> GetById(Guid providerId, Guid id)
    {
        if (id == Guid.Empty) return null;
        var model = await dataContext.ProviderInsurancePayers
            .Where(x => x.ProviderId == providerId && x.Id == id)
            .FirstOrDefaultAsync();

        return model == null
            ? null
            : mapper.Map<ProviderInsurancePayer>(model);
    }

    public async Task<ProviderInsurancePayer> Update(ProviderInsurancePayer providerInsurancePayer, CancellationToken cancellationToken = default)
    {
        var model = await dataContext.ProviderInsurancePayers
            .Where(x => x.Id == providerInsurancePayer.Id && x.ProviderId == providerInsurancePayer.ProviderId)
            .FirstOrDefaultAsync();

        if (model == null)
        {
            throw new ExecutionException(new ValidationError(Errors.PayerNotFoundCode, Errors.PayerNotFoundDetail, ValidationType.NotFound));
        }

        model.PayerId = providerInsurancePayer.PayerId;
        model.Name = providerInsurancePayer.Name;
        model.CoverageType = providerInsurancePayer.CoverageType;
        model.OtherCoverageTypeName = providerInsurancePayer.OtherCoverageTypeName;
        model.PhoneNumber = providerInsurancePayer.PhoneNumber;
        model.Address = providerInsurancePayer.Address;

        await dataContext.SaveChangesAsync(cancellationToken);
        return mapper.Map<ProviderInsurancePayer>(model);
    }

    public async Task<InsurancePayer> GetClearingHousePayer(ClearingHouseType clearingHouse, string payerId, CancellationToken cancellationToken = default)
    {
        var query = await dataContext.InsurancePayers
            .SingleOrDefaultAsync(x => x.PayerId == payerId && x.ClearingHouse == clearingHouse, cancellationToken: cancellationToken);

        return mapper.Map<InsurancePayer>(query);
    }

    public async Task<InsurancePayer> GetClearingHousePayerByProviderPayerId(Guid providerId, Guid providerPayerId, CancellationToken cancellationToken = default)
    {
        var result = await dataContext.InsurancePayers
            .AsNoTracking()
            .Join(dataContext.ProviderInsurancePayers,
                x => new { x.PayerId, x.ClearingHouse },
                y => new { y.PayerId, y.ClearingHouse },
                (InsurancePayer, ProviderInsurancePayer) => new { InsurancePayer, ProviderInsurancePayer }
            )
            .Where(x => x.ProviderInsurancePayer.ProviderId == providerId && x.ProviderInsurancePayer.Id == providerPayerId)
            .Select(s => s.InsurancePayer)
            .ProjectTo<InsurancePayer>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(cancellationToken);
        return result;
    }

    public async Task<ProviderInsurancePayer> GetByPayerNumber(Guid providerId, string payerNumber, ClearingHouseType clearingHouse, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(payerNumber)) throw new ArgumentNullException(nameof(payerNumber));
        var model = await dataContext.ProviderInsurancePayers
            .Join(dataContext.InsurancePayers,
                x => new { x.PayerId, x.ClearingHouse },
                y => new { y.PayerId, y.ClearingHouse },
                (ProviderInsurancePayer, InsurancePayer) => new { ProviderInsurancePayer, InsurancePayer }
            )
            .AsNoTracking()
            .Where(x => x.ProviderInsurancePayer.ProviderId == providerId && x.InsurancePayer.PayerId == payerNumber)
            .Select(x => x.ProviderInsurancePayer)
            .ProjectTo<ProviderInsurancePayer>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(cancellationToken);
        return model;
    }
}