using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Communications.Inbox.Abstractions;
using carepatron.core.Application.Communications.Inbox.Builders;
using carepatron.core.Application.Communications.Inbox.Extensions;
using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Application.Communications.Inbox.Models.Settings;
using carepatron.core.Application.Communications.Inbox.Utilities;
using carepatron.core.Application.Staff.Models;
using carepatron.core.Common;
using carepatron.core.Extensions;
using carepatron.core.Models.Pagination;
using carepatron.core.Paging;
using carepatron.core.Paging.Models;
using carepatron.infra.sql.Extensions;
using carepatron.infra.sql.Models.Contact;
using carepatron.infra.sql.Models.Inbox;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.infra.sql.Repositories
{
    public class InboxRepository(DataContext dataContext,
        IMapper mapper,
        ITokenisedPaginator tokenisedPaginator,
        IDateTimeProvider dateTimeProvider) : IInboxRepository
    {
        private readonly DataContext dataContext = dataContext;
        private readonly IMapper mapper = mapper;
        private readonly ITokenisedPaginator tokenisedPaginator = tokenisedPaginator;
        private readonly IDateTimeProvider dateTimeProvider = dateTimeProvider;

        public async Task<InboxInfo> Get(Guid providerId, Guid personId)
        {
            var model = await dataContext.Inboxes
                .Include(x => x.InboxAccounts)
                .FirstOrDefaultAsync(x => x.PersonId == personId && x.ProviderId == providerId);

            return mapper.Map<InboxInfo>(model);
        }

        public async Task<InboxInfo> Get(Guid id)
        {
            var model = await dataContext.Inboxes.FindAsync(id);

            return mapper.Map<InboxInfo>(model);
        }

        public async Task<InboxInfo> SaveInbox(InboxInfo inboxInfo)
        {
            var dataModel = await dataContext.Inboxes.FindAsync(inboxInfo.Id);

            if (dataModel != null)
            {
                dataModel.HasConnectedAccount = inboxInfo.HasConnectedAccount;
                dataContext.Update(dataModel);
            }
            else
            {
                dataModel = mapper.Map<InboxDataModel>(inboxInfo);
                dataContext.Add(dataModel);
            }

            await dataContext.SaveChangesAsync();

            return mapper.Map<InboxInfo>(dataModel);
        }

        public async Task CreateInbox(core.Application.Communications.Inbox.Models.InboxSetting inboxSetting)
        {
            var dataModel = mapper.Map<InboxDataModel>(inboxSetting);

            foreach (var inboxStaffDataModel in dataModel.InboxStaffs)
            {
                inboxStaffDataModel.Person = (inboxStaffDataModel.PersonId != null)
                    ? await dataContext.Persons.FindAsync(inboxStaffDataModel.PersonId) // Load person entity here to do this in one go
                    : null;
            }

            dataContext.Add(dataModel);

            await dataContext.SaveChangesAsync();
        }

        public async Task<InboxStaff[]> GetInboxStaffsByRole(Guid inboxId, InboxStaffRoleType inboxStaffRoleType)
        {
            return await dataContext.InboxStaffRoles
                .Where(x => x.InboxId == inboxId)
                .Where(x => x.RoleType == inboxStaffRoleType)
                .ProjectTo<InboxStaff>(mapper.ConfigurationProvider)
                .ToArrayAsync();
        }

        public async Task AddInboxStaffs(Guid inboxId, InboxStaff[] inboxStaffs)
        {
            var inboxDataModel = await dataContext.Inboxes.FindAsync(inboxId);

            if (inboxDataModel is null) return;

            var dataModels = mapper.Map<InboxStaffRoleDataModel[]>(inboxStaffs);

            foreach (var dataModel in dataModels)
            {
                dataModel.Inbox = inboxDataModel;
                dataModel.InboxId = inboxId;
            }

            dataContext.AddRange(dataModels);

            await dataContext.SaveChangesAsync();
        }

        public async Task DeleteInboxStaffs(Guid[] inboxStaffIds)
        {
            var dataModels = await dataContext.InboxStaffRoles
                .Where(x => inboxStaffIds.Contains(x.Id))
                .ToArrayAsync();

            dataContext.RemoveRange(dataModels);

            await dataContext.SaveChangesAsync();
        }

        public async Task DeleteInbox(Guid id)
        {
            var dataModel = await dataContext.Inboxes.FindAsync(id);

            if (dataModel is null) return;

            var trackedSenders = await dataContext.InboxTrackedSenders
                .Where(t => t.ProviderId == dataModel.ProviderId && t.InboxId.HasValue && t.InboxId.Value == dataModel.Id)
                .ToListAsync();

            if (trackedSenders?.Any() == true)
            {
                dataContext.RemoveRange(trackedSenders);
            }

            dataContext.Remove(dataModel);

            await dataContext.SaveChangesAsync();
        }
        public async Task<InboxAccount> AddInboxAccount(InboxAccount inboxAccount)
        {
            var dataModel = mapper.Map<InboxAccountDataModel>(inboxAccount);

            dataContext.Add(dataModel);

            await dataContext.SaveChangesAsync();

            return inboxAccount;
        }

        public async Task<InboxAccount> UpdateInboxAccountLastSync(Guid inboxAccountId, DateTime lastSync, InboxAccountSettings inboxAccountSettings = null)
        {
            var dataModel = await dataContext.InboxAccounts.FindAsync(inboxAccountId);
            if (dataModel == null) return null;

            dataModel.LastSync = lastSync;

            if (inboxAccountSettings is not null) dataModel.Settings = inboxAccountSettings;

            dataContext.Update(dataModel);

            await dataContext.SaveChangesAsync();

            return mapper.Map<InboxAccount>(dataModel);
        }

        public async Task<InboxAccount> UpdateInboxAccountSettings(Guid inboxAccountId, InboxAccountSettings settings)
        {
            var dataModel = await dataContext.InboxAccounts.FindAsync(inboxAccountId);
            if (dataModel == null) return null;

            if (settings is not null) dataModel.Settings = settings;

            dataContext.Update(dataModel);

            await dataContext.SaveChangesAsync();

            return mapper.Map<InboxAccount>(dataModel);
        }

        public async Task<InboxAccount> GetInboxAccount(Guid inboxId, string accountId, ExternalSource externalSource)
        {
            var dataModel = await dataContext.InboxAccounts
                .FirstOrDefaultAsync(x => x.ExternalAccountId == accountId && x.InboxId == inboxId && x.ExternalSource == externalSource);

            return mapper.Map<InboxAccount>(dataModel);
        }

        public async Task DeleteInboxAccount(Guid inboxAccountId)
        {
            var dataModel = await dataContext.InboxAccounts.FindAsync(inboxAccountId);

            if (dataModel is null) return;

            dataContext.Remove(dataModel);

            await dataContext.SaveChangesAsync();
        }

        public async Task DeleteInboxAccounts(Guid[] inboxAccountIds)
        {
            var inboxAccounts = await dataContext.InboxAccounts.Where(iba => inboxAccountIds.Contains(iba.Id)).ToArrayAsync();

            if (inboxAccounts is null) return;

            dataContext.RemoveRange(inboxAccounts);

            await dataContext.SaveChangesAsync();
        }

        public async Task<PaginatedResult<InboxAccount>> GetInboxAccountsWithWebHookSync(PaginationRequest pagination, ExternalSource[] externalSources)
        {
            var query = dataContext.InboxAccounts
                .Where(x => x.Settings != null);

            if (externalSources != null && externalSources.Any())
            {
                query = query.Where(x => externalSources.Contains(x.ExternalSource));
            }

            var dataModels = await query.Skip(pagination.Offset).Take(pagination.Limit + 1).ToListAsync();

            var inboxAccounts = mapper.Map<InboxAccount[]>(dataModels.Take(pagination.Limit));

            return new PaginatedResult<InboxAccount>(inboxAccounts, new Pagination(inboxAccounts.Length, pagination.Offset, dataModels.Count > inboxAccounts.Length));
        }

        public async Task<InboxInfo[]> GetInboxInfosByAccountId(string accountId, ExternalSource externalSource)
        {
            return await dataContext.Inboxes
                .Include(x => x.InboxAccounts)
                .Where(x => x.InboxAccounts.Any(y => y.ExternalAccountId == accountId && y.ExternalSource == externalSource))
                .ProjectTo<InboxInfo>(mapper.ConfigurationProvider)
                .ToArrayAsync();
        }

        public async Task<InboxAccount> GetInboxAccountById(Guid inboxAccountId)
        {
            var dataModel = await dataContext.InboxAccounts
                .Include(x => x.Inbox)
                .FirstOrDefaultAsync(x => x.Id == inboxAccountId);

            return mapper.Map<InboxAccount>(dataModel);
        }

        public async Task<InboxAccount[]> GetInboxAccountsByProviderId(Guid providerId)
            => await dataContext.InboxAccounts
                .Where(x => x.Inbox.ProviderId == providerId)
                .ProjectTo<InboxAccount>(mapper.ConfigurationProvider)
                .ToArrayAsync();

        public async Task<Dictionary<Guid, int>> GetAllUnreadConversationsCount(Guid[] inboxIds)
        {
            // Filter inbox messages by unread status and group by conversation id
            return await dataContext.InboxMessages
                .FilterByInboxes(inboxIds)
                .FilterByStatus(MessageStatus.Default)
                .FilterUnread()
                .Where(x => x.ConversationId.HasValue)
                .GroupBy(x => x.InboxId)
                .Select(group => new
                {
                    InboxId = group.Key,
                    UnreadCount = group.Select(x => x.ConversationId).Distinct().Count()
                })
                .ToDictionaryAsync(x => x.InboxId.GetValueOrDefault(), x => x.UnreadCount);
        }

        public async Task<ConversationResponse[]> GetConversation(Guid providerId,
            Guid inboxId,
            Guid conversationId,
            MessageStatus status)
        {
            // Filter the messages
            var filteredMessagesQuery = dataContext.InboxMessages
                .FilterByInboxes(inboxId)
                .FilterByConversationIds(conversationId)
                .FilterByStatus(status);

            var conversationSummaries = await filteredMessagesQuery
                .GroupByConversationId()
                .GetConversationSummaryByConversation();

            // TODO: May need to apply sorting in here in the future
            var latestInboxMessage = await filteredMessagesQuery
                .OrderByDescending(x => x.CreatedAt)
                .FirstOrDefaultAsync();

            if (latestInboxMessage is null) return [];

            // Get staff information
            var providerStaffMembers = await GetProviderStaffMembers(providerId);

            // Map to conversation response
            var conversation = mapper.Map<ConversationResponse>(latestInboxMessage);

            // Get inbox contact links
            var inboxContacts = await GetInboxContactsQuery(providerId)
                .FilterByAccountIds(new[] { conversation }.ExtractParticipantAccountIds())
                .ProjectTo<InboxContact>(mapper.ConfigurationProvider)
                .ToArrayAsync();

            // Build response
            var conversationResponse = new ConversationResponseBuilder([conversation], conversationSummaries, inboxContacts, providerStaffMembers).Build();

            return [.. conversationResponse];
        }

        public async Task<InboxMessage> GetMessage(Guid inboxId, Guid inboxMessageId)
        {
            var message = dataContext.InboxMessages.FirstOrDefault(x => x.InboxId == inboxId && x.Id == inboxMessageId);
            return mapper.Map<InboxMessage>(message);
        }

        public async Task<MessageResponse> GetMessageResponse(Guid inboxId, Guid inboxMessageId)
        {
            var message = await dataContext.InboxMessages.FirstOrDefaultAsync(x => x.InboxId == inboxId && x.Id == inboxMessageId);
            return mapper.Map<MessageResponse>(message);
        }

        public async Task<InboxMessage[]> GetMessages(Guid inboxId, Guid conversationId, MessageStatus status)
        {
            return await dataContext.InboxMessages
                .FilterByInboxes(inboxId)
                .FilterByConversationIds(conversationId)
                .FilterByStatus(status)
                .ProjectTo<InboxMessage>(mapper.ConfigurationProvider)
                .ToArrayAsync();
        }

        public async Task<TokenisedPaginatedResult<MessageResponse>> GetMessagesByConversationId(Guid providerId, Guid[] inboxIds, MessageStatus status, Guid conversationId, TokenisedPaginationRequest pagination)
        {
            var inboxContactsQuery = GetInboxContactsQuery(providerId);

            var query = dataContext.InboxMessages
                .Include(x => x.InboxMessageSeenBys)
                .Include(x => x.InboxMessageAttachments)
                .Include(x => x.InboxConversation.InboxConversationParticipants)
                .Include(x => x.InboxMessageDrafts)
                    .ThenInclude(y => y.InboxMessageAttachments)
                .Include(x => x.LastSendAttemptByPerson)
                .FilterByInboxes(inboxIds)
                .FilterMessageByStatus(status)
                .FilterByConversationIds(conversationId);

            return await GetMessages(providerId, query, inboxContactsQuery, pagination);
        }

        public async Task<TokenisedPaginatedResult<MessageResponse>> GetMessagesByContactConversation(Guid providerId, Guid contactId, MessageStatus status, Guid conversationId, TokenisedPaginationRequest pagination)
        {
            var inboxContactsQuery = GetInboxContactsQuery(providerId);

            var query = dataContext.InboxMessages
                .Include(x => x.InboxMessageSeenBys)
                .Include(x => x.InboxMessageAttachments)
                .Include(x => x.InboxConversation.InboxConversationParticipants)
                .Include(x => x.InboxMessageDrafts)
                    .ThenInclude(y => y.InboxMessageAttachments)
                .Include(x => x.LastSendAttemptByPerson)
                .FilterByProviderInboxes(providerId)
                .FilterMessageByStatus(status)
                .FilterByContact(inboxContactsQuery, contactId)
                .FilterByConversationIds(conversationId);

            return await GetMessages(providerId, query, inboxContactsQuery, pagination);
        }

        public async Task<Dictionary<Guid, bool>> HasInboxMessages(params Guid[] inboxIds)
        {
            return inboxIds.IsNullOrEmpty()
                ? throw new ArgumentNullException(nameof(inboxIds))
                : await dataContext.Inboxes
                .Where(x => inboxIds.Contains(x.Id))
                .Select(x => new { x.Id, HasMessages = x.InboxMessages.Any() })
                .ToDictionaryAsync(key => key.Id, val => val.HasMessages);
        }

        public async Task<InboxMessage[]> AddInboxMessages(InboxMessage[] inboxMessages, Guid personId, DateTime lastSync)
        {
            inboxMessages = await RemoveExistingMessagesByAccountAndExternalMessageId(inboxMessages);

            if (inboxMessages.Length <= 0) return Array.Empty<InboxMessage>();

            inboxMessages = await AssignInboxMessageConversations(inboxMessages);

            var dataModels = mapper.Map<InboxMessageDataModel[]>(inboxMessages);

            dataContext.AddRange(dataModels);

            // Sent messages are marked as Read during sync in the Mapper
            // Check if any of the messages are read and add seen by
            if (inboxMessages.Any(x => x.IsRead))
            {
                AddInboxMessageSeenBys(inboxMessages.Where(x => x.IsRead).Select(x => x.Id).ToArray(), personId, lastSync);
            }

            await dataContext.SaveChangesAsync();

            return inboxMessages;
        }

        public async Task<MessageUpdateResponse[]> UpdateInboxMessageStatusByConversation(Guid[] inboxIds,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus status,
            MessageStatus[] statusFilter,
            ConversationFiltering filterBy)
        {
            var date = dateTimeProvider.GetDateTimeUtc();

            var query = dataContext.InboxMessages
                .FilterByInboxes(inboxIds)
                .FilterByConversationIds(selectedConversationIds)
                .ExcludeByConversationIds(excludedConversationIds)
                .FilterConversationByStatuses(statusFilter)
                .FilterConversationByReadStatus(filterBy);

            var latestConversationMessages = await dataContext.InboxConversations.GetLatestConversationMessage(query);

            var result = await GetUpdatedMessages(latestConversationMessages);

            await dataContext.InboxMessages
                .FilterConversationByStatuses(statusFilter)
                .Where(x => result.Select(x => x.ConversationId).Contains(x.ConversationId.Value))
                .ExecuteUpdateAsync(x => x
                    .SetProperty(y => y.Status, status)
                    .SetProperty(y => y.StatusChangedAt, date));

            return result;
        }

        public async Task UpdateInboxMessageStatusByConversation(Guid inboxId, Guid[] conversationIds, MessageStatus destinationStatus, MessageStatus[] sourceStatuses)
        {
            var date = dateTimeProvider.GetDateTimeUtc();

            await dataContext.InboxMessages
                .FilterByInboxes(inboxId)
                .FilterByConversationIds(conversationIds)
                .FilterConversationByStatuses(sourceStatuses) // Only update if status is equal to statusFilter
                .ExecuteUpdateAsync(x => x.SetProperty(y => y.Status, destinationStatus).SetProperty(y => y.StatusChangedAt, date));
        }

        public async Task<InboxMessage[]> DeleteInboxDraftMessagesByInboxAccountId(string accountId)
        {
            var messages = await dataContext.InboxMessages
                                .Where(m => m.Status == MessageStatus.Draft && m.FromAccountId == accountId)
                                .ToArrayAsync();

            if (messages != null && messages.Any()) 
            {
                dataContext.InboxMessages.RemoveRange(messages);

                await dataContext.SaveChangesAsync();
                
                return mapper.Map<InboxMessage[]>(messages);
            } 

            return [];
        }

        public async Task<InboxMessage[]> DeleteInboxMessagesByConversation(Guid[] inboxIds,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            ConversationFiltering filterBy)
        {
            var query = dataContext.InboxMessages
                .FilterByInboxes(inboxIds)
                .FilterByConversationIds(selectedConversationIds)
                .ExcludeByConversationIds(excludedConversationIds)
                .FilterConversationByStatus(MessageStatus.Deleted)
                .FilterConversationByReadStatus(filterBy);

            var latestConversationMessages = await dataContext.InboxConversations.GetLatestConversationMessage(query);

            var messages = await GetUpdatedMessages(latestConversationMessages);

            var deleteQuery = dataContext.InboxMessages
                .FilterMessageByStatus(MessageStatus.Deleted)
                .Where(x => messages.Select(x => x.ConversationId).Contains(x.ConversationId.Value));

            var inboxMessageDataModels = await deleteQuery
                .Include(x => x.InboxMessageDrafts) // Include draft of messages to process purge of draft data
                    .ThenInclude(y => y.InboxMessageAttachments) // Include draft attachments
                .ToArrayAsync();

            if (inboxMessageDataModels.Length <= 0) return [];

            // Map to inbox messages first to preserve the conversation id once the conversation is deleted
            var result = mapper.Map<InboxMessage[]>(inboxMessageDataModels);

            var inboxMessageDraftDataModels = inboxMessageDataModels
                .Where(x => x.InboxMessageDrafts.Count > 0)
                .SelectMany(x => x.InboxMessageDrafts).ToArray();

            if (inboxMessageDraftDataModels.Length > 0)
            {
                // Explicitly remove drafts of messages
                dataContext.RemoveRange(inboxMessageDraftDataModels);
            }

            var conversationIds = inboxMessageDataModels
                .Where(x => x.ConversationId != null)
                .Select(x => x.ConversationId.Value)
                .ToArray();

            await TryDeleteInboxConversations(conversationIds);

            dataContext.RemoveRange(inboxMessageDataModels);

            await dataContext.SaveChangesAsync();

            return result;
        }

        public async Task<MessageUpdateResponse[]> UpdateInboxMessageSetReadByConversation(Guid[] inboxIds,
            Guid personId,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus statusFilter)
        {
            var query = dataContext.InboxMessages
                .FilterByInboxes(inboxIds);

            return await UpdateInboxMessageSetRead(query, personId, selectedConversationIds, excludedConversationIds, statusFilter);
        }

        public async Task<MessageUpdateResponse[]> UpdateInboxMessageSetReadByContactConversation(Guid providerId,
            Guid contactId,
            Guid personId,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus statusFilter)
        {
            var query = dataContext.InboxMessages
                .FilterByProviderInboxes(providerId)
                .FilterByContact(GetInboxContactsQuery(providerId), contactId);

            return await UpdateInboxMessageSetRead(query, personId, selectedConversationIds, excludedConversationIds, statusFilter);
        }

        public async Task<MessageUpdateResponse[]> UpdateInboxMessageSetUnreadByConversation(Guid[] inboxIds,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus statusFilter)
        {
            var query = dataContext.InboxMessages
                .FilterByInboxes(inboxIds);

            return await UpdateInboxMessageSetUnread(query, selectedConversationIds, excludedConversationIds, statusFilter);
        }

        public async Task<MessageUpdateResponse[]> UpdateInboxMessageSetUnreadByContactConversation(Guid providerId,
            Guid contactId,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus statusFilter)
        {
            var query = dataContext.InboxMessages
                .FilterByProviderInboxes(providerId)
                .FilterByContact(GetInboxContactsQuery(providerId), contactId);

            return await UpdateInboxMessageSetUnread(query, selectedConversationIds, excludedConversationIds, statusFilter);
        }

        public async Task<InboxMessageAttachment[]> AddInboxMessageAttachments(InboxMessageAttachment[] messageAttachments)
        {
            var dataModels = mapper.Map<InboxMessageAttachmentDataModel[]>(messageAttachments);

            dataContext.AddRange(dataModels);

            await dataContext.SaveChangesAsync();

            return messageAttachments;
        }

        public async Task<MessageAttachment> AddInboxMessageAttachment(InboxMessageAttachment messageAttachment)
        {
            var dataModel = mapper.Map<InboxMessageAttachmentDataModel>(messageAttachment);

            dataContext.Add(dataModel);

            await dataContext.SaveChangesAsync();

            return mapper.Map<MessageAttachment>(dataModel);
        }

        public async Task<MessageAttachmentStream[]> GetMessageAttachmentStreamByMessage(Guid inboxMessageId)
        {
            var dataModel = await dataContext.InboxMessageAttachments
                .Include(x => x.Media)
                .Where(x => x.InboxMessageId == inboxMessageId)
                .ToArrayAsync();

            return mapper.Map<MessageAttachmentStream[]>(dataModel);
        }

        public async Task<MessageAttachmentStream> GetMessageAttachmentStream(Guid attachmentId)
        {
            var dataModel = await dataContext.InboxMessageAttachments
                .Include(x => x.Media)
                .Where(x => x.Id == attachmentId)
                .SingleOrDefaultAsync();

            return mapper.Map<MessageAttachmentStream>(dataModel);
        }

        public async Task<InboxMessageAttachment[]> GetInboxMessageAttachments(Guid inboxMessageId)
        {
            var dataModel = await dataContext.InboxMessageAttachments
                .Include(x => x.Media)
                .Where(x => x.InboxMessageId == inboxMessageId)
                .ToArrayAsync();

            return mapper.Map<InboxMessageAttachment[]>(dataModel);
        }

        public async Task<InboxMessageAttachment> GetInboxMessageAttachment(Guid inboxMessageAttachmentId)
        {
            var dataModel = await dataContext.InboxMessageAttachments
                .Include(x => x.Media)
                .SingleOrDefaultAsync(x => x.Id == inboxMessageAttachmentId);

            return mapper.Map<InboxMessageAttachment>(dataModel);
        }

        public async Task DeleteInboxMessageAttachments(params Guid[] inboxMessageAttachmentIds)
        {
            var dataModels = await dataContext.InboxMessageAttachments
                .Where(x => inboxMessageAttachmentIds.Contains(x.Id))
                .ToArrayAsync();

            if (dataModels.IsNullOrEmpty()) return;

            dataContext.RemoveRange(dataModels);

            await dataContext.SaveChangesAsync();
        }

        public async Task<Guid> AddInboxMessage(InboxMessage message)
        {
            var dataModel = mapper.Map<InboxMessageDataModel>(message);

            dataContext.Add(dataModel);

            await dataContext.SaveChangesAsync();

            return dataModel.Id;
        }

        public async Task AddInboxMessageSeenBy(Guid inboxMessageId,
            Guid seenByPersonId,
            DateTime seenAt)
        {
            var dataModel = new InboxMessageSeenByDataModel()
            {
                Id = Guid.NewGuid(),
                InboxMessageId = inboxMessageId,
                PersonId = seenByPersonId,
                Timestamp = seenAt
            };

            dataContext.Add(dataModel);

            await dataContext.SaveChangesAsync();
        }

        public async Task<InboxMessage> GetInboxMessage(Guid inboxMessageId)
        {
            var dataModel = await dataContext.InboxMessages.FindAsync(inboxMessageId);

            return mapper.Map<InboxMessage>(dataModel);
        }

        public async Task UpdateInboxMessageDraft(InboxMessage draftMessage)
        {
            var dataModel = await dataContext.InboxMessages.FindAsync(draftMessage.Id);

            if (dataModel is null) return;

            var date = dateTimeProvider.GetDateTimeUtc();

            dataModel.ExternalSource = draftMessage.ExternalSource;
            dataModel.MessageBody = draftMessage.MessageBody;
            dataModel.MessageBodyPlainText = draftMessage.MessageBodyPlainText;
            dataModel.MessageType = draftMessage.MessageType;
            dataModel.MessageSubject = draftMessage.MessageSubject;
            dataModel.MessagePreview = draftMessage.MessagePreview;
            dataModel.FromAccountId = draftMessage.From.AccountId;
            dataModel.FromDisplayName = draftMessage.From.DisplayName;
            dataModel.ToAccountId = draftMessage.To.AccountId;
            dataModel.ToDisplayName = draftMessage.To.DisplayName;
            dataModel.Recipients = draftMessage.Recipients;
            dataModel.StatusChangedAt = date;
            dataModel.Status = draftMessage.Status;

            // move draft to another inbox
            dataModel.InboxId = draftMessage.InboxId;
            dataModel.DraftForInboxMessageId = draftMessage.DraftForInboxMessageId;
            dataModel.ConversationId = draftMessage.ConversationId;
            dataModel.ExternalConversationId = draftMessage.ExternalConversationId;

            // update last send columns
            dataModel.LastSendAttemptByPersonId = draftMessage.LastSendAttemptByPersonId;
            dataModel.LastSendStatusAt = draftMessage.LastSendStatusAt;
            dataModel.LastSendStatus = draftMessage.LastSendStatus;
            dataModel.LastSendAttemptError = draftMessage.LastSendAttemptError;
            dataModel.SendAttemptCount = draftMessage.SendAttemptCount;
            dataModel.InboxMessageBulkTransactionId = draftMessage.InboxMessageBulkTransactionId;

            dataModel.ScheduledAt = draftMessage.ScheduledAt;

            dataContext.Update(dataModel);

            await dataContext.SaveChangesAsync();
        }

        public async Task<MessageResponse> UpdateSentInboxMessageDraft(Guid providerId, Guid inboxMessageDraftId, InboxMessage updatedSentDraftMessage, Guid personId, DateTime sentOn)
        {
            var dataModel = await dataContext.InboxMessages.FindAsync(inboxMessageDraftId);

            if (dataModel is null) return null;

            // Update the draft message with the sent message details from external app
            dataModel.ExternalSourceMessageId = updatedSentDraftMessage.ExternalSourceMessageId;
            dataModel.DownloadTimestamp = sentOn;
            dataModel.MessageBody = updatedSentDraftMessage.MessageBody;
            dataModel.MessageType = updatedSentDraftMessage.MessageType;
            dataModel.MessageSubject = updatedSentDraftMessage.MessageSubject;
            dataModel.MessagePreview = updatedSentDraftMessage.MessagePreview;
            dataModel.MessageBodyPlainText = updatedSentDraftMessage.MessageBodyPlainText;
            dataModel.FromAccountId = updatedSentDraftMessage.From.AccountId;
            dataModel.ToAccountId = updatedSentDraftMessage.To.AccountId;
            dataModel.Recipients = updatedSentDraftMessage.Recipients;
            dataModel.ConversationId = updatedSentDraftMessage.ConversationId;
            dataModel.ExternalConversationId = updatedSentDraftMessage.ExternalConversationId;
            dataModel.Status = updatedSentDraftMessage.Status;
            dataModel.StatusChangedAt = sentOn;
            dataModel.DraftForInboxMessageId = updatedSentDraftMessage.DraftForInboxMessageId;
            dataModel.EmailRfcMessageId = updatedSentDraftMessage.EmailRfcMessageId; // Set rfc message id

            // update last send columns
            dataModel.LastSendAttemptByPersonId = updatedSentDraftMessage.LastSendAttemptByPersonId;
            dataModel.LastSendStatusAt = updatedSentDraftMessage.LastSendStatusAt;
            dataModel.LastSendStatus = updatedSentDraftMessage.LastSendStatus;
            dataModel.LastSendAttemptError = updatedSentDraftMessage.LastSendAttemptError;
            dataModel.SendAttemptCount = updatedSentDraftMessage.SendAttemptCount;

            dataModel.ScheduledAt = updatedSentDraftMessage.ScheduledAt;

            dataContext.Update(dataModel);

            if (!dataModel.InboxMessageSeenBys.Any(x => x.PersonId == personId))
            {
                // Use existing logic of assigning seen bys to messages
                AddInboxMessageSeenBys([inboxMessageDraftId], personId, sentOn);
            }

            await dataContext.SaveChangesAsync();

            // Eager-load the person entity to get the seen by details
            var person = await dataContext.Persons.FindAsync(personId);

            var messageResponse = mapper.Map<MessageResponse>(dataModel);

            var providerStaffMembers = await GetProviderStaffMembers(providerId);

            // Query linking table to get contact details of the recipients
            var inboxContacts = await GetInboxContactsQuery(providerId)
                .FilterByAccountIds(new[] { messageResponse}.ExtractParticipantAccountIds())
                .ProjectTo<InboxContact>(mapper.ConfigurationProvider)
                .ToArrayAsync();

            // Set recipient contact details to the message response
            messageResponse = new MessageResponseBuilder([messageResponse], inboxContacts, providerStaffMembers).Build().First();

            return messageResponse;
        }

        public async Task DeleteInboxMessage(Guid inboxMessageId)
        {
            var dataModel = await dataContext.InboxMessages.FindAsync(inboxMessageId);

            if (dataModel is null) return;

            dataContext.Remove(dataModel);

            await dataContext.SaveChangesAsync();
        }

        public async Task DeleteInboxMessages(Guid[] inboxMessageIds)
        {
            await dataContext.InboxMessages.Where(x => inboxMessageIds.Contains(x.Id))
                .ExecuteDeleteAsync();
        }

        public async Task<InboxInfo[]> GetMultipleInboxes(PersonId personId)
        {
            var inboxInfos = new List<InboxInfo>();

            var inboxesQ = dataContext.Inboxes
                .FilterInboxesOf(personId)
                .Select(i => new { Inbox = i, i.InboxStaffs, i.InboxReplyFormat, DraftsCount = i.InboxMessages.Count(m => m.Status == MessageStatus.Draft) });

            var inboxes = await inboxesQ.ToArrayAsync();

            foreach (var inbox in inboxes)
            {
                var inboxInfo = mapper.Map<InboxInfo>(inbox.Inbox);

                var staff = inbox.InboxStaffs.FirstOrDefault(i => i.PersonId == personId.Id) ?? inbox.InboxStaffs.First(ibs => !ibs.PersonId.HasValue);

                inboxInfo.RoleType = (int)staff.RoleType;

                inboxInfo.DraftsCount = inbox.DraftsCount;

                inboxInfo.ReplyFormat = inbox.InboxReplyFormat != null ? mapper.Map<InboxReplyFormat>(inbox.InboxReplyFormat) : default;

                inboxInfos.Add(inboxInfo);
            }

            return inboxInfos.ToArray();
        }

        public async Task<Guid[]> GetMultipleInboxIdsOf(PersonId personId)
        {
            return await dataContext.Inboxes
                .FilterInboxesOf(personId)
                .Select(i => i.Id).ToArrayAsync();
        }

        public async Task<Dictionary<string, Guid>> GetInboxConversationsByEmailRfcMessageIds(Guid inboxId, params string[] emailRfcMessageIds)
        {
            return await dataContext.InboxMessages
                .FilterByInboxes(inboxId)
                .Where(x => emailRfcMessageIds.Contains(x.EmailRfcMessageId))
                .GroupBy(x => x.EmailRfcMessageId)
                .ToDictionaryAsync(key => key.Key, val => val.First().ConversationId.Value);
        }

        public async Task<Dictionary<string, Guid>> GetInboxConversationsByExternalConversationIds(Guid inboxId, params string[] externalConversationIds)
        {
            return await dataContext.InboxConversationThreads
                .Include(x => x.InboxConversation)
                .Where(x => x.InboxConversation.InboxId == inboxId)
                .Where(x => externalConversationIds.Contains(x.ExternalConversationId))
                .GroupBy(x => x.ExternalConversationId)
                .ToDictionaryAsync(key => key.Key, val => val.First().ConversationId);
        }

        // TODO: Move the consumer of this call to a common service
        private async Task AddInboxConversationParticipants(InboxConversationParticipant[] inboxConversationParticipants)
        {
            var inboxConversationParticipantsDataModels = mapper.Map<InboxConversationParticipantDataModel[]>(inboxConversationParticipants);

            dataContext.AddRange(inboxConversationParticipantsDataModels);

            await dataContext.SaveChangesAsync();
        }

        // TODO: Move the consumer of this call to a common service
        private async Task<InboxConversation[]> AddInboxConversations(InboxConversation[] inboxConversation)
        {
            var inboxConversationDataModels = mapper.Map<InboxConversationDataModel[]>(inboxConversation);

            dataContext.AddRange(inboxConversationDataModels);

            await dataContext.SaveChangesAsync();

            return mapper.Map<InboxConversation[]>(inboxConversationDataModels);
        }

        public async Task<InboxMessage> GetInboxMessageBySendStatus(Guid inboxId, Guid inboxMessageId, MessageSendStatus sendStatus)
        {
            var dataModel = await dataContext.InboxMessages
                .FilterByInboxes(inboxId)
                .FilterByInboxMessageId(inboxMessageId)
                .FilterBySendStatus(sendStatus)
                .FirstOrDefaultAsync();

            return mapper.Map<InboxMessage>(dataModel);
        }

        public async Task<InboxMessageBulkTransaction> UpsertBulkTransaction(InboxMessageBulkTransaction bulkTransaction)
        {
            if (bulkTransaction == null)
                return null;

            var entity = await dataContext.InboxMessageBulkTransactions.FindAsync(bulkTransaction.Id);

            if (entity != null)
            {
                entity.BulkRecipients = bulkTransaction.BulkRecipients;
                entity.TotalMessages = bulkTransaction.TotalMessages;
                entity.SentMessages = bulkTransaction.SentMessages;
                entity.FailedMessages = bulkTransaction.FailedMessages;
                entity.EndDateTimeUtc = bulkTransaction.EndDateTimeUtc;
                entity.Status = bulkTransaction.Status;
                entity.LastUpdatedByPersonId = bulkTransaction.LastUpdatedByPersonId;

                dataContext.Update(entity);
            }
            else
            {
                bulkTransaction.StartDateTimeUtc = dateTimeProvider.GetDateTimeUtc();

                entity = mapper.Map<InboxMessageBulkTransactionDataModel>(bulkTransaction);

                dataContext.Add(entity);
            }

            await dataContext.SaveChangesAsync();

            return mapper.Map<InboxMessageBulkTransaction>(entity);
        }

        public async Task<InboxMessageBulkTransaction> GetBulkTransaction(Guid bulkTransactionId)
        {
            var bulkTransaction = await dataContext.InboxMessageBulkTransactions.FindAsync(bulkTransactionId);

            return bulkTransaction != null ? mapper.Map<InboxMessageBulkTransaction>(bulkTransaction) : null;
        }

        public async Task<Recipient[]> GetRecipients(Guid providerId, BulkRecipients bulkRecipients)
        {
            var predicate = PredicateBuilder.New<ContactDataModel>(false);

            if (bulkRecipients.Clients != null)
            {
                if (bulkRecipients.Clients.Any(c => c.IsAll()))
                {
                    predicate = predicate.Or(t => t.IsClient);
                }
                else
                {
                    var clientIds = bulkRecipients.Clients.Select(t => t.Id).ToArray();

                    predicate = predicate.Or(t => t.IsClient && clientIds.Contains(t.Id));
                }
            }

            if (bulkRecipients.Status != null)
            {
                if (!bulkRecipients.Status.Any(t => t.IsAll()))
                {
                    var statusIds = bulkRecipients.Status.Select(t => t.Id).ToArray();

                    predicate = predicate.Or(t => statusIds.Contains(t.Status));
                }
            }

            if (bulkRecipients.Tags != null)
            {
                if (bulkRecipients.Tags.Any(t => t.IsAll()))
                {
                    predicate = predicate.Or(t => t.Tags.Any());
                }
                else
                {
                    var tagIds = bulkRecipients.Tags.Where(t => t.Id.HasValue).Select(t => t.Id.Value).ToArray();

                    predicate = predicate.Or(c => c.Tags.Any() && c.Tags.Any(t => tagIds.Contains(t.TagId)));
                }
            }

            predicate = PredicateBuilder.New<ContactDataModel>(true).And(t => t.ProviderId == providerId).And(predicate);

            var recipients = await dataContext.Contacts
                .Where(predicate)
                .Select(e => new RecipientDetail() { AccountId = e.Email, DisplayName = e.FullName }).ToArrayAsync();

            recipients = await AggregateTeamRecipients(recipients);
            recipients = AggregateNonClients(recipients);

            return recipients.Where(r => !string.IsNullOrWhiteSpace(r.AccountId)).Select(t => new Recipient()
            {
                To = [t]
            }).ToArray();

            async Task<RecipientDetail[]> AggregateTeamRecipients(RecipientDetail[] source)
            {
                RecipientDetail[] teamRecipients = Array.Empty<RecipientDetail>();

                if (bulkRecipients.Teams != null)
                {
                    if (bulkRecipients.Teams.Any(t => t.IsAll()))
                    {
                        teamRecipients = await dataContext.ProviderStaff
                        .Where(x => x.ProviderId == providerId)
                        .Select(x => new RecipientDetail() { AccountId = x.Person.Email, DisplayName = x.Person.FullName })
                        .ToArrayAsync();
                    }
                    else
                    {
                        teamRecipients = bulkRecipients.Teams.Select(t => new RecipientDetail() { AccountId = t.AccountId, DisplayName = t.DisplayName }).ToArray();
                    }
                }

                return recipients = source.Union(teamRecipients).Distinct(t => t.AccountId).ToArray(); ;
            }

            RecipientDetail[] AggregateNonClients(RecipientDetail[] source)
            {
                if (bulkRecipients.NonClients != null && bulkRecipients.NonClients.Any())
                {
                    var emailRecipients = bulkRecipients.NonClients.Select(e => new RecipientDetail() { AccountId = e.Id, DisplayName = e.DisplayName });

                    source = source.Union(emailRecipients).Distinct(t => t.AccountId).ToArray();
                }

                return source;
            }
        }

        public async Task<Recipient[]> GetSentRecipientsOfBulkMessage(InboxMessage hostedInboxMessage)
        {
            var query = dataContext.InboxMessages.Where(im => im.InboxMessageBulkTransactionId == hostedInboxMessage.InboxMessageBulkTransactionId && im.Id != hostedInboxMessage.Id && im.InboxId == hostedInboxMessage.InboxId);

            return await query.Select(t => t.Recipients).ToArrayAsync();
        }

        private async Task<TokenisedPaginatedResult<MessageResponse>> GetMessages(Guid providerId,
            IQueryable<InboxMessageDataModel> inboxMessageQuery,
            IQueryable<InboxContactDataModel> inboxContactsQuery,
            TokenisedPaginationRequest pagination)
        {
            var result = await tokenisedPaginator.TransformAndPaginateAsync<InboxMessageDataModel, MessageResponse>(inboxMessageQuery, nameof(InboxMessageDataModel.CreatedAt), pagination);

            if (result.Items.Any())
            {
                // Materialize inbox contacts to apply contact id to conversation participants
                var inboxContacts = await inboxContactsQuery
                    .FilterByAccountIds(result.Items.ExtractParticipantAccountIds())
                    .ProjectTo<InboxContact>(mapper.ConfigurationProvider)
                    .ToArrayAsync();

                var providerStaffMembers = await GetProviderStaffMembers(providerId);

                result.Items = new MessageResponseBuilder(result.Items, inboxContacts, providerStaffMembers).Build();
            }

            return result;
        }

        public async Task<Guid[]> GetInboxIdsByPerson(Guid providerId, Guid personId, params InboxStaffRoleType[] roleTypes)
        {
            var query = dataContext.Inboxes
                .Include(x => x.InboxStaffs)
                .Where(x => x.ProviderId == providerId)
                .Where(x => x.InboxStaffs.Any(y =>
                    (y.PersonId == personId || y.PersonId == null) &&
                    (roleTypes.IsNullOrEmpty() || roleTypes.Contains(y.RoleType))
                ));

            return await query
                .Select(x => x.Id)
                .ToArrayAsync();
        }

        private async Task<MessageUpdateResponse[]> UpdateInboxMessageSetRead(IQueryable<InboxMessageDataModel> inboxMessageQuery,
            Guid personId,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus statusFilter)
        {
            inboxMessageQuery = inboxMessageQuery
                .FilterByConversationIds(selectedConversationIds)
                .ExcludeByConversationIds(excludedConversationIds)
                .FilterConversationByStatus(statusFilter);

            var unseenMessageIds = await inboxMessageQuery
                .IsNotSeenByPerson(personId)
                .Select(x => x.Id) // Only materialize ids
                .Distinct()
                .ToArrayAsync();

            await inboxMessageQuery
                .FilterUnread()
                .ExecuteUpdateAsync(x => x.SetProperty(y => y.IsRead, true));

            // Only insert when there are unseen messages
            if (!unseenMessageIds.IsNullOrEmpty())
            {
                await dataContext.InboxMessageSeenBy
                    .AddRangeAsync(unseenMessageIds.Select(x => new InboxMessageSeenByDataModel
                    {
                        Id = Guid.NewGuid(),
                        InboxMessageId = x,
                        PersonId = personId,
                        Timestamp = dateTimeProvider.GetDateTimeUtc()
                    }));
            }

            await dataContext.SaveChangesAsync();

            var result = await GetUpdatedMessages(inboxMessageQuery);

            return result;
        }

        private async Task<MessageUpdateResponse[]> UpdateInboxMessageSetUnread(IQueryable<InboxMessageDataModel> inboxMessageQuery,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus statusFilter)
        {
            inboxMessageQuery = inboxMessageQuery
                .FilterByConversationIds(selectedConversationIds)
                .ExcludeByConversationIds(excludedConversationIds)
                .FilterConversationByStatus(statusFilter)
                .FilterRead();

            var latestConversationMessages = await dataContext.InboxConversations.GetLatestConversationMessage(inboxMessageQuery);

            var result = await GetUpdatedMessages(latestConversationMessages);

            await dataContext.InboxMessages
                .Where(x => latestConversationMessages.Select(y => y.Id).Contains(x.Id))
                .ExecuteUpdateAsync(x => x.SetProperty(y => y.IsRead, false));

            await dataContext.SaveChangesAsync();

            return result;
        }

        private async Task TryDeleteInboxConversations(params Guid[] conversationIds)
        {
            // Clean up if all messages in a conversation are deleted, delete the conversation record
            var inboxConversationDataModels = await dataContext.InboxConversations
                .Include(x => x.InboxMessages)
                .Include(x => x.InboxConversationParticipants)
                .Where(x => conversationIds.Contains(x.Id))
                .Where(x => x.InboxMessages.Where(y => y.Status != MessageStatus.Deleted 
                    && y.Status != MessageStatus.Draft).Count() == 0)
                .ToArrayAsync();

            if (inboxConversationDataModels.Length <= 0) return;

            dataContext.RemoveRange(inboxConversationDataModels);
        }

        private async Task<InboxMessage[]> AssignInboxMessageConversations(InboxMessage[] inboxMessages)
        {
            var inboxId = inboxMessages.First().InboxId;

            // Select all conversations from the inbox messages
            var messageConversations = inboxMessages
                .Select(x => x.Conversation)
                .ToArray();

            // Do a join on incoming message conversations with existing conversations in the database
            // This will return a tuple of (InboxConversation, bool, Participants[]) where bool is a flag to indicate if the conversation is existing or not
            var joinedConversations = await JoinInboxConversations(inboxId.Value, messageConversations);

            // Check if there are new conversations that need to be added
            var newConversations = joinedConversations
                .Where(x => x.IsNewConversation)
                .Select(x => x.Conversation)
                .ToArray();

            if (newConversations.Any()) { await AddInboxConversations(newConversations); }

            // Check if there are existing conversations with new participants to be added
            var newConversationParticipants = joinedConversations
                .Where(x => !x.IsNewConversation)
                .Where(x => x.NewParticipants.Any())
                .SelectMany(x => x.NewParticipants)
                .ToArray();

            if (newConversationParticipants.Any()) { await AddInboxConversationParticipants(newConversationParticipants); }

            // Assign the conversation id to the inbox messages
            inboxMessages = AssignInboxMessagesToAConversation(inboxMessages, joinedConversations.Select(x => x.Conversation).ToArray());

            return inboxMessages;
        }

        private async Task<(InboxConversation Conversation, bool IsNewConversation, InboxConversationParticipant[] NewParticipants)[]> JoinInboxConversations(Guid inboxId, InboxConversation[] messageConversations)
        {
            // Pass through the custom comparer to remove possible duplicates based on conversation type and participants
            var distinctMessageConversations = messageConversations
                .Distinct(new InboxConversationEqualityComparer());

            // Get external threads from incoming message conversations
            var externalThreadIds = distinctMessageConversations
                .SelectMany(x => x.ConversationThreads)
                .Select(x => x.ExternalConversationId.ToLower())
                .ToArray();

            // Find the conversation ids having the external thread ids
            var conversationIds = await dataContext.InboxConversationThreads
                .Include(x => x.InboxConversation)
                .Where(x => x.InboxConversation.InboxId == inboxId)
                .Where(x => externalThreadIds.Contains(x.ExternalConversationId.ToLower()))
                .Select(x => x.ConversationId)
                .ToArrayAsync();

            // Select all conversations that are already in the database and project to InboxConversation model
            var dbMessageConversations = dataContext.InboxConversations
                .Include(x => x.InboxConversationParticipants)
                .Include(x => x.InboxConversationThreads)
                .Where(x => x.InboxId == inboxId)
                .Where(x => conversationIds.Contains(x.Id)) // Add pre-emptive filtering here to only include those that are in the conversation ids
                .ProjectTo<InboxConversation>(mapper.ConfigurationProvider);

            var joinedConversations = distinctMessageConversations
                .GroupJoin(
                    dbMessageConversations,
                    messageConversationsKeySelector => messageConversationsKeySelector, // Assign the conversation from the inbox message as the left table
                    dbConversationsKeySelector => dbConversationsKeySelector, // Assign the conversation from the DB as the right table
                    (messageConversation, dbConversation) => new
                    {
                        MessageConversation = messageConversation,
                        DbConversation = dbConversation
                    },
                     new InboxConversationEqualityComparer() // Pass through the custom comparer and match based on conversation type and participants
                ).SelectMany(
                    joinResult => joinResult.DbConversation.DefaultIfEmpty(),
                    (joinResult, conversationKeySelector) => new
                    {
                        joinResult.MessageConversation,
                        ConversationKeySelector = conversationKeySelector
                    } // Assign the result selector as a new object with the left table (MessageConversation) and the defined conversation KeySelector
                ).Select(
                    result =>
                    {
                        // No matching conversation found in db set IsNewConversation = true to flag that a new conversation needs to be added
                        if (result.ConversationKeySelector == null) return (result.MessageConversation, true, []);

                        // Adding participants
                        var newParticipants = result.MessageConversation.ConversationParticipants
                            .Where(x => !result.ConversationKeySelector.ConversationParticipants.Any(y => y.AccountId.ToLower() == x.AccountId.ToLower()))
                            .Select(x => new InboxConversationParticipant
                            {
                                // Create a new participant here with the db conversation id
                                // Generate a new conversation participant id here for race condition
                                Id = Guid.NewGuid(),
                                AccountId = x.AccountId,
                                ProviderId = x.ProviderId,
                                ConversationId = result.ConversationKeySelector.Id
                            })
                            .ToArray();

                        // Conversation found in db set IsNewConversation = false to flag that conversation already exists
                        return (result.ConversationKeySelector, false, newParticipants);
                    }
                ).ToArray();

            return joinedConversations;
        }

        private InboxMessage[] AssignInboxMessagesToAConversation(InboxMessage[] inboxMessages, InboxConversation[] conversations)
        {
            // Do distinct here to ensure that there are no duplicates and to avoid duplicate inbox messages
            conversations = conversations
                .Distinct(new InboxConversationEqualityComparer())
                .ToArray();

            inboxMessages = inboxMessages
                .GroupJoin(
                    conversations,
                    messagesKeySelector => messagesKeySelector.Conversation, // Assign the conversation from the inbox message as the left table
                    conversationsKeySelector => conversationsKeySelector, // Assign the conversation from the conversations as the right table
                    (message, conversation) => new
                    {
                        Message = message,
                        Conversation = conversation
                    },
                    new InboxConversationEqualityComparer() // Pass through the custom comparer and match based on the thread
                ).SelectMany(
                    joinResult => joinResult.Conversation.DefaultIfEmpty(),
                    (joinResult, conversationKeySelector) => new
                    {
                        joinResult.Message,
                        ConversationKeySelector = conversationKeySelector
                    } // Assign the result selector as a new object with the left table (InboxMessageConversation) and the defined conversation KeySelector
                ).Where(x => x.ConversationKeySelector != null) // Filter the result to include only those that have conversations matched
                .Select(
                    result => {
                        // Assign conversation record to the message for consistency
                        result.Message.Conversation = result.ConversationKeySelector;

                        // Assign conversation id to the message
                        result.Message.ConversationId = result.ConversationKeySelector.Id;

                        return result.Message;
                    }
                ).ToArray();

            return inboxMessages;
        }

        /// <summary>
        /// This method will return only the new inbox messages that are not already in the database based on the fromAccountId and externalSourceMessageId combination
        /// </summary>
        private async Task<InboxMessage[]> RemoveExistingMessagesByAccountAndExternalMessageId(InboxMessage[] syncedInboxMessages)
        {
            var inboxId = syncedInboxMessages.First().InboxId;

            // This query will attempt left join on the syncedInboxMessages with the existing inbox messages based on the fromAccountId and externalSourceMessageId combination
            // Once the join is done, a where clause is added to include only those that are null (i.e. not in the database)
            var result = syncedInboxMessages // Set incoming messages as the left table
                .GroupJoin(
                    dataContext.InboxMessages
                        .FilterByInboxes(inboxId.Value)
                        .Select(x => new { x.FromAccountId, x.ExternalSourceMessageId }), // Set data models as the right table
                    messagesKeySelector => new {
                        FromAccountId = messagesKeySelector.From.AccountId,
                        messagesKeySelector.ExternalSourceMessageId
                    }, // Set the left table key selector (From.AccountId and ExternalSourceMessageId)
                    dataModelsKeySelector => new {
                        dataModelsKeySelector.FromAccountId,
                        dataModelsKeySelector.ExternalSourceMessageId
                    }, // Set the right table key selector (FromAccountId and ExternalSourceMessageId)
                    (messages, dataModels) => new {
                        Messages = messages,
                        DataModels = dataModels
                    }) // Set the result selector a new object with the left table (Messages) and right table (DataModels)
                .SelectMany(
                    joinResult => joinResult.DataModels.DefaultIfEmpty(), // Set the default value for the right table (DataModels) to null if there is not match
                    (joinResult, joinKeySelector) => new {
                        joinResult.Messages,
                        KeySelector = joinKeySelector
                    }) // Set the result selector a new object with the left table (Messages) and the defined KeySelector
                .Where(result => result.KeySelector == null) // Filter the result to include only those that are null (i.e. not in the database)
                .Select(result => result.Messages)
                .ToArray();

            return result;
        }

        private void AddInboxMessageSeenBys(Guid[] inboxMessageIds, Guid personId, DateTime seenDateTimeUtc)
        {
            var seenByDataModels = inboxMessageIds.Select(inboxMessageId => new InboxMessageSeenByDataModel()
            {
                Id = Guid.NewGuid(),
                InboxMessageId = inboxMessageId,
                PersonId = personId,
                Timestamp = seenDateTimeUtc
            });

            dataContext.AddRange(seenByDataModels);
        }

        private IQueryable<InboxContactDataModel> GetInboxContactsQuery(Guid providerId)
        {
            return dataContext.InboxContacts
                .Include(x => x.Contact)
                .FilterByProvider(providerId);
        }

        private async Task<ProviderStaffMember[]> GetProviderStaffMembers(Guid providerId)
        {
            return await dataContext.ProviderStaff
                .Include(x => x.Person)
                .Where(x => x.ProviderId == providerId)
                .ProjectTo<ProviderStaffMember>(mapper.ConfigurationProvider)
                .ToArrayAsync();
        }

        private async Task<MessageUpdateResponse[]> GetUpdatedMessages(IQueryable<InboxMessageDataModel> inboxMessageQuery)
            => await inboxMessageQuery
                .ProjectTo<MessageUpdateResponse>(mapper.ConfigurationProvider)
                .ToArrayAsync();

        public async Task<InboxAccount[]> GetInboxAccounts(Guid providerId, Guid personId, ExternalSource externalSource)
        {
            var query = dataContext.InboxAccounts
                .Where(ia => ia.ExternalSource == externalSource && ia.Inbox.ProviderId == providerId && ia.Inbox.PersonId == personId);

            var inboxAccounts = await query.ToListAsync();

            return inboxAccounts != null ? mapper.Map<InboxAccount[]>(inboxAccounts) : [];
        }
    }
}