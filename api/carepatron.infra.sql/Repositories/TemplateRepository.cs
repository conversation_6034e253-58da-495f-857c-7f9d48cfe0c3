﻿using AutoMapper;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Extensions;
using carepatron.core.Models.Common;
using carepatron.core.Models.Pagination;
using carepatron.core.Models.Tags;
using carepatron.core.Repositories.Templates;
using carepatron.infra.sql.Extensions;
using carepatron.infra.sql.Models.Tags;
using carepatron.infra.sql.Models.Templates;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Users.Models;
using carepatron.core.Authorization.Models;
using carepatron.core.Authorization.ResourceNames;

namespace carepatron.infra.sql.Repositories
{
    public class TemplateRepository : ITemplateRepository
    {
        private readonly DataContext dataContext;
        private readonly IMapper mapper;

        public TemplateRepository(DataContext dataContext, IMapper mapper)
        {
            this.dataContext = dataContext;
            this.mapper = mapper;
        }

        public async Task<PaginatedResult<IntakeTemplate>> GetIntakeTemplates(Guid providerId, int limit, int offset)
        {
            var models = await dataContext
                .Templates
                .Where(x => x.ProviderId == providerId)
                .OrderBy(x => x.Title)
                .Skip(offset)
                .Take(limit + 1)
                .ProjectTo<IntakeTemplate>(mapper.ConfigurationProvider)
                .AsNoTracking()
                .ToListAsync();

            var items = models.Take(limit).ToList();

            return new PaginatedResult<IntakeTemplate>(items,
                new Pagination(items.Count, offset, items.Count < models.Count));
        }

        public async Task<DefaultIntakeTemplate> GetDefaultIntakeTemplate(Guid providerId, Guid id)
        {
            var model = await dataContext.DefaultIntakeTemplates.FirstOrDefaultAsync(x =>
                x.ProviderId == providerId && x.TemplateId == id);

            return mapper.Map<DefaultIntakeTemplate>(model);
        }

        public async Task<DefaultIntakeTemplate> CreateDefaultIntakeTemplate(DefaultIntakeTemplate template)
        {
            var model = mapper.Map<DefaultIntakeTemplateDataModel>(template);

            await dataContext.AddAsync(model);
            await dataContext.SaveChangesAsync();

            return template;
        }

        public async Task DeleteDefaultIntakeTemplate(Guid providerId, Guid id)
        {
            var model = await dataContext.DefaultIntakeTemplates.FirstOrDefaultAsync(x =>
                x.TemplateId == id && x.ProviderId == providerId);

            if (model == null)
                return;

            dataContext.Remove(model);
            await dataContext.SaveChangesAsync();
        }

        public async Task<TemplateCollection> CreateCollection(TemplateCollection collection)
        {
            var model = mapper.Map<TemplateCollectionDataModel>(collection);

            await dataContext.AddAsync(model);
            await dataContext.SaveChangesAsync();

            return collection;
        }

        public async Task<TemplateCollection[]> CreateCollections(TemplateCollection[] collections)
        {
            var models = mapper.Map<TemplateCollectionDataModel[]>(collections);

            await dataContext.AddRangeAsync(models);
            await dataContext.SaveChangesAsync();

            return collections;
        }

        public async Task<TemplateCollection> UpdateCollection(TemplateCollection collection)
        {
            var model = await dataContext.TemplateCollections.FirstOrDefaultAsync(x => x.Id == collection.Id);

            if (model is null)
            {
                return collection;
            }

            model.Title = collection.Title;
            model.LastUpdatedByPersonId = collection.LastUpdatedByPersonId;
            model.UpdatedDateTimeUtc = DateTime.UtcNow;

            await dataContext.SaveChangesAsync();

            return collection;
        }

        public async Task<TemplateCollection> GetCollection(Guid providerId, Guid id)
        {
            var model = await dataContext.TemplateCollections.FirstOrDefaultAsync(x =>
                x.Id == id && x.ProviderId == providerId);

            return mapper.Map<TemplateCollection>(model);
        }

        public async Task DeleteCollection(Guid providerId, Guid id)
        {
            var model = await dataContext.TemplateCollections.FirstOrDefaultAsync(x =>
                x.Id == id && x.ProviderId == providerId);

            if (model == null)
                return;

            dataContext.Remove(model);
            await dataContext.SaveChangesAsync();
        }

        public async Task<PaginatedResult<TemplateCollection>> GetCollections(Guid providerId, int limit, int offset)
        {
            var models = await dataContext.TemplateCollections
                .Where(x => x.ProviderId == providerId)
                .OrderBy(x => x.Title)
                .Skip(offset)
                .Take(limit + 1)
                .AsNoTracking()
                .ToListAsync();

            var items = mapper.Map<TemplateCollection[]>(models.Take(limit));

            return new PaginatedResult<TemplateCollection>(items,
                new Pagination(items.Length, offset, items.Length < models.Count()));
        }

        public async Task<Template> CreateTemplate(Template template)
        {
            var tags = template.Tags;

            var newTags = tags.Where(x => x.Id == Guid.Empty);
            var providerTags = await dataContext.Tags
                .Where(x => x.ProviderId == template.ProviderId)
                .ToListAsync();

            var existingTags = providerTags
                .Where(x => tags.Any(t => t.Id == x.Id || t.Title.ToLower() == x.Title.ToLower()))
                .ToArray();

            var missingTags = newTags
                .Where(x => !existingTags.Any(t => t.Title == x.Title))
                .Select(x => new Tag(Guid.NewGuid(), template.ProviderId, x.Title, TagType.Template, x.ColorHex, true))
                .ToArray();

            var missingTagModels = mapper.Map<TagDataModel[]>(missingTags);

            await dataContext.AddRangeAsync(missingTagModels);

            var model = mapper.Map<TemplateDataModel>(template);

            model.Tags = missingTags.Concat(mapper.Map<Tag[]>(existingTags))
                .EmptyIfNull()
                .Select(x => new TemplateTagDataModel(template.Id, x.Id))
                .ToList();

            await dataContext.AddAsync(model);
            await dataContext.SaveChangesAsync();

            return mapper.Map<Template>(model);
        }

        public async Task UpdatePublicTemplateContentJson(Guid publicTemplateId)
        {
            var model = await dataContext.PublicTemplates.FirstOrDefaultAsync(x => x.Id == publicTemplateId);

            if (model is null)
            {
                return;
            }

            model.ContentJsonb = JsonConvert.DeserializeObject<JObject>(model.ContentJson);
            await dataContext.SaveChangesAsync();
        }

        public async Task<Template> UpdateTemplate(Template template, bool includeSoftDeleted = false)
        {
            var query = dataContext.Templates.AsQueryable();

            if (includeSoftDeleted)
            {
                query = query.IgnoreQueryFilters();
            }

            var model = await query.FirstOrDefaultAsync(x => x.Id == template.Id);

            if (model is null)
            {
                return template;
            }

            model.Title = template.Title;
            model.Content = template.Content;
            model.ContentJson = template.ContentJson?.ToString();
            model.ContentJsonb = template.ContentJson;
            model.Description = template.Description;
            model.Type = template.Type;
            model.TemplateCollectionId = template.TemplateCollectionId;
            model.LastUpdatedByPersonId = template.LastUpdatedByPersonId;
            model.UpdatedDateTimeUtc = DateTime.UtcNow;
            model.HasAiPrompts = template.HasAiPrompts;
            model.Tags = template.Tags
                .EmptyIfNull()
                .Select(x => new TemplateTagDataModel(template.Id, x.Id))
                .PreferExisingItems(model.Tags, x => x.TagId)
                .ToList();

            await dataContext.SaveChangesAsync();

            return template;
        }

        public async Task SetTemplateCollection(Guid id, Guid? templateCollectionId)
        {
            var model = await dataContext.Templates.FirstOrDefaultAsync(x => x.Id == id);
            if (model is null)
                return;
            
            model.TemplateCollectionId = templateCollectionId;
            await dataContext.SaveChangesAsync();
        }

        public async Task<bool> HasAiPrompts(Guid templateId)
        {
            return await dataContext
                .TemplateAiPrompts
                .Where(x => x.TemplateId == templateId).CountAsync() > 0;
        }

        public async Task DeleteTemplate(Guid providerId, Guid id)
        {
            var model = await dataContext.Templates.FirstOrDefaultAsync(x => x.Id == id && x.ProviderId == providerId);

            if (model == null)
                return;

            dataContext.Remove(model);
            await dataContext.SaveChangesAsync();
        }

        public async Task<TemplateMeta[]> GetTemplates(DateRange dateRange, Guid[] templateIds, Guid? lastTemplateId,
            int limit, bool? hasAiPrompts)
        {
            IQueryable<TemplateDataModel> query = dataContext.Templates;

            if (lastTemplateId.HasValue)
            {
                query = query.Where(x => x.Id.CompareTo(lastTemplateId.Value) > 0);
            }

            if (dateRange != null)
            {
                query = query.Where(n =>
                    n.CreatedDateTimeUtc >= dateRange.FromDate && n.CreatedDateTimeUtc <= dateRange.ToDate);
            }

            if (templateIds?.Any() == true)
            {
                query = query.Where(n => templateIds.Contains(n.Id));
            }

            if (hasAiPrompts.HasValue)
            {
                query = query.Where(x => x.HasAiPrompts == hasAiPrompts);
            }

            var models = await query
                .OrderBy(x => x.Id)
                .ProjectTo<TemplateMeta>(mapper.ConfigurationProvider)
                .Take(limit)
                .ToArrayAsync();

            return models;
        }

        public async Task<PublicTemplateMeta[]> GetPublicTemplates(DateRange dateRange, Guid[] publicTemplateIds,
            Guid? lastPublicTemplateId, int limit, bool? hasAiPrompts)
        {
            IQueryable<PublicTemplateDataModel> query = dataContext.PublicTemplates;

            if (lastPublicTemplateId.HasValue)
            {
                query = query.Where(x => x.Id.CompareTo(lastPublicTemplateId.Value) > 0);
            }

            if (hasAiPrompts.HasValue)
                query = query.Where(x => x.HasAiPrompts == hasAiPrompts);

            if (dateRange != null)
            {
                query = query.Where(n =>
                    n.CreatedDateTimeUtc >= dateRange.FromDate && n.CreatedDateTimeUtc <= dateRange.ToDate);
            }

            if (publicTemplateIds?.Any() == true)
            {
                query = query.Where(n => publicTemplateIds.Contains(n.Id));
            }

            var models = await query
                .OrderBy(x => x.Id)
                .Take(limit)
                .ProjectTo<PublicTemplateMeta>(mapper.ConfigurationProvider)
                .ToArrayAsync();

            return models;
        }

        public async Task<PaginatedResult<SimpleTemplate>> GetSimpleTemplates(Guid providerId, string searchTerm,
            Guid[] tags, string[] collections, UnifiedSearchSortBy? sortBy, int limit, int offset,
            bool? hasAiPrompts, Guid? templatesFolderId, Guid? currentLoggedInPersonId, bool favorites)
        {
            var query = dataContext.Templates
                .Include(x => x.Tags)
                .ThenInclude(x => x.Tag)
                .Where(x => x.ProviderId == providerId);

            var favoriteTemplateIds = await dataContext.TemplatePersonFavorites
                .Where(f => f.PersonId == currentLoggedInPersonId)
                .AsNoTracking()
                .Select(f => f.TemplateId)
                .ToListAsync();

            query = ApplyFilter(query, searchTerm, tags, collections, hasAiPrompts, providerId, templatesFolderId,
                currentLoggedInPersonId, favorites);

            query = ApplySorting(query, sortBy, favoriteTemplateIds.ToHashSet());

            var totalCount = await query
                .CountAsync();

            var templatesQuery = query
                .Skip(offset)
                .Take(limit + 1)
                .Select(x => new SimpleTemplate
                {
                    Id = x.Id,
                    ProviderId = x.ProviderId,
                    CreatedDateTimeUtc = x.CreatedDateTimeUtc,
                    UpdatedDateTimeUtc = x.UpdatedDateTimeUtc,
                    Collection = x.TemplateCollection.Title,
                    Description = x.Description,
                    Tags = mapper.Map<Tag[]>(x.Tags),
                    Title = x.Title,
                    Type = x.Type,
                    FolderId = x.TemplateFolder.FolderId,
                    IsFavorite = favoriteTemplateIds.Contains(x.Id),
                    IsPublished = x.IsPublished,
                    HasAiPrompts = x.HasAiPrompts,
                    IsIntakeDefault = x.DefaultIntakeTemplate != null,
                    CopiedFromTemplateId = x.CopiedFromTemplateId,
                    CreatedByPersonId = x.CreatedByPersonId,
                    LastUpdatedByPersonId = x.LastUpdatedByPersonId,
                });

            var items = await templatesQuery
                .AsNoTracking()
                .ToArrayAsync();

            await EnrichPersonProfiles(items);

            return new PaginatedResult<SimpleTemplate>(items,
                    new Pagination(items.Length, offset, items.Length < limit))
            {
                TotalCount = totalCount
            };

            async Task EnrichPersonProfiles(SimpleTemplate[] items)
            {
                var personIds = items
                                .Select(x => x.CreatedByPersonId)
                                .Concat(items.Select(x => x.LastUpdatedByPersonId))
                                .Distinct()
                                .ToArray();

                var persons = await dataContext.Persons.Include(x => x.ProfilePhoto)
                    .Where(x => personIds.Contains(x.Id))
                    .ProjectTo<SimplePerson>(mapper.ConfigurationProvider)
                    .ToDictionaryAsync(x => x.Id);

                foreach (var item in items)
                {
                    if (persons.TryGetValue(item.CreatedByPersonId, out var createdByPerson))
                    {
                        item.CreatedByPerson = createdByPerson;
                    }

                    if (persons.TryGetValue(item.LastUpdatedByPersonId, out var lastUpdatedByPerson))
                    {
                        item.LastUpdatedByPerson = lastUpdatedByPerson;
                    }
                }
            }
        }

        private IQueryable<TemplateDataModel> ApplySorting(IQueryable<TemplateDataModel> query, UnifiedSearchSortBy? sortBy, HashSet<Guid> favoriteTemplateIds)
        {
            return sortBy switch
            {
                UnifiedSearchSortBy.MostRecent => query
                    .OrderByDescending(x => favoriteTemplateIds.Contains(x.Id))
                    .ThenByDescending(f => f.UpdatedDateTimeUtc),
                UnifiedSearchSortBy.Title => query
                    .OrderByDescending(x => favoriteTemplateIds.Contains(x.Id))
                    .ThenBy(f => f.Title),
                _ => query.OrderBy(f => f.Title)
            };
        }

        private IQueryable<TemplateDataModel> ApplyFilter(IQueryable<TemplateDataModel> query, string searchTerm,
            Guid[] tags, string[] collections, bool? hasAiPrompts, Guid providerId, Guid? templatesFolderId,
            Guid? favoritesByPersonId, bool favorites)
        {
            if (!string.IsNullOrWhiteSpace(searchTerm))
                query = query.Where(x => EF.Functions.ILike(x.Title, $"%{searchTerm}%"));
            
            //todo: alexcgcarepatron - backwards compatibiltiy. to be removed once v2 fully integrated.
            var oldToNewCollectionMapping = new Dictionary<string, string>
            {
                { "Note", "Notes"},
                { "Enrolment", "Forms"},
                { "Report", "Plans & reports"},
                { "InTake", "Forms"},
                { "Assessment", "Assessments"},
                { "Email", "Letters"}
            };
            
            var newToOldCollectionsMapping = new Dictionary<string, List<string>>
            {
                { "Notes", ["Note"] },
                { "Forms", ["Enrolment", "InTake" ]},
                { "Plans & reports", ["Report"] },
                { "Assessments", ["Assessment"] },
                { "Letters", ["Email"] },
            };

            if (collections is { Length: > 0 })
            {
                var collectionFilters = new List<string>();
                foreach (var collection in collections)
                {
                    collectionFilters.Add(collection);
                    if (oldToNewCollectionMapping.TryGetValue(collection, out var newCollection))
                        collectionFilters.Add(newCollection);
                    
                    if (newToOldCollectionsMapping.TryGetValue(collection, out var oldCollection))
                        collectionFilters.AddRange(oldCollection);
                }
                
                var containsOther = collectionFilters.Any(x => x == "Other");
                
                query = query.Where(x => (containsOther && !x.TemplateCollectionId.HasValue) || collectionFilters.Contains(x.TemplateCollection.Title));
            }

            if (favorites)
            {
                query = query.Where(x => x.FavoriteByPersons.Any(y => y.PersonId == favoritesByPersonId));
            }

            if (hasAiPrompts.HasValue)
                query = query.Where(x => x.HasAiPrompts == hasAiPrompts);

            if (templatesFolderId.HasValue)
                query = query
                    .Where(x => x.TemplateFolder.FolderId == templatesFolderId);

            if (tags.Length > 0)
                query = query
                    .Where(x => x.Tags.Any(y => tags.Contains(y.Tag.Id)));

            return query;
        }

        public async Task<PaginatedResult<TemplateMeta>> GetPaginatedTemplatesByFolderId(Guid providerId, Guid folderId,
            bool deletedOnly, int limit = 20, int offset = 0)
        {
            var query = dataContext.Templates
                .Include(x => x.TemplateFolder)
                .Where(x => x.ProviderId == providerId && x.TemplateFolder.FolderId == folderId);

            if (deletedOnly)
            {
                query = query
                    .IgnoreQueryFilters()
                    .Where(x => x.DeletedAtUtc.HasValue);
            }

            var models = await query
                .ProjectTo<TemplateMeta>(mapper.ConfigurationProvider)
                .Skip(offset)
                .Take(limit + 1)
                .ToArrayAsync();

            var items = models.Take(limit).ToArray();

            return new PaginatedResult<TemplateMeta>(items,
                new Pagination(items.Length, offset, items.Length < models.Length));
        }

        public async Task<Template[]> GetTemplatesByFolderId(Guid providerId, Guid folderId)
        {
            var models = await dataContext
                .Templates
                .Where(x => x.ProviderId == providerId && x.TemplateFolder.FolderId == folderId)
                .Include(x => x.Tags)
                .ThenInclude(x => x.Tag)
                .Include(x => x.TemplateCollection)
                .Include(x => x.FormFields)
                .Include(x => x.FileAttachments)
                .Include(x => x.DefaultIntakeTemplate)
                .Include(x => x.AiPrompts)
                .AsSplitQuery()
                .AsNoTracking()
                .ToArrayAsync();

            var items = mapper.Map<Template[]>(models);

            return items;
        }

        public async Task BulkDelete(Guid providerId, Guid[] templateIds, bool isSoftDelete)
        {
            if (templateIds == null || !templateIds.Any())
            {
                return;
            }

            var query = dataContext
                .Templates
                .Where(x => x.ProviderId == providerId && templateIds.Contains(x.Id));

            var templates = await query.ToArrayAsync();

            if (!isSoftDelete)
            {
                dataContext.HardDeleteRange(templates);
            }
            else
            {
                dataContext.SoftDeleteRange(templates);
            }

            await dataContext.SaveChangesAsync();
        }

        public async Task Restore(Guid providerId, Guid id)
        {
            var model = await dataContext
                .Templates
                .IgnoreQueryFilters()
                .Include(x => x.TemplateFolder)
                .ThenInclude(x => x.Folder)
                .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Id == id);

            if (model == null)
                return;

            model.DeletedAtUtc = null;

            await dataContext.SaveChangesAsync();
        }

        public async Task RestoreByFolderId(Guid providerId, Guid id)
        {
            await dataContext
                .Templates
                .Include(x => x.TemplateFolder)
                .IgnoreQueryFilters()
                .Where(x => x.ProviderId == providerId && x.TemplateFolder.FolderId == id && x.DeletedAtUtc.HasValue)
                .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.DeletedAtUtc, (DateTime?)null));
        }

        public async Task HardDelete(Guid providerId, Guid id)
        {
            var model = await dataContext.Templates
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Id == id);

            if (model == null)
                return;

            dataContext.HardDelete(model);

            await dataContext.SaveChangesAsync();
        }

        public async Task<ResourceName> GetResourceName(Guid id)
        {
            var values = await dataContext.Templates
                .AsNoTracking()
                .Select(c => new
                {
                    c.ProviderId, c.Id
                })
                .FirstOrDefaultAsync(c => c.Id == id);

            if (values == null)
                return null;

            return new ResourceNameBuilder()
                .BuildTemplate(values.ProviderId, values.Id);
        }

        public async Task<PaginatedResult<Template>> GetTemplates(Guid providerId,
            string searchTerm,
            Guid[] tags,
            string[] collections,
            int limit,
            int offset,
            bool? hasAiPrompts)
        {
            var query = dataContext.Templates
                .Where(x => x.ProviderId == providerId);

            if (!string.IsNullOrWhiteSpace(searchTerm))
                query = query.Where(x => EF.Functions.ILike(x.Title, $"%{searchTerm}%"));

            //todo: alexgcarepatron - to be removed
            var oldToNewCollectionMapping = new Dictionary<string, string>
            {
                { "Note", "Notes"},
                { "Enrolment", "Forms"},
                { "Report", "Plans & reports"},
                { "InTake", "Forms"},
                { "Assessment", "Assessments"},
                { "Email", "Letters"}
            };
            
            var newToOldCollectionsMapping = new Dictionary<string, List<string>>
            {
                { "Notes", ["Note"] },
                { "Forms", ["Enrolment", "InTake" ]},
                { "Plans & reports", ["Report"] },
                { "Assessments", ["Assessment"] },
                { "Letters", ["Email"] },
            };

            if (collections is { Length: > 0 })
            {
                var collectionFilters = new List<string>();
                foreach (var collection in collections)
                {
                    collectionFilters.Add(collection);
                    if (oldToNewCollectionMapping.TryGetValue(collection, out var newCollection))
                        collectionFilters.Add(newCollection);
                    
                    if (newToOldCollectionsMapping.TryGetValue(collection, out var oldCollection))
                        collectionFilters.AddRange(oldCollection);
                }
                
                var containsOther = collectionFilters.Any(x => x == "Other");
                
                query = query.Where(x => (containsOther && !x.TemplateCollectionId.HasValue) || collectionFilters.Contains(x.TemplateCollection.Title));
            }

            if (hasAiPrompts.HasValue)
                query = query.Where(x => x.HasAiPrompts == hasAiPrompts);

            if (tags.Length > 0)
                query = query.Where(x => x.Tags.Any(y => tags.Contains(y.TagId)));

            var totalCount = await query.CountAsync();

            var models = await query
                .Include(x => x.Tags)
                .ThenInclude(x => x.Tag)
                .Include(x => x.TemplateCollection)
                .Include(x => x.FormFields)
                .Include(x => x.FileAttachments)
                .Include(x => x.DefaultIntakeTemplate)
                .Include(x => x.AiPrompts)
                .OrderBy(x => x.Title)
                .Skip(offset)
                .Take(limit + 1)
                .AsNoTracking()
                .AsSplitQuery()
                .ToListAsync();

            var items = mapper.Map<Template[]>(models.Take(limit));

            return new PaginatedResult<Template>(items,
                new Pagination(items.Length, offset, items.Length < models.Count))
            {
                TotalCount = totalCount
            };
        }

        public async Task<Template[]> GetTemplatesByIds(Guid? providerId, Guid[] ids)
        {
            var query = dataContext.Templates
                .Where(x => ids.Contains(x.Id));

            if (providerId.HasValue)
            {
                query = query.Where(x => x.ProviderId == providerId);
            }

            query = query
                .Include(x => x.Tags)
                .ThenInclude(x => x.Tag)
                .Include(x => x.TemplateCollection)
                .Include(x => x.FormFields)
                .Include(x => x.AiPrompts)
                .Include(x => x.FileAttachments)
                .Include(x => x.DefaultIntakeTemplate);

            var templates = await query.ProjectTo<Template>(mapper.ConfigurationProvider)
                .AsNoTracking()
                .AsSplitQuery()
                .ToArrayAsync();

            return templates;
        }

        public async Task<PublicTemplate> CreatePublicTemplate(PublicTemplate template)
        {
            var model = mapper.Map<PublicTemplateDataModel>(template);

            await dataContext.AddAsync(model);
            await dataContext.SaveChangesAsync();

            return template;
        }

        public async Task<PublicTemplate> UpdatePublicTemplate(PublicTemplate template)
        {
            var model = await dataContext.PublicTemplates.FirstOrDefaultAsync(x => x.Id == template.Id);

            if (model is null)
            {
                return template;
            }

            var newModel = mapper.Map<PublicTemplateDataModel>(template);

            model.Title = newModel.Title;
            model.Content = newModel.Content;
            model.ContentJson = newModel.ContentJson;
            model.ContentJsonb = newModel.ContentJsonb;
            model.Description = newModel.Description;
            model.Collection = newModel.Collection;
            model.HasAiPrompts = template.HasAiPrompts;
            model.UpdatedDateTimeUtc = DateTime.UtcNow;
            model.Tags = newModel.Tags
                .EmptyIfNull()
                .Select(x => new PublicTemplateTagDataModel(template.Id, x.Title))
                .PreferExisingItems(model.Tags, x => x.Title)
                .ToList();
            
            model.Professions = newModel.Professions
                .EmptyIfNull()
                .Select(x => new PublicTemplateProfessionDataModel(newModel.Id, x.Profession))
                .PreferExisingItems(model.Professions, x => x.Profession)
                .ToList();

            await dataContext.SaveChangesAsync();

            return template;
        }

        public async Task UpsertPublicTemplate(PublicTemplate publicTemplate)
        {
            var model = await dataContext.PublicTemplates.FirstOrDefaultAsync(x => x.Id == publicTemplate.Id);

            if (model is not null)
            {
                model.Title = publicTemplate.Title;
                model.Content = publicTemplate.Content;
                model.ContentJson = publicTemplate.ContentJson.ToString();
                model.ContentJsonb = publicTemplate.ContentJson;
                model.Description = publicTemplate.Description;
                model.Collection = publicTemplate.Collection;
                model.HasAiPrompts = publicTemplate.HasAiPrompts;
                model.UpdatedDateTimeUtc = DateTime.UtcNow;
                model.Tags = publicTemplate.Tags
                    .EmptyIfNull()
                    .Select(x => new PublicTemplateTagDataModel(publicTemplate.Id, x.Title))
                    .PreferExisingItems(model.Tags, x => x.Title)
                    .ToList();
                model.Professions = publicTemplate.Professions
                    .EmptyIfNull()
                    .Select(x => new PublicTemplateProfessionDataModel(publicTemplate.Id, x))
                    .PreferExisingItems(model.Professions, x => x.Profession)
                    .ToList();
            }
            else
            {
                model = mapper.Map<PublicTemplateDataModel>(publicTemplate);
                await dataContext.AddAsync(model);
            }

            await dataContext.SaveChangesAsync();
        }

        public async Task DeletePublicTemplate(Guid id)
        {
            var model = await dataContext.PublicTemplates.FirstOrDefaultAsync(x => x.Id == id);

            if (model == null)
                return;

            dataContext.Remove(model);
            await dataContext.SaveChangesAsync();
        }

        public async Task<PaginatedResult<PublicTemplate>> GetPublicTemplates(string searchTerm,
            string[] collections,
            string[] tags,
            string[] professions,
            int limit,
            int offset,
            bool? hasAiPrompts,
            bool? isRecommended,
            Guid[] recommendedPublicTemplateIds,
            UnifiedSearchSortBy? sortBy)
        {
            var query = dataContext.PublicTemplates
                .AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchTerm))
                query = query.Where(x => EF.Functions.ILike(x.Title, $"%{searchTerm}%"));

            if (collections is { Length: > 0 })
            {
                var collectionHashSet = collections.Where(x => !string.IsNullOrEmpty(x)).ToHashSet();
                
                var containsOther = collectionHashSet.Any(x => x == "Other");
                
                query = query.Where(x => (containsOther && string.IsNullOrEmpty(x.Collection)) || collectionHashSet.Contains(x.Collection));
            }
            

            if (hasAiPrompts.HasValue)
            {
                query = query.Where(x => x.HasAiPrompts == hasAiPrompts);
            }

            if (isRecommended.HasValue)
            {
                query = isRecommended.Value
                    ? query
                        .Where(x => recommendedPublicTemplateIds.Contains(x.Id))
                    : query
                        .Where(x => !recommendedPublicTemplateIds.Contains(x.Id));
            }

            // contains are case sensitive :(
            if (tags.Length > 0)
                query = query.Where(x => x.Tags.Any(y => tags.Contains(y.Title)));

            // contains are case sensitive :(
            if (professions.Length > 0)
                query = query.Where(x => x.Professions.Any(y => professions.Contains(y.Profession)));

            var totalCount = await query.CountAsync();
            var models = await query
                .Include(x => x.Tags)
                .Include(x => x.Professions)
                .Include(x => x.FileAttachments)
                .Include(x => x.AiPrompts)
                .SortBy(sortBy)
                .Skip(offset)
                .Take(limit + 1)
                .AsNoTracking()
                .AsSplitQuery()
                .ToListAsync();

            var items = mapper.Map<PublicTemplate[]>(models.Take(limit));

            return new PaginatedResult<PublicTemplate>(items,
                new Pagination(items.Length, offset, items.Length < models.Count))
            {
                TotalCount = totalCount
            };
        }

        public async Task UpdateTemplateMetrics(Guid templateId, int copyCount, int viewCount, int useCount)
        {
            var template = await dataContext.Templates.FirstOrDefaultAsync(x => x.Id == templateId);

            if (template is null)
            {
                return;
            }

            template.CopyCount = copyCount;
            template.ViewCount = viewCount;
            template.UseCount = useCount;

            await dataContext.SaveChangesAsync();
        }

        public async Task UpdatePublicTemplateMetrics(Guid publicTemplateId, int addCopyCount, int addViewCount,
            int addUseCount)
        {
            await dataContext.PublicTemplates
                .Where(x => x.Id == publicTemplateId)
                .ExecuteUpdateAsync(x =>
                    x.SetProperty(template => template.CopyCount, template => template.CopyCount + addCopyCount)
                        .SetProperty(template => template.ViewCount, template => template.ViewCount + addViewCount)
                        .SetProperty(template => template.UseCount, template => template.UseCount + addUseCount));

            await dataContext.SaveChangesAsync();
        }

        public async Task<TemplatesUsedByPerson> GetTemplatesUsedByPerson(Guid personId, Guid providerId)
        {
            var model = await dataContext.TemplatesUsedByPersons.FirstOrDefaultAsync(x =>
                x.PersonId == personId && x.ProviderId == providerId);

            return mapper.Map<TemplatesUsedByPerson>(model);
        }

        public async Task<TemplatesUsedByPerson> SaveTemplatesUsedByPerson(TemplatesUsedByPerson templatesUsedByPerson)
        {
            var model = await dataContext.TemplatesUsedByPersons
                .FirstOrDefaultAsync(x =>
                    x.PersonId == templatesUsedByPerson.PersonId && x.ProviderId == templatesUsedByPerson.ProviderId);

            if (model is null)
            {
                model = mapper.Map<TemplatesUsedByPersonDataModel>(templatesUsedByPerson);

                await dataContext.AddAsync(model);
            }
            else
            {
                model.Favourites = templatesUsedByPerson.Favourites;
                model.RecentlyUsed = templatesUsedByPerson.RecentlyUsed;
            }

            await dataContext.SaveChangesAsync();

            return templatesUsedByPerson;
        }

        public async Task<Template> GetTemplate(Guid id, Guid? providerId = null, bool includeSoftDeleted = false, Guid? currentLoggedInPersonId = null)
        {
            var query = dataContext.Templates
                .Where(x => x.Id == id);

            if (providerId.HasValue)
            {
                query = query.Where(x => x.ProviderId == providerId);
            }

            if (includeSoftDeleted)
            {
                query = query.IgnoreQueryFilters();
            }

            query = query
                .Include(t => t.Tags)
                .ThenInclude(t => t.Tag)
                .Include(t => t.TemplateCollection)
                .Include(t => t.FileAttachments)
                .Include(t => t.DefaultIntakeTemplate)
                .Include(t => t.FormFields)
                .Include(x => x.AiPrompts)
                .Include(x => x.LastUpdatedByPerson)
                .Include(x => x.CreatedByPerson)
                .Include(x => x.FavoriteByPersons)
                .AsSplitQuery();

            var model = await query.FirstOrDefaultAsync();

            return mapper.Map<Template>(model, opts => opts.Items["currentLoggedInPersonId"] = currentLoggedInPersonId);
        }

        public Task<TemplateMeta> GetMeta(Guid id, Guid? providerId = null)
        {
            var query = dataContext.Templates
                .Where(x => x.Id == id);

            if (providerId.HasValue)
            {
                query = query.Where(x => x.ProviderId == providerId);
            }

            return query
                .ProjectTo<TemplateMeta>(mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();
        }

        public async Task<TemplateMeta[]> GetMeta(Guid[] ids, Guid? providerId = null)
        {
            if (ids == null || !ids.Any())
            {
                return Array.Empty<TemplateMeta>();
            }

            var query = dataContext.Templates
                .Where(x => ids.Contains(x.Id));

            if (providerId.HasValue)
            {
                query = query.Where(x => x.ProviderId == providerId);
            }

            return await query.ProjectTo<TemplateMeta>(mapper.ConfigurationProvider).ToArrayAsync();
        }

        public async Task MarkTemplateAsPublished(Guid providerId, Guid templateId)
        {
            var entity = await dataContext.Templates
                .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Id == templateId);

            if (entity is null) return;

            entity.IsPublished = true;
            await dataContext.SaveChangesAsync();
        }

        public async Task MarkTemplateAsUnpublished(Guid providerId, Guid templateId)
        {
            var entity = await dataContext.Templates
                .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Id == templateId);

            if (entity is null) return;

            entity.IsPublished = false;
            await dataContext.SaveChangesAsync();
        }

        public async Task<PublicTemplate> GetPublicTemplate(Guid id)
        {
            var model = await dataContext.PublicTemplates
                .Include(x => x.Tags)
                .Include(x => x.FileAttachments)
                .AsSplitQuery()
                .FirstOrDefaultAsync(x => x.Id == id);

            return mapper.Map<PublicTemplate>(model);
        }

        public Task<PublicTemplateMeta> GetPublicTemplateMeta(Guid id)
        {
            return dataContext.PublicTemplates
                .ProjectTo<PublicTemplateMeta>(mapper.ConfigurationProvider)
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<Template[]> CreateTemplates(Template[] templates)
        {
            foreach (var template in templates)
            {
                var model = mapper.Map<TemplateDataModel>(template);
                model.Tags = template.Tags
                    .EmptyIfNull()
                    .Select(x => new TemplateTagDataModel(template.Id, x.Id))
                    .PreferExisingItems(model.Tags, x => x.TagId)
                    .ToList();

                await dataContext.AddAsync(model);
            }

            await dataContext.SaveChangesAsync();

            return templates;
        }

        public async Task<TemplateCollection> GetCollectionByName(Guid providerId, string collection)
        {
            if (string.IsNullOrWhiteSpace(collection))
                return null;

            var model = await dataContext.TemplateCollections
                .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Title.ToLower() == collection.ToLower());

            return mapper.Map<TemplateCollection>(model);
        }
    }
}