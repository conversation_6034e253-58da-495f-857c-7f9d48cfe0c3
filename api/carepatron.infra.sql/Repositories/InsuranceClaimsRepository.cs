using System;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Insurance.Interfaces;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Extensions;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Pagination;
using carepatron.core.Repositories.Insurance;
using carepatron.core.Repositories.Permissions;
using carepatron.infra.sql.Extensions;
using carepatron.infra.sql.Models.Insurance;
using carepatron.infra.sql.Utils;
using Microsoft.EntityFrameworkCore;

namespace carepatron.infra.sql.Repositories;

public class InsuranceClaimsRepository
    (DataContext dataContext, IMapper mapper) : IInsuranceClaimsRepository, IPermissionRepository<IBaseInsuranceClaim>
{
    public async Task<string> GetNextNumber(Guid providerId, int limit = 3)
    {
        var invalid = true;
        var i = 0;
        var claimNumber = string.Empty;

        while (invalid && i < 5)
        {
            var range = Convert.ToInt32(3 * Math.Pow(10, i));
            var latestNumber = await GetLastNumber(providerId, range);
            if (string.IsNullOrEmpty(latestNumber))
            {
                return "1".PadLeft(6, '0');
            }

            claimNumber = Next(latestNumber, 1);
            invalid = await DoesNumberExist(providerId, claimNumber);
            claimNumber = invalid ? string.Empty : claimNumber;
            i++;
        }

        return claimNumber;
    }

    public async Task<bool> DoesNumberExist(
        Guid providerId,
        string number,
        bool includeDeleted = true
    )
    {
        var query = dataContext
            .InsuranceClaims.AsNoTracking()
            .Where(x => x.ProviderId == providerId && x.Number == number);
        if (includeDeleted)
        {
            query = query.IgnoreQueryFilters();
        }

        return await query.AnyAsync();
    }

    public async Task<PaginatedResult<InsuranceClaim>> Get(
        Guid providerId,
        Guid? contactId,
        PaginationRequest paginationRequest,
        ClaimStatus[] statuses,
        DateTime? fromDate,
        DateTime? toDate,
        Guid[] taskIds,
        CancellationToken cancellationToken = default,
        Guid? assignedToPersonId = null,
        string searchTerm = null
    )
    {
        var query = dataContext
            .InsuranceClaims
            .Include(x => x.ServiceLines.OrderBy(y => y.OrderIndex).ThenBy(y => y.Id))
                .ThenInclude(x => x.BillableItem)
            .Include(x => x.TaskClaims)
            .Where(x => x.ProviderId == providerId);

        if (assignedToPersonId.HasValue)
        {
            query = query
                .Join(dataContext.ContactAssignedStaff,
                    claim => claim.ContactId,
                    con => con.ContactId,
                    (claim, con) => new { Claim = claim, AssignedContact = con })
                .Where(x => x.AssignedContact.PersonId == assignedToPersonId.Value)
                .Select(x => x.Claim);
        }

        if (contactId.HasValue)
        {
            query = query.Where(x => x.ContactId == contactId);
        }

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(x => EF.Functions.ILike(x.Number, $"%{searchTerm}%"));
        }

        if (statuses != null && statuses.Length > 0)
        {
            query = query.Where(x => statuses.Contains(x.Status));
        }

        if (fromDate.HasValue || toDate.HasValue)
        {
            query = query.Where(x =>
                x.ServiceLines.Any(s =>
                    (!fromDate.HasValue || s.Date >= DateOnly.FromDateTime(fromDate.Value))
                    && (!toDate.HasValue || s.Date <= DateOnly.FromDateTime(toDate.Value))
                )
                || (
                    (!fromDate.HasValue || x.CreatedDateTimeUtc >= fromDate.Value)
                    && (!toDate.HasValue || x.CreatedDateTimeUtc <= toDate.Value)
                )
            );
        }

        if (taskIds.Length > 0)
        {
            query = query.Where(x => x.TaskClaims.Any(t => taskIds.Contains(t.TaskId)));
        }

        var total = await query.CountAsync(cancellationToken);

        query = query.OrderByDescending(x => x.Number).ThenByDescending(x => x.CreatedDateTimeUtc);

        if (paginationRequest?.Offset != null)
        {
            query = query.Skip(paginationRequest.Offset);
        }

        if (paginationRequest?.Limit != null)
        {
            query = query.Take(paginationRequest.Limit);
        }

        var result = await query
            .ProjectTo<InsuranceClaim>(mapper.ConfigurationProvider)
            .ToArrayAsync(cancellationToken);
        var paginatedResult = PaginatedResult<InsuranceClaim>.FromResult(
            result,
            paginationRequest
        );
        paginatedResult.TotalCount = total;
        return paginatedResult;
    }


    public async Task<InsuranceClaimReference[]> GetTaskClaims(Guid providerId, Guid contactId, Guid taskId, CancellationToken cancellationToken)
    {
        var query = dataContext.InsuranceClaims
            .Include(x => x.TaskClaims)
            .Where(x => x.ProviderId == providerId
                && x.ContactId == contactId)
            .Where(x => x.TaskClaims.Any(tc => tc.TaskId == taskId));

        query = query.OrderByDescending(x => x.Number).ThenByDescending(x => x.CreatedDateTimeUtc);

        var result = await query
            .ProjectTo<InsuranceClaimReference>(mapper.ConfigurationProvider)
            .ToArrayAsync(cancellationToken);

        return result;
    }

    public async Task<InsuranceClaim> GetById(Guid id, Guid providerId)
    {
        var claim = await dataContext.InsuranceClaims
            .Where(x => x.Id == id && x.ProviderId == providerId)
            .Include(x => x.ServiceLines)
            .Include(x => x.ContactInsurancePolicy)
            .ProjectTo<InsuranceClaim>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync();
        return claim;
    }

    public async Task<InsuranceClaimExportData[]> ExportByProviderId(Guid providerId,
        Guid personId,
        ClaimStatus[] statuses,
        Guid[] staffIds,
        string[] payerIds,
        bool isAssignedOnly,
        DateOnly? fromDate = null,
        DateOnly? toDate = null)
    {
        var query = dataContext
           .InsuranceClaims
           .AsNoTracking()
           .Include(x => x.ServiceLines.OrderBy(y => y.OrderIndex).ThenBy(y => y.Id))
               .ThenInclude(x => x.BillableItem)
           .Include(x => x.TaskClaims)
           .Include(x => x.InsuranceClaimUsProfessional)
                .ThenInclude(y => y.BillingDetail)
            .Include(x => x.InsuranceClaimUsProfessional)
                .ThenInclude(y => y.ServiceFacility)
            .Include(x => x.InsuranceClaimUsProfessional)
                .ThenInclude(y => y.ReferringProviders)
            .Include(x => x.InsuranceClaimUsProfessional)
                .ThenInclude(y => y.DiagnosticCodes)
            .Include(x => x.RenderingProviders)
           .Include(x => x.Client)
           .Include(x => x.ContactInsurancePolicy)
           .Where(x => x.ProviderId == providerId);

        if (isAssignedOnly)
        {
            query = query
                .Join(dataContext.ContactAssignedStaff, claim => claim.ContactId, con => con.ContactId, (claim, con) => new { Claim = claim, AssignedContact = con })
                .Where(x => x.AssignedContact.PersonId == personId)
                .Select(x => x.Claim);
        }

        if (staffIds != null && staffIds.Length > 0)
        {
            query = query.Where(x => x.RenderingProviders.Any(rp => rp.StaffPersonId.HasValue && staffIds.Contains(rp.StaffPersonId.Value)));
        }

        if (payerIds != null && payerIds.Length > 0)
        {
            query = query.Where(x => payerIds.Contains(x.ContactInsurancePolicy.PayerNumber));
        }

        if (statuses != null && statuses.Length > 0)
        {
            query = query.Where(x => statuses.Contains(x.Status));
        }

        if (fromDate.HasValue || toDate.HasValue)
        {
            fromDate ??= DateOnly.MinValue;
            toDate ??= DateOnly.MaxValue;

            query = query.Where(x =>
                x.ToDate >= fromDate &&
                x.FromDate <= toDate
            );
        }

        var result = await query.ToArrayAsync();
        var mappedClaims = mapper.Map<InsuranceClaimExportData[]>(result);

        return mappedClaims;
    }

    public async Task CloseDraftClaimsForContact(Guid providerId, Guid contactId)
    {
        await dataContext.InsuranceClaims
             .Where(x => x.ProviderId == providerId && x.ContactId == contactId && x.Status == ClaimStatus.Draft)
             .ExecuteUpdateAsync(x => x.SetProperty(c => c.Status, ClaimStatus.Closed));
    }

    public async Task<PaginatedResult<InsuranceClaimListEntry>> GetByProviderId(
        Guid providerId,
        Guid personId,
        Guid[] contactIds,
        Guid[] staffIds,
        string[] payerIds,
        bool isAssignedOnly,
        int offset,
        int limit,
        ClaimStatus[] statuses,
        DateOnly? fromDate = null,
        DateOnly? toDate = null,
        string? searchTerm = null,
        Sorting sorting = null)
    {
        var query = dataContext
            .InsuranceClaims
            .Include(x => x.Client)
            .Include(x => x.RenderingProviders)
            .Include(x => x.ServiceLines.OrderBy(y => y.OrderIndex).ThenBy(y => y.Id))
                .ThenInclude(y => y.BillableItem)
            .Include(x => x.TaskClaims)
            .Include(x => x.ContactInsurancePolicy)
                .ThenInclude(x => x.ContactInsurancePolicy)
                    .ThenInclude(x => x.Payer)
            .Where(x => x.ProviderId == providerId);

        if (isAssignedOnly)
        {
            query = query
                .Join(dataContext.ContactAssignedStaff, claim => claim.ContactId, con => con.ContactId, (claim, con) => new { Claim = claim, AssignedContact = con })
                .Where(x => x.AssignedContact.PersonId == personId)
                .Select(x => x.Claim);
        }

        if (contactIds != null && contactIds.Length > 0)
        {
            query = query.Where(x => contactIds.Contains(x.ContactId));
        }

        if (staffIds != null && staffIds.Length > 0)
        {
            query = query.Where(x => x.RenderingProviders.Any(rp => rp.StaffPersonId.HasValue && staffIds.Contains(rp.StaffPersonId.Value)));
        }

        if (payerIds != null && payerIds.Length > 0)
        {
            query = query.Where(x => payerIds.Contains(x.ContactInsurancePolicy.PayerNumber));
        }

        if (statuses != null && statuses.Length > 0)
        {
            query = query.Where(x => statuses.Contains(x.Status));
        }

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(x => EF.Functions.ILike(x.Number, $"%{searchTerm}%"));
        }

        if (fromDate.HasValue || toDate.HasValue)
        {
            fromDate ??= DateOnly.MinValue;
            toDate ??= DateOnly.MaxValue;

            query = query.Where(x =>
                x.ToDate >= fromDate &&
                x.FromDate <= toDate
            );
        }

        if (sorting != null && sorting.HasSort)
        {
            // create new variable so ThenBy can be used
            IOrderedQueryable<InsuranceClaimDataModel> orderedQuery = query.OrderWith(KeySelector(sorting.Primary), sorting.Primary.Direction);

            if (sorting.Secondaries.Any())
            {
                foreach (var secondarySorting in sorting.Secondaries)
                {
                    orderedQuery = orderedQuery.ThenOrderWith(KeySelector(secondarySorting), secondarySorting.Direction);
                }
            }

            // set original query to an ordered query
            // add created date as sort field for ef to return consistent results for common fields
            query = orderedQuery.ThenOrderWith(x => x.CreatedDateTimeUtc, sorting.Primary.Direction);
        }

        var totalCount = await query.CountAsync();

        var claims = await query
            .AsNoTracking()
            .Skip(offset)
            .Take(limit)
            .ToArrayAsync();

        var hasMore = offset + claims.Length < totalCount;

        var paginatedResult = new PaginatedResult<InsuranceClaimListEntry>(
            mapper.Map<InsuranceClaimListEntry[]>(claims),
            new Pagination(limit, offset, hasMore),
            totalCount);

        return paginatedResult;
    }

    private Expression<Func<InsuranceClaimDataModel, object>> KeySelector(SortingField sortingField)
    {
        if (sortingField.MatchesField(nameof(InsuranceClaimDataModel.FromDate)))
        {
            return x => x.FromDate;
        }

        if (sortingField.MatchesField(nameof(InsuranceClaimDataModel.Number)))
        {
            return (x) => x.Number;
        }

        if (sortingField.MatchesField(nameof(InsuranceClaimDataModel.LastSubmittedDateTimeUtc)))
        {
            return (x) => x.LastSubmittedDateTimeUtc;
        }

        return (x) => sortingField.Field;
    }

    public async Task<InsuranceClaim> UpdateStatus(
        Guid id,
        Guid providerId,
        ClaimStatus status,
        string statusReason,
        CancellationToken cancellationToken = default
    )
    {
        if (id == Guid.Empty)
            return default;

        var model = await dataContext
            .InsuranceClaims
            .Where(x => x.Id == id && x.ProviderId == providerId)
            .FirstOrDefaultAsync(cancellationToken);

        if (model is null)
        {
            throw ValidationError.NotFound.AsException();
        }

        // Update general claim information status
        model.Status = status;
        model.StatusReason = statusReason;
        model.UpdatedDateTimeUtc = DateTime.UtcNow;

        await dataContext.SaveChangesAsync(cancellationToken);

        var result = mapper.Map<InsuranceClaim>(model);
        return result;
    }

    public async Task HardDelete(Guid providerId, Guid claimId)
    {
        var model = await dataContext
            .InsuranceClaims.IgnoreQueryFilters()
            .Where(x => x.ProviderId == providerId && x.Id == claimId)
            .FirstOrDefaultAsync();

        dataContext.HardDelete(model);

        await dataContext.SaveChangesAsync();
    }

    public async Task<InsuranceClaim[]> GetByIds(Guid providerId, Guid[] ids)
    {
        var claims = await dataContext
            .InsuranceClaims.Include(x => x.ServiceLines)
            .Where(x => x.ProviderId == providerId && ids.Contains(x.Id))
            .ProjectTo<InsuranceClaim>(mapper.ConfigurationProvider)
            .ToArrayAsync();
        return claims;
    }

    public async Task<InsuranceClaim[]> GetClaimsByServiceLines(
        Guid providerId,
        Guid[] serviceLineIds
    )
    {
        var claims = await dataContext.InsuranceClaims
            .Include(x => x.ServiceLines)
                .ThenInclude(x => x.BillableItem)
            .Where(x =>
                x.ProviderId == providerId && x.ServiceLines.Any(s => serviceLineIds.Contains(s.Id))
            )
            .ProjectTo<InsuranceClaim>(mapper.ConfigurationProvider)
            .ToArrayAsync();
        return claims;
    }

    public async Task<InsuranceClaim[]> UpdateStatus(InsuranceClaimStatusUpdateRequest[] claimUpdates, CancellationToken cancellationToken)
    {
        var claimIds = claimUpdates.Select(c => c.ClaimId).ToArray();
        var dbClaims = await dataContext.InsuranceClaims
            .Where(x => claimIds.Contains(x.Id))
            .ToListAsync(cancellationToken);
        foreach (var claim in dbClaims)
        {
            var claimUpdate = claimUpdates.First(c => c.ClaimId == claim.Id);
            claim.Status = claimUpdate.Status;
            claim.StatusReason = claimUpdate.StatusReason;
            claim.UpdatedDateTimeUtc = DateTime.UtcNow;
        }
        await dataContext.SaveChangesAsync(cancellationToken);
        return mapper.Map<InsuranceClaim[]>(dbClaims);
    }

    private async Task<string> GetLastNumber(Guid providerId, int range = 3)
    {
        var seedInvoiceNumber = await dataContext
            .InsuranceClaims.IgnoreQueryFilters()
            .Where(x => x.ProviderId == providerId)
            .OrderByDescending(i => i.CreatedDateTimeUtc)
            .Take(range)
            .GroupBy(x => x.Number.Length, x => x.Number)
            .OrderByDescending(x => x.Key)
            .Take(1)
            .Select(x => x.Max())
            .FirstOrDefaultAsync();
        return seedInvoiceNumber;
    }

    private static string Next(string value, int increment)
    {
        return Regex
            .Replace(
                value,
                "[0-9]+$",
                match => (long.Parse(match.Value) + increment).ToString($"D{match.Length}")
            )
            .PadLeft(6, '0');
    }

    public async Task<InsuranceClaim> GetById(Guid id, Guid providerId, CancellationToken cancellationToken, bool ignoreQueryFilters = false)
    {
        if (id == Guid.Empty)
            return null;
        var query = ignoreQueryFilters
            ? dataContext.InsuranceClaims.IgnoreQueryFilters()
            : dataContext.InsuranceClaims;
        query = query
            .Include(x => x.ServiceLines.OrderBy(y => y.OrderIndex).ThenBy(y => y.Id))
                .ThenInclude(x => x.BillableItem)
            .Where(x => x.Id == id && x.ProviderId == providerId);

        var model = await query
            .ProjectTo<InsuranceClaim>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(cancellationToken);
        if (model is null)
            return null;

        return model;
    }

    public async Task<bool> IsAssignedAsync(Guid personId, params Guid[] ids)
    {
        if (ids is null || ids.Length == 0) return true;

        if (personId.IsEmpty()) return false;

        return await dataContext.InsuranceClaims
            .Where(i => ids.Contains(i.Id))
            .Where(i => i.Client.Contact.AssignedStaff.Any(s => s.PersonId == personId))
            .CountAsync() == ids.Length;
    }

    public async Task<bool> IsProviderOwnedAsync(Guid providerId, params Guid[] ids)
    {
        if (ids == null || ids.Length == 0)
        {
            return false;
        }

        return await dataContext.InsuranceClaims
            .Where(x => x.ProviderId == providerId && ids.Contains(x.Id))
            .CountAsync() == ids.Distinct().Count();
    }

    public async Task UpdateServiceLines(Guid id, Guid providerId, ClaimServiceLine[] claimServiceLines)
    {
        var claim = await dataContext.InsuranceClaims
            .Include(x => x.ServiceLines)
            .FirstOrDefaultAsync(x => x.Id == id && x.ProviderId == providerId);
        if (claim is null) return;

        var totalAmount = claimServiceLines.Sum(x => x.Amount);
        claim.Amount = totalAmount;
        claim.UpdatedDateTimeUtc = DateTime.UtcNow;

        EntityUtilities.UpdateList(
            dataContext,
            claim.ServiceLines,
            claimServiceLines,
             (x, p) => x.Id == p.Id,
            x => new InsuranceClaimServiceLineDataModel
            {
                Id = x.Id,
                ProviderId = providerId,
                InsuranceClaimId = id,
                Date = x.Date,
                Code = x.Code,
                BillableItemId = x.BillableItemId,
                Units = x.Units,
                Amount = x.Amount,
                TaxAmount = x.TaxAmount,
                CurrencyCode = x.CurrencyCode,
                DiagnosticCodeReferences = x.DiagnosticCodeReferences,
                EPSDT = x.EPSDT,
                FamilyPlanningService = x.FamilyPlanningService,
                Description = x.Description,
                Detail = x.Detail,
                ServiceId = x.ServiceId,
                Modifiers = x.Modifiers,
                POSCode = x.POSCode,
                Emergency = x.Emergency,
                SupplementalInfo = x.SupplementalInfo,
                OrderIndex = x.OrderIndex
            }
        );

        await dataContext.SaveChangesAsync();
    }

}
