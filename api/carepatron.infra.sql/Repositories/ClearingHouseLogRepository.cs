using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using carepatron.core.Application.ClearingHouse.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Repositories.ClearingHouse;
using carepatron.infra.sql.Models.ClearingHouse;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace carepatron.infra.sql.Repositories;

public class ClearingHouseLogRepository(DataContext dataContext, IMapper mapper) : IClearingHouseLogRepository
{
    public async Task<ClearingHouseLog[]> BulkUpsert(ClearingHouseType clearingHouse, ClearingHouseLogType type, ClearingHouseLog[] models, CancellationToken cancellationToken)
    {
        if (models is null) throw new ArgumentNullException(nameof(models));

        var dataModels = mapper.Map<ClearingHouseLogDataModel[]>(models);
        var externalIds = dataModels.Select(m => m.ExternalId).ToHashSet();
        var now = DateTime.UtcNow;
        // Fetch existing records by ExternalId
        var existingDataModels = await dataContext.ClearingHouseLogs
            .Where(d => d.ClearingHouse == clearingHouse && d.Type == type && externalIds.Contains(d.ExternalId))
            .ToDictionaryAsync(d => d.ExternalId, cancellationToken);

        var newEntities = new List<ClearingHouseLogDataModel>();

        foreach (var dataModel in dataModels)
        {
            dataModel.UpdatedDateTimeUtc = now;

            if (existingDataModels.TryGetValue(dataModel.ExternalId, out var existing))
            {
                // Skip existing record if the status is not pending
                if (existing.Status != ClearingHouseLogStatus.Pending)
                {
                    Log.ForContext("Args", new
                    {
                        dataModel.ExternalId,
                        dataModel.ClearingHouse,
                        dataModel.Type,
                        dataModel.Status
                    }, true)
                    .Warning("Duplicate message already processed");
                    continue;
                }
                // Update existing record
                existing.Status = dataModel.Status;
                existing.RawResponse = dataModel.RawResponse;
                existing.UpdatedDateTimeUtc = dataModel.UpdatedDateTimeUtc;
            }
            else
            {
                dataModel.CreatedDateTimeUtc = now;
                // New entity to insert
                newEntities.Add(dataModel);
            }
        }

        if (newEntities.Count != 0)
        {
            await dataContext.ClearingHouseLogs.AddRangeAsync(newEntities, cancellationToken);
        }

        await dataContext.SaveChangesAsync(cancellationToken);

        // Return all updated and inserted records
        var resultDataModels = existingDataModels.Values.Concat(newEntities).ToArray();
        return mapper.Map<ClearingHouseLog[]>(resultDataModels);
    }
    
    public async Task<ClearingHouseLog> UpdateStatus(string externalId, ClearingHouseType clearingHouse, ClearingHouseLogType type, ClearingHouseLogStatus status, CancellationToken cancellationToken)
    {
        var dataModel = await dataContext.ClearingHouseLogs.FirstOrDefaultAsync(x => 
            x.ExternalId == externalId && 
            x.ClearingHouse == clearingHouse && 
            x.Type == type, cancellationToken);
        
        if (dataModel is null)
        {
            // If log record not found, log a warning and return.
            // It's not critical condition but something to investigate if we see these frequently in prod.
            Log
                .ForContext("Args", new
                {
                    externalId,
                    clearingHouse,
                    type
                }, true)
                .Warning("No clearing house log record found");
            return null;
        }
        
        dataModel.Status = status;
        dataModel.UpdatedDateTimeUtc = DateTime.UtcNow;
        
        await dataContext.SaveChangesAsync(cancellationToken);
        
        return mapper.Map<ClearingHouseLog>(dataModel);
    }
}
