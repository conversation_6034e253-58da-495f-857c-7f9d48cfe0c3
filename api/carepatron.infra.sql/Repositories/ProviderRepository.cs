﻿using System;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using carepatron.core.Application.Workspace.Billing.Models;
using carepatron.core.Application.Workspace.Locations.Models;
using carepatron.core.Application.Workspace.Preferences.Models;
using carepatron.core.Application.Workspace.Providers.Models;
using carepatron.core.Application.Workspace.Schemas.Models;
using carepatron.core.Models.Media;
using carepatron.core.Repositories.Provider;
using carepatron.infra.sql.Models.Media;
using carepatron.infra.sql.Models.Provider;
using Microsoft.EntityFrameworkCore;

namespace carepatron.infra.sql.Repositories;

public class ProviderRepository(DataContext dataContext, IMapper mapper) : IProviderRepository
{
    public async Task Create(Provider provider)
    {
        var model = mapper.Map<ProviderDataModel>(provider);
        model.LastAccessedUtc = DateTime.UtcNow;

        dataContext.Providers.Add(model);

        await dataContext.SaveChangesAsync();
    }

    public async Task UpdateSettings(ProviderSettings provider)
    {
        var model = await dataContext
            .Providers.Include(x => x.Logo)
            .FirstOrDefaultAsync(x => x.Id == provider.Id);

        var newModel = mapper.Map<ProviderDataModel>(provider);

        model.Address = newModel.Address;
        model.Name = newModel.Name;
        model.CountryCode = newModel.CountryCode;
        model.NationalProviderId = newModel.NationalProviderId;
        model.PhoneNumber = newModel.PhoneNumber;
        model.UpdatedDateTimeUtc = DateTime.UtcNow;
        model.Website = newModel.Website;
        model.PrimaryColorHex = newModel.PrimaryColorHex;
        await SaveLogo(provider.Logo, model, provider.Id);

        await dataContext.SaveChangesAsync();
    }

    public async Task<Provider> GetProvider(Guid id)
    {
        ProviderDataModel model = (
            await dataContext.Providers.Where(x => x.Id == id).Include(x => x.Logo).ToListAsync()
        ).FirstOrDefault();

        return mapper.Map<Provider>(model);
    }

    public async Task<ProviderSettings> GetProviderSettings(Guid id)
    {
        ProviderDataModel model = await dataContext
            .Providers.Include(x => x.Logo)
            .Include(x => x.Locations)
            .Include(x => x.ReminderSettings)
            .FirstOrDefaultAsync(x => x.Id == id);

        return mapper.Map<ProviderSettings>(model);
    }

    public async Task<Provider[]> GetProviders(Guid[] ids)
    {
        var models = await dataContext
            .Providers.Where(x => ids.Contains(x.Id))
            .Include(x => x.Logo)
            .ToListAsync();

        return mapper.Map<Provider[]>(models);
    }

    public async Task<Provider[]> GetProviders(Guid? providerId, int limit)
    {
        var query = dataContext.Providers.AsQueryable();

        if (providerId.HasValue)
            query = query.Where(x => x.Id.CompareTo(providerId.Value) > 0);

        var entities = await query.OrderBy(x => x.Id).Take(limit).ToArrayAsync();

        return mapper.Map<Provider[]>(entities);
    }

    public async Task<ProviderBillingSettings> GetBillingSettings(Guid providerId)
    {
        var model = await dataContext
            .ProviderBillingSettings.Include(x => x.Provider)
            .FirstOrDefaultAsync(x => x.ProviderId == providerId);

        if (model is null)
        {
            return null;
        }

        var result = mapper.Map<ProviderBillingSettings>(model);

        return result;
    }

    public async Task<ProviderBillingSettings> GetBillingSettingsByStripeId(string stripeAccountId)
    {
        var model = await dataContext
            .ProviderBillingSettings.Include(x => x.Provider)
            .FirstOrDefaultAsync(x => x.StripeAccountId == stripeAccountId);

        if (model is null)
        {
            return null;
        }

        var result = mapper.Map<ProviderBillingSettings>(model);

        return result;
    }

    public async Task SaveBillingSettings(ProviderBillingSettings billingSettings)
    {
        var model = await dataContext
            .ProviderBillingSettings.Include(x => x.Provider)
            .FirstOrDefaultAsync(x => x.ProviderId == billingSettings.ProviderId);

        var newModel = mapper.Map<ProviderBillingSettingsDataModel>(billingSettings);
        if (model is null)
        {
            newModel.Id = Guid.NewGuid();
            newModel.CreatedDateTimeUtc = DateTime.UtcNow;
            newModel.LastUpdatedDateTimeUtc = DateTime.UtcNow;
            await dataContext.AddAsync(newModel);
        }
        else
        {
            model.CurrencyCode = newModel.CurrencyCode;
            model.Address = newModel.Address;
            model.LastUpdatedDateTimeUtc = DateTime.UtcNow;
            model.TaxName = newModel.TaxName;
            model.TaxNumber = newModel.TaxNumber;
            model.IsClientChargedPaymentProcessingFee =
                newModel.IsClientChargedPaymentProcessingFee;
            model.ServiceReceiptAutomationCadence = newModel.ServiceReceiptAutomationCadence;
            model.StatementDescriptor = newModel.StatementDescriptor;
        }

        await dataContext.SaveChangesAsync();
    }

    public async Task SavePaymentProviderDetails(
        Guid providerId,
        PaymentAccountDetails paymentProviderAccountDetails
    )
    {
        var billingSettings = await dataContext
            .ProviderBillingSettings.Include(x => x.Provider)
            .FirstOrDefaultAsync(x => x.ProviderId == providerId);

        if (billingSettings == null)
        {
            throw new InvalidOperationException("No billing settings");
        }

        billingSettings.LastUpdatedDateTimeUtc = DateTime.UtcNow;
        billingSettings.StripeAccountId = paymentProviderAccountDetails.Id;
        billingSettings.IsOnboardingComplete = paymentProviderAccountDetails.IsOnboardingComplete;
        billingSettings.ChargesEnabled = paymentProviderAccountDetails.ChargesEnabled;
        billingSettings.PayoutsEnabled = paymentProviderAccountDetails.PayoutsEnabled;
        billingSettings.IsRejected = paymentProviderAccountDetails.IsRejected;
        billingSettings.StatementDescriptor = paymentProviderAccountDetails.StatementDescriptor;
        billingSettings.HasRequirementsDue = paymentProviderAccountDetails.HasRequirementsDue;

        await dataContext.SaveChangesAsync();
    }

    public async Task SaveStripeAccount(Guid providerId, string stripeAccountId)
    {
        var model = await dataContext.ProviderBillingSettings.FirstOrDefaultAsync(x =>
            x.ProviderId == providerId
        );

        if (model is null)
            return;

        model.StripeAccountId = stripeAccountId;
        model.LastUpdatedDateTimeUtc = DateTime.UtcNow;

        await dataContext.SaveChangesAsync();
    }

    private async Task SaveLogo(ProviderLogo logo, ProviderDataModel providerModel, Guid providerId)
    {
        var providerLogoModel = providerModel?.Logo;

        if (logo is null)
        {
            if (providerLogoModel is null)
                return;
            dataContext.ProviderLogo.Remove(providerLogoModel);
            providerModel.Logo = null;
            return;
        }

        if (logo.FileId == providerModel?.Logo?.FileId)
            return;

        var newProviderLogoModel = mapper.Map<ProviderLogoDataModel>(logo);
        var newMediaModel = new MediaDataModel()
        {
            Id = newProviderLogoModel.FileId,
            ContentType = newProviderLogoModel.ContentType,
            FileExtensions = newProviderLogoModel.FileExtension,
            FileName = newProviderLogoModel.FileName,
            FileSize = newProviderLogoModel.FileSize,
            Url = newProviderLogoModel.Url,
            MediaType = "Photo",
        };
        await dataContext.Medias.AddAsync(newMediaModel);

        if (providerLogoModel is null)
        {
            newProviderLogoModel.Id = Guid.NewGuid();
            newProviderLogoModel.FileId = newMediaModel.Id;
            newProviderLogoModel.ProviderId = providerId;
            newProviderLogoModel.CreatedDateTimeUtc = DateTime.UtcNow;
            newProviderLogoModel.LastUpdatedDateTimeUtc = DateTime.UtcNow;
            await dataContext.ProviderLogo.AddAsync(newProviderLogoModel);
        }
        else
        {
            providerLogoModel.FileId = newMediaModel.Id;
            providerLogoModel.Url = newProviderLogoModel.Url;
            providerLogoModel.ContentType = newProviderLogoModel.ContentType;
            providerLogoModel.FileExtension = newProviderLogoModel.FileExtension;
            providerLogoModel.FileName = newProviderLogoModel.FileName;
            providerLogoModel.FileSize = newProviderLogoModel.FileSize;
            providerLogoModel.LastUpdatedDateTimeUtc = DateTime.UtcNow;
        }
    }

    public async Task<ProviderLocation[]> GetProviderLocations(Guid providerId)
    {
        var locations = await dataContext
            .ProviderLocations.Where(x => x.ProviderId == providerId)
            .ToArrayAsync();

        return mapper.Map<ProviderLocation[]>(locations);
    }

    public async Task<ProviderLocation> GetProviderLocationById(Guid id)
    {
        var location = await dataContext
            .ProviderLocations.Where(x => x.Id == id)
            .FirstOrDefaultAsync();
        return mapper.Map<ProviderLocation>(location);
    }

    public async Task<ProviderLocation[]> GetProviderLocationByIds(
        Guid providerId,
        Guid[] locationIds
    )
    {
        var entities = await dataContext
            .ProviderLocations.Where(x => x.ProviderId == providerId && locationIds.Contains(x.Id))
            .ToArrayAsync();

        return mapper.Map<ProviderLocation[]>(entities);
    }

    public async Task<ProviderLocation> CreateProviderLocation(ProviderLocation providerLocation)
    {
        var model = mapper.Map<ProviderLocationDataModel>(providerLocation);
        model.CreatedDateTimeUtc = DateTime.UtcNow;
        model.UpdatedDateTimeUtc = DateTime.UtcNow;
        await dataContext.AddAsync(model);

        await dataContext.SaveChangesAsync();
        // changed return type from Task to Task<ProviderLocation> so we can apply the mapper config here
        return mapper.Map<ProviderLocation>(model);
    }

    public async Task<ProviderLocation> UpdateProviderLocation(ProviderLocation providerLocation)
    {
        var model = await dataContext.ProviderLocations.FirstOrDefaultAsync(x =>
            x.Id == providerLocation.Id && x.ProviderId == providerLocation.ProviderId
        );

        if (model is null)
        {
            return null;
        }

        model = mapper.Map(providerLocation, model);
        model.UpdatedDateTimeUtc = DateTime.UtcNow;
        await dataContext.SaveChangesAsync();
        // changed return type from Task to Task<ProviderLocation> so we can apply the mapper config here
        return mapper.Map<ProviderLocation>(model);
    }

    public async Task DeleteProviderLocation(Guid id, Guid providerId)
    {
        var model = await dataContext.ProviderLocations.FirstOrDefaultAsync(x =>
            x.Id == id && x.ProviderId == providerId
        );

        if (model == null)
            return;

        dataContext.ProviderLocations.Remove(model);
        await dataContext.SaveChangesAsync();
    }

    public async Task<CountryCurrency> GetCountryCurrency(string countryCode)
    {
        var dataModel = await dataContext.CountryCurrency.FirstOrDefaultAsync(x =>
            x.CountryCode.Equals(countryCode)
        );

        return mapper.Map<CountryCurrency>(dataModel);
    }

    public async Task<CountryCurrency> GetCountryCurrency(int id)
    {
        var dataModel = await dataContext.CountryCurrency.FirstOrDefaultAsync(x => x.Id == id);

        return mapper.Map<CountryCurrency>(dataModel);
    }

    public async Task SaveProviderContactFieldSettings(ProviderContactFieldSettings settings)
    {
        var newModel = mapper.Map<ProviderContactFieldSettingsDataModel>(settings);

        var existingModel = await dataContext.ProviderContactFieldSettings.FirstOrDefaultAsync(x =>
            x.ProviderId == settings.ProviderId
        );

        if (existingModel == null)
        {
            await dataContext.AddAsync(newModel);
        }
        else
        {
            existingModel.VisibleFields = newModel.VisibleFields;
            existingModel.Categories = newModel.Categories;
            existingModel.CategoryOrder = newModel.CategoryOrder;
        }

        await dataContext.SaveChangesAsync();
    }

    public async Task SaveProviderOnlineBookingOptions(ProviderOnlineBookingOptions options)
    {
        var existingModel = await dataContext.ProviderOnlineBookingOptions.FirstOrDefaultAsync(x =>
            x.ProviderId == options.ProviderId
        );

        if (existingModel is null)
        {
            var newModel = mapper.Map<ProviderOnlineBookingOptionsDataModel>(options);
            await dataContext.AddAsync(newModel);
        }
        else
            mapper.Map(options, existingModel);

        await dataContext.SaveChangesAsync();
    }

    public async Task Delete(Guid providerId, Guid deleteByPersonId, string deletedReason)
    {
        var existingModel = await dataContext.Providers.FirstOrDefaultAsync(x =>
            x.Id == providerId
        );

        existingModel.DeletedAtUtc = DateTime.UtcNow;
        existingModel.DeletedByPersonId = deleteByPersonId;
        existingModel.DeletedReason = deletedReason;

        await dataContext.SaveChangesAsync();
    }

    public async Task<ProviderContactFieldSettings> GetProviderContactFieldSettings(Guid id)
    {
        var model = await dataContext.ProviderContactFieldSettings.FirstOrDefaultAsync(x =>
            x.ProviderId == id
        );

        return mapper.Map<ProviderContactFieldSettings>(model);
    }

    public async Task<ProviderOnlineBookingOptions> GetProviderOnlineBookingOptions(Guid providerId)
    {
        var model = await dataContext.ProviderOnlineBookingOptions.FirstOrDefaultAsync(x =>
            x.ProviderId == providerId
        );

        return mapper.Map<ProviderOnlineBookingOptions>(model);
    }

    public async Task<ProviderContactFieldSettings[]> GetProviderContactFieldSettings(Guid[] ids)
    {
        var models = await dataContext
            .ProviderContactFieldSettings.Where(x => ids.Contains(x.ProviderId))
            .ToListAsync();

        return mapper.Map<ProviderContactFieldSettings[]>(models);
    }

    public async Task SaveWorkspacePreference(ProviderWorkspacePreference workspacePreference)
    {
        var entity = await dataContext
            .ProviderWorkspacePreferences.Where(x =>
                x.ProviderId == workspacePreference.ProviderId && x.Key == workspacePreference.Key
            )
            .FirstOrDefaultAsync();

        if (entity is null)
        {
            entity = mapper.Map<ProviderWorkspacePreferenceDataModel>(workspacePreference);
            await dataContext.ProviderWorkspacePreferences.AddAsync(entity);
        }
        else
        {
            entity.Preferences = workspacePreference.Preferences;
            dataContext.ProviderWorkspacePreferences.Update(entity);
        }

        await dataContext.SaveChangesAsync();
    }

    public async Task<ProviderWorkspacePreference[]> GetWorkspacePreferences(Guid providerId)
    {
        var entities = await dataContext
            .ProviderWorkspacePreferences.Where(x => x.ProviderId == providerId)
            .ToArrayAsync();

        return mapper.Map<ProviderWorkspacePreference[]>(entities);
    }

    public async Task CreateLogo(ProviderLogo logo)
    {
        await SaveLogo(logo, null, logo.ProviderId);
        await dataContext.SaveChangesAsync();
    }

    /// <summary>
    /// Updates the LastAccessedUtc property of a provider to the current UTC time.
    /// <remarks>This method is ignoring change tracking, so it will not track changes to the entity.</remarks> 
    /// </summary>
    /// <param name="providerId"></param>
    public async Task UpdateLastAccessedUtc(Guid providerId)
    {
        await dataContext.Providers.Where(x => x.Id == providerId)
            .ExecuteUpdateAsync(x => x.SetProperty(y => y.LastAccessedUtc, DateTime.UtcNow));
    }
}
