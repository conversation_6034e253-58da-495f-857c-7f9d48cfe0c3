using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Constants;
using carepatron.core.Models.Execution;
using carepatron.core.Repositories.Insurance;
using carepatron.infra.sql.Models.Insurance;
using carepatron.infra.sql.Utils;
using Microsoft.EntityFrameworkCore;
using Provider = carepatron.core.Application.Workspace.Providers.Models.Provider;

namespace carepatron.infra.sql.Repositories;

public class InsuranceClaimUSProfessionalsRepository(DataContext dataContext, IMapper mapper) : IInsuranceClaimUSProfessionalsRepository
{
    public async Task<InsuranceClaimUSProfessional> GetById(
        Guid id,
        Guid providerId,
        CancellationToken cancellationToken = default,
        bool ignoreQueryFilters = false
    )
    {
        return await GetClaimById(id, providerId, cancellationToken, ignoreQueryFilters);
    }

    public async Task<InsuranceClaimUSProfessional> GetById(
        Guid id,
        CancellationToken cancellationToken = default,
        bool ignoreQueryFilters = false
    )
    {
        return await GetClaimById(id, null, cancellationToken, ignoreQueryFilters);
    }

    public async Task<InsuranceClaimUSProfessional> Create(
        InsuranceClaimUSProfessional claim,
        CancellationToken cancellationToken = default
    )
    {
        var claimDetails = mapper.Map<InsuranceClaimUSProfessionalDataModel>(claim);
        claimDetails.CreatedDateTimeUtc = DateTime.UtcNow;
        claimDetails.UpdatedDateTimeUtc = DateTime.UtcNow;
        dataContext.Add(claimDetails);

        var claimHeader = mapper.Map<InsuranceClaimDataModel>(claim);
        claimHeader.CreatedDateTimeUtc = DateTime.UtcNow;
        claimHeader.UpdatedDateTimeUtc = DateTime.UtcNow;
        dataContext.Add(claimHeader);

        SetClaimTasksMapping(claimDetails, claim);

        await dataContext.SaveChangesAsync(cancellationToken);
        return claim;
    }

    public async Task<InsuranceClaimUSProfessional> Update(
        InsuranceClaimUSProfessional claim,
        CancellationToken cancellationToken = default
    )
    {
        var model = await dataContext.InsuranceClaimsUSProfessional
            .Include(x => x.Incident)
            .Include(x => x.ServiceFacility)
            .Include(x => x.DiagnosticCodes)
            .Include(x => x.ReferringProviders)
            .Include(x => x.BillingDetail)
                .ThenInclude(x => x.BillingProfile)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.Client)
                    .ThenInclude(x => x.Contact)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.ContactInsurancePolicy)
                    .ThenInclude(x => x.ContactInsurancePolicy)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.ServiceLines)
                    .ThenInclude(x => x.BillableItem)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.TaskClaims)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.RenderingProviders)
                    .ThenInclude(x => x.StaffMember)
            .FirstOrDefaultAsync(x => x.Id == claim.Id, cancellationToken);

        if (model == null)
        {
            throw new ExecutionException(
                new ValidationError(
                    Errors.NotFoundErrorCode,
                    Errors.NotFoundErrorDetails,
                    ValidationType.NotFound
                )
            );
        }

        model.OriginalReferenceNumber = claim.OriginalReferenceNumber;
        model.PatientsAccountNumber = claim.PatientsAccountNumber;
        model.PriorAuthorizationNumber = claim.PriorAuthorizationNumber;
        model.ResubmissionCode = claim.ResubmissionCode;
        model.Lab = claim.Lab;
        model.LabCharges = claim.LabCharges;
        model.AdditionalClaimInformation = claim.AdditionalClaimInformation;
        model.UpdatedDateTimeUtc = DateTime.UtcNow;

        // Set the export/print to be invalid so it will re-upload
        // updated filled-up claim form to S3 when user triggers export
        model.ExportId = claim.ExportId;
        model.IsExportValid = claim.IsExportValid;
        model.PrintId = claim.PrintId;
        model.IsPrintValid = claim.IsPrintValid;

        // Update claim tasks mapping
        SetClaimTasksMapping(model, claim);

        // Owned entities
        SetClaimHeader(model, claim);
        SetClientUpdates(model, claim.Client);
        SetIncidentUpdates(model, claim.Incident);
        SetBillingDetailUpdates(model, claim.BillingDetail);
        SetContactInsurancePolicyUpdates(model, claim.ContactInsurancePolicy);
        SetFacilityUpdates(model, claim.ServiceFacility);

        // Collections
        SetDiagnosticCodeUpdates(model, claim);
        SetServiceLineUpdates(model, claim);
        SetRenderingProviderUpdates(model, claim);
        SetReferringProviderUpdates(model, claim);

        await dataContext.SaveChangesAsync(cancellationToken);
        var result = mapper.Map<InsuranceClaimUSProfessional>(model);
        result.DiagnosticCodes = claim.DiagnosticCodes;
        result.ServiceLines = claim.ServiceLines;
        result.RenderingProviders = claim.RenderingProviders;
        result.ReferringProviders = claim.ReferringProviders;

        return result;
    }

    public async Task<InsuranceClaimUSProfessional[]> MoveClaimsToNewContact(
        Provider provider,
        Contact newContact,
        Guid[] fromContactIds
    )
    {
        var claimsToBeMoved = await dataContext
            .InsuranceClaimsUSProfessional
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.Client)
            .Where(x => x.ProviderId == provider.Id && fromContactIds.Contains(x.ClaimHeader.Client.ContactId))
            .ToListAsync();

        claimsToBeMoved.ForEach(claim =>
        {
            claim.UpdatedDateTimeUtc = DateTime.UtcNow;

            claim.ClaimHeader.ContactId = newContact.Id;
            claim.ClaimHeader.Client.ContactId = newContact.Id;
            claim.ClaimHeader.UpdatedDateTimeUtc = DateTime.UtcNow;
        });

        await dataContext.SaveChangesAsync();

        var mappedResult = mapper.Map<InsuranceClaimUSProfessional[]>(claimsToBeMoved);
        return mappedResult;
    }

    public async Task Delete(
        Guid id,
        Guid providerId,
        CancellationToken cancellationToken = default
    )
    {
        var claim = await dataContext.InsuranceClaimsUSProfessional
            .Include(x => x.ClaimHeader)
            .Where(x => x.Id == id && x.ProviderId == providerId)
            .FirstOrDefaultAsync(cancellationToken);
        if (claim is not null)
            dataContext.SoftDelete(claim);
        if (claim?.ClaimHeader is not null)
            dataContext.SoftDelete(claim.ClaimHeader);

        await dataContext.SaveChangesAsync(cancellationToken);
    }

    public async Task Restore(Guid providerId, Guid claimId)
    {
        var dataModel = await dataContext
            .InsuranceClaimsUSProfessional.IgnoreQueryFilters()
            .Include(s => s.ClaimHeader)
            .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Id == claimId);
        if (dataModel == null)
            return;

        dataModel.DeletedAtUtc = null;
        dataModel.ClaimHeader.DeletedAtUtc = null;

        await dataContext.SaveChangesAsync();
    }

    public async Task<InsuranceClaimUSProfessional[]> GetByIds(Guid providerId, Guid[] ids)
    {
        var claims = await dataContext
            .InsuranceClaimsUSProfessional
                .Include(x => x.ClaimHeader)
                    .ThenInclude(x => x.ServiceLines)
            .Where(x => x.ProviderId == providerId && ids.Contains(x.Id))
            .ToArrayAsync();
        return mapper.Map<InsuranceClaimUSProfessional[]>(claims);
    }

    private static void SetClaimHeader(
        InsuranceClaimUSProfessionalDataModel model,
        InsuranceClaimUSProfessional claim
    )
    {
        if (model.ClaimHeader is null)
            return;

        model.ClaimHeader.ClientControlNumber = claim.ClientControlNumber;
        model.ClaimHeader.ContactId = claim.ContactId;
        model.ClaimHeader.Status = claim.Status;
        model.ClaimHeader.SubmissionMethod = claim.SubmissionMethod;
        model.ClaimHeader.UpdatedDateTimeUtc = DateTime.UtcNow;
        model.ClaimHeader.LastSubmittedDateTimeUtc = claim.LastSubmittedDateTimeUtc;
        model.ClaimHeader.AmountPaid = claim.AmountPaid;
        model.ClaimHeader.FromDate = claim.FromDate;
        model.ClaimHeader.ToDate = claim.ToDate;
        model.ClaimHeader.Amount = claim.Amount;
    }

    private static void SetClientUpdates(
        InsuranceClaimUSProfessionalDataModel model,
        ClaimClient client
    )
    {
        model.ClaimHeader.Client ??= new InsuranceClaimClientDataModel();

        // Check if a client contact is empty
        // If empty, possibly the contact has been deleted
        if (client.Contact is not null)
            model.ClaimHeader.Client.ContactId = client.Contact.Id;

        model.ClaimHeader.Client.FirstName = client.FirstName;
        model.ClaimHeader.Client.MiddleName = client.MiddleName;
        model.ClaimHeader.Client.LastName = client.LastName;
        model.ClaimHeader.Client.DateOfBirth = client.DateOfBirth;
        model.ClaimHeader.Client.Address = client.Address;
        model.ClaimHeader.Client.Sex = client.Sex;
        model.ClaimHeader.Client.DateOfBirth = client.DateOfBirth;
        model.ClaimHeader.Client.PhoneNumber = client.PhoneNumber;
    }

    private static void SetIncidentUpdates(
        InsuranceClaimUSProfessionalDataModel model,
        ClaimIncident claimIncident
    )
    {
        if (claimIncident is null)
        {
            model.Incident = null;
            return;
        }

        model.Incident ??= new InsuranceClaimIncidentDataModel();
        model.Incident.IsConditionRelatedToEmployment =
            claimIncident.IsConditionRelatedToEmployment;
        model.Incident.IsConditionRelatedToAutoAccident =
            claimIncident.IsConditionRelatedToAutoAccident;
        model.Incident.IsConditionRelatedToOtherAccident =
            claimIncident.IsConditionRelatedToOtherAccident;
        model.Incident.AutoAccidentState = claimIncident.AutoAccidentState;
        model.Incident.CurrentIllnessDate = claimIncident.CurrentIllnessDate;
        model.Incident.CurrentIllnessQualifier = claimIncident.CurrentIllnessQualifier;
        model.Incident.OtherAssociatedDate = claimIncident.OtherAssociatedDate;
        model.Incident.OtherAssociatedQualifier = claimIncident.OtherAssociatedQualifier;
        model.Incident.UnableToWorkFrom = claimIncident.UnableToWorkFrom;
        model.Incident.UnableToWorkTo = claimIncident.UnableToWorkTo;
        model.Incident.HospitalizationFrom = claimIncident.HospitalizationFrom;
        model.Incident.HospitalizationTo = claimIncident.HospitalizationTo;
    }

    private static void SetContactInsurancePolicyUpdates(
        InsuranceClaimUSProfessionalDataModel model,
        ClaimContactInsurancePolicy claimContactInsurancePolicy
    )
    {
        if (claimContactInsurancePolicy is null)
        {
            model.ClaimHeader.ContactInsurancePolicy = null;
            return;
        }

        model.ClaimHeader.ContactInsurancePolicy ??= new InsuranceClaimContactInsurancePolicyDataModel();
        if (claimContactInsurancePolicy.ContactInsurancePolicy is not null)
        {
            model.ClaimHeader.ContactInsurancePolicy.ContactInsurancePolicyId = claimContactInsurancePolicy
                .ContactInsurancePolicy
                .Id;
        }
        model.ClaimHeader.ContactInsurancePolicy.PayerId = claimContactInsurancePolicy.PayerId;
        model.ClaimHeader.ContactInsurancePolicy.PayerNumber = claimContactInsurancePolicy.PayerNumber;
        model.ClaimHeader.ContactInsurancePolicy.PayerPhoneNumber =
            claimContactInsurancePolicy.PayerPhoneNumber;
        model.ClaimHeader.ContactInsurancePolicy.PayerName = claimContactInsurancePolicy.PayerName;
        model.ClaimHeader.ContactInsurancePolicy.InsuranceType = claimContactInsurancePolicy.InsuranceType;
        model.ClaimHeader.ContactInsurancePolicy.CoverageType = claimContactInsurancePolicy.CoverageType;
        model.ClaimHeader.ContactInsurancePolicy.Address = claimContactInsurancePolicy.Address;
        model.ClaimHeader.ContactInsurancePolicy.PolicyHolderRelationshipType =
            claimContactInsurancePolicy.PolicyHolderRelationshipType;
        model.ClaimHeader.ContactInsurancePolicy.PolicyHolderFirstName =
            claimContactInsurancePolicy.PolicyHolderFirstName;
        model.ClaimHeader.ContactInsurancePolicy.PolicyHolderMiddleName =
            claimContactInsurancePolicy.PolicyHolderMiddleName;
        model.ClaimHeader.ContactInsurancePolicy.PolicyHolderLastName =
            claimContactInsurancePolicy.PolicyHolderLastName;
        model.ClaimHeader.ContactInsurancePolicy.PolicyHolderDateOfBirth =
            claimContactInsurancePolicy.PolicyHolderDateOfBirth;
        model.ClaimHeader.ContactInsurancePolicy.PolicyHolderSex = claimContactInsurancePolicy.PolicyHolderSex;
        model.ClaimHeader.ContactInsurancePolicy.PolicyHolderPhoneNumber =
            claimContactInsurancePolicy.PolicyHolderPhoneNumber;
        model.ClaimHeader.ContactInsurancePolicy.PolicyHolderMemberId =
            claimContactInsurancePolicy.PolicyHolderMemberId;
        model.ClaimHeader.ContactInsurancePolicy.PolicyHolderGroupId =
            claimContactInsurancePolicy.PolicyHolderGroupId;
        model.ClaimHeader.ContactInsurancePolicy.OtherCoverageTypeName =
            claimContactInsurancePolicy.OtherCoverageTypeName;
        model.ClaimHeader.ContactInsurancePolicy.PolicyHolderAddress =
            claimContactInsurancePolicy.PolicyHolderAddress;
    }

    private static void SetFacilityUpdates(
        InsuranceClaimUSProfessionalDataModel model,
        ClaimFacility claimFacility
    )
    {
        if (claimFacility is null)
        {
            model.ServiceFacility = null;
            return;
        }
        model.ServiceFacility ??= new InsuranceClaimFacilityDataModel();
        model.ServiceFacility.Name = claimFacility.Name;
        model.ServiceFacility.ProviderLocationId = claimFacility.ProviderLocationId;
        model.ServiceFacility.PlaceOfService = claimFacility.PlaceOfService;
        model.ServiceFacility.Address = claimFacility.Address;
        model.ServiceFacility.NationalProviderId = claimFacility.NationalProviderId;
        model.ServiceFacility.OtherIdQualifier = claimFacility.OtherIdQualifier;
        model.ServiceFacility.OtherId = claimFacility.OtherId;
    }

    private static void SetBillingDetailUpdates(
        InsuranceClaimUSProfessionalDataModel model,
        ClaimBillingDetail claimBillingDetail
    )
    {
        if (claimBillingDetail is null)
        {
            model.BillingDetail = null;
            return;
        }
        model.BillingDetail ??= new InsuranceClaimBillingDetailDataModel();
        if (claimBillingDetail.BillingProfile is not null)
        {
            model.BillingDetail.BillingProfileId = claimBillingDetail.BillingProfile.Id;
        }
        model.BillingDetail.Name = claimBillingDetail.Name ?? claimBillingDetail.BillingProfile?.Name;
        model.BillingDetail.Type = claimBillingDetail.Type;
        model.BillingDetail.TaxNumberType = claimBillingDetail.TaxNumberType;
        model.BillingDetail.TaxNumber = claimBillingDetail.TaxNumber;
        model.BillingDetail.NationalProviderId = claimBillingDetail.NationalProviderId;
        model.BillingDetail.TaxonomyCode = claimBillingDetail.TaxonomyCode;
        model.BillingDetail.Address = claimBillingDetail.Address;
        model.BillingDetail.OtherIdQualifier = claimBillingDetail.OtherIdQualifier;
        model.BillingDetail.OtherId = claimBillingDetail.OtherId;
        model.BillingDetail.PhoneNumber = claimBillingDetail.PhoneNumber;
    }

    private void SetDiagnosticCodeUpdates(
        InsuranceClaimUSProfessionalDataModel model,
        InsuranceClaimUSProfessional claim
    )
    {
        var reqList = claim.DiagnosticCodes?.ToList() ?? [];
        var dbList = model.DiagnosticCodes ?? [];
        EntityUtilities.UpdateList(
            dataContext,
            dbList,
            reqList,
            (x, p) => x.Id == p.Id,
            x => new InsuranceClaimDiagnosticCodeDataModel
            {
                Id = x.Id,
                ProviderId = model.ProviderId,
                InsuranceClaimId = model.Id,
                Reference = x.Reference,
                Code = x.Code,
                Description = x.Description,
                OnsetDateFrom = x.OnsetDateFrom,
                OnsetDateTo = x.OnsetDateTo
            }
        );
    }

    private void SetServiceLineUpdates(
        InsuranceClaimUSProfessionalDataModel model,
        InsuranceClaimUSProfessional claim
    )
    {
        var reqList = claim.ServiceLines?.ToList() ?? [];
        var dbList = model.ClaimHeader.ServiceLines ?? [];
        EntityUtilities.UpdateList(
            dataContext,
            dbList,
            reqList,
            (x, p) => x.Id == p.Id,
            x => new InsuranceClaimServiceLineDataModel
            {
                Id = x.Id,
                ProviderId = model.ProviderId,
                InsuranceClaimId = model.Id,
                Date = x.Date,
                Code = x.Code,
                BillableItemId = x.BillableItemId,
                Units = x.Units,
                Amount = x.Amount,
                TaxAmount = x.TaxAmount,
                CurrencyCode = x.CurrencyCode,
                DiagnosticCodeReferences = x.DiagnosticCodeReferences,
                EPSDT = x.EPSDT,
                FamilyPlanningService = x.FamilyPlanningService,
                Description = x.Description,
                Detail = x.Detail,
                ServiceId = x.ServiceId,
                Modifiers = x.Modifiers,
                POSCode = x.POSCode,
                Emergency = x.Emergency,
                SupplementalInfo = x.SupplementalInfo,
                OrderIndex = x.OrderIndex
            }
        );
    }

    private void SetRenderingProviderUpdates(
        InsuranceClaimUSProfessionalDataModel model,
        InsuranceClaimUSProfessional claim
    )
    {
        var reqList = claim.RenderingProviders?.ToList() ?? [];
        var dbList = model.ClaimHeader.RenderingProviders ?? [];
        EntityUtilities.UpdateList(
            dataContext,
            dbList,
            reqList,
            (x, p) => x.Id == p.Id,
            x => new InsuranceClaimRenderingProviderDataModel
            {
                Id = x.Id,
                InsuranceClaimId = model.Id,
                StaffProviderId = model.ProviderId,
                ProviderId = model.ProviderId,
                StaffPersonId = x.StaffMember?.PersonId,
                FirstName = x.FirstName,
                MiddleName = x.MiddleName,
                LastName = x.LastName,
                NationalProviderId = x.NationalProviderId,
                OtherIdQualifier = x.OtherIdQualifier,
                OtherId = x.OtherId,
                OrderIndex = x.OrderIndex
            }
        );
    }

    private void SetReferringProviderUpdates(
        InsuranceClaimUSProfessionalDataModel model,
        InsuranceClaimUSProfessional claim
    )
    {
        var reqList = claim.ReferringProviders?.ToList() ?? [];
        var dbList = model.ReferringProviders ?? [];
        EntityUtilities.UpdateList(
            dataContext,
            dbList,
            reqList,
            (x, p) => x.Id == p.Id,
            x => new InsuranceClaimReferringProviderDataModel
            {
                Id = x.Id,
                InsuranceClaimId = model.Id,
                ProviderId = model.ProviderId,
                FirstName = x.FirstName,
                MiddleName = x.MiddleName,
                LastName = x.LastName,
                NationalProviderId = x.NationalProviderId,
                Qualifier = x.Qualifier,
                OtherIdQualifier = x.OtherIdQualifier,
                OtherId = x.OtherId,
                IncludeReferrerInformation = x.IncludeReferrerInformation,
            }
        );
    }

    private void SetClaimTasksMapping(
        InsuranceClaimUSProfessionalDataModel model,
        InsuranceClaimUSProfessional claim
    )
    {
        var reqList = claim.Tasks?.ToList() ?? [];
        var dbList = model.ClaimHeader.TaskClaims ?? [];
        EntityUtilities.UpdateList(
            dataContext,
            dbList,
            reqList,
            (x, p) => x.InsuranceClaimId == p.InsuranceClaimId && x.TaskId == p.TaskId,
            x => new InsuranceClaimTaskDataModel(model.Id, x.TaskId)
        );
    }

    private async Task<InsuranceClaimUSProfessional> GetClaimById(Guid id, Guid? providerId, CancellationToken cancellationToken, bool ignoreQueryFilters = false)
    {
        if (id == Guid.Empty)
            return null;
        var query = ignoreQueryFilters
            ? dataContext.InsuranceClaimsUSProfessional.IgnoreQueryFilters()
            : dataContext.InsuranceClaimsUSProfessional;
        query = query
            .AsSplitQuery()
            .Include(x => x.Incident)
            .Include(x => x.BillingDetail)
                .ThenInclude(x => x.BillingProfile)
            .Include(x => x.ServiceFacility)
            .Include(x => x.DiagnosticCodes)
            .Include(x => x.ReferringProviders)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.Client)
                    .ThenInclude(x => x.Contact)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.RenderingProviders.OrderBy(y => y.OrderIndex).ThenBy(y => y.Id))
                    .ThenInclude(x => x.StaffMember)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.TaskClaims)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.ContactInsurancePolicy)
                    .ThenInclude(x => x.ContactInsurancePolicy)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.ServiceLines.OrderBy(y => y.OrderIndex).ThenBy(y => y.Id))
           .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.ServiceLines)
                     .ThenInclude(x => x.BillableItem)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.ServiceLines)
                    .ThenInclude(x => x.Allocations)
            .Where(x => x.Id == id);

        if (providerId.HasValue)
        {
            query = query.Where(x => x.ProviderId == providerId.Value);
        }

        var model = await query.FirstOrDefaultAsync(cancellationToken);
        var result = mapper.Map<InsuranceClaimUSProfessional>(model);
        return result;
    }

    public async Task<InsuranceClaimUSProfessional[]> GetByContactLineItemIds(Guid contactId, Guid providerId, Guid[] lineItemIds, CancellationToken cancellationToken = default)
    {
        if (lineItemIds == null || lineItemIds.Length == 0) return [];

        var result = await dataContext.InsuranceClaimsUSProfessional
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.ServiceLines.OrderBy(y => y.OrderIndex).ThenBy(y => y.Id))
           .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.ServiceLines)
                     .ThenInclude(x => x.BillableItem)
           .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.ClearingHouseMetadata)
            .Include(x => x.ClaimHeader)
                .ThenInclude(x => x.ServiceLines)
                    .ThenInclude(x => x.Allocations)
            .Where(x => x.ProviderId == providerId
                && x.ClaimHeader != null
                && x.ClaimHeader.ContactId == contactId
                && x.ClaimHeader.ServiceLines != null
                && x.ClaimHeader.ServiceLines.Any(sl => lineItemIds.Contains(sl.Id))
            ).ToListAsync(cancellationToken);

        return mapper.Map<InsuranceClaimUSProfessional[]>(result);
    }
}
