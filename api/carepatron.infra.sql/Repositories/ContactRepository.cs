﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Abstractions;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Authorization.Models;
using carepatron.core.Authorization.ResourceNames;
using carepatron.core.Extensions;
using carepatron.core.Models.Common;
using carepatron.core.Models.Pagination;
using carepatron.core.Models.Permissions;
using carepatron.infra.sql.Extensions;
using carepatron.infra.sql.Models.Contact;
using carepatron.infra.sql.Models.Media;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.infra.sql.Repositories;

public class ContactRepository(DataContext dataContext, IMapper mapper) : IContactRepository, IActivityCheck
{
    public async Task<Contact> Create(Contact contact)
    {
        var model = mapper.Map<ContactDataModel>(contact);

        dataContext.Add(model);

        await dataContext.SaveChangesAsync();

        return contact;
    }

    public async Task<Contact[]> Create(Contact[] contacts)
    {
        var models = mapper.Map<ContactDataModel[]>(contacts);

        dataContext.AddRange(models);

        await dataContext.SaveChangesAsync();

        return contacts;
    }

    public async Task<Relationship> CreateRelationship(Relationship relationship)
    {
        var model = mapper.Map<ContactRelationshipDataModel>(relationship);

        dataContext.Add(model);

        await dataContext.SaveChangesAsync();

        return relationship;
    }

    public async Task<Relationship[]> CreateRelationships(Relationship[] relationships)
    {
        var models = mapper.Map<ContactRelationshipDataModel[]>(relationships);

        foreach (var model in models)
        {
            dataContext.Add(model);
        }

        await dataContext.SaveChangesAsync();

        return relationships;
    }

    public async Task Delete(Guid id)
    {
        var model = await dataContext.Contacts
            .FirstOrDefaultAsync(x => x.Id == id);

        if (model == null) return;

        dataContext.SoftDelete(model);

        await dataContext.SaveChangesAsync();
    }

    public async Task DeleteRelationship(Guid contactId, Guid toContactId)
    {
        var model = (await dataContext.ContactRelationships
                .Where(x => x.ContactId == contactId && x.ToContactId == toContactId)
                .ToListAsync())
            .FirstOrDefault();

        if (model == null) return;

        dataContext.Remove(model);

        await dataContext.SaveChangesAsync();
    }

    public async Task DeleteRelationships((Guid ContactId, Guid ToContactId)[] contactIdPairs)
    {
        foreach (var pair in contactIdPairs)
        {
            var model = (await dataContext.ContactRelationships
                    .Where(x => x.ContactId == pair.ContactId && x.ToContactId == pair.ToContactId)
                    .ToListAsync())
                .FirstOrDefault();

            if (model == null) continue;

            dataContext.Remove(model);
        }

        await dataContext.SaveChangesAsync();
    }

    public async Task<Contact> Get(Guid id)
    {
        return await Get(id, false);
    }


    public async Task<Contact> Get(Guid id, bool includeSoftDeleted)
    {
        var query = dataContext.Contacts.AsQueryable();
        if (includeSoftDeleted)
        {
            query = query.IgnoreQueryFilters();
        }

        var model = await
            query
            .Include(c => c.Emails)
            .Include(c => c.PhoneNumbers)
            .Include(c => c.Addresses)
            .Include(c => c.Languages)
            .Include(c => c.ProfilePhoto)
            .Include(c => c.AssignedStaff)
                .ThenInclude(s => s.Person)
                .ThenInclude(p => p.ProfilePhoto)
            .Include(c => c.Tags)
                .ThenInclude(t => t.Tag)
            .FirstOrDefaultAsync(x => x.Id == id);

        return mapper.Map<Contact>(model);
    }

    public async Task<Contact[]> Get(Guid[] ids)
    {
        var models = await dataContext.Contacts
            .Where(x => ids.Contains(x.Id))
            .OrderBy(x => x.FirstName)
            .ToListAsync();

        return mapper.Map<Contact[]>(models);
    }

    public async Task<Contact[]> Get(Guid providerId, Guid[] ids)
    {
        var models = await dataContext.Contacts
            .Where(x => x.ProviderId == providerId && ids.Contains(x.Id))
            .OrderBy(x => x.FirstName)
            .ToListAsync();

        return mapper.Map<Contact[]>(models);
    }

    public async Task<ContactPerson> GetContactPerson(Guid providerId, Guid contactId)
    {
        var contact = await dataContext.Contacts
            .Include(x => x.Person)
            .Where(x => x.ProviderId == providerId && x.Id == contactId)
            .FirstOrDefaultAsync();

        return mapper.Map<ContactPerson>(contact);
    }

    public async Task<Dictionary<Guid, Contact>> GetByIds(Guid providerId, params Guid[] contactIds)
    {
        var models = await dataContext.Contacts
            .Where(x => contactIds.Contains(x.Id) && x.ProviderId == providerId)
            .ToArrayAsync();
        return mapper.Map<Contact[]>(models).ToDictionary(x => x.Id);
    }
    public async Task<SimpleContact> GetReference(Guid id)
    {
        return await dataContext.Contacts
            .Where(x => x.Id == id)
            .ProjectTo<SimpleContact>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync();
    }

    public async Task<Contact> GetContactByPersonIdAndProviderId(Guid personId, Guid providerId)
    {
        var model = (await dataContext.Contacts
                .Where(x => x.PersonId == personId && x.ProviderId == providerId)
                .ToListAsync())
            .FirstOrDefault();

        return mapper.Map<Contact>(model);
    }

    public async Task<Guid[]> GetAssignedContactIds(Guid providerId, Guid staffPersonId)
    {
        return await dataContext.Contacts
            .Where(contact => contact.ProviderId == providerId)
            .Where(contact => contact.AssignedStaff
                .Any(staff => staff.PersonId == staffPersonId))
            .Select(c => c.Id)
            .ToArrayAsync();
    }

    public async Task<Dictionary<Guid, Guid[]>> GetAssignedContactIds(Guid providerId, Guid[] staffPersonIds)
    {
        return await dataContext.Contacts
            .Where(contact => contact.ProviderId == providerId)
            .Where(contact => contact.AssignedStaff
                .Any(staff => staffPersonIds.Contains(staff.PersonId)))
            .SelectMany(contact => contact.AssignedStaff
            .Where(staff => staffPersonIds.Contains(staff.PersonId))
                .Select(staff => new
                {
                    StaffId = staff.PersonId,
                    ContactId = contact.Id
                }))
            .GroupBy(x => x.StaffId)
            .ToDictionaryAsync(g => g.Key, g => g.Select(x => x.ContactId).ToArray());
    }

    public async Task<Contact[]> GetByEmail(Email email, Guid? providerId = null)
    {
        if (email == null) return Array.Empty<Contact>();

        var query = dataContext.Contacts
            .Where(x => x.Email == email);

        if (!providerId.IsNullOrEmpty())
        {
            query = query.Where(x => x.ProviderId == providerId && x.Email == email);
        }

        return mapper.Map<Contact[]>(await query.ToArrayAsync());
    }

    public async Task<Contact[]> GetByPersonId(Guid personId)
    {
        var models = await dataContext.Contacts
            .Where(x => x.PersonId == personId)
            .ToListAsync();

        return mapper.Map<Contact[]>(models);
    }

    public async Task<bool> IsClientOfProvider(Guid personId)
    {
        return await dataContext.Contacts
            .AnyAsync(x => x.PersonId == personId && x.IsClient);
    }

    public async Task<Contact> GetByPersonIdAndProviderId(Guid personId, Guid providerId)
    {
        var model = await dataContext.Contacts
            .Where(x => x.PersonId == personId && x.ProviderId == providerId)
            .FirstOrDefaultAsync();

        return mapper.Map<Contact>(model);
    }

    public async Task<Contact[]> GetByPersonIdAndProviderIds(Guid personId, Guid[] providerIds)
    {
        var model = await dataContext.Contacts
            .Where(x => x.PersonId == personId && providerIds.Contains(x.ProviderId))
            .ToListAsync();
        return mapper.Map<Contact[]>(model);
    }

    public async Task<PaginatedResult<Contact>> GetContacts(Guid providerId,
        string searchTerm,
        bool? isClient,
        Guid[] tags,
        Guid[] assignedStaff,
        bool isUnassigned,
        string[] statuses,
        bool isArchived,
        Guid currentPersonId,
        StandardPermission clientProfilesViewPermission,
        PaginationRequest paginationRequest,
        Sorting sorting)
    {
        IQueryable<ContactDataModel> query = GetContactsQuery(providerId, searchTerm, isClient, tags, assignedStaff, isUnassigned, statuses, currentPersonId, clientProfilesViewPermission, isArchived: isArchived);

        var models = await query
            .Include(x => x.Emails)
            .Include(x => x.PhoneNumbers)
            .Include(x => x.Addresses)
            .Include(x => x.AssignedStaff)
            .ThenInclude(x => x.Person)
            .SortBy(sorting)
            .Skip(paginationRequest.Offset)
            .Take(paginationRequest.Limit + 1)
            .ToListAsync();

        var totalCount = await query.CountAsync();
        var contacts = mapper.Map<Contact[]>(models.Take(paginationRequest.Limit));

        return new PaginatedResult<Contact>(contacts, new Pagination(contacts.Length, paginationRequest.Offset, models.Count() > paginationRequest.Limit), totalCount);
    }

    public async Task<Contact[]> GetContacts(
        Guid providerId,
        string searchTerm,
        Guid[] tag,
        Guid[] assignedStaff,
        bool isUnassigned,
        string[] statuses,
        bool isArchived,
        Guid currentPersonId,
        StandardPermission clientProfilesViewPermission,
        Guid[] excludedContactIds,
        bool? isClient)
    {
        var query = GetContactsQuery(providerId, searchTerm, isClient, tag, assignedStaff, isUnassigned, statuses, currentPersonId, clientProfilesViewPermission, excludedContactIds, isArchived);

        var contacts = await query.ToListAsync();

        return mapper.Map<Contact[]>(contacts);
    }

    public async Task<Contact[]> GetContacts(Guid providerId, Guid[] contactIds)
    {
        if (contactIds is null || !contactIds.Any())
            return Array.Empty<Contact>();

        var models = await dataContext.Contacts
            .Where(x => x.ProviderId == providerId && contactIds.Any(id => x.Id == id))
            .ToListAsync();

        return mapper.Map<Contact[]>(models);
    }

    public async Task<Relationship> GetRelationship(Guid contactId, Guid toContactId)
    {
        var model = (await dataContext.ContactRelationships.Where(x => x.ContactId == contactId && x.ToContactId == toContactId).ToListAsync()).FirstOrDefault();

        return mapper.Map<Relationship>(model);
    }

    public async Task<Relationship[]> GetRelationshipsForContact(Guid contactId)
    {
        var models = await dataContext.ContactRelationships.Where(x => x.ContactId == contactId).ToListAsync();

        return mapper.Map<Relationship[]>(models);
    }

    public async Task<Relationship[]> GetRelationshipsForContacts(Guid[] contactIds)
    {
        var models = await dataContext.ContactRelationships.Where(x => contactIds.Contains(x.ContactId)).ToListAsync();

        return mapper.Map<Relationship[]>(models);
    }

    public async Task<Relationship[]> GetRelationshipsToContact(Guid toContactId)
    {
        var models = await dataContext.ContactRelationships.Where(x => x.ToContactId == toContactId).ToListAsync();

        return mapper.Map<Relationship[]>(models);
    }

    public async Task<Relationship> Save(Relationship relationship)
    {
        var existingRelationship = (await dataContext.ContactRelationships.Where(x => x.ContactId == relationship.ContactId && x.ToContactId == relationship.ToContactId).ToListAsync()).FirstOrDefault();

        if (existingRelationship != null)
        {
            existingRelationship.UpdatedDateTimeUtc = DateTime.UtcNow;
            existingRelationship.EmergencyContact = relationship.EmergencyContact;
            existingRelationship.RelationshipAccessType = relationship.RelationshipAccessType;
            existingRelationship.RelationshipType = relationship.RelationshipType;
            existingRelationship.ReverseRelationshipType = relationship.ReverseRelationshipType;
        }
        else
        {
            var newModel = mapper.Map<ContactRelationshipDataModel>(relationship);

            dataContext.Add(newModel);
        }

        await dataContext.SaveChangesAsync();

        return relationship;
    }

    public async Task<Contact> Update(Contact contact)
    {
        var existingModel = await dataContext.Contacts.Include(x => x.Settings).FirstOrDefaultAsync(x => x.Id == contact.Id);

        if (existingModel == null)
        {
            throw new ArgumentException($"contact {contact.Id} does not exist");
        }

        existingModel.PersonId = contact.PersonId;
        existingModel.FirstName = contact.FirstName;
        existingModel.MiddleNames = contact.MiddleNames;
        existingModel.LastName = contact.LastName;
        existingModel.PreferredName = contact.PreferredName;
        existingModel.Email = contact.Email;
        existingModel.BirthDate = contact.BirthDate;
        existingModel.BusinessName = contact.BusinessName;
        existingModel.IdentificationNumber = contact.IdentificationNumber;
        existingModel.Gender = contact.Gender;
        existingModel.IsClient = contact.IsClient;
        existingModel.IsArchived = contact.IsArchived;
        existingModel.PhoneNumber = contact.PhoneNumber;
        existingModel.UpdatedDateTimeUtc = DateTime.UtcNow;
        existingModel.Ethnicity = contact.Ethnicity;
        existingModel.Status = contact.Status;
        existingModel.Occupation = contact.Occupation;
        existingModel.EmploymentStatus = contact.EmploymentStatus;
        existingModel.LivingArrangements = contact.LivingArrangements;
        existingModel.RelationshipStatus = contact.RelationshipStatus;
        existingModel.PreferredLanguage = contact.PreferredLanguage;
        existingModel.Sex = contact.Sex;
        existingModel.Fields = contact.Fields;

        existingModel.AssignedStaff = contact.AssignedStaff
            .EmptyIfNull()
            .Select(x => new ContactAssignedStaffDataModel { ContactId = contact.Id, PersonId = x.Id })
            .PreferExisingItems(existingModel.AssignedStaff, x => x.PersonId)
            .ToList();
        existingModel.Tags = contact.Tags
            .EmptyIfNull()
            .Select(x => new ContactTagDataModel(contact.Id, x.Id))
            .PreferExisingItems(existingModel.Tags, x => x.TagId)
            .ToList();

        if (contact.Settings is not null)
        {
            var contactSettings = contact.Settings;
            existingModel.Settings = new ContactSettingsDataModel
            {
                ContactId = existingModel.Id,
                TimeZone = contactSettings.TimeZone,
                NotificationMethod = new NotificationMethod()
                {
                    Email = contactSettings.NotificationMethod?.Email ?? false,
                    Sms = contactSettings.NotificationMethod?.Sms ?? false
                }
            };
        }

        // update profile photo if it has changed
        if (contact.ProfilePhoto != null && existingModel.ProfilePhotoId != contact.ProfilePhoto.Id)
        {
            var existingProfile = await dataContext.Medias.Where(x => x.Id == contact.ProfilePhoto.Id).FirstOrDefaultAsync();

            if (existingProfile == null)
            {
                var newProfile = mapper.Map<MediaDataModel>(contact.ProfilePhoto);
                existingModel.ProfilePhoto = newProfile;

                dataContext.Add<MediaDataModel>(newProfile);
            }
            else
            {
                existingModel.ProfilePhoto = mapper.Map<MediaDataModel>(contact.ProfilePhoto);
            }
        }

        if (contact.ProfilePhoto == null)
        {
            existingModel.ProfilePhoto = null;
        }

        if (contact.Addresses is not null)
        {
            var existingContactAddresses = await dataContext.ContactAddresses
                .Where(x => x.ContactId == contact.Id)
                .ToListAsync();

            // modify or delete existing addresses
            foreach (var existingAddress in existingContactAddresses)
            {
                var address = contact.Addresses.FirstOrDefault(x => x.Id == existingAddress.Id);
                if (address is not null)
                {
                    existingAddress.AddressDetails = address.AddressDetails;
                    existingAddress.Address = address.AddressDetails?.ToString();
                    existingAddress.IsPrimary = address.IsPrimary;
                    existingAddress.AddressType = address.Type;
                }
                else
                {
                    dataContext.Remove(existingAddress);
                }
            }

            // add new addresses
            foreach (var address in contact.Addresses)
            {
                var existingAddress = existingContactAddresses.FirstOrDefault(x => x.Id == address.Id);
                if (existingAddress is null)
                {
                    var newAddress = mapper.Map<ContactAddressDataModel>(address);
                    newAddress.Id = Guid.NewGuid();
                    newAddress.ContactId = contact.Id;
                    newAddress.ProviderId = contact.ProviderId;
                    dataContext.Add(newAddress);
                }
            }
        }

        if (contact.Emails is not null)
        {
            var existingContactEmails = await dataContext.ContactEmails
                .Where(x => x.ContactId == contact.Id)
                .ToListAsync();

            // modify or delete existing emails
            foreach (var existingEmail in existingContactEmails)
            {
                var email = contact.Emails.FirstOrDefault(x => x.Id == existingEmail.Id);
                if (email is not null)
                {
                    existingEmail.Email = email.Email;
                    existingEmail.IsPrimary = email.IsPrimary;
                    existingEmail.Type = email.Type;
                }
                else
                {
                    dataContext.Remove(existingEmail);
                }
            }

            // add new emails
            foreach (var email in contact.Emails)
            {
                var existingEmail = existingContactEmails.FirstOrDefault(x => x.Id == email.Id);
                if (existingEmail is null)
                {
                    var newEmail = mapper.Map<ContactEmailDataModel>(email);
                    newEmail.Id = Guid.NewGuid();
                    newEmail.ProviderId = contact.ProviderId;
                    newEmail.ContactId = contact.Id;
                    dataContext.Add(newEmail);
                }
            }
        }

        if (contact.PhoneNumbers is not null)
        {
            var existingContactPhones = await dataContext.ContactPhones
                .Where(x => x.ContactId == contact.Id)
                .ToListAsync();

            // modify or delete existing phones
            foreach (var existingPhone in existingContactPhones)
            {
                var phone = contact.PhoneNumbers.FirstOrDefault(x => x.Id == existingPhone.Id);
                if (phone is not null)
                {
                    existingPhone.PhoneNumber = phone.PhoneNumber;
                    existingPhone.IsPrimary = phone.IsPrimary;
                    existingPhone.Type = phone.Type;
                }
                else
                {
                    dataContext.Remove(existingPhone);
                }
            }

            // add new phones
            foreach (var phone in contact.PhoneNumbers)
            {
                var existingPhone = existingContactPhones.FirstOrDefault(x => x.Id == phone.Id);
                if (existingPhone is null)
                {
                    var newPhone = mapper.Map<ContactPhoneDataModel>(phone);
                    newPhone.Id = Guid.NewGuid();
                    newPhone.ProviderId = contact.ProviderId;
                    newPhone.ContactId = contact.Id;
                    dataContext.Add(newPhone);
                }
            }
        }

        if (contact.Languages is not null)
        {
            var existingContactLanguages = await dataContext.ContactLanguages
                .Where(x => x.ContactId == contact.Id)
                .ToListAsync();

            // modify or delete existing languages
            foreach (var existingLanguage in existingContactLanguages)
            {
                var language = contact.Languages.FirstOrDefault(x => x.Id == existingLanguage.Id);
                if (language is not null)
                {
                    existingLanguage.Language = language.Language;
                    existingLanguage.IsPrimary = language.IsPrimary;
                }
                else
                {
                    dataContext.Remove(existingLanguage);
                }
            }

            // add new languages
            foreach (var language in contact.Languages)
            {
                var existingLanguage = existingContactLanguages.FirstOrDefault(x => x.Id == language.Id);
                if (existingLanguage is null)
                {
                    var newLanguage = mapper.Map<ContactLanguageDataModel>(language);
                    newLanguage.Id = Guid.NewGuid();
                    newLanguage.ProviderId = contact.ProviderId;
                    newLanguage.ContactId = contact.Id;
                    dataContext.Add(newLanguage);
                }
            }
        }

        await dataContext.SaveChangesAsync();

        return contact;
    }

    public async Task<Contact> Update(RelationshipContactDetails contact)
    {
        var model = await dataContext.Contacts.FindAsync(contact.Id);

        if (model == null)
        {
            throw new ArgumentException($"contact {contact.Id} does not exist");
        }

        model.FirstName = contact.FirstName;
        model.MiddleNames = contact.MiddleNames;
        model.LastName = contact.LastName;
        model.Email = contact.Email;
        model.BirthDate = contact.BirthDate;
        model.BusinessName = contact.BusinessName;
        model.PhoneNumber = contact.PhoneNumber;
        model.Sex = contact.Sex;
        model.UpdatedDateTimeUtc = DateTime.UtcNow;

        // Update address if it's changed
        if (contact.Address != null && model.Addresses.FirstOrDefault(x => x.Address == contact.Address) == null)
        {
            model.Addresses = new List<ContactAddressDataModel>
            {
                { new ContactAddressDataModel { ContactId = model.Id, AddressType = AddressType.Residential, Address = contact.Address } }
            };
        }

        // Remove address if it no longer exists
        if (contact.Address == null && model.Addresses.Any())
        {
            model.Addresses = null;
        }

        await dataContext.SaveChangesAsync();
        return mapper.Map<Contact>(model);
    }

    public async Task BulkAssignStaffToContacts(Guid providerId, Guid[] contactIds, Guid[] newStaffIds)
    {
        if (contactIds == null || newStaffIds == null || !contactIds.Any() || !newStaffIds.Any())
        {
            return;
        }

        var query = dataContext.Contacts.Where(x => x.ProviderId == providerId && contactIds.Any(id => x.Id == id));

        await BulkAssignStaffToContacts(query, newStaffIds);
    }

    public async Task<Guid[]> BulkAssignStaffToContacts(
        Guid providerId,
        string searchTerm,
        Guid[] tag,
        Guid[] assignedStaff,
        bool isUnassigned,
        string[] statuses,
        Guid currentPersonId,
        StandardPermission clientProfilesViewPermission,
        Guid[] excludedContactIds,
        bool? isClient,
        Guid[] newStaffIds)
    {
        var query = GetContactsQuery(providerId, searchTerm, isClient, tag, assignedStaff, isUnassigned, statuses, currentPersonId, clientProfilesViewPermission, excludedContactIds);
        return await BulkAssignStaffToContacts(query, newStaffIds);
    }

    private async Task<Guid[]> BulkAssignStaffToContacts(IQueryable<ContactDataModel> query, Guid[] newStaffIds)
    {
        var contacts = await query.ToListAsync();

        foreach (var contact in contacts)
        {
            contact.AssignedStaff = newStaffIds
                // new staff
                .Select(staffPersonId => new ContactAssignedStaffDataModel { ContactId = contact.Id, PersonId = staffPersonId })
                // existing staff
                .Union(contact.AssignedStaff)
                // ensure we use the existing tracked staff
                .PreferExisingItems(contact.AssignedStaff, x => x.PersonId)
                .ToList();
        }

        await dataContext.SaveChangesAsync();

        return contacts.Select(c => c.Id).ToArray();
    }

    public async Task<Contact[]> BulkUnassignStaffToContacts(Guid[] contactIds, Guid[] staffIds)
    {
        if (contactIds == null || staffIds == null || !contactIds.Any() || !staffIds.Any())
        {
            return Array.Empty<Contact>();
        }

        var query = dataContext.Contacts.Where(x => contactIds.Any(id => x.Id == id));

        var contacts = await query.ToListAsync();

        foreach (var contact in contacts)
        {
            contact.AssignedStaff = contact.AssignedStaff
                .Where(x => !staffIds.Contains(x.PersonId))
                .ToList();
        }

        await dataContext.SaveChangesAsync();
        return mapper.Map<Contact[]>(contacts);
    }


    public async Task BulkAddTagsToContacts(Guid providerId, Guid[] contactIds, Guid[] newTagIds)
    {
        if (contactIds == null || newTagIds == null || !contactIds.Any() || !newTagIds.Any())
        {
            return;
        }

        var contacts = await dataContext.Contacts.Where(x => x.ProviderId == providerId && contactIds.Any(id => x.Id == id)).ToListAsync();

        AddTagsToContacts(newTagIds, contacts);

        await dataContext.SaveChangesAsync();
    }

    public async Task BulkAddTagsToContacts(
        Guid providerId,
        string searchTerm,
        bool? isClient,
        Guid[] tag,
        Guid[] assignedStaff,
        bool isUnassigned,
        string[] statuses,
        Guid currentPersonId,
        StandardPermission clientProfilesViewPermission,
        Guid[] excludedContactIds,
        Guid[] newTagIds)
    {
        var query = GetContactsQuery(providerId, searchTerm, isClient, tag, assignedStaff, isUnassigned, statuses, currentPersonId, clientProfilesViewPermission, excludedContactIds);

        var contacts = await query.ToListAsync();

        foreach (var contact in contacts)
        {
            AddTagsToContacts(newTagIds, contacts);
        }

        await dataContext.SaveChangesAsync();
    }

    private static void AddTagsToContacts(Guid[] newTagIds, List<ContactDataModel> contacts)
    {
        foreach (var contact in contacts)
        {
            contact.Tags = newTagIds
                // new tags
                .Select(tagId => new ContactTagDataModel { ContactId = contact.Id, TagId = tagId })
                // existing tags
                .Union(contact.Tags)
                // ensure we use the existing tracked tags
                .PreferExisingItems(contact.Tags, x => x.TagId)
                .ToList();
        }
    }

    public async Task BulkCreate(List<Contact> contactsToCreate)
    {
        var models = mapper.Map<ContactDataModel[]>(contactsToCreate);

        dataContext.AddRange(models);

        await dataContext.SaveChangesAsync();
    }

    public async Task BulkUpdateStatusOfContacts(Guid providerId, Guid[] contactIds, string newStatus)
    {
        if (contactIds == null || !contactIds.Any())
        {
            return;
        }

        var query = dataContext.Contacts.Where(x => x.ProviderId == providerId && contactIds.Contains(x.Id));

        await BulkUpdateStatusOfContacts(query, newStatus);
    }

    public async Task BulkUpdateStatusOfContacts(
        Guid providerId,
        string searchTerm,
        Guid[] tag,
        Guid[] assignedStaff,
        bool isUnassigned,
        string[] statuses,
        Guid currentPersonId,
        StandardPermission clientProfilesViewPermission,
        Guid[] excludedContactIds,
        bool? isClient,
        string newStatus)
    {
        var query = GetContactsQuery(providerId, searchTerm, isClient, tag, assignedStaff, isUnassigned, statuses, currentPersonId, clientProfilesViewPermission);

        if (excludedContactIds != null && excludedContactIds.Any())
        {
            query = query.Where(x => !excludedContactIds.Contains(x.Id));
        }

        await BulkUpdateStatusOfContacts(query, newStatus);
    }

    private async Task BulkUpdateStatusOfContacts(IQueryable<ContactDataModel> query, string newStatus)
    {
        await query.ExecuteUpdateAsync(setter => setter.SetProperty(x => x.Status, newStatus));
    }

    public async Task<Contact[]> BulkDelete(Guid providerId, Guid[] contactIds, bool hardDelete)
    {
        if (contactIds == null || !contactIds.Any())
        {
            return Array.Empty<Contact>();
        }

        var query = dataContext.Contacts
            .Where(x => x.ProviderId == providerId && contactIds.Contains(x.Id));

        var contacts = await BulkDelete(query, hardDelete);

        return mapper.Map<Contact[]>(contacts);
    }

    public async Task<Contact[]> BulkDelete(
        Guid providerId,
        string searchTerm,
        Guid[] tag,
        Guid[] assignedStaff,
        bool isUnassigned,
        string[] statuses,
        Guid currentPersonId,
        StandardPermission clientProfilesViewPermission,
        Guid[] excludedContactIds,
        bool? isClient,
        bool hardDelete)
    {
        var query = GetContactsQuery(providerId, searchTerm, isClient, tag, assignedStaff, isUnassigned, statuses, currentPersonId, clientProfilesViewPermission);

        if (excludedContactIds != null && excludedContactIds.Any())
        {
            query = query.Where(x => !excludedContactIds.Contains(x.Id));
        }

        var contacts = await BulkDelete(query, hardDelete);
        return mapper.Map<Contact[]>(contacts);
    }

    public async Task<Contact[]> BulkArchive(Guid providerId, Guid[] contactIds, bool isArchived)
    {
        var contacts = await dataContext.Contacts
            .Where(x => x.ProviderId == providerId && contactIds.Contains(x.Id))
            .ToArrayAsync();

        foreach (var contact in contacts)
            contact.IsArchived = isArchived;

        await dataContext.SaveChangesAsync();
        return mapper.Map<Contact[]>(contacts);
    }

    private async Task<ContactDataModel[]> BulkDelete(IQueryable<ContactDataModel> query, bool hardDelete)
    {
        var contacts = await query.ToArrayAsync();

        if (hardDelete)
        {
            dataContext.HardDeleteRange(contacts);
        }
        else
        {
            dataContext.SoftDeleteRange(contacts);
        }

        await dataContext.SaveChangesAsync();
        return contacts;
    }

    public async Task<ResourceName> GetResourceName(Guid id)
    {
        var values = await dataContext.Contacts
            .AsNoTracking()
            .Select(c => new
            {
                ProviderId = c.ProviderId,
                Id = c.Id
            })
            .FirstOrDefaultAsync(c => c.Id == id);

        if (values == null)
            return null;

        return new ResourceNameBuilder()
            .BuildContact(values.ProviderId, values.Id);
    }

    public async Task<StatusCountArggregate[]> AggregateContactStatusCount(Guid providerId, string[] statuses)
    {
        var entities = await dataContext.Contacts
            .Where(x => statuses.Contains(x.Status) && x.ProviderId == providerId)
            .GroupBy(x => x.Status)
            .Select(x => new StatusCountArggregate(x.Key, x.Count()))
            .ToArrayAsync();

        return entities;
    }

    public async Task UpdateContactsByStatus(string oldStatus, string newStatus, Guid providerId)
    {
        var entities = await dataContext.Contacts
            .Where(x => x.Status == oldStatus && x.ProviderId == providerId)
            .ToArrayAsync();

        foreach (var entity in entities)
            entity.Status = newStatus;

        await dataContext.SaveChangesAsync();
    }

    private IQueryable<ContactDataModel> GetContactsQuery(
        Guid providerId,
        string searchTerm,
        bool? isClient,
        Guid[] tags,
        Guid[] assignedStaff,
        bool isUnassigned,
        string[] statuses,
        Guid currentPersonId,
        StandardPermission clientProfilesViewPermission,
        Guid[] excludedContactIds = null,
        bool isArchived = false)
    {
        var query = dataContext.Contacts.Where(x => x.ProviderId == providerId);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var pattern = $"%{searchTerm}%";
            query = query.Where(x => EF.Functions.ILike(x.FirstName + " " + x.LastName, pattern) ||
                                     EF.Functions.ILike(x.IdentificationNumber, pattern) ||
                                     EF.Functions.ILike(x.PhoneNumber, pattern));
        }

        if (isClient.HasValue)
        {
            query = query.Where(x => x.IsClient == isClient.Value);
        }

        if (tags != null && tags.Any())
        {
            query = query.Where(contact => contact.Tags.Any(tag => tags.Any(tagId => tagId == tag.TagId)));
        }

        if (clientProfilesViewPermission == StandardPermission.Assigned)
        {
            query = query.Where(x => x.AssignedStaff.Any(y => y.PersonId == currentPersonId));
        }

        if ((assignedStaff != null && assignedStaff.Any()) || isUnassigned)
        {
            var hasAssignedStaff = assignedStaff?.Any() ?? false;

            query = query.Where(x => (hasAssignedStaff && x.AssignedStaff.Any(y => assignedStaff.Contains(y.PersonId))) || (isUnassigned && !x.AssignedStaff.Any()));
        }

        if (statuses != null && statuses.Any())
        {
            query = query.Where(contact => statuses.Contains(contact.Status));
        }

        if (excludedContactIds != null && excludedContactIds.Any())
        {
            query = query.Where(x => !excludedContactIds.Contains(x.Id));
        }

        if (!isArchived)
        {
            query = query.Where(x => !x.IsArchived);
        }

        return query;
    }

    public async Task<Contact[]> GetByEmailAddressOrPhoneNumber(Guid providerId, Email emailAddress, string phoneNumber)
    {
        if (string.IsNullOrEmpty(emailAddress) && string.IsNullOrEmpty(phoneNumber))
            return Array.Empty<Contact>();

        var models = await dataContext.Contacts
            .Where(x => x.ProviderId == providerId &&
                        (
                            (!string.IsNullOrEmpty(emailAddress) && x.Email == emailAddress) ||
                            (!string.IsNullOrEmpty(phoneNumber) && x.PhoneNumber == phoneNumber)
                        )
            )
            .ToArrayAsync();
        return mapper.Map<Contact[]>(models);
    }

    public async Task<Relationship[]> GetRelationshipsAndReverseRelationships(Guid contactId, Guid[] toContactIds)
    {
        var models = await dataContext.ContactRelationships.Where(x =>
            (x.ContactId == contactId && toContactIds.Contains(x.ToContactId))
            || (x.ToContactId == contactId && toContactIds.Contains(x.ContactId))
        ).ToListAsync();

        return mapper.Map<Relationship[]>(models);
    }

    public async Task<bool> IsActive(Guid providerId, DateRange dateRange)
    {
        if (providerId == Guid.Empty) return false;
        var count = await dataContext.Contacts
            .Where(x => x.ProviderId == providerId && x.CreatedDateTimeUtc >= dateRange.FromDate && x.CreatedDateTimeUtc <= dateRange.ToDate)
            .CountAsync();

        return count > 0;
    }

    public async Task Restore(Guid providerId, Guid contactId)
    {
        var model = await dataContext.Contacts
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Id == contactId);

        if (model == null)
        {
            return;
        }

        model.DeletedAtUtc = null;

        await dataContext.SaveChangesAsync();
    }

    public async Task HardDelete(Guid providerId, Guid contactId)
    {
        var model = await dataContext.Contacts
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Id == contactId);

        if (model == null)
        {
            return;
        }

        dataContext.HardDelete(model);

        await dataContext.SaveChangesAsync();
    }

    public async Task<CollectionResult<ContactDuplicate>> GetContactDuplicates(Guid providerId)
    {
        var duplicateContactsByEmail = await dataContext.DuplicateContacts
            .Where(x => x.ProviderId == providerId && !x.Contact.IsArchived)
            .Select(x => x.Contact)
            .GroupBy(x => x.Email)
            .Where(x => x.Key != null && x.Count() > 1)
            .Select(g => new { Email = g.Key, Duplicates = g.ToArray() })
            .ToArrayAsync();


        var results = duplicateContactsByEmail
            .Select(x => new ContactDuplicate()
            {
                Email = x.Email,
                Contacts = mapper.Map<Contact[]>(x.Duplicates)
            })
            .ToArray();

        return new CollectionResult<ContactDuplicate>(results);
    }

    public async Task Delete(Guid providerId, Guid[] ids)
    {
        var contacts = await dataContext.Contacts
            .Where(x => x.ProviderId == providerId && ids.Contains(x.Id))
            .ToListAsync();

        dataContext.SoftDeleteRange(contacts);

        await dataContext.SaveChangesAsync();
    }

    public async Task<DuplicateContact[]> GetPotentialDuplicatesByEmail(Guid providerId, Email email)
    {
        var models = await dataContext.Contacts
            .Where(x => x.ProviderId == providerId && x.Email == email && x.IsClient == true && x.IsArchived == false)
            .ToListAsync();

        return mapper.Map<DuplicateContact[]>(models);
    }

    public async Task<DuplicateContact[]> GetDuplicates(Guid providerId, Guid[] contactIds)
    {
        var models = await dataContext.DuplicateContacts
            .Where(x => x.ProviderId == providerId && contactIds.Contains(x.ContactId))
            .ToArrayAsync();

        return mapper.Map<DuplicateContact[]>(models);
    }

    public async Task AddDuplicates(DuplicateContact[] newDuplicateContacts)
    {
        var contactIds = newDuplicateContacts.Select(d => d.ContactId).ToArray();

        var existingDuplicateContacts = await dataContext.DuplicateContacts
            .Where(x => contactIds.Contains(x.ContactId))
            .ToArrayAsync();

        //do not add existing duplicates
        newDuplicateContacts = newDuplicateContacts
            .Where(x => !existingDuplicateContacts.Any(d => d.ContactId == x.ContactId))
            .ToArray();
        var models = mapper.Map<DuplicateContactDataModel[]>(newDuplicateContacts);

        dataContext.AddRange(models);

        await dataContext.SaveChangesAsync();
    }

    public async Task<DuplicateContact[]> GetDuplicatesByEmail(Guid providerId, Email email)
    {
        var models = await dataContext.DuplicateContacts
            .Where(x => x.ProviderId == providerId && x.Contact.Email == email)
            .ToArrayAsync();

        return mapper.Map<DuplicateContact[]>(models);
    }

    public async Task DeleteDuplicates(Guid providerId, DuplicateContact[] duplicatesToDelete)
    {
        //retrieve first before delete
        var models = await dataContext.DuplicateContacts
            .Where(x => x.ProviderId == providerId && duplicatesToDelete.Select(d => d.ContactId).Contains(x.ContactId))
            .ToArrayAsync();

        dataContext.RemoveRange(models);

        await dataContext.SaveChangesAsync();
    }

    public async Task<ContactSettings> GetContactSettings(Guid contactId)
    {
        var model = await dataContext.ContactSettings
            .FirstOrDefaultAsync(x => x.ContactId == contactId);

        return mapper.Map<ContactSettings>(model);
    }

    public async Task<Dictionary<Guid, ContactSettings>> GetContactSettings(Guid[] contactIds)
    {
        var models = await dataContext.ContactSettings
            .Where(x => contactIds.Contains(x.ContactId))
            .ToArrayAsync();

        return models.ToDictionary(x => x.ContactId, x => mapper.Map<ContactSettings>(x));
    }

    public async Task<ExternalContact[]> GetExternalContacts(Guid providerId, Email[] emails)
    {
        if (!emails.Any()) return Array.Empty<ExternalContact>();

        var models = await dataContext.ExternalContacts
            .Where(x => x.ProviderId == providerId && emails.Contains(x.Email))
            .ToArrayAsync();

        return mapper.Map<ExternalContact[]>(models);
    }

    public async Task<ExternalContact[]> GetExternalContactsByContactId(Guid providerId, Guid contactId)
    {
        var models = await dataContext.ExternalContacts
            .Where(x => x.ProviderId == providerId && x.ContactId == contactId)
            .ToArrayAsync();

        return mapper.Map<ExternalContact[]>(models);
    }

    public async Task<ExternalContact> GetExternalContact(Guid providerId, Guid id)
    {
        var model = await dataContext.ExternalContacts
            .Where(x => x.ProviderId == providerId && x.Id == id)
            .FirstOrDefaultAsync();
        
        return mapper.Map<ExternalContact>(model);
    }

    public async Task BulkCreateExternalContacts(ExternalContact[] externalContacts)
    {
        if (!externalContacts.Any()) return;
        
        var models = mapper.Map<ExternalContactDataModel[]>(externalContacts);
        await dataContext.ExternalContacts.AddRangeAsync(models);
        await dataContext.SaveChangesAsync();
    }

    public async Task LinkExternalContact(Guid externalContactId, Guid contactId)
    {
        var model = await dataContext.ExternalContacts
            .Where(x => x.Id == externalContactId)
            .FirstOrDefaultAsync();

        model.ContactId = contactId;
        
        await dataContext.SaveChangesAsync();
    }

    public async Task BulkUnlinkExternalContacts(Guid providerId, Guid[] externalContactIds)
    {
        if (!externalContactIds.Any()) return;

        await dataContext.ExternalContacts
            .Where(x => x.ProviderId == providerId && externalContactIds.Contains(x.Id))
            .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.ContactId, (Guid?)null));

        await dataContext.SaveChangesAsync();
    }

    public async Task<Contact[]> GetClientsByEmails(Guid providerId, Email[] emails, bool checkOtherEmails = false)
    {
        var query = dataContext.Contacts.Where(x => x.ProviderId == providerId && x.IsClient && !x.IsArchived);

        if (checkOtherEmails)
        {
            query = query
                .Include(x => x.Emails)
                .Where(x => emails.Contains(x.Email) || x.Emails.Any(e => emails.Contains(e.Email)));
        }
        else
        {
            query = query.Where(x => emails.Contains(x.Email));
        }

        var models = await query.ToListAsync();
        return mapper.Map<Contact[]>(models);
    }
}