using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Common;
using carepatron.core.Constants;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Invoices;
using carepatron.core.Models.Pagination;
using carepatron.core.Repositories.Payments;
using carepatron.infra.sql.Models.Invoices;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.infra.sql.Models.Payment;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Extensions;
using carepatron.infra.sql.Utils;
using carepatron.infra.sql.Models.Insurance;
using carepatron.core.Models.Payments.Models;
using carepatron.core.Common.Filters;
using carepatron.infra.sql.Extensions;
using carepatron.core.Repositories.Permissions;
using System.Linq.Expressions;
using carepatron.core.Application.Billables.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Payments;
using Payment = carepatron.core.Application.Payments.Models.Payment;

namespace carepatron.infra.sql.Repositories;

public class PaymentRepository(
    DataContext dataContext,
    IMapper mapper
) : IPaymentRepository, IPermissionRepository<Payment>
{
    public async Task<Payment> Create(Payment payment)
    {
        var dataModel = mapper.Map<PaymentDataModel>(payment);

        var now = DateTime.UtcNow;
        dataModel.CreatedDateTimeUtc = now;
        dataModel.LastUpdatedDateTimeUtc = now;

        if (payment.InvoiceId != null)
        {
            dataModel.InvoicePayment = new InvoicePaymentDataModel(payment.InvoiceId.Value, dataModel.Id);
        }

        var invoice = await dataContext.Invoices.FindAsync(payment.InvoiceId)
                      ?? await dataContext.Invoices
                          .Where(x => x.Id == payment.InvoiceId)
                          .Include(x => x.InvoiceLineItems)
                          .ThenInclude(x => x.Allocations)
                          .FirstAsync();

        await AllocatePayment(invoice, dataModel);
        await dataContext.AddAsync(dataModel);
        await dataContext.SaveChangesAsync();
        return mapper.Map<Payment>(dataModel);
    }

    public async Task<Payment> CreatePayment(Payment payment)
    {
        var dataModel = mapper.Map<PaymentDataModel>(payment);

        var now = DateTime.UtcNow;
        dataModel.CreatedDateTimeUtc = now;
        dataModel.LastUpdatedDateTimeUtc = now;

        if (payment.InvoiceId != null)
        {
            dataModel.InvoicePayment = new InvoicePaymentDataModel(payment.InvoiceId.Value, dataModel.Id);
            var invoiceDataModel = await dataContext.Invoices.FindAsync(payment.InvoiceId.Value);
            if (invoiceDataModel == null)
            {
                throw new ExecutionException(new ValidationError(Errors.InvoiceNotFoundCode, Errors.InvoiceNotFoundDetails, ValidationType.BadRequest));
            }

            invoiceDataModel.Status = InvoiceStatus.Paid;
            invoiceDataModel.PaymentDate = payment.PaymentDate;
        }

        var invoice = await dataContext.Invoices
            .Include(x => x.InvoiceLineItems)
            .Where(x => x.Id == payment.InvoiceId)
            .FirstAsync();

        await AllocatePayment(invoice, dataModel);
        await dataContext.AddAsync(dataModel);
        await dataContext.SaveChangesAsync();

        return mapper.Map<Payment>(dataModel);
    }

    public async Task<Payment> CreateInsurancePayment(Payment payment, Guid[] insuranceClaimIds = null)
    {
        var dataModel = mapper.Map<PaymentDataModel>(payment);

        var now = DateTime.UtcNow;
        dataModel.CreatedDateTimeUtc = now;
        dataModel.LastUpdatedDateTimeUtc = now;

        if (insuranceClaimIds != null && insuranceClaimIds.Length > 0)
        {
            dataModel.InsuranceClaimPayments =
            [
                .. insuranceClaimIds.Select(x => new InsuranceClaimPaymentDataModel
                {
                    Id = Guid.NewGuid(),
                    PaymentId = dataModel.Id,
                    InsuranceClaimId = x
                })
            ];
        }

        await dataContext.AddAsync(dataModel);
        await dataContext.SaveChangesAsync();

        return mapper.Map<Payment>(dataModel);
    }

    [Obsolete("Use GetPayment(Guid providerId, Guid id) instead")]
    public Task<Payment> GetPayment(Guid id)
    {
        return dataContext.Payments
            .AsNoTracking()
            .Where(x => x.Id == id)
            .ProjectTo<Payment>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync();
    }
    
    public Task<Payment> GetPayment(Guid providerId, Guid id, bool includeSoftDeleted = false)
    {
        return GetPaymentBaseQuery(providerId: providerId, id: id, includeSoftDeleted: includeSoftDeleted)
            .ProjectTo<Payment>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync();
    }

    public async Task<Payment> GetPaymentByIntentId(Guid id)
    {
        return await dataContext.Payments
            .Where(x => x.PaymentIntentId == id)
            .ProjectTo<Payment>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync();
    }

    public async Task DeletePayment(Guid providerId, Guid id)
    {
        var dataModel = await GetPaymentBaseQuery(providerId: providerId, id: id).FirstOrDefaultAsync();

        if (dataModel is null)
        {
            throw new ExecutionException(PaymentErrors.NotFound);
        }
        
        dataContext.SoftDelete(dataModel);

        await dataContext.SaveChangesAsync();
    }
    
    public async Task RestorePayment(Guid providerId, Guid id)
    {
        var paymentDataModel = await GetPaymentBaseQuery(providerId: providerId, id: id, includeSoftDeleted: true).FirstOrDefaultAsync();

        if (paymentDataModel is null)
        {
            throw new ExecutionException(PaymentErrors.NotFound);
        }

        paymentDataModel.DeletedAtUtc = null;
        
        await dataContext.SaveChangesAsync();
    }

    public async Task<Payment> CreatePaymentFromIntent(
        Guid paymentIntentId,
        string paymentStatus = StripePaymentIntentStatus.Succeeded,
        string paymentType = StripePaymentMethodTypes.Card)
    {
        var paymentIntentDataModel = await dataContext.PaymentIntents.FindAsync(paymentIntentId);
        if (paymentIntentDataModel == null)
        {
            throw new ExecutionException(new ValidationError(Errors.PaymentIntentNotFoundCode, Errors.PaymentIntentNotFoundDetail, ValidationType.BadRequest));
        }

        var now = DateTime.UtcNow;

        paymentIntentDataModel.Status = paymentStatus.ToPaymentIntentStatus();

        if (paymentIntentDataModel.Status != PaymentIntentStatus.Succeeded)
        {
            Log.Error("CreatePaymentFromIntent does not support payment intents with status '{status}'", paymentStatus);
            throw new NotSupportedException("Invalid paymentintent status");
        }

        var invoiceDataModel = await dataContext.Invoices.FindAsync(paymentIntentDataModel.InvoiceId);
        if (invoiceDataModel == null)
        {
            throw new ExecutionException(new ValidationError(Errors.InvoiceNotFoundCode, Errors.InvoiceNotFoundDetails, ValidationType.BadRequest));
        }

        var dataModel = new PaymentDataModel
        {
            Id = Guid.NewGuid(),
            Type = paymentType,
            Amount = paymentIntentDataModel.Amount,
            ChargeAmount = paymentIntentDataModel.ChargeAmount,
            TransferAmount = paymentIntentDataModel.TransferAmount,
            Fee = paymentIntentDataModel.Fee,
            IsClientChargedFee = paymentIntentDataModel.IsClientChargedFee,
            CurrencyCode = paymentIntentDataModel.CurrencyCode,
            PaymentProvider = PaymentProviders.Stripe,
            ContactId = paymentIntentDataModel.ContactId,
            ProviderId = paymentIntentDataModel.ProviderId,
            PaymentIntentId = paymentIntentDataModel.Id,
            CreatedDateTimeUtc = now,
            LastUpdatedDateTimeUtc = now,
            PaymentDate = now,
            PayerType = PayerType.SelfPay,
            IsBillingV2 = invoiceDataModel.IsBillingV2,
            PayoutStatus = PayoutStatus.Pending,
            Allocations = new List<PaymentAllocationDataModel>(),
        };

        // Update invoice
        dataModel.InvoicePayment = new InvoicePaymentDataModel(paymentIntentDataModel.InvoiceId, dataModel.Id);

        invoiceDataModel.Status = InvoiceStatus.Paid;
        invoiceDataModel.PaymentDate = now;
        await AllocatePayment(invoiceDataModel, dataModel);
        await dataContext.AddAsync(dataModel);
        await dataContext.SaveChangesAsync();
        return mapper.Map<Payment>(dataModel);
    }

    private async Task AllocatePayment(InvoiceDataModel invoice, PaymentDataModel payment)
    {
        // if credit invoice or invoice not use credit or invoice covered total by credit, just allocate payment like normal
        if (invoice.CreditsUsed == 0
            || (payment.ChargeAmount == invoice.CreditsUsed) // only a single payment to allocate
            || (invoice.TaxPrice + invoice.TaxExclusivePrice) <= 0
            || invoice.TaxPrice + invoice.TaxExclusivePrice == invoice.CreditsUsed)
        {
            AllocateItem(invoice, payment, payment.ChargeAmount - (payment.IsClientChargedFee ? payment.Fee : 0));
            return;
        }

        // not credit invoice, need to distribute payment and credit used 
        if (invoice.CreditsUsed != invoice.TaxPrice + invoice.TaxExclusivePrice)
        {
            // create payment intent and payment for credit used
            var creditPaymentIntent = new PaymentIntentDataModel
            {
                Id = Guid.NewGuid(),
                ProviderId = payment.ProviderId,
                ContactId = payment.ContactId,
                InvoiceId = invoice.Id,
                Amount = CurrencyHandler.Get(invoice.CurrencyCode).ToStripeAmount(invoice.CreditsUsed),
                AmountDue = invoice.CreditsUsed,
                ChargeAmount = invoice.CreditsUsed,
                TransferAmount = invoice.CreditsUsed,
                Fee = 0,
                IsClientChargedFee = false,
                CurrencyCode = invoice.CurrencyCode,
                PaymentMethodType = null,
                Status = PaymentIntentStatus.Succeeded,
                CreatedDateTimeUtc = DateTime.UtcNow,
            };
            await dataContext.AddAsync(creditPaymentIntent);

            var paymentId = Guid.NewGuid();
            var creditPayment = new PaymentDataModel
            {
                Id = paymentId,
                ContactId = payment.ContactId,
                ProviderId = payment.ProviderId,
                Type = PaymentTypes.CreditBalance,
                Amount = creditPaymentIntent.Amount,
                ChargeAmount = creditPaymentIntent.ChargeAmount,
                TransferAmount = creditPaymentIntent.TransferAmount,
                Fee = 0,
                IsClientChargedFee = false,
                CurrencyCode = invoice.CurrencyCode,
                PaymentProvider = PaymentProviders.CustomerBalance,
                PaymentIntentId = creditPaymentIntent.Id,
                CreatedDateTimeUtc = DateTime.UtcNow,
                LastUpdatedDateTimeUtc = DateTime.UtcNow,
                PaymentDate = DateTime.UtcNow,
                IsBillingV2 = invoice.IsBillingV2,
                PayerType = PayerType.SelfPay,
                InvoicePayment = new InvoicePaymentDataModel(invoice.Id, paymentId),
                Allocations = new List<PaymentAllocationDataModel>(),
            };
            AllocateItem(invoice, creditPayment, invoice.CreditsUsed);
            dataContext.Add(creditPayment);
            var chargeAmount = payment.ChargeAmount - (payment.IsClientChargedFee ? payment.Fee : 0);
            AllocateItem(invoice, payment, chargeAmount);
        }
    }

    private static void AllocateItem(InvoiceDataModel invoice, PaymentDataModel dataModel, decimal availableToAllocate)
    {
        if (dataModel.IsBillingV2 != true)
        {
            Log.ForContext("Allocation", new
            {
                InvoiceId = invoice.Id,
                PaymentId = dataModel.Id
            }, true)
            .Information("Skipping allocation for v1 payment");
            return;
        }

        var isCreditPayment = dataModel.ChargeAmount <= 0;

        // need to sort line item by price so the allocation is not cut off early
        // ex. if payment is -100, line items 80, -80, -100, allocation will be until the last item
        var sortedLineItems = isCreditPayment
            ? invoice.InvoiceLineItems.OrderByDescending(x => x.Price).ToList()
            : invoice.InvoiceLineItems.OrderBy(x => x.Price).ToList();

        foreach (var lineItem in sortedLineItems)
        {
            if (!lineItem.BillableItemId.HasValue)
            {
                continue;
            }

            if (availableToAllocate == 0 && dataModel.ChargeAmount != 0)
            {
                break;
            }

            var lineItemTotal = CurrencyHandler.Get(lineItem.CurrencyCode).Round(lineItem.Total);
            var amountToAllocate = lineItemTotal - lineItem.Allocations.EmptyIfNull().Sum(a => a.Amount);
            if (amountToAllocate == 0)
            {
                continue;
            }

            var allocationAmount = isCreditPayment
                ? Math.Max(amountToAllocate, availableToAllocate)
                : Math.Min(amountToAllocate, availableToAllocate);

            var newAllocation = new PaymentAllocationDataModel
            {
                Id = Guid.NewGuid(),
                PaymentId = dataModel.Id,
                BillableItemId = lineItem.BillableItemId.Value,
                ProviderId = dataModel.ProviderId,
                ContactId = dataModel.ContactId,
                Amount = allocationAmount,
                ClaimLineId = null,
                InvoiceLineItemId = lineItem.Id,
            };
            dataModel.Allocations.Add(newAllocation);
            availableToAllocate -= newAllocation.Amount;
        }
    }

    public async Task UpdatePaymentPayout(Payment payment)
    {
        var entity = await dataContext.Payments
            .Where(x => x.Id == payment.Id)
            .FirstOrDefaultAsync();

        if (entity is null) return;

        entity.PayoutStatus = payment.PayoutStatus;
        entity.PayoutDateUtc = payment.PayoutDateUtc;
        entity.PayoutCurrencyCode = payment.PayoutCurrencyCode;
        entity.PayoutAmount = payment.PayoutAmount;

        await dataContext.SaveChangesAsync();
    }

    public async Task UpdatePaymentDetails(Payment payment)
    {
        var entity = await dataContext.Payments
            .Where(x => x.Id == payment.Id)
            .FirstOrDefaultAsync() ?? throw ValidationError.NotFound.AsException();

        entity.PayerName = payment.PayerName;
        entity.PaymentDate = payment.PaymentDate;
        entity.Amount = payment.Amount;
        entity.ChargeAmount = payment.ChargeAmount;
        entity.Reference = payment.Reference;

        await dataContext.SaveChangesAsync();
    }

    public async Task<List<(string currencyCode, decimal totalAmountDue)>> GetOnlinePaymentTotals(Guid providerId)
    {
        var result = await dataContext.Payments
            .Where(x => x.ProviderId == providerId
                        && x.PaymentProvider == PaymentProviders.Stripe)
            .GroupBy(x => x.CurrencyCode)
            .Select(g => new Tuple<string, decimal>(g.Key, g.Sum(x => x.PaymentIntent.AmountDue)).ToValueTuple())
            .ToListAsync();

        return result;
    }

    public async Task<PaymentIntent> CreatePaymentIntent(PaymentIntent paymentIntent)
    {
        var dataModel = mapper.Map<PaymentIntentDataModel>(paymentIntent);

        dataModel.CreatedDateTimeUtc = DateTime.UtcNow;

        await dataContext.AddAsync(dataModel);
        await dataContext.SaveChangesAsync();

        return mapper.Map<PaymentIntent>(dataModel);
    }

    public async Task<PaymentIntent> UpdatePaymentIntent(PaymentIntent paymentIntent)
    {
        var model = await dataContext.PaymentIntents.FindAsync(paymentIntent.Id);

        model.Amount = paymentIntent.Amount;
        model.AmountDue = paymentIntent.AmountDue;
        model.ChargeAmount = paymentIntent.ChargeAmount;
        model.TransferAmount = paymentIntent.TransferAmount;
        model.Fee = paymentIntent.Fee;
        model.IsClientChargedFee = paymentIntent.IsClientChargedFee;
        model.CurrencyCode = paymentIntent.CurrencyCode;
        model.PaymentMethodType = paymentIntent.PaymentMethodType;
        model.ContactId = paymentIntent.ContactId;
        model.Status = paymentIntent.Status;
        model.ClientSecret = paymentIntent.ClientSecret;
        model.StripePaymentIntentId = paymentIntent.StripePaymentIntentId;
        model.SavePaymentMethodOptions = paymentIntent.SavePaymentMethodOptions;
        model.FailureReason = paymentIntent.FailureReason;

        await dataContext.SaveChangesAsync();

        return mapper.Map<PaymentIntent>(model);
    }

    public async Task<PaymentIntent> GetPaymentIntent(Guid id)
    {
        var dataModel = await dataContext.PaymentIntents
            .FirstOrDefaultAsync(x => x.Id == id && x.Status != PaymentIntentStatus.Failed);

        return mapper.Map<PaymentIntent>(dataModel);
    }

    public async Task<PaymentIntent> GetPaymentIntentByInvoice(Guid invoiceId)
    {
        // TODO: The unique index ix_payment_intents_invoice_id prevents creating a new payment intent
        // if the invoice has an existing payment intent with status Canceled. Check if this is correct.
        var dataModel = await dataContext.PaymentIntents
            .Where(x => x.InvoiceId == invoiceId
                        && x.Status != PaymentIntentStatus.Failed
                        && x.Status != PaymentIntentStatus.Canceled)
            .OrderByDescending(x => x.CreatedDateTimeUtc)
            .FirstOrDefaultAsync();

        if (dataModel == null) return null;

        return mapper.Map<PaymentIntent>(dataModel);
    }

    public async Task<PaymentIntent> GetPaymentIntentByStripePaymentIntentId(string id, bool includeFailed = false)
    {
        var dataModel = dataContext.PaymentIntents
            .Where(x => x.StripePaymentIntentId == id);
        if (!includeFailed)
        {
            dataModel = dataModel.Where(x => x.Status != PaymentIntentStatus.Failed);
        }

        var result = await dataModel.FirstOrDefaultAsync();
        return mapper.Map<PaymentIntent>(result);
    }

    public async Task DeletePaymentIntent(Guid paymentIntentId)
    {
        var paymentIntent = await dataContext.PaymentIntents
            .Where(x => x.Id == paymentIntentId)
            .FirstOrDefaultAsync();

        if (paymentIntent is null) return;

        dataContext.Remove(paymentIntent);
        await dataContext.SaveChangesAsync();
    }

    public async Task SetPaymentFailureByPaymentIntentId(Guid paymentIntentId, string reason,
        CancellationToken cancellationToken)
    {
        var paymentIntent = await dataContext.PaymentIntents
            .Include(paymentIntentDataModel => paymentIntentDataModel.Invoice)
            .ThenInclude(invoiceDataModel => invoiceDataModel.InvoicePayment)
            .Include(paymentIntentDataModel => paymentIntentDataModel.Payment)
            .FirstAsync(x => x.Id == paymentIntentId, cancellationToken: cancellationToken);

        paymentIntent.Status = PaymentIntentStatus.Failed;
        paymentIntent.FailureReason = reason;
        paymentIntent.Invoice.Status = InvoiceStatus.Unpaid;
        paymentIntent.Invoice.PaymentDate = null;

        dataContext.InvoicePayments.RemoveRange(paymentIntent.Invoice.InvoicePayment);
        dataContext.Update(paymentIntent);

        await dataContext.SaveChangesAsync(cancellationToken);
    }

    public async Task<decimal> GetTotalInvoicePayment(Guid invoiceId, string currencyCode)
    {
        var query = dataContext.InvoicePayments
            .Where(x => x.InvoiceId == invoiceId)
            .Include(x => x.Payment)
            .GroupBy(x => 1)
            .Select(x => x.Sum(y => y.Payment.Amount));

        var result = await query.FirstOrDefaultAsync();

        return CurrencyHandler.Get(currencyCode).FromStripeAmount((long)result);
    }

    public async Task UpdatePaymentRefundStatus(Guid paymentId, PaymentRefundStatus? status)
    {
        var payment = await dataContext.Payments.FindAsync(paymentId);
        payment.RefundStatus = status;
        await dataContext.SaveChangesAsync();
    }

    public async Task CreateAllocation(PaymentAllocation allocation)
    {
        var model = mapper.Map<PaymentAllocationDataModel>(allocation);
        await dataContext.AddAsync(model);
        await dataContext.SaveChangesAsync();
    }

    public async Task DeleteAllocationByInvoiceLineItemId(Guid providerId, Guid contactId, params Guid[] invoiceLineItemIds)
    {
        var allocations = await dataContext.PaymentAllocations
            .Where(x => x.ProviderId == providerId && x.ContactId == contactId && invoiceLineItemIds.Contains(x.InvoiceLineItemId.Value))
            .ToListAsync();

        dataContext.RemoveRange(allocations);
        await dataContext.SaveChangesAsync();
    }

    public async Task DeleteAllocation(Guid providerId, Guid contactId, Guid paymentId, Guid billableItemId)
    {
        var allocation = await dataContext.PaymentAllocations
            .Where(x => x.PaymentId == paymentId && x.BillableItemId == billableItemId)
            .FirstOrDefaultAsync();

        if (allocation is null) return;

        dataContext.Remove(allocation);
        await dataContext.SaveChangesAsync();
    }

    public async Task<PaginatedResult<ContactPayment>> GetPaymentEntries(Guid providerId,
        Guid contactId,
        string[] paymentProviders,
        bool isUnallocated,
        string? searchTerm,
        DateTime? fromDate,
        DateTime? toDate,
        int offset,
        int limit,
        CancellationToken cancellationToken)
    {
        var query = dataContext.Payments
            .AsNoTracking()
            .Include(x => x.CreditAdjustments)
            .Include(x => x.Allocations)
            .ThenInclude(y => y.InvoiceLineItem)
            .ThenInclude(z => z.Invoice)
            .Where(x => x.ProviderId == providerId && x.ContactId == contactId)
            .Where(x =>
                (!fromDate.HasValue || x.PaymentDate >= fromDate) &&
                (!toDate.HasValue || x.PaymentDate <= toDate)
            );

        if (paymentProviders != null && paymentProviders.Length > 0)
        {
            query = query.Where(x => paymentProviders.Contains(x.PaymentProvider));
        }

        if (isUnallocated)
        {
            query = query.Where(x => x.IsBillingV2 == true && ((x.Allocations.Sum(y => y.Amount)
                                      + x.Refunds.Where(r => r.ReducePaymentAllocation).Sum(r => r.Amount)
                                      + x.CreditAdjustments.Sum(y => y.Amount)
                ) < (x.ChargeAmount - (x.IsClientChargedFee ? x.Fee : 0))));
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(x => EF.Functions.ILike(x.Reference, $"%{searchTerm}%"));
        }

        var total = await query
            .CountAsync(cancellationToken);

        var payments = await query
            .OrderByDescending(x => x.PaymentDate)
            .Skip(offset)
            .Take(limit)
            .ToArrayAsync(cancellationToken);

        var paymentEntries = mapper.Map<ContactPayment[]>(payments);
        var hasMore = offset + payments.Length < total;

        return new PaginatedResult<ContactPayment>(paymentEntries, new Pagination(limit, offset, hasMore), total);
    }

    public Task<List<PaymentAllocation>> GetPaymentAllocations(Guid providerId, Guid paymentId)
    {
        return dataContext.PaymentAllocations
            .AsNoTracking()
            .Where(x => x.ProviderId == providerId && x.PaymentId == paymentId)
            .ProjectTo<PaymentAllocation>(mapper.ConfigurationProvider)
            .ToListAsync();
    }
    
    public Task<PaymentAllocation[]> GetInsurancePaymentAllocations(Guid providerId, Guid claimId)
    {
        return dataContext.PaymentAllocations
            .AsNoTracking()
            .Where(x => x.ProviderId == providerId && x.ClaimLine.InsuranceClaimId == claimId)
            .ProjectTo<PaymentAllocation>(mapper.ConfigurationProvider)
            .ToArrayAsync();
    }

    public async Task<Payment[]> MovePaymentsToNewContact(Guid providerId, Contact newContact, Guid[] contactIds)
    {
        var paymentIntents = await dataContext.PaymentIntents
            .Where(x => x.ProviderId == providerId && contactIds.Contains(x.ContactId))
            .ToListAsync();

        foreach (var paymentIntent in paymentIntents)
        {
            paymentIntent.ContactId = newContact.Id;
        }

        var payments = await dataContext.Payments
            .Where(x => x.ProviderId == providerId && contactIds.Contains(x.ContactId))
            .ToListAsync();

        foreach (var payment in payments)
        {
            payment.ContactId = newContact.Id;
        }

        await dataContext.SaveChangesAsync();
        return mapper.Map<Payment[]>(payments);
    }

    public Task<PaymentDetail> GetDetail(Guid providerId, Guid contactId, Guid paymentId)
    {
        return dataContext.Payments
            .AsNoTracking()
            .Where(x => x.ProviderId == providerId && x.Id == paymentId && x.ContactId == contactId)
            .ProjectTo<PaymentDetail>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync();
    }

    public async Task CreatePaymentAllocations(IEnumerable<PaymentAllocation> paymentAllocations)
    {
        var dataModel = mapper.Map<PaymentAllocationDataModel[]>(paymentAllocations);
        await dataContext.PaymentAllocations.AddRangeAsync(dataModel);
        await dataContext.SaveChangesAsync();
    }

    [Obsolete("Migration helper method, will be removed")]
    public async Task<Dictionary<Guid, Payment>> GetPaymentsByInvoiceIds(Guid[] invoiceIds)
    {
        var query = await dataContext.Payments
            .AsNoTracking()
            .Where(x => invoiceIds.Contains(x.InvoicePayment.InvoiceId))
            .ProjectTo<Payment>(mapper.ConfigurationProvider, dest => dest.Allocations)
            .ToListAsync();
        var result = query.DistinctBy(x => x.InvoiceId).ToDictionary(x => x.InvoiceId.Value);
        return result;
    }

    public async Task UpdatePaymentToV2Billing(Guid paymentId)
    {
        var payment = await dataContext.Payments.FindAsync(paymentId);
        payment.IsBillingV2 = true;
        await dataContext.SaveChangesAsync();
    }

    public async Task<PaymentDetail> Allocate(Payment payment, CancellationToken cancellationToken)
    {
        var existingPayment = await dataContext.Payments.Where(x => x.Id == payment.Id
                                                                    && x.ContactId == payment.ContactId
                                                                    && x.ProviderId == payment.ProviderId)
            .Include(x => x.Allocations)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingPayment == null)
        {
            return null;
        }

        EntityUtilities.UpdateList(dataContext, existingPayment.Allocations, payment.Allocations.ToList() ?? [],
            (x, p) => x.Id == p.Id,
            x => new PaymentAllocationDataModel
            {
                Id = x.Id,
                PaymentId = x.PaymentId,
                BillableItemId = x.BillableItemId,
                ProviderId = x.ProviderId,
                ContactId = x.ContactId,
                Amount = x.Amount,
                ClaimLineId = x.ClaimLineId,
                InvoiceLineItemId = x.InvoiceLineItemId
            }
        );

        dataContext.Update(existingPayment);
        await dataContext.SaveChangesAsync(cancellationToken);
        return mapper.Map<PaymentDetail>(existingPayment);
    }

    public async Task<AllocationItem[]> GetAvailableAllocationItems(
        Guid providerId,
        Guid contactId,
        PayerType payerType,
        string currencyCode,
        int limit = 20)
    {
        if (payerType == PayerType.Self || payerType == PayerType.SelfPay)
        {
            var query = dataContext.InvoiceLineItems
                .Include(x => x.Allocations)
                .Include(x => x.Invoice)
                .Include(x => x.BillableItem)
                .Where(x => x.Invoice.Status != InvoiceStatus.Void && x.Invoice.Status != InvoiceStatus.Processing)
                .Where(x => x.BillableItemId.HasValue)
                .Where(x => x.BillableItem.ProviderId == providerId && x.BillableItem.ContactId == contactId)
                .Where(x => x.BillableItem.Unpaid != 0 || x.Allocations.Count == 0);

            if (!string.IsNullOrEmpty(currencyCode))
            {
                query = query.Where(x => x.CurrencyCode == currencyCode);
            }

            var unallocated = await query
                .OrderByDescending(x => x.Date)
                .Take(20)
                .ProjectTo<InvoiceUnallocatedItem>(mapper.ConfigurationProvider)
                .ToArrayAsync();
            return unallocated;
        }
        else
        {
            var query = dataContext.BillableItems
                .Include(x => x.InsuranceClaimServiceLines)
                .ThenInclude(x => x.InsuranceClaim)
                .Where(x => x.ProviderId == providerId && x.ContactId == contactId && x.IsInsuranceEnabled)
                .Where(x => x.InsuranceUnpaid != 0 || x.PaymentAllocations.Count == 0);
            
                if (!string.IsNullOrEmpty(currencyCode))
                {
                    query = query.Where(x => x.CurrencyCode == currencyCode);;
                }
                
                var unallocated = await query
                    .GroupJoin(
                        dataContext.InsuranceClaimServiceLines,
                        billableItem => billableItem.Id,
                        insuranceClaimServiceLine => insuranceClaimServiceLine.BillableItemId,
                        (billableItem, insuranceClaimServiceLines) => new { billableItem, subgroup = insuranceClaimServiceLines })
                    .SelectMany(
                        joinedSet => joinedSet.subgroup.DefaultIfEmpty(),
                    (billableItem, insuranceClaimServiceLine) => new InsuranceUnallocatedItemProjectionModel
                    {
                        BillableItemId = billableItem.billableItem.Id,
                        Date = billableItem.billableItem.Date,
                        Description = billableItem.billableItem.Description,
                        Detail = billableItem.billableItem.Detail,
                        CurrencyCode = billableItem.billableItem.CurrencyCode,
                        Amount = billableItem.billableItem.InsuranceAmount,
                        Paid = billableItem.billableItem.InsurancePaid,
                        ClaimLineId = insuranceClaimServiceLine.Id,
                        ClaimId = insuranceClaimServiceLine.InsuranceClaim.Id,
                        ClaimDate = billableItem.billableItem.Date,
                        ClaimStatus = insuranceClaimServiceLine.InsuranceClaim.Status,
                        ClaimType = insuranceClaimServiceLine.InsuranceClaim.Type,
                        ClaimNumber = insuranceClaimServiceLine.InsuranceClaim.Number,
                        ClaimSubmissionMethod = insuranceClaimServiceLine.InsuranceClaim.SubmissionMethod
                    }).OrderByDescending(x => x.Date)
                .Take(20)
                .ToArrayAsync();

            return unallocated.Select(x => new InsuranceUnallocatedItem()
            {
                BillableItemId =  x.BillableItemId,
                Date = x.Date,
                Description = x.Description,
                Detail = x.Detail,
                Amount = x.Amount,
                Paid = x.Paid,
                CurrencyCode = x.CurrencyCode,
                ClaimLineId = x.ClaimLineId,
                Claim = (x.ClaimLineId is not null && x.ClaimId is not null) ? new BillableClaimReference()
                {
                    Id = x.ClaimId.Value,
                    Date = x.ClaimDate,
                    ClaimStatus = x.ClaimStatus ?? ClaimStatus.Unknown,
                    Type = x.ClaimType ?? ClaimType.Unknown,
                    Number = x.ClaimNumber,
                    SubmissionMethod = x.ClaimSubmissionMethod ?? ClaimSubmissionMethod.None,
                } : null
            }).ToArray<AllocationItem>();
        }
    }
    
    public async Task<PaymentIntent[]> DeletePaymentIntentsByInvoiceId(Guid[] invoiceIds, PaymentIntentStatus[] states)
    {
        var paymentIntents = await dataContext.PaymentIntents
            .Where(x => invoiceIds.Contains(x.InvoiceId) && states.Contains(x.Status))
            .ToListAsync();

        dataContext.RemoveRange(paymentIntents);

        await dataContext.SaveChangesAsync();

        return mapper.Map<PaymentIntent[]>(paymentIntents);
    }

    public async Task<PaymentAllocation[]> MovePaymentAllocationsToNewContact(
        Guid providerId,
        Contact contact,
        Guid[] deletedContactIds
    )
    {
        var allocations = await dataContext.PaymentAllocations
            .Where(x => x.ProviderId == providerId && deletedContactIds.Contains(x.ContactId))
            .ToListAsync();

        foreach (var allocation in allocations)
        {
            allocation.ContactId = contact.Id;
        }

        await dataContext.SaveChangesAsync();
        return mapper.Map<PaymentAllocation[]>(allocations);
    }

    public async Task<PaginatedResult<PaymentListEntry>> GetProviderPayments(
        Guid providerId,
        Guid personId,
        Guid[] contactIds,
        bool isAssignedOnly,
        int offset,
        int limit,
        Filter<decimal> amountFilter,
        string[] paymentProviders,
        string[] types,
        PayoutStatus[] statuses,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        string? searchTerm = null,
        Sorting? sorting = null
    )
    {
        var query = dataContext.Payments
            .AsNoTracking()
            .Where(x => x.ProviderId == providerId);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(x => EF.Functions.ILike(x.Reference, $"%{searchTerm}%"));
        }

        if (isAssignedOnly)
        {
            query = query
                .Join(dataContext.ContactAssignedStaff, payment => payment.ContactId, con => con.ContactId, (payment, con) => new { Payment = payment, AssignedContact = con })
                .Where(x => x.AssignedContact.PersonId == personId)
                .Select(x => x.Payment);
        }

        if (contactIds != null && contactIds.Length > 0)
        {
            query = query.Where(payment => contactIds.Contains(payment.ContactId));
        }

        if (amountFilter != null)
        {
            query = query.Apply(amountFilter, p => p.ChargeAmount);
        }

        if (paymentProviders != null && paymentProviders.Length > 0)
        {
            query = query.Where(x => paymentProviders.Contains(x.PaymentProvider));
        }

        if (types != null && types.Length > 0)

        {
            var hasOtherTypes = types.Contains("Other");

            query = query.Where(x => types.Contains(x.Type) ||
                (hasOtherTypes && x.PaymentProvider == PaymentProviders.Manual &&
                    x.Type != PaymentTypes.Cash &&
                    x.Type != PaymentTypes.InternetBanking));
        }

        if (statuses != null && statuses.Length > 0)
        {
            query = query.Where(x => statuses.Any(s => s == x.PayoutStatus));
        }

        if (fromDate.HasValue)
        {
            query = query.Where(x => x.PaymentDate >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(x => x.PaymentDate <= toDate.Value);
        }

        if (sorting != null && sorting.HasSort)
        {
            IOrderedQueryable<PaymentDataModel> orderedQuery = query.OrderWith(KeySelector(sorting.Primary), sorting.Primary.Direction);
            if (sorting.Secondaries.Any())
            {
                foreach (var secondarySorting in sorting.Secondaries)
                {
                    orderedQuery = orderedQuery.ThenOrderWith(KeySelector(secondarySorting), secondarySorting.Direction);
                }
            }
            query = orderedQuery.ThenOrderWith(x => x.CreatedDateTimeUtc, sorting.Primary.Direction);
        }

        var totalCount = await query.CountAsync();

        var payments = await query
            .AsNoTracking()
            .Include(x => x.Contact)
            .Skip(offset)
            .Take(limit)
            .ToArrayAsync();

        var hasMore = offset + payments.Length < totalCount;

        var paginatedResult = new PaginatedResult<PaymentListEntry>(
          mapper.Map<PaymentListEntry[]>(payments),
          new Pagination(limit, offset, hasMore),
          totalCount);

        return paginatedResult;
    }

    private Expression<Func<PaymentDataModel, object>> KeySelector(SortingField sortingField)
    {
        if (sortingField.MatchesField(nameof(PaymentDataModel.PaymentDate)))
        {
            return (x) => x.PaymentDate;
        }

        return (x) => sortingField.Field;
    }

    public async Task<bool> IsAssignedAsync(Guid personId, params Guid[] ids)
    {
        if (ids is null || !ids.Any())
            return true;

        if (personId.IsEmpty())
            return false;

        return await dataContext.Payments
            .Where(i => i.Contact.AssignedStaff.Any(s => s.PersonId == personId))
            .Where(i => ids.Contains(i.Id))
            .CountAsync() == ids.Count();
    }

    public async Task<bool> IsProviderOwnedAsync(Guid providerId, Guid[] ids)
    {
        if (providerId.IsEmpty() || ids is null || !ids.Any())
            return false;

        return await dataContext.Payments.Where(x => x.ProviderId == providerId && ids.Contains(x.Id)).CountAsync() == ids.Count();
    }

    private IQueryable<PaymentDataModel> GetPaymentBaseQuery(
        Guid providerId, 
        Guid id,
        bool includeSoftDeleted = false)
    {
        var query = dataContext.Payments.AsQueryable().Where(x => x.ProviderId == providerId && x.Id == id);
        
        if (includeSoftDeleted)
        {
            query = query.IgnoreQueryFilters();
        }
        
        return query;
    }
    
    private class InsuranceUnallocatedItemProjectionModel()
    {
        public Guid BillableItemId { get; set; }
        public DateOnly? Date { get; set; }
        public string Description { get; set; }
        public string Detail { get; set; }
        public string CurrencyCode { get; set; }
        public decimal Amount { get; set; }
        public decimal Paid { get; set; }
        public Guid? ClaimLineId { get; set; }
        public Guid? ClaimId { get; set; }
        public DateOnly? ClaimDate { get; set; }
        public ClaimStatus? ClaimStatus { get; set; }
        public ClaimType? ClaimType { get; set; }
        public string ClaimNumber { get; set; }
        public ClaimSubmissionMethod? ClaimSubmissionMethod { get; set; }
    };

}