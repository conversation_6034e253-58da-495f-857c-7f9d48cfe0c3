using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Extensions;
using carepatron.core.Repositories.Templates;
using carepatron.infra.sql.Models.Templates;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.infra.sql.Repositories;

public class TempTemplateProfessionRepository(DataContext dataContext,
    IMapper mapper) : ITempTemplateProfessionRepository
{
    public async Task PurgeTempPublicTemplateProfessions(params Guid[] ids)
    {
        if (ids.IsNullOrEmpty()) return;

        await dataContext.TempPublicTemplateProfessions
            .Where(x => ids.Contains(x.PublicTemplateId))
            .ExecuteDeleteAsync();
    }

    public async Task CreateTempPublicTemplateProfessions(Guid publicTemplateId, string[] professions)
    {
        if (professions is { Length: 0})
            return;

        var existingProfessions = await dataContext
            .TempPublicTemplateProfessions
            .Where(x => x.PublicTemplateId == publicTemplateId)
            .Select(x => x.Profession)
            .ToListAsync();
            
        var existingProfessionsSet = new HashSet<string>(existingProfessions);
            
        var models = professions
            .Where(p => !existingProfessionsSet.Contains(p))
            .Select(x => new TempPublicTemplateProfessionDataModel(publicTemplateId, x))
            .ToList();

        await dataContext.TempPublicTemplateProfessions.AddRangeAsync(models);
        await dataContext.SaveChangesAsync();
    }

    public async Task SetPublicTemplateAuthor(Guid publicTemplateId, Author author)
    {
        var model = await dataContext.PublicTemplates.FirstOrDefaultAsync(x => x.Id == publicTemplateId);

        if (model is null)
        {
            return;
        }

        model.Author = mapper.Map<TemplateAuthor>(author);
        await dataContext.SaveChangesAsync();
    }

    public async Task<PublicTemplate> GetLivePublicTemplate(Guid id)
    {
        var model = await dataContext.PublicTemplates
            .Include(x => x.Professions)
            .FirstOrDefaultAsync(x => x.Id == id);

        return mapper.Map<PublicTemplate>(model);
    }

    public async Task UpdatePublicTemplateProfessions(Guid publicTemplateId, string[] professions)
    {
        var model = await dataContext.PublicTemplates.FirstOrDefaultAsync(x => x.Id == publicTemplateId);

        if (model is null)
        {
            return;
        }

        model.Professions = professions
            .EmptyIfNull()
            .Select(x => new PublicTemplateProfessionDataModel(publicTemplateId, x))
            .PreferExisingItems(model.Professions, x => x.Profession)
            .ToList();

        await dataContext.SaveChangesAsync();
    }

    public async Task<IList<PublicTemplate>> GetLivePublicTemplates(Guid[] publicTemplateIds)
    {
        var models = await dataContext.PublicTemplates
            .Include(x => x.Professions)
            .Where(x => publicTemplateIds.Contains(x.Id))
            .ToListAsync();

        return mapper.Map<IList<PublicTemplate>>(models);;
    }

    public async Task<IList<(Guid PublicTemplateId, string[] Professions)>> GetLivePublicTemplatesForBackup(Guid[] publicTemplateIds)
    {
        var query = await dataContext.PublicTemplates
            .AsNoTracking()
            .Where(x => publicTemplateIds.Contains(x.Id))
            .Select(x => new
            {
                PublicTemplateId = x.Id,
                Professions = x.Professions.Select(p => p.Profession)
            })
            .ToListAsync();

        return query.Select(x => (x.PublicTemplateId, x.Professions.ToArray())).ToList();
    }

    public async Task<IList<TempPublicTemplateProfession>> GetTempPublicTemplateProfessions(Guid publicTemplateId)
    {
        var models = await dataContext
            .TempPublicTemplateProfessions
            .Where(x => x.PublicTemplateId == publicTemplateId)
            .ProjectTo<TempPublicTemplateProfession>(mapper.ConfigurationProvider)
            .ToListAsync();

        return models;
    }
}