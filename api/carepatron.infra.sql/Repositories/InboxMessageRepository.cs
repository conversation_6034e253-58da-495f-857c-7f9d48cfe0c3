﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Communications.Inbox.Abstractions;
using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Extensions;
using carepatron.infra.sql.Extensions;
using carepatron.infra.sql.Models.Inbox;
using DotNext.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.infra.sql.Repositories;
public class InboxMessageRepository(DataContext dataContext,
    IMapper mapper) : IInboxMessageRepository
{
    private readonly DataContext dataContext = dataContext;
    private readonly IMapper mapper = mapper;

    public async Task<InboxMessage[]> GetHasAttachmentsByInboxId(Guid inboxId)
        => await GetHasAttachmentsQuery()
                .FilterByInboxes(inboxId)
                .ProjectTo<InboxMessage>(mapper.ConfigurationProvider)
                .ToArrayAsync();

    public async Task<InboxMessage[]> GetHasAttachmentsByConversationIds(params Guid[] conversationIds)
        => await GetHasAttachmentsQuery()
                .FilterByConversations(conversationIds)
                .ProjectTo<InboxMessage>(mapper.ConfigurationProvider)
                .ToArrayAsync();

    public async Task<InboxMessage> Get(Guid id)
        => await dataContext.InboxMessages
            .AsNoTracking()
            .Include(x => x.InboxMessageSeenBys)
            .Include(x => x.InboxMessageAttachments)
            .Include(x => x.InboxConversation.InboxConversationParticipants)
            .Include(x => x.LastSendAttemptByPerson)
            .ProjectTo<InboxMessage>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(x => x.Id == id);

    public async Task UpdateStatus(Guid inboxId,
        string[] messageIdsToDelete,
        string[] messageIdsToArchive,
        string[] messageIdsToOpen,
        MessageStatus[] filterStatuses,
        DateTime updatedAt)
    {
        var idsToDeleteHash = new HashSet<string>(messageIdsToDelete ?? []);
        var idsToArchiveHash = new HashSet<string>(messageIdsToArchive ?? []);
        var idsToOpenHash = new HashSet<string>(messageIdsToOpen ?? []);

        var allIdsToProcess = idsToDeleteHash
            .Concat(idsToArchiveHash)
            .Concat(idsToOpenHash)
            .ToArray();

        // Do nothing
        if (allIdsToProcess.IsNullOrEmpty()) return;

        await dataContext.InboxMessages
            .FilterByInboxes(inboxId)
            .ExcludeByStatuses(filterStatuses)
            .FilterByExternalSourceMessageIds(allIdsToProcess)
            // Excludes message ids that are already in the desired status
            .Where(x => idsToDeleteHash.Contains(x.ExternalSourceMessageId) && x.Status != MessageStatus.Deleted
                || idsToArchiveHash.Contains(x.ExternalSourceMessageId) && x.Status != MessageStatus.Archived
                || idsToOpenHash.Contains(x.ExternalSourceMessageId) && x.Status != MessageStatus.Default)
            // Update with case statement
            .ExecuteUpdateAsync(x => x
                .SetProperty(y => y.Status,
                    y => idsToDeleteHash.Contains(y.ExternalSourceMessageId) ? MessageStatus.Deleted :
                        idsToArchiveHash.Contains(y.ExternalSourceMessageId) ? MessageStatus.Archived :
                        idsToOpenHash.Contains(y.ExternalSourceMessageId) ? MessageStatus.Default :
                        y.Status)
                .SetProperty(y => y.StatusChangedAt, updatedAt));
    }

    private IQueryable<InboxMessageDataModel> GetHasAttachmentsQuery()
        => dataContext.InboxMessages
            .AsNoTracking()
            .Include(x => x.InboxMessageAttachments)
            .HasAttachments();
}
