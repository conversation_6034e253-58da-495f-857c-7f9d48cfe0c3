﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Communications.Inbox.Abstractions;
using carepatron.core.Application.Communications.Inbox.Builders;
using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Extensions;
using carepatron.core.Paging;
using carepatron.core.Paging.Models;
using carepatron.infra.sql.Extensions;
using carepatron.infra.sql.Models.Inbox;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.infra.sql.Repositories;
public class InboxConversationRepository(DataContext dataContext,
    IMapper mapper,
    ITokenisedPaginator tokenisedPaginator) : IInboxConversationRepository
{
    private readonly DataContext dataContext = dataContext;
    private readonly IMapper mapper = mapper;
    private readonly ITokenisedPaginator tokenisedPaginator = tokenisedPaginator;

    public async Task<InboxConversation> Get(Guid id)
    {
        return await dataContext.InboxConversations
            .Include(x => x.InboxConversationParticipants)
            .Include(x => x.InboxConversationThreads)
            .ProjectTo<InboxConversation>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(x => x.Id == id);
    }

    public async Task<InboxConversation[]> GetByParticipant(Guid providerId,
        string accountId,
        AccountType accountType)
        => await dataContext.InboxConversations
            .AsNoTracking()
            .Include(x => x.InboxConversationParticipants)
            .Where(x => x.InboxConversationParticipants.Any(p => p.ProviderId == providerId
                && p.AccountId == accountId
                && p.AccountType == accountType))
            .ProjectTo<InboxConversation>(mapper.ConfigurationProvider)
            .ToArrayAsync();

    public async Task Add(params InboxConversation[] conversations)
    {
        conversations ??= [];

        if (conversations.IsNullOrEmpty()) return;

        var dataModels = mapper.Map<InboxConversationDataModel[]>(conversations);

        dataContext.AddRange(dataModels);

        await dataContext.SaveChangesAsync();
    }

    public async Task Update(InboxConversation conversation)
        => await dataContext.InboxConversations
            .Where(x => x.Id == conversation.Id)
            .ExecuteUpdateAsync(setter => setter
                .SetProperty(x => x.InboxId, conversation.InboxId)
                .SetProperty(x => x.Name, conversation.Name)
            );

    public async Task Delete(params Guid[] ids)
    {
        ids ??= [];

        if (ids.IsNullOrEmpty()) return;

        var dataModels = await dataContext.InboxConversations
            .Where(x => ids.Contains(x.Id))
            .ToArrayAsync();

        if (dataModels.IsNullOrEmpty()) return;

        dataContext.RemoveRange(dataModels);

        await dataContext.SaveChangesAsync();
    }

    public async Task AddParticipants(params InboxConversationParticipant[] participants)
    {
        participants ??= [];

        if (participants.IsNullOrEmpty()) return;

        var dataModels = mapper.Map<InboxConversationParticipantDataModel[]>(participants);

        dataContext.AddRange(dataModels);

        await dataContext.SaveChangesAsync();
    }

    public async Task UpdateParticipants(params InboxConversationParticipant[] participants)
    {
        participants ??= [];

        if (participants.IsNullOrEmpty()) return;

        var participantIds = participants
            .Select(x => x.Id)
            .ToArray();

        var dataModels = await dataContext.InboxConversationParticipants
            .Where(x => participantIds.Contains(x.Id))
            .ToArrayAsync();

        if (dataModels.IsNullOrEmpty()) return;

        foreach (var dataModel in dataModels)
        {
            var participant = participants.FirstOrDefault(x => x.Id == dataModel.Id);

            if (participant is null) continue;

            dataModel.RoleType = participant.RoleType;
            dataModel.ProviderId = participant.ProviderId;
            dataModel.AccountId = participant.AccountId;
            dataModel.AccountType = participant.AccountType;
        }

        await dataContext.SaveChangesAsync();
    }

    public async Task DeleteParticipants(params Guid[] participantIds)
    {
        participantIds ??= [];

        if (participantIds.IsNullOrEmpty()) return;

        var dataModels = await dataContext.InboxConversationParticipants
            .Where(x => participantIds.Contains(x.Id))
            .ToArrayAsync();

        if (dataModels.IsNullOrEmpty()) return;

        dataContext.RemoveRange(dataModels);

        await dataContext.SaveChangesAsync();
    }

    public async Task AddThreads(params InboxConversationThread[] threads)
    {
        threads ??= [];

        if (threads.IsNullOrEmpty()) return;

        var dataModels = mapper.Map<InboxConversationThreadDataModel[]>(threads);

        dataContext.AddRange(dataModels);

        await dataContext.SaveChangesAsync();
    }

    public async Task UpdateThreads(params InboxConversationThread[] threads)
    {
        threads ??= [];

        if (threads.IsNullOrEmpty()) return;

        var threadIds = threads
            .Select(x => x.Id)
            .ToArray();

        var dataModels = await dataContext.InboxConversationThreads
            .Where(x => threadIds.Contains(x.Id))
            .ToArrayAsync();

        if (dataModels.IsNullOrEmpty()) return;

        foreach (var dataModel in dataModels)
        {
            var thread = threads.FirstOrDefault(x => x.Id == dataModel.Id);

            if (thread is null) continue;

            dataModel.ExternalConversationId = thread.ExternalConversationId;
        }

        await dataContext.SaveChangesAsync();
    }

    public async Task DeleteThreads(params Guid[] threadIds)
    {
        threadIds ??= [];

        if (threadIds.IsNullOrEmpty()) return;

        var dataModels = await dataContext.InboxConversationThreads
            .Where(x => threadIds.Contains(x.Id))
            .ToArrayAsync();

        if (dataModels.IsNullOrEmpty()) return;

        dataContext.RemoveRange(dataModels);

        await dataContext.SaveChangesAsync();
    }

    public async Task DeleteMessages(params Guid[] conversationIds)
        => await dataContext.InboxMessages
            .FilterByConversations(conversationIds)
            .ExecuteDeleteAsync();

    public async Task<TokenisedPaginatedResult<ConversationResponse>> GetByParticipantAccount(Guid providerId,
        GetConversationResponseParams getConversationResponseParams,
        params string[] participantAccountIds)
    {
        var pagination = getConversationResponseParams.Pagination;

        var participantConversationIds = dataContext.InboxConversationParticipants
            .Where(p => p.ProviderId == providerId && participantAccountIds.Contains(p.AccountId.ToLower()))
            .Select(p => p.ConversationId)
            .Distinct();

        var baseMessagesQuery = dataContext.InboxMessages
            .AsNoTracking()
            .Where(m => m.ConversationId.HasValue && participantConversationIds.Contains(m.ConversationId.Value))
            .FilterConversationByStatus(getConversationResponseParams.Status)
            .FilterConversationByReadStatus(getConversationResponseParams.FilterBy);

        if (!getConversationResponseParams.SearchTerm.IsNullOrEmpty())
        {
            baseMessagesQuery = await ApplySearchFilter(baseMessagesQuery, providerId, getConversationResponseParams.SearchTerm);
        }

        var inboxConversationQuery = baseMessagesQuery
            .GetLatestConversationMessage();

        var countQuery = inboxConversationQuery.CountAsync();

        if (getConversationResponseParams.IsCountOnly) return new TokenisedPaginatedResult<ConversationResponse>([], null, await countQuery);

        // Apply paginationsortingdatetime filtering on the conversations
        if (pagination?.SortingDateTimeUtc.HasValue ?? false)
            inboxConversationQuery = inboxConversationQuery.ApplyPagination(getConversationResponseParams.SortBy, pagination.SortingDateTimeUtc.Value);

        // Apply sorting by created_at on the conversations
        var sortedConversationsQuery = inboxConversationQuery
            .ApplyConversationSorting(getConversationResponseParams.SortBy, getConversationResponseParams.ReplyAccountIds);

        var result = await tokenisedPaginator.TransformAndPaginateAsync<InboxMessageDataModel, ConversationResponse>(sortedConversationsQuery, countQuery, pagination);

        return result;
    }

    public async Task<TokenisedPaginatedResult<ConversationResponse>> GetByInboxIds(Guid providerId,
        GetConversationResponseParams getConversationResponseParams,
        params Guid[] inboxIds)
    {
        var pagination = getConversationResponseParams.Pagination;

        var baseMessagesQuery = dataContext.InboxMessages
            .AsNoTracking()
            .FilterByInboxes(inboxIds)
            .FilterConversationByStatus(getConversationResponseParams.Status);

        // Apply search query after the draft calculation to ensure that draft counts are accurate
        if (!getConversationResponseParams.SearchTerm.IsNullOrEmpty())
        {
            baseMessagesQuery = await ApplySearchFilter(baseMessagesQuery, providerId, getConversationResponseParams.SearchTerm);
        }

        var inboxConversationQuery = await dataContext.InboxConversations
            .GetLatestConversationMessage(baseMessagesQuery);

        // Only execute this when the folder is a draft folder
        // Inbox / Delete / Closed folder states excludes the draft inherently, do this to avoid further processing
        if (getConversationResponseParams.Status == MessageStatus.Draft)
        {
            // Do a check for composed drafts and include them in the query as they are not included in the conversation table
            var composedDraftsQuery = baseMessagesQuery.FilterByStatus(MessageStatus.Draft);

            // Append composed drafts in the existing query to include them in the conversation list
            // Use concat instead of union as concat is faster since there is no need for deduplication
            inboxConversationQuery = inboxConversationQuery.Concat(composedDraftsQuery);
        }

        // Apply filtering based on the read status of the conversation
        inboxConversationQuery = inboxConversationQuery.FilterConversationByReadStatus(getConversationResponseParams.FilterBy);

        var countQuery = inboxConversationQuery.CountAsync();

        if (getConversationResponseParams.IsCountOnly) return new TokenisedPaginatedResult<ConversationResponse>([], null, await countQuery);

        // Apply paginationsortingdatetime filtering on the conversations
        if (pagination?.SortingDateTimeUtc.HasValue ?? false)
            inboxConversationQuery = inboxConversationQuery.ApplyPagination(getConversationResponseParams.SortBy, pagination.SortingDateTimeUtc.Value);

        // Apply sorting by created_at on the conversations
        var sortedConversationsQuery = inboxConversationQuery
            .ApplyConversationSorting(getConversationResponseParams.SortBy, getConversationResponseParams.ReplyAccountIds);

        var result = await tokenisedPaginator.TransformAndPaginateAsync<InboxMessageDataModel, ConversationResponse>(sortedConversationsQuery, countQuery, pagination);

        return result;
    }

    public async Task<Dictionary<string, ConversationSummary>> GetSummary(Guid[] ids)
        => await dataContext.InboxConversations
            .GetConversationSummaryByConversation(ids);

    private async Task<IQueryable<InboxMessageDataModel>> ApplySearchFilter(
        IQueryable<InboxMessageDataModel> messageQuery,
        Guid providerId,
        string searchTerm)
    {
        searchTerm = searchTerm.ToLower();

        // Materialize the matching contact accounts to avoid query complexity
        var matchingContactAccounts = await dataContext.InboxContacts
            .AsNoTracking()
            .FilterByProvider(providerId)
            .SearchByContactName(searchTerm)
            .Select(x => x.AccountId.ToLower())
            .ToArrayAsync();

        return messageQuery
            .FilterBySearchQuery(matchingContactAccounts, searchTerm);
    }
}
