using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;
using AngleSharp.Common;
using AutoMapper;
using carepatron.core.Repositories.ClearingHouse;
using Microsoft.EntityFrameworkCore;

namespace carepatron.infra.sql.Repositories.ClearingHouse;

public class ClearingHouseIdLookupRepository(DataContext dataContext) : IClearingHouseIdLookupRepository
{
    public async Task<long?> Get(Guid entityId, CancellationToken cancellationToken)
    {
        var identity = await dataContext.ClearingHouseIdLookups
          .AsNoTracking()
          .Where(x => x.EntityId == entityId)
          .Select(x => x.Identity as long?)
          .SingleOrDefaultAsync(cancellationToken);

        return identity;
    }

    public async Task<long> GetOrAdd(Guid entityId, CancellationToken cancellationToken)
    {
        var result = await Get(entityId, cancellationToken);

        if (!result.HasValue)
        {
            // insert directly using raw SQL to reduce possible concurrency issues
            await dataContext.Database.ExecuteSqlAsync($"INSERT INTO carepatron.clearing_house_id_lookups (entity_id) VALUES ({entityId})");
            result = await Get(entityId, cancellationToken);
        }

        return result.Value;
    }

    public async Task<Guid?> Lookup(long identifier, CancellationToken cancellationToken)
    {
        return await dataContext.ClearingHouseIdLookups
            .AsNoTracking()
            .Where(x => x.Identity == identifier)
            .Select(x => x.EntityId as Guid?)
            .SingleOrDefaultAsync(cancellationToken);
    }

    public async Task<Guid?> Lookup(string identifier, CancellationToken cancellationToken)
    {
        if (!long.TryParse(identifier, out var id))
        {
            return null;
        }

        return await Lookup(id, cancellationToken);
    }

    public async Task<Dictionary<long, Guid>> Lookup(long[] identifiers, CancellationToken cancellationToken)
    {
        var results = await dataContext.ClearingHouseIdLookups
            .AsNoTracking()
            .Where(x => identifiers.Contains(x.Identity))
            .ToDictionaryAsync(
                x => x.Identity,
                x => x.EntityId,
                cancellationToken
            );

        return results;
    }


    public async Task<Dictionary<string, Guid>> Lookup(string[] identifiers, CancellationToken cancellationToken)
    {
        // find all numeric identifiers
        var pcns = identifiers.Aggregate(new List<long>(), (acc, k) =>
                {
                    if (long.TryParse(k, out var pcn))
                    {
                        acc.Add(pcn);
                    }
                    return acc;
                })
            .ToArray();

        var results = await Lookup(pcns, cancellationToken);

        // convert back to string based keys
        return results.ToDictionary(
            kvp => kvp.Key.ToString(),
            kvp => kvp.Value
        );
    }
}
