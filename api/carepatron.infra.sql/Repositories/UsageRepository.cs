using System.Threading.Tasks;
using carepatron.core.Repositories.Billing;

using Microsoft.EntityFrameworkCore;
using Npgsql;
using NpgsqlTypes;
using Serilog;

namespace carepatron.infra.sql.Repositories;

public class UsageRepository : IUsageRepository
{
    private readonly DataContext dataContext;

    public UsageRepository(DataContext dataContext)
    {
        this.dataContext = dataContext;
    }

    public async Task UpdateUsage(char partition)
    {
        var p = new NpgsqlParameter { Value = partition, NpgsqlDbType = NpgsqlDbType.Char };
        var updated = await dataContext.Database.ExecuteSqlInterpolatedAsync(
            @$"UPDATE carepatron.billing_accounts ba
                    SET used_storage_megabytes = us.usage_megabytes
                    FROM(
                        SELECT
                            p.id,
                            (ceil( 
								coalesce(contacts.count * 7, 0) +
								coalesce(tasks.count * 5, 0) +
								coalesce(invoices.count * 6, 0) +
								coalesce(contact_notes.count * 5, 0) +
								coalesce(contact_files.usage / 1000000, 0))
							) as usage_megabytes
                        FROM carepatron.providers p
                        LEFT JOIN (
                            SELECT provider_id, count(1) as count
                            FROM carepatron.contact_notes x
                            GROUP BY provider_id
                        ) as contact_notes
                        ON p.id = contact_notes.provider_id
                        LEFT JOIN (
                            SELECT provider_id, count(1) as count
                            FROM carepatron.contacts x
                            GROUP BY provider_id
                        ) as contacts
                        ON p.id = contacts.provider_id
                        LEFT JOIN (
                            SELECT provider_id, count(1) as count
                            FROM carepatron.tasks x
                            GROUP BY provider_id
                        ) as tasks
                        ON p.id = tasks.provider_id
                        LEFT JOIN (
                            SELECT provider_id, count(1) as count
                            FROM carepatron.invoices x
                            GROUP BY provider_id
                        ) as invoices
                        ON p.id = invoices.provider_id
                        LEFT JOIN (
                            SELECT provider_id, SUM(x.file_size) as usage
                            FROM carepatron.contact_files x
                            GROUP BY provider_id
                        ) as contact_files
                        ON p.id = contact_files.provider_id
                        where left(p.id::text,1) = {p}
                        AND p.last_accessed_utc > (NOW() - INTERVAL '7 days')
                    ) as us
                    WHERE us.id = ba.provider_id
                ");


        Log.Information("{Updated} rows updated", updated);

        await dataContext.SaveChangesAsync();
    }
}
