﻿using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Abstractions;
using carepatron.core.Application.ClientPortal.Models;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Trash.Models;
using carepatron.core.Extensions;
using carepatron.core.Models.Common;
using carepatron.core.Models.Invoices;
using carepatron.core.Models.Pagination;
using carepatron.core.Models.Permissions;
using carepatron.core.Repositories.Tasks;
using carepatron.infra.sql.Models.Tasks;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using carepatron.core.Application.Tasks.Extensions;
using carepatron.infra.sql.Extensions;
using carepatron.infra.sql.Utils;
using LinqKit;

namespace carepatron.infra.sql.Repositories
{
    public class TaskRepository(DataContext dataContext, IMapper mapper) : ITaskRepository, IActivityCheck
    {
        public async Task Create(SaveTaskModel task)
        {
            var model = mapper.Map<TaskDataModel>(task);
            
            await dataContext.AddAsync(model);
            await dataContext.SaveChangesAsync();
        }

        public async Task BulkCreate(SaveTaskModel[] tasks)
        {
            var models = mapper.Map<TaskDataModel[]>(tasks);
            await dataContext.Tasks.AddRangeAsync(models);
            await dataContext.SaveChangesAsync();
        }

        public async Task Delete(Guid id)
        {
            var model = await dataContext.Tasks
                .FirstOrDefaultAsync(x => x.Id == id);

            if (model == null)
                return;

            dataContext.SoftDelete(model);

            await dataContext.SaveChangesAsync();
        }

        public async Task BulkDelete(Guid[] ids)
        {
            var models = await dataContext.Tasks
                .IgnoreQueryFilters()
                .Where(x => ids.Contains(x.Id))
                .ToArrayAsync();

            dataContext.HardDeleteRange(models);

            await dataContext.SaveChangesAsync();
        }

        public async Task<IList<SimpleTaskModel>> GetByCallId(Guid callId)
        {
            var model = await dataContext.Tasks
                .Where(x => x.CallId == callId)
                .ToListAsync();

            return mapper.Map<List<SimpleTaskModel>>(model);
        }

        public async Task<IList<TaskModel>> GetFullTaskDataByCallId(Guid callId)
        {
            var model = await dataContext.Tasks
                .Where(x => x.CallId == callId)
                .ToListAsync();

            return mapper.Map<List<TaskModel>>(model);
        }

        public async Task<PaginatedResult<TaskModel>> Get(
            Guid? providerId,
            Guid? currentPersonId,
            SchedulingPermission schedulingPermission,
            TaskType[] types,
            DateTime? fromStartDate,
            DateTime? toEndDate,
            Guid[] contactIds,
            Guid[] staffIds,
            TaskContactStatus? taskContactStatus,
            InvoiceStatus? invoiceStatus,
            string attendeeStatusId,
            bool? unInvoiced,
            string location,
            string[] externalCalendarIds,
            bool? unAssigned,
            int limit,
            int offset)
        {
            // ensure we don't fetch everything
            if (!providerId.HasValue &&
                (types == null || types.Any()) &&
                !fromStartDate.HasValue &&
                !toEndDate.HasValue &&
                (contactIds == null || contactIds.Any()) &&
                (staffIds == null || staffIds.Any()))
            {
                return new PaginatedResult<TaskModel>(Array.Empty<TaskModel>(), new Pagination(0, 0));
            }

            var query = dataContext.Tasks
                .Include(s => s.TaskClaims)
                .ThenInclude(ict => ict.InsuranceClaim)
                .Select(x => x);

            if (providerId.HasValue)
            {
                query = query.Where(task => task.ProviderId == providerId);
            }

            if (schedulingPermission == SchedulingPermission.OwnCalendar)
            {
                query = query.Where(task => task.Staff.Any(taskStaff => taskStaff.PersonId == currentPersonId));
            }

            if (types != null && types.Any())
            {
                query = query.Where(task => types.Contains(task.Type));
            }

            if (contactIds != null && contactIds.Any())
            {
                query = query.Where(task => task.Contacts.Any(taskContact => contactIds.Contains(taskContact.ContactId)));
            }

            var isUnassigned = unAssigned.GetValueOrDefault();
            if ((staffIds != null && staffIds.Any()) || isUnassigned)
            {
                // HACK: task_staff is related to person record and deleting a staff member doesn't cascade to task_staff table
                // Here we need to fetch all staff members and check task_staff that are not in the staffMemberPersonIds
                var staffMembers = await dataContext.ProviderStaff
                    .Where(x => x.ProviderId == providerId)
                    .ToArrayAsync();
                var staffMemberPersonIds = staffMembers.Select(x => x.PersonId).ToArray();

                var hasAssignedStaff = staffIds?.Any() ?? false;

                query = query.Where(task => (hasAssignedStaff && task.Staff.Any(taskStaff => staffIds.Contains(taskStaff.PersonId))) ||
                                            (isUnassigned &&
                                             (!task.Staff.Any() || !task.Staff.Any(taskStaff => staffMemberPersonIds.Contains(taskStaff.PersonId)))));
            }

            if (taskContactStatus is not null)
            {
                query = query.Where(task => task.Contacts.Any(taskContact => taskContact.TaskContactStatus == taskContactStatus));
            }

            // filter by custom attendee status
            if (!string.IsNullOrWhiteSpace(attendeeStatusId))
            {
                // tries to parse the attendee status id to a TaskContactStatus (e.g. "Confirmed" to TaskContactStatus.Confirmed)
                // if it fails, it will use the attendeeStatusId as is
                if (attendeeStatusId.TryParseAttendeeStatus(out var parsedContactStatus))
                {
                    // find matching taskContact.TaskContactStatus (old) or taskContact.AttendeeStatusId (new)
                    query = query.Where(task => task.Contacts.Any(taskContact =>
                        (!string.IsNullOrEmpty(taskContact.AttendeeStatusId) && taskContact.AttendeeStatusId == attendeeStatusId) ||
                        (string.IsNullOrEmpty(taskContact.AttendeeStatusId) && taskContact.TaskContactStatus == parsedContactStatus)));
                }
                else
                {
                    query = query.Where(task => task.Contacts.Any(taskContact => taskContact.AttendeeStatusId == attendeeStatusId));
                }
            }

            if (unInvoiced ?? false)
            {
                query = query.Where(task => !task.Invoices.Any() && !task.InvoiceTasks.Any());
            }
            else if (invoiceStatus is not null)
            {
                query = query.Where(task => task.Invoices.Any(invoice => invoice.Status == invoiceStatus));
            }

            if (!string.IsNullOrEmpty(location))
            {
                query = query.Where(task => task.Location == location);
            }

            if (fromStartDate.HasValue && toEndDate.HasValue)
                query = query.WhereTasksBetween(fromStartDate.Value, toEndDate.Value);

            if (externalCalendarIds != null && externalCalendarIds.Any())
                query = query.Where(task => task.ExternalCalendarId == null || externalCalendarIds.Contains(task.ExternalCalendarId))
                    .Include(x => x.ExternalContacts);
            else
                query = query.Where(task => task.ExternalCalendarId == null);


            var models = await query
                .Skip(offset)
                .Take(limit + 1)
                .ProjectTo<TaskMappingDataModel>(mapper.ConfigurationProvider)
                .Select(mappingModel => mapper.Map<TaskModel>(mappingModel))
                .ToListAsync();

            var items = models.Take(limit).ToArray();

            return new PaginatedResult<TaskModel>(items, new Pagination(items.Length, offset, items.Length < models.Count()));
        }

        public async Task<TaskModel> Get(Guid id)
        {
            return await this.Get(id, false);
        }

        public async Task<TaskModel> Get(Guid id, bool includeSoftDeleted)
        {
            var query = dataContext.Tasks.AsQueryable();
            if (includeSoftDeleted)
                query = query.IgnoreQueryFilters();
            var model = await query
                .Where(x => x.Id == id)
                .Include(task => task.Contacts)
                .ThenInclude(taskContact => taskContact.Contact)
                .ThenInclude(contact => contact.AssignedStaff)
                .ThenInclude(assignedStaff => assignedStaff.Person)
                .Include(task => task.Staff)
                .ThenInclude(taskStaff => taskStaff.Person)
                .Include(task => task.Invoices)
                .Include(task => task.Notes)
                .Include(task => task.TaskClaims)
                .ThenInclude(ict => ict.InsuranceClaim)
                .ProjectTo<TaskMappingDataModel>(mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();

            return mapper.Map<TaskModel>(model);
        }

        public async Task<TaskModel> Get(Guid providerId, Guid id)
        {
            var model = await dataContext.Tasks
                .Where(x => x.ProviderId == providerId && x.Id == id)
                .Include(task => task.Contacts)
                .ThenInclude(taskContact => taskContact.Contact)
                .ThenInclude(contact => contact.AssignedStaff)
                .ThenInclude(assignedStaff => assignedStaff.Person)
                .Include(task => task.Staff)
                .ThenInclude(taskStaff => taskStaff.Person)
                .ProjectTo<TaskMappingDataModel>(mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();

            return mapper.Map<TaskModel>(model);
        }

        public async Task<TaskModel[]> GetTasks(Guid[] ids)
        {
            var entities = await dataContext.Tasks
                .Include(taskDataModel => taskDataModel.Staff)
                .Include(taskDataModel => taskDataModel.Contacts)
                .ThenInclude(taskContactDataModel => taskContactDataModel.Contact)
                .Where(x => ids.Contains(x.Id))
                .ToArrayAsync();

            return mapper.Map<TaskModel[]>(entities);
        }

        public async Task<TaskModel[]> Get(Guid providerId,
            DateTime fromStartDate,
            DateTime toEndDate,
            Guid[] staffIds,
            TaskType[] taskTypes = null,
            DeclineEventType[] declineEventTypes = null,
            bool includeInvoiceAndNotes = false,
            string[] externalCalendarIds = null)
        {
            var query = dataContext.Tasks
                .Where(task => task.ProviderId == providerId && (taskTypes != null && taskTypes.Any() ? taskTypes.Contains(task.Type) : task.Type != TaskType.External))
                .WhereTasksBetween(fromStartDate, toEndDate);

            if (staffIds.Length > 0)
                query = query.Where(task => task.Staff.Any(taskStaff => staffIds.Contains(taskStaff.PersonId)));

            if (declineEventTypes != null && declineEventTypes.Any())
                query = query.Where(task => task.DeclineEventType != null && declineEventTypes.Contains(task.DeclineEventType.Value));

            if (includeInvoiceAndNotes)
                query = query.Include(task => task.Invoices).Include(x => x.Notes);

            if (externalCalendarIds != null && externalCalendarIds.Any())
                query = query.Where(task => task.ExternalCalendarId == null || externalCalendarIds.Contains(task.ExternalCalendarId));
            else
                query = query.Where(task => task.ExternalCalendarId == null);

            var entity = await query.ProjectTo<TaskMappingDataModel>(mapper.ConfigurationProvider).ToArrayAsync();

            return mapper.Map<TaskModel[]>(entity);
        }

        public async Task Save(SaveTaskModel task)
        {
            var record = await dataContext.Tasks
                .Include(taskDataModel => taskDataModel.Staff)
                .Include(taskDataModel => taskDataModel.Files)
                .Include(taskDataModel => taskDataModel.Contacts)
                .FirstOrDefaultAsync(x => x.Id == task.Id);

            var mappedModel = mapper.Map<TaskDataModel>(task);

            if (record == null)
                return;

            // standard info
            record.ParentId = mappedModel.ParentId;
            record.Title = mappedModel.Title;
            record.Description = mappedModel.Description;
            record.Location = mappedModel.Location;
            record.CallId = mappedModel.CallId;
            record.ContactReminderConfigs = mappedModel.ContactReminderConfigs;

            // time based info
            record.StartDate = mappedModel.StartDate;
            record.EndDate = mappedModel.EndDate;
            record.OccurrenceEndDate = mappedModel.OccurrenceEndDate;
            record.ExDate = mappedModel.ExDate;
            record.RRule = mappedModel.RRule;
            record.TimeZone = mappedModel.TimeZone;
            record.AllDay = mappedModel.AllDay;
            record.DeclineEventType = mappedModel.DeclineEventType;

            record.Staff = mappedModel.Staff
                .EmptyIfNull()
                .PreferExisingItems(record.Staff, x => x.PersonId)
                .ToList();

            record.Files = mappedModel.Files
                .EmptyIfNull()
                .PreferExisingItems(record.Files, x => x.Id)
                .ToList();

            record.ItemsSnapshot = mappedModel.ItemsSnapshot;

            record.LocationsSnapshot = mappedModel.LocationsSnapshot;

            record.Contacts = mappedModel.Contacts
                .EmptyIfNull()
                .PreferExisingItems(record.Contacts, x => x.ContactId)
                .ToList();

            record.LocationType = task.LocationType;
            record.LocationPOSCode = task.LocationPOSCode;
            record.VirtualLocationProduct = task.VirtualLocationProduct;

            record.ExternalId = task.ExternalId;
            record.IsFree = task.IsFree;
            record.IsBillingV2 = task.IsBillingV2 ?? false;

            EntityUtilities.UpdateList(
                dataContext,
                record.ExternalContacts,
                task.ExternalContacts,
                (dataModel, model) => dataModel.ExternalContactId == model.ExternalContactId,
                mapper.Map<TaskExternalContactDataModel>
            );

            // audit info
            record.UpdatedDateTimeUtc = task.LastUpdatedDateTimeUtc;
            record.LastUpdatedByPersonId = task.LastUpdatedByPersonId;
            await dataContext.SaveChangesAsync();
        }

        public Task<SimpleTaskContact> GetTaskContact(Guid taskId, Guid contactId)
        {
            return dataContext.TaskContacts
                .Where(x => x.TaskId == taskId && x.ContactId == contactId)
                .ProjectTo<SimpleTaskContact>(mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();
        }

        public async Task UpdateTaskContact(SimpleTaskContact simpleTaskContact)
        {
            var taskContact = await dataContext.TaskContacts
                .Where(x => x.TaskId == simpleTaskContact.TaskId &&
                            x.ContactId == simpleTaskContact.ContactId)
                .FirstOrDefaultAsync();

            if (taskContact is null) return;

            mapper.Map(simpleTaskContact, taskContact);
            await dataContext.SaveChangesAsync();
        }

        public async Task AddTaskContact(SimpleTaskContact simpleTaskContact)
        {
            var taskContact = mapper.Map<TaskContactDataModel>(simpleTaskContact);
            await dataContext.TaskContacts.AddAsync(taskContact);
            await dataContext.SaveChangesAsync();
        }

        public async Task UpdateSchedule(Guid taskId, DateTime startDate, DateTime endDate)
        {
            var task = await dataContext.Tasks
                .FirstOrDefaultAsync(x => x.Id == taskId);

            if (task == null)
                return;
            task.StartDate = startDate;
            task.EndDate = endDate;
            await dataContext.SaveChangesAsync();
        }

        public async Task<TaskModel> GetChildTaskByParent(Guid parentId, DateTime dateTime)
        {
            var task = await dataContext.Tasks
                .Where(x => x.ParentId == parentId && x.StartDate == dateTime)
                .ProjectTo<TaskMappingDataModel>(mapper.ConfigurationProvider)
                .FirstOrDefaultAsync();

            return mapper.Map<TaskModel>(task);
        }

        public async Task<TaskModel[]> GetChildrenTasksByParent(Guid parentId)
        {
            var tasks = await dataContext.Tasks
                .Where(x => x.ParentId == parentId)
                .ProjectTo<TaskMappingDataModel>(mapper.ConfigurationProvider)
                .ToArrayAsync();

            return mapper.Map<TaskModel[]>(tasks);
        }

        public async Task<TaskModel[]> GetFutureTasks(Guid[] providerIds, TaskType? taskType)
        {
            var currentDate = DateTime.UtcNow;

            var query = dataContext.Tasks
                .Where(x => providerIds.Contains(x.ProviderId) &&
                            ((string.IsNullOrEmpty(x.RRule) && x.EndDate >= currentDate) ||
                             (!string.IsNullOrEmpty(x.RRule) && (x.OccurrenceEndDate == null || x.OccurrenceEndDate >= currentDate))));

            if (taskType.HasValue)
                query = query.Where(x => x.Type == taskType.Value);
            else
                query = query.Where(x => x.Type != TaskType.External);

            var entities = await query
                .OrderBy(x => x.ProviderId)
                .ProjectTo<TaskMappingDataModel>(mapper.ConfigurationProvider)
                .ToArrayAsync();

            return mapper.Map<TaskModel[]>(entities);
        }

        public async Task<PaginatedResult<ClientPortalTask>> Get(DateTime? fromStartDate, DateTime? toEndDate, Guid[] contactIds, Guid[] providerIds, int limit, int offset)
        {
            var query = dataContext.Tasks
                .Where(x => providerIds.Contains(x.ProviderId) && x.Contacts.Any(taskContact => contactIds.Contains(taskContact.ContactId)));

            if (fromStartDate.HasValue && toEndDate.HasValue)
                query = query.WhereTasksBetween(fromStartDate.Value, toEndDate.Value);

            var models = await query
                .Include(x => x.Provider)
                .Include(task => task.Contacts.Where(y => contactIds.Contains(y.ContactId)))
                .ThenInclude(taskContact => taskContact.Contact)
                .Include(task => task.Staff)
                .ThenInclude(taskStaff => taskStaff.Person)
                .Include(x => x.Notes.Where(y => contactIds.Contains(y.ContactId) && y.SharedNotes.Any()))
                .Skip(offset)
                .Take(limit + 1)
                .ToListAsync();

            var items = mapper.Map<ClientPortalTask[]>(models.Take(limit));
            return new PaginatedResult<ClientPortalTask>(items, new Pagination(items.Length, offset, items.Length < models.Count));
        }

        public async Task<TaskModel[]> GetTasksCreatedByDateRange(Guid providerId, DateRange dateRange)
        {
            if (providerId == Guid.Empty) return [];

            var tasks = await dataContext.Tasks
                .Where(x => x.ProviderId == providerId && x.CreatedDateTimeUtc >= dateRange.FromDate && x.CreatedDateTimeUtc <= dateRange.ToDate && x.Type != TaskType.External)
                .ProjectTo<TaskMappingDataModel>(mapper.ConfigurationProvider)
                .ToArrayAsync();

            return mapper.Map<TaskModel[]>(tasks);
        }

        public async Task<bool> IsActive(Guid providerId, DateRange dateRange)
        {
            if (providerId == Guid.Empty) return false;
            var count = await dataContext.Tasks
                .Where(x => x.ProviderId == providerId && x.CreatedDateTimeUtc >= dateRange.FromDate && x.CreatedDateTimeUtc <= dateRange.ToDate && x.Type != TaskType.External)
                .CountAsync();

            return count > 0;
        }

        public async Task<TaskModel[]> GetExternalTasks(Guid providerId, Guid personId, string externalCalendarId)
        {
            var tasks = await dataContext.Tasks
                .Where(x => x.ProviderId == providerId &&
                            x.CreatedByPersonId == personId &&
                            x.ExternalCalendarId == externalCalendarId && x.Type == TaskType.External)
                .ProjectTo<TaskMappingDataModel>(mapper.ConfigurationProvider)
                .Select(x => mapper.Map<TaskModel>(x))
                .ToArrayAsync();
            return tasks;
        }

        public async Task<TaskModel> GetExternalTask(Guid providerId, string externalCalendarId, string externalId)
        {
            var task = await dataContext.Tasks
                .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.ExternalCalendarId == externalCalendarId && x.ExternalId == externalId);
            return mapper.Map<TaskModel>(task);
        }

        public async Task DeleteByContactId(Guid providerId, Guid contactId)
        {
            //Delete Tasks where Contact is the only contact
            await dataContext.Tasks
                .IgnoreQueryFilters()
                .Where(x => x.ProviderId == providerId && x.Contacts.Any(y => y.ContactId == contactId) && x.Contacts.Count() == 1 && x.DeletedAtUtc == null)
                .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.DeletedAtUtc, DateTime.UtcNow));
        }

        public async Task Restore(Guid providerId, Guid id)
        {
            var model = await dataContext.Tasks
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Id == id);

            if (model == null)
                return;

            model.DeletedAtUtc = null;

            await dataContext.SaveChangesAsync();
        }

        public async Task HardDelete(Guid providerId, Guid id)
        {
            var model = await dataContext.Tasks
                .IgnoreQueryFilters()
                .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Id == id);

            if (model == null)
                return;

            dataContext.HardDelete(model);

            await dataContext.SaveChangesAsync();
        }

        public async Task RestoreByContactId(Guid providerId, Guid contactId)
        {
            //prevent trashed tasks from being restored
            var trashTaskIds = dataContext.TrashItems
                .Where(x => x.FromContactId == contactId && x.Type == TrashType.Task)
                .Select(x => x.EntityId).ToArray();

            await dataContext.Tasks
                .IgnoreQueryFilters()
                .Where(x => x.ProviderId == providerId && x.Contacts.Any(y => y.ContactId == contactId) && x.DeletedAtUtc.HasValue && !trashTaskIds.Contains(x.Id))
                .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.DeletedAtUtc, (DateTime?)null));
        }

        public async Task MergeTasks(Guid providerId, Guid contactId, Guid[] sourceContactIds)
        {
            await dataContext.TaskContacts
                .Where(x => x.Task.ProviderId == providerId && sourceContactIds.Contains(x.ContactId)
                                                            //Ensure the destination contact is not already assigned to the task
                                                            && !x.Task.Contacts.Any(y => y.ContactId == contactId)
                )
                .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.ContactId, contactId));
        }

        public async Task<TaskModel> GetScheduledTask(Guid providerId, DateTime startDate, DateTime endDate, Guid[] itemIds)
        {
            var tasks = await dataContext.Tasks
                .Where(x => x.ProviderId == providerId
                            && x.StartDate == startDate
                            && x.EndDate == endDate)
                .ToArrayAsync();

            var task = tasks.FirstOrDefault(x => x.ItemsSnapshot.Any(i => itemIds.Contains(i.Id)));

            return mapper.Map<TaskModel>(task);
        }

        public async Task<TaskModel[]> GetRecurringTasksByContactId(Guid providerId, Guid contactId, DateTime? fromDate, DateTime? toDate, bool? billingV2 = null)
        {
            var taskQuery = dataContext.Tasks
                .Where(x => x.ProviderId == providerId &&
                            x.Contacts.Any((contact) => contact.ContactId == contactId) &&
                            x.RRule != null);
            if (billingV2 != null)
            {
                taskQuery = taskQuery.Where(x => x.IsBillingV2 == billingV2);
            }

            if (fromDate.HasValue)
            {
                taskQuery = taskQuery.Where(x => x.OccurrenceEndDate >= fromDate || x.OccurrenceEndDate == null);
            }

            if (toDate.HasValue)
            {
                taskQuery = taskQuery.Where(x => x.StartDate <= toDate);
            }

            var tasks = await taskQuery.ToArrayAsync();

            return mapper.Map<TaskModel[]>(tasks);
        }

        public async Task<TaskModel[]> Get(Guid providerId, Guid[] staffIds, DateRange[] dateRanges)
        {
            var query = dataContext.Tasks
                .Where(task => task.ProviderId == providerId);

            if (staffIds != null && staffIds.Any())
            {
                query = query.Where(task => task.Staff.Any(taskStaff => staffIds.Contains(taskStaff.PersonId)));
            }

            if (dateRanges.Length > 0)
            {
                var predicate = PredicateBuilder.New<TaskDataModel>();
                foreach (var dateRange in dateRanges)
                {
                    predicate = predicate.Or(TaskDataModelExtensions.WhereTasksBetweenExpression(dateRange.FromDate, dateRange.ToDate));
                }

                query = query.Where(predicate);
            }

            var tasks = await query.ToArrayAsync();

            return mapper.Map<TaskModel[]>(tasks);
        }

        public async Task<CollectionResult<TaskContactRecentService>> GetTaskContactRecentServices(Guid providerId, Guid contactId, int limit)
        {
            var itemsSnapshots = (await dataContext.Tasks
                    .Where(x => x.ProviderId == providerId &&
                                x.Contacts.Any(c => c.ContactId == contactId) &&
                                x.ItemsSnapshot.Any())
                    .OrderByDescending(x => x.CreatedDateTimeUtc)
                    .Select(x => x.ItemsSnapshot)
                    .Take(3)
                    .ToArrayAsync())
                .SelectMany(x => x)
                .ToArray();

            if (!itemsSnapshots.Any()) return CollectionResult<TaskContactRecentService>.Empty;

            var itemIds = itemsSnapshots.Select(x => x.Id).ToHashSet();

            var items = await dataContext.Items
                .Where(x => itemIds.Contains(x.Id))
                .ToArrayAsync();

            List<TaskContactRecentService> itemsList = new();
            foreach (var snapshot in itemsSnapshots)
            {
                if (itemsList.Count >= limit) break;

                var item = items.FirstOrDefault(x => x.Id == snapshot.Id);
                if (item is null) continue;
                itemsList.Add(mapper.Map<TaskContactRecentService>(item));
            }

            return new CollectionResult<TaskContactRecentService>(itemsList);
        }

        public async Task<TaskLocationSnapshot[]> GetTaskStaffRecentLocations(Guid providerId, Guid[] staffIds)
        {
            if (staffIds.IsNullOrEmpty()) return Array.Empty<TaskLocationSnapshot>();

            var locationsSnapshots = (await dataContext.Tasks
                    .Where(x => x.ProviderId == providerId &&
                                x.Staff.Any(s => staffIds.Contains(s.PersonId)) &&
                                x.LocationsSnapshot.Any())
                    .OrderByDescending(x => x.CreatedDateTimeUtc)
                    .Select(x => x.LocationsSnapshot)
                    .Take(3)
                    .ToArrayAsync())
                .SelectMany(x => x)
                .ToArray();

            if (!locationsSnapshots.Any()) return Array.Empty<TaskLocationSnapshot>();

            var locationsSnapshotsDictionary = locationsSnapshots
                .Where(x => x.LocationId.HasValue)
                .DistinctBy(x => x.LocationId.Value)
                .ToDictionary(x => x.LocationId.Value);

            var providerLocations = await dataContext.ProviderLocations
                .Where(x => x.ProviderId == providerId && locationsSnapshotsDictionary.Keys.Contains(x.Id))
                .ToArrayAsync();

            var result = providerLocations.Select(x => locationsSnapshotsDictionary[x.Id]).ToArray();

            return result;
        }

        public async Task UpdateTaskContactsStatus(Guid providerId, TaskContactStatus[] legacyStatuses, string[] oldStatuses, string newStatus)
        {
            await dataContext.TaskContacts
                .Where(x => x.Task.ProviderId == providerId &&
                            (oldStatuses.Contains(x.AttendeeStatusId)
                             || (x.TaskContactStatus.HasValue && legacyStatuses.Contains(x.TaskContactStatus.Value))))
                .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.AttendeeStatusId, newStatus));

            await dataContext.SaveChangesAsync();
        }
    }
}