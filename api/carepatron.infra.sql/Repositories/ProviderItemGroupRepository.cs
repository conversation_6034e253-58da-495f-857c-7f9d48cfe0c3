using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Workspace.ServiceItems.Models;
using carepatron.core.Models.Pagination;
using carepatron.core.Repositories.Item;
using carepatron.infra.sql.Models.Items;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.infra.sql.Repositories
{
    public class ProviderItemGroupRepository : IProviderItemGroupRepository
    {
        private readonly IMapper mapper;
        private readonly DataContext dataContext;

        public ProviderItemGroupRepository(IMapper mapper, DataContext dataContext)
        {
            this.mapper = mapper;
            this.dataContext = dataContext;
        }

        public async Task Create(ProviderItemGroup itemGroup)
        {
            var model = mapper.Map<ProviderItemGroupDataModel>(itemGroup);

            dataContext.Add(model);

            await dataContext.SaveChangesAsync();
        }

        public async Task Update(ProviderItemGroup itemGroup)
        {
            var model = await dataContext.ProviderItemGroups
                 .FirstOrDefaultAsync(x => x.Id == itemGroup.Id);

            mapper.Map(itemGroup, model);

            dataContext.Update(model);

            await dataContext.SaveChangesAsync();
        }

        public async Task<ProviderItemGroup> Get(Guid providerId, Guid id)
        {
            var result = await dataContext.ProviderItemGroups
                .Include(x => x.GroupItems)
                .Where(x => x.ProviderId == providerId && x.Id == id)
                .FirstOrDefaultAsync();

            return mapper.Map<ProviderItemGroup>(result);
        }

        public Task<ProviderItemGroup[]> Get(Guid providerId, params Guid[] ids)
        {
            return dataContext.ProviderItemGroups
                .Include(x => x.GroupItems)
                .Where(x => x.ProviderId == providerId && ids.Contains(x.Id))
                .ProjectTo<ProviderItemGroup>(mapper.ConfigurationProvider)
                .ToArrayAsync();
        }

        public async Task Delete(Guid providerId, Guid id)
        {
            var dataModel = await dataContext.ProviderItemGroups
                .FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Id == id);

            if (dataModel is null)
                return;

            dataContext.Remove(dataModel);
            await dataContext.SaveChangesAsync();
        }

        public async Task<PaginatedResult<ProviderItemGroup>> GetByProviderId(Guid providerId, string searchTerm, int limit, int offset)
        {
            var query = dataContext.ProviderItemGroups
                .Where(x => x.ProviderId == providerId);

            bool hasSearchTerm = !string.IsNullOrWhiteSpace(searchTerm);
            if (hasSearchTerm)
            {
                var pattern = $"%{searchTerm}%";

                query = query
                    .Include(itemGroup => itemGroup.GroupItems.Where(x => (
                        EF.Functions.ILike(x.Item.Title, pattern)
                        || EF.Functions.ILike(x.Item.DisplayName, pattern)
                        || EF.Functions.ILike(x.Item.Description, pattern)
                        || EF.Functions.ILike(x.Item.Code, pattern))))
                    .Where(itemGroup => EF.Functions.ILike(itemGroup.Name, pattern)
                                        || itemGroup.GroupItems.Any(x => x.Item != null && (
                                            EF.Functions.ILike(x.Item.Title, pattern)
                                            || EF.Functions.ILike(x.Item.DisplayName, pattern)
                                            || EF.Functions.ILike(x.Item.Description, pattern)
                                            || EF.Functions.ILike(x.Item.Code, pattern))));
            }

            var models = await query
                .OrderBy(x => x.Order)
                .Skip(offset)
                .Take(limit + 1)
                .ToListAsync();

            return new PaginatedResult<ProviderItemGroup>(
                mapper.Map<ProviderItemGroup[]>(models.Take(limit)),
                new Pagination(limit, offset, models.Count > limit));
        }

        public async Task Update(ProviderItemGroup[] itemGroups)
        {
            var existingItemGroups = await dataContext.ProviderItemGroups
                .Where(x => itemGroups.Select(y => y.Id).Contains(x.Id))
                .ToArrayAsync();

            foreach (var itemGroup in itemGroups)
            {
                var existingItemGroup = existingItemGroups.FirstOrDefault(x => x.Id == itemGroup.Id);

                if (existingItemGroup == null)
                {
                    continue;
                }

                mapper.Map(itemGroup, existingItemGroup);
            }
            await dataContext.SaveChangesAsync();
        }
        
        public async Task AddItemToGroup(Guid itemId, Guid groupId, int order = 99)
        {
            var itemGroupItem = new ProviderItemGroupItemDataModel
            {
                ProviderItemId = itemId,
                ProviderItemGroupId = groupId,
                Order = order
            };

            dataContext.ProviderItemGroupItems.Add(itemGroupItem);
            await dataContext.SaveChangesAsync();
        }

        public async Task RemoveItemFromGroup(Guid itemId, Guid groupId)
        {
            var record = await dataContext.ProviderItemGroupItems
                .Where(x => x.ProviderItemId == itemId && x.ProviderItemGroupId == groupId)
                .FirstOrDefaultAsync();

            if (record is null) return;

            dataContext.ProviderItemGroupItems.Remove(record);
            await dataContext.SaveChangesAsync();
        }
    }
}