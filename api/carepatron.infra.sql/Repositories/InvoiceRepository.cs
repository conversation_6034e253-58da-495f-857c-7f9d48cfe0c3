using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Trash.Models;
using carepatron.core.Authorization.Models;
using carepatron.core.Authorization.ResourceNames;
using carepatron.core.Common;
using carepatron.core.Extensions;
using carepatron.core.Models.Common;
using carepatron.core.Models.Invoices;
using carepatron.core.Models.Pagination;
using carepatron.core.Repositories.Invoices;
using carepatron.infra.sql.Extensions;
using carepatron.infra.sql.Models.Invoices;
using carepatron.infra.sql.Utils;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace carepatron.infra.sql.Repositories;

public class InvoiceRepository(
    DataContext dataContext,
    I<PERSON>apper mapper,
    IDateTimeProvider dateTimeProvider)
    : IInvoiceRepository, IActivityCheck
{
    private PayoutStatus[] intransitStatuses = new PayoutStatus[] { PayoutStatus.Pending, PayoutStatus.InTransit };

    public async Task<InvoiceListEntry> Create(Guid providerId, Invoice invoice, IEnumerable<Guid> staffIds)
    {
        var dataModel = mapper.Map<InvoiceDataModel>(invoice);
        dataModel.CreatedDateTimeUtc = DateTime.UtcNow;
        dataModel.LastUpdatedDateTimeUtc = DateTime.UtcNow;
        dataModel.ProviderId = providerId;

        dataModel.Staff = staffIds?.Select(staffId => new InvoiceStaffDataModel(dataModel.Id, staffId)).ToList();

        await dataContext.AddAsync(dataModel);

        await dataContext.SaveChangesAsync();

        return mapper.Map<InvoiceListEntry>(dataModel);
    }

    public async Task<Guid[]> Create(Guid providerId, IEnumerable<Invoice> invoices, IEnumerable<Guid> staffIds)
    {
        if (!invoices.Any())
        {
            return [];
        }

        var seedInvoiceNumber = await GetSeedInvoiceNumber(invoices);

        var dataModels = invoices.Select((x, idx) =>
        {
            var dataModel = mapper.Map<InvoiceDataModel>(x);
            dataModel.Id = x.Id == Guid.Empty ? Guid.NewGuid() : x.Id;
            dataModel.Number = Next(seedInvoiceNumber, idx);
            dataModel.CreatedDateTimeUtc = DateTime.UtcNow;
            dataModel.LastUpdatedDateTimeUtc = DateTime.UtcNow;
            dataModel.ProviderId = providerId;
            dataModel.InvoiceLineItems = x.LineItems.Select(item =>
            {
                var lineItemDataModel = mapper.Map<InvoiceLineItemDataModel>(item);
                lineItemDataModel.Id = Guid.NewGuid();
                lineItemDataModel.InvoiceId = dataModel.Id;
                return lineItemDataModel;
            }).ToList();
            dataModel.Staff = staffIds?.Select(staffId => new InvoiceStaffDataModel(dataModel.Id, staffId)).ToList();
            dataModel.TaskInvoices = x.TaskIds.EmptyIfNull().Distinct().Select(taskId => new InvoiceTaskDataModel(dataModel.Id, taskId)).ToList();
            return dataModel;
        }).ToList();

        await dataContext.AddRangeAsync(dataModels);
        await dataContext.SaveChangesAsync();
        return dataModels.Select(i => i.Id).ToArray();
    }

    public async Task<InvoiceListEntry[]> GetInvoicesByIds(Guid providerId, IEnumerable<Guid> ids)
    {
        var dataModels = await dataContext.Invoices
            .AsNoTracking()
            .Where(x => ids.Contains(x.Id) && x.ProviderId == providerId)
            .Include(x => x.InvoiceLineItems)
            .Include(x => x.InvoicePayment).ThenInclude(x => x.Payment).ThenInclude(x => x.Allocations)
            .Include(x => x.Tasks)
            .Include(x => x.Contact)
            .Include(x => x.BillTo)
            .Include(x => x.Staff).ThenInclude(x => x.Person)
            .ToArrayAsync();
        var models = mapper.Map<InvoiceListEntry[]>(dataModels);
        return models;
    }

    [Obsolete("Migration tool only. To be removed")]
    public async Task<(InvoiceListEntry[] invoices, InvoiceListEntry[] additionalInvoices)> GetByIds(IEnumerable<Guid> ids, bool isGetDeleted = false)
    {
        var invoiceQuery = dataContext.Invoices
            .AsNoTracking();
        invoiceQuery = isGetDeleted ? invoiceQuery.IgnoreQueryFilters() : invoiceQuery;
        invoiceQuery = invoiceQuery
            .Include(x => x.Contact)
            .Include(x => x.BillTo)
            .Include(x => x.Staff).ThenInclude(x => x.Person)
            .Include(x => x.Provider)
            .Include(x => x.TaskInvoices)
            .Where(x => x.IsBillingV2 == false)
            .Where(x => x.InvoiceLineItems.Any())
            .Where(x => x.Status != InvoiceStatus.Void && x.Status != InvoiceStatus.Invalid)
            .OrderByDescending(x => x.CreatedDateTimeUtc);
        var invoiceBaseResult = await invoiceQuery
            .Where(x => ids.Contains(x.Id))
            .ToArrayAsync();
        var result = mapper.Map<InvoiceListEntry[]>(invoiceBaseResult);
        var additionalResult = await invoiceQuery.Where(x => !invoiceBaseResult.Select(x => x.Id).Contains(x.Id))
            .Where(x => x.TaskId.HasValue && invoiceBaseResult.Select(x => x.TaskId).Contains(x.TaskId))
            .ToArrayAsync();
        var additionalInvoices = mapper.Map<InvoiceListEntry[]>(additionalResult);
        return (result, additionalInvoices);
    }

    [Obsolete("Migration tool only. To be removed")]
    public async Task<(InvoiceListEntry[] invoices, InvoiceListEntry[] additionalInvoices)> GetByDateRange(DateTime fromDate, DateTime toDate, Guid[] providerIds, Guid? lastInvoiceId, int limit,
        bool isGetDeleted)
    {
        var invoiceQuery = dataContext.Invoices
            .AsNoTracking();
        invoiceQuery = providerIds == null || providerIds.Length == 0
            ? invoiceQuery
            : invoiceQuery.Where(x => providerIds.Contains(x.ProviderId));

        invoiceQuery = isGetDeleted ? invoiceQuery.IgnoreQueryFilters() : invoiceQuery;

        invoiceQuery = invoiceQuery
            .OrderBy(x => x.CreatedDateTimeUtc)
            .Include(x => x.Contact)
            .Include(x => x.BillTo)
            .Include(x => x.Staff).ThenInclude(x => x.Person)
            .Include(x => x.Provider)
            .Include(x => x.TaskInvoices)
            .Where(x => x.Status != InvoiceStatus.Void && x.Status != InvoiceStatus.Invalid)
            .Where(x => x.IsBillingV2 == false)
            .Where(x => x.InvoiceLineItems.Any());

        var invoiceDateRangeQuery = await
            invoiceQuery.Where(x => x.CreatedDateTimeUtc >= fromDate && x.CreatedDateTimeUtc <= toDate)
                .Take(limit)
                .ToArrayAsync();

        var result = mapper.Map<InvoiceListEntry[]>(invoiceDateRangeQuery);

        var additionalResult = await invoiceQuery.Where(x => !invoiceDateRangeQuery.Select(x => x.Id).Contains(x.Id))
            .Where(x => x.TaskId.HasValue && invoiceDateRangeQuery.Select(x => x.TaskId).Contains(x.TaskId))
            .ToArrayAsync();
        var additionalInvoices = mapper.Map<InvoiceListEntry[]>(additionalResult);
        return (result, additionalInvoices);
    }

    public async Task Update(Guid providerId, Invoice invoice)
    {
        var dataModel = await dataContext.Invoices
            .Include(x => x.TaskInvoices)
            .Include(x => x.InvoiceLineItems)
            .Include(x => x.Staff)
            .FirstOrDefaultAsync(x => x.Id == invoice.Id && x.ProviderId == providerId);

        if (dataModel == null)
        {
            Log.Warning("Invoice with id {Id} and providerId {providerId} not found", invoice.Id, providerId);
            return;
        }

        dataModel.Number = invoice.Number;
        dataModel.Status = invoice.Status;
        dataModel.ContactId = invoice.ContactId;
        dataModel.DueDate = invoice.DueDate;
        dataModel.IssueDate = invoice.IssueDate;
        dataModel.ServiceDate = invoice.ServiceDate;
        dataModel.TaskId = invoice.TaskId;
        dataModel.History = invoice.History;
        dataModel.Description = invoice.Description;
        dataModel.CurrencyCode = invoice.CurrencyCode;
        dataModel.TaxExclusivePrice = invoice.TaxExclusivePrice;
        dataModel.TaxPrice = invoice.TaxPrice;
        dataModel.LastUpdatedDateTimeUtc = DateTime.UtcNow;
        dataModel.TaxName = invoice.TaxName;
        dataModel.TaxNumber = invoice.TaxNumber;
        dataModel.POSONumber = invoice.POSONumber;
        dataModel.Title = invoice.Title;
        dataModel.BillToId = invoice.BillToId;
        dataModel.StaffDetail = invoice.StaffDetail;
        dataModel.ProviderDetail = invoice.ProviderDetail;
        dataModel.BillToDetail = invoice.BillToDetail;
        dataModel.ContactDetail = invoice.ContactDetail;
        dataModel.CreditsUsed = invoice.CreditsUsed;
        dataModel.IsBillingV2 = invoice.IsBillingV2;

        if (dataModel.PaymentDate == null && invoice.PaymentDate != null)
        {
            dataModel.PaymentDate = invoice.PaymentDate;
        }

        EntityUtilities.UpdateList(
            dataContext,
            dataModel.InvoiceLineItems,
            invoice.LineItems,
            (x, y) => x.Id == y.Id,
            item =>
            {
                var lineItemDataModel = mapper.Map<InvoiceLineItemDataModel>(item);
                lineItemDataModel.Id = item.Id;
                lineItemDataModel.InvoiceId = invoice.Id;
                lineItemDataModel.BillableItemId = item.BillableItemId;
                return lineItemDataModel;
            },
            (item, updated, existing) =>
            {
                existing.TaxRates ??= [];
                existing.TaxRates.Clear();
                foreach (var taxRate in item.TaxRates ?? [])
                {
                    existing.TaxRates.Add(taxRate);
                }
            }
        );
        dataModel.Staff = invoice.StaffIds.EmptyIfNull().Distinct().Select(staffId => new InvoiceStaffDataModel(dataModel.Id, staffId)).ToList();
        dataModel.TaskInvoices = invoice.TaskIds.EmptyIfNull().Distinct().Select(taskId => new InvoiceTaskDataModel(dataModel.Id, taskId)).ToList();
        dataContext.Invoices.Update(dataModel);
        await dataContext.SaveChangesAsync();
    }


    public async Task Delete(Guid providerId, Guid id)
    {
        var dataModel = await dataContext.Invoices.FirstOrDefaultAsync(x => x.Id == id && x.ProviderId == providerId);

        if (dataModel == null)
            return;

        dataContext.SoftDelete(dataModel);
        await dataContext.SaveChangesAsync();
    }

    public async Task<Invoice> Get(Guid providerId, Guid invoiceId)
    {
        var dataModel = await dataContext.Invoices
            .AsNoTracking()
            .ProjectTo<Invoice>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(x => x.Id == invoiceId && x.ProviderId == providerId);
        return dataModel;
    }

    public async Task<Invoice> Get(Guid invoiceId, bool includeDeleted = false)
    {
        var query = dataContext.Invoices.AsQueryable();
        if (includeDeleted)
        {
            query = query.IgnoreQueryFilters();
        }

        var dataModel = await query
            .AsNoTracking()
            .ProjectTo<Invoice>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(x => x.Id == invoiceId);
        return dataModel;
    }

    public async Task<Invoice[]> Get(Guid providerId, Guid[] ids)
    {
        var dataModel = await dataContext.Invoices
            .AsNoTracking()
            .Where(x => ids.Contains(x.Id) && x.ProviderId == providerId)
            .ProjectTo<Invoice>(mapper.ConfigurationProvider)
            .ToArrayAsync();
        return dataModel;
    }

    public async Task<Invoice[]> Get(Guid[] ids)
    {
        var dataModel = await dataContext.Invoices
            .AsNoTracking()
            .Where(x => ids.Contains(x.Id))
            .ProjectTo<Invoice>(mapper.ConfigurationProvider)
            .ToArrayAsync();
        return dataModel;
    }

    public async Task<Invoice[]> Get(Guid providerId, Guid contactId, InvoiceStatus[] statuses)
    {
        var results = await dataContext.Invoices
            .AsNoTracking()
            .Where(i => i.ProviderId == providerId && i.ContactId == contactId && statuses.Contains(i.Status))
            .ProjectTo<Invoice>(mapper.ConfigurationProvider)
            .ToArrayAsync();
        return results;
    }


    public async Task<InvoiceListEntry[]> GetContactsPaidInvoicesByDateRange(Guid providerId, Guid contactId, DateTime from, DateTime to)
    {
        var dataModels = await dataContext.Invoices
            .AsNoTracking()
            .Where(x => x.ContactId == contactId &&
                        x.ProviderId == providerId &&
                        x.Status == InvoiceStatus.Paid &&
                        x.PaymentDate.HasValue && x.PaymentDate >= from && x.PaymentDate <= to &&
                        x.ServiceReceipts.Count <= 0)
            .Include(x => x.InvoiceLineItems)
            .Include(x => x.InvoicePayment).ThenInclude(x => x.Payment).ThenInclude(x => x.Allocations)
            .Include(x => x.Tasks)
            .Include(x => x.Contact)
            .Include(x => x.BillTo)
            .Include(x => x.Staff).ThenInclude(x => x.Person)
            .ToArrayAsync();
        var models = mapper.Map<InvoiceListEntry[]>(dataModels);
        return models;
    }

    public async Task<InvoiceListEntry> GetInvoiceListEntry(Guid providerId, Guid invoiceId)
    {
        var dataModel = await dataContext.Invoices
            .AsNoTracking()
            .Where(x => x.Id == invoiceId && x.ProviderId == providerId)
            .Include(x => x.InvoiceLineItems)
            .Include(x => x.InvoicePayment).ThenInclude(x => x.Payment).ThenInclude(x => x.Allocations)
            .Include(x => x.Tasks)
            .Include(x => x.Contact)
            .Include(x => x.BillTo)
            .Include(x => x.Staff).ThenInclude(x => x.Person)
            .FirstOrDefaultAsync();
        return mapper.Map<InvoiceListEntry>(dataModel);
    }

    public async Task<InvoiceListEntry> GetInvoiceListEntry(Guid invoiceId)
    {
        // todo merge these two `GetInvoiceListEntry` methods to create a single overloaded method with providerId
        var dataModel = await dataContext.Invoices
            .AsNoTracking()
            .Where(x => x.Id == invoiceId)
            .Include(x => x.InvoiceLineItems)
            .Include(x => x.InvoicePayment).ThenInclude(x => x.Payment).ThenInclude(x => x.Allocations)
            .Include(x => x.Tasks)
            .Include(x => x.Contact)
            .Include(x => x.BillTo)
            .Include(x => x.Staff).ThenInclude(x => x.Person)
            .FirstOrDefaultAsync();
        return mapper.Map<InvoiceListEntry>(dataModel);
    }

    public async Task<Invoice[]> GetTaskInvoices(Guid providerId, Guid[] taskIds, Guid[] assignedStaff)
    {
        var query = dataContext.InvoiceTasks
            .AsNoTracking()
            .Where(x => x.Invoice.ProviderId == providerId && taskIds.Contains(x.TaskId))
            .Select(x => x.Invoice);

        if (assignedStaff?.Any() == true)
        {
            query = query.Where(x => x.Contact.AssignedStaff.Any(s => assignedStaff.Contains(s.PersonId)));
        }

        var models = await query.Distinct().ProjectTo<Invoice>(mapper.ConfigurationProvider).ToArrayAsync();
        return models;
    }

    public async Task<PaginatedResult<InvoiceListEntry>> GetByProviderId(
        bool assignedOnly,
        Guid personId,
        Guid providerId,
        int limit,
        int offset,
        string searchTerm,
        Guid[] staffIds,
        Guid[] contactIds,
        InvoiceStatus? status,
        DateOnly? fromDate = null,
        DateOnly? toDate = null,
        Sorting sorting = null)
    {
        var query = dataContext.Invoices.Where(x => x.ProviderId == providerId);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(x => EF.Functions.ILike(x.Number, $"%{searchTerm}%"));
        }

        if (assignedOnly)
        {
            query = query
                .Join(dataContext.ContactAssignedStaff, inv => inv.ContactId, con => con.ContactId, (inv, con) => new { Invoice = inv, AssignedContact = con })
                .Where(x => x.AssignedContact.PersonId == personId)
                .Select(x => x.Invoice);
        }

        if (staffIds != null && staffIds.Any())
        {
            query = query.Where(invoice => invoice.Staff.Any(staffMember => staffIds.Any(staffId => staffMember.PersonId == staffId)));
        }

        if (contactIds != null && contactIds.Any())
        {
            query = query.Where(invoice => contactIds.Any(id => id == invoice.ContactId));
        }

        if (status.HasValue)
        {
            query = query.Where(x => x.Status == status);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(x => x.IssueDate >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(x => x.IssueDate <= toDate.Value);
        }

        // sort
        if (sorting != null && sorting.HasSort)
        {
            // create new variable so ThenBy can be used
            IOrderedQueryable<InvoiceDataModel> orderedQuery = query.OrderWith(KeySelector(sorting.Primary), sorting.Primary.Direction);

            if (sorting.Secondaries.Any())
            {
                foreach (var secondarySorting in sorting.Secondaries)
                {
                    orderedQuery = orderedQuery.ThenOrderWith(KeySelector(secondarySorting), secondarySorting.Direction);
                }
            }

            // set original query to an ordered query
            // add created date as sort field for ef to return consistent results for common fields
            query = orderedQuery.ThenOrderWith(x => x.CreatedDateTimeUtc, sorting.Primary.Direction);
        }
        else
        {
            // default ordering
            query = query.OrderByDescending(x => x.IssueDate)
                .ThenByDescending(x => x.CreatedDateTimeUtc);
        }

        var totalCount = await query.CountAsync();

        var dataModels = await query
            .AsNoTracking()
            .Include(x => x.InvoiceLineItems)
            .Include(x => x.InvoicePayment)
            .ThenInclude(x => x.Payment)
            .ThenInclude(x => x.Allocations)
            .Include(x => x.InvoicePayment)
            .ThenInclude(x => x.Payment)
            .ThenInclude(x => x.Refunds)
            .Include(x => x.Tasks)
            .Include(x => x.Contact)
            .Include(x => x.BillTo)
            .Include(x => x.Staff)
            .ThenInclude(x => x.Person)
            .Skip(offset)
            .Take(limit + 1)
            .ToArrayAsync();
        var models = mapper.Map<InvoiceListEntry[]>(dataModels);
        //After Map not supported when using ProjectTo
        var paginatedResult = new PaginatedResult<InvoiceListEntry>(
            models.Take(limit).ToArray(),
            new Pagination(limit, offset, dataModels.Length > limit),
            totalCount);

        return paginatedResult;
    }
    

    private Expression<Func<InvoiceDataModel, object>> KeySelector(SortingField sortingField)
    {
        if (sortingField.MatchesField(nameof(InvoiceDataModel.DueDate)))
        {
            return (x) => x.DueDate;
        }

        if (sortingField.MatchesField(nameof(InvoiceDataModel.IssueDate)))
        {
            return (x) => x.IssueDate;
        }

        if (sortingField.MatchesField(nameof(InvoiceDataModel.Number)))
        {
            return (x) => x.Number;
        }

        if (sortingField.MatchesField(nameof(InvoiceDataModel.Status)))
        {
            return (x) => x.Status;
        }

        if (sortingField.MatchesField(nameof(InvoiceDataModel.PaymentDate)))
        {
            return (x) => x.PaymentDate;
        }

        return (x) => sortingField.Field;
    }

    public async Task<InvoicePaymentStatus> GetInvoicePaymentStatus(Guid providerId, Guid personId, bool assignedOnly, DateOnly? fromDate, DateOnly? toDate, string timeZone = null)
    {
        timeZone = string.IsNullOrEmpty(timeZone) ? "UTC" : timeZone;

        var localNow = dateTimeProvider.GetDateTimeUtc().ToTimeZoneDateTime(timeZone).AsUtcKind();
        var from = fromDate.HasValue
            ? fromDate.ToDateTime()
            : localNow.ToFirstDayOfTheMonth();
        var to = toDate.HasValue
            ? toDate.ToDateTime().ToEndOfDay()
            : localNow.ToLastDayOfTheMonth();

        var query = dataContext.Invoices
            .AsNoTracking()
            .Where(x => x.ProviderId == providerId)
            .Where(x =>
                //unpaid filter by due date
                //paid filter by paid date
                ((x.Status != InvoiceStatus.Paid && x.DueDate >= from.ToDateOnly())
                 || (x.Status == InvoiceStatus.Paid && x.PaymentDate >= from.AsZonedDateTimeToUtc(timeZone))) &&
                ((x.Status != InvoiceStatus.Paid && x.DueDate <= to.ToDateOnly())
                 || (x.Status == InvoiceStatus.Paid && x.PaymentDate <= to.AsZonedDateTimeToUtc(timeZone))));

        var overdueStatuses = new[] { InvoiceStatus.Unpaid, InvoiceStatus.Sent };
        var overdue = dataContext.Invoices
            .Where(x => x.ProviderId == providerId && overdueStatuses.Contains(x.Status) &&
                        x.DueDate < localNow.ToDateOnly())
            .Sum(x => x.TaxPrice + x.TaxExclusivePrice);

        var inTransit = dataContext.Invoices
            .Include(x => x.InvoicePayment)
            .ThenInclude(x => x.Payment)
            .Where(x => x.ProviderId == providerId &&
                        x.InvoicePayment.Any(y => y.Payment.PayoutStatus.HasValue &&
                                                  intransitStatuses.Contains(y.Payment.PayoutStatus.Value)))
            .SelectMany(x => x.InvoicePayment)
            .Sum(x => x.Payment.PayoutAmount);

        if (assignedOnly)
        {
            query = query
                .Join(dataContext.ContactAssignedStaff, inv => inv.ContactId, con => con.ContactId, (inv, con) => new { Invoice = inv, AssignedContact = con })
                .Where(x => x.AssignedContact.PersonId == personId)
                .Select(x => x.Invoice);
        }

        var dataModels = await query
            .GroupBy(x => x.Status)
            .Select(x => new
            {
                Status = x.Key,
                Total = x.Sum(grouping => grouping.TaxPrice + grouping.TaxExclusivePrice)
            })
            .ToDictionaryAsync(x => x.Status, x => x.Total);

        return new InvoicePaymentStatus
        {
            Unpaid = dataModels.GetValueOrDefault(InvoiceStatus.Unpaid) + dataModels.GetValueOrDefault(InvoiceStatus.Sent) + dataModels.GetValueOrDefault(InvoiceStatus.Processing),
            Paid = dataModels.GetValueOrDefault(InvoiceStatus.Paid),
            Overdue = overdue,
            InTransit = inTransit ?? 0M
        };
    }

    public async Task<string> GetSeedInvoiceNumber(Guid providerId)
    {
        var invalid = true;
        var i = 0;
        var invoiceNumber = string.Empty;

        while (invalid && i < 5)
        {
            var range = Convert.ToInt32(3 * Math.Pow(10, i));
            var latestNumber = await GetLastInvoiceNumber(providerId, range);
            if (string.IsNullOrEmpty(latestNumber))
            {
                return "1".PadLeft(6, '0');
            }

            invoiceNumber = Next(latestNumber, 1);
            invalid = await DoesInvoiceNumberExist(providerId, invoiceNumber);
            invoiceNumber = invalid ? string.Empty : invoiceNumber;
            i++;
        }

        if (invalid)
        {
            Log.Error("Failed to generate invoice number for provider {ProviderId}", providerId);
        }
        else
        {
            Log.Information("Generated invoice number {InvoiceNumber}", invoiceNumber);
        }

        return invoiceNumber;
    }

    public async Task<bool> DoesInvoiceNumberExist(Guid providerId, string number, bool includeDeleted = true)
    {
        var query = dataContext.Invoices.AsNoTracking().Where(x => x.ProviderId == providerId && x.Number == number);
        if (includeDeleted)
        {
            query = query.IgnoreQueryFilters();
        }

        return await query.AnyAsync();
    }


    public async Task UpdateStatus(Guid providerId, Invoice invoice)
    {
        var entity = await dataContext.Invoices
            .Where(x => x.Id == invoice.Id && x.ProviderId == providerId)
            .FirstOrDefaultAsync();

        if (entity is null) return;

        entity.Status = invoice.Status;
        entity.VoidedDate = invoice.VoidedDate;
        entity.History = invoice.History;
        entity.LastUpdatedDateTimeUtc = DateTime.UtcNow;
        await dataContext.SaveChangesAsync();
    }


    private async Task<string> GetSeedInvoiceNumber(IEnumerable<Invoice> invoices)
    {
        if (!invoices.Any(x => string.IsNullOrEmpty(x.Number)))
            return invoices.FirstOrDefault().Number;

        var providerId = invoices.FirstOrDefault()?.ProviderId;
        return await GetSeedInvoiceNumber(providerId.Value);
    }

    private string Next(string value, int increment)
    {
        return Regex.Replace(value.Trim(), "[0-9]+$", match => (long.Parse(match.Value) + increment).ToString($"D{match.Length}"))
            .PadLeft(6, '0');
    }

    public async Task<ResourceName> GetResourceName(Guid id)
    {
        var values = await dataContext.Invoices
            .AsNoTracking()
            .Select(c => new
            {
                ProviderId = c.ProviderId,
                ContactId = c.ContactId,
                Id = c.Id
            })
            .FirstOrDefaultAsync(c => c.Id == id);

        if (values == null)
            return null;

        return new ResourceNameBuilder()
            .BuildInvoice(values.ProviderId, values.ContactId, values.Id);
    }

    private async Task<string> GetLastInvoiceNumber(Guid providerId, int range = 3)
    {
        // Finds the 'highest' invoice number from the last three invoices.
        // highest is defined by
        // - has the most characters
        // - then by alphabetical descending
        // Notes: Postgres sorting means invoice number preferences are lowercase > uppercase > numeric
        // eg:  inv123 > INV123 > 000123
        var seedInvoiceNumber = await dataContext.Invoices
            .IgnoreQueryFilters()
            .Where(x => x.ProviderId == providerId)
            .OrderByDescending(i => i.CreatedDateTimeUtc)
            .Take(range)
            .GroupBy(x => x.Number.Length, x => x.Number)
            .OrderByDescending(x => x.Key)
            .Take(1)
            .Select(x => x.Max())
            .FirstOrDefaultAsync();

        return seedInvoiceNumber?.Trim();
    }

    public async Task<bool> IsActive(Guid providerId, DateRange dateRange)
    {
        if (providerId == Guid.Empty) return false;
        var count = await dataContext.Invoices
            .Where(x => x.ProviderId == providerId && x.CreatedDateTimeUtc >= dateRange.FromDate && x.CreatedDateTimeUtc <= dateRange.ToDate)
            .CountAsync();

        return count > 0;
    }

    public async Task DeleteByContactId(Guid providerId, Guid contactId)
    {
        await dataContext.Invoices
            .Where(x => x.ProviderId == providerId && x.ContactId == contactId && !x.DeletedAtUtc.HasValue)
            .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.DeletedAtUtc, DateTime.UtcNow));
    }

    public async Task Restore(Guid providerId, Guid invoiceId, Guid? toContactId)
    {
        var dataModel = await dataContext.Invoices.IgnoreQueryFilters().FirstOrDefaultAsync(x => x.ProviderId == providerId && x.Id == invoiceId);

        if (dataModel == null)
            return;

        dataModel.DeletedAtUtc = null;

        if (toContactId.HasValue)
        {
            dataModel.ContactId = toContactId.Value;
        }

        await dataContext.SaveChangesAsync();
    }

    public async Task RestoreByContactId(Guid providerId, Guid contactId)
    {
        //prevent trashed invoices from being restored
        var trashInvoiceIds = dataContext.TrashItems.Where(x => x.ProviderId == providerId && x.FromContactId == contactId && x.Type == TrashType.Invoice)
            .Select(x => x.EntityId).ToArray();

        await dataContext.Invoices
            .IgnoreQueryFilters()
            .Where(x => x.ProviderId == providerId && x.ContactId == contactId && x.DeletedAtUtc.HasValue && !trashInvoiceIds.Contains(x.Id))
            .ExecuteUpdateAsync(setter => setter.SetProperty(x => x.DeletedAtUtc, (DateTime?)null));
    }

    public async Task<InvoiceListEntry> GetByPaymentId(Guid paymentId)
    {
        var dataModel = await dataContext.Invoices
            .AsNoTracking()
            .Where(x => x.InvoicePayment.Any(y => y.PaymentId == paymentId))
            .Include(x => x.InvoiceLineItems)
            .Include(x => x.InvoicePayment).ThenInclude(x => x.Payment).ThenInclude(x => x.Allocations)
            .Include(x => x.Tasks)
            .Include(x => x.Contact)
            .Include(x => x.BillTo)
            .Include(x => x.Staff).ThenInclude(x => x.Person)
            .FirstOrDefaultAsync();
        return mapper.Map<InvoiceListEntry>(dataModel);
    }

    public async Task MoveTaskInvoice(Guid providerId, Guid contactId, Guid previousTaskId, Guid newTaskId)
    {
        var invoices = await dataContext.Invoices
            .Include(x => x.TaskInvoices)
            .Where(x => x.ProviderId == providerId && x.ContactId == contactId)
            .Where(x => x.TaskId == previousTaskId || x.TaskInvoices.Any(ti => ti.TaskId == previousTaskId))
            .ToListAsync();

        foreach (var invoice in invoices)
        {
            if (invoice.TaskId == previousTaskId)
            {
                // update the primary task id for backwards compatibility
                invoice.TaskId = newTaskId;
            }

            // update the N:N task id as well.
            foreach (var taskInvoice in invoice.TaskInvoices.Where(x => x.TaskId == previousTaskId))
            {
                dataContext.Remove(taskInvoice);
            }

            invoice.TaskInvoices.Add(new InvoiceTaskDataModel(invoice.Id, newTaskId));
        }

        await dataContext.SaveChangesAsync();
    }

    public async Task<Invoice[]> MoveInvoicesToNewContact(Guid providerId,
        Contact newContact,
        Guid[] fromContactIds
    )
    {
        var invoices = await dataContext.Invoices
            .Where(x => x.ProviderId == providerId &&
                        (fromContactIds.Contains(x.ContactId) || (x.BillToId.HasValue && fromContactIds.Contains(x.BillToId.Value))))
            .Include(x => x.InvoiceLineItems)
            .ToListAsync();

        foreach (var invoice in invoices)
        {
            // contact to archive is the bill to contact
            if (invoice.BillToId.HasValue && fromContactIds.Contains(invoice.BillToId.Value))
            {
                invoice.BillToId = newContact.Id;
            }

            // contact to archive is the contact
            if (fromContactIds.Contains(invoice.ContactId))
            {
                invoice.ContactId = newContact.Id;
            }
        }

        await dataContext.SaveChangesAsync();

        return mapper.Map<Invoice[]>(invoices);
    }

    public async Task<TaskContactInvoice[]> GetTaskContactInvoices(Guid providerId, Guid taskId, Guid contactId)
    {
        var entities = await dataContext.Invoices
            .Where(x => x.ProviderId == providerId &&
                        x.Tasks.Any(y => y.Id == taskId) &&
                        x.ContactId == contactId)
            .ProjectTo<TaskContactInvoice>(mapper.ConfigurationProvider)
            .ToArrayAsync();
        return entities;
    }

    public async Task AttachedBillableItemAndSwitchToV2Billing(Guid invoiceId, IEnumerable<InvoiceLineItem> items)
    {
        var invoice = await dataContext.Invoices.Include(x => x.InvoiceLineItems).FirstAsync(x => x.Id == invoiceId);
        invoice.IsBillingV2 = true;
        foreach (var lineItem in invoice.InvoiceLineItems)
        {
            lineItem.BillableItemId = items.Single(x => x.Id == lineItem.Id).BillableItemId;
        }

        await dataContext.SaveChangesAsync();
    }

    public async Task<Invoice[]> GetInvoicesByBillableIds(params Guid[] billableIds)
    {
        var affectedInvoices = await dataContext.InvoiceLineItems
            .AsNoTracking()
            .Include(x => x.BillableItem)
            .ThenInclude(x => x.Billable)
            .Where(x => billableIds.Contains(x.BillableItem.BillableId))
            .Select(x => x.InvoiceId)
            .Distinct()
            .ToArrayAsync();

        var result = await dataContext.Invoices
            .AsNoTracking()
            .Where(x => affectedInvoices.Contains(x.Id))
            .ProjectTo<Invoice>(mapper.ConfigurationProvider)
            .ToArrayAsync();

        return result;
    }
}
