using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Repositories.Insurance;
using carepatron.infra.sql.Models.Insurance;
using Microsoft.EntityFrameworkCore;

namespace carepatron.infra.sql.Repositories;

public class InsuranceRemittancePaymentsRepository(DataContext dataContext, IMapper mapper) : IInsuranceRemittancePaymentsRepository
{
    public async Task<InsuranceRemittancePayment> Create(InsuranceRemittancePayment insuranceRemittancePayment, CancellationToken cancellationToken = default)
    {
        var dataModel = mapper.Map<InsuranceRemittancePaymentDataModel>(insuranceRemittancePayment);
        await dataContext.InsuranceRemittancePayments.AddAsync(dataModel, cancellationToken);
        await dataContext.SaveChangesAsync(cancellationToken);
        return insuranceRemittancePayment;
    }

    public async Task<InsuranceRemittancePayment> GetByReference(Guid providerId, string paymentReference, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(paymentReference))
        {
            throw new ArgumentNullException(nameof(paymentReference));
        }

        var result = await dataContext.InsuranceRemittancePayments
            .Where(x => x.ProviderId == providerId && x.InsuranceRemittanceAdvice.PaymentReference == paymentReference)
            .ProjectTo<InsuranceRemittancePayment>(mapper.ConfigurationProvider)
            .FirstOrDefaultAsync(cancellationToken);

        return result;
    }
}
