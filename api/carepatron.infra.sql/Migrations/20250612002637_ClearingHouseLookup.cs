﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace carepatron.infra.sql.Migrations
{
    /// <inheritdoc />
    public partial class ClearingHouseLookup : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "clearing_house_id_lookups",
                schema: "carepatron",
                columns: table => new
                {
                    identity = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    entity_id = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_clearing_house_id_lookups", x => x.identity);
                });

            migrationBuilder.CreateIndex(
                name: "ix_clearing_house_id_lookups_entity_id",
                schema: "carepatron",
                table: "clearing_house_id_lookups",
                column: "entity_id",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "clearing_house_id_lookups",
                schema: "carepatron");
        }
    }
}
