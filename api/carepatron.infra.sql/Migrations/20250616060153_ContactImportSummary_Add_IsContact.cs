﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace carepatron.infra.sql.Migrations
{
    /// <inheritdoc />
    public partial class ContactImportSummary_Add_IsContact : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "is_contact",
                schema: "carepatron",
                table: "contact_import_summaries",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "is_contact",
                schema: "carepatron",
                table: "contact_import_summaries");
        }
    }
}
