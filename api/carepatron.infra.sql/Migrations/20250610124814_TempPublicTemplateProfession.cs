﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace carepatron.infra.sql.Migrations
{
    /// <inheritdoc />
    public partial class TempPublicTemplateProfession : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "temp_public_template_professions",
                schema: "carepatron",
                columns: table => new
                {
                    public_template_id = table.Column<Guid>(type: "uuid", nullable: false),
                    profession = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_temp_public_template_professions", x => new { x.public_template_id, x.profession });
                });

            migrationBuilder.CreateIndex(
                name: "ix_temp_public_template_professions_profession",
                schema: "carepatron",
                table: "temp_public_template_professions",
                column: "profession");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "temp_public_template_professions",
                schema: "carepatron");
        }
    }
}
