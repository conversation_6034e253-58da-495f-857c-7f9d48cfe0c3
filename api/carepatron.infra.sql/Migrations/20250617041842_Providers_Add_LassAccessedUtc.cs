﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace carepatron.infra.sql.Migrations
{
    /// <inheritdoc />
    public partial class Providers_Add_LassAccessedUtc : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "last_accessed_utc",
                schema: "carepatron",
                table: "providers",
                type: "timestamp with time zone",
                nullable: false,
                defaultValueSql: "NOW() AT TIME ZONE 'UTC'");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "last_accessed_utc",
                schema: "carepatron",
                table: "providers");
        }
    }
}
