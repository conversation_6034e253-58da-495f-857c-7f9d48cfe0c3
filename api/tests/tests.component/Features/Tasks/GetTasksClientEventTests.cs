﻿using Bogus;
using carepatron.core.Application.ConnectedApps.Models;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Users.Models;
using carepatron.core.Models.Invoices;
using carepatron.core.Models.Pagination;
using carepatron.infra.sql.Models.Tasks;
using Microsoft.EntityFrameworkCore;
using Moq;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Builders.DataModels;
using tests.common.Extensions;
using tests.common.Mocks;
using tests.component.Builders;
using tests.component.Extensions;
using carepatron.core.Models.Permissions;
using carepatron.core.Application.Calendar.Models;
using carepatron.infra.sql.Models.ConnectedApp;
using carepatron.core.Application.ConnectedApps.Models.Settings;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Workspace.Locations.Models;
using carepatron.infra.sql.Models.Insurance;

namespace tests.component.Features.Tasks
{
    [Trait(nameof(CodeOwner), CodeOwner.TasksAndScheduling)]
    public class GetTasksClientEventTests : BaseTestClass
    {
        public GetTasksClientEventTests(ComponentTestFixture testFixture) : base(testFixture)
        {
        }

        [Fact]
        public async Task GetTasks_ClientEvent_ByProvider_Test()
        {
            var contact1 = ContactModelBuilder.Any().WithProviderId(ProviderId).Create();
            var contact2 = ContactModelBuilder.Any().WithProviderId(ProviderId).Create();

            var task1 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId).Create();
            var task2 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id, contact2.Id).WithStaff(PersonId).WithProvider(ProviderId).Create();

            var note1 = NoteModelBuilder.Any().WithProviderId(ProviderId).WithTaskId(task1.Id).WithContactId(contact1.Id).WithCreatedByPersonId(PersonId).Create();
            var note2 = NoteModelBuilder.Any().WithProviderId(ProviderId).WithTaskId(task2.Id).WithContactId(contact1.Id).WithCreatedByPersonId(PersonId).Create();
            var note3 = NoteModelBuilder.Any().WithProviderId(ProviderId).WithTaskId(task2.Id).WithContactId(contact2.Id).WithCreatedByPersonId(PersonId).Create();

            await DataContext.AddRangeAsync(contact1, contact2, task1, task2, note1, note2, note3);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();

            // assert correct count
            result.Items.Should().HaveCount(2);

            var actualTask1 = result.Items.First(x => x.Id == task1.Id);
            var actualTask2 = result.Items.First(x => x.Id == task2.Id);

            // assert basic properties
            actualTask1.Should().BeEquivalentTo(task1, opt => opt
                .ExcludingMissingMembers()
                .Excluding(x => x.Contacts)
                .Excluding(x => x.Staff)
                .Excluding(x => x.Notes)
                .Excluding(x => x.LocationsSnapshot)
                .Excluding(x => x.ExternalContacts)
                .Excluding(x => x.Invoices));

            actualTask2.Should().BeEquivalentTo(task2, opt => opt
                .ExcludingMissingMembers()
                .Excluding(x => x.Contacts)
                .Excluding(x => x.Staff)
                .Excluding(x => x.Notes)
                .Excluding(x => x.LocationsSnapshot)
                .Excluding(x => x.ExternalContacts)
                .Excluding(x => x.Invoices));

            // assert Contact contains props from the actual Contact and the Task i.e RecipientStatus
            actualTask1.Contacts.First(x => x.Id == contact1.Id).Should().BeEquivalentTo(contact1, opt => opt.Excluding(x => x.Status).ExcludingMissingMembers());
            actualTask1.Contacts.First(x => x.Id == contact1.Id).Should().BeEquivalentTo(task1.Contacts.First(), opt => opt.Excluding(x => x.AttendeeStatusId).ExcludingMissingMembers());
            actualTask1.Notes.Should().BeNullOrEmpty();
            actualTask1.Invoices.Should().BeNullOrEmpty();

            actualTask2.Contacts.Should().BeEquivalentTo([contact1, contact2], opt => opt.Excluding(x => x.Status).ExcludingMissingMembers());
            actualTask2.Invoices.Should().BeNullOrEmpty();
        }

        [Fact]
        public async Task GetTasks_ClientEvent_BasicDateRanges_Test()
        {
            var tomorrowAt12pm = new DateTime(DateTime.Now.AddDays(1).Date.Ticks).AddHours(12).ToUniversalTime();
            var tomorrowAt1pm = new DateTime(DateTime.Now.AddDays(1).Date.Ticks).AddHours(13).ToUniversalTime();

            var nextWeekAt12pm = new DateTime(DateTime.Now.AddDays(7).Date.Ticks).AddHours(12).ToUniversalTime();
            var nextWeekAt1pm = new DateTime(DateTime.Now.AddDays(7).Date.Ticks).AddHours(13).ToUniversalTime();

            var nextMonthAt12pm = new DateTime(DateTime.Now.AddMonths(1).Date.Ticks).AddHours(12).ToUniversalTime();
            var nextMonthAt1pm = new DateTime(DateTime.Now.AddMonths(1).Date.Ticks).AddHours(13).ToUniversalTime();

            var contact1 = ContactModelBuilder.Any().WithProviderId(ProviderId).Create();

            var tomorrowsTask = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithStartDate(tomorrowAt12pm)
                .WithEndDate(tomorrowAt1pm)
                .Create();
            var nextWeeksTask = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithStartDate(nextWeekAt12pm)
                .WithEndDate(nextWeekAt1pm)
                .Create();
            var nextMonthsTask = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithStartDate(nextMonthAt12pm)
                .WithEndDate(nextMonthAt1pm)
                .Create();

            await DataContext.AddRangeAsync(contact1, tomorrowsTask, nextWeeksTask, nextMonthsTask);
            await DataContext.SaveChangesAsync();

            // get all tasks from today until 14 days
            var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                    $"api/providers/{ProviderId}/tasks?fromStartDate={DateTime.UtcNow.ToString("O")}&toEndDate={DateTime.UtcNow.AddDays(14).ToString("O")}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            // assert the correct tasks are returned
            var ids = result.Items.Select(x => x.Id).Should()
                .NotContain(nextMonthsTask.Id).And
                .BeEquivalentTo(new Guid[] { tomorrowsTask.Id, nextWeeksTask.Id });
        }

        /*
         * Represenation of returning date ranges
         *
         * --------------------|Start---------------------|End--------------------------
         *   |--notInDate--| |--date1--|    |--date2--|  |--date3--|   |--notInDate2--|
         *                  |--------------date4--------------|
         */
        [Fact]
        public async Task GetTasks_ClientEvent_AdvancedDateRanges_Test()
        {
            var contact1 = ContactModelBuilder.Any().WithProviderId(ProviderId).Create();

            var start = DateTime.Parse("2000-01-01T00:00:00Z").ToUniversalTime();
            var end = DateTime.Parse("2000-01-31T23:59:59Z").ToUniversalTime();

            var notInDate = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("notInDate")
                .WithStartDate(start.AddDays(-2).ToUniversalTime())
                .WithEndDate(start.AddDays(-2).AddHours(1).ToUniversalTime())
                .Create();
            var date1 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("date1")
                .WithStartDate(start.AddHours(-1).ToUniversalTime())
                .WithEndDate(start.AddHours(1).ToUniversalTime())
                .Create();
            var date2 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("date2")
                .WithStartDate(start.AddDays(2).ToUniversalTime())
                .WithEndDate(start.AddDays(2).AddHours(1).ToUniversalTime())
                .Create();
            var date3 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("date3")
                .WithStartDate(end.AddHours(-1).ToUniversalTime())
                .WithEndDate(end.AddHours(1).ToUniversalTime())
                .Create();
            var date4 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("date4")
                .WithStartDate(start.AddHours(-1).ToUniversalTime())
                .WithEndDate(end.AddHours(1).ToUniversalTime())
                .Create();
            var notInDate2 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("notInDate2")
                .WithStartDate(end.AddHours(1).ToUniversalTime())
                .WithEndDate(end.AddHours(2).ToUniversalTime())
                .Create();

            await DataContext.AddRangeAsync(contact1, notInDate, date1, date2, date3, date4, notInDate2);
            await DataContext.SaveChangesAsync();

            // get all tasks from today until 14 days
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?&fromStartDate={start.ToString("O")}&toEndDate={end.ToString("O")}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();

            // assert the correct tasks are returned
            var ids = result.Items.Select(x => x.Id);

            ids.Should().Contain(date1.Id);
            ids.Should().Contain(date2.Id);
            ids.Should().Contain(date3.Id);
            ids.Should().Contain(date4.Id);

            result.Items.Should().HaveCount(4);
        }

        /*
         * Represenation of returning date ranges
         *
         * --------------------|Start---------------------|End--------------------------
         *   |--notInDate--| |--date1--|    |--date2--|  |--date3--|   |--notInDate2--|
         *                  |--------------date4--------------|
         *                  |--------------date5-------------->
         */
        [Fact]
        public async Task GetTasks_ClientEvent_Recurrence_AdvancedDateRanges_Test()
        {
            var contact1 = ContactModelBuilder.Any().WithProviderId(ProviderId).Create();

            var start = DateTime.Parse("2000-01-07T00:00:00Z").ToUniversalTime();
            var end = DateTime.Parse("2000-01-14T23:59:59Z").ToUniversalTime();

            var notInDate = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("notInDate")
                .WithStartDate(start.AddDays(-7).ToUniversalTime())
                .WithEndDate(start.AddDays(-7).AddHours(1).ToUniversalTime())
                .WithRRule("FREQ=DAILY")
                .WithOccurenceEndDate(start.AddDays(-1).AddHours(1).ToUniversalTime())
                .Create();
            var date1 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("date1")
                .WithStartDate(start.AddDays(-7).ToUniversalTime())
                .WithEndDate(start.AddDays(-7).AddHours(1).ToUniversalTime())
                .WithRRule("FREQ=DAILY")
                .WithOccurenceEndDate(start.AddDays(1).AddHours(1).ToUniversalTime())
                .Create();
            var date2 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("date2")
                .WithStartDate(start.AddDays(1).ToUniversalTime())
                .WithEndDate(start.AddDays(1).AddHours(1).ToUniversalTime())
                .WithRRule("FREQ=DAILY")
                .WithOccurenceEndDate(start.AddDays(2).AddHours(1).ToUniversalTime())
                .Create();
            var date3 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("date3")
                .WithStartDate(end.AddHours(-1).ToUniversalTime())
                .WithEndDate(end.ToUniversalTime())
                .WithRRule("FREQ=DAILY")
                .WithOccurenceEndDate(end.AddDays(1).AddHours(1).ToUniversalTime())
                .Create();
            var date4 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("date4")
                .WithStartDate(start.AddDays(-8).ToUniversalTime())
                .WithEndDate(start.AddDays(-8).AddHours(1).ToUniversalTime())
                .WithRRule("FREQ=WEEKLY")
                .WithOccurenceEndDate(end.AddDays(1).AddHours(1).ToUniversalTime())
                .Create();
            var date5 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("date5")
                .WithStartDate(start.AddDays(-8).ToUniversalTime())
                .WithEndDate(start.AddDays(-8).AddHours(1).ToUniversalTime())
                .WithRRule("FREQ=WEEKLY")
                // no end
                .WithOccurenceEndDate(null)
                .Create();
            var notInDate2 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId)
                .WithTitle("notInDate2")
                .WithStartDate(end.AddDays(1).ToUniversalTime())
                .WithEndDate(end.AddDays(1).AddHours(1).ToUniversalTime())
                .WithRRule("FREQ=DAILY")
                .WithOccurenceEndDate(end.AddDays(7).AddHours(1).ToUniversalTime())
                .Create();

            await DataContext.AddRangeAsync(contact1, notInDate, date1, date2, date3, date4, date5, notInDate2);
            await DataContext.SaveChangesAsync();

            // get all tasks from today until 14 days
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?fromStartDate={start.ToString("O")}&toEndDate={end.ToString("O")}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();

            // assert the correct tasks are returned
            var titles = result.Items.Select(x => x.Title);

            titles.Should().Contain(date1.Title);
            titles.Should().Contain(date2.Title);
            titles.Should().Contain(date3.Title);
            titles.Should().Contain(date4.Title);
            titles.Should().Contain(date5.Title);

            result.Items.Should().HaveCount(5);
        }

        [Fact]
        public async Task GetTasks_ClientEvent_ByProvider_And_Staff_Test()
        {
            var otherStaff = PersonModelBuilder.Any().Create();
            var otherStaffRecord = ProviderStaffModelBuilder.Any().WithPersonId(otherStaff.Id).WithProviderId(ProviderId).Create();

            var contact1 = ContactModelBuilder.Any().WithProviderId(ProviderId).Create();
            var contact2 = ContactModelBuilder.Any().WithProviderId(ProviderId).Create();

            var task1 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId).Create();
            var task2 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact2.Id).WithStaff(otherStaff.Id).WithProvider(ProviderId).Create();

            await DataContext.AddRangeAsync(contact1, contact2, task1, task2, otherStaff, otherStaffRecord);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();

            var actualTask1 = result.Items.First(x => x.Id == task1.Id);

            // assert only one staff's task are returned
            result.Items.Should().HaveCount(1);

            // assert basic properties
            actualTask1.Should().BeEquivalentTo(task1, opt => opt
                .ExcludingMissingMembers()
                .Excluding(x => x.Contacts)
                .Excluding(x => x.Staff)
                .Excluding(x => x.Invoices)
                .Excluding(x => x.LocationsSnapshot)
                .Excluding(x => x.ExternalContacts)
                .Excluding(x => x.Notes));

            // assert Contact contains props from the actual Contact and the Task i.e RecipientStatus
            actualTask1.Contacts.First().Should().BeEquivalentTo(contact1, opt => opt.Excluding(x => x.Status).ExcludingMissingMembers());
            actualTask1.Contacts.First().Should().BeEquivalentTo(task1.Contacts.First(), opt => opt.Excluding(x => x.AttendeeStatusId).ExcludingMissingMembers());
        }

        [Fact]
        public async Task GetTasks_ClientEvent_ByProvider_And_Contacts_Test()
        {
            var contact1 = ContactModelBuilder.Any().WithProviderId(ProviderId).Create();
            var contact2 = ContactModelBuilder.Any().WithProviderId(ProviderId).Create();

            var task1 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId).Create();
            var task2 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact2.Id).WithStaff(PersonId).WithProvider(ProviderId).Create();

            await DataContext.AddRangeAsync(contact1, contact2, task1, task2);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?contactId={contact1.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();

            var actualTask1 = result.Items.First(x => x.Id == task1.Id);

            // assert only one staff's task are returned
            result.Items.Should().HaveCount(1);

            // assert basic properties
            actualTask1.Should().BeEquivalentTo(task1, opt => opt
                .ExcludingMissingMembers()
                .Excluding(x => x.Contacts)
                .Excluding(x => x.Staff)
                .Excluding(x => x.Invoices)
                .Excluding(x => x.LocationsSnapshot)
                .Excluding(x => x.ExternalContacts)
                .Excluding(x => x.Notes));

            // assert Contact contains props from the actual Contact and the Task i.e RecipientStatus
            actualTask1.Contacts.First().Should().BeEquivalentTo(contact1, opt => opt.Excluding(x => x.Status)
                .ExcludingMissingMembers());
            actualTask1.Contacts.First().Should().BeEquivalentTo(task1.Contacts.First(), opt => opt
                .Excluding(x => x.AttendeeStatusId)
                .ExcludingMissingMembers());
        }

        [Fact]
        public async Task GetTasks_ClientEvent_ByProvider_And_Type_Test()
        {
            var contact1 = ContactModelBuilder.Any().WithProviderId(ProviderId).Create();
            var contact2 = ContactModelBuilder.Any().WithProviderId(ProviderId).Create();

            var task1 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact1.Id).WithStaff(PersonId).WithProvider(ProviderId).Create();
            var task2 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact2.Id).WithStaff(PersonId).WithProvider(ProviderId).Create();
            var task3 = TaskDataModelBuilder.AnyStandardTask().WithContacts(contact2.Id).WithStaff(PersonId).WithProvider(ProviderId).Create();

            await DataContext.AddRangeAsync(contact1, contact2, task1, task2, task3);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?type={TaskType.ClientEvent}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();

            // assert correct count
            result.Items.Should().HaveCount(2);

            var actualTask1 = result.Items.First(x => x.Id == task1.Id);
            var actualTask2 = result.Items.First(x => x.Id == task2.Id);

            // assert basic properties
            actualTask1.Should().BeEquivalentTo(task1, opt => opt
                .ExcludingMissingMembers()
                .Excluding(x => x.Contacts)
                .Excluding(x => x.Staff)
                .Excluding(x => x.Invoices)
                .Excluding(x => x.LocationsSnapshot)
                .Excluding(x => x.ExternalContacts)
                .Excluding(x => x.Notes));

            actualTask2.Should().BeEquivalentTo(task2, opt => opt
                .ExcludingMissingMembers()
                .Excluding(x => x.Contacts)
                .Excluding(x => x.Staff)
                .Excluding(x => x.Invoices)
                .Excluding(x => x.LocationsSnapshot)
                .Excluding(x => x.ExternalContacts)
                .Excluding(x => x.Notes));

            // assert Contact contains props from the actual Contact and the Task i.e RecipientStatus
            actualTask1.Contacts.First().Should().BeEquivalentTo(contact1, opt => opt.Excluding(x => x.Status).ExcludingMissingMembers());
            actualTask1.Contacts.First().Should().BeEquivalentTo(task1.Contacts.First(), opt => opt.Excluding(x => x.AttendeeStatusId).ExcludingMissingMembers());

            actualTask2.Contacts.First().Should().BeEquivalentTo(contact2, opt => opt.Excluding(x => x.Status).ExcludingMissingMembers());
            actualTask2.Contacts.First().Should().BeEquivalentTo(task2.Contacts.First(), opt => opt.Excluding(x => x.AttendeeStatusId).ExcludingMissingMembers());
        }

        [Theory]
        [InlineData(TaskContactStatus.Unconfirmed)]
        [InlineData(TaskContactStatus.Confirmed)]
        [InlineData(TaskContactStatus.Cancelled)]
        [InlineData(TaskContactStatus.DidNotAttend)]
        [InlineData(TaskContactStatus.Attended)]
        public async Task GetTasks_ClientEvent_AppointmentStatusFilter_Test(TaskContactStatus status)
        {
            var faker = new Faker();
            var contacts = new ContactFaker(ProviderId)
                .Generate(5);

            await DataContext.AddRangeAsync(contacts.ToDataModel());

            var tasks = new TaskFaker()
                .RuleFor(_ => _.Type, TaskType.ClientEvent)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .Generate(10)
                .ToDataModel()
                .ToList();

            var taskContacts = contacts.Select(x => x.ToTaskContact(faker.PickRandom<TaskContactStatus>())).ToList();
            tasks.ForEach(x => x.WithContact(taskContacts.Select(y => y.ToDataModel(x.Id)).ToList()));

            await DataContext.AddRangeAsync(tasks);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?appointmentStatus={status}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            var expected = tasks.Where(x => x.Contacts.Any(y => y.TaskContactStatus == status)).ToArray();
            result.Items.Should().HaveCount(expected.Length);
            result.Items.Should().BeEquivalentTo(expected, opt => opt
                .Excluding(x => x.ItemsSnapshot)
                .Excluding(x => x.Notes)
                .Excluding(x => x.Staff)
                .Excluding(x => x.Invoices)
                .Excluding(x => x.LocationsSnapshot)
                .Excluding(x => x.ExternalContacts)
                .Excluding(x => x.Contacts)
                .ExcludingMissingMembers()
                .UsingSimpleDateTimePrecision());

            foreach (var task in expected)
            {
                var actualContacts = result.Items.First(x => x.Id == task.Id).Contacts;
                actualContacts.Should().BeEquivalentTo(task.Contacts, opt => opt
                    .Excluding(x => x.AttendeeStatusId)
                    .ExcludingMissingMembers());
            }
        }

        [Theory]
        [InlineData(InvoiceStatus.Invalid)]
        [InlineData(InvoiceStatus.Unpaid)]
        [InlineData(InvoiceStatus.Sent)]
        [InlineData(InvoiceStatus.Paid)]
        [InlineData(InvoiceStatus.Void)]
        public async Task GetTasks_ClientEvent_InvoiceStatusFilter_Test(InvoiceStatus status)
        {
            var tasks = new TaskFaker()
                .RuleFor(_ => _.Type, TaskType.ClientEvent)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .Generate(10);

            DataContext.AddRange(tasks.ToDataModel());

            foreach (var task in tasks)
            {
                var contact = new ContactFaker(ProviderId)
                    .Generate();

                var invoice = new InvoiceFaker(ProviderId)
                    .RuleFor(x => x.ContactId, contact.Id)
                    .RuleFor(x => x.Status, f => f.PickRandom<InvoiceStatus>())
                    .RuleFor(x => x.TaskId, task.Id)
                    .Generate();

                task.Invoices = new SimpleInvoice[] { invoice.ToSimpleInvoice() };

                DataContext.AddRange(
                    contact.ToDataModel(),
                    invoice.ToDataModel());
            }

            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?invoiceStatus={status}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            var expectedTasks = tasks.Where(tasks => tasks.Invoices.Any(i => i.Status == status)).ToList();

            result.Items.Should().HaveCount(expectedTasks.Count);
            result.Items.Should().BeEquivalentTo(expectedTasks,
                opt => opt.UsingSimpleDateTimePrecision()
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.InvoicesReference)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.TaskInvoicesReference)
                    .Excluding(x => x.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .Excluding(x => x.ClaimsReference));
        }

        [Fact]
        public async Task GetTasks_ClientEvent_NoInvoiceFilter_Test()
        {
            var invoicedTasks = new TaskFaker()
                .RuleFor(_ => _.Type, TaskType.ClientEvent)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .Generate(3);

            DataContext.AddRange(invoicedTasks.ToDataModel());

            foreach (var task in invoicedTasks)
            {
                var contact = new ContactFaker(ProviderId)
                    .Generate();

                var invoice = new InvoiceFaker(ProviderId)
                    .RuleFor(x => x.ContactId, contact.Id)
                    .RuleFor(x => x.Status, f => f.PickRandom<InvoiceStatus>())
                    .RuleFor(x => x.TaskId, task.Id)
                    .Generate();

                task.Invoices = new SimpleInvoice[] { invoice.ToSimpleInvoice() };

                DataContext.AddRange(
                    contact.ToDataModel(),
                    invoice.ToDataModel());
            }

            var notInvoicedTasks = new TaskFaker()
                .RuleFor(_ => _.Type, TaskType.ClientEvent)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .Generate(5);

            DataContext.AddRange(notInvoicedTasks.ToDataModel());

            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?unInvoiced=true")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(notInvoicedTasks.Count);
            result.Items.Should().BeEquivalentTo(notInvoicedTasks,
                opt => opt
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.ClaimsReference)
                    .Excluding(x => x.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .UsingSimpleDateTimePrecision());
        }

        [Fact]
        public async Task GetTasks_ClientEvent_IncludeExternalEvents_Test()
        {
            var tasks = new TaskFaker()
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, new[] { SimplePerson.FromPerson(this.Data.CurrentUser) })
                .RuleFor(_ => _.StaffIds, new[] { this.Data.CurrentUser.Id })
                .Generate(10);

            foreach (var task in tasks)
            {
                var taskDataModel = task.ToDataModel();
                taskDataModel.WithStaff(task.Staff.Select(s =>
                    new TaskStaffDataModel()
                    {
                        PersonId = s.Id,
                        TaskId = task.Id
                    }
                ).ToList());
                DataContext.Add(taskDataModel);
            }

            await DataContext.SaveChangesAsync();

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(tasks.Count);
            result.Items.Should().BeEquivalentTo(tasks, opt => opt.ExcludingMissingMembers()
                .Excluding(x => x.Invoices)
                .Excluding(x => x.Notes)
                .Excluding(x => x.ClaimsReference)
                .Excluding(x => x.LocationsSnapshot)
                .Excluding(x => x.ExternalContacts)
                .UsingSimpleDateTimePrecision());
            connectedCalendarService.Verify(x => x.GetExternalTasks(It.IsAny<Guid[]>(), It.IsAny<Guid?>(), It.IsAny<GetEventsOptions>()));
        }

        [Fact]
        public async Task GetTasks_ClientEvent_AppointmentStatusFilter_ExcludeExternalEvents_Test()
        {
            var faker = new Faker();
            var contacts = new ContactFaker(ProviderId)
                .Generate(5);

            var tasks = new TaskFaker()
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Contacts,
                    f => new[]
                    {
                        f.PickRandom<TaskContact>(contacts.Select(c => c.ToTaskContact(faker.PickRandom<TaskContactStatus>())))
                    })
                .RuleFor(_ => _.Staff, new[] { SimplePerson.FromPerson(this.Data.CurrentUser) })
                .RuleFor(_ => _.StaffIds, new[] { this.Data.CurrentUser.Id })
                .Generate(10);

            DataContext.AddRange(contacts.ToDataModel());
            foreach (var task in tasks)
            {
                var taskDataModel = task.ToDataModel();
                taskDataModel.WithStaff(task.Staff.Select(s =>
                    new TaskStaffDataModel()
                    {
                        PersonId = s.Id,
                        TaskId = task.Id
                    }
                ).ToList());
                DataContext.Add(taskDataModel);
                DataContext.AddRange(task.Contacts.Select(c => c.ToDataModel(task.Id)));
            }

            await DataContext.SaveChangesAsync();

            var mockExternalEventsTaskModel = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, new[] { SimplePerson.FromPerson(this.Data.CurrentUser) })
                .RuleFor(_ => _.StaffIds, new[] { this.Data.CurrentUser.Id })
                .Generate(5)
                .ToArray();

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Setup(x =>
                    x.GetExternalTasks(It.IsAny<Guid[]>(), It.IsAny<Guid?>(), It.IsAny<GetEventsOptions>()))
                .ReturnsAsync(mockExternalEventsTaskModel);

            var status = TaskContactStatus.Attended;
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&appointmentStatus={status}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            var expectedTasks = tasks.Where(tasks => tasks.Contacts.Any(c => c.Status == status)).ToList();

            result.Items.Should().HaveCount(expectedTasks.Count);
            result.Items.Should().BeEquivalentTo(expectedTasks, opt => opt.ExcludingMissingMembers()
                .Excluding(x => x.Invoices)
                .Excluding(x => x.Notes)
                .Excluding(x => x.ClaimsReference)
                .Excluding(x => x.LocationsSnapshot)
                .Excluding(x => x.ExternalContacts)
                .Excluding(x => x.Contacts)
                .UsingSimpleDateTimePrecision());

            foreach (var task in expectedTasks)
            {
                var actualContacts = result.Items.First(x => x.Id == task.Id).Contacts;
                actualContacts.Should().BeEquivalentTo(task.Contacts, opt => opt
                    .Excluding(x => x.AttendeeStatusId)
                    .ExcludingMissingMembers());
            }

            result.Items.Select(item => item.Type).Should().NotContain(TaskType.External);
            result.Items.Should().NotContainEquivalentOf(mockExternalEventsTaskModel,
                opt => opt.ExcludingMissingMembers()
                    .UsingSimpleDateTimePrecision());
        }

        [Fact]
        public async Task GetTasks_ClientEvent_InvoiceStatusFilter_ExcludeExternalEvents_Test()
        {
            var tasks = new TaskFaker()
                .RuleFor(_ => _.Type, TaskType.ClientEvent)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, new[] { SimplePerson.FromPerson(this.Data.CurrentUser) })
                .RuleFor(_ => _.StaffIds, new[] { this.Data.CurrentUser.Id })
                .Generate(10);

            foreach (var task in tasks)
            {
                var taskDataModel = task.ToDataModel();
                taskDataModel.WithStaff(task.Staff.Select(s =>
                    new TaskStaffDataModel()
                    {
                        PersonId = s.Id,
                        TaskId = task.Id
                    }
                ).ToList());

                var contact = new ContactFaker(ProviderId)
                    .Generate();

                var invoice = new InvoiceFaker(ProviderId)
                    .RuleFor(x => x.ContactId, contact.Id)
                    .RuleFor(x => x.Status, f => f.PickRandom<InvoiceStatus>())
                    .RuleFor(x => x.TaskId, task.Id)
                    .Generate();

                task.Invoices = new SimpleInvoice[] { invoice.ToSimpleInvoice() };

                DataContext.AddRange(
                    taskDataModel,
                    contact.ToDataModel(),
                    invoice.ToDataModel());
            }

            await DataContext.SaveChangesAsync();

            var mockExternalEventsTaskModel = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, new[] { SimplePerson.FromPerson(this.Data.CurrentUser) })
                .RuleFor(_ => _.StaffIds, new[] { this.Data.CurrentUser.Id })
                .Generate(5)
                .ToArray();

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Setup(x =>
                    x.GetExternalTasks(It.IsAny<Guid[]>(), It.IsAny<Guid?>(), It.IsAny<GetEventsOptions>()))
                .ReturnsAsync(mockExternalEventsTaskModel);

            var status = InvoiceStatus.Unpaid;
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&invoiceStatus={status}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            var expectedTasks = tasks.Where(tasks => tasks.Invoices.Any(i => i.Status == status)).ToList();

            result.Items.Should().HaveCount(expectedTasks.Count);
            result.Items.Should().BeEquivalentTo(expectedTasks,
                opt => opt.UsingSimpleDateTimePrecision()
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.InvoicesReference)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .Excluding(x => x.ClaimsReference));
            result.Items.Select(item => item.Type).Should().NotContain(TaskType.External);
            result.Items.Should().NotContainEquivalentOf(mockExternalEventsTaskModel,
                opt => opt.ExcludingMissingMembers().Excluding(x => x[0].Invoices).UsingSimpleDateTimePrecision());
        }

        [Fact]
        public async Task GetTasks_ClientEvent_NoInvoiceFilter_ExcludeExternalEvents_Test()
        {
            var invoicedTasks = new TaskFaker()
                .RuleFor(_ => _.Type, TaskType.ClientEvent)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, new[] { SimplePerson.FromPerson(this.Data.CurrentUser) })
                .RuleFor(_ => _.StaffIds, new[] { this.Data.CurrentUser.Id })
                .Generate(3);

            foreach (var task in invoicedTasks)
            {
                var taskDataModel = task.ToDataModel();
                taskDataModel.WithStaff(task.Staff.Select(s =>
                    new TaskStaffDataModel()
                    {
                        PersonId = s.Id,
                        TaskId = task.Id
                    }
                ).ToList());

                var contact = new ContactFaker(ProviderId)
                    .Generate();

                var invoice = new InvoiceFaker(ProviderId)
                    .RuleFor(x => x.ContactId, contact.Id)
                    .RuleFor(x => x.Status, f => f.PickRandom<InvoiceStatus>())
                    .RuleFor(x => x.TaskId, task.Id)
                    .Generate();

                task.Invoices = new SimpleInvoice[] { invoice.ToSimpleInvoice() };

                DataContext.AddRange(
                    taskDataModel,
                    contact.ToDataModel(),
                    invoice.ToDataModel());
            }

            var notInvoicedTasks = new TaskFaker()
                .RuleFor(_ => _.Type, TaskType.ClientEvent)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, new[] { SimplePerson.FromPerson(this.Data.CurrentUser) })
                .RuleFor(_ => _.StaffIds, new[] { this.Data.CurrentUser.Id })
                .Generate(5);

            foreach (var task in notInvoicedTasks)
            {
                var taskDataModel = task.ToDataModel();
                taskDataModel.WithStaff(task.Staff.Select(s =>
                    new TaskStaffDataModel()
                    {
                        PersonId = s.Id,
                        TaskId = task.Id
                    }
                ).ToList());

                DataContext.AddRange(
                    taskDataModel);
            }

            await DataContext.SaveChangesAsync();

            var mockExternalEventsTaskModel = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, new[] { SimplePerson.FromPerson(this.Data.CurrentUser) })
                .RuleFor(_ => _.StaffIds, new[] { this.Data.CurrentUser.Id })
                .Generate(5)
                .ToArray();

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Setup(x =>
                    x.GetExternalTasks(It.IsAny<Guid[]>(), It.IsAny<Guid?>(), It.IsAny<GetEventsOptions>()))
                .ReturnsAsync(mockExternalEventsTaskModel);

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&unInvoiced=true")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(notInvoicedTasks.Count);
            result.Items.Should().BeEquivalentTo(notInvoicedTasks,
                opt => opt
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.ClaimsReference)
                    .Excluding(x => x.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .UsingSimpleDateTimePrecision());
            result.Items.Select(item => item.Type).Should().NotContain(TaskType.External);
            result.Items.Should().AllSatisfy(x => mockExternalEventsTaskModel
                .Should()
                .NotContainEquivalentOf(x, opt => opt
                    .Excluding(y => y.Invoices)
                    .Excluding(y => y.Notes)
                    .Excluding(y => y.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .UsingSimpleDateTimePrecision()));

            // result.Items.Should().NotContainEquivalentOf(mockExternalEventsTaskModel,
            //     opt => opt.ExcludingMissingMembers()
            //         .UsingSimpleDateTimePrecision());
        }

        [Fact]
        public async Task GetTasks_ClientEvent_LocationFilter_Test()
        {
            var otherTasks = new TaskFaker()
                .RuleFor(_ => _.Type, TaskType.ClientEvent)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .Generate(4);

            DataContext.AddRange(otherTasks.ToDataModel());

            var location = "Test Location";
            var expectedTasks = new TaskFaker()
                .RuleFor(_ => _.Type, TaskType.ClientEvent)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Location, location)
                .Generate(3);

            DataContext.AddRange(expectedTasks.ToDataModel());

            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?location={location}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(expectedTasks.Count);
            result.Items.Should().BeEquivalentTo(expectedTasks,
                opt => opt
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.ClaimsReference)
                    .Excluding(x => x.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .UsingSimpleDateTimePrecision());
        }

        [Fact]
        public async Task Get_external_tasks_should_not_throw_exception()
        {
            var tasks = new TaskFaker()
                .RuleFor(_ => _.Type, TaskType.ClientEvent)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .Generate(10)
                .ToDataModel()
                .ToList();

            tasks.ForEach(x => x.WithStaff(new List<TaskStaffDataModel> { new() { PersonId = PersonId } }));

            await DataContext.AddRangeAsync(tasks);
            await DataContext.SaveChangesAsync();

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Setup(x => x.GetExternalTasks(It.IsAny<Guid[]>(), It.IsAny<Guid?>(), It.IsAny<GetEventsOptions>())).Throws(new Exception("Test exception"));

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();
            result.Items.Should().NotBeNullOrEmpty();

            var expectedTasks = tasks.Select(x => new
            {
                x.Id,
                x.Location,
                x.StartDate,
                x.EndDate
            }).ToArray();
            result.Items.Should().BeEquivalentTo(expectedTasks, opt => opt.UsingSimpleDateTimePrecision());
        }

        [Fact]
        public async Task GetTasks_ClientEvent_With_OtherStaff_IncludeExternalEvents_As_Private_Test()
        {
            var appProduct = await DataContext.ConnectedAppProducts.FirstOrDefaultAsync(i => i.Code == ConnectedAppProducts.Google);

            var otherStaffPerson = new PersonFaker().Generate();
            var otherStaff = new ProviderStaffFaker(ProviderId, otherStaffPerson.Id).Generate();
            var connectedAppSettingsFaker = new GoogleConnectedAppSettingsFaker()
                .RuleFor(x => x.Calendar, new CalendarSetting()
                {
                    Display = true,
                    Push = true,
                    UseTwoWaySync = false
                });

            // Add connected apps for the current user and other staff
            var currentPersonConnectedApp = new ConnectedAppFaker()
                .WithPerson(PersonId)
                .WithProvider(ProviderId)
                .WithAccount(Data.CurrentUser.Email)
                .RuleFor(_ => _.ProductId, appProduct.Id)
                .RuleFor(_ => _.DisplayCalendar, true)
                .RuleFor(_ => _.Settings, connectedAppSettingsFaker.Generate())
                .Generate();

            var otherPersonConnectedApp = new ConnectedAppFaker()
                .WithPerson(otherStaffPerson.Id)
                .WithProvider(ProviderId)
                .WithAccount(otherStaffPerson.Email)
                .RuleFor(_ => _.ProductId, appProduct.Id)
                .RuleFor(_ => _.DisplayCalendar, true)
                .RuleFor(_ => _.Settings, connectedAppSettingsFaker.Generate())
                .Generate();

            DataContext.AddRange(otherStaffPerson.ToDataModel(), otherStaff.ToDataModel(), otherPersonConnectedApp.ToDataModel(), currentPersonConnectedApp.ToDataModel());

            // Add tasks for the current user
            var currentUserTasks = new TaskFaker()
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, [SimplePerson.FromPerson(this.Data.CurrentUser)])
                .RuleFor(_ => _.StaffIds, [this.Data.CurrentUser.Id])
                .Generate(3);

            foreach (var task in currentUserTasks)
            {
                var taskDataModel = task.ToDataModel();
                taskDataModel.WithStaff(task.Staff.Select(s =>
                    new TaskStaffDataModel()
                    {
                        PersonId = s.Id,
                        TaskId = task.Id
                    }
                ).ToList());
                DataContext.Add(taskDataModel);
            }

            // Add tasks for other staff
            var otherStaffTasks = new TaskFaker()
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, [SimplePerson.FromPerson(otherStaffPerson)])
                .RuleFor(_ => _.StaffIds, [otherStaffPerson.Id])
                .Generate(3);

            foreach (var task in otherStaffTasks)
            {
                var taskDataModel = task.ToDataModel();
                taskDataModel.WithStaff(task.Staff.Select(s =>
                    new TaskStaffDataModel()
                    {
                        PersonId = s.Id,
                        TaskId = task.Id
                    }
                ).ToList());
                DataContext.Add(taskDataModel);
            }

            await DataContext.SaveChangesAsync();

            var extContact1 = new ContactFaker().Generate().ToTaskContact(TaskContactStatus.Attended);
            var extContact2 = new ContactFaker().Generate().ToTaskContact(TaskContactStatus.Attended);
            var extContact3 = new ContactFaker().Generate().ToTaskContact(TaskContactStatus.Attended);

            //Mock external tasks
            var currentStaffExternalTasks = new TaskFaker()
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Type, TaskType.External)
                .RuleFor(_ => _.Staff, [SimplePerson.FromPerson(this.Data.CurrentUser)])
                .RuleFor(_ => _.StaffIds, [this.Data.CurrentUser.Id])
                .RuleFor(_ => _.Contacts, [extContact1, extContact2])
                .Generate(4);

            var otherStaffExternalTasks = new TaskFaker()
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Type, TaskType.External)
                .RuleFor(_ => _.Staff, [SimplePerson.FromPerson(otherStaffPerson)])
                .RuleFor(_ => _.StaffIds, [otherStaffPerson.Id])
                .RuleFor(_ => _.Contacts, [extContact1, extContact2, extContact3])
                .Generate(5);


            var connectedCalendarRepo = ResolveService<ConnectedCalendarRepositoryMock>();
            connectedCalendarRepo.Setup(x => x.GetEvents(It.IsAny<ConnectedApp>(), It.IsAny<SimplePerson>(), It.IsAny<GetEventsOptions>()))
                .Returns((ConnectedApp connectedApp, SimplePerson person, GetEventsOptions eventOptions) =>
                {
                    if (person.Id == Data.CurrentUser.Id)
                    {
                        return Task.FromResult(currentStaffExternalTasks.ToArray());
                    }
                    else
                    {
                        return Task.FromResult(otherStaffExternalTasks.ToArray());
                    }
                });

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.UseRealImplementation();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&staffId={otherStaffPerson.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(currentUserTasks.Count + otherStaffTasks.Count + currentStaffExternalTasks.Count + otherStaffExternalTasks.Count);

            currentUserTasks.Should().AllSatisfy(task => result.Items.Should()
                .ContainEquivalentOf(task, opt => opt.ExcludingMissingMembers()
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.ClaimsReference)
                    .Excluding(x => x.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .UsingSimpleDateTimePrecision()));

            otherStaffTasks.Should().AllSatisfy(task => result.Items.Should()
                .ContainEquivalentOf(task, opt => opt.ExcludingMissingMembers()
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.ClaimsReference)
                    .Excluding(x => x.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .UsingSimpleDateTimePrecision()));

            currentStaffExternalTasks.Should().AllSatisfy(task => result.Items.Should()
                .ContainEquivalentOf(task, opt => opt.ExcludingMissingMembers()
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .UsingSimpleDateTimePrecision()));

            otherStaffExternalTasks.Should().AllSatisfy(task =>
            {
                var taskModel = result.Items.First(x => x.Id == task.Id);
                taskModel.Should().NotBeNull();
                taskModel.Title.Should().Be("Private");
                taskModel.Description.Should().BeNullOrEmpty();
                taskModel.Contacts.Should().BeEmpty();
                taskModel.Should().BeEquivalentTo(task, opt => opt
                    .ExcludingMissingMembers()
                    .Excluding(x => x.Title)
                    .Excluding(x => x.Description)
                    .Excluding(x => x.Contacts)
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .UsingSimpleDateTimePrecision());
            });
        }

        [Fact]
        public async Task GetTasks_NoStaff_Exclude_ExternalTasks_Test()
        {
            var contact1 = new ContactFaker().RuleFor(_ => _.ProviderId, ProviderId).Generate();
            var tasks = new TaskFaker()
                .WithContacts([contact1], TaskContactStatus.Confirmed)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .Generate(3);

            await DataContext.AddAsync(contact1.ToDataModel());
            await DataContext.AddRangeAsync(tasks.ToDataModel());
            await DataContext.AddRangeAsync(tasks.SelectMany(i => i.Contacts.Select(j => j.ToDataModel(i.Id)).ToArray()));
            await DataContext.SaveChangesAsync();

            var mockExternalEventsTaskModel = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, new[] { SimplePerson.FromPerson(this.Data.CurrentUser) })
                .RuleFor(_ => _.StaffIds, new[] { this.Data.CurrentUser.Id })
                .Generate(5)
                .ToArray();

            Guid[] guids;
            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Setup(x =>
                    x.GetExternalTasks(It.IsAny<Guid[]>(), It.IsAny<Guid?>(), It.IsAny<GetEventsOptions>()))
                .ReturnsAsync(mockExternalEventsTaskModel);

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?contactId={contact1.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(3);

            // assert basic properties
            result.Items.Should().BeEquivalentTo(tasks, opt => opt
                .ExcludingMissingMembers()
                .UsingSimpleDateTimePrecision()
                .Excluding(x => x.Contacts)
                .Excluding(x => x.Staff)
                .Excluding(x => x.Invoices)
                .Excluding(x => x.ClaimsReference)
                .Excluding(x => x.LocationsSnapshot)
                .Excluding(x => x.ExternalContacts)
                .Excluding(x => x.Notes));

            result.Items.Should().NotContainEquivalentOf(mockExternalEventsTaskModel,
                opt => opt.ExcludingMissingMembers().UsingSimpleDateTimePrecision());
        }

        [Fact]
        public async Task Get_client_appointments_staff_own_calendar_should_return_own_tasks_only()
        {
            var contact1 = new ContactFaker(ProviderId)
                .Generate()
                .ToDataModel();

            var contact2 = new ContactFaker(ProviderId)
                .Generate()
                .ToDataModel();

            var staffPerson1 = new PersonFaker()
                .Generate()
                .ToDataModel();

            var providerStaff1 = new ProviderStaffFaker(ProviderId)
                .RuleFor(x => x.PersonId, staffPerson1.Id)
                .Generate()
                .ToDataModel();

            var staffPerson2 = new PersonFaker()
                .Generate()
                .ToDataModel();

            var providerStaff2 = new ProviderStaffFaker(ProviderId)
                .RuleFor(x => x.PersonId, staffPerson2.Id)
                .Generate()
                .ToDataModel();

            var staffPerson3 = new PersonFaker()
                .Generate()
                .ToDataModel();

            var providerStaff3 = new ProviderStaffFaker(ProviderId)
                .RuleFor(x => x.PersonId, staffPerson3.Id)
                .Generate()
                .ToDataModel();

            var task1 = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [providerStaff3.PersonId, providerStaff1.PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel> { new() { ContactId = contact1.Id } });

            var task2 = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [providerStaff2.PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel> { new() { ContactId = contact2.Id } });

            var task3 = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [providerStaff3.PersonId, providerStaff2.PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel> { new() { ContactId = contact1.Id } });

            var task4 = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [providerStaff1.PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel> { new() { ContactId = contact2.Id } });

            var task5 = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [providerStaff3.PersonId, providerStaff1.PersonId, providerStaff2.PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel> { new() { ContactId = contact1.Id } });

            var staffPermission = new ProviderPermissionsFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.PersonId, providerStaff3.PersonId)
                .RuleFor(x => x.SchedulingView, SchedulingPermission.OwnCalendar)
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(contact1, contact2, staffPerson1, staffPerson2, staffPerson3, providerStaff1, providerStaff2, providerStaff3, staffPermission);
            await DataContext.AddRangeAsync(task1, task2, task3, task4, task5);
            await DataContext.SaveChangesAsync();

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Reset();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={providerStaff2.PersonId}")
                .WithBearerAuthorization(staffPerson3.PersonAsJwtToken())
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(2);

            result.Items.Should().ContainEquivalentOf(new { task3.Id }, opt => opt.ExcludingMissingMembers().UsingSimpleDateTimePrecision());
            result.Items.Should().ContainEquivalentOf(new { task5.Id }, opt => opt.ExcludingMissingMembers().UsingSimpleDateTimePrecision());

            //assert that the items have the correct staff
            result.Items.Should().OnlyContain(x => x.StaffIds.Contains(providerStaff3.PersonId));
            result.Items.Should().OnlyContain(x => x.StaffIds.Contains(providerStaff2.PersonId));
        }

        [Fact]
        public async Task Get_client_appointments_staff_own_calendar_should_return_own_external_tasks_only()
        {
            var appProduct = await DataContext.ConnectedAppProducts.FirstOrDefaultAsync(i => i.Code == ConnectedAppProducts.Google);

            var otherStaffPerson = new PersonFaker().Generate();
            var otherStaff = new ProviderStaffFaker(ProviderId, otherStaffPerson.Id).Generate();

            var connectedAppSettingsFaker = new GoogleConnectedAppSettingsFaker()
                .RuleFor(x => x.Calendar, new CalendarSetting()
                {
                    Display = true,
                    Push = true,
                    UseTwoWaySync = false
                });
            // Add connected apps for the current user and other staff
            var currentPersonConnectedApp = new ConnectedAppFaker()
                .WithPerson(Data.StaffMember.Id)
                .WithProvider(ProviderId)
                .WithAccount(Data.StaffMember.Email)
                .RuleFor(_ => _.ProductId, appProduct.Id)
                .RuleFor(_ => _.DisplayCalendar, true)
                .RuleFor(_ => _.Settings, connectedAppSettingsFaker.Generate())
                .Generate();

            var otherPersonConnectedApp = new ConnectedAppFaker()
                .WithPerson(otherStaffPerson.Id)
                .WithProvider(ProviderId)
                .WithAccount(otherStaffPerson.Email)
                .RuleFor(_ => _.ProductId, appProduct.Id)
                .RuleFor(_ => _.DisplayCalendar, true)
                .RuleFor(_ => _.Settings, connectedAppSettingsFaker.Generate())
                .Generate();

            DataContext.AddRange(otherStaffPerson.ToDataModel(), otherStaff.ToDataModel(), otherPersonConnectedApp.ToDataModel(), currentPersonConnectedApp.ToDataModel());

            // Add tasks for the current user
            var currentUserTasks = new TaskFaker()
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, [SimplePerson.FromPerson(this.Data.StaffMember)])
                .RuleFor(_ => _.StaffIds, [this.Data.StaffMember.Id])
                .Generate(3);

            foreach (var task in currentUserTasks)
            {
                var taskDataModel = task.ToDataModel();
                taskDataModel.WithStaff(task.Staff.Select(s =>
                    new TaskStaffDataModel()
                    {
                        PersonId = s.Id,
                        TaskId = task.Id
                    }
                ).ToList());
                DataContext.Add(taskDataModel);
            }

            // Add tasks for other staff that are also participated by the current staff member
            var otherStaffTasks = new TaskFaker()
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, [SimplePerson.FromPerson(otherStaffPerson), SimplePerson.FromPerson(Data.StaffMember)])
                .RuleFor(_ => _.StaffIds, [otherStaffPerson.Id])
                .Generate(3);

            foreach (var task in otherStaffTasks)
            {
                var taskDataModel = task.ToDataModel();
                taskDataModel.WithStaff(task.Staff.Select(s =>
                    new TaskStaffDataModel()
                    {
                        PersonId = s.Id,
                        TaskId = task.Id
                    }
                ).ToList());
                DataContext.Add(taskDataModel);
            }

            await DataContext.SaveChangesAsync();

            var extContact1 = new ContactFaker().Generate().ToTaskContact(TaskContactStatus.Attended);
            var extContact2 = new ContactFaker().Generate().ToTaskContact(TaskContactStatus.Attended);
            var extContact3 = new ContactFaker().Generate().ToTaskContact(TaskContactStatus.Attended);

            //Mock external tasks
            var currentStaffExternalTasks = new TaskFaker()
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Type, TaskType.External)
                .RuleFor(_ => _.Staff, [SimplePerson.FromPerson(this.Data.StaffMember)])
                .RuleFor(_ => _.StaffIds, [this.Data.StaffMember.Id])
                .RuleFor(_ => _.Contacts, [extContact1, extContact2])
                .Generate(4);

            var otherStaffExternalTasks = new TaskFaker()
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Type, TaskType.External)
                .RuleFor(_ => _.Staff, [SimplePerson.FromPerson(otherStaffPerson)])
                .RuleFor(_ => _.StaffIds, [otherStaffPerson.Id])
                .RuleFor(_ => _.Contacts, [extContact1, extContact2, extContact3])
                .Generate(5);


            var connectedCalendarRepo = ResolveService<ConnectedCalendarRepositoryMock>();
            connectedCalendarRepo.Setup(x => x.GetEvents(It.IsAny<ConnectedApp>(), It.IsAny<SimplePerson>(), It.IsAny<GetEventsOptions>()))
                .Returns((ConnectedApp connectedApp, SimplePerson person, GetEventsOptions eventOptions) =>
                {
                    if (person.Id == Data.StaffMember.Id)
                    {
                        return Task.FromResult(currentStaffExternalTasks.ToArray());
                    }
                    else
                    {
                        return Task.FromResult(otherStaffExternalTasks.ToArray());
                    }
                });

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.UseRealImplementation();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={Data.StaffMember.Id}&staffId={otherStaffPerson.Id}")
                .WithBearerAuthorization(Data.StaffMember.PersonAsJwtToken())
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(currentUserTasks.Count + otherStaffTasks.Count + currentStaffExternalTasks.Count);

            currentUserTasks.Should().AllSatisfy(task => result.Items.Should()
                .ContainEquivalentOf(task, opt => opt.ExcludingMissingMembers()
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.ClaimsReference)
                    .Excluding(x => x.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .UsingSimpleDateTimePrecision()));

            otherStaffTasks.Should().AllSatisfy(task => result.Items.Should()
                .NotContainEquivalentOf(task, opt => opt.ExcludingMissingMembers()
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.ClaimsReference)
                    .Excluding(x => x.ExternalContacts)
                    .UsingSimpleDateTimePrecision()));
        }

        [Fact]
        public async Task GetTasks_ClientEvent_Include_Persisted_External_Tasks_With_Active_CalendarSubscription_Test()
        {
            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Reset();

            var calendarSubscription = await CreateCalendarSubscription(PersonId, ProviderId);

            var ownedExternalTasks = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .RuleFor(x => x.Type, TaskType.External)
                .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
                .RuleFor(x => x.ExternalEventType, ExternalEventType.Google)
                .RuleFor(x => x.ExternalId, f => f.Random.Hash(10))
                .Generate(3)
                .ToDataModel();

            var staffCalendarSubscription = await CreateCalendarSubscription(Data.StaffMember.Id, ProviderId);

            var staffExternalTasks = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [Data.StaffMember.Id])
                .RuleFor(x => x.Type, TaskType.External)
                .RuleFor(x => x.ExternalCalendarId, staffCalendarSubscription.CalendarId)
                .RuleFor(x => x.ExternalEventType, ExternalEventType.Google)
                .RuleFor(x => x.ExternalId, f => f.Random.Hash(10))
                .Generate(3)
                .ToDataModel();

            var internalTasks = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .Generate(3)
                .ToDataModel();

            DataContext.AddRange(ownedExternalTasks);
            DataContext.AddRange(staffExternalTasks);
            DataContext.AddRange(internalTasks);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&staffId={Data.StaffMember.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            var expected = ownedExternalTasks.Concat(internalTasks).Concat(staffExternalTasks)
                .Select(x =>
                    new
                    {
                        x.Id,
                        x.Type,
                        Title = x.Staff.Any(s => s.PersonId == PersonId) ? x.Title : "Private",
                    })
                .ToArray();
            result.Items.Should().BeEquivalentTo(expected, opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
        } 
        
        [Fact]
        public async Task GetTasks_ClientEvent_Exclude_Persisted_External_Tasks_With_Inactive_CalendarSubscription_Test()
        {
            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Reset();

            var calendarSubscription = await CreateCalendarSubscription(PersonId, ProviderId, false);

            var ownedExternalTasks = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .RuleFor(x => x.Type, TaskType.External)
                .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
                .RuleFor(x => x.ExternalEventType, ExternalEventType.Google)
                .RuleFor(x => x.ExternalId, f => f.Random.Hash(10))
                .Generate(3)
                .ToDataModel();

            var staffCalendarSubscription = await CreateCalendarSubscription(Data.StaffMember.Id, ProviderId, false);

            var staffExternalTasks = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [Data.StaffMember.Id])
                .RuleFor(x => x.Type, TaskType.External)
                .RuleFor(x => x.ExternalCalendarId, staffCalendarSubscription.CalendarId)
                .RuleFor(x => x.ExternalEventType, ExternalEventType.Google)
                .RuleFor(x => x.ExternalId, f => f.Random.Hash(10))
                .Generate(3)
                .ToDataModel();

            var internalTasks = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .Generate(3)
                .ToDataModel();

            DataContext.AddRange(ownedExternalTasks);
            DataContext.AddRange(staffExternalTasks);
            DataContext.AddRange(internalTasks);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&staffId={Data.StaffMember.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            var expected = internalTasks
                .Select(x =>
                    new
                    {
                        x.Id,
                        x.Type,
                        Title = x.Staff.Any(s => s.PersonId == PersonId) ? x.Title : "Private",
                    })
                .ToArray();
            result.Items.Should().BeEquivalentTo(expected, opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
        }

        private async Task<CalendarSubscriptionDataModel> CreateCalendarSubscription(Guid personId, Guid providerId, bool isActive = true)
        {
            var appProduct = await DataContext.ConnectedAppProducts.FirstOrDefaultAsync(i => i.Code == ConnectedAppProducts.Google);

            var connectedAppSettings = new GoogleConnectedAppSettingsFaker()
                .WithCalendar(true, true, true)
                .Generate();

            var connectedApp = new ConnectedAppFaker()
                .WithPerson(personId)
                .WithProvider(providerId)
                .WithSettings(connectedAppSettings)
                .RuleFor(x => x.ProductId, appProduct.Id)
                .Generate()
                .ToDataModel();

            var calendarSubscription = new CalendarSubscriptionFaker()
                .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
                .RuleFor(x => x.InitialSyncStatus, InitialSyncStatus.Success)
                .RuleFor(x => x.IsActive, isActive)
                .Generate()
                .ToDataModel();

            DataContext.AddRange(connectedApp, calendarSubscription);
            await DataContext.SaveChangesAsync();

            return calendarSubscription;
        }

        [Fact]
        public async Task GetTasks_ClientEvent_OneInvoice_MultipleTask_Test()
        {
            var invoicedTasks = new TaskFaker()
                .RuleFor(_ => _.Type, TaskType.ClientEvent)
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Staff, new[] { SimplePerson.FromPerson(this.Data.CurrentUser) })
                .RuleFor(_ => _.StaffIds, new[] { this.Data.CurrentUser.Id })
                .Generate(3);

            var contact = new ContactFaker(ProviderId)
                .Generate();

            var invoice = new InvoiceFaker(ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.Status, f => f.PickRandom<InvoiceStatus>())
                .RuleFor(x => x.TaskIds, invoicedTasks.Select(x => x.Id).ToArray())
                .Generate();

            foreach (var task in invoicedTasks)
            {
                var taskDataModel = task.ToDataModel();
                taskDataModel.WithStaff(task.Staff.Select(s =>
                    new TaskStaffDataModel()
                    {
                        PersonId = s.Id,
                        TaskId = task.Id
                    }
                ).ToList());
                task.Invoices = [invoice.ToSimpleInvoice()];
                DataContext.AddRange(taskDataModel);
            }

            DataContext.AddRange(contact.ToDataModel(), invoice.ToDataModel());
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&unInvoiced=false")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();
            result.Items.Should().BeEquivalentTo(invoicedTasks,
                opt => opt.UsingSimpleDateTimePrecision()
                    .Excluding(x => x.Invoices)
                    .Excluding(x => x.InvoicesReference)
                    .Excluding(x => x.Notes)
                    .Excluding(x => x.ClaimsReference)
                    .Excluding(x => x.LocationsSnapshot)
                    .Excluding(x => x.ExternalContacts)
                    .Excluding(x => x.TaskInvoicesReference));
            result.Items.Select(x => x.InvoicesReference).Should()
                .AllSatisfy(x => x.Should()
                    .ContainSingle(x => x.Id == invoice.Id));
        }

        [Fact]
        public async Task GetTasks_ClientEvent_ByProvider_WithClaimsReferences()
        {
            var contact = new ContactFaker(ProviderId).Generate();
            var task = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact.Id).WithStaff(PersonId).WithProvider(ProviderId).Create();
            var note = NoteModelBuilder.Any().WithProviderId(ProviderId).WithTaskId(task.Id).WithContactId(contact.Id).WithCreatedByPersonId(PersonId).Create();
            
            var claimId = Guid.NewGuid();
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(ProviderId, contact.Id).Generate()
            };

            var claimDetail = new USProfessionalClaimFaker(ProviderId)
                .WithClient(ProviderId, contact)
                .WithServiceLines([.. serviceLines])
                .RuleFor(x => x.Id, claimId)
                .Generate();
            DataContext.InsuranceClaimsUSProfessional.Add(claimDetail.ToDataModel(claimId));

            var claimHeader = claimDetail.ToClaimHeaderDataModel(claimId);
            claimHeader.Amount = serviceLines.Sum(x => x.Amount);
            claimHeader.FromDate = serviceLines.Min(x => x.Date);
            claimHeader.ToDate = serviceLines.Max(x => x.Date);

            DataContext.InsuranceClaims.Add(claimHeader);

            var insuranceClaimTask = new InsuranceClaimTaskDataModel(claimId, task.Id);
            DataContext.InsuranceClaimTasks.Add(insuranceClaimTask);

            await DataContext.AddRangeAsync(contact.ToDataModel(), task, note);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act
            var response = await ClientApi.SendAsync(request);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Items.Should().HaveCount(1);

            var actualTask = result.Items.First(x => x.Id == task.Id);
            actualTask.Should().BeEquivalentTo(task, opt => opt
                .ExcludingMissingMembers()
                .Excluding(x => x.Contacts)
                .Excluding(x => x.Staff)
                .Excluding(x => x.Notes)
                .Excluding(x => x.TaskClaims)
                .Excluding(x => x.LocationsSnapshot)
                .Excluding(x => x.ExternalContacts)
                .Excluding(x => x.Invoices));

            // assert Contact contains props from the actual Contact and the Task i.e RecipientStatus
            actualTask.Contacts.First(x => x.Id == contact.Id).Should().BeEquivalentTo(contact, opt => opt.Excluding(x => x.Status).ExcludingMissingMembers());
            actualTask.Contacts.First(x => x.Id == contact.Id).Should().BeEquivalentTo(task.Contacts.First(), opt => opt.Excluding(x => x.AttendeeStatusId).ExcludingMissingMembers());
            actualTask.Notes.Should().BeNullOrEmpty();
            actualTask.Invoices.Should().BeNullOrEmpty();

            var claimReference = actualTask.ClaimsReference.First();
            claimReference.Id.Should().Be(claimId);
            claimReference.Amount.Should().Be(serviceLines.Sum(x => x.Amount));
            claimReference.FromDate.Should().Be(serviceLines.Min(x => x.Date));
            claimReference.ToDate.Should().Be(serviceLines.Max(x => x.Date));
        }

        [Fact]
        public async Task GetTasks_ClientEvent_ByProvider_With_LocationsSnapshot()
        {
            var contact = new ContactFaker(ProviderId)
                .Generate()
                .ToDataModel();

            var physicalLocation = new TaskLocationSnapshotFaker(Guid.NewGuid())
                .RuleFor(x => x.Type, LocationType.Physical)
                .Generate();
            var videoCallLocation = new TaskLocationSnapshotFaker(Guid.NewGuid())
                .RuleFor(x => x.Type, LocationType.VideoCall)
                .Generate();

            var task = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .RuleFor(x => x.LocationPOSCode, (string)null)
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel> { new() { ContactId = contact.Id } })
                .WithLocations(new List<TaskLocationSnapshot> { physicalLocation, videoCallLocation });

            await DataContext.AddRangeAsync(contact, task);
            await DataContext.SaveChangesAsync();

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Reset();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(1);

            var actualTask = result.Items.First(x => x.Id == task.Id);
            actualTask.Should().BeEquivalentTo(new
            {
                // video call should be prioritized
                LocationPOSCode = videoCallLocation.PosCode,
                LocationsSnapshot = new List<TaskLocationSnapshot> { physicalLocation, videoCallLocation }
            }, opt => opt.ExcludingMissingMembers());
            
            var taskDb = await DataContext.Tasks
                .FirstOrDefaultAsync(x => x.Id == task.Id);
            
            taskDb.LocationsSnapshot.Should().HaveCount(2);
            taskDb.LocationsSnapshot.Should().ContainEquivalentOf(physicalLocation);
            taskDb.LocationsSnapshot.Should().ContainEquivalentOf(videoCallLocation);
            taskDb.LocationPOSCode.Should().BeNullOrWhiteSpace();
        }


        [Fact]
        public async Task Get_tasks_should_include_events_with_no_assigned_staff_test()
        {
            string timeZone = "Asia/Manila";
            List<TaskDataModel> tasksDataModel = new List<TaskDataModel>();
            var fromDate = new DateTime(2025, 3, 22, 16, 0, 0, DateTimeKind.Utc);
            var endDate = new DateTime(2025, 3, 30, 0, 0, 0, DateTimeKind.Utc);
            
            for (int i = 0; i < 5; i++)
            {
                var faker = new Faker();
                var startDate = faker.Date.Between(fromDate, endDate); 
                
                var task = new TaskFaker()
                    .RuleFor(x => x.Id, Guid.NewGuid())
                    .RuleFor(x => x.ProviderId, ProviderId)
                    .RuleFor(x => x.StartDate, startDate)
                    .RuleFor(x => x.EndDate, startDate.AddMinutes(30))
                    .RuleFor(x => x.TimeZone, timeZone)
                    .Generate()
                    .ToDataModel()
                    .WithStaff(new List<TaskStaffDataModel>
                    {
                        new() { PersonId = PersonId }
                    });
                
                tasksDataModel.Add(task);
            }


            await DataContext.AddRangeAsync(tasksDataModel);

            var unassignedTasksDataModel = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StartDate, (faker, x) => faker.Date.Between(fromDate, endDate))
                .RuleFor(x => x.EndDate, (faker, x) => x.StartDate.AddMinutes(30))
                .RuleFor(x => x.TimeZone, timeZone)
                .Generate(5)
                .ToDataModel();

            await DataContext.AddRangeAsync(unassignedTasksDataModel);
            await DataContext.SaveChangesAsync();

            var fromStartDate = fromDate;
            var toEndDate = new DateTime(2025, 3, 30, 15, 59, 59, DateTimeKind.Utc);
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&unAssigned=true" +
                                                                        $"&fromStartDate={fromStartDate.ToString("O")}" +
                                                                        $"&toEndDate={toEndDate.ToString("O")}" +
                                                                        $"&offset=0&limit=500")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();
            result.Items.Should().NotBeNullOrEmpty();
            
            result.Items.Should().HaveCount(10);

            var allTasks = tasksDataModel.Concat(unassignedTasksDataModel).Select(x => new
            {
                x.Id,
                x.ProviderId,
                x.StartDate,
                x.EndDate
            }).ToArray();
            result.Items.Should().BeEquivalentTo(allTasks, opt => opt.ExcludingFields().UsingSimpleDateTimePrecision());
        }
        
        [Fact]
        public async Task Get_tasks_should_not_include_events_with_no_assigned_staff_test()
        {
            List<TaskDataModel> tasksDataModel = new List<TaskDataModel>();
            string timeZone = "Asia/Manila";
            var fromDate = new DateTime(2025, 3, 22, 16, 0, 0, DateTimeKind.Utc);
            var endDate = new DateTime(2025, 3, 30, 0, 0, 0, DateTimeKind.Utc);
            
            for (int i = 0; i < 5; i++)
            {
                var faker = new Faker();
                var startDate = faker.Date.Between(fromDate, endDate); 
                
                var task = new TaskFaker()
                    .RuleFor(x => x.Id, Guid.NewGuid())
                    .RuleFor(x => x.ProviderId, ProviderId)
                    .RuleFor(x => x.StartDate, startDate)
                    .RuleFor(x => x.EndDate, startDate.AddMinutes(30))
                    .RuleFor(x => x.TimeZone, timeZone)
                    .Generate()
                    .ToDataModel()
                    .WithStaff(new List<TaskStaffDataModel>
                    {
                        new() { PersonId = PersonId }
                    });
                
                tasksDataModel.Add(task);
            }

            await DataContext.AddRangeAsync(tasksDataModel);

            var unassignedTasks = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StartDate, (faker, x) => faker.Date.Between(fromDate, endDate))
                .RuleFor(x => x.EndDate, (faker, x) => x.StartDate.AddMinutes(30))
                .RuleFor(x => x.TimeZone, timeZone)
                .Generate(5)
                .ToDataModel();

            await DataContext.AddRangeAsync(unassignedTasks);
            await DataContext.SaveChangesAsync();
            
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&unAssigned=false")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();
            result.Items.Should().NotBeNullOrEmpty();

            result.Items.Should().AllSatisfy(x => x.StaffIds.Should().NotBeNullOrEmpty());
        }

        [Fact]
        public async Task Get_tasks_should_include_events_with_assigned_staff_but_not_a_staff_member_of_the_provider()
        {
            string timeZone = "Asia/Manila";
            var newPerson = new PersonFaker()
                .Generate()
                .ToDataModel();
            await DataContext.AddAsync(newPerson);
            
            List<TaskDataModel> tasksDataModel = new List<TaskDataModel>();
            var fromDate = new DateTime(2025, 3, 22, 16, 0, 0, DateTimeKind.Utc);
            var endDate = new DateTime(2025, 3, 30, 0, 0, 0, DateTimeKind.Utc);
            
            for (int i = 0; i < 5; i++)
            {
                var faker = new Faker();
                var startDate = faker.Date.Between(fromDate, endDate); 
                
                var task = new TaskFaker()
                    .RuleFor(x => x.Id, Guid.NewGuid())
                    .RuleFor(x => x.ProviderId, ProviderId)
                    .RuleFor(x => x.StartDate, startDate)
                    .RuleFor(x => x.EndDate, startDate.AddMinutes(30))
                    .RuleFor(x => x.TimeZone, timeZone)
                    .Generate()
                    .ToDataModel()
                    .WithStaff(new List<TaskStaffDataModel>
                    {
                        new() { PersonId = PersonId }
                    });
                
                tasksDataModel.Add(task);
            }

            await DataContext.AddRangeAsync(tasksDataModel);

            var assignedToDeletedStaffMember = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StartDate, (faker, x) => faker.Date.Between(fromDate, endDate))
                .RuleFor(x => x.EndDate, (faker, x) => x.StartDate.AddMinutes(30))
                .RuleFor(x => x.TimeZone, timeZone)
                .RuleFor(x => x.StaffIds, [newPerson.Id])
                .Generate(5)
                .ToDataModel();

            await DataContext.AddRangeAsync(assignedToDeletedStaffMember);
            await DataContext.SaveChangesAsync();

            var fromStartDate = fromDate;
            var toEndDate = new DateTime(2025, 3, 30, 15, 59, 59, DateTimeKind.Utc);
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&unAssigned=true" +
                                                                        $"&fromStartDate={fromStartDate.ToString("O")}" +
                                                                        $"&toEndDate={toEndDate.ToString("O")}" +
                                                                        $"&offset=0&limit=500")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();
            result.Items.Should().NotBeNullOrEmpty();
            
            result.Items.Should().HaveCount(10);

            var allTasks = tasksDataModel.Concat(assignedToDeletedStaffMember).Select(x => new
            {
                x.Id,
                x.ProviderId,
                x.StartDate,
                x.EndDate
            }).ToArray();
            result.Items.Should().BeEquivalentTo(allTasks, opt => opt.ExcludingFields().UsingSimpleDateTimePrecision());
        }
        
        [Fact]
        public async Task Get_tasks_should_map_task_contact_legacy_status_to_attendee_status()
        {
            var contacts = new ContactFaker(ProviderId)
                .Generate(5)
                .ToDataModel();

            var task = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel>
                {
                    new() { ContactId = contacts[0].Id, TaskContactStatus = TaskContactStatus.Unconfirmed },
                    new() { ContactId = contacts[1].Id, TaskContactStatus = TaskContactStatus.Confirmed },
                    new() { ContactId = contacts[2].Id, TaskContactStatus = TaskContactStatus.Cancelled },
                    new() { ContactId = contacts[3].Id, TaskContactStatus = TaskContactStatus.DidNotAttend },
                    new() { ContactId = contacts[4].Id, TaskContactStatus = TaskContactStatus.Attended }
                });

            await DataContext.AddRangeAsync(task);
            await DataContext.AddRangeAsync(contacts);
            await DataContext.SaveChangesAsync();

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Reset();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(1);

            var actualTask = result.Items.First(x => x.Id == task.Id);
            actualTask.Should().NotBeNull();
            
            actualTask.Contacts.Should().BeEquivalentTo([
                new
                {
                    contacts[0].Id,
                    Status = TaskContactStatus.Unconfirmed,
                    AttendeeStatusId = nameof(TaskContactStatus.Unconfirmed)
                },
                new
                {
                    contacts[1].Id,
                    Status = TaskContactStatus.Confirmed,
                    AttendeeStatusId = nameof(TaskContactStatus.Confirmed)
                },
                new
                {
                    contacts[2].Id,
                    Status = TaskContactStatus.Cancelled,
                    AttendeeStatusId = nameof(TaskContactStatus.Cancelled)
                },
                new
                {
                    contacts[3].Id,
                    Status = TaskContactStatus.DidNotAttend,
                    AttendeeStatusId = nameof(TaskContactStatus.DidNotAttend)
                },
                new
                {
                    contacts[4].Id,
                    Status = TaskContactStatus.Attended,
                    AttendeeStatusId = nameof(TaskContactStatus.Attended)
                }
            ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
        }
        
        [Fact]
        public async Task Get_tasks_with_attendee_status_id_filter()
        {
            var contact = new ContactFaker(ProviderId)
                .Generate()
                .ToDataModel();

            var attendeeStatusConfirmed = new TaskAttendeeStatusFaker()
                .RuleFor(x => x.Id, TaskContactStatus.Confirmed.ToString())
                .RuleFor(x => x.Group, TaskAttendeeGroupStatus.Accepted)
                .Generate()
                .ToDataModel(ProviderId);
            
            var attendeeStatusUnconfirmed = new TaskAttendeeStatusFaker()
                .RuleFor(x => x.Id, TaskContactStatus.Unconfirmed.ToString())
                .RuleFor(x => x.Group, TaskAttendeeGroupStatus.Pending)
                .Generate()
                .ToDataModel(ProviderId);

            var taskWithLegacyStatus = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel>
                {
                    new() { ContactId = contact.Id, TaskContactStatus = TaskContactStatus.Confirmed }
                });
            
            var taskWithLegacyStatus2 = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel>
                {
                    new() { ContactId = contact.Id, TaskContactStatus = TaskContactStatus.Unconfirmed }
                });

            var taskWithNewStatus = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel>
                {
                    new() { ContactId = contact.Id, AttendeeStatusId = attendeeStatusConfirmed.Id }
                });
            
            var taskWithNewStatus2 = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel>
                {
                    new() { ContactId = contact.Id, TaskContactStatus = TaskContactStatus.Unconfirmed, AttendeeStatusId = attendeeStatusUnconfirmed.Id }
                });

            await DataContext.AddRangeAsync(taskWithLegacyStatus, taskWithLegacyStatus2, taskWithNewStatus, taskWithNewStatus2);
            await DataContext.AddRangeAsync(contact);
            await DataContext.AddRangeAsync(attendeeStatusConfirmed, attendeeStatusUnconfirmed);
            await DataContext.SaveChangesAsync();

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Reset();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&attendeeStatusId={attendeeStatusConfirmed.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(2);

            result.Items.Should().BeEquivalentTo([
                new { taskWithLegacyStatus.Id },
                new { taskWithNewStatus.Id },
            ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
        }

        [Fact]
        public async Task Get_tasks_with_attendee_status_id_filter_with_updated_task_contact()
        {
            var contact = new ContactFaker(ProviderId)
                .Generate()
                .ToDataModel();

            var customAttendeeStatusId = Guid.NewGuid().ToString();

            var task1 = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel>
                {
                    new() { ContactId = contact.Id, TaskContactStatus = TaskContactStatus.Unconfirmed, AttendeeStatusId = customAttendeeStatusId }
                });
            
            var task2 = new TaskFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.StaffIds, [PersonId])
                .Generate()
                .ToDataModel()
                .WithContact(new List<TaskContactDataModel>
                {
                    new() { ContactId = contact.Id, TaskContactStatus = TaskContactStatus.Unconfirmed }
                });

            await DataContext.AddRangeAsync(task1, task2);
            await DataContext.AddRangeAsync(contact);
            await DataContext.SaveChangesAsync();

            var connectedCalendarService = ResolveService<ConnectedCalendarServiceMock>();
            connectedCalendarService.Reset();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/tasks?staffId={PersonId}&attendeeStatusId={nameof(TaskContactStatus.Unconfirmed)}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<TaskModel>>();
            result.Should().NotBeNull();

            result.Items.Should().HaveCount(1);
            
            result.Items.Should().BeEquivalentTo([
                new { task2.Id },
            ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
        }
    }
}