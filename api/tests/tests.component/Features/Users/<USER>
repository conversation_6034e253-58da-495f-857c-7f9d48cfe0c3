﻿using carepatron.core.Application.ConnectedApps.Abstractions.Settings;
using carepatron.core.Application.ConnectedApps.Factory;
using carepatron.core.Application.ConnectedApps.Models.Settings;
using carepatron.core.Application.Users.Models;
using carepatron.core.Mappers;
using carepatron.infra.sql.Models.ConnectedApp;
using carepatron.infra.sql.SeedData;
using carepatron.infra.stripe.Extensions;
using Newtonsoft.Json.Linq;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Builders.DataModels;
using tests.common.Extensions;
using tests.component.Builders;
using tests.component.Data.Mappers;
using tests.component.Extensions;
using Microsoft.EntityFrameworkCore;
using tests.common.StripeFakes.Fakers;

namespace tests.component.Features.Users
{
    [Trait(nameof(CodeOwner), CodeOwner.Platform)]
    public class GetInitialContextTests(ComponentTestFixture testFixture) : BaseTestClass(testFixture)
    {
        [Fact]
        public async Task get_initialContext_should_return_InitialContextResult_when_providing_currentProviderId()
        {
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();

            result.Person.Should().NotBeNull();
            result.ConnectedApps.Should().NotBeNull();
            result.Providers.Should().NotBeNull();
            result.Staff.Should().NotBeNull();
            result.Features.Should().NotBeNull();
            result.Features.Should().BeEmpty();
            result.BillingAccount.Should().NotBeNull();
            result.BillingSettings.Should().NotBeNull();
        }

        [Fact]
        public async Task GetInitialContext_ShouldReturnStripeAccountInfo_WhenStripeIsSetupOnBillingSettings()
        {
            var existingSettings = await DataContext
                .ProviderBillingSettings.Where(settings => settings.ProviderId == ProviderId)
                .SingleOrDefaultAsync();

            var accountDetails = new StripeAccountFaker().Generate().ToPaymentAccountDetails();

            existingSettings.ChargesEnabled = accountDetails.ChargesEnabled;
            existingSettings.PayoutsEnabled = accountDetails.PayoutsEnabled;
            existingSettings.IsRejected = accountDetails.IsRejected;
            existingSettings.IsOnboardingComplete = accountDetails.IsOnboardingComplete;
            existingSettings.StatementDescriptor = accountDetails.StatementDescriptor;
            existingSettings.HasRequirementsDue = accountDetails.HasRequirementsDue;
            existingSettings.StripeAccountId = accountDetails.Id;
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();
 
            result.BillingSettings.Should().NotBeNull();
            result.BillingSettings.StripeAccountInfo.Should().BeEquivalentTo(accountDetails);
        }

        [Fact]
        public async Task get_initialContext_should_return_InitialContextResult_when_providing_null_currentProviderId()
        {
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId=")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();
            result.Person.Should().NotBeNull();
            result.ConnectedApps.Should().NotBeNull();
            result.Providers.Should().NotBeNull();
            result.Staff.Should().NotBeNull();
            result.Features.Should().NotBeNull();
            result.Features.Should().BeEmpty();
            result.BillingAccount.Should().NotBeNull();
            result.BillingSettings.Should().NotBeNull();
        }
        
        [Fact]
        public async Task Get_initialContext_should_auto_select_currentProviderId_when_providing_non_existing_currentProviderId()
        {
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={Guid.NewGuid()}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();

            result.CurrentProviderId.Should().Be(ProviderId);
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task get_initial_context_should_return_settings_object(bool hasSettings)
        {
            ConnectedAppDataModel appModel;

            if (hasSettings)
            {
                var product = ConnectedAppProductsSeedData.Products.FirstOrDefault(x => x.Code == ConnectedAppProducts.Google);

                appModel = ConnectedAppModelBuilder.Any()
                    .WithPersonId(PersonId)
                    .WithProviderId(ProviderId)
                    .WithProductId(product.Id)
                    .Create();

                var settingsInstance = ConnectedAppSettingsFactory.Create(ConnectedAppProducts.Google);

                var calendarSettings = settingsInstance.AsSettingType<IConnectedAppCalendarSettings>()?.Calendar;
                if (calendarSettings != null)
                {
                    calendarSettings.Push = true;
                    calendarSettings.Display = true;
                }

                var inboxSettings = settingsInstance.AsSettingType<IConnectedAppInboxSettings>()?.Inbox;
                if (inboxSettings != null)
                {
                    inboxSettings.Sync = true;
                }

                appModel.Settings = settingsInstance;
            }
            else
            {
                appModel = ConnectedAppModelBuilder.Any()
                .WithPersonId(PersonId)
                .WithProviderId(ProviderId)
                .Create();

                appModel.Settings = null;
            }

            DataContext.AddRange(appModel);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var stringResult = await response.Content.ReadAsStringAsync();
            var result = await response.Content.ReadAsAsync<InitialContextResult>();
            var connectedApp = result.ConnectedApps.First();

            result.ConnectedApps.Should().HaveCount(1);

            if (hasSettings)
            {
                var settings = (GoogleConnectedAppSettings)connectedApp.Settings;
                var appSettings = (GoogleConnectedAppSettings)appModel.Settings;

                settings.Should().NotBeNull();
                settings.ProductCode.Should().Be(appSettings.ProductCode);
                settings.Version.Should().Be(appSettings.Version);
                settings.Inbox.Sync.Should().Be(appSettings.Inbox.Sync);
                settings.Calendar.Push.Should().Be(appSettings.Calendar.Push);
                settings.Calendar.Display.Should().Be(appSettings.Calendar.Display);

                // Ensure settings are camel case
                stringResult.Should()
                    .Contain(
                        "\"settings\":{\"inbox\":{\"sync\":true,\"inboxId\":null},\"calendar\":{\"push\":true,\"display\":true,\"useTwoWaySync\":false},\"version\":\"1.0\",\"productCode\":\"GOOGLE\"}");
            }
            else
            {
                connectedApp.Settings.Should().BeNull();
                stringResult.Should().Contain("\"settings\":null");
            }
        }

        [Fact]
        public async Task Get_initialContext_should_return_InitialContextResult_with_PersonalSettings()
        {
            // arrange
            var faker = new PersonalSettingsFaker(PersonId);

            //arrange
            var existingPersonalSettings = new PersonalSettingsFaker(PersonId)
                .RuleFor(_ => _.TimeZone, "Asia/Manila")
                .RuleFor(_ => _.Locale, "en-US")
                .Generate();

            DataContext.PersonalSettings.Add(existingPersonalSettings.ToDataModel());

            await DataContext.SaveChangesAsync();


            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();

            result.PersonalSettings.Should().NotBeNull();
            result.PersonalSettings.Should().BeEquivalentTo(existingPersonalSettings);
        }


        [Fact]
        public async Task Get_initialContext_should_return_InitialContextResult_with_PersonalPreference()
        {
            // arrange
            var faker = new PersonalPreferenceFaker(PersonId);

            var calendarPreferences = new Dictionary<string, object>
                    {
                        { "appointmentColor", "Team" },
                        { "appointmentStatus", "Unconfirmed" },
                        { "groupBy", "Location" },
                        { "invoiceStatus", "Paid" },
                        { "secondaryTimeZone", null },
                        { "showWeekends", false },
                        { "startOfWeek", "Sunday" }
                    };
            var dataModel = faker
                .RuleFor(_ => _.Key, "calendar")
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Preferences, calendarPreferences)
                .Generate()
                .ToDataModel();

            DataContext.Add(dataModel);
            await DataContext.SaveChangesAsync();


            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();

            result.PersonalPreferences.Should().NotBeNull();
            result.PersonalPreferences.Should().BeEquivalentTo(new Dictionary<string, Dictionary<string, object>> { { "calendar", calendarPreferences } });
        }



        [Fact]
        public async Task Get_initialContext_should_return_with_PersonalPreferences_Including_NonProvider_Specific()
        {
            // arrange
            var otherProvider = new ProviderFaker().Generate().ToDataModel();
            var faker = new PersonalPreferenceFaker(PersonId);

            var provider1Preference = faker
                .RuleFor(_ => _.Key, "calendar")
                .RuleFor(_ => _.ProviderId, otherProvider.Id)
                .RuleFor(_ => _.Preferences, new Dictionary<string, object>
                    {
                        { "appointmentColor", "Team" },
                        { "appointmentStatus", "Confirmed" },
                        { "groupBy", "Location" },
                        { "invoiceStatus", "Unpaid" },
                        { "secondaryTimeZone", "Asia/Manila" },
                        { "showWeekends", true },
                        { "startOfWeek", "Monday" }
                    })
                .Generate()
                .ToDataModel();

            var provider2Preference = faker
                .RuleFor(_ => _.Key, "calendar")
                .RuleFor(_ => _.ProviderId, ProviderId)
                .RuleFor(_ => _.Preferences, new Dictionary<string, object>
                    {
                        { "appointmentColor", "Team" },
                        { "appointmentStatus", "Unconfirmed" },
                        { "groupBy", "Location" },
                        { "invoiceStatus", "Paid" },
                        { "secondaryTimeZone", null },
                        { "showWeekends", false },
                        { "startOfWeek", "Sunday" }
                    })
                .Generate()
                .ToDataModel();

            var provider3Preference = faker
                .RuleFor(_ => _.Key, "others")
                .RuleFor(_ => _.ProviderId, (Guid?)null)
                .RuleFor(_ => _.Preferences, new Dictionary<string, object>
                    {
                        { "setting1", true },
                        { "setting2", 1 },
                        { "setting3", "test-1" },
                    })
                .Generate()
                .ToDataModel();

            DataContext.AddRange(otherProvider, provider1Preference, provider2Preference, provider3Preference);
            await DataContext.SaveChangesAsync();


            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();

            result.PersonalPreferences.Should().NotBeNull();
            result.PersonalPreferences.Should().BeEquivalentTo(
                new Dictionary<string, Dictionary<string, object>>
                {
                    { "calendar", provider2Preference.Preferences },
                    { "others", provider3Preference.Preferences }
                });
        }

        [Fact]
        public async Task Get_initial_context_should_include_workspace_preference()
        {
            var workspaceSetting = new ProviderWorkspacePreferenceFaker(ProviderId, "calendar")
                .RuleFor(x => x.Preferences, x => new JObject
                {
                    { "eventColor1", "#111" },
                    { "eventColor2", "#222" },
                    { "eventColor3", "#333" }
                })
                .Generate()
                .ToDataModel();

            await DataContext.AddAsync(workspaceSetting);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();
            result.Should().NotBeNull();
            result.WorkspacePreferences.Should().NotBeNull();
            result.WorkspacePreferences.Should().ContainKey("calendar");
            result.WorkspacePreferences["calendar"].Should().BeEquivalentTo(workspaceSetting.Preferences);
        }


        [Fact]
        public async Task Get_Initial_context_Client_Portal_User_with_Provider_Test()
        {

            var clientPortalUser = new PersonFaker().Generate().ToDataModel();

            var contact = new ContactFaker(ProviderId)
                .WithPerson(clientPortalUser)
                .Generate()
                .ToDataModel();

            DataContext.AddRange(clientPortalUser, contact);

            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(clientPortalUser.PersonAsJwtToken())
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();
            result.Should().NotBeNull();
            result.CanBookAppointment.Should().BeTrue();
        }

        [Fact]
        public async Task Get_Initial_context_Client_Portal_User_No_Provider_Test()
        {

            var clientPortalUser = new PersonFaker().Generate().ToDataModel();


            DataContext.AddRange(clientPortalUser);

            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(clientPortalUser.PersonAsJwtToken())
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();
            result.Should().NotBeNull();
            result.CanBookAppointment.Should().BeFalse();
        }

        [Fact]
        public async Task Get_Initial_context_Client_Portal_User_Should_Include_Providers()
        {
            var clientPortalUser = new PersonFaker().Generate();
            var provider1 = new ProviderFaker().RuleFor(x => x.LogoId, (string)null).Generate();
            var provider2 = new ProviderFaker().RuleFor(x => x.LogoId, (string)null).Generate();
            var provider3 = new ProviderFaker().RuleFor(x => x.LogoId, (string)null).Generate();

            var staff1 = new ProviderStaffFaker(provider1.Id, clientPortalUser.Id).Generate();
            var staff2 = new ProviderStaffFaker(provider2.Id, clientPortalUser.Id).Generate();


            var contact = new ContactFaker(provider3.Id)
                .WithPerson(clientPortalUser)
                .Generate();

            DataContext.AddRange(clientPortalUser.ToDataModel(), provider1.ToDataModel(), provider2.ToDataModel(), provider3.ToDataModel(), staff1.ToDataModel(), staff2.ToDataModel(), contact.ToDataModel());

            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?isPortal=true")
                .WithBearerAuthorization(clientPortalUser.PersonAsJwtToken())
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();
            result.Should().NotBeNull();
            result.CanBookAppointment.Should().BeTrue();

            var expectedProviders = ProviderMapper.ToProviderStaffDetails([provider1, provider2], [staff1, staff2]);
            result.Providers.Should().BeEquivalentTo(expectedProviders, opt=>opt.UsingSimpleDateTimePrecision());
        }

        [Fact]
        public async Task Initial_context_should_include_registeredWorkspace_object()
        {
            var registeredWorkspace = new RegisteredWorkspaceFaker(ProviderId)
                .Generate()
                .ToDataModel();

            await DataContext.AddAsync(registeredWorkspace);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();

            result.Person.Should().NotBeNull();
            result.ConnectedApps.Should().NotBeNull();
            result.Providers.Should().NotBeNull();
            result.Staff.Should().NotBeNull();
            result.Features.Should().NotBeNull();
            result.Features.Should().BeEmpty();
            result.BillingAccount.Should().NotBeNull();
            result.BillingSettings.Should().NotBeNull();
            result.RegisteredWorkspace.Should().NotBeNull();
            result.RegisteredWorkspace.Should()
                .BeEquivalentTo(registeredWorkspace, opt => opt.ExcludingMissingMembers());
        }

        [Fact]
        public async Task Get_Initial_context_Client_Portal_User_With_Personal_Settings_Test()
        {
            var clientPortalUser = new PersonFaker().Generate().ToDataModel();
            var personalSettings = new PersonalSettingsFaker(clientPortalUser.Id)
                .RuleFor(_ => _.TimeZone, "Asia/Manila")
                .RuleFor(_ => _.Locale, "en-US")
                .Generate();

            DataContext.AddRange(clientPortalUser, personalSettings.ToDataModel());

            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, "api/me/initialContext")
                .WithBearerAuthorization(clientPortalUser.PersonAsJwtToken())
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();
            result.Should().NotBeNull();
            result.PersonalSettings.Should().NotBeNull();
            result.PersonalSettings.Should().BeEquivalentTo(personalSettings);
        }

        [Fact]
        public async Task Get_Initial_context_Client_Portal_User_Without_Personal_Settings_Test()
        {
            var clientPortalUser = new PersonFaker().Generate().ToDataModel();

            DataContext.AddRange(clientPortalUser);

            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, "api/me/initialContext")
                .WithBearerAuthorization(clientPortalUser.PersonAsJwtToken())
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();
            result.Should().NotBeNull();
            result.PersonalSettings.Should().BeNull();
        }

        [Theory]
        [InlineData(true, null)]
        [InlineData(false, null)]
        [InlineData(true, "ac156138-7c0c-41d7-bc08-893157d6ccb9")]
        [InlineData(false, "ac156138-7c0c-41d7-bc08-893157d6ccb9")]
        public async Task get_initial_context_should_return_settings_with_and_without_inbox_id(bool isSync, string id)
        {
            var product = ConnectedAppProductsSeedData.Products.FirstOrDefault(x => x.Code == ConnectedAppProducts.Google);

            var inboxId = (id.IsNullOrEmpty()) ? (Guid?)null : Guid.Parse(id);

            var settings = new GoogleConnectedAppSettingsFaker()
                .WithCalendar(true, true)
                .WithInbox(isSync, inboxId)
                .Generate();

            var connectedApp = ConnectedAppModelBuilder.Any()
                .WithPersonId(PersonId)
                .WithProviderId(ProviderId)
                .WithProductId(product.Id)
                .WithPushToCalendar(true)
                .WithDisplayCalendar(true)
                .WithSettings(settings)
                .WithAccount("<EMAIL>")
                .Create();

            DataContext.AddRange(connectedApp);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();

            result.ConnectedApps.Should().HaveCount(1);

            var app = result.ConnectedApps.First();

            var appSettings = app.Settings.AsSettingType<IConnectedAppInboxSettings>();

            appSettings.Inbox.Sync.Should().Be(isSync);
            appSettings.Inbox.InboxId.Should().Be(inboxId);
        }

        [Fact]
        public async Task GetInitialContext_Should_Update_Provider_LastAccessedUtc()
        {
            // Arrange - Update the current provider's LastAccessedUtc before the call
            var provider = await DataContext.Providers
                .AsNoTracking()
                .FirstAsync(p => p.Id == ProviderId);
            provider.LastAccessedUtc = DateTime.UtcNow.AddMinutes(-10); // Set to 10 minutes ago
            await DataContext.SaveChangesAsync();
            
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();
            

            // Act - Call the getInitialContext endpoint
            var response = await ClientApi.SendAsync(request);

            // Assert - Verify response is successful
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();
            result.Should().NotBeNull();
            result.CurrentProviderId.Should().Be(ProviderId);

            // Assert - Verify LastAccessedUtc has been updated
            var providerAfter = await DataContext.Providers
                .AsNoTracking()
                .FirstAsync(p => p.Id == ProviderId);
            
            providerAfter.LastAccessedUtc.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        [Fact]
        public async Task GetInitialContext_Should_Update_LastAccessedUtc_When_ProviderId_Is_Null()
        {
            // Arrange - Get the current provider's LastAccessedUtc before the call
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, "api/me/initialContext")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act - Call the getInitialContext endpoint without providerId
            var response = await ClientApi.SendAsync(request);

            // Assert - Verify response is successful
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();
            result.Should().NotBeNull();

            // Assert - Verify LastAccessedUtc has NOT been updated for our provider
            // since we didn't specify a currentProviderId
            var providerAfter = await DataContext.Providers
                .AsNoTracking()
                .FirstAsync(p => p.Id == ProviderId);
            
            providerAfter.LastAccessedUtc.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        }

        [Fact]
        public async Task GetInitialContext_Should_Update_LastAccessedUtc_Only_For_Current_Provider()
        {
            // Arrange - Create another provider
            var otherProvider = new ProviderFaker().Generate().ToDataModel();
            DataContext.Providers.Add(otherProvider);
            await DataContext.SaveChangesAsync();
            
            // Get initial LastAccessedUtc for both providers
            var currentProviderBefore = await DataContext.Providers
                .AsNoTracking()
                .FirstAsync(p => p.Id == ProviderId);
            var otherProviderBefore = await DataContext.Providers
                .AsNoTracking()
                .FirstAsync(p => p.Id == otherProvider.Id);
            
            var currentProviderInitialTime = currentProviderBefore.LastAccessedUtc;
            var otherProviderInitialTime = otherProviderBefore.LastAccessedUtc;
            
            // Add a small delay to ensure timestamp difference
            await Task.Delay(100);
            
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/me/initialContext?currentProviderId={ProviderId}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            // Act - Call the getInitialContext endpoint with specific providerId
            var response = await ClientApi.SendAsync(request);

            // Assert - Verify response is successful
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<InitialContextResult>();
            result.Should().NotBeNull();
            result.CurrentProviderId.Should().Be(ProviderId);

            // Assert - Verify only current provider's LastAccessedUtc was updated
            var currentProviderAfter = await DataContext.Providers
                .AsNoTracking()
                .FirstAsync(p => p.Id == ProviderId);
            var otherProviderAfter = await DataContext.Providers
                .AsNoTracking()
                .FirstAsync(p => p.Id == otherProvider.Id);
            
            // Current provider should be updated
            currentProviderAfter.LastAccessedUtc.Should().BeAfter(currentProviderInitialTime);
            
            // Other provider should remain unchanged
            otherProviderAfter.LastAccessedUtc.Should().Be(otherProviderInitialTime);
        }
    }
}
