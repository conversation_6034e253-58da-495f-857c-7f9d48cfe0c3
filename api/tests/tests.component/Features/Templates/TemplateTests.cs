﻿using System.Net;
using carepatron.api.Contracts.Requests.Templates;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Notes.Models;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Application.Users.Models;
using carepatron.core.Models.Billing;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Media;
using carepatron.core.Models.Pagination;
using carepatron.core.Models.Tags;
using carepatron.infra.sql.Models.Folders;
using carepatron.infra.sql.Models.Templates;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using tests.common.Builders.DataModels;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Extensions;
using tests.common.Mocks;
using tests.common.Utilities;
using tests.component.Builders;
using tests.component.Builders.Requests;
using tests.component.Data.Fakers;
using TestFileUtilities = tests.common.Utilities.FileUtilities;

namespace tests.component.Features.Templates;

[Trait(nameof(CodeOwner), CodeOwner.NotesAndDocuments)]
public class TemplateTests : BaseTestClass
{
    public TemplateTests(ComponentTestFixture testFixture) : base(testFixture)
    {
    }

    [Fact]
    public async Task GetTemplate_Test()
    {
        var model = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        await DataContext.AddRangeAsync(model);
        await DataContext.SaveChangesAsync();

        var request =
            new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/templates/{model.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        model.Should().BeEquivalentTo(result, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task GetTemplate_ContentJsonDepth_GreaterThan83_ShouldFail_Test()
    {
        var model = new TemplateFaker()
            .RuleFor(_ => _.CreatedByPersonId, PersonId)
            .RuleFor(_ => _.LastUpdatedByPersonId, PersonId)
            .RuleFor(_ => _.ProviderId, ProviderId)
            .RuleFor(_ => _.ContentJson, JObject.FromObject(JsonUtilities.CreateNestedObject(84)))
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(model);
        await DataContext.SaveChangesAsync();

        var request =
            new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/templates/{model.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.BadRequest);

        DataContext.Remove(model);
        await DataContext.SaveChangesAsync();
    }

    [Fact]
    public async Task GetTemplate_ContentJsonDepth_LessThan83_ShouldOK_Test()
    {
        var model = new TemplateFaker()
            .RuleFor(_ => _.CreatedByPersonId, PersonId)
            .RuleFor(_ => _.LastUpdatedByPersonId, PersonId)
            .RuleFor(_ => _.ProviderId, ProviderId)
            .RuleFor(_ => _.ContentJson, JObject.FromObject(JsonUtilities.CreateNestedObject(82)))
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(model);
        await DataContext.SaveChangesAsync();

        var request =
            new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/templates/{model.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        model.Should().BeEquivalentTo(result, opt =>
            opt.ExcludingMissingMembers()
                .UsingSimpleDateTimePrecision()
                .Excluding(x => x.ContentJson)
                .Excluding(x => x.FileAttachments)
                .Excluding(x => x.AiPrompts));
    }

    [Fact]
    public async Task GetTemplate_WithContentJsonOnly_Test()
    {
        var model = new TemplateFaker()
            .RuleFor(_ => _.CreatedByPersonId, PersonId)
            .RuleFor(_ => _.LastUpdatedByPersonId, PersonId)
            .RuleFor(_ => _.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        model.ContentJsonb = null;

        await DataContext.AddRangeAsync(model);
        await DataContext.SaveChangesAsync();

        var request =
            new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/templates/{model.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        model.Should().BeEquivalentTo(result, opt =>
            opt.ExcludingMissingMembers()
                .UsingSimpleDateTimePrecision()
                .Excluding(x => x.ContentJson)
                .Excluding(x => x.FileAttachments)
                .Excluding(x => x.AiPrompts));

        result.ContentJson.Should().BeEquivalentTo(JObject.Parse(model.ContentJson));
    }

    [Fact]
    public async Task GetTemplates_Test()
    {
        var collection = TemplateCollectionDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        var model = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .WithCollectionId(collection.Id)
            .Create();

        await DataContext.AddRangeAsync(model, collection);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates?collectionId={collection.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<Template>>();
        var template = result.Items.First(x => x.Id == model.Id);

        result.Items.All(x => x.ProviderId == ProviderId).Should().BeTrue();
        template.Should().BeEquivalentTo(model, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task GetTemplates_ByType_Test()
    {
        var collection = TemplateCollectionDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        var model = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .WithCollectionId(collection.Id)
            .WithType(TemplateType.Enrolment)
            .Create();

        var model2 = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .WithCollectionId(collection.Id)
            .Create();

        await DataContext.AddRangeAsync(model, model2, collection);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates?type={TemplateType.Enrolment}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<Template>>();
        var template = result.Items.First(x => x.Id == model.Id);

        result.Items.All(x => x.ProviderId == ProviderId).Should().BeTrue();
        template.Should().BeEquivalentTo(model, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task GetTemplatesUsedByPerson_Test()
    {
        var collection = TemplateCollectionDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        var model = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .WithCollectionId(collection.Id)
            .Create();

        var templateUsedByPersonDataModel = TemplatesUsedByPersonDataModelBuilder.Any()
            .WithPersonId(PersonId)
            .WithProviderId(ProviderId)
            .WithFavourites(model.Id)
            .WithRecentlyUsed(model.Id)
            .Create();

        await DataContext.AddRangeAsync(model, collection, templateUsedByPersonDataModel);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/templates/me")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<TemplatesUsedByPersonResponse>();
        var recentlyUsed = result.RecentlyUsed.First();
        var fav = result.RecentlyUsed.First();

        recentlyUsed.Id.Should().Be(model.Id);
        fav.Id.Should().Be(model.Id);
    }

    [Fact]
    public async Task CreateTemplate_Test()
    {
        var tagModel = TagDataModelBuilder.Any()
            .WithTitle("Important")
            .WithProviderId(ProviderId).WithType(TagType.Template)
            .Create();
        var existingTag = new Tag(tagModel.Id, ProviderId, "Important", TagType.Template);
        var missingTag = new Tag(Guid.Empty, ProviderId, "Tucks", TagType.Template);

        var folder = new FolderFaker(ProviderId, PersonId)
            .Generate()
            .ToDataModel();
        
        await DataContext.AddRangeAsync(tagModel, folder);
        await DataContext.SaveChangesAsync();

        var model = new SaveTemplateRequestFaker()
            .RuleFor(x => x.Tags, new[] { existingTag, missingTag })
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.FolderId, folder.Id)
            .Generate();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/templates")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(model)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        result.Should().BeEquivalentTo(model, opt => opt.ExcludingMissingMembers().Excluding(x => x.Tags));
        result.Tags.Select(x => x.Title).Should().Contain(existingTag.Title);
        result.Tags.Select(x => x.Title).Should().Contain(missingTag.Title);
        result.FolderId.Should().Be(folder.Id);

        var missingTagModel = await DataContext.Tags.AsNoTracking()
            .FirstOrDefaultAsync(x => x.ProviderId == ProviderId && x.Title == missingTag.Title);

        missingTagModel.Should().NotBeNull();

        var missingTemplateTagModel = DataContext.TemplateTags.AsNoTracking()
            .FirstOrDefaultAsync(x => x.TemplateId == result.Id && x.TagId == missingTagModel.Id);

        missingTemplateTagModel.Should().NotBeNull();

        var templateFolder =
            await DataContext.TemplateFolders.AsNoTracking().FirstOrDefaultAsync(x => x.TemplateId == result.Id && x.FolderId == folder.Id);

        templateFolder.Should().NotBeNull();
    }
    
    [Fact]
    public async Task Create_Template_Empty_Body_Returns_BadRequest()
    {
        var request =
            new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/templates")
                .WithBearerAuthorization(IdentityToken)
                .WithContent(new StringContent(JsonConvert.SerializeObject(null, (JsonSerializerSettings)null), Encoding.UTF8, "application/json"))
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task CreateTemplate_ContentJsonDepth_LessThan83_ShouldOK_Test()
    {
        var model = new SaveTemplateRequestFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(_ => _.ContentJson, JObject.FromObject(JsonUtilities.CreateNestedObject(82)))
            .Generate();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/templates")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(model)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        result.Should().BeEquivalentTo(model, opt => opt.ExcludingMissingMembers());
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task Get_templates_should_filter_by_hasAiPrompts(bool hasAiPrompts)
    {
        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.HasAiPrompts, true)
            .Generate(3)
            .ToDataModel()
            .ToList();

        templates.AddRange(new TemplateFaker()
            .RuleFor(x => x.HasAiPrompts, false)
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate(3)
            .ToDataModel()
            .ToList());

        await DataContext.AddRangeAsync(templates);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates?hasAiPrompts={hasAiPrompts}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<Template>>();
        result.Should().NotBeNull();

        result.Items.Should().NotBeEmpty();
        result.Items.Select(x => x.HasAiPrompts).All(x => x == hasAiPrompts).Should().BeTrue();
    }

    [Fact]
    public async Task CreateTemplate_ContentJsonDepth_GreaterThan83_ShouldFail_Test()
    {
        var model = new SaveTemplateRequestFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(_ => _.ContentJson, JObject.FromObject(JsonUtilities.CreateNestedObject(84)))
            .Generate();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/templates")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(model)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task CreateTemplate_And_Share_To_Community_Test()
    {
        var tag = TagDataModelBuilder.Any()
            .WithTitle("Important")
            .WithProviderId(ProviderId).WithType(TagType.Template)
            .Create();

        await DataContext.AddRangeAsync(tag);
        await DataContext.SaveChangesAsync();

        var model = new SaveTemplateRequestFaker()
            .RuleFor(x => x.Tags,
                new[] { new Tag(tag.Id, ProviderId, "Important", TagType.Template, tag.ColorHex) })
            .RuleFor(x => x.ShareToCommunity, true)
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/templates")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(model)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        result.Should().BeEquivalentTo(model, opt => opt.ExcludingMissingMembers().Excluding(x => x.ContentJson));

        var publicTemplate =
            await DataContext.PublicTemplates.AsNoTracking().Include(x => x.Tags)
                .FirstOrDefaultAsync(x => x.Id == result.Id);

        publicTemplate.Should().BeEquivalentTo(model,
            opt => opt.ExcludingMissingMembers().UsingCaseInsensitivity().Excluding(x => x.ContentJson));

        var templateJson = await response.Content.ReadAsAsync<JObject>();
        var contentJson = templateJson.GetValue("contentJson");
        contentJson.Type.Should().Be(JTokenType.String);
    }

    [Fact]
    public async Task UpdatedTemplate_Test()
    {
        var tag = TagDataModelBuilder.Any().WithProviderId(ProviderId).WithType(TagType.Template)
            .Create();

        var model = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .WithTags(new[] { tag.Id })
            .Create();
        
        var folder = new FolderFaker(ProviderId, PersonId)
            .Generate()
            .ToDataModel();
        
        var templateFolder = new TemplateFolderDataModel(folder.Id, model.Id);

        var newFolder = new FolderFaker(ProviderId, PersonId)
            .Generate()
            .ToDataModel(); 
        
        await DataContext.AddRangeAsync(tag, model, folder, templateFolder, newFolder);
        await DataContext.SaveChangesAsync();

        var updatedModel = new SaveTemplateRequestFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.Title, "Enrollment")
            .RuleFor(x => x.FolderId, newFolder.Id)
            .Generate();

        var request =
            new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/templates/{model.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(updatedModel)
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        result.Should().BeEquivalentTo(updatedModel,
            opt => opt.ExcludingMissingMembers().Excluding(x => x.ContentJson));
        
        result.FolderId.Should().Be(newFolder.Id);

        var templateJson = await response.Content.ReadAsAsync<JObject>();
        var contentJson = templateJson.GetValue("contentJson");
        contentJson.Type.Should().Be(JTokenType.String);
        
        templateFolder =
            await DataContext.TemplateFolders.FirstOrDefaultAsync(x => x.TemplateId == result.Id && x.FolderId == newFolder.Id);

        templateFolder.Should().NotBeNull();
    }
    
    [Fact]
    public async Task Update_Template_Empty_Body_Returns_BadRequest()
    {
        var request =
            new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/templates/{Guid.NewGuid()}")
                .WithBearerAuthorization(IdentityToken)
                .WithContent(new StringContent(JsonConvert.SerializeObject(null, (JsonSerializerSettings)null), Encoding.UTF8, "application/json"))
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Code.Should().Be(Errors.RequestCannotBeEmptyCode);
        result.Details.Should().Be(Errors.RequestCannotBeEmptyDetails);
    }
    
    [Fact]
    public async Task Update_Template_Null_Collection_Set_To_Other()
    {
        var collection = TemplateCollectionDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .WithTitle("Other")
            .Create();
        
        var template = new TemplateFaker()
            .RuleFor(_ => _.CreatedByPersonId, PersonId)
            .RuleFor(_ => _.LastUpdatedByPersonId, PersonId)
            .RuleFor(_ => _.ProviderId, ProviderId)
            .RuleFor(x => x.TemplateCollectionId, collection.Id)
            .Generate()
            .ToDataModel();
        
        await DataContext.AddRangeAsync(template, collection);
        await DataContext.SaveChangesAsync();
        
        var updatedModel = new SaveTemplateRequestFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.Collection, (string)null)
            .RuleFor(x => x.Title, "Enrollment")
            .Generate();
        
        var request =
            new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/templates/{template.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(updatedModel)
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var result = await response.Content.ReadAsAsync<Template>();
        result.Collection.Should().Be("Other");
    }
    
    [Fact]
    public async Task Update_Template_Null_Collection_Id_Set_To_Other()
    {
        var collection = TemplateCollectionDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .WithTitle("Other")
            .Create();
        
        var template = new TemplateFaker()
            .RuleFor(_ => _.CreatedByPersonId, PersonId)
            .RuleFor(_ => _.LastUpdatedByPersonId, PersonId)
            .RuleFor(_ => _.ProviderId, ProviderId)
            .RuleFor(x => x.TemplateCollectionId, collection.Id)
            .Generate()
            .ToDataModel();
        
        await DataContext.AddRangeAsync(template, collection);
        await DataContext.SaveChangesAsync();
        
        var updatedModel = new SaveTemplateRequestFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.TemplateCollectionId, (Guid?)null)
            .RuleFor(x => x.Title, "Enrollment")
            .Generate();
        
        var request =
            new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/templates/{template.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(updatedModel)
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var result = await response.Content.ReadAsAsync<Template>();
        
        result.Collection.Should().Be("Other");
    }

    [Fact]
    public async Task UpdatedTemplate_WithJObjectContentJson_Test()
    {
        var tag = TagDataModelBuilder.Any().WithProviderId(ProviderId).WithType(TagType.Template)
            .Create();

        var model = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .WithTags(new[] { tag.Id })
            .Create();

        await DataContext.AddRangeAsync(tag, model);
        await DataContext.SaveChangesAsync();

        var updatedModel = new SaveTemplateRequestFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.Title, "Enrollment")
            .Generate();

        var request =
            new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/templates/{model.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithHeader("x-version", "1.0.0.0")
                .WithPayload(updatedModel)
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        result.Should().BeEquivalentTo(updatedModel,
            opt => opt.ExcludingMissingMembers().Excluding(x => x.ContentJson));

        var templateJson = await response.Content.ReadAsAsync<JObject>();
        var contentJson = templateJson.GetValue("contentJson");
        contentJson.Type.Should().Be(JTokenType.Object);
    }

    [Fact]
    public async Task UpdatedTemplate_And_Share_To_Community_Creates_Public_Test()
    {
        var model = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        await DataContext.AddAsync(model);
        await DataContext.SaveChangesAsync();

        var updatedModel = new SaveTemplateRequestFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.Title, "Enrollment")
            .RuleFor(x => x.ShareToCommunity, true)
            .Generate();

        var request =
            new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/templates/{model.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(updatedModel)
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        result.Should().BeEquivalentTo(updatedModel, opt => opt.ExcludingMissingMembers());

        var publicTemplate =
            await DataContext.PublicTemplates.AsNoTracking()
                .Include(x => x.Tags)
                .Include(x => x.FileAttachments)
                .FirstOrDefaultAsync(x => x.Id == result.Id);

        publicTemplate.Should().BeEquivalentTo(result, opt =>
            opt.ExcludingMissingMembers().Excluding(x => x.CreatedDateTimeUtc).Excluding(x => x.UpdatedDateTimeUtc)
                .Excluding(x => x.ContentJson));

        var templateJson = await response.Content.ReadAsAsync<JObject>();
        var contentJson = templateJson.GetValue("contentJson");
        contentJson.Type.Should().Be(JTokenType.String);
    }

    [Fact]
    public async Task UpdatedTemplate_And_Share_To_Community_Updates_Public_Test()
    {
        var model = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        var existingPublicTemplateModel = PublicTemplateDataModelBuilder.Any()
            .WithId(model.Id)
            .Create();

        await DataContext.AddRangeAsync(model, existingPublicTemplateModel);
        await DataContext.SaveChangesAsync();

        var updatedModel = new SaveTemplateRequestFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.Title, "Enrollment")
            .RuleFor(x => x.ShareToCommunity, true)
            .Generate();

        var request =
            new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/templates/{model.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(updatedModel)
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        result.Should().BeEquivalentTo(updatedModel, opt => opt.ExcludingMissingMembers());

        var publicTemplate =
            await DataContext.PublicTemplates.AsNoTracking()
                .Include(x => x.Tags)
                .Include(x => x.FileAttachments)
                .FirstOrDefaultAsync(x => x.Id == result.Id);

        publicTemplate.Should().BeEquivalentTo(result, opt =>
            opt.ExcludingMissingMembers().Excluding(x => x.CreatedDateTimeUtc).Excluding(x => x.UpdatedDateTimeUtc)
                .Excluding(x => x.ContentJson));

        var templateJson = await response.Content.ReadAsAsync<JObject>();
        var contentJson = templateJson.GetValue("contentJson");
        contentJson.Type.Should().Be(JTokenType.String);
    }

    [Fact]
    public async Task CreateTemplatesUsedByPerson_Test()
    {
        var model = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        var model2 = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        await DataContext.AddRangeAsync(model, model2);
        await DataContext.SaveChangesAsync();

        var payloadRequest = SaveTemplatesUsedByPersonRequestBuilder
            .Any()
            .WithAddFavourites(model.Id)
            .WithAddRecentlyUsed(model2.Id)
            .WithProviderId(ProviderId)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/templates/me")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payloadRequest)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<TemplatesUsedByPerson>();

        result.Favourites.Should().Contain(model.Id);
        result.RecentlyUsed.Should().Contain(model2.Id);
    }

    [Fact]
    public async Task AddToTemplatesUsedByPerson_Test()
    {
        var model = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        var model2 = TemplateDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        var templatesUsedByPersonDataModel = TemplatesUsedByPersonDataModelBuilder.Any()
            .WithPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        await DataContext.AddRangeAsync(model, model2, templatesUsedByPersonDataModel);
        await DataContext.SaveChangesAsync();

        var payloadRequest = SaveTemplatesUsedByPersonRequestBuilder
            .Any()
            .WithAddFavourites(model.Id)
            .WithAddRecentlyUsed(model2.Id)
            .WithProviderId(ProviderId)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/templates/me")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payloadRequest)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<TemplatesUsedByPerson>();

        result.Favourites.Should().Contain(model.Id);
        result.RecentlyUsed.Should().Contain(model2.Id);
    }

    [Fact]
    public async Task Get_templates_should_have_isPublished_flag()
    {
        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.IsPublished, true)
            .Generate(10)
            .ToDataModel();

        await DataContext.AddRangeAsync(templates);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/templates")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<Template>>();
        result.Should().NotBeNull();
        result.Items.Should().NotBeNullOrEmpty();

        foreach (var item in result.Items)
        {
            item.Should().BeEquivalentTo(new
            {
                IsPublished = true
            }, opt => opt.ExcludingMissingMembers());
        }
    }

    [Fact]
    public async Task DuplicateTemplate_Test()
    {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
        fileStorageRepositoryMock.UseRealImplementation();

        var templateId = Guid.NewGuid();
        var contentJson = new JObject
        {
            { "type", "doc" }
        };

        var content = new JArray();

        var formFieldModels = new TemplateFormFieldFaker(templateId)
            .Generate(3)
            .ToDataModel();

        var aiPromptModels = new TemplateAiPromptFaker(templateId)
            .Generate(3)
            .ToDataModel()
            .ToList();

        aiPromptModels[1].DeletedAtUtc = DateTime.UtcNow;

        var aiPromptsJObjects = aiPromptModels
            .Where(x => x.DeletedAtUtc == null)
            .Select(x => new JObject
            {
                { "type", "aiSmartPrompt" },
                {
                    "attrs", new JObject
                    {
                        { "id", x.Id },
                        { "version", x.Version },
                        { "type", "AiSmartPrompt" }
                    }
                }
            })
            .ToList();

        content.Add(aiPromptsJObjects);

        var formFieldObjects = formFieldModels.Select(x => new JObject
            {
                { "type", "formField" },
                {
                    "attrs", new JObject
                    {
                        { "id", x.Id },
                        { "version", x.Version },
                        { "type", "ClientInfo" }
                    }
                }
            })
            .ToList();

        content.Add(formFieldObjects);

        var signatureId = Guid.NewGuid();

        var signatureObject = new JObject
        {
            { "type", "formField" },
            {
                "attrs", new JObject
                {
                    { "id", signatureId },
                    { "version", "1.0.0" },
                    { "type", "Signature" }
                }
            }
        };

        content.Add(signatureObject);

        contentJson["content"] = content;

        var template = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.Id, templateId)
            .RuleFor(x => x.ContentJson, contentJson)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.HasAiPrompts, true)
            .Generate()
            .ToDataModel();

        DataContext.Add(template);
        DataContext.AddRange(formFieldModels);
        DataContext.AddRange(aiPromptModels);

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post,
                $"/api/providers/{ProviderId}/templates/{templateId}/duplicate")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();
        var resultContentJson = result.ContentJson.ToString();

        result.Id.Should().NotBe(template.Id);

        result.Form.Fields.Values.ToList().ForEach(field =>
        {
            resultContentJson.Contains(field.Id.ToString()).Should().BeTrue();

            formFieldModels.Select(x => x.Id).Any(x => x == field.Id).Should().BeFalse();
        });

        result.AiPrompts.ForEach(aiPrompt =>
        {
            resultContentJson.Contains(aiPrompt.Id.ToString()).Should().BeTrue();

            aiPromptModels.Select(x => x.Id).Any(x => x == aiPrompt.Id).Should().BeFalse();
        });

        result.AiPrompts.Should().HaveCount(2);
        result.HasAiPrompts.Should().BeTrue();
        resultContentJson.Should().NotContain(template.Id.ToString());
    }

    [Fact]
    public async Task GetTemplates_ShouldNotReturn_DeletedFormFields()
    {
        var templateModel = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate()
            .ToDataModel();

        var formFieldModels = new TemplateFormFieldFaker(templateModel.Id)
            .RuleFor(x => x.Deleted, true)
            .Generate(5)
            .ToDataModel();

        await DataContext.AddAsync(templateModel);
        await DataContext.AddRangeAsync(formFieldModels);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/templates")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<Template>>();
        result.Should().NotBeNull();
        result.Items.Should().NotBeNullOrEmpty();

        foreach (var item in result.Items)
        {
            item.Form.Fields.Should().NotContain(x => x.Value.Deleted);
        }
    }

    [Fact]
    public async Task DeletedTemplate_Test()
    {
        var model = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.IsPublished, false)
            .Generate()
            .ToDataModel();

        await DataContext.AddAsync(model);
        await DataContext.SaveChangesAsync();

        var request =
            new HttpRequestMessageBuilder(HttpMethod.Delete, $"api/providers/{ProviderId}/templates/{model.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            DataContext.Templates.AsNoTracking()
                .Where(x => x.Id == model.Id)
                .Should()
                .BeEmpty();

            DataContext.TrashItems.Where(x => x.EntityId == model.Id)
                .Should()
                .NotBeEmpty();
    }

        [Fact]
        public async Task Published_Templates_Cannot_Be_Deleted_Test()
        {
            var templateModel = new TemplateFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.CreatedByPersonId, PersonId)
                .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
                .RuleFor(x => x.IsPublished, true)
                .Generate()
                .ToDataModel();

        await DataContext.AddAsync(templateModel);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Delete,
                $"api/providers/{ProviderId}/templates/{templateModel.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.BadRequest);

        var result = await DataContext.Templates.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == templateModel.Id);

        result.Should().NotBeNull();
    }

    [Fact]
    public async Task GetTemplates_FilterFormFields_Test()
    {
        var templateModel = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate()
            .ToDataModel();

        var formFieldModels = new TemplateFormFieldFaker(templateModel.Id)
            .RuleFor(x => x.Type, FormFieldType.LongText.ToString())
            .Generate(3)
            .ToDataModel();

        var formFieldsJArray = new JArray();

        foreach (var formField in formFieldModels.SkipLast(1))
        {
            var formFieldJObject = new JObject
            {
                { "type", "formField" },
                {
                    "attrs", new JObject
                    {
                        { "id", formField.Id },
                        { "type", formField.Type },
                        { "version", formField.Version }
                    }
                }
            };

            formFieldsJArray.Add(formFieldJObject);
        }

        var content = new JObject
        {
            { "type", "doc" },
            { "content", formFieldsJArray }
        };

        templateModel.ContentJsonb = content;

        await DataContext.AddAsync(templateModel);
        await DataContext.AddRangeAsync(formFieldModels);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/templates")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<Template>>();
        result.Should().NotBeNull();
        result.Items.Should().NotBeNullOrEmpty();

        foreach (var item in result.Items)
        {
            item.Form.Should().NotBeNull();
            item.Form.Fields.Should().BeEquivalentTo(new Dictionary<Guid, TemplateFormFieldDataModel>()
            {
                { formFieldModels[0].Id, formFieldModels[0] },
                { formFieldModels[1].Id, formFieldModels[1] }
            }, options => options.ExcludingMissingMembers());
        }
    }

    [Fact]
    public async Task GetTemplate_FilterFormFields_Test()
    {
        var templateModel = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate()
            .ToDataModel();

        var formFieldModels = new TemplateFormFieldFaker(templateModel.Id)
            .RuleFor(x => x.Type, FormFieldType.LongText.ToString())
            .Generate(3)
            .ToDataModel();

        var formFieldsJArray = new JArray();

        foreach (var formField in formFieldModels.SkipLast(1))
        {
            var formFieldJObject = new JObject
            {
                { "type", "formField" },
                {
                    "attrs", new JObject
                    {
                        { "id", formField.Id },
                        { "type", formField.Type },
                        { "version", formField.Version }
                    }
                }
            };

            formFieldsJArray.Add(formFieldJObject);
        }

        var content = new JObject
        {
            { "type", "doc" },
            { "content", formFieldsJArray }
        };

        templateModel.ContentJsonb = content;

        await DataContext.AddAsync(templateModel);
        await DataContext.AddRangeAsync(formFieldModels);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates/{templateModel.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();
        result.Should().NotBeNull();

        result.Form.Should().NotBeNull();
        result.Form.Fields.Should().BeEquivalentTo(new Dictionary<Guid, TemplateFormFieldDataModel>()
        {
            { formFieldModels[0].Id, formFieldModels[0] },
            { formFieldModels[1].Id, formFieldModels[1] }
        }, options => options.ExcludingMissingMembers());
    }

    [Fact]
    public async Task DuplicateTemplate_FromPublicTemplate_Test()
    {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
        fileStorageRepositoryMock.UseRealImplementation();

        var publicTemplateId = Guid.NewGuid();

        var contentJson = new JObject
        {
            { "type", "doc" }
        };

        var content = new JArray();

        var formFieldModels = new PublicTemplateFormFieldFaker(publicTemplateId)
            .Generate(3)
            .ToDataModel();

        var aiPromptModels = new PublicTemplateAiPromptFaker(publicTemplateId)
            .Generate(3)
            .ToDataModel()
            .ToList();

        var aiPromptsJObjects = aiPromptModels
            .Select(x => new JObject
            {
                { "type", "aiSmartPrompt" },
                {
                    "attrs", new JObject
                    {
                        { "id", x.Id },
                        { "version", x.Version },
                        { "type", "AiSmartPrompt" }
                    }
                }
            })
            .ToList();

        content.Add(aiPromptsJObjects);

        var formFieldObjects = formFieldModels.Select(x => new JObject
            {
                { "type", "formField" },
                {
                    "attrs", new JObject
                    {
                        { "id", x.Id },
                        { "version", x.Version },
                        { "type", "ClientInfo" }
                    }
                }
            })
            .ToList();

        content.Add(formFieldObjects);

        var signatureId = Guid.NewGuid();

        var signatureObject = new JObject
        {
            { "type", "formField" },
            {
                "attrs", new JObject
                {
                    { "id", signatureId },
                    { "version", "1.0.0" },
                    { "type", "Signature" }
                }
            }
        };

        content.Add(signatureObject);

        contentJson["content"] = content;

        var publicTemplate = new PublicTemplateFaker()
            .RuleFor(x => x.Id, publicTemplateId)
            .RuleFor(x => x.ContentJson, contentJson)
            .RuleFor(x => x.HasAiPrompts, true)
            .Generate()
            .ToDataModel();

        var publicTemplateAttachments =
            new PublicTemplateAttachmentFaker(publicTemplateId, (SimplePerson)Data.CurrentUser)
                .Generate(3)
                .ToDataModel();

        var sampleFile = TestFileUtilities.GetTestFile(SampleFile.SampleImage);

        await Task.WhenAll(publicTemplateAttachments.Select(async file =>
        {
            var uploadableFile = new UploadableFile
            {
                Bytes = sampleFile.Bytes, FileKey = file.FileId.ToString(), ContentType = sampleFile.ContentType
            };
            await fileStorageRepositoryMock.Object.Upload(uploadableFile, FileLocationType.Files);
        }));

        DataContext.Add(publicTemplate);
        DataContext.AddRange(formFieldModels);
        DataContext.AddRange(aiPromptModels);
        DataContext.AddRange(publicTemplateAttachments);

        await DataContext.SaveChangesAsync();

        var request =
            new HttpRequestMessageBuilder(HttpMethod.Post, $"/api/providers/{ProviderId}/templates/duplicate")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(new CreateTemplateFromPublicTemplateRequest(publicTemplateId))
                .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        result.FileAttachments.Count.Should().Be(publicTemplateAttachments.Count);
        result.Form.Fields.Count.Should().Be(formFieldModels.Count);

        var resultContentJson = result.ContentJson.ToString();
        result.Id.Should().NotBe(publicTemplate.Id);

        result.Form.Fields.Values.ToList().ForEach(field =>
        {
            resultContentJson.Contains(field.Id.ToString()).Should().BeTrue();

            formFieldModels.Select(x => x.Id).Any(x => x == field.Id).Should().BeFalse();
        });

        result.AiPrompts.ForEach(aiPrompt =>
        {
            resultContentJson.Contains(aiPrompt.Id.ToString()).Should().BeTrue();

            aiPromptModels.Select(x => x.Id).Any(x => x == aiPrompt.Id).Should().BeFalse();
        });

        result.FileAttachments.ToList().ForEach(attachment =>
        {
            publicTemplateAttachments.Select(x => x.FileId).Any(x => x == attachment.Id).Should().BeFalse();
        });

        resultContentJson.Should().NotContain(publicTemplate.Id.ToString());

        result.HasAiPrompts.Should().BeTrue();
    }
    
    [Fact]
    public async Task Create_Template_Sharing_Config_Test()
    {
        var template = new TemplateFaker()
            .RuleFor(_ => _.CreatedByPersonId, PersonId)
            .RuleFor(_ => _.LastUpdatedByPersonId, PersonId)
            .RuleFor(_ => _.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        DataContext.AddRange(template);
        await DataContext.SaveChangesAsync();

        var payload = new SaveTemplateSharingConfigRequest
        {
            VerificationOption = VerificationOption.Never
        };
        
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/templates/{template.Id}/sharing-config")
            .WithPayload(payload)
            .WithBearerAuthorization(IdentityToken)
            .Create();
    
        var result = await ClientApi.SendAsync(request);
        result.StatusCode.Should().Be(HttpStatusCode.OK);
    
        var response = await result.Content.ReadAsAsync<TemplateSharingConfig>();
        
        response.Should().NotBeNull();
        
        var templateSharingConfig = await DataContext.TemplateSharingConfigs.AsNoTracking().FirstOrDefaultAsync(x => x.TemplateId == template.Id);
        templateSharingConfig.Should().BeEquivalentTo(new
        {
            TemplateId = template.Id,
            payload.VerificationOption
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task Update_Template_Sharing_Config_Test()
    {
        var template = new TemplateFaker()
            .RuleFor(_ => _.CreatedByPersonId, PersonId)
            .RuleFor(_ => _.LastUpdatedByPersonId, PersonId)
            .RuleFor(_ => _.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var templateSharingConfig = new TemplateSharingConfigFaker(template.Id)
            .RuleFor(x => x.VerificationOption, VerificationOption.Never)
            .Generate()
            .ToDataModel();

        var billing = new BillingAccountFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BillingAccountType, BillingAccountType.Essential)
            .Generate()
            .ToDataModel();
        
        DataContext.AddRange(template, templateSharingConfig, billing);
        await DataContext.SaveChangesAsync();
    
        var payload = new SaveTemplateSharingConfigRequest
        {
            VerificationOption = VerificationOption.Existing,
            ShowPoweredBy = true,
            UseWorkspaceBranding = true
        };
        
        var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/templates/{template.Id}/sharing-config/{templateSharingConfig.Id}")
            .WithPayload(payload)
            .WithBearerAuthorization(IdentityToken)
            .Create();
    
        var result = await ClientApi.SendAsync(request);
        result.StatusCode.Should().Be(HttpStatusCode.OK);
    
        var response = await result.Content.ReadAsAsync<TemplateSharingConfig>();
        
        response.Should().NotBeNull();
        response.Should().BeEquivalentTo(payload, options => options.ExcludingMissingMembers());
        var templateSharingConfigUpdated = await DataContext.TemplateSharingConfigs.AsNoTracking().FirstOrDefaultAsync(x => x.Id == templateSharingConfig.Id);
        templateSharingConfigUpdated.VerificationOption.Should().Be(payload.VerificationOption);
    }
    
    [Fact]
    public async Task Get_Template_Sharing_Config_Test()
    {
        var template = new TemplateFaker()
            .RuleFor(_ => _.CreatedByPersonId, PersonId)
            .RuleFor(_ => _.LastUpdatedByPersonId, PersonId)
            .RuleFor(_ => _.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var templateSharingConfig = new TemplateSharingConfigFaker(template.Id)
            .RuleFor(x => x.VerificationOption, VerificationOption.Never)
            .Generate()
            .ToDataModel();
    
        DataContext.AddRange(template, templateSharingConfig);
        await DataContext.SaveChangesAsync();
        
        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/templates/{template.Id}/sharing-config")
            .WithBearerAuthorization(IdentityToken)
            .Create();
    
        var result = await ClientApi.SendAsync(request);
        result.StatusCode.Should().Be(HttpStatusCode.OK);
        var response = await result.Content.ReadAsAsync<TemplateSharingConfig>();
        response.Should().NotBeNull();
        response.Should().BeEquivalentTo(templateSharingConfig, options => options.ExcludingMissingMembers());
    }
    
    [Fact]
    public async Task GetTemplates_V2_Filter_By_Search_Term_Test()
    {
        var collection = TemplateCollectionDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.TemplateCollectionId, collection.Id)
            .Generate(25)
            .ToDataModel()
            .ToList();

        templates[0].Title = "search this 0";
        templates[3].Title = "search this 1";
        templates[6].Title = "search this 2";

        var defaultIntake = new DefaultIntakeTemplateFaker(templates[0].Id)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .Generate()
            .ToDataModel();
        
        await DataContext.AddRangeAsync(templates);
        await DataContext.AddRangeAsync(collection, defaultIntake);
        await DataContext.SaveChangesAsync();

        var searchTerm = "search";

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates/v2?searchTerm={searchTerm}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<SimpleTemplate>>();

        result.Items.All(x => x.ProviderId == ProviderId).Should().BeTrue();

        result.Items.Should().OnlyContain(x => x.Title.StartsWith("search this"));
        result.TotalCount.Should().Be(3);
    }
    
    [Fact]
    public async Task GetTemplates_V2_Prioritise_Favorites_Test()
    {
        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate(25)
            .ToDataModel()
            .ToList();

        var favoriteTemplates = templates.Skip(3).Take(3).Select(x => new TemplatePersonFavoriteDataModel
        {
            PersonId = PersonId,
            TemplateId = x.Id,
        })
        .ToList();
        
        await DataContext.AddRangeAsync(templates);
        await DataContext.AddRangeAsync(favoriteTemplates);
        await DataContext.SaveChangesAsync();


        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates/v2")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<SimpleTemplate>>();

        result.Items.Take(3).Should().AllSatisfy(y => y.IsFavorite = true);
    }
    
    [Fact]
    public async Task GetTemplates_V2_Filter_By_FavoritesOnly_Test()
    {
        var collection = TemplateCollectionDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.TemplateCollectionId, collection.Id)
            .Generate(25)
            .ToDataModel()
            .ToList();

        var templatePersonFavorites = templates.Take(3)
            .Select(x => new TemplatePersonFavoriteDataModel
            {
                PersonId = PersonId,
                TemplateId = x.Id
            })
            .ToList();
        
        await DataContext.AddRangeAsync(templates);
        await DataContext.AddRangeAsync(templatePersonFavorites);
        await DataContext.AddRangeAsync(collection);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates/v2?favoritesOnly=true")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<SimpleTemplate>>();

        result.Items.All(x => x.ProviderId == ProviderId).Should().BeTrue();

        result.Items.Should().AllSatisfy(y => y.IsFavorite = true);
    }
    
    [Fact]
    public async Task Set_Template_As_Favorite_Test()
    {
        //Arrange
        var template = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate()
            .ToDataModel();

        await DataContext.AddAsync(template);
        await DataContext.SaveChangesAsync();
            

        var setAsFavoriteRequest = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/templates/me/favorites/{template.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        
        var getTemplateRequest = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/templates/{template.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        // Act
        var setAsFavoriteResponse = await ClientApi.SendAsync(setAsFavoriteRequest);
        var getTemplateResponse = await ClientApi.SendAsync(getTemplateRequest);
        
        // Assert
        setAsFavoriteResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        getTemplateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var result = await getTemplateResponse.Content.ReadAsAsync<Template>();
        
        var favoriteDataModel = await DataContext.TemplatePersonFavorites.AsNoTracking()
            .FirstOrDefaultAsync(x => x.TemplateId == template.Id && x.PersonId == PersonId);
        
        result.IsFavorite.Should().Be(true);

        favoriteDataModel.Should().NotBeNull();
    }
    
    [Fact]
    public async Task UnSet_Template_As_Favorite_Test()
    {
        //Arrange
        var template = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate()
            .ToDataModel();

        var templatePersonFavorite = new TemplatePersonFavoriteDataModel
        {
            PersonId = PersonId,
            TemplateId = template.Id
        };

        await DataContext.AddRangeAsync(template, templatePersonFavorite);
        await DataContext.SaveChangesAsync();
            

        var setAsFavoriteRequest = new HttpRequestMessageBuilder(HttpMethod.Delete, $"api/providers/{ProviderId}/templates/me/favorites/{template.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        
        var getTemplateRequest = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/templates/{template.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        // Act
        var setAsFavoriteResponse = await ClientApi.SendAsync(setAsFavoriteRequest);
        var getTemplateResponse = await ClientApi.SendAsync(getTemplateRequest);
        
        // Assert
        setAsFavoriteResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        getTemplateResponse.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var result = await getTemplateResponse.Content.ReadAsAsync<Template>();
        
        result.IsFavorite.Should().Be(false);
        
        var favoriteDataModel = await DataContext.TemplatePersonFavorites.AsNoTracking()
            .FirstOrDefaultAsync(x => x.TemplateId == template.Id && x.PersonId == PersonId);
        favoriteDataModel.Should().BeNull();
    }
    
    [Fact]
    public async Task GetTemplates_V2_Filter_By_Collection_Test()
    {
        var collection = TemplateCollectionDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        var newCollection = "Letters";
        var oldCollection = "Email";

        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate(25)
            .ToDataModel()
            .ToList();
        
        templates[0].TemplateCollectionId = collection.Id;
        
        await DataContext.AddRangeAsync(templates);
        await DataContext.AddRangeAsync(collection);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates/v2?collection[0]={collection.Title}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<SimpleTemplate>>();

        result.Items.All(x => x.ProviderId == ProviderId).Should().BeTrue();

        result.Items.Should().AllSatisfy(x => x.Collection.Should().BeOneOf(oldCollection));
    }
    
    [Fact]
    public async Task GetTemplates_V2_Filter_By_Collection_Other_Test()
    {
        var collection = TemplateCollectionDataModelBuilder.Any()
            .WithCreatedByPersonId(PersonId)
            .WithLastUpdatedByPersonId(PersonId)
            .WithProviderId(ProviderId)
            .Create();

        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate(25)
            .ToDataModel()
            .ToList();
        
        templates[0].TemplateCollectionId = collection.Id;
        templates[1].TemplateCollectionId = collection.Id;
        templates[2].TemplateCollectionId = collection.Id;
        templates[3].TemplateCollectionId = collection.Id;
        templates[4].TemplateCollectionId = collection.Id;
        
        await DataContext.AddRangeAsync(templates);
        await DataContext.AddRangeAsync(collection);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates/v2?collection[0]=Other")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<SimpleTemplate>>();

        result.Items.All(x => x.ProviderId == ProviderId).Should().BeTrue();

        result.Items.Should().AllSatisfy(x => x.Collection.Should().BeOneOf("Other", null));
    }
    
    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task GetTemplates_V2_Should_Filter_By_HasAiPrompts(bool hasAiPrompts)
    {
        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.HasAiPrompts, true)
            .Generate(3)
            .ToDataModel()
            .ToList();

        templates.AddRange(new TemplateFaker()
            .RuleFor(x => x.HasAiPrompts, false)
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate(3)
            .ToDataModel()
            .ToList());

        await DataContext.AddRangeAsync(templates);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates?hasAiPrompts={hasAiPrompts}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<Template>>();
        result.Should().NotBeNull();

        result.Items.Should().NotBeEmpty();
        result.Items.Should().OnlyContain(x => x.HasAiPrompts == hasAiPrompts);
    }
    
    [Fact]
    public async Task GetTemplates_V2_Filter_By_Tag_Test()
    {
        var tag = new TagFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.Title, "Intake")
            .Generate()
            .ToDataModel();

        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate(25)
            .ToDataModel()
            .ToList();
        
        var templateTag = new TemplateTagDataModel(templates[0].Id, tag.Id);
        
        await DataContext.AddRangeAsync(templates);
        await DataContext.AddRangeAsync(tag, templateTag);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates/v2?tags[0]={tag.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<SimpleTemplate>>();

        result.Items.All(x => x.ProviderId == ProviderId).Should().BeTrue();

        result.Items.Should().OnlyContain(x => x.Tags.Any(y => y.Id == tag.Id));
    }
    
    [Fact]
    public async Task GetTemplates_V2_Sort_By_Title_Test()
    {
        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate(25)
            .ToDataModel()
            .ToList();
        
        
        await DataContext.AddRangeAsync(templates);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates/v2?sortBy={UnifiedSearchSortBy.Title}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<SimpleTemplate>>();

        result.Items.All(x => x.ProviderId == ProviderId).Should().BeTrue();

        result.Items.Should().BeInAscendingOrder(x => x.Title);
    }
    
    [Fact]
    public async Task GetTemplates_V2_Sort_By_MostRecent_Test()
    {
        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate(25)
            .ToDataModel()
            .ToList();
        
        
        await DataContext.AddRangeAsync(templates);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates/v2?sortBy={UnifiedSearchSortBy.MostRecent}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<SimpleTemplate>>();

        result.Items.All(x => x.ProviderId == ProviderId).Should().BeTrue();

        result.Items.Should().BeInDescendingOrder(x => x.UpdatedDateTimeUtc);
    }
    
    [Fact]
    public async Task GetTemplates_V2_Should_Include_TotalCount()
    {
        var totalCount = 25;
        var templates = new TemplateFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate(totalCount)
            .ToDataModel()
            .ToList();
        
        await DataContext.AddRangeAsync(templates);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"api/providers/{ProviderId}/templates/v2?limit=10")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<SimpleTemplate>>();

        result.Items.All(x => x.ProviderId == ProviderId).Should().BeTrue();
        result.TotalCount.Should().Be(totalCount);
    }

    [Fact]
    public async Task UpdatedTemplate_WithAiSmartPromptId_ShouldSet_HasAiPrompts_True_Test()
    {
        // Arrange
        var templateId = Guid.NewGuid();

        var formFieldModels = new TemplateFormFieldFaker(templateId)
            .Generate(3)
            .ToDataModel();

        var aiPromptModels = new TemplateAiPromptFaker(templateId)
            .Generate(3)
            .ToDataModel()
            .ToList();

        var content = new JArray();

        var formFieldObjects = formFieldModels.Select((x, i) => new JObject
            {
                { "type", "formField" },
                {
                    "attrs", new JObject
                    {
                        { "id", x.Id },
                        { "version", x.Version },
                        { "type", "ClientInfo" },
                        { "aiSmartPromptId", aiPromptModels[i].Id }
                    }
                }
            })
            .ToList();

        content.Add(formFieldObjects);

        var contentJson = new JObject
        {
            { "type", "doc" },
            { "content", content }
        };

        var template = new TemplateFaker()
            .RuleFor(x => x.Id, templateId)
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.HasAiPrompts, false)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(template);
        await DataContext.SaveChangesAsync();

        var updatedModel = new SaveTemplateRequestFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.ContentJson, contentJson)
            .RuleFor(x => x.Title, "Enrollment")
            .Generate();

        var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/templates/{template.Id}")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(updatedModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        result.HasAiPrompts.Should().BeTrue();
    }

    [Fact]
    public async Task UpdatedTemplate_WithAiSmartPrompts_ShouldSet_HasAiPrompts_True_Test()
    {
        // Arrange
        var templateId = Guid.NewGuid();

        var aiPromptModels = new TemplateAiPromptFaker(templateId)
            .Generate(3)
            .ToDataModel()
            .ToList();

        var content = new JArray();

        var aiPromptsJObjects = aiPromptModels
            .Where(x => x.DeletedAtUtc == null)
            .Select(x => new JObject
            {
                { "type", "aiSmartPrompt" },
                {
                    "attrs", new JObject
                    {
                        { "id", x.Id },
                        { "version", x.Version },
                        { "type", "AiSmartPrompt" }
                    }
                }
            })
            .ToList();

        content.Add(aiPromptsJObjects);

        var contentJson = new JObject
        {
            { "type", "doc" },
            { "content", content }
        };

        var template = new TemplateFaker()
            .RuleFor(x => x.Id, templateId)
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.HasAiPrompts, false)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(template);
        await DataContext.SaveChangesAsync();

        var updatedModel = new SaveTemplateRequestFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.ContentJson, contentJson)
            .RuleFor(x => x.Title, "Enrollment")
            .Generate();

        var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/templates/{template.Id}")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(updatedModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        result.HasAiPrompts.Should().BeTrue();
    }

    [Fact]
    public async Task UpdatedTemplate_WithoutAiSmartPrompts_ShouldSet_HasAiPrompts_False_Test()
    {
        // Arrange
        var templateId = Guid.NewGuid();

        var content = new JArray();

        // Add some formField objects without aiSmartPromptId
        var formFieldModels = new TemplateFormFieldFaker(templateId)
            .Generate(3)
            .ToDataModel();

        var formFieldObjects = formFieldModels.Select(x => new JObject
            {
                { "type", "formField" },
                {
                    "attrs", new JObject
                    {
                        { "id", x.Id },
                        { "version", x.Version },
                        { "type", "ClientInfo" }
                    }
                }
            })
            .ToList();

        content.Add(formFieldObjects);

        var contentJson = new JObject
        {
            { "type", "doc" },
            { "content", content }
        };

        var template = new TemplateFaker()
            .RuleFor(x => x.Id, templateId)
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.HasAiPrompts, true)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(template);
        await DataContext.SaveChangesAsync();

        var updatedModel = new SaveTemplateRequestFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.ContentJson, contentJson)
            .RuleFor(x => x.Title, "Enrollment")
            .Generate();

        var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/templates/{template.Id}")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(updatedModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Template>();

        result.HasAiPrompts.Should().BeFalse();
    }
}