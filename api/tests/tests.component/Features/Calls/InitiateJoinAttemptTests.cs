﻿using carepatron.core.Application.Calls.Events;
using carepatron.core.IntegrationEvents.Models;
using Moq;
using tests.common.Data.Mappers;
using tests.common.Mocks;
using tests.component.Builders;

namespace tests.component.Features.Calls
{
    [Trait(nameof(CodeOwner), CodeOwner.Communications)]
    public class InitiateJoinAttemptTests : BaseTestClass
    {
        public InitiateJoinAttemptTests(ComponentTestFixture testFixture) : base(testFixture)
        {
        }

        [Theory]
        [InlineData(true, true)]  // Host active, link reused -> Should ask to join
        [InlineData(true, false)] // Host active, link not reused -> Should ask to join (max attendees)
        [InlineData(false, true)] // Host inactive, link reused -> Should ask to join
        [InlineData(false, false)] // Host inactive, link not reused -> Should notify host of waiting
        public async Task PublishEvent_Test(bool isHostActive, bool isLinkReused)
        {
            var host = new PersonFaker()
                .Generate()
                .ToDataModel();

            var call = new CallFaker()
                .RuleFor(x => x.CreatedByPersonId, host.Id)
                .RuleFor(x => x.Attendees, new List<Guid> { host.Id, PersonId })
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.IsActive, true)
                .Generate()
                .ToDataModel();

            var hostAttendee = new CallAttendeeFaker(call.Id, host.Id)
                .RuleFor(x => x.IsActive, isHostActive)
                .Generate()
                .ToDataModel();

            var currentUserAttendee = new CallAttendeeFaker(call.Id, PersonId)
                .RuleFor(x => x.IsActive, isLinkReused)
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(call, host, hostAttendee, currentUserAttendee);
            await DataContext.SaveChangesAsync();

            var eventPublisherMock = Fixture.ResolveService<EventPublisherMock>();
            eventPublisherMock.Reset();

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/calls/{call.Id}/initiate")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            // Should notify host of waiting attendee when:
            // 1. Host is not active AND
            // 2. Link is not being reused
            bool shouldNotifyHostOfWaitingAttendee = !isHostActive && !isLinkReused;

            // Should ask to join when:
            // 1. The link is being reused, or
            // 2. The number of active attendees has reached the expected number
            var activeAttendees = new[] { hostAttendee, currentUserAttendee }.Where(a => a.IsActive).ToArray();
            var expectedAttendees = new[] { hostAttendee, currentUserAttendee };
            bool shouldAskToJoin = isLinkReused || activeAttendees.Length >= expectedAttendees.Length;

            eventPublisherMock.Verify(
                publisher => publisher.Publish(It.IsAny<OutgoingEvent<CallAttendeeJoinAttemptedEvent>>()),
                shouldNotifyHostOfWaitingAttendee ? Times.Once : Times.Never);
            
            eventPublisherMock.Verify(
                publisher => publisher.Publish(It.IsAny<OutgoingEvent<CallAttendeeAskedToJoinEvent>>()),
                shouldAskToJoin ? Times.Once : Times.Never);
        }

        [Theory]
        [InlineData(true)]  // Host active -> Should ask to join
        [InlineData(false)] // Host inactive -> Should notify host of waiting
        public async Task PublishEvent_Anonymous_Test(bool isHostActive)
        {
            var host = new PersonFaker()
                .Generate()
                .ToDataModel();

            var call = new CallFaker()
                .RuleFor(x => x.CreatedByPersonId, host.Id)
                .RuleFor(x => x.Attendees, new List<Guid> { host.Id })
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.IsActive, true)
                .Generate()
                .ToDataModel();

            var hostAttendee = new CallAttendeeFaker(call.Id, host.Id)
                .RuleFor(x => x.IsActive, isHostActive)
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(call, host, hostAttendee);
            await DataContext.SaveChangesAsync();

            var eventPublisherMock = Fixture.ResolveService<EventPublisherMock>();
            eventPublisherMock.Reset();

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/calls/{call.Id}/initiate")
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            // For anonymous users, we only care about host's active status
            eventPublisherMock.Verify(
                publisher => publisher.Publish(It.IsAny<OutgoingEvent<CallAttendeeJoinAttemptedEvent>>()),
                !isHostActive ? Times.Once : Times.Never);
            
            eventPublisherMock.Verify(
                publisher => publisher.Publish(It.IsAny<OutgoingEvent<CallAttendeeAskedToJoinEvent>>()),
                !isHostActive ? Times.Never : Times.Once);
        }

        [Fact]
        public async Task PublishEvent_HostJoining_Test()
        {
            var call = new CallFaker()
                .RuleFor(x => x.CreatedByPersonId, PersonId)
                .RuleFor(x => x.Attendees, new List<Guid> { PersonId })
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.IsActive, true)
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(call);
            await DataContext.SaveChangesAsync();

            var eventPublisherMock = Fixture.ResolveService<EventPublisherMock>();
            eventPublisherMock.Reset();

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/calls/{call.Id}/initiate")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            // When host is joining, no events should be published
            eventPublisherMock.Verify(
                publisher => publisher.Publish(It.IsAny<OutgoingEvent<CallAttendeeJoinAttemptedEvent>>()),
                Times.Never);
            
            eventPublisherMock.Verify(
                publisher => publisher.Publish(It.IsAny<OutgoingEvent<CallAttendeeAskedToJoinEvent>>()),
                Times.Never);
        }
    }
}
