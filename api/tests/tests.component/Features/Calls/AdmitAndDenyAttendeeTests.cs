using carepatron.core.Application.Calls.Events;
using carepatron.core.IntegrationEvents.Models;
using Moq;
using tests.common.Data.Mappers;
using tests.common.Mocks;
using tests.component.Builders;

namespace tests.component.Features.Calls
{
    [Trait(nameof(CodeOwner), CodeOwner.Communications)]
    public class AdmitAndDenyAttendeeTests : BaseTestClass
    {
        public AdmitAndDenyAttendeeTests(ComponentTestFixture testFixture) : base(testFixture)
        {
        }

        [Fact]
        public async Task AdmitAttendee_PublishesAdmittedEvent()
        {
            // Use PersonId and IdentityToken as host
            var attendee = new PersonFaker().Generate().ToDataModel();

            var call = new CallFaker()
                .RuleFor(x => x.CreatedBy<PERSON>ersonId, PersonId)
                .RuleFor(x => x.Attendees, new List<Guid> { PersonId, attendee.Id })
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.IsActive, true)
                .Generate()
                .ToDataModel();

            var hostAttendee = new CallAttendeeFaker(call.Id, PersonId)
                .RuleFor(x => x.IsActive, true)
                .Generate()
                .ToDataModel();

            var attendeeModel = new CallAttendeeFaker(call.Id, attendee.Id)
                .RuleFor(x => x.IsActive, false)
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(call, attendee, hostAttendee, attendeeModel);
            await DataContext.SaveChangesAsync();

            var eventPublisherMock = Fixture.ResolveService<EventPublisherMock>();
            eventPublisherMock.Reset();

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/calls/{call.Id}/attendees/{attendeeModel.Id}/admit")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            eventPublisherMock.Verify(
                publisher => publisher.Publish(It.IsAny<OutgoingEvent<CallAttendeeAskToJoinAdmittedEvent>>()),
                Times.Once);
        }

        [Fact]
        public async Task DenyAttendee_PublishesDeniedEvent()
        {
            // Use PersonId and IdentityToken as host
            var attendee = new PersonFaker().Generate().ToDataModel();

            var call = new CallFaker()
                .RuleFor(x => x.CreatedByPersonId, PersonId)
                .RuleFor(x => x.Attendees, new List<Guid> { PersonId, attendee.Id })
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.IsActive, true)
                .Generate()
                .ToDataModel();

            var hostAttendee = new CallAttendeeFaker(call.Id, PersonId)
                .RuleFor(x => x.IsActive, true)
                .Generate()
                .ToDataModel();

            var attendeeModel = new CallAttendeeFaker(call.Id, attendee.Id)
                .RuleFor(x => x.IsActive, false)
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(call, attendee, hostAttendee, attendeeModel);
            await DataContext.SaveChangesAsync();

            var eventPublisherMock = Fixture.ResolveService<EventPublisherMock>();
            eventPublisherMock.Reset();

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/calls/{call.Id}/attendees/{attendeeModel.Id}/deny")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            eventPublisherMock.Verify(
                publisher => publisher.Publish(It.IsAny<OutgoingEvent<CallAttendeeAskToJoinDeniedEvent>>()),
                Times.Once);
        }
    }
}
