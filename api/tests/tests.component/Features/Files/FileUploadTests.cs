using Amazon.S3;
using Amazon.S3.Model;
using Bogus;
using carepatron.api.Contracts.Requests.Files;
using carepatron.core.Application.Files.Models;
using carepatron.core.Models.Media;
using carepatron.infra.s3.Factory;
using Microsoft.Extensions.DependencyInjection;
using System.IO;
using System.Net;
using System.Net.Http.Headers;
using tests.common.Mocks;
using tests.component.Builders;
using Xunit.Abstractions;

namespace tests.component.Features.Files
{
    public class FileUploadTests: BaseTestClass
    {
        private readonly ITestOutputHelper output;
        private const string resourceFilePath = "./Resources/";
        private const string sampleImage = "SampleImage.jpg";
        private readonly Guid FileImportId = new Guid("9f8b5a8e-3f73-45a7-a4a7-f7a3f0a561bc");

        public FileUploadTests(ComponentTestFixture testFixture, ITestOutputHelper output) : base(testFixture)
        {
            this.output = output;
        }
        
        [Fact]
        public async Task InitializeUpload_Test()
        {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock.UseRealImplementation();

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/files/upload/initialize")
                    .WithBearerAuthorization(IdentityToken)
                    .Create();
            var result = await ClientApi.SendAsync(request);
            result.StatusCode.Should().Be(HttpStatusCode.OK);
            var response = await result.Content.ReadAsAsync<InitializeMultipartUploadResult>();
            response.UploadId.Should().NotBeNullOrEmpty();
            response.FileId.Should().NotBeEmpty();
        }

        [Fact]
        public async Task UploadPart_Test()
        {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock.UseRealImplementation();

            var fullFilePath = Path.Combine(resourceFilePath, sampleImage);
            Stream fileStream = System.IO.File.OpenRead(fullFilePath);
            using MemoryStream ms = new();
            fileStream.CopyTo(ms);
            var fileStreamInBytes = ms.ToArray();

            var initialUploadResult = await CreateInitialFileUploadResult();

            fileStorageRepositoryMock.UploadPart(initialUploadResult.FileId, initialUploadResult.UploadId, 1, true, fileStream, FileLocationType.ClientImport);

            var form = new MultipartFormDataContent();
            var fileContent = new ByteArrayContent(fileStreamInBytes);
            fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse("multipart/form-data");
            form.Add(fileContent, "file", "sample.zip");

            var partNumber = 1;
            var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/files/upload/parts?fileId={initialUploadResult.FileId}&uploadId={initialUploadResult.UploadId}&partNumber={partNumber}&isLastPart=true")
                    .WithBearerAuthorization(IdentityToken)
                    .WithContent(form)
                    .Create();

            var result = await ClientApi.SendAsync(request);
            result.StatusCode.Should().Be(HttpStatusCode.OK);
            var response = await result.Content.ReadAsAsync<PartUploadResult>();
            response.Should().NotBeNull();
            response.PartNumber.Should().Be(partNumber);
            response.PartETag.Should().NotBeNullOrEmpty();
        }
        
        [Fact]
        public async Task CompleteUpload_Test()
        {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock.UseRealImplementation();

            var initialUploadResult = await CreateInitialFileUploadResult();

            var fullFilePath = Path.Combine(resourceFilePath, sampleImage);
            Stream fileStream = System.IO.File.OpenRead(fullFilePath);
            using MemoryStream fileStreamMs = new();
            fileStream.CopyTo(fileStreamMs);
            var fileStreamInBytes = fileStreamMs.ToArray();

            await UploadPart(sampleImage, initialUploadResult.FileId, fileStreamInBytes, 1, initialUploadResult.UploadId, 1);

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/files/upload/Complete")
                    .WithBearerAuthorization(IdentityToken)
                    .WithPayload(new CompleteUploadRequest { FileId = initialUploadResult.FileId, UploadId = initialUploadResult.UploadId })
                    .Create();
            var result = await ClientApi.SendAsync(request);
            result.StatusCode.Should().Be(HttpStatusCode.OK);

            var amazonS3Factory = ResolveService<IAmazonS3ClientFactory>();
            var clientBucket = amazonS3Factory.GetClientBucket(FileLocationType.ClientImport);

            var s3Object = await clientBucket.Client.GetObjectAsync(new GetObjectRequest
            {
                BucketName = clientBucket.BucketName,
                Key = initialUploadResult.FileId.ToString()
            });

            s3Object.Should().NotBeNull();
            s3Object.HttpStatusCode.Should().Be(HttpStatusCode.OK);

            var s3ObjectStream = s3Object.ResponseStream;
            using MemoryStream s3ObjectStreamMs = new();
            s3ObjectStream.CopyTo(s3ObjectStreamMs);
            var s3ObjectStreamInBytes = s3ObjectStreamMs.ToArray();
            fileStreamInBytes.Should().BeEquivalentTo(s3ObjectStreamInBytes);
        }

        [Fact]
        public async Task CompleteUpload_WithPresignedUrls_Test()
        {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock.UseRealImplementation();

            var initialUploadResult = await CreateInitialFileUploadResult(new UploadInitializeRequest
            {
                FileLocationType = FileLocationType.ClientImport,
                ContentType = "image/jpeg"
            });

            var fullFilePath = Path.Combine(resourceFilePath, sampleImage);
            Stream fileStream = System.IO.File.OpenRead(fullFilePath);
            using MemoryStream fileStreamMs = new();
            fileStream.CopyTo(fileStreamMs);
            var fileStreamInBytes = fileStreamMs.ToArray();

            await UploadPart(sampleImage, initialUploadResult.FileId, fileStreamInBytes, 1, initialUploadResult.UploadId, 1);

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/files/upload/Complete")
                    .WithBearerAuthorization(IdentityToken)
                    .WithPayload(new CompleteUploadRequest { FileId = initialUploadResult.FileId, UploadId = initialUploadResult.UploadId })
                    .Create();
            var result = await ClientApi.SendAsync(request);
            result.StatusCode.Should().Be(HttpStatusCode.OK);

            var completeUploadResult = await result.Content.ReadAsAsync<CompleteUploadResult>();

            completeUploadResult.Url.Should().Contain("inline");
            completeUploadResult.DownloadUrl.Should().Contain("attachment");

            var amazonS3Factory = ResolveService<IAmazonS3ClientFactory>();
            var clientBucket = amazonS3Factory.GetClientBucket(FileLocationType.ClientImport);

            var s3Object = await clientBucket.Client.GetObjectAsync(new GetObjectRequest
            {
                BucketName = clientBucket.BucketName,
                Key = initialUploadResult.FileId.ToString()
            });

            s3Object.Should().NotBeNull();
            s3Object.HttpStatusCode.Should().Be(HttpStatusCode.OK);

            var s3ObjectStream = s3Object.ResponseStream;
            using MemoryStream s3ObjectStreamMs = new();
            s3ObjectStream.CopyTo(s3ObjectStreamMs);
            var s3ObjectStreamInBytes = s3ObjectStreamMs.ToArray();
            fileStreamInBytes.Should().BeEquivalentTo(s3ObjectStreamInBytes);
        }

        [Theory]
        [InlineData(1048576 * 10, 5*1048576)] //10MB 5MB
        [InlineData(1048576 * 100, 10*1048576, Skip = "Performance local testing")] //100MB 10MB
        public async Task Upload_Multipart_APIs_Parallel_Test(int fileSize, int chunkSize)
        {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock.UseRealImplementation();
            //Step 1: Initialize Multipart Upload
            var initialUploadResult = await CreateInitialFileUploadResult(); // initializeResult.Content.ReadAsAsync<InitializeMultipartUploadResult>();

            var fileId = initialUploadResult.FileId;
            //Step 2: Upload Parts
            var file = new MemoryStream(new Faker().Random.Bytes(fileSize)); //100MB

            int totalChunks = (int)(file.Length / chunkSize);
            if (file.Length % chunkSize != 0)
            {
                totalChunks++;
            }

            string uploadId = initialUploadResult.UploadId;

            List<(int,byte[])> buffers = new List<(int,byte[])>();
            for (int i = 0; i < totalChunks; i++)
            {
                long position = i * (long)chunkSize;
                int toRead = (int)Math.Min(file.Length - position, chunkSize);
                byte[] buffer = new byte[toRead];
                await file.ReadAsync(buffer, 0, buffer.Length);
                buffers.Add((i + 1, buffer));
            }

            var uploadTasks = buffers.AsParallel().Select((Func<(int, byte[]), Task>)(async buffer =>
            {
                await UploadPart("test.zip", initialUploadResult.FileId, buffer.Item2, totalChunks, initialUploadResult.UploadId, buffer.Item1);
            }));
            
            await Task.WhenAll(uploadTasks);

            //Step 3: Complete Multipart Upload
            var compleRequest = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/files/upload/Complete")
                    .WithBearerAuthorization(IdentityToken)
                    .WithPayload(new CompleteUploadRequest { FileId = fileId, UploadId = uploadId })
                    .Create();


            var completeResult = await ClientApi.SendAsync(compleRequest);
            completeResult.StatusCode.Should().Be(HttpStatusCode.OK);

            var amazonS3Factory = ResolveService<IAmazonS3ClientFactory>();
            var clientBucket = amazonS3Factory.GetClientBucket(FileLocationType.ClientImport);

            var s3Object = await clientBucket.Client.GetObjectAsync(new GetObjectRequest
            {
                BucketName = clientBucket.BucketName,
                Key = initialUploadResult.FileId.ToString()
            });

            s3Object.Should().NotBeNull();
            s3Object.HttpStatusCode.Should().Be(HttpStatusCode.OK);

            var s3ObjectStream = s3Object.ResponseStream;
            using MemoryStream s3ObjectStreamMs = new();
            s3ObjectStream.CopyTo(s3ObjectStreamMs);
            var s3ObjectStreamInBytes = s3ObjectStreamMs.ToArray();

            file.Position = 0;
            var msFile = new MemoryStream();
            file.CopyTo(msFile);
            msFile.Position = 0;
            var msFileInBytes = msFile.ToArray();

            msFileInBytes.Should().BeEquivalentTo(s3ObjectStreamInBytes);
        }


        [Fact]
        public async Task Upload_Multipart_Abort_Test()
        {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock.UseRealImplementation();
            //Step 1: Initialize Multipart Upload
            var initialUploadResult = await CreateInitialFileUploadResult(); // initializeResult.Content.ReadAsAsync<InitializeMultipartUploadResult>();

            var fileId = initialUploadResult.FileId;
            //Step 2: Upload Parts
            var file = new MemoryStream(new Faker().Random.Bytes(1048576 * 20)); //50MB

            int chunckSize = 1048576 * 10; //10MB. 5MiB is the minimum chunk size: https://docs.aws.amazon.com/AmazonS3/latest/userguide/qfacts.html 
            int totalChunks = (int)(file.Length / chunckSize);
            if (file.Length % chunckSize != 0)
            {
                totalChunks++;
            }

            string uploadId = initialUploadResult.UploadId;

            List<(int, byte[])> buffers = new List<(int, byte[])>();
            for (int i = 0; i < totalChunks; i++)
            {
                long position = i * (long)chunckSize;
                int toRead = (int)Math.Min(file.Length - position, chunckSize);
                byte[] buffer = new byte[toRead];
                await file.ReadAsync(buffer, 0, buffer.Length);
                buffers.Add((i + 1, buffer));
            }

            var uploadTasks = buffers.AsParallel().Select((Func<(int, byte[]), Task>)(async buffer =>
            {
                await UploadPart("test.zip", initialUploadResult.FileId, buffer.Item2, totalChunks, initialUploadResult.UploadId, buffer.Item1);
            }));

            await Task.WhenAll(uploadTasks);

            //Step 3: Complete Multipart Upload
            var abortRequest = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/files/upload/abort")
                    .WithBearerAuthorization(IdentityToken)
                    .WithPayload(new AbortUploadRequest { FileId = fileId, UploadId = uploadId })
                    .Create();


            var abortResponse = await ClientApi.SendAsync(abortRequest);
            abortResponse.StatusCode.Should().Be(HttpStatusCode.OK);

            var amazonS3Factory = ResolveService<IAmazonS3ClientFactory>();
            var clientBucket = amazonS3Factory.GetClientBucket(FileLocationType.ClientImport);

            var exception = await Assert.ThrowsAsync<AmazonS3Exception>(()=> clientBucket.Client.ListPartsAsync(clientBucket.BucketName, fileId.ToString(), uploadId));
            exception.StatusCode.Should().Be(HttpStatusCode.NotFound);
            exception.Message.Should().Be("The specified upload does not exist. The upload ID may be invalid, or the upload may have been aborted or completed.");
        }
        
        [Fact]
        public async Task GeneratePresignedUrl_Test()
        {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock.UseRealImplementation();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/files/{FileImportId}/url?fileLocationType={nameof(FileLocationType.ClientImport)}")
                .WithBearerAuthorization(IdentityToken)
                .Create();
            
            var result = await ClientApi.SendAsync(request);
            
            result.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var presignedUrl = await result.Content.ReadAsStringAsync();
            
            presignedUrl.Should().NotBeNullOrEmpty();
        }

        private async Task UploadPart(string fileName, Guid fileId, byte[] buffer, int totalChunks, string uploadId, int partNumber)
        {
            var form = new MultipartFormDataContent();
            var fileContent = new ByteArrayContent(buffer);
            fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse("multipart/form-data");

            var isLastPart = partNumber == totalChunks;
            form.Add(fileContent, "file", fileName);

            var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/files/upload/parts?fileId={fileId}&uploadId={uploadId}&partNumber={partNumber}&isLastPart={isLastPart}")
                    .WithBearerAuthorization(IdentityToken)
                    .WithContent(form)
                    .Create();

            var result = await ClientApi.SendAsync(request);
            result.StatusCode.Should().Be(HttpStatusCode.OK);

            var response = await result.Content.ReadAsAsync<carepatron.core.Models.Media.PartUploadResult>();
            response.Should().NotBeNull();
            response.PartNumber.Should().Be(partNumber);
            response.PartETag.Should().NotBeNullOrEmpty();
        }

        private async Task<InitializeMultipartUploadResult> CreateInitialFileUploadResult(UploadInitializeRequest request = null)
        {
            var initialUploadRequest = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/files/upload/initialize")
                 .WithBearerAuthorization(IdentityToken)
                 .WithPayload(request)
                 .Create();
            var initialUploadResponse = await ClientApi.SendAsync(initialUploadRequest);
            initialUploadResponse.StatusCode.Should().Be(HttpStatusCode.OK);
            var initialUploadResult = await initialUploadResponse.Content.ReadAsAsync<InitializeMultipartUploadResult>();
            initialUploadResult.UploadId.Should().NotBeNullOrEmpty();
            initialUploadResult.FileId.Should().NotBeEmpty();

            return initialUploadResult;
        }
    }
}
