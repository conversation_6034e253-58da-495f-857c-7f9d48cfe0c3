﻿using carepatron.api.Contracts.Requests.Items;
using carepatron.core.Application.Workspace.ServiceItems.Models;
using carepatron.core.Models.Pagination;
using Microsoft.EntityFrameworkCore;
using tests.common.Data.Mappers;
using tests.component.Builders;

namespace tests.component.Features.ProviderItemGroups
{
    [Trait(nameof(CodeOwner), CodeOwner.TasksAndScheduling)]
    public class ProviderItemGroupsTests : BaseTestClass
    {
        public ProviderItemGroupsTests(ComponentTestFixture testFixture) : base(testFixture)
        {
        }

        [Fact]
        public async Task CreateItemGroup()
        {
            var location = new ProviderLocationFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate()
                .ToDataModel();

            var providerItem = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.BookableOnline, true)
                .RuleFor(x => x.AllowNewClients, true)
                .RuleFor(x => x.AssignedStaffIds, new Guid[] { PersonId })
                .RuleFor(x => x.LocationIds, new[] { location.Id })
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(location, providerItem);
            await DataContext.SaveChangesAsync();

            var requestModel = new SaveProviderItemGroupRequest
            {
                Name = $"ServiceGroup - {ProviderId} {providerItem.Id}",
                ItemIds = new[] { providerItem.Id }
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/itemGroups")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(requestModel)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<ProviderItemGroup>();
            result.Should().NotBeNull();

            var itemGroup = DataContext.ProviderItemGroups.FirstOrDefault(x => x.Id == result.Id);
            itemGroup.Should().NotBeNull();

            var itemGroupItems = DataContext.ProviderItemGroupItems.Where(x => x.ProviderItemGroupId == result.Id)?.ToList();
            itemGroupItems.Should().NotBeNull();
            itemGroupItems.Count.Should().Be(1);
            itemGroupItems.FirstOrDefault().ProviderItemId.Should().Be(providerItem.Id);
        }

        [Fact]
        public async Task CreateItemGroup_Should_Save_Order()
        {
            var location = new ProviderLocationFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate()
                .ToDataModel();

            var providerItem = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.BookableOnline, true)
                .RuleFor(x => x.AllowNewClients, true)
                .RuleFor(x => x.AssignedStaffIds, new Guid[] { PersonId })
                .RuleFor(x => x.LocationIds, new[] { location.Id })
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(location, providerItem);
            await DataContext.SaveChangesAsync();

            var requestModel = new SaveProviderItemGroupRequest
            {
                Name = $"ServiceGroup - {ProviderId} {providerItem.Id}",
                ItemIds = new[] { providerItem.Id },
                Order = 4
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/itemGroups")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(requestModel)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<ProviderItemGroup>();
            result.Should().NotBeNull();
            result.Order.Should().Be(4);

            var itemGroup = await DataContext.ProviderItemGroups.FirstOrDefaultAsync(x => x.Id == result.Id);
            itemGroup.Should().NotBeNull();
            itemGroup.Order.Should().Be(4);
        }

        [Fact]
        public async Task CreateItemGroup_NullItemIds()
        {
            var location = new ProviderLocationFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(location);
            await DataContext.SaveChangesAsync();

            var requestModel = new SaveProviderItemGroupRequest
            {
                Name = $"ServiceGroup - {ProviderId}",
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/itemGroups")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(requestModel)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<ProviderItemGroup>();
            result.Should().NotBeNull();

            var itemGroup = DataContext.ProviderItemGroups.FirstOrDefault(x => x.Id == result.Id);
            itemGroup.Should().NotBeNull();

            var itemGroupItems = DataContext.ProviderItemGroupItems.Where(x => x.ProviderItemGroupId == result.Id)?.ToList();
            itemGroupItems.Should().BeEmpty();
        }

        [Fact]
        public async Task UpdateItemGroup()
        {
            var location = new ProviderLocationFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate()
                .ToDataModel();

            var providerItem = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.BookableOnline, true)
                .RuleFor(x => x.AllowNewClients, true)
                .RuleFor(x => x.AssignedStaffIds, new Guid[] { PersonId })
                .RuleFor(x => x.LocationIds, new[] { location.Id })
                .Generate()
                .ToDataModel();

            var providerItem2 = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.BookableOnline, true)
                .RuleFor(x => x.AllowNewClients, true)
                .RuleFor(x => x.AssignedStaffIds, new Guid[] { PersonId })
                .RuleFor(x => x.LocationIds, new[] { location.Id })
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(location, providerItem, providerItem2);
            await DataContext.SaveChangesAsync();

            var requestModel = new SaveProviderItemGroupRequest
            {
                Name = $"ServiceGroup - {ProviderId} {providerItem.Id}",
                ItemIds = new[] { providerItem.Id }
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/itemGroups")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(requestModel)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<ProviderItemGroup>();
            result.Should().NotBeNull();

            var itemGroup = DataContext.ProviderItemGroups.FirstOrDefault(x => x.Id == result.Id);
            itemGroup.Should().NotBeNull();

            var itemGroupItems = DataContext.ProviderItemGroupItems.Where(x => x.ProviderItemGroupId == result.Id)?.ToList();
            itemGroupItems.Should().NotBeNull();
            itemGroupItems.Count.Should().Be(1);

            var updateRequestModel = new SaveProviderItemGroupRequest
            {
                Name = $"ServiceGroup - {ProviderId} {providerItem.Id} {providerItem2.Id}",
                ItemIds = new[] { providerItem.Id, providerItem2.Id }
            };

            var updateRequest = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/itemGroups/{result.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(updateRequestModel)
                .Create();

            var updateResponse = await ClientApi.SendAsync(updateRequest);
            updateResponse.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var updateResult = await updateResponse.Content.ReadAsAsync<ProviderItemGroup>();
            updateResult.Should().NotBeNull();

            itemGroup = DataContext.ProviderItemGroups.FirstOrDefault(x => x.Id == result.Id);
            itemGroup.Should().NotBeNull();

            itemGroupItems = DataContext.ProviderItemGroupItems.Where(x => x.ProviderItemGroupId == result.Id)?.ToList();
            itemGroupItems.Should().NotBeNull();
            itemGroupItems.Count.Should().Be(2);
            itemGroupItems.Select(x => x.ProviderItemId).Should().BeEquivalentTo(updateRequestModel.ItemIds.ToArray());
        }

        [Fact]
        public async Task UpdateItemGroup_NullItemIds()
        {
            var location = new ProviderLocationFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate()
                .ToDataModel();

            var providerItem = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.BookableOnline, true)
                .RuleFor(x => x.AllowNewClients, true)
                .RuleFor(x => x.AssignedStaffIds, new Guid[] { PersonId })
                .RuleFor(x => x.LocationIds, new[] { location.Id })
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(location, providerItem);
            await DataContext.SaveChangesAsync();

            var requestModel = new SaveProviderItemGroupRequest
            {
                Name = $"ServiceGroup - {ProviderId}",
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/itemGroups")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(requestModel)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<ProviderItemGroup>();
            result.Should().NotBeNull();

            var itemGroup = DataContext.ProviderItemGroups.FirstOrDefault(x => x.Id == result.Id);
            itemGroup.Should().NotBeNull();

            var itemGroupItems = DataContext.ProviderItemGroupItems.Where(x => x.ProviderItemGroupId == result.Id)?.ToList();
            itemGroupItems.Should().BeEmpty();

            var updateRequestModel = new SaveProviderItemGroupRequest
            {
                Name = $"ServiceGroup - {ProviderId} {providerItem.Id}",
                ItemIds = new[] { providerItem.Id }
            };

            var updateRequest = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/itemGroups/{result.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(updateRequestModel)
                .Create();

            var updateResponse = await ClientApi.SendAsync(updateRequest);
            updateResponse.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var updateResult = await updateResponse.Content.ReadAsAsync<ProviderItemGroup>();
            updateResult.Should().NotBeNull();

            itemGroup = DataContext.ProviderItemGroups.FirstOrDefault(x => x.Id == result.Id);
            itemGroup.Should().NotBeNull();

            itemGroupItems = DataContext.ProviderItemGroupItems.Where(x => x.ProviderItemGroupId == result.Id)?.ToList();
            itemGroupItems.Should().NotBeNull();
            itemGroupItems.Count.Should().Be(1);
            itemGroupItems.Select(x => x.ProviderItemId).Should().Equal(updateRequestModel.ItemIds.ToArray());
        }

        [Fact]
        public async Task GetItemGroups()
        {
            var location = new ProviderLocationFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate()
                .ToDataModel();

            var providerItem = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.BookableOnline, true)
                .RuleFor(x => x.AllowNewClients, true)
                .RuleFor(x => x.AssignedStaffIds, new Guid[] { PersonId })
                .RuleFor(x => x.LocationIds, new[] { location.Id })
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(location, providerItem);
            await DataContext.SaveChangesAsync();

            var requestModel = new SaveProviderItemGroupRequest
            {
                Name = $"ServiceGroup - {ProviderId} {providerItem.Id}",
                ItemIds = new[] { providerItem.Id }
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/itemGroups")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(requestModel)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var getRequest = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/itemGroups")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var getReponse = await ClientApi.SendAsync(getRequest);
            getReponse.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await getReponse.Content.ReadAsAsync<PaginatedResult<ProviderItemGroup>>();
            result.Should().NotBeNull();
            result.Items.Should().NotBeNull();
            result.Items.Count.Should().Be(1);
            result.Items.FirstOrDefault().ItemIds.Should().Equal(requestModel.ItemIds);
            result.Items.FirstOrDefault().Name.Should().Be(requestModel.Name);
        }

        [Fact]
        public async Task GetItemGroups_Search_By_Group_Name()
        {
            var location = new ProviderLocationFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate()
                .ToDataModel();

            var providerItem = new ProviderItemFaker(ProviderId)
                .RuleFor(x => x.Title, f => f.Random.Guid().ToString())
                .RuleFor(x => x.DisplayName, f => f.Random.Guid().ToString())
                .RuleFor(x => x.Description, f => f.Random.Guid().ToString())
                .RuleFor(x => x.Code, f => f.Random.Guid().ToString())
                .RuleFor(x => x.BookableOnline, true)
                .RuleFor(x => x.AllowNewClients, true)
                .RuleFor(x => x.AssignedStaffIds, [PersonId])
                .RuleFor(x => x.LocationIds, [location.Id])
                .Generate()
                .ToDataModel();

            var providerItemGroups = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Name, f => f.Random.Guid().ToString())
                .RuleFor(x => x.ItemIds, [providerItem.Id])
                .Generate(3)
                .ToDataModel();

            var targetGroup = providerItemGroups[0];

            targetGroup.Name = "Test group 1";

            await DataContext.AddRangeAsync(location, providerItem);
            await DataContext.AddRangeAsync(providerItemGroups);
            await DataContext.SaveChangesAsync();

            var searchTerm = "test";
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/itemGroups?searchTerm={searchTerm}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<ProviderItemGroup>>();
            result.Should().NotBeNull();
            result.Items.Should().NotBeNull();
            result.Items.Count.Should().Be(1);
            result.Items.First().Id.Should().Be(targetGroup.Id);
        }
        
        [Fact]
        public async Task GetItemGroups_Search_By_Group_Item_Title()
        {
            var location = new ProviderLocationFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate()
                .ToDataModel();

            var providerItems = new ProviderItemFaker(ProviderId)
                .RuleFor(x => x.Title, f => f.Random.Guid().ToString())
                .RuleFor(x => x.DisplayName, f => f.Random.Guid().ToString())
                .RuleFor(x => x.Description, f => f.Random.Guid().ToString())
                .RuleFor(x => x.Code, f => f.Random.Guid().ToString())
                .RuleFor(x => x.BookableOnline, true)
                .RuleFor(x => x.AllowNewClients, true)
                .RuleFor(x => x.AssignedStaffIds, [PersonId])
                .RuleFor(x => x.LocationIds, [location.Id])
                .Generate(3)
                .ToDataModel()
                .ToArray();

            var providerItemGroups = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Name, f => f.Random.Guid().ToString())
                .Generate(3);

            providerItemGroups[0].ItemIds = [providerItems[0].Id];
            providerItemGroups[1].ItemIds = [providerItems[1].Id];
            providerItemGroups[2].ItemIds = [providerItems[2].Id];
            
            var targetGroup = providerItemGroups[1];
            var targetItem = providerItems[1];
            targetItem.Title = "Test service 1";

            await DataContext.AddRangeAsync(location);
            await DataContext.AddRangeAsync(providerItems);
            await DataContext.AddRangeAsync(providerItemGroups.ToDataModel());
            await DataContext.SaveChangesAsync();

            var searchTerm = "test";
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/itemGroups?searchTerm={searchTerm}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<ProviderItemGroup>>();
            result.Should().NotBeNull();
            result.Items.Should().NotBeNull();
            result.Items.Count.Should().Be(1);
            result.Items.First().Id.Should().Be(targetGroup.Id);
        }
        
        [Fact]
        public async Task GetItemGroups_Search_By_Group_Item_Title_Should_Return_Filtered_ItemIds()
        {
            var location = new ProviderLocationFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate()
                .ToDataModel();

            var providerItems = new ProviderItemFaker(ProviderId)
                .RuleFor(x => x.Title, f => f.Random.Guid().ToString())
                .RuleFor(x => x.DisplayName, f => f.Random.Guid().ToString())
                .RuleFor(x => x.Description, f => f.Random.Guid().ToString())
                .RuleFor(x => x.Code, f => f.Random.Guid().ToString())
                .RuleFor(x => x.BookableOnline, true)
                .RuleFor(x => x.AllowNewClients, true)
                .RuleFor(x => x.AssignedStaffIds, [PersonId])
                .RuleFor(x => x.LocationIds, [location.Id])
                .Generate(3)
                .ToDataModel()
                .ToArray();

            var providerItemGroup = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Name, f => f.Random.Guid().ToString())
                .RuleFor(x => x.ItemIds, providerItems.Select(x => x.Id).ToArray())
                .Generate();

            var targetItem = providerItems[1];
            targetItem.Title = "Test service 1";

            await DataContext.AddRangeAsync(location);
            await DataContext.AddRangeAsync(providerItems);
            await DataContext.AddRangeAsync(providerItemGroup.ToDataModel());
            await DataContext.SaveChangesAsync();

            var searchTerm = "test";
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/itemGroups?searchTerm={searchTerm}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<ProviderItemGroup>>();
            result.Should().NotBeNull();
            result.Items.Should().NotBeNull();
            result.Items.Count.Should().Be(1);
            result.Items.First().Id.Should().Be(providerItemGroup.Id);
            
            var itemGroup = result.Items.First();
            itemGroup.ItemIds.Should().NotBeNull();
            itemGroup.ItemIds.Length.Should().Be(1);
            itemGroup.ItemIds.Should().Contain(targetItem.Id);
            itemGroup.NumberOfServices.Should().Be(1);
        }

        [Fact]
        public async Task DeleteItemGroups()
        {
            var location = new ProviderLocationFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate()
                .ToDataModel();

            var providerItem = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.BookableOnline, true)
                .RuleFor(x => x.AllowNewClients, true)
                .RuleFor(x => x.AssignedStaffIds, new Guid[] { PersonId })
                .RuleFor(x => x.LocationIds, new[] { location.Id })
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(location, providerItem);
            await DataContext.SaveChangesAsync();

            var requestModel = new SaveProviderItemGroupRequest
            {
                Name = $"ServiceGroup - {ProviderId} {providerItem.Id}",
                ItemIds = new[] { providerItem.Id }
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/itemGroups")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(requestModel)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);
            var result = await response.Content.ReadAsAsync<ProviderItemGroup>();
            result.Should().NotBeNull();

            var deleteRequest = new HttpRequestMessageBuilder(HttpMethod.Delete, $"api/providers/{ProviderId}/itemGroups/{result.Id}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var deleteResponse = await ClientApi.SendAsync(deleteRequest);
            deleteResponse.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var itemGroup = DataContext.ProviderItemGroups.FirstOrDefault(x => x.Id == result.Id);
            itemGroup.Should().BeNull();

            var itemGroupItems = DataContext.ProviderItemGroupItems.Where(x => x.ProviderItemGroupId == result.Id)?.ToList();
            itemGroupItems.Should().BeNullOrEmpty();
        }

        [Fact]
        public async Task ReorderProviderItemGroups_Should_Order_ProviderItemGroups()
        {
            var groupItems = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate(3)
                .ToDataModel();

            var providerItemGroup1 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Order, 0)
                .RuleFor(x => x.ItemIds, groupItems.Select(x => x.Id).ToArray())
                .Generate()
                .ToDataModel();

            var group2Items = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate(2)
                .ToDataModel();

            var providerItemGroup2 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Order, 1)
                .RuleFor(x => x.ItemIds, group2Items.Select(x => x.Id).ToArray())
                .Generate()
                .ToDataModel();

            var group3Items = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate(1)
                .ToDataModel();

            var providerItemGroup3 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Order, 2)
                .RuleFor(x => x.ItemIds, group3Items.Select(x => x.Id).ToArray())
                .Generate()
                .ToDataModel();

            DataContext.AddRange(providerItemGroup1, providerItemGroup2, providerItemGroup3);
            DataContext.AddRange(groupItems);
            DataContext.AddRange(group2Items);
            DataContext.AddRange(group3Items);
            await DataContext.SaveChangesAsync();

            var newItemGroupOrdering = new[]
            {
                new ProviderItemGroup
                {
                    Id = providerItemGroup1.Id,
                    Order = 2
                },
                new ProviderItemGroup
                {
                    Id = providerItemGroup2.Id,
                    Order = 0
                },
                new ProviderItemGroup
                {
                    Id = providerItemGroup3.Id,
                    Order = 1
                }
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/itemGroups/reorder")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(newItemGroupOrdering)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var updatedProviderItemGroups = DataContext.ProviderItemGroups
                .AsNoTracking()
                .Where(x => x.ProviderId == ProviderId)
                .OrderBy(x => x.Order)
                .ToList();

            updatedProviderItemGroups.Count.Should().Be(3);
            updatedProviderItemGroups.Should().BeEquivalentTo([
                new
                {
                    Id = providerItemGroup2.Id,
                    Order = 0
                },
                new
                {
                    Id = providerItemGroup3.Id,
                    Order = 1
                },
                new
                {
                    Id = providerItemGroup1.Id,
                    Order = 2
                }
            ], opt => opt.ExcludingMissingMembers());
        }

        [Fact]
        public async Task GetProviderItemGroups_Should_Order_ProviderItemGroup_Items()
        {
            var itemGroup1Items = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate(3)
                .ToDataModel()
                .ToArray();

            var providerItemGroup1 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Order, 0)
                .RuleFor(x => x.ItemIds, itemGroup1Items.Select(x => x.Id).ToArray())
                .Generate()
                .ToDataModel();

            var itemGroup2Items = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate(4)
                .ToDataModel()
                .ToArray();

            var providerItemGroup2 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Order, 1)
                .RuleFor(x => x.ItemIds, itemGroup2Items.Select(x => x.Id).ToArray())
                .Generate()
                .ToDataModel();


            //reverse the ordering of the items in providerItemGroup2 when inserting
            providerItemGroup1.GroupItems = providerItemGroup1.GroupItems.Reverse().ToArray();
            providerItemGroup2.GroupItems = providerItemGroup2.GroupItems.Reverse().ToArray();

            DataContext.AddRange(itemGroup1Items);
            DataContext.AddRange(itemGroup2Items);
            DataContext.AddRange(providerItemGroup1, providerItemGroup2);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/itemGroups")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PaginatedResult<ProviderItemGroup>>();
            result.Should().NotBeNull();
            result.Items.Should().NotBeNull();
            result.Items.Count.Should().Be(2);

            result.Items.Should().BeEquivalentTo([
                new ProviderItemGroup
                {
                    Id = providerItemGroup1.Id,
                    Order = 0,
                    Name = providerItemGroup1.Name,
                    ProviderId = providerItemGroup1.ProviderId,
                    ItemIds = itemGroup1Items.Select(x => x.Id).ToArray(),
                    NumberOfServices = itemGroup1Items.Length
                },
                new ProviderItemGroup
                {
                    Id = providerItemGroup2.Id,
                    Order = 1,
                    Name = providerItemGroup2.Name,
                    ProviderId = providerItemGroup2.ProviderId,
                    ItemIds = itemGroup2Items.Select(x => x.Id).ToArray(),
                    NumberOfServices = itemGroup2Items.Length
                }
            ], opt => opt.WithStrictOrderingFor(x => x.ItemIds));
        }


        [Fact]
        public async Task ReorderProviderItemGroups_Should_Order_ProviderItemGroup_Items()
        {
            var itemGroup1Items = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate(3)
                .ToDataModel()
                .ToArray();

            var providerItemGroup1 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Order, 0)
                .RuleFor(x => x.ItemIds, itemGroup1Items.Select(x => x.Id).ToArray())
                .Generate()
                .ToDataModel();

            var itemGroup2Items = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate(4)
                .ToDataModel()
                .ToArray();

            var providerItemGroup2 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Order, 1)
                .RuleFor(x => x.ItemIds, itemGroup2Items.Select(x => x.Id).ToArray())
                .Generate()
                .ToDataModel();

            var itemGroup3Items = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate(1)
                .ToDataModel()
                .ToArray();

            var providerItemGroup3 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Order, 2)
                .RuleFor(x => x.ItemIds, itemGroup3Items.Select(x => x.Id).ToArray())
                .Generate()
                .ToDataModel();

            DataContext.AddRange(providerItemGroup1, providerItemGroup2, providerItemGroup3);
            DataContext.AddRange(itemGroup1Items);
            DataContext.AddRange(itemGroup2Items);
            DataContext.AddRange(itemGroup3Items);
            await DataContext.SaveChangesAsync();


            var itemToMove = itemGroup1Items[1];
            var newItemGroup1Items = itemGroup1Items.Except([itemToMove]).ToList();

            var newItemGroup2Items = itemGroup2Items.ToList();
            //put the item to move at the top of the list
            newItemGroup2Items.Insert(0, itemToMove);

            var newItemGroupOrdering = new[]
            {
                new ProviderItemGroup
                {
                    Id = providerItemGroup1.Id,
                    Order = 0,
                    ItemIds = newItemGroup1Items.Select(x => x.Id).ToArray()
                },
                new ProviderItemGroup
                {
                    Id = providerItemGroup2.Id,
                    Order = 1,
                    ItemIds = newItemGroup2Items.Select(x => x.Id).ToArray()
                }
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/itemGroups/reorder")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(newItemGroupOrdering)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var updatedProviderItemGroups = await DataContext.ProviderItemGroups
                .AsNoTracking()
                .Where(x => x.ProviderId == ProviderId)
                .OrderBy(x => x.Order)
                .ToArrayAsync();

            updatedProviderItemGroups.Count().Should().Be(3);
            updatedProviderItemGroups.Should().BeEquivalentTo([
                new
                {
                    Id = providerItemGroup1.Id,
                    Order = 0,
                    GroupItems = newItemGroup1Items.Select((x, idx) => new
                    {
                        ProviderItemGroupId = providerItemGroup1.Id,
                        ProviderItemId = x.Id,
                        Order = idx
                    }).ToArray()
                },
                new
                {
                    Id = providerItemGroup2.Id,
                    Order = 1,
                    GroupItems = newItemGroup2Items.Select((x, idx) => new
                    {
                        ProviderItemGroupId = providerItemGroup2.Id,
                        ProviderItemId = x.Id,
                        Order = idx
                    }).ToArray()
                },
                new
                {
                    Id = providerItemGroup3.Id,
                    Order = 2,
                    GroupItems = itemGroup3Items.Select((x, idx) => new
                    {
                        ProviderItemGroupId = providerItemGroup3.Id,
                        ProviderItemId = x.Id,
                        Order = idx
                    }).ToArray()
                }
            ], opt => opt.ExcludingMissingMembers());
        }

        [Fact]
        public async Task GetItems_By_ProviderItemGroupId()
        {
            var providerItems = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .Generate(3)
                .ToDataModel()
                .ToArray();

            var providerItem1 = providerItems[0];
            var providerItem2 = providerItems[1];

            providerItem2.Order = 0;
            providerItem1.Order = 1;

            var providerItemGroup = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Order, 0)
                .RuleFor(x => x.ItemIds, [providerItem2.Id, providerItem1.Id])
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(providerItems);
            await DataContext.AddRangeAsync(providerItemGroup);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/itemGroups/{providerItemGroup.Id}/items")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<CollectionResult<ProviderItem>>();

            result.Should().NotBeNull();
            result.Items.Should().NotBeNull();
            result.Items.Count.Should().Be(2);

            result.Items.Should().BeEquivalentTo([
                new { providerItem2.Id, },
                new { providerItem1.Id, }
            ], opt => opt.ExcludingMissingMembers().WithStrictOrdering());
        }
        
        [Fact]
        public async Task GetItems_By_ProviderItemGroupId_Search_By_Title()
        {
            var providerItems = new ProviderItemFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Title, f => f.Random.Guid().ToString())
                .RuleFor(x => x.DisplayName, f => f.Random.Guid().ToString())
                .RuleFor(x => x.Description, f => f.Random.Guid().ToString())
                .RuleFor(x => x.Code, f => f.Random.Guid().ToString())
                .Generate(3)
                .ToDataModel()
                .ToArray();

            var providerItem1 = providerItems[0];
            var providerItem2 = providerItems[1];
            var providerItem3 = providerItems[2];
            
            var targetItem = providerItem1;
            targetItem.Title = "Test service 1";

            var providerItemGroup = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.Order, 0)
                .RuleFor(x => x.ItemIds, [providerItem1.Id, providerItem2.Id, providerItem3.Id])
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(providerItems);
            await DataContext.AddRangeAsync(providerItemGroup);
            await DataContext.SaveChangesAsync();

            var searchTerm = "test";
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/itemGroups/{providerItemGroup.Id}/items?searchTerm={searchTerm}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<CollectionResult<ProviderItem>>();

            result.Should().NotBeNull();
            result.Items.Should().NotBeNull();
            result.Items.Count.Should().Be(1);

            result.Items.Should().BeEquivalentTo([
                new { targetItem.Id, },
            ], opt => opt.ExcludingMissingMembers().WithStrictOrdering());
        }

        [Fact]
        public async Task GetItems_By_ProviderItemGroupId_Should_Return_Empty()
        {
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/itemGroups/{Guid.NewGuid()}/items")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<CollectionResult<ProviderItem>>();

            result.Should().NotBeNull();
            result.Items.Should().BeEmpty();
        }

        [Fact]
        public async Task SaveItemToItemGroups_Should_Add_To_Group_And_Remove_From_Other_Group()
        {
            var items = new ProviderItemFaker(ProviderId)
                .Generate(2)
                .ToDataModel()
                .ToArray();

            var item1 = items[0];
            var item2 = items[1];

            var group1 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ItemIds, [item1.Id])
                .Generate()
                .ToDataModel();

            var group2 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ItemIds, [item2.Id])
                .Generate()
                .ToDataModel();

            var group3 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ItemIds, [item2.Id])
                .Generate()
                .ToDataModel();

            var group4 = new ProviderItemGroupFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ItemIds, [item1.Id, item2.Id])
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(items);
            await DataContext.AddRangeAsync(group1, group2, group3, group4);
            await DataContext.SaveChangesAsync();

            var request = new SaveItemToItemGroupsRequest
            {
                ItemGroupsToCreate = [group2.Id, group4.Id],
                ItemGroupsToDelete = [group1.Id, group3.Id]
            };

            var httpRequest = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/itemGroups/items/{item1.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(request)
                .Create();

            var response = await ClientApi.SendAsync(httpRequest);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var groupIds = new[] { group1.Id, group2.Id, group3.Id, group4.Id };

            var itemGroupsDb = await DataContext.ProviderItemGroups
                .Include(x => x.GroupItems)
                .AsNoTracking()
                .Where(x => x.ProviderId == ProviderId && groupIds.Contains(x.Id))
                .ToListAsync();

            // should be removed from group1
            var group1Db = itemGroupsDb.FirstOrDefault(x => x.Id == group1.Id);
            group1Db.Should().NotBeNull();
            group1Db.GroupItems.Should().BeEmpty();

            // should be added to group2
            var group2Db = itemGroupsDb.FirstOrDefault(x => x.Id == group2.Id);
            group2Db.Should().NotBeNull();
            group2Db.GroupItems.Should().BeEquivalentTo([
                new { ProviderItemId = item2.Id, Order = 0 },
                new { ProviderItemId = item1.Id, Order = 99 },
            ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

            // should not change
            var group3Db = itemGroupsDb.FirstOrDefault(x => x.Id == group3.Id);
            group3Db.Should().NotBeNull();
            group3Db.GroupItems.Should().BeEquivalentTo([
                new { ProviderItemId = item2.Id, Order = 0 }
            ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

            // should not change
            var group4Db = itemGroupsDb.FirstOrDefault(x => x.Id == group4.Id);
            group4Db.Should().NotBeNull();
            group4Db.GroupItems.Should().BeEquivalentTo([
                new { ProviderItemId = item1.Id, Order = 0 },
                new { ProviderItemId = item2.Id, Order = 1 },
            ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
        }
    }
}