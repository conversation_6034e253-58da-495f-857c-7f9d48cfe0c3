using tests.common.Data.Mappers;
using System.Net;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Models.Pagination;
using tests.component.Builders;

namespace tests.component.Features.Contacts
{
    [Trait(nameof(CodeOwner), CodeOwner.Clients)]
    public class GetContactImportSummariesTests : BaseTestClass
    {
        public GetContactImportSummariesTests(ComponentTestFixture testFixture) : base(testFixture)
        {
        }

        [Fact]
        public async Task GetContactImportSummaries_Between_Dates()
        {
            var faker = new ContactImportSummaryFaker(ProviderId, PersonId);
            var import1 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 3, 3, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import2 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 1, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import3 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 3, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import4 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 5, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import5 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 5, 10, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();

            await DataContext.AddRangeAsync(import1, import2, import3, import4, import5);

            await DataContext.SaveChangesAsync();

            var fromDate = new DateTime(2024, 2, 4, 2, 30, 0, DateTimeKind.Utc);
            var toDate = new DateTime(2024, 2, 5, 11, 0, 0, DateTimeKind.Utc);

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/contacts/imports/summary?fromDate={fromDate:O}&toDate={toDate:O}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<CollectionResult<ContactImportSummary>>();
            result.Items.Should().HaveCount(3);
            result.Items.Should().BeEquivalentTo(new[] { import3, import4, import5 }, opt => opt.ExcludingMissingMembers());

        }


        [Fact]
        public async Task GetContactImportSummaries_With_FromDate_Only()
        {
            var faker = new ContactImportSummaryFaker(ProviderId, PersonId);
            var import1 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 3, 3, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import2 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 1, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import3 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 3, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import4 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 5, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import5 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 5, 10, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();

            await DataContext.AddRangeAsync(import1, import2, import3, import4, import5);

            await DataContext.SaveChangesAsync();

            var fromDate = new DateTime(2024, 2, 4, 2, 30, 0, DateTimeKind.Utc);
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/contacts/imports/summary?fromDate={fromDate:O}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<CollectionResult<ContactImportSummary>>();
            result.Items.Should().HaveCount(3);
            result.Items.Should().BeEquivalentTo(new[] { import3, import4, import5 }, opt => opt.ExcludingMissingMembers());

        }

        [Fact]
        public async Task GetContactImportSummaries_With_ToDate_Only()
        {
            var faker = new ContactImportSummaryFaker(ProviderId, PersonId);
            var import1 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 3, 3, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import2 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 1, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import3 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 3, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import4 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 5, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import5 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 5, 10, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();

            await DataContext.AddRangeAsync(import1, import2, import3, import4, import5);

            await DataContext.SaveChangesAsync();

            var toDate = new DateTime(2024, 2, 4, 4, 0, 0, DateTimeKind.Utc);
            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/contacts/imports/summary?toDate={toDate:O}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<CollectionResult<ContactImportSummary>>();
            result.Items.Should().HaveCount(3);
            result.Items.Should().BeEquivalentTo(new[] { import1, import2, import3 }, opt => opt.ExcludingMissingMembers());

        }

        [Fact]
        public async Task GetContactImportSummaries_With_No_DateRange()
        {
            var faker = new ContactImportSummaryFaker(ProviderId, PersonId);
            var import1 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 3, 3, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import2 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 1, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import3 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 3, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import4 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 5, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();
            var import5 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 5, 10, 21, 0, DateTimeKind.Utc)).Generate().ToDataModel();

            await DataContext.AddRangeAsync(import1, import2, import3, import4, import5);

            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/contacts/imports/summary")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<CollectionResult<ContactImportSummary>>();
            result.Items.Should().HaveCount(5);
            result.Items.Should().BeEquivalentTo(new[] { import1, import2, import3, import4, import5 }, opt => opt.ExcludingMissingMembers());

        }

        [Theory]
        [InlineData(null, 2)]
        [InlineData(false, 2)]
        [InlineData(true, 3)]
        public async Task GetContactImportSummaries_Should_Filter_By_IsContact(bool? isContact, int expectedCount)
        {
            var importSummaries = new ContactImportSummaryFaker(ProviderId, PersonId)
                .Generate(5)
                .ToDataModel();
            
            importSummaries[0].IsContact = true;
            importSummaries[1].IsContact = true;
            importSummaries[2].IsContact = true;

            await DataContext.AddRangeAsync(importSummaries);
            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/contacts/imports/summary?isContact={isContact}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<CollectionResult<ContactImportSummary>>();
            result.Items.Should().HaveCount(expectedCount);
            result.Items.Should().AllSatisfy(x => x.IsContact.Should().Be(isContact ?? false));
        }

        [Fact]
        public async Task GetContactImportSummaries_Should_Include_OriginalFileName()
        {
            var import = new ContactImportSummaryFaker(ProviderId, PersonId)
                .RuleFor(c => c.OriginalFileName, f => f.System.FileName())
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(import);

            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/contacts/imports/summary")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<CollectionResult<ContactImportSummary>>();
            result.Items.Should().HaveCount(1);
            result.Items.Should().BeEquivalentTo([new { import.Id, import.OriginalFileName }], opt => opt.ExcludingMissingMembers());
        }

        [Fact]
        public async Task GetContactImportSummaries_With_Statuses_Filter()
        {
            var faker = new ContactImportSummaryFaker(ProviderId, PersonId);
            var import1 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 3, 3, 21, 0, DateTimeKind.Utc))
                .RuleFor(c => c.Status, f => ImportSummaryStatus.Pending).Generate().ToDataModel();
            var import2 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 1, 21, 0, DateTimeKind.Utc))
                .RuleFor(c => c.Status, f => ImportSummaryStatus.Running).Generate().ToDataModel();
            var import3 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 3, 21, 0, DateTimeKind.Utc))
                .RuleFor(c => c.Status, f => ImportSummaryStatus.Successful).Generate().ToDataModel();
            var import4 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 4, 5, 21, 0, DateTimeKind.Utc))
                .RuleFor(c => c.Status, f => ImportSummaryStatus.Failed).Generate().ToDataModel();
            var import5 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 5, 10, 21, 0, DateTimeKind.Utc))
                .RuleFor(c => c.Status, f => ImportSummaryStatus.Draft).Generate().ToDataModel();
            var import6 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 6, 10, 21, 0, DateTimeKind.Utc))
                .RuleFor(c => c.Status, f => ImportSummaryStatus.Preprocessing).Generate().ToDataModel();
            var import7 = faker.RuleFor(c => c.CreatedDateTimeUtc, f => new DateTime(2024, 2, 7, 10, 21, 0, DateTimeKind.Utc))
                .RuleFor(c => c.Status, f => ImportSummaryStatus.ReadyForMapping).Generate().ToDataModel();

            await DataContext.AddRangeAsync(import1, import2, import3, import4, import5, import6, import7);

            await DataContext.SaveChangesAsync();

            var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                    $"api/providers/{ProviderId}/contacts/imports/summary?status={ImportSummaryStatus.Draft}&status={ImportSummaryStatus.Preprocessing}&status={ImportSummaryStatus.ReadyForMapping}")
                .WithBearerAuthorization(IdentityToken)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<CollectionResult<ContactImportSummary>>();
            result.Items.Should().HaveCount(3);
            result.Items.Should().BeEquivalentTo(new[] { import5, import6, import7 }, opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
        }
    }
}
