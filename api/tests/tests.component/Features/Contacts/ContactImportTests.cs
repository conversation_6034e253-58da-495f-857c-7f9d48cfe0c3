﻿using carepatron.core.Application.Contacts.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Repositories.SQS;
using carepatron.api.Contracts.Requests.Contacts;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using carepatron.core.Models.Execution;
using tests.common.Data.Fakers;
using carepatron.core.Application.Schema.Models;
using tests.component.Builders;

namespace tests.component.Features.Contacts
{
    [Trait(nameof(CodeOwner), CodeOwner.Clients)]
    public class ContactImportTests : BaseTestClass
    {

        public ContactImportTests(ComponentTestFixture testFixture) : base(testFixture)
        {
            ResolveService<Mock<ISqsRepository>>().Reset();
        }

        [Fact]
        public async Task ContactImport_SimplePractice_Test()
        {
            var publishedEvents = new List<EventData<ImportExternalContactData>>();
            var sqsRepositoryMock = this.ResolveService<Mock<ISqsRepository>>();
            sqsRepositoryMock.Setup(x => x.SendMessage(QueueType.Task, It.IsAny<EventMessage>(), It.IsAny<bool>()))
                .Callback(new InvocationAction(i =>
                {
                    var eventMessage = (EventMessage)i.Arguments[1];
                    var publishedEvent = eventMessage.As<EventData<ImportExternalContactData>>();
                    publishedEvents.Add(publishedEvent);
                }));

            var payload = new ContactImportRequest
            {
                ImportFileId = Guid.NewGuid(),
                ImportSource = ExternalContactImportSource.SimplePractice,
                FileName = "test-file.zip",
                FileExtension = ".zip",
                FileSize = 1000000
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/contacts/imports")
                            .WithBearerAuthorization(IdentityToken)
                            .WithPayload(payload)
                            .Create();


            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var importSummary = await response.Content.ReadAsAsync<ContactImportSummary>();

            importSummary.FileId.Should().Be(payload.ImportFileId);
            importSummary.FileName.Should().Be(payload.FileName);

            var importSummaryDataModel = DataContext.ContactImportSummaries.FirstOrDefault(i => i.Id == importSummary.Id);

            importSummaryDataModel.Should().BeEquivalentTo(new
            {
                ProviderId,
                importSummary.FileId,
                payload.FileName,
                payload.FileSize,
                payload.FileExtension,
                Status = ImportSummaryStatus.Pending,
                ImportType = ImportType.Standard,
            } , opt => opt.ExcludingMissingMembers());

            sqsRepositoryMock.Verify(x => x.SendMessage(QueueType.Task, It.IsAny<EventMessage>(), It.IsAny<bool>()), Times.Once);

            publishedEvents.Should().HaveCount(1);
            publishedEvents.First().Data.Should().BeEquivalentTo(new ImportExternalContactData(payload.ImportFileId, ProviderId, PersonId, ExternalContactImportSource.SimplePractice, importSummary.Id));
        }

        [Fact]
        public async Task ContactImport_Validation_Test()
        {
            var payload = new ContactImportRequest
            {
                ImportFileId = Guid.Empty,
                ImportSource = ExternalContactImportSource.SimplePractice,
                FileName = string.Empty,
                FileExtension = string.Empty,
                FileSize = 1000000
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/contacts/imports")
                            .WithBearerAuthorization(IdentityToken)
                            .WithPayload(payload)
                            .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.BadRequest);

            var result = await response.Content.ReadAsAsync<ValidationError>();

            result.Code.Should().Be("IsRequiredValidationFailure");
        }

        [Fact]
        public async Task ContactImport_CSV_Test()
        {
            var publishedEvents = new List<EventData<ImportContactsFromCsvData>>();
            var sqsRepositoryMock = this.ResolveService<Mock<ISqsRepository>>();
            sqsRepositoryMock.Setup(x => x.SendMessage(QueueType.Task, It.IsAny<EventMessage>(), It.IsAny<bool>()))
                .Callback(new InvocationAction(i =>
                {
                    var eventMessage = (EventMessage)i.Arguments[1];
                    var publishedEvent = eventMessage.As<EventData<ImportContactsFromCsvData>>();
                    publishedEvents.Add(publishedEvent);
                }));


            var dataSchemaObject = new DataSchemaFaker(SchemaTypes.ContactSchema, ProviderId).Generate();
            var payload = new ContactImportRequest
            {
                ImportFileId = Guid.NewGuid(),
                ImportSource = ExternalContactImportSource.CSV,
                FileName = "test-file.zip",
                FileExtension = ".zip",
                FileSize = 1000000,
                MappedColumns = GetMappedColumns(),
                DataSchema = dataSchemaObject
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/contacts/imports")
                            .WithBearerAuthorization(IdentityToken)
                            .WithPayload(payload)
                            .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var importSummary = await response.Content.ReadAsAsync<ContactImportSummary>();

            importSummary.FileId.Should().Be(payload.ImportFileId);
            importSummary.FileName.Should().Be(payload.FileName);

            var importSummaryDataModel = DataContext.ContactImportSummaries.FirstOrDefault(i => i.Id == importSummary.Id);
            
            importSummaryDataModel.Should().BeEquivalentTo(new
            {
                ProviderId,
                importSummary.FileId,
                payload.FileName,
                payload.FileSize,
                payload.FileExtension,
                Status = ImportSummaryStatus.Pending,
                ImportType = ImportType.Standard,
                IsContact = false
            } , opt => opt.ExcludingMissingMembers());

            sqsRepositoryMock.Verify(x => x.SendMessage(QueueType.Task, It.IsAny<EventMessage>(), It.IsAny<bool>()), Times.Once);

            publishedEvents.Should().HaveCount(1);

            publishedEvents.First().Data.Should()
                .BeEquivalentTo(new ImportContactsFromCsvData(
                    ProviderId,
                    importSummary.Id,
                    PersonId,
                    payload.MappedColumns,
                    payload.DataSchema));
        }

        [Fact]
        public async Task ContactImport_CSV_should_validate_missing_mapped_columns_test()
        {
            var dataSchemaObject = new DataSchemaFaker(SchemaTypes.ContactSchema, ProviderId).Generate();
            var payload = new ContactImportRequest
            {
                ImportFileId = Guid.NewGuid(),
                ImportSource = ExternalContactImportSource.CSV,
                FileName = "test-file.zip",
                FileExtension = ".zip",
                FileSize = 1000000,
                DataSchema = dataSchemaObject
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/contacts/imports")
                            .WithBearerAuthorization(IdentityToken)
                            .WithPayload(payload)
                            .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.BadRequest);

            var result = await response.Content.ReadAsAsync<ValidationError>();

            result.Code.Should().Be("IsRequiredValidationFailure");
        }

        [Fact]
        public async Task ContactImport_Should_Set_OriginalFileName_Test()
        {
            var dataSchemaObject = new DataSchemaFaker(SchemaTypes.ContactSchema, ProviderId).Generate();
            var payload = new ContactImportRequest
            {
                ImportFileId = Guid.NewGuid(),
                ImportSource = ExternalContactImportSource.CSV,
                FileName = "test-file.zip",
                FileExtension = ".zip",
                FileSize = 1000000,
                MappedColumns = GetMappedColumns(),
                DataSchema = dataSchemaObject
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/contacts/imports")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var importSummary = await response.Content.ReadAsAsync<ContactImportSummary>();

            importSummary.OriginalFileName.Should().Be(payload.FileName);
        }
        
        [Fact]
        public async Task ContactImport_Should_Set_IsContact_Test()
        {
            var payload = new ContactImportRequest
            {
                ImportFileId = Guid.NewGuid(),
                ImportSource = ExternalContactImportSource.CSV,
                FileName = "test-file.zip",
                FileExtension = ".zip",
                FileSize = 1000000,
                IsContact = true,
                MappedColumns = GetMappedColumns()
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/contacts/imports")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(System.Net.HttpStatusCode.OK);

            var importSummary = await response.Content.ReadAsAsync<ContactImportSummary>();

            importSummary.IsContact.Should().Be(true);
        }

        private List<ImportContactsOption> GetMappedColumns()
        {
            return
            [
                new ImportContactsOption("firstName", "Name", null, ImportContactsFieldOption.FirstPart, " ", false),
                new ImportContactsOption("lastName", "Name", null, ImportContactsFieldOption.LastPart, " ", false),
                new ImportContactsOption("phoneNumber", "Phone", null, ImportContactsFieldOption.WholeField, null, false, null, "NZ"),
                new ImportContactsOption("email", "Email", null, ImportContactsFieldOption.WholeField, null, false),
                new ImportContactsOption("address", "Address", null, ImportContactsFieldOption.WholeField, null, false)
            ];
        }
    }
}
