using System.IO;
using System.Reflection;
using Agents.Module;
using Agents.Module.AgentRunners;
using Agents.Module.Infrastructure.InversionOfControl;
using Agents.Module.Services;
using carepatron.core.Application.Contacts.Agents;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Schema.Models.Properties;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using tests.common.Mocks;

namespace tests.component.Features.Contacts;

public class ImportContactMappingAgentTests
{
    private readonly ServiceProvider serviceProvider;

    public ImportContactMappingAgentTests()
    {
        serviceProvider = new ServiceCollection()
            .RegisterAgentServices(new ConfigurationBuilder()
                .SetBasePath(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? string.Empty)
                .AddJsonFile("appsettings.Test.json")
                .Build())
            .AddScoped<TimerFactory>(provider => (interval) => new MockTimer(interval))
            .BuildServiceProvider();
    }
    
    [Fact(Skip = "Manual integration test")]
    public async Task ImportContactMappingAgent_Mapping_DateFormat_Test()
    {
        var formattedLayout = @"
Layout: 
1. Name
    - FirstName: String
    - LastName: String
    - BirthDate: Date
";

        var csvContent = @"
First Name,Last Name,DOB
John,Doe,12/31/2020
";
        var (result, rawResult) = await RunAgent(formattedLayout, csvContent);

        var expectedMappings = new List<ImportContactsOption>
        {
            CreateMapping("FirstName", "First Name", fieldOptions: ImportContactsFieldOption.WholeField, delimiter: null),
            CreateMapping("LastName", "Last Name", fieldOptions: ImportContactsFieldOption.WholeField, delimiter: null),
            CreateMapping("BirthDate", "DOB", fieldOptions: ImportContactsFieldOption.WholeField, delimiter: null, dateFormat: DateFormatType.MMDDYYYY)
        };

        result.Should().NotBeNull();
        result.Mappings.Should().BeEquivalentTo(expectedMappings);
    }
    
    [Fact(Skip = "Manual integration test")]
    public async Task ImportContactMappingAgent_Clean_Update_Suggested_CustomField_Test()
    {
        var formattedLayout = @"
Layout: 
1. Name
    - FirstName: String
    - LastName: String
";

        var csvContent = @"
First Name,Last Name,Lab Test Result!@#$%^&*,ADSASDFASFE$WAS
John,Doe,positive for COVID-19,what is this
";
        var (result, rawResult) = await RunAgent(formattedLayout, csvContent);

        var expectedMappings = new List<ImportContactsOption>
        {
            CreateMapping("FirstName", "First Name", fieldOptions: ImportContactsFieldOption.WholeField),
            CreateMapping("LastName", "Last Name", fieldOptions: ImportContactsFieldOption.WholeField)
        };

        result.Should().NotBeNull();
        result.Mappings.Should().BeEquivalentTo(expectedMappings);

        var expectedCustomFieldsMapping = new List<ImportContactsOption>
        {
            CreateMapping("Lab Test Result", "Lab Test Result!@#$%^&*"),
            CreateMapping("ADSASDFASFE$WAS", "ADSASDFASFE$WAS")
        };

        result.SuggestedCustomFields.Select(x => x.ColumnMapping).Should().BeEquivalentTo(expectedCustomFieldsMapping);
    }

    [Fact(Skip = "Manual integration test")]
    public async Task ImportContactMappingAgent_Using_MultipleEmail_Test()
    {
        var formattedLayout = @"
Layout: 
1. Name
    - FirstName: String
    - LastName: String
2. Contact Details
    - PhoneNumber: Phone
    - Email: Email
3. Others
    - Tags: Lookup
";

        var csvContent = @"
First Name,Last Name,Email,Alternate Email
John,Doe,<EMAIL>,<EMAIL>
";
        var (result, rawResult) = await RunAgent(formattedLayout, csvContent);

        var expectedMappings = new List<ImportContactsOption>
        {
            CreateMapping("FirstName", "First Name", fieldOptions: ImportContactsFieldOption.WholeField),
            CreateMapping("LastName", "Last Name", fieldOptions: ImportContactsFieldOption.WholeField),
            CreateMapping("PhoneNumber", string.Empty, spreadsheetMultipleFieldNames: [], isMultiple: true, fieldOptions: ImportContactsFieldOption.WholeField),
            CreateMapping("Tags", string.Empty, spreadsheetMultipleFieldNames: [], isMultiple: true, fieldOptions: ImportContactsFieldOption.WholeField),
            CreateMapping("Email", string.Empty, spreadsheetMultipleFieldNames: ["Email", "Alternate Email"], isMultiple: true, fieldOptions: ImportContactsFieldOption.WholeField),
        };

        result.Should().NotBeNull();
        result.Mappings.Should().BeEquivalentTo(expectedMappings);
    }

    [Fact(Skip = "Manual integration test")]
    public async Task ImportContactMappingAgent_Using_ImportContactsFieldOption_Test()
    {
        var formattedLayout = @"
Layout: 
1. Name
    - FirstName: String
    - LastName: String
    - MiddleNames: String
2. Contact Details
    - PhoneNumber: Phone
    - Email: Email
";

        var csvContent = @"
Nombre completo,Número de Teléfono,Correo Electrónico,Signo del Zodiaco,Etiqueta,Dirección
Tatyana Álvarez,+228 54 827 441,<EMAIL>,Aries,Report,""18 Stroman Turnpike, New Ansley, 98482, Lancashire, Saint Martin""
María José Álvarez,+506 7870 4321,<EMAIL>,Pisces,Test 5,""873 Antonietta Highway, Cuyahoga Falls, 41546-4726, Isle of Wight, Guinea""
";
        var (result, rawResult) = await RunAgent(formattedLayout, csvContent);

        var expectedMappings = new List<ImportContactsOption>
        {
            CreateMapping("FirstName", "Nombre completo", fieldOptions: ImportContactsFieldOption.FirstPart, delimiter: " "),
            CreateMapping("LastName", "Nombre completo", fieldOptions: ImportContactsFieldOption.LastPart, delimiter: " "),
            CreateMapping("MiddleNames", "Nombre completo", fieldOptions: ImportContactsFieldOption.MiddlePart, delimiter: " "),
            CreateMapping("PhoneNumber", "Número de Teléfono", fieldOptions: ImportContactsFieldOption.WholeField),
            CreateMapping("Email", "Correo Electrónico", fieldOptions: ImportContactsFieldOption.WholeField),
        };

        result.Should().NotBeNull();
        result.Mappings.Should().BeEquivalentTo(expectedMappings);

        var expectedCustomFieldsMapping = new List<ImportContactsOption>
        {
            CreateMapping("Signo del Zodiaco", "Signo del Zodiaco"),
            CreateMapping("Etiqueta", "Etiqueta"),
            CreateMapping("Dirección", "Dirección")
        };

        result.SuggestedCustomFields.Select(x => x.ColumnMapping).Should().BeEquivalentTo(expectedCustomFieldsMapping);
    }

    [Fact(Skip = "Manual integration test")]
    public async Task ImportContactMappingAgent_Map_From_Multiple_Csv_Fields_Test()
    {
        var formattedLayout = @"
Layout: 
1. Basic
    - Email: Email
";

        var csvContent = @"
E-mail Address,Favorite Color 1, Favorite Color 2, Favorite Color 3
<EMAIL>,Red,Green,Blue
";
        var (result, rawResult) = await RunAgent(formattedLayout, csvContent);

        var expectedMappings = new List<ImportContactsOption>
        {
            CreateMapping("Email", "E-mail Address"),
        };

        result.Should().NotBeNull();
        result.Mappings.Should().BeEquivalentTo(expectedMappings);

        var expectedCsvFields = new List<string>
        {
            "Favorite Color 1",
            "Favorite Color 2",
            "Favorite Color 3"
        };

        result.SuggestedCustomFields.Should().HaveCount(1);

        var actualCustomField = result.SuggestedCustomFields.First();

        actualCustomField.ColumnMapping.CarepatronFieldName.Should().Be("Favorite Color");
        actualCustomField.ColumnMapping.SpreadsheetFieldName.Should().BeNullOrEmpty();
        actualCustomField.ColumnMapping.SpreadsheetMultipleFieldNames.Should().BeEquivalentTo(expectedCsvFields, opt => opt.WithoutStrictOrdering());
        actualCustomField.ColumnMapping.IsMultiple.Should().BeTrue();
        // actualCustomField.ColumnMapping.AsOneValue.Should().BeFalse();
        actualCustomField.ColumnMapping.Delimiter.Should().BeNullOrEmpty();
        actualCustomField.ColumnMapping.FieldOptions.Should().Be(ImportContactsFieldOption.WholeField);

        var optionSetProperty = actualCustomField.Property as OptionSetV2Property;
        optionSetProperty.Should().NotBeNull();
        optionSetProperty.Multiple.Should().BeTrue();
        optionSetProperty.FreeSolo.Should().BeTrue();

        optionSetProperty.Options.Should().HaveCount(3);
        optionSetProperty.Options.Should().BeEquivalentTo([
            new { Id = "Red", Label = "Red" },
            new { Id = "Green", Label = "Green" },
            new { Id = "Blue", Label = "Blue" }
        ], opt => opt.WithStrictOrdering().ExcludingMissingMembers());
    }

    [Fact(Skip = "Manual integration test")]
    public async Task ImportContactMappingAgent_Date_Test()
    {
        var formattedLayout = @"
Layout: 
1. Basic
    - Email: Email
";

        var csvContent = @"
E-mail Address,Availability,Last Checkup
<EMAIL>,2025-01-15 - 2025-04-15,2024-01-15
";
        var (result, rawResult) = await RunAgent(formattedLayout, csvContent);

        var expectedMappings = new List<ImportContactsOption>
        {
            CreateMapping("Email", "E-mail Address"),
        };

        result.Should().NotBeNull();
        result.Mappings.Should().BeEquivalentTo(expectedMappings);

        result.SuggestedCustomFields.Should().HaveCount(3);

        var firstPartMapping = result.SuggestedCustomFields
            .Where(x => x.ColumnMapping.SpreadsheetFieldName == "Availability" && x.ColumnMapping.FieldOptions == ImportContactsFieldOption.FirstPart).FirstOrDefault();
        firstPartMapping.Should().NotBeNull();
        firstPartMapping.ColumnMapping.CarepatronFieldName.Should().Be("Availability From");
        firstPartMapping.ColumnMapping.Delimiter.Should().Be(" - ");
        var firstPartProperty = firstPartMapping.Property as DateProperty;
        firstPartProperty.Should().NotBeNull();
        firstPartProperty.ShowDateDiff.Should().BeFalse();

        var lastPartMapping = result.SuggestedCustomFields
            .Where(x => x.ColumnMapping.SpreadsheetFieldName == "Availability" && x.ColumnMapping.FieldOptions == ImportContactsFieldOption.LastPart).FirstOrDefault();
        lastPartMapping.Should().NotBeNull();
        lastPartMapping.ColumnMapping.CarepatronFieldName.Should().Be("Availability To");
        lastPartMapping.ColumnMapping.Delimiter.Should().Be(" - ");
        var lastPartProperty = lastPartMapping.Property as DateProperty;
        lastPartProperty.Should().NotBeNull();
        lastPartProperty.ShowDateDiff.Should().BeFalse();

        var wholeFieldMapping = result.SuggestedCustomFields
            .Where(x => x.ColumnMapping.SpreadsheetFieldName == "Last Checkup" && x.ColumnMapping.FieldOptions == ImportContactsFieldOption.WholeField).FirstOrDefault();
        wholeFieldMapping.Should().NotBeNull();
        wholeFieldMapping.ColumnMapping.CarepatronFieldName.Should().Be("Last Checkup");
        wholeFieldMapping.ColumnMapping.Delimiter.Should().BeNullOrEmpty();
        var wholeFieldProperty = wholeFieldMapping.Property as DateProperty;
        wholeFieldProperty.Should().NotBeNull();
        wholeFieldProperty.ShowDateDiff.Should().BeTrue();
    }

    private async Task<(ImportContactMappingsResponse ParsedValue, string RawValue)> RunAgent(string formattedLayout, string csvContent)
    {
        using (var scope = serviceProvider.CreateScope())
        {
            var client = scope.ServiceProvider.GetRequiredService<IAgentClient>();

            var instruction = ImportContactMappingAgent.GetSystemInstruction(formattedLayout);

            var result = await client.Run(
                new ImportContactMappingAgent(instruction),
                new UserInput(Role.User, new TextUserInputPart(csvContent)));

            return (ImportContactMappingAgent.Parse(result.Output), result.Output);
        }
    }

    private ImportContactsOption CreateMapping(string carepatronFieldName,
        string spreadsheetFieldName,
        ImportContactsFieldOption? fieldOptions = null,
        bool? isMultiple = false,
        string delimiter = "",
        List<string> spreadsheetMultipleFieldNames = null,
        DateFormatType? dateFormat = null
    )
    {
        return new ImportContactsOption
        {
            CarepatronFieldName = carepatronFieldName,
            SpreadsheetFieldName = spreadsheetFieldName,
            FieldOptions = fieldOptions ?? ImportContactsFieldOption.WholeField,
            IsMultiple = isMultiple ?? false,
            Delimiter = delimiter,
            SpreadsheetMultipleFieldNames = spreadsheetMultipleFieldNames?.ToArray() ?? [],
            DateFormat = dateFormat
        };
    }
}