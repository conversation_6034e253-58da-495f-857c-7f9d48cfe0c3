using System.Net;
using carepatron.core.Application.Contacts.Models;
using carepatron.api.Contracts.Requests.Contacts;
using carepatron.core.Events.Events;
using Microsoft.EntityFrameworkCore;
using tests.common.Data.Mappers;
using carepatron.core.Models.Execution;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Repositories.SQS;
using Moq;
using tests.component.Builders;

namespace tests.component.Features.Contacts
{
    [Trait(nameof(CodeOwner), CodeOwner.Clients)]
    public class UpdateContactImportSummaryTests : BaseTestClass
    {
        private readonly Mock<ISqsRepository> sqsRepositoryMock;
        
        public UpdateContactImportSummaryTests(ComponentTestFixture testFixture) : base(testFixture)
        {
            sqsRepositoryMock = ResolveService<Mock<ISqsRepository>>();
            sqsRepositoryMock.Reset();
        }

        [Fact]
        public async Task UpdateContactImportSummary_LastStatusSeenBy_Test()
        { 
            var contactImportSummary = new ContactImportSummaryFaker(ProviderId, PersonId)
                .RuleFor(c=>c.LastStatusSeenBy, f=>[f.Random.Guid(), f.Random.Guid()])
                .Generate()
                .ToDataModel();

            await DataContext.AddAsync(contactImportSummary);
            await DataContext.SaveChangesAsync();

            var payload = new UpdateContactImportSummaryRequest
            {
                LastStatusSeenBy = contactImportSummary.LastStatusSeenBy.Concat([Guid.NewGuid(), Guid.NewGuid()]).ToArray()
            };
            var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/contacts/imports/summary/{contactImportSummary.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var updatedContactImportSummary = await response.Content.ReadAsAsync<ContactImportSummary>();
            updatedContactImportSummary.LastStatusSeenBy.Should().BeEquivalentTo(payload.LastStatusSeenBy);

            var contactImportSummaryDb = await DataContext.ContactImportSummaries.AsNoTracking().FirstOrDefaultAsync(c => c.Id == contactImportSummary.Id);
            contactImportSummaryDb.LastStatusSeenBy.Should().BeEquivalentTo(payload.LastStatusSeenBy);            

        }


        [Fact]
        public async Task UpdateContactImportSummary_Unknown_Id_Must_Return_NotFound_Test()
        {
            var mockSummaryId = Guid.NewGuid();
            var payload = new UpdateContactImportSummaryRequest
            {
                LastStatusSeenBy = [Guid.NewGuid(), Guid.NewGuid()]
            };
            var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/contacts/imports/summary/{mockSummaryId}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(HttpStatusCode.NotFound);

            var updatedContactImportSummary = await response.Content.ReadAsAsync<ValidationError>();
            updatedContactImportSummary.Code.Should().Be(Errors.NotFoundErrorCode);
            updatedContactImportSummary.Details.Should().Be(Errors.NotFoundErrorDetails);

        }
        
        [Fact]
        public async Task UpdateContactImportSummary_Should_Not_Update_OriginalFileName_Test()
        {
            var contactImportSummary = new ContactImportSummaryFaker(ProviderId, PersonId)
                .RuleFor(c => c.LastStatusSeenBy, f => [f.Random.Guid(), f.Random.Guid()])
                .RuleFor(c => c.OriginalFileName, f => f.Lorem.Word())
                .Generate()
                .ToDataModel();

            await DataContext.AddAsync(contactImportSummary);
            await DataContext.SaveChangesAsync();

            var payload = new UpdateContactImportSummaryRequest
            {
                LastStatusSeenBy = contactImportSummary.LastStatusSeenBy.Concat([Guid.NewGuid(), Guid.NewGuid()]).ToArray()
            };
            var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/contacts/imports/summary/{contactImportSummary.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var updatedContactImportSummary = await response.Content.ReadAsAsync<ContactImportSummary>();
            updatedContactImportSummary.OriginalFileName.Should().Be(contactImportSummary.OriginalFileName);

            var contactImportSummaryDb = await DataContext.ContactImportSummaries.AsNoTracking().FirstOrDefaultAsync(c => c.Id == contactImportSummary.Id);
            contactImportSummaryDb.OriginalFileName.Should().Be(contactImportSummary.OriginalFileName);          
        }

        [Theory]
        [InlineData(ImportSummaryStatus.ReadyForMapping, ImportSummaryStatus.Pending, true)]
        [InlineData(ImportSummaryStatus.ReadyForMapping, ImportSummaryStatus.Running, false)]
        [InlineData(ImportSummaryStatus.Draft, ImportSummaryStatus.Pending, false)]
        public async Task UpdateContactImportSummary_Start_Import_Test(ImportSummaryStatus status, ImportSummaryStatus newStatus, bool shouldStartImport)
        {
            var contactImportSummary = new ContactImportSummaryFaker(ProviderId, PersonId)
                .RuleFor(c => c.Status, status)
                .Generate()
                .ToDataModel();

            await DataContext.AddAsync(contactImportSummary);
            await DataContext.SaveChangesAsync();

            var payload = new UpdateContactImportSummaryRequest
            {
                Status = newStatus,
                LastStatusSeenBy = []
            };
            var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/contacts/imports/summary/{contactImportSummary.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var updatedContactImportSummary = await response.Content.ReadAsAsync<ContactImportSummary>();
            updatedContactImportSummary.Status.Should().Be(newStatus);

            var contactImportSummaryDb = await DataContext.ContactImportSummaries.AsNoTracking().FirstOrDefaultAsync(c => c.Id == contactImportSummary.Id);
            contactImportSummaryDb.Status.Should().Be(newStatus);

            if (shouldStartImport)
                sqsRepositoryMock.Verify(x => x.SendMessage(QueueType.Task, It.Is<EventMessage>(m => m.EventType == EventType.ImportContactsFromAiCsv), true), Times.Once);
            else
                sqsRepositoryMock.Verify(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>(), It.IsAny<bool>()), Times.Never);
        }
        
        [Fact]
        public async Task UpdateContactImportSummary_Should_Cancel_Import_Test()
        {
            var contactImportSummary = new ContactImportSummaryFaker(ProviderId, PersonId)
                .RuleFor(c => c.Status, ImportSummaryStatus.ReadyForMapping)
                .Generate()
                .ToDataModel();

            await DataContext.AddAsync(contactImportSummary);
            await DataContext.SaveChangesAsync();

            var payload = new UpdateContactImportSummaryRequest
            {
                Status = ImportSummaryStatus.Cancelled,
                LastStatusSeenBy = []
            };
            var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/contacts/imports/summary/{contactImportSummary.Id}")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);

            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var updatedContactImportSummary = await response.Content.ReadAsAsync<ContactImportSummary>();
            updatedContactImportSummary.Status.Should().Be(ImportSummaryStatus.Cancelled);

            var contactImportSummaryDb = await DataContext.ContactImportSummaries.AsNoTracking().FirstOrDefaultAsync(c => c.Id == contactImportSummary.Id);
            contactImportSummaryDb.Status.Should().Be(ImportSummaryStatus.Cancelled);
        }
    }
}
