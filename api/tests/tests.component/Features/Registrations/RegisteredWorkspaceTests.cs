﻿using System.Net;
using Bogus;
using carepatron.api.Contracts.Requests.Providers;
using carepatron.api.Contracts.Requests.Registrations;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Registrations.Events;
using carepatron.core.Application.Registrations.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Schema.Models.Properties;
using carepatron.core.Application.Staff.Models;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Application.Workspace.Locations.Models;
using carepatron.core.Application.Workspace.Models;
using carepatron.core.Application.Workspace.Providers.Models;
using carepatron.core.Localisation;
using carepatron.core.Localisation.SchemaDisplayNames;
using carepatron.core.Models.Execution;
using carepatron.core.Repositories.User;
using carepatron.infra.sql.Models.Templates;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json.Linq;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Extensions;
using tests.common.Mocks;
using tests.component.Builders;
using tests.component.Extensions;
using static carepatron.core.Application.Schema.Models.ContactCoreSchema;

namespace tests.component.Features.Registrations
{
    [Trait(nameof(CodeOwner), CodeOwner.Workspace)]
    public class RegisteredWorkspaceTests : BaseTestClass
    {
        public RegisteredWorkspaceTests(ComponentTestFixture testFixture) : base(testFixture)
        {
            Fixture.ClearEvents();
        }

        [Fact]
        public async Task RegisteredWorkspace_test()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            var x = await response.Content.ReadAsStringAsync();
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var providerDb = DataContext.Providers
                .AsNoTracking()
                .Where(x => x.Id == result.ProviderId)
                .FirstOrDefault();

            providerDb.Should().NotBeNull();
            providerDb.Should().BeEquivalentTo(new
            {
                Id = result.ProviderId,
                payload.Name,
                payload.CountryCode
            }, opt => opt.ExcludingMissingMembers());

            var registeredWorkspaceDb = DataContext.RegisteredWorkspaces
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .FirstOrDefault();
            registeredWorkspaceDb.Should().NotBeNull();
            registeredWorkspaceDb.Should().BeEquivalentTo(new
            {
                result.ProviderId,
                payload.Profession,
                payload.TeamSize,
                payload.ToolsUsed,
                Features = payload.ExploreFeatures,
            }, opt => opt.ExcludingMissingMembers());

            var adminStaff = DataContext.ProviderStaff
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId)
                .FirstOrDefault();
            adminStaff.Should().NotBeNull();
            adminStaff.Role.Should().Be(ProviderRole.Master_Admin);

            var personDb = DataContext.Persons
                .AsNoTracking()
                .Where(x => x.Id == result.PersonId)
                .FirstOrDefault();
            personDb.Should().NotBeNull();
            personDb.Should().BeEquivalentTo(new
            {
                Id = result.PersonId,
                Email = newEmail,
                registerResult.Sub
            }, opt => opt.ExcludingMissingMembers());

            var cognitoUser = await userRepository.GetUser(newEmail);
            cognitoUser.Profile.Should().BeNull();

            var personRegisteredEvents = this.Fixture.GetPublishedEventsOfType<PersonRegisteredEvent>();
            personRegisteredEvents.Should().HaveCount(1);

            var workspaceCreatedEvents = this.Fixture.GetPublishedEventsOfType<WorkspaceCreatedEvent>();
            workspaceCreatedEvents.Should().HaveCount(1);

            var contactsDb = DataContext.Contacts
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToList();

            contactsDb.Should().AllSatisfy(x => x.IsDemo.Should().BeTrue());
        }

        [Fact]
        public async Task RegisterWorkspace_ShouldCreateDefaultTemplates_BasedOnProfession_test()
        {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock.UseRealImplementation();

            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            //Psychiatry Template Ids in json
            var templateIds = new[]
            {
                new Guid("033d7a7c-631c-4435-857c-ab13254a9abf"),
                new Guid("2944a6da-f2d5-44a3-ac2a-62e8216c4ac6"),
                new Guid("70e61744-c2f2-4ec8-acab-57ce15e170cd"),
                new Guid("53abaa70-fe30-493c-86bc-528868a8306b"),
                new Guid("02ed2c49-2e1e-46d0-8270-b79a573fa7c9"),
                new Guid("db60f10b-9ead-45ee-aab6-5212a8aba505"),
                new Guid("787c6d29-ecfb-44f9-a5a6-c055315642ed"),
                new Guid("c70cc7fa-879f-4797-99f7-7b9a3daaf5dd"),
                new Guid("75eb2da6-86b6-4c86-b986-a619ed846a70"),
                new Guid("2248b16f-79f7-4e1c-9951-14160de44863"),
                new Guid("41b0f53c-973d-4bb1-9580-5952fea65241"),
                new Guid("7c4758ef-3a23-463c-8ed8-61b31d434491")
            };

            var (existingTemplates, existingtemplateFormFields) = await SetupTemplates(templateIds);

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = "Psychiatry",
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var providerDb = DataContext.Providers
                .AsNoTracking()
                .Where(x => x.Id == result.ProviderId)
                .FirstOrDefault();

            providerDb.Should().NotBeNull();
            providerDb.Should().BeEquivalentTo(new
            {
                Id = result.ProviderId,
                payload.Name,
                payload.CountryCode
            }, opt => opt.ExcludingMissingMembers());

            var registeredWorkspaceDb = DataContext.RegisteredWorkspaces
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .FirstOrDefault();
            registeredWorkspaceDb.Should().NotBeNull();
            registeredWorkspaceDb.Should().BeEquivalentTo(new
            {
                result.ProviderId,
                payload.Profession,
                payload.TeamSize,
                payload.ToolsUsed,
                Features = payload.ExploreFeatures,
            }, opt => opt.ExcludingMissingMembers());

            var adminStaff = DataContext.ProviderStaff
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId)
                .FirstOrDefault();
            adminStaff.Should().NotBeNull();
            adminStaff.Role.Should().Be(ProviderRole.Master_Admin);

            var personDb = DataContext.Persons
                .AsNoTracking()
                .Where(x => x.Id == result.PersonId)
                .FirstOrDefault();
            personDb.Should().NotBeNull();
            personDb.Should().BeEquivalentTo(new
            {
                Id = result.PersonId,
                Email = newEmail,
                registerResult.Sub
            }, opt => opt.ExcludingMissingMembers());

            var cognitoUser = await userRepository.GetUser(newEmail);
            cognitoUser.Profile.Should().BeNull();

            var personRegisteredEvents = this.Fixture.GetPublishedEventsOfType<PersonRegisteredEvent>();
            personRegisteredEvents.Should().HaveCount(1);

            var workspaceCreatedEvents = this.Fixture.GetPublishedEventsOfType<WorkspaceCreatedEvent>();
            workspaceCreatedEvents.Should().HaveCount(1);

            var newTemplateFormFields = await DataContext.TemplateFormFields
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToListAsync();

            newTemplateFormFields.Count.Should().Be(existingtemplateFormFields.Length);

            var newTemplates = await DataContext.Templates
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToListAsync();

            newTemplates.Count.Should().Be(existingTemplates.Length);


            newTemplateFormFields.Should().AllSatisfy(x =>
            {
                var newTemplate = newTemplates.First(i => i.Id == x.TemplateId);
                var template = existingTemplates.First(i => i.Title == newTemplate.Title);
                var templateFormField = existingtemplateFormFields.First(i => i.TemplateId == template.Id);
                x.Should().BeEquivalentTo(new
                {
                    x.TemplateId,
                    x.ProviderId,
                    x.Schema,
                    x.Version,
                    x.Type
                }, options => options.ExcludingMissingMembers());
            });


            newTemplates.Should().AllSatisfy(newTemplate =>
            {
                var newTemplateFormField = existingTemplates.First(x => x.Title == newTemplate.Title);

                var contentJsonString = newTemplate.ContentJsonb.ToString();
                contentJsonString = contentJsonString.Replace(existingtemplateFormFields[1].Id.ToString(), existingtemplateFormFields[1].Id.ToString());
                newTemplate.ContentJsonb = JObject.Parse(contentJsonString);

                newTemplateFormField.Should().BeEquivalentTo(new
                {
                    ProviderId,
                    newTemplate.Title,
                    TemplateType.Enrolment,
                    newTemplate.ContentJsonb,
                }, options => options.ExcludingMissingMembers());
            });
        }

        [Fact]
        public async Task RegisterWorkspace_ShouldCreateDefaultTemplates_BasedOnDefault_test()
        {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock.UseRealImplementation();

            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            //Others Template Ids in default-templates.json file
            var templateIds = new Guid[]
            {
                new Guid("0d6f66a3-977f-4246-bf57-55fb1ed1f2de"),
                new Guid("6de4f0f7-c882-4fb4-8d11-4612c7655f77"),
                new Guid("a74cc938-67bb-4146-b18a-3ced0c329831"),
                new Guid("e956c9cc-b848-4941-aff5-486ecc07570f"),
                new Guid("d061ad48-4aaa-4299-9b58-88b84cb8a3e3"),
                new Guid("94574362-478d-4059-9fcb-be4e3d975078"),
                new Guid("7829a1d7-e41b-46ff-9264-6d2e9f0c0b1c"),
                new Guid("ba73b0e0-2ec2-40cf-b60e-7b9a63c1f7d4"),
                new Guid("cc0abd3e-b3d5-4df6-b170-5ce6615263a1"),
                new Guid("7cd77e97-a38a-4443-b47c-362eb7980187"),
                new Guid("0a51070d-0d74-44dd-9edf-2f629bd0ae54"),
                new Guid("e26cf251-9924-4684-8af5-f7f1172022f4"),
                new Guid("12f74561-07df-4052-9d11-65002814b9f6"),
                new Guid("5506fa62-411c-400b-89f5-cdda0a3891e2"),
                new Guid("5afdda16-d8a2-4e65-a258-fe1df60f5fcd"),
                new Guid("9dd7e0a5-d713-4f60-8554-f0a7f1ee1bf6"),
                new Guid("7575760a-825b-4955-9427-90af1d4bee26"),
                new Guid("726e91d0-65bb-4f0a-b356-c35a54904778"),
                new Guid("88ca2662-571a-4c00-9c63-e8bfa4da2f07"),
                new Guid("0059a7c2-2ce0-4dfb-9d1b-156316d2c51a"),
                new Guid("30e70560-6dd1-410a-9ac6-99eceeca84bc"),
                new Guid("224b263b-58cb-4651-b7ab-8428556c2590"),
                new Guid("b1e6b9ab-f9f9-4891-92ee-ce60a6163db2"),
                new Guid("5f75cfda-2bde-477a-9301-32267a7c4da1"),
                new Guid("478052a5-aa9e-49d6-8815-705ac55f1dd4"),
                new Guid("933d9594-9390-4e9e-b2aa-45b15b724f60"),
                new Guid("a41eb85e-91a6-4be3-bb9d-5111b99ac8ca"),
                new Guid("616919e5-7172-43c2-94ab-2f22748a4f60"),
                new Guid("954ea077-8e63-46e2-9b62-7448f7b49ae0"),
                new Guid("cdd3c548-a040-4a1f-8087-e15d366b2107"),
                new Guid("a2fe26a1-d27d-43ff-95fd-ddcc60e8552e"),
                new Guid("17ae4a92-449c-473c-9432-37facbcbf536"),
                new Guid("3e7f1986-ebd9-455a-93d6-3e78d31ded19"),
                new Guid("9be088a5-0ea7-43ec-a6b9-b6a9bcd3ac68"),
                new Guid("3542a1f9-733d-4566-ba9f-25f76d3cfe51"),
                new Guid("9b414d2a-6b22-4aec-bc87-a37576bd69f7"),
                new Guid("06481562-751d-4faf-bf5d-2dac0c26538c"),
                new Guid("46d8835b-4c87-4575-8f62-365f48f6112c"),
                new Guid("0f01a015-16a1-43fd-a0f7-fffa242586c2"),
                new Guid("59321e27-93f5-45ee-a848-0a8f01ba0ce8"),
                new Guid("38bf872d-94f6-4726-a11f-430de76a5567"),
                new Guid("df223280-dee0-4221-875a-1e8c56dea282"),
                new Guid("6bd8537c-cfb1-4008-b70f-aed8e6773d68"),
                new Guid("d03069d6-ce88-48e7-b9f6-8758d8ee5062"),
                new Guid("3e5b8509-d493-4f3a-93ba-f58659bb8ba7"),
                new Guid("18a29fd7-6845-47cd-a093-ff8fc7980120"),
                new Guid("f296ece5-6942-4df1-a332-26b18fb6458a"),
                new Guid("521d4872-d824-4695-8883-a5b4403dabee"),
                new Guid("781f0796-8e85-4c55-a120-1f591e3b2947"),
                new Guid("e12735df-9ee0-4b8f-9bdd-57bee806423e"),
                new Guid("4eae0aab-0657-48ee-93e3-83c46454189e"),
                new Guid("10dc8443-5b2f-4f3f-b89a-ef01da136ba3"),
                new Guid("5193e90e-2d0d-4302-96a7-cdf12323b14f"),
                new Guid("1a8c4e58-0579-447a-a640-d80dfc321661"),
                new Guid("f38a117d-dbdd-457e-852c-052045051e5b"),
                new Guid("457126c6-3091-43c3-8bfa-5dd28b6add8c"),
                new Guid("64edf7c1-9b73-4340-a364-468af37c3df2"),
                new Guid("75eb2da6-86b6-4c86-b986-a619ed846a70"),
                new Guid("2248b16f-79f7-4e1c-9951-14160de44863"),
                new Guid("41b0f53c-973d-4bb1-9580-5952fea65241"),
                new Guid("7c4758ef-3a23-463c-8ed8-61b31d434491"),
            };

            var (existingTemplates, existingTemplateFormFields) = await SetupTemplates(templateIds);


            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = "Other-Profession",
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var providerDb = DataContext.Providers
                .AsNoTracking()
                .Where(x => x.Id == result.ProviderId)
                .FirstOrDefault();

            providerDb.Should().NotBeNull();
            providerDb.Should().BeEquivalentTo(new
            {
                Id = result.ProviderId,
                payload.Name,
                payload.CountryCode
            }, opt => opt.ExcludingMissingMembers());

            var registeredWorkspaceDb = DataContext.RegisteredWorkspaces
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .FirstOrDefault();
            registeredWorkspaceDb.Should().NotBeNull();
            registeredWorkspaceDb.Should().BeEquivalentTo(new
            {
                result.ProviderId,
                payload.Profession,
                payload.TeamSize,
                payload.ToolsUsed,
                Features = payload.ExploreFeatures,
            }, opt => opt.ExcludingMissingMembers());

            var adminStaff = DataContext.ProviderStaff
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId)
                .FirstOrDefault();
            adminStaff.Should().NotBeNull();
            adminStaff.Role.Should().Be(ProviderRole.Master_Admin);

            var personDb = DataContext.Persons
                .AsNoTracking()
                .Where(x => x.Id == result.PersonId)
                .FirstOrDefault();
            personDb.Should().NotBeNull();
            personDb.Should().BeEquivalentTo(new
            {
                Id = result.PersonId,
                Email = newEmail,
                registerResult.Sub
            }, opt => opt.ExcludingMissingMembers());

            var cognitoUser = await userRepository.GetUser(newEmail);
            cognitoUser.Profile.Should().BeNull();

            var personRegisteredEvents = this.Fixture.GetPublishedEventsOfType<PersonRegisteredEvent>();
            personRegisteredEvents.Should().HaveCount(1);

            var workspaceCreatedEvents = this.Fixture.GetPublishedEventsOfType<WorkspaceCreatedEvent>();
            workspaceCreatedEvents.Should().HaveCount(1);

            var newTemplateFormFields = await DataContext.TemplateFormFields
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToListAsync();

            newTemplateFormFields.Count.Should().Be(existingTemplateFormFields.Length);

            var newTemplates = await DataContext.Templates
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToListAsync();

            newTemplates.Count.Should().Be(existingTemplates.Length);


            newTemplateFormFields.Should().AllSatisfy(x =>
            {
                var newTemplate = newTemplates.First(i => i.Id == x.TemplateId);
                var template = existingTemplates.First(i => i.Title == newTemplate.Title);
                var templateFormField = existingTemplateFormFields.First(i => i.TemplateId == template.Id);
                x.Should().BeEquivalentTo(new
                {
                    x.TemplateId,
                    x.ProviderId,
                    x.Schema,
                    x.Version,
                    x.Type
                }, options => options.ExcludingMissingMembers());
            });


            newTemplates.Should().AllSatisfy(newTemplate =>
            {
                var newTemplateFormField = existingTemplates.First(x => x.Title == newTemplate.Title);

                var contentJsonString = newTemplate.ContentJsonb.ToString();
                contentJsonString = contentJsonString.Replace(existingTemplateFormFields[1].Id.ToString(), existingTemplateFormFields[1].Id.ToString());
                newTemplate.ContentJsonb = JObject.Parse(contentJsonString);

                newTemplateFormField.Should().BeEquivalentTo(new
                {
                    ProviderId,
                    newTemplate.Title,
                    TemplateType.Enrolment,
                    newTemplate.ContentJsonb,
                }, options => options.ExcludingMissingMembers());
            });
        }

        [Fact]
        public async Task RegisterWorkspace_ShouldNotCreate_DeletedFormFields_Test()
        {
            var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock.UseRealImplementation();

            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var templateIds = new[]
            {
                new Guid("033d7a7c-631c-4435-857c-ab13254a9abf"),
                new Guid("2944a6da-f2d5-44a3-ac2a-62e8216c4ac6"),
                new Guid("70e61744-c2f2-4ec8-acab-57ce15e170cd"),
                new Guid("53abaa70-fe30-493c-86bc-528868a8306b"),
                new Guid("02ed2c49-2e1e-46d0-8270-b79a573fa7c9"),
                new Guid("db60f10b-9ead-45ee-aab6-5212a8aba505"),
                new Guid("787c6d29-ecfb-44f9-a5a6-c055315642ed"),
                new Guid("c70cc7fa-879f-4797-99f7-7b9a3daaf5dd"),
                new Guid("75eb2da6-86b6-4c86-b986-a619ed846a70"),
                new Guid("2248b16f-79f7-4e1c-9951-14160de44863"),
                new Guid("41b0f53c-973d-4bb1-9580-5952fea65241"),
                new Guid("7c4758ef-3a23-463c-8ed8-61b31d434491")
            };
            var (templateModels, templateFormFields) = await SetupTemplates(templateIds);

            Array.ForEach(templateFormFields, x => x.Deleted = true);

            await DataContext.SaveChangesAsync();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = "Psychiatry",
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var newTemplateFormFields = await DataContext.TemplateFormFields
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToListAsync();

            newTemplateFormFields.Should().BeEmpty();

            var newTemplates = await DataContext.Templates
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToListAsync();

            newTemplates.Count.Should().Be(templateModels.Length);
        }

        [Fact]
        public async Task RegisterWorkspace_WithPromoCode_PublishesCodeToWorkspaceCreatedEvent()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila",
                ReferralCode = "COMPONENTTEST100"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var providerDb = DataContext.Providers
                .AsNoTracking()
                .Where(x => x.Id == result.ProviderId)
                .FirstOrDefault();

            providerDb.Should().NotBeNull();
            providerDb.Should().BeEquivalentTo(new
            {
                Id = result.ProviderId,
                payload.Name,
                payload.CountryCode
            }, opt => opt.ExcludingMissingMembers());

            var registeredWorkspaceDb = DataContext.RegisteredWorkspaces
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .FirstOrDefault();
            registeredWorkspaceDb.Should().NotBeNull();
            registeredWorkspaceDb.Should().BeEquivalentTo(new
            {
                result.ProviderId,
                payload.Profession,
                payload.TeamSize,
                payload.ToolsUsed,
                Features = payload.ExploreFeatures,
            }, opt => opt.ExcludingMissingMembers());

            var adminStaff = DataContext.ProviderStaff
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId)
                .FirstOrDefault();
            adminStaff.Should().NotBeNull();
            adminStaff.Role.Should().Be(ProviderRole.Master_Admin);

            var personDb = DataContext.Persons
                .AsNoTracking()
                .Where(x => x.Id == result.PersonId)
                .FirstOrDefault();
            personDb.Should().NotBeNull();
            personDb.Should().BeEquivalentTo(new
            {
                Id = result.PersonId,
                Email = newEmail,
                registerResult.Sub
            }, opt => opt.ExcludingMissingMembers());

            var cognitoUser = await userRepository.GetUser(newEmail);
            cognitoUser.Profile.Should().BeNull();

            var personRegisteredEvents = this.Fixture.GetPublishedEventsOfType<PersonRegisteredEvent>();
            personRegisteredEvents.Should().HaveCount(1);

            var workspaceCreatedEvents = this.Fixture.GetPublishedEventsOfType<WorkspaceCreatedEvent>();
            workspaceCreatedEvents.Should().HaveCount(1);
            workspaceCreatedEvents.FirstOrDefault().ReferralCode.Should().NotBeNullOrEmpty();
            workspaceCreatedEvents.FirstOrDefault().CustomerReferralCode.Should().BeNullOrEmpty();
        }

        [Fact]
        public async Task RegisterWorkspace_WithReferralCode_PublishesReferralCodeToWorkspaceCreatedEvent()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila",
                CustomerReferralCode = "COMPONENTTEST100"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var providerDb = DataContext.Providers
                .AsNoTracking()
                .Where(x => x.Id == result.ProviderId)
                .FirstOrDefault();

            providerDb.Should().NotBeNull();
            providerDb.Should().BeEquivalentTo(new
            {
                Id = result.ProviderId,
                payload.Name,
                payload.CountryCode
            }, opt => opt.ExcludingMissingMembers());

            var registeredWorkspaceDb = DataContext.RegisteredWorkspaces
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .FirstOrDefault();
            registeredWorkspaceDb.Should().NotBeNull();
            registeredWorkspaceDb.Should().BeEquivalentTo(new
            {
                result.ProviderId,
                payload.Profession,
                payload.TeamSize,
                payload.ToolsUsed,
                Features = payload.ExploreFeatures,
            }, opt => opt.ExcludingMissingMembers());

            var adminStaff = DataContext.ProviderStaff
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId)
                .FirstOrDefault();
            adminStaff.Should().NotBeNull();
            adminStaff.Role.Should().Be(ProviderRole.Master_Admin);

            var personDb = DataContext.Persons
                .AsNoTracking()
                .Where(x => x.Id == result.PersonId)
                .FirstOrDefault();
            personDb.Should().NotBeNull();
            personDb.Should().BeEquivalentTo(new
            {
                Id = result.PersonId,
                Email = newEmail,
                registerResult.Sub
            }, opt => opt.ExcludingMissingMembers());

            var cognitoUser = await userRepository.GetUser(newEmail);
            cognitoUser.Profile.Should().BeNull();

            var personRegisteredEvents = this.Fixture.GetPublishedEventsOfType<PersonRegisteredEvent>();
            personRegisteredEvents.Should().HaveCount(1);

            var workspaceCreatedEvents = this.Fixture.GetPublishedEventsOfType<WorkspaceCreatedEvent>();
            workspaceCreatedEvents.Should().HaveCount(1);
            workspaceCreatedEvents.FirstOrDefault().ReferralCode.Should().BeNullOrEmpty();
            // This will get Stripe promotion code based on the promo code in our app config
            // that will be used for customer referrals
            workspaceCreatedEvents.FirstOrDefault().CustomerReferralCode.Should().Be("COMPONENTTEST100");
        }

        [Fact]
        public async Task RegisteredWorkspace_should_return_not_found_when_no_cognito_user()
        {
            var faker = new Faker();
            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(IdentityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.IsSuccessStatusCode.Should().BeFalse();
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task RegisterWorkspace_should_add_person_with_basic_info()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(2),
                PhoneNumber = faker.Phone.PhoneNumber(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var nameSplit = payload.Name.Split(' ');
            var personDb = DataContext.Persons
                .Where(x => x.Id == result.PersonId)
                .FirstOrDefault();
            personDb.Should().NotBeNull();
            personDb.FirstName.Should().Be($"{nameSplit[0]}");
            var lastName = nameSplit.Where((x, index) => index > 0).ToArray();
            personDb.LastName.Should().Be(string.Join(" ", lastName));
            
            personDb.PhoneNumber.Should().Be(payload.PhoneNumber);
        }
        
        [Fact]
        public async Task RegisterWorkspace_should_not_update_person_phone_number()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var person = new PersonFaker()
                .RuleFor(x => x.Email, newEmail)
                .RuleFor(x => x.PhoneNumber, faker.Phone.PhoneNumber())
                .Generate()
                .ToDataModel();
            
            await DataContext.AddAsync(person);
            await DataContext.SaveChangesAsync();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(2),
                PhoneNumber = faker.Phone.PhoneNumber(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();
            
            result.PersonId.Should().Be(person.Id);

            var personDb = await DataContext.Persons
                .AsNoTracking()
                .Where(x => x.Id == person.Id)
                .FirstOrDefaultAsync();
            
            personDb.Should().NotBeNull();
            
            personDb.PhoneNumber.Should().Be(person.PhoneNumber);
        }
        
        [Fact]
        public async Task RegisterWorkspace_should_have_empty_last_name()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = "NoSpaceName",
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var nameSplit = payload.Name.Split(' ');
            var personDb = DataContext.Persons
                .Where(x => x.Id == result.PersonId)
                .FirstOrDefault();
            personDb.Should().NotBeNull();
            personDb.FirstName.Should().Be($"{nameSplit[0]}");
            personDb.LastName.Should().BeEmpty();
        }

        [Fact]
        public async Task RegisterWorkspace_should_have_contact_core_layout_schema()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = "NoSpaceName",
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var layoutSchema = DataContext.LayoutSchemas
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .FirstOrDefault();

            layoutSchema.Should().NotBeNull();
            layoutSchema.Elements.Should().NotBeEmpty();
            layoutSchema.Elements.Should().BeEquivalentTo(CoreSchema.CurrentSchemaContext.ContactLayoutElements);
        }

        [Theory]
        [InlineData("https://www.test-spammer.com")]
        [InlineData("http://www.test-spammer.com")]
        [InlineData("http://localhost")]
        [InlineData("Test Start http://www.test-spammer.com")]
        [InlineData("http://www.test-spammer.com/ Test End")]
        [InlineData("Test Start http://www.test-spammer.com/spam Test End")]
        public async Task RegisterWorkspace_Should_Not_Contain_Url_In_Name(string name)
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = name,
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);


            var validationError = await response.Content.ReadAsAsync<ValidationError>();

            validationError.Should().BeEquivalentTo(new
            {
                Code = "PredicateValidator",
                Details = "The specified condition was not met for 'Name'.",
                Type = ValidationType.BadRequest
            });
        }


        [Theory]
        [InlineData("<EMAIL>")]
        [InlineData("My <EMAIL> Workspace")]
        [InlineData("test <EMAIL>")]
        [InlineData("<EMAIL> Workspace")]
        [InlineData("<EMAIL>-test")]
        [InlineData("<EMAIL>")]
        public async Task RegisterWorkspace_Should_Not_Contain_Email_Address_In_Name(string name)
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = name,
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);


            var validationError = await response.Content.ReadAsAsync<ValidationError>();

            validationError.Should().BeEquivalentTo(new
            {
                Code = "PredicateValidator",
                Details = "The specified condition was not met for 'Name'.",
                Type = ValidationType.BadRequest
            });
        }

        [Fact]
        public async Task RegisterWorkspace_Should_Return_Bad_Request_When_There_Is_Html_in_Name()
        {
            var name = @"<script>alert('xss')</script><div onload=""alert('xss')"">My Workspace</div>";

            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = name,
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task RegisterWorkspace_Should_HaveDefaultTaxRate()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Name.FullName(),
                CountryCode = "NZ",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();
            var taxRate = await DataContext.TaxRates.FirstOrDefaultAsync(x => x.ProviderId == result.ProviderId);
            taxRate.Should().NotBeNull();
            taxRate.IsDefault.Should().BeTrue();
        }

        [Fact]
        public async Task RegisterWorkspace_should_create_personal_settings()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Name.FullName(),
                CountryCode = "NZ",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithHeader("Accept-Language", "en-US")
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var personalSettingsDb = await DataContext.PersonalSettings
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.PersonId == result.PersonId);
            personalSettingsDb.Should().NotBeNull();
            personalSettingsDb.Should().BeEquivalentTo(new
            {
                result.PersonId,
                payload.TimeZone,
                Locale = "en-US"
            });
        }

        [Fact]
        public async Task RegisterWorkspace_should_provision_initial_onboarding_info()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var addressFaker = new AddressFaker();
            var defaultServiceFaker = new DefaultServiceFaker();

            var locations = addressFaker.Generate(2).ToArray();
            var services = defaultServiceFaker.Generate(2).ToArray();

            Dictionary<int, ProvisioningTimeBlock[]> timeBlocks = new Dictionary<int, ProvisioningTimeBlock[]>
            {
                { 1, new[] { new ProvisioningTimeBlock { Start = new TimeOnly(9, 0), End = new TimeOnly(10, 0), Locations = [locations.First()], IsOnline = true } } },
                { 2, new[] { new ProvisioningTimeBlock { Start = new TimeOnly(9, 0), End = new TimeOnly(10, 0), Locations = [locations.First()], IsOnline = true } } },
            };

            var staffSchedules = new ProvisioningStaffScheduleFaker()
                .RuleFor(x => x.Services, f => [services.First()])
                .RuleFor(x => x.TimeBlocks, f => timeBlocks)
                .Generate();

            var payload = new RegisteredWorkspaceRequest
            {
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila",
                FirstName = faker.Name.FirstName(),
                LastName = faker.Name.LastName(),
                BusinessName = faker.Company.CompanyName(),
                Website = faker.Internet.Url(),
                BillingAddress = addressFaker.Generate(),
                Locations = locations,
                Services = services,
                StaffSchedules = [staffSchedules]
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var providerDb = DataContext.Providers
                .AsNoTracking()
                .Where(x => x.Id == result.ProviderId)
                .FirstOrDefault();

            providerDb.Should().NotBeNull();
            providerDb.Should().BeEquivalentTo(new
            {
                Id = result.ProviderId,
                payload.BusinessName,
                payload.CountryCode,
                payload.Website
            }, opt => opt.ExcludingMissingMembers());

            var registeredWorkspaceDb = DataContext.RegisteredWorkspaces
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .FirstOrDefault();
            registeredWorkspaceDb.Should().NotBeNull();
            registeredWorkspaceDb.Should().BeEquivalentTo(new
            {
                result.ProviderId,
                payload.Profession,
                payload.TeamSize,
                payload.ToolsUsed,
                Features = payload.ExploreFeatures,
            }, opt => opt.ExcludingMissingMembers());

            var adminStaff = DataContext.ProviderStaff
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId)
                .FirstOrDefault();
            adminStaff.Should().NotBeNull();
            adminStaff.Role.Should().Be(ProviderRole.Master_Admin);

            var providerBillingSettings = DataContext.ProviderBillingSettings
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .FirstOrDefault();
            providerBillingSettings.Should().NotBeNull();
            providerBillingSettings.Should().BeEquivalentTo(new
            {
                result.ProviderId,
                Address = payload.BillingAddress
            }, opt => opt.ExcludingMissingMembers());

            var providerLocations = DataContext.ProviderLocations
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToList();
            providerLocations.Should().NotBeEmpty();
            providerLocations.Should().BeEquivalentTo(payload.Locations.Select(l =>
                    new
                    {
                        Name = l.StreetAddress,
                        Type = LocationType.Physical,
                        AddressDetails = l
                    }),
                opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

            var providerItems = DataContext.Items
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToList();
            providerItems.Should().NotBeEmpty();
            providerItems.Should().BeEquivalentTo(payload.Services.Select(s =>
                    new
                    {
                        s.Name,
                        s.Code,
                        s.Duration,
                        s.Price,
                        s.ColorHex
                    }),
                opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

            var personDb = DataContext.Persons
                .AsNoTracking()
                .Where(x => x.Id == result.PersonId)
                .FirstOrDefault();
            personDb.Should().NotBeNull();
            personDb.Should().BeEquivalentTo(new
            {
                Id = result.PersonId,
                Email = newEmail,
                registerResult.Sub,
                payload.FirstName,
                payload.LastName
            }, opt => opt.ExcludingMissingMembers());

            var staffSchedulesDb = await DataContext.StaffSchedules
                .Include(x => x.StaffScheduleTimeBlocks)
                .Include(x => x.ServiceItems)
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId)
                .ToListAsync();

            staffSchedulesDb.Should().HaveCount(1);

            var staffScheduleDb = staffSchedulesDb.First();
            staffScheduleDb.Should().BeEquivalentTo(new
            {
                staffSchedules.Name,
                staffSchedules.TimeZone,
                IsActive = true,
            }, opt => opt.ExcludingMissingMembers());

            staffScheduleDb.ServiceItems.Should().BeEquivalentTo(new[] { new { Item = services.First() } }, opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

            staffScheduleDb.StaffScheduleTimeBlocks.Should().BeEquivalentTo(timeBlocks.SelectMany(x => x.Value).Select(x =>
                    new
                    {
                        x.Start,
                        x.End,
                        x.IsOnline,
                        Locations = new[] { locations.First() }
                    }), opt => opt.ExcludingMissingMembers().WithoutStrictOrdering()
            );

            var cognitoUser = await userRepository.GetUser(newEmail);
            cognitoUser.Profile.Should().BeNull();

            var personRegisteredEvents = this.Fixture.GetPublishedEventsOfType<PersonRegisteredEvent>();
            personRegisteredEvents.Should().HaveCount(1);

            var workspaceCreatedEvents = this.Fixture.GetPublishedEventsOfType<WorkspaceCreatedEvent>();
            workspaceCreatedEvents.Should().HaveCount(1);
        }

        [Fact]
        public async Task RegisterWorkspace_should_allow_empty_services_and_locations_for_requested_schedules()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var addressFaker = new AddressFaker();
            var defaultServiceFaker = new DefaultServiceFaker();

            var locations = addressFaker.Generate(2).ToArray();
            var services = defaultServiceFaker.Generate(2).ToArray();

            Dictionary<int, ProvisioningTimeBlock[]> timeBlocks = new Dictionary<int, ProvisioningTimeBlock[]>
            {
                { 1, new[] { new ProvisioningTimeBlock { Start = new TimeOnly(9, 0), End = new TimeOnly(10, 0), Locations = [], IsOnline = true } } },
                { 2, new[] { new ProvisioningTimeBlock { Start = new TimeOnly(9, 0), End = new TimeOnly(10, 0), Locations = [], IsOnline = true } } },
            };

            var staffSchedules = new ProvisioningStaffScheduleFaker()
                .RuleFor(x => x.Services, [])
                .RuleFor(x => x.TimeBlocks, timeBlocks)
                .Generate();

            var payload = new RegisteredWorkspaceRequest
            {
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila",
                FirstName = faker.Name.FirstName(),
                LastName = faker.Name.LastName(),
                BusinessName = faker.Company.CompanyName(),
                Website = faker.Internet.Url(),
                BillingAddress = addressFaker.Generate(),
                Locations = locations,
                Services = services,
                StaffSchedules = [staffSchedules]
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var staffSchedulesDb = await DataContext.StaffSchedules
                .Include(x => x.StaffScheduleTimeBlocks)
                .Include(x => x.ServiceItems)
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId)
                .ToListAsync();

            staffSchedulesDb.Should().HaveCount(1);

            var staffScheduleDb = staffSchedulesDb.First();
            staffScheduleDb.Should().BeEquivalentTo(new
            {
                staffSchedules.Name,
                staffSchedules.TimeZone,
                IsActive = true,
            }, opt => opt.ExcludingMissingMembers());

            staffScheduleDb.ServiceItems.Should().BeEmpty();

            staffScheduleDb.StaffScheduleTimeBlocks.ToList().ForEach(x => { x.StaffScheduleTimeBlockLocations.Should().BeEmpty(); });
        }

        [Fact]
        public async Task RegisterWorkspace_should_save_all_staff_schedule_locations()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var addressFaker = new AddressFaker();
            var defaultServiceFaker = new DefaultServiceFaker();

            var locations = addressFaker.Generate(2).ToArray();
            var services = defaultServiceFaker.Generate(2).ToArray();

            Dictionary<int, ProvisioningTimeBlock[]> timeBlocks = new Dictionary<int, ProvisioningTimeBlock[]>
            {
                { 1, new[] { new ProvisioningTimeBlock { Start = new TimeOnly(9, 0), End = new TimeOnly(10, 0), Locations = locations, IsOnline = true } } },
                { 2, new[] { new ProvisioningTimeBlock { Start = new TimeOnly(9, 0), End = new TimeOnly(10, 0), Locations = locations, IsOnline = true } } },
            };

            var staffSchedules = new ProvisioningStaffScheduleFaker()
                .RuleFor(x => x.Services, [])
                .RuleFor(x => x.TimeBlocks, timeBlocks)
                .Generate();

            var payload = new RegisteredWorkspaceRequest
            {
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila",
                FirstName = faker.Name.FirstName(),
                LastName = faker.Name.LastName(),
                BusinessName = faker.Company.CompanyName(),
                Website = faker.Internet.Url(),
                BillingAddress = addressFaker.Generate(),
                Locations = locations,
                Services = services,
                StaffSchedules = [staffSchedules]
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var staffSchedulesDb = await DataContext.StaffSchedules
                .Include(x => x.StaffScheduleTimeBlocks)
                .ThenInclude(x => x.StaffScheduleTimeBlockLocations)
                .Include(x => x.ServiceItems)
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId)
                .ToListAsync();

            staffSchedulesDb.Should().HaveCount(1);

            var providerLocationsDb = await DataContext.ProviderLocations.AsNoTracking().Where(x => x.ProviderId == result.ProviderId).ToListAsync();
            providerLocationsDb.Should().HaveCount(2);

            var staffScheduleDb = staffSchedulesDb.First();
            staffScheduleDb.Should().BeEquivalentTo(new
            {
                staffSchedules.Name,
                staffSchedules.TimeZone,
                IsActive = true,
            }, opt => opt.ExcludingMissingMembers());

            staffScheduleDb.ServiceItems.Should().BeEmpty();

            staffScheduleDb.StaffScheduleTimeBlocks.ToList().ForEach(x =>
            {
                var expected = providerLocationsDb.Select(l => new { LocationId = l.Id, l.ProviderId }).ToArray();
                x.StaffScheduleTimeBlockLocations.Should().BeEquivalentTo(expected, opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
            });
        }

        [Fact]
        public async Task RegisterWorkspace_should_save_only_selected_staff_schedule_locations()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var addressFaker = new AddressFaker();
            var defaultServiceFaker = new DefaultServiceFaker();

            var locations = addressFaker.Generate(2).ToArray();
            var services = defaultServiceFaker.Generate(2).ToArray();

            Dictionary<int, ProvisioningTimeBlock[]> timeBlocks = new Dictionary<int, ProvisioningTimeBlock[]>
            {
                { 1, new[] { new ProvisioningTimeBlock { Start = new TimeOnly(9, 0), End = new TimeOnly(10, 0), Locations = [locations.First()], IsOnline = true } } },
                { 2, new[] { new ProvisioningTimeBlock { Start = new TimeOnly(9, 0), End = new TimeOnly(10, 0), Locations = [locations.First()], IsOnline = true } } },
            };

            var staffSchedules = new ProvisioningStaffScheduleFaker()
                .RuleFor(x => x.Services, [])
                .RuleFor(x => x.TimeBlocks, timeBlocks)
                .Generate();

            var payload = new RegisteredWorkspaceRequest
            {
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila",
                FirstName = faker.Name.FirstName(),
                LastName = faker.Name.LastName(),
                BusinessName = faker.Company.CompanyName(),
                Website = faker.Internet.Url(),
                BillingAddress = addressFaker.Generate(),
                Locations = locations,
                Services = services,
                StaffSchedules = [staffSchedules]
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var staffSchedulesDb = await DataContext.StaffSchedules
                .Include(x => x.StaffScheduleTimeBlocks)
                .ThenInclude(x => x.StaffScheduleTimeBlockLocations)
                .Include(x => x.ServiceItems)
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId)
                .ToListAsync();

            staffSchedulesDb.Should().HaveCount(1);

            var providerLocationsDb = await DataContext.ProviderLocations.AsNoTracking().Where(x => x.ProviderId == result.ProviderId).ToListAsync();
            providerLocationsDb.Should().HaveCount(2);

            var staffScheduleDb = staffSchedulesDb.First();
            staffScheduleDb.Should().BeEquivalentTo(new
            {
                staffSchedules.Name,
                staffSchedules.TimeZone,
                IsActive = true,
            }, opt => opt.ExcludingMissingMembers());

            staffScheduleDb.ServiceItems.Should().BeEmpty();

            staffScheduleDb.StaffScheduleTimeBlocks.ToList().ForEach(x =>
            {
                var location = providerLocationsDb.FirstOrDefault(l => l.Address == locations.First().ToString());
                var expected = new { LocationId = location.Id, location.ProviderId };
                x.StaffScheduleTimeBlockLocations.Should().BeEquivalentTo(new[] { expected }, opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
            });
        }

        [Fact]
        public async Task RegisterWorkspace_should_not_create_items_if_services_is_empty()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila",
                Services = Array.Empty<DefaultService>()
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var providerDb = DataContext.Providers
                .AsNoTracking()
                .Where(x => x.Id == result.ProviderId)
                .FirstOrDefault();

            providerDb.Should().NotBeNull();
            providerDb.Should().BeEquivalentTo(new
            {
                Id = result.ProviderId,
                payload.Name,
                payload.CountryCode
            }, opt => opt.ExcludingMissingMembers());

            var providerItems = DataContext.Items
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToList();
            providerItems.Should().BeEmpty();
        }

        [Fact]
        public async Task RegisterWorkspace_should_use_default_schedule_if_no_schedules_in_payload()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            var x = await response.Content.ReadAsStringAsync();
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var providerDb = DataContext.Providers
                .AsNoTracking()
                .Where(x => x.Id == result.ProviderId)
                .FirstOrDefault();

            providerDb.Should().NotBeNull();

            var staffScheduleDb = await DataContext.StaffSchedules
                .Include(x => x.StaffScheduleTimeBlocks)
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId);

            var expectedSchedule = StaffSchedule.DefaultStaffSchedule(result.PersonId, result.ProviderId, payload.TimeZone);

            staffScheduleDb.Should().BeEquivalentTo(expectedSchedule, opts =>
                opts.Excluding(i => i.Id)
                    .Excluding(i => i.CreatedDateTimeUtc)
                    .Excluding(i => i.UpdatedDateTimeUtc)
                    .ExcludingMissingMembers()
                    .UsingSimpleDateTimePrecision()
            );
        }

        [Fact]
        public async Task RegisterWorkspace_should_assign_custom_locations_to_default_schedules()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var addressFaker = new AddressFaker();
            var locations = addressFaker.Generate(2).ToArray();

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila",
                Locations = locations
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            var x = await response.Content.ReadAsStringAsync();
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var providerDb = DataContext.Providers
                .AsNoTracking()
                .Where(x => x.Id == result.ProviderId)
                .FirstOrDefault();

            providerDb.Should().NotBeNull();

            var staffScheduleDb = await DataContext.StaffSchedules
                .Include(x => x.StaffScheduleTimeBlocks)
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.ProviderId == result.ProviderId && x.PersonId == result.PersonId);

            var providerLocations = await DataContext.ProviderLocations
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToListAsync();

            providerLocations.Should().NotBeEmpty();
            providerLocations.Should().BeEquivalentTo(payload.Locations.Select(l =>
                    new
                    {
                        Name = l.StreetAddress,
                        Type = LocationType.Physical,
                        AddressDetails = l
                    }),
                opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

            var expectedSchedule = StaffSchedule.DefaultStaffSchedule(result.PersonId, result.ProviderId, payload.TimeZone);

            staffScheduleDb.StaffScheduleTimeBlocks.ToList().ForEach(x =>
            {
                x.StaffScheduleTimeBlockLocations.Should().BeEquivalentTo(providerLocations.Select(l => new { LocationId = l.Id, l.ProviderId }),
                    opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
            });
        }

        [Fact]
        public async Task RegisterWorkspace_should_apply_localization()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var locale = "es-ES";

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Name.FullName(),
                CountryCode = "NZ",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithHeader("Accept-Language", locale)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var dataSchemaDb = await DataContext.DataSchemas
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.ProviderId == result.ProviderId);

            var layoutSchemaDb = await DataContext.LayoutSchemas
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.ProviderId == result.ProviderId);

            var clients = await DataContext.Contacts
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.IsClient)
                .ToListAsync();

            var tags = await DataContext.Tags
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToListAsync();

            dataSchemaDb.Should().NotBeNull();
            layoutSchemaDb.Should().NotBeNull();

            var localizer = ResolveService<IStringLocalizer<SchemaDisplayNames>>();
            using (locale.AsThreadCulture())
            {
                var excludedProperties = new string[] { "Fields" };
                var excludedOptionSet = new string[] { nameof(Contact.PreferredLanguage) };

                foreach (var item in dataSchemaDb.Properties)
                {
                    var controlProperty = item.Value as ControlProperty;
                    if (controlProperty is null || excludedProperties.Contains(item.Key)) continue;

                    controlProperty.DisplayName.Should().Be(localizer[$"ContactProperty_{item.Key}"]);

                    var optionSetProperty = item.Value as OptionSetV2Property;
                    if (optionSetProperty is not null && !excludedOptionSet.Contains(item.Key))
                    {
                        foreach (var option in optionSetProperty.Options)
                        {
                            option.Value.DisplayName.Should().Be(localizer[$"ContactProperty_{item.Key}_{option.Key}"]);
                        }

                        foreach (var group in optionSetProperty.Groups)
                        {
                            group.Value.DisplayName.Should().Be(localizer[$"ContactProperty_{item.Key}_Group_{group.Key}"]);
                        }
                    }
                }

                var localizedLayoutHeadings = new string[]
                {
                    localizer["ContactLayout_AboutClient"],
                    localizer["ContactLayout_Arrangements"],
                    localizer["ContactLayout_ContactDetails"],
                    localizer["ContactLayout_Name"],
                    localizer["ContactLayout_ProviderDetails"]
                };

                foreach (var layout in layoutSchemaDb.Elements)
                {
                    var layoutContainer = layout as LayoutContainer;
                    if (layoutContainer is null) continue;
                    localizedLayoutHeadings.Should().Contain(layoutContainer.Heading);
                }

                // these tags are the keys in the resource file
                var expectedTags = new Dictionary<string, string[]>
                {
                    { "<EMAIL>", new[] { "Telehealth", "Referral", "Intake", "Couple" } },
                    { "<EMAIL>", new[] { "Elevated Risk", "Insurance" } },
                    { "<EMAIL>", new[] { "Discount", "Assessment", "Group" } }
                };

                foreach (var (email, clientTags) in expectedTags)
                {
                    var client = clients.FirstOrDefault(x => x.Email == email);
                    client.Should().NotBeNull();
                    var expectedClientTags = clientTags.Select(title => new { Title = localizer[title] });
                    client.Tags.Should().BeEquivalentTo(expectedClientTags, x => x.ExcludingMissingMembers());
                }
            }
        }

        [Fact]
        public async Task RegisterWorkspace_should_not_apply_localization_when_local_is_default()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var defaultLocale = "en-US";

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Name.FullName(),
                CountryCode = "NZ",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithHeader("Accept-Language", defaultLocale)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var dataSchemaDb = await DataContext.DataSchemas
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.ProviderId == result.ProviderId);

            var layoutSchemaDb = await DataContext.LayoutSchemas
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.ProviderId == result.ProviderId);

            var clients = await DataContext.Contacts
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId && x.IsClient)
                .ToListAsync();

            var tags = await DataContext.Tags
                .AsNoTracking()
                .Where(x => x.ProviderId == result.ProviderId)
                .ToListAsync();

            dataSchemaDb.Should().NotBeNull();
            layoutSchemaDb.Should().NotBeNull();

            dataSchemaDb.Properties.Select(x => (x.Value as ControlProperty).DisplayName)
                .Should().BeEquivalentTo(new[] { "Client Notes", "Date First Seen", "Referred By" },
                    opt => opt.WithoutStrictOrdering());

            layoutSchemaDb.Elements.Select(x => (x as LayoutContainer).Heading)
                .Should().BeEquivalentTo(new[] { LayoutContainerHeading.Name, LayoutContainerHeading.ContactDetails, LayoutContainerHeading.AboutClient, LayoutContainerHeading.ProviderDetails },
                    opt => opt.WithoutStrictOrdering());

            // these tags are already the display names
            var expectedTags = new Dictionary<string, string[]>
            {
                { "<EMAIL>", new[] { "Telehealth", "Referral", "Intake", "Couple" } },
                { "<EMAIL>", new[] { "Elevated Risk", "Insurance" } },
                { "<EMAIL>", new[] { "Discount", "Assessment", "Group" } }
            };

            foreach (var (email, clientTags) in expectedTags)
            {
                var client = clients.FirstOrDefault(x => x.Email == email);
                client.Should().NotBeNull();
                var expectedClientTags = clientTags.Select(title => new { Title = title });
                client.Tags.Should().BeEquivalentTo(expectedClientTags, x => x.ExcludingMissingMembers());
            }
        }

        [Fact]
        public async Task RegisterWorkspace_should_save_branding_information()
        {
            var faker = new Faker();
            var newEmail = faker.Internet.CarePatronEmail();

            var userRepository = ResolveService<IUserRepository>();
            var registerResult = await userRepository.Register(null, newEmail, "", "password123", true);
            var identityToken = registerResult.UserAsJwtToken(newEmail);

            var logoRequest = new SaveLogoRequest
            {
                FileId = Guid.NewGuid(),
                FileName = faker.System.FileName(),
                ContentType = faker.System.MimeType(),
                FileSize = faker.Random.Number(),
                Url = faker.Internet.Url()
            };

            var payload = new RegisteredWorkspaceRequest
            {
                Name = faker.Company.CompanyName(),
                CountryCode = "PH",
                ExploreFeatures = faker.Random.WordsArray(5),
                Profession = faker.Random.Words(2),
                TeamSize = faker.Random.Words(2),
                ToolsUsed = faker.Commerce.ProductName(),
                TimeZone = "Asia/Manila",
                Logo = logoRequest,
                PrimaryColorHex = "#FF0000"
            };

            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/persons/registrations/workspace")
                .WithBearerAuthorization(identityToken)
                .WithPayload(payload)
                .Create();

            var response = await ClientApi.SendAsync(request);
            response.StatusCode.Should().Be(HttpStatusCode.OK);

            var result = await response.Content.ReadAsAsync<PersonRegisteredWorkspace>();
            result.Should().NotBeNull();

            var providerDb = await DataContext.Providers
                .AsNoTracking()
                .Include(x => x.Logo)
                .ThenInclude(x => x.File)
                .FirstOrDefaultAsync(x => x.Id == result.ProviderId);

            providerDb.Should().NotBeNull();
            providerDb.PrimaryColorHex.Should().Be(payload.PrimaryColorHex);
            providerDb.Logo.Should().BeEquivalentTo(logoRequest, opt => opt.ExcludingMissingMembers());
            providerDb.Logo.File.Should().BeEquivalentTo(new
            {
                Id = providerDb.Logo.FileId,
                providerDb.Logo.ContentType,
                providerDb.Logo.FileExtension,
                providerDb.Logo.FileName,
                providerDb.Logo.FileSize,
                providerDb.Logo.Url,
                MediaType = "Photo"
            }, opt => opt.ExcludingMissingMembers());

            var providerOnlineBookingOptionsDb = await DataContext.ProviderOnlineBookingOptions
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.ProviderId == result.ProviderId);

            providerOnlineBookingOptionsDb.Should().NotBeNull();
            providerOnlineBookingOptionsDb.ColorHex.Should().Be(payload.PrimaryColorHex);
        }

        private async Task<(TemplateDataModel[], TemplateFormFieldDataModel[])> SetupTemplates(Guid[] templateIds)
        {
            var templates = await DataContext.Templates.IgnoreQueryFilters().Where(x => templateIds.Contains(x.Id)).ToArrayAsync();
            var templateFormFields = await DataContext.TemplateFormFields.Where(x => templateIds.Contains(x.TemplateId)).ToArrayAsync();
            if (templates.Any())
            {
                DataContext.HardDeleteRange(templates);
            }

            if (templateFormFields.Any())
            {
                DataContext.TemplateFormFields.RemoveRange(templateFormFields);
            }

            await DataContext.SaveChangesAsync();
            var templateModels = new List<TemplateDataModel>();
            var formFieldModels = new List<TemplateFormFieldDataModel>();
            foreach (var i in templateIds)
            {
                var template = new TemplateFaker()
                    .RuleFor(x => x.Id, i)
                    .RuleFor(x => x.Title, i.ToString())
                    .RuleFor(x => x.ProviderId, ProviderId)
                    .RuleFor(x => x.CreatedByPersonId, PersonId)
                    .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
                    .Generate().ToDataModel();

                var formField = new TemplateFormFieldFaker(i)
                    .RuleFor(x => x.ProviderId, ProviderId)
                    .Generate()
                    .ToDataModel();

                DataContext.AddRange(template, formField);
                templateModels.Add(template);
                formFieldModels.Add(formField);
            }


            await DataContext.SaveChangesAsync();

            return (templateModels.ToArray(), formFieldModels.ToArray());
        }
    }
}