using System.Net;
using Bogus;
using carepatron.api.Contracts.Requests.Insurance;
using carepatron.core.Application.History.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Utilities;
using ClaimMD.Module.Models.Http.Requests;
using Microsoft.EntityFrameworkCore;
using Moq;
using Newtonsoft.Json;
using tests.common.Data.Fakers.ClaimMD;
using tests.common.Data.Mappers;
using tests.common.Mocks;
using tests.component.Builders;

namespace tests.component.Features.Insurance;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class SubmitClaimsTests : BaseClaimsTests
{
    private readonly ClaimMdHttpClientMock claimMdHttpClientMock = new();

    public SubmitClaimsTests(ComponentTestFixture fixture) : base(fixture)
    {
        claimMdHttpClientMock = ResolveService<ClaimMdHttpClientMock>();
        claimMdHttpClientMock.SetupMocks();
    }

    [Fact]
    public async Task SubmitClaim_ShouldUpdateStatusSuccessfully()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Manual))
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Amount.Should().Be(detail.Amount);
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Submitted);
        dbClaim.ClaimHeader.StatusReason.Should().BeNullOrEmpty();
        dbClaim.ClaimHeader.LastSubmittedDateTimeUtc.Should().NotBeNull();

        // There should be no metadata as this is a manual claim submission
        var dbClaimMetadata = await DataContext.InsuranceClaimClearingHouseMetadata.AsNoTracking()
            .FirstOrDefaultAsync(x => x.InsuranceClaimId == detail.Id);
        dbClaimMetadata.Should().BeNull();

        var historyDetail = ClaimHistoryModel.Create(detail);
        historyDetail.Status = ClaimStatus.Submitted;
        await CheckHistoryRecordExists(
            detail.Id,
            HistoryAction.ClaimStatusChanged,
            new ClaimStatusChangedHistoryActionDetail(historyDetail)
        );
    }

    [Fact]
    public async Task SubmitClaim_ForbiddenIfNoPermissions()
    {
        var (_, provider, token) = await SetupProviderAndLogin(noPermissions: true);

        var contact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
        ]);

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Code.Should().Be(Errors.UnauthorisedErrorCode);
        result.Details.Should().Be(Errors.UnauthorisedErrorDetails);

        await CheckHistoryRecordNotExists(
            detail.Id,
            HistoryAction.ClaimSubmitted
        );
    }

    [Fact]
    public async Task SubmitClaim_ShouldValidateIfClaimNotValidatedYet()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([contact.ToDataModel()]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, contact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = contact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var address = new AddressFaker()
            .RuleFor(x => x.StreetAddress, x => null)
            .Generate();
        var claimClient = new ClaimClientFaker(provider.Id, contact)
            .RuleFor(x => x.FirstName, x => null)
            .RuleFor(x => x.LastName, x => null)
            .RuleFor(x => x.MiddleName, x => null)
            .RuleFor(x => x.DateOfBirth, x => null)
            .RuleFor(x => x.PhoneNumber, x => null)
            .RuleFor(x => x.Sex, x => null)
            .RuleFor(x => x.Address, x => address)
            .Generate();
        var renderingProvider = new ClaimRenderingProviderFaker(provider.Id, staffMember)
            .RuleFor(x => x.FirstName, x => null)
            .RuleFor(x => x.LastName, x => null)
            .RuleFor(x => x.MiddleName, x => null)
            .Generate();
        var referringProvider = new ClaimReferringProviderFaker(provider.Id)
            .RuleFor(x => x.FirstName, x => null)
            .RuleFor(x => x.LastName, x => null)
            .RuleFor(x => x.MiddleName, x => null)
            .RuleFor(x => x.Qualifier, x => null)
            .Generate();
        var facility = new ClaimFacilityFaker(provider.Id)
            .RuleFor(x => x.Name, x => null)
            .RuleFor(x => x.Address, x => address)
            .Generate();
        var claimPolicy = new ClaimContactInsurancePolicyFaker(provider.Id, contact.Id, policy)
            .RuleFor(x => x.PolicyHolderFirstName, x => null)
            .RuleFor(x => x.PolicyHolderLastName, x => null)
            .RuleFor(x => x.PolicyHolderMiddleName, x => null)
            .RuleFor(x => x.PolicyHolderDateOfBirth, x => null)
            .RuleFor(x => x.PolicyHolderPhoneNumber, x => null)
            .RuleFor(x => x.PolicyHolderGroupId, x => null)
            .RuleFor(x => x.PolicyHolderMemberId, x => null)
            .RuleFor(x => x.CoverageType, x => null)
            .Generate();
        var claimBillingDetail = new ClaimBillingDetailFaker(provider.Id, defaultBillingProfile)
            .RuleFor(x => x.TaxNumberType, x => null)
            .RuleFor(x => x.TaxNumber, x => null)
            .RuleFor(x => x.TaxonomyCode, x => null)
            .RuleFor(x => x.NationalProviderId, x => null)
            .RuleFor(x => x.Address, x => address)
            .Generate();
        var serviceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id)
            .RuleFor(x => x.Units, x => 0)
            .RuleFor(x => x.Amount, x => 0)
            .Generate();

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(claimBillingDetail)
            .WithFacility(facility)
            .WithClient(claimClient)
            .WithContactInsurancePolicy(claimPolicy)
            .WithRenderingProviders([renderingProvider])
            .WithDiagnosticCodes(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders([referringProvider])
            .WithServiceLines([serviceLine])
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithBearerAuthorization(token)
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Manual))
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.InsuranceClaimFailedToValidateCode);
        validationError.Details.Should().Be(Errors.InsuranceClaimFailedToValidateDetails);
        var errors = JsonConvert.DeserializeObject<FieldValidationError[]>(validationError.AdditionalDataJson);
        errors.Should().NotBeNull();
        errors.Length.Should().BeGreaterThan(0);

        var fieldNameErrors = new List<string>
        {
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.FirstName)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.LastName)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.MiddleName)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.DateOfBirth)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.PhoneNumber)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.Sex)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.Address)}.{nameof(Address.StreetAddress)}",
            $"{nameof(InsuranceClaimUSProfessional.RenderingProviders)}[0].{nameof(ClaimRenderingProvider.FirstName)}",
            $"{nameof(InsuranceClaimUSProfessional.RenderingProviders)}[0].{nameof(ClaimRenderingProvider.LastName)}",
            $"{nameof(InsuranceClaimUSProfessional.RenderingProviders)}[0].{nameof(ClaimRenderingProvider.MiddleName)}",
            $"{nameof(InsuranceClaimUSProfessional.ReferringProviders)}[0].{nameof(ClaimReferringProvider.FirstName)}",
            $"{nameof(InsuranceClaimUSProfessional.ReferringProviders)}[0].{nameof(ClaimReferringProvider.LastName)}",
            $"{nameof(InsuranceClaimUSProfessional.ReferringProviders)}[0].{nameof(ClaimReferringProvider.MiddleName)}",
            $"{nameof(InsuranceClaimUSProfessional.ReferringProviders)}[0].{nameof(ClaimReferringProvider.Qualifier)}",
            $"{nameof(InsuranceClaimUSProfessional.ServiceFacility)}.{nameof(ClaimFacility.Name)}",
            $"{nameof(InsuranceClaimUSProfessional.ServiceFacility)}.{nameof(ClaimFacility.Address)}.{nameof(Address.StreetAddress)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderFirstName)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderLastName)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderMiddleName)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderDateOfBirth)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderPhoneNumber)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderGroupId)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderMemberId)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.CoverageType)}",
            $"{nameof(InsuranceClaimUSProfessional.BillingDetail)}.{nameof(ClaimBillingDetail.TaxNumberType)}",
            $"{nameof(InsuranceClaimUSProfessional.BillingDetail)}.{nameof(ClaimBillingDetail.TaxNumber)}",
            $"{nameof(InsuranceClaimUSProfessional.BillingDetail)}.{nameof(ClaimBillingDetail.TaxonomyCode)}",
            $"{nameof(InsuranceClaimUSProfessional.BillingDetail)}.{nameof(ClaimBillingDetail.NationalProviderId)}",
            $"{nameof(InsuranceClaimUSProfessional.BillingDetail)}.{nameof(ClaimBillingDetail.Address)}.{nameof(Address.StreetAddress)}",
            $"{nameof(InsuranceClaimUSProfessional.ServiceLines)}[0].{nameof(ClaimServiceLine.Units)}",
            $"{nameof(InsuranceClaimUSProfessional.ServiceLines)}[0].{nameof(ClaimServiceLine.Amount)}"
        };

        foreach (var error in errors)
        {
            error.Should().NotBeNull();
            error.FieldName.Should().NotBeNullOrEmpty();
            var isErrorInList = fieldNameErrors.Contains(error.FieldName);
            fieldNameErrors.Should().Contain(error.FieldName);
            error.ErrorMessage.Should().NotBeNullOrEmpty();
        }

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);

        await CheckHistoryRecordNotExists(
            detail.Id,
            HistoryAction.ClaimSubmitted
        );
    }

    [Fact]
    public async Task SubmitClaim_Electronic_ShouldUpdateStatusSuccessfully()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());
        var payer = new InsurancePayerFaker()
            .RuleFor(s => s.PayerId, providerInsurancePayer.PayerId)
            .RuleFor(s => s.Name, providerInsurancePayer.Name)
            .RuleFor(s => s.ClearingHouse, providerInsurancePayer.ClearingHouse)
            .RuleFor(s => s.Professional, PayerTransactionAvailability.Enrollment)
            .Generate();
        DataContext.InsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var uploadedClaim = new ClaimDtoFaker()
            .RuleFor(c => c.RemoteClaimId, Base36GuidEncoder.Encode(detail.Id))
            .Generate();
        var claimUploadResponse = new ClaimUploadResponseFaker([uploadedClaim])
            .Generate();
        claimMdHttpClientMock
            .Setup(x => x.UploadBatchFiles(It.IsAny<ClaimUploadRequest>()))
            .ReturnsAsync(claimUploadResponse);

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Amount.Should().Be(detail.Amount);
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Submitted);
        dbClaim.ClaimHeader.StatusReason.Should().BeNullOrEmpty();
        dbClaim.ClaimHeader.SubmissionMethod.Should().Be(ClaimSubmissionMethod.Electronic);
        dbClaim.ClaimHeader.LastSubmittedDateTimeUtc.Should().NotBeNull();

        var dbClaimMetadata = await DataContext.InsuranceClaimClearingHouseMetadata.AsNoTracking()
            .FirstOrDefaultAsync(x => x.InsuranceClaimId == detail.Id);
        dbClaimMetadata.Should().NotBeNull();
        dbClaimMetadata.PayerClaimId.Should().Be(uploadedClaim.SenderICN);
        dbClaimMetadata.ClearingHouse.Should().Be(ClearingHouseType.ManagedClaimMd);
        dbClaimMetadata.PayerId.Should().Be(uploadedClaim.PayerId);
        dbClaimMetadata.ClearingHouseClaimId.Should().Be(uploadedClaim.ClaimMdId);
        dbClaimMetadata.ProviderId.Should().Be(provider.Id);
        dbClaimMetadata.InsuranceClaimId.Should().Be(detail.Id);

        claimMdHttpClientMock.Verify(
            x => x.UploadBatchFiles(It.IsAny<ClaimUploadRequest>()),
            Times.AtLeastOnce
        );

        var historyDetail = ClaimHistoryModel.Create(detail);
        historyDetail.Status = ClaimStatus.Submitted;
        historyDetail.SubmissionMethod = ClaimSubmissionMethod.Electronic;
        await CheckHistoryRecordExists(
            detail.Id,
            HistoryAction.ClaimSubmitted,
            new ClaimSubmittedHistoryActionDetail(
                historyDetail,
                ClaimPayerHistoryModel.Create(detail),
                new ClaimHistoryMetadataReferences(
                    dbClaimMetadata.ClearingHouseClaimId,
                    dbClaimMetadata.PayerClaimId
                )
            )
        );
    }

    [Fact]
    public async Task SubmitClaim_Electronic_ShouldReturnErrorIfPayerDoesNotSupportClaims()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());
        var payer = new InsurancePayerFaker()
            .RuleFor(s => s.PayerId, providerInsurancePayer.PayerId)
            .RuleFor(s => s.Name, providerInsurancePayer.Name)
            .RuleFor(s => s.ClearingHouse, providerInsurancePayer.ClearingHouse)
            .RuleFor(s => s.Professional, PayerTransactionAvailability.False)
            .Generate();
        DataContext.InsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithBearerAuthorization(token)
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.ClaimNotValidForElectronicSubmissionCode);
        validationError.Details.Should().Be(Errors.ClaimNotValidForElectronicSubmissionDetails);

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);

        await CheckHistoryRecordNotExists(
            detail.Id,
            HistoryAction.ClaimSubmitted
        );
    }

    [Fact]
    public async Task SubmitClaim_Electronic_ShouldReturnErrorIfPayerRecordDoesNotExist()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithBearerAuthorization(token)
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.PayerInvalidCode);
        validationError.Details.Should().Be(Errors.PayerInvalidDetail);

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);

        await CheckHistoryRecordNotExists(
            detail.Id,
            HistoryAction.ClaimSubmitted
        );
    }

    [Fact]
    public async Task SubmitClaim_Electronic_ShouldReturnErrorIfPayerIdMissing()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());
        var payer = new InsurancePayerFaker()
            .RuleFor(s => s.PayerId, providerInsurancePayer.PayerId)
            .RuleFor(s => s.Name, providerInsurancePayer.Name)
            .RuleFor(s => s.ClearingHouse, providerInsurancePayer.ClearingHouse)
            .RuleFor(s => s.Professional, PayerTransactionAvailability.False)
            .Generate();
        DataContext.InsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var claimPolicy = new ClaimContactInsurancePolicyFaker(provider.Id, contact.Id, policy)
            .RuleFor(x => x.PayerId, x => null)
            .Generate();

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(claimPolicy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithBearerAuthorization(token)
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.PayerRequiredCode);
        validationError.Details.Should().Be(Errors.PayerRequiredDetail);

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);

        await CheckHistoryRecordNotExists(
            detail.Id,
            HistoryAction.ClaimSubmitted
        );
    }

    [Fact]
    public async Task SubmitClaim_Electronic_ShouldReturnErrorIfNPIMissing()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());
        var payer = new InsurancePayerFaker()
            .RuleFor(s => s.PayerId, providerInsurancePayer.PayerId)
            .RuleFor(s => s.Name, providerInsurancePayer.Name)
            .RuleFor(s => s.ClearingHouse, providerInsurancePayer.ClearingHouse)
            .RuleFor(s => s.Professional, PayerTransactionAvailability.False)
            .Generate();
        DataContext.InsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var claimBillingDetail = new ClaimBillingDetailFaker(provider.Id, defaultBillingProfile)
            .RuleFor(x => x.NationalProviderId, x => null)
            .Generate();

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(claimBillingDetail)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithBearerAuthorization(token)
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.BillingProfilesNationalProviderIdRequiredCode);
        validationError.Details.Should().Be(Errors.BillingProfilesNationalProviderIdRequiredDetail);

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);

        await CheckHistoryRecordNotExists(
            detail.Id,
            HistoryAction.ClaimSubmitted
        );
    }

    [Fact]
    public async Task SubmitClaim_Electronic_ShouldReturnErrorIfTaxNumberMissing()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());
        var payer = new InsurancePayerFaker()
            .RuleFor(s => s.PayerId, providerInsurancePayer.PayerId)
            .RuleFor(s => s.Name, providerInsurancePayer.Name)
            .RuleFor(s => s.ClearingHouse, providerInsurancePayer.ClearingHouse)
            .RuleFor(s => s.Professional, PayerTransactionAvailability.False)
            .Generate();
        DataContext.InsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var claimBillingDetail = new ClaimBillingDetailFaker(provider.Id, defaultBillingProfile)
            .RuleFor(x => x.TaxNumber, x => null)
            .Generate();

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(claimBillingDetail)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithBearerAuthorization(token)
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.BillingProfilesTaxNumberRequiredCode);
        validationError.Details.Should().Be(Errors.BillingProfilesTaxNumberRequiredDetail);

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);

        await CheckHistoryRecordNotExists(
            detail.Id,
            HistoryAction.ClaimSubmitted
        );
    }

    [Fact]
    public async Task SubmitClaim_Electronic_ShouldReturnErrorIfUploadHasErrorMessages()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());
        var payer = new InsurancePayerFaker()
            .RuleFor(s => s.PayerId, providerInsurancePayer.PayerId)
            .RuleFor(s => s.Name, providerInsurancePayer.Name)
            .RuleFor(s => s.ClearingHouse, providerInsurancePayer.ClearingHouse)
            .RuleFor(s => s.Professional, PayerTransactionAvailability.Enrollment)
            .Generate();
        DataContext.InsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var errorMessage = new ClaimMessageDtoFaker().RuleFor(s => s.Status, "R").Generate();
        var uploadedClaim = new ClaimDtoFaker()
            .RuleFor(c => c.RemoteClaimId, f => Base36GuidEncoder.Encode(detail.Id))
            .RuleFor(c => c.Messages, c => [errorMessage])
            .Generate();
        var claimUploadResponse = new ClaimUploadResponseFaker([uploadedClaim])
            .Generate();
        claimMdHttpClientMock
            .Setup(x => x.UploadBatchFiles(It.IsAny<ClaimUploadRequest>()))
            .ReturnsAsync(claimUploadResponse);

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.InsuranceClaimFailedToValidateCode);
        validationError.Details.Should().Be(Errors.InsuranceClaimFailedToValidateDetails);
        var errors = JsonConvert.DeserializeObject<FieldValidationError[]>(validationError.AdditionalDataJson);
        errors.Should().NotBeNull();
        errors.Length.Should().Be(1);

        foreach (var error in errors)
        {
            error.Should().NotBeNull();
            error.FieldName.Should().BeEmpty();
            error.ErrorMessage.Should().Be(errorMessage.Content);
        }

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);

        var dbClaimErrors = await DataContext.InsuranceClaimErrors.AsNoTracking()
            .Where(x => x.InsuranceClaimId == detail.Id)
            .ToListAsync();
        dbClaimErrors.Should().NotBeNull();
        dbClaimErrors.Count.Should().Be(1);
        dbClaimErrors[0].Message.Should().Be(errorMessage.Content);
        dbClaimErrors[0].Field.Should().BeEmpty();
        dbClaimErrors[0].Status.Should().Be(ClaimErrorStatus.Active);

        await CheckHistoryRecordNotExists(
            detail.Id,
            HistoryAction.ClaimSubmitted
        );
    }

    [Fact]
    public async Task SubmitClaim_Electronic_ShouldReturnErrorIfUploadHasApiErrors()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());
        var payer = new InsurancePayerFaker()
            .RuleFor(s => s.PayerId, providerInsurancePayer.PayerId)
            .RuleFor(s => s.Name, providerInsurancePayer.Name)
            .RuleFor(s => s.ClearingHouse, providerInsurancePayer.ClearingHouse)
            .RuleFor(s => s.Professional, PayerTransactionAvailability.Enrollment)
            .Generate();
        DataContext.InsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var errorMessage = faker.Lorem.Sentence();
        claimMdHttpClientMock
            .Setup(x => x.UploadBatchFiles(It.IsAny<ClaimUploadRequest>()))
            .ThrowsAsync(new ExecutionException(
                new ValidationError(
                    Errors.ClearingHouseGeneralErrorCode,
                    errorMessage,
                    ValidationType.InternalServer
                )
            ));

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.InternalServerError);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.ClearingHouseGeneralErrorCode);
        validationError.Details.Should().Be(errorMessage);

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);

        await CheckHistoryRecordNotExists(
            detail.Id,
            HistoryAction.ClaimSubmitted
        );
    }

    [Fact]
    public async Task SubmitClaim_Electronic_ShouldReturnErrorIfSubmittedClaimNotFoundInClearingHouseResponse()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());
        var payer = new InsurancePayerFaker()
            .RuleFor(s => s.PayerId, providerInsurancePayer.PayerId)
            .RuleFor(s => s.Name, providerInsurancePayer.Name)
            .RuleFor(s => s.ClearingHouse, providerInsurancePayer.ClearingHouse)
            .RuleFor(s => s.Professional, PayerTransactionAvailability.Enrollment)
            .Generate();
        DataContext.InsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var claimUploadResponse = new ClaimUploadResponseFaker().Generate();
        claimMdHttpClientMock
            .Setup(x => x.UploadBatchFiles(It.IsAny<ClaimUploadRequest>()))
            .ReturnsAsync(claimUploadResponse);

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.InternalServerError);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be("Internal server error");

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);

        await CheckHistoryRecordNotExists(
            detail.Id,
            HistoryAction.ClaimSubmitted
        );
    }

    [Fact]
    public async Task SubmitClaim_Electronic_UpsertErrorMessagesIfClaimFailedToSubmit()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());
        var payer = new InsurancePayerFaker()
            .RuleFor(s => s.PayerId, providerInsurancePayer.PayerId)
            .RuleFor(s => s.Name, providerInsurancePayer.Name)
            .RuleFor(s => s.ClearingHouse, providerInsurancePayer.ClearingHouse)
            .RuleFor(s => s.Professional, PayerTransactionAvailability.Enrollment)
            .Generate();
        DataContext.InsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());

        var existingErrors = new InsuranceClaimErrorFaker(provider.Id, detail)
            .Generate(3);
        DataContext.InsuranceClaimErrors.AddRange(existingErrors.Select(s => s.ToDataModel()));

        await DataContext.SaveChangesAsync();

        var existingErrorMessage = new ClaimMessageDtoFaker()
            .RuleFor(s => s.Status, "R")
            .RuleFor(s => s.Content, existingErrors[0].Message)
            .Generate();
        var newErrorMessage = new ClaimMessageDtoFaker()
            .RuleFor(s => s.Status, "R")
            .Generate();
        var uploadedClaim = new ClaimDtoFaker()
            .RuleFor(c => c.RemoteClaimId, Base36GuidEncoder.Encode(detail.Id))
            .RuleFor(c => c.Messages, c => [existingErrorMessage, newErrorMessage])
            .Generate();
        var claimUploadResponse = new ClaimUploadResponseFaker([uploadedClaim])
            .Generate();
        claimMdHttpClientMock
            .Setup(x => x.UploadBatchFiles(It.IsAny<ClaimUploadRequest>()))
            .ReturnsAsync(claimUploadResponse);

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.InsuranceClaimFailedToValidateCode);
        validationError.Details.Should().Be(Errors.InsuranceClaimFailedToValidateDetails);
        var errors = JsonConvert.DeserializeObject<FieldValidationError[]>(validationError.AdditionalDataJson);
        errors.Should().NotBeNull();
        errors.Length.Should().Be(2);

        var dbClaimErrors = await DataContext.InsuranceClaimErrors.AsNoTracking()
            .Where(x => x.InsuranceClaimId == detail.Id)
            .ToListAsync();
        dbClaimErrors.Should().NotBeNull();
        dbClaimErrors.Count.Should().Be(4);
        dbClaimErrors.Where(s => s.Status == ClaimErrorStatus.Active).Count().Should().Be(2);
        dbClaimErrors.Where(s => s.Status == ClaimErrorStatus.Inactive).Count().Should().Be(2);

        await CheckHistoryRecordNotExists(
            detail.Id,
            HistoryAction.ClaimSubmitted
        );
    }

    [Fact]
    public async Task SubmitClaim_ShouldValidatePayerPhoneAndAddressIfManualSubmission()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        var memberContact = new ContactFaker(provider.Id)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var claimClient = new ClaimClientFaker(provider.Id, contact).Generate();
        var renderingProvider = new ClaimRenderingProviderFaker(provider.Id, staffMember).Generate();
        var referringProvider = new ClaimReferringProviderFaker(provider.Id).Generate();
        var facility = new ClaimFacilityFaker(provider.Id).Generate();
        var claimBillingDetail = new ClaimBillingDetailFaker(provider.Id, defaultBillingProfile).Generate();
        var serviceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate();
        var claimPolicy = new ClaimContactInsurancePolicyFaker(provider.Id, contact.Id, policy)
            .RuleFor(s => s.Address, x => null)
            .RuleFor(x => x.PayerPhoneNumberDetails, x => null)
            .Generate();

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(claimBillingDetail)
            .WithFacility(facility)
            .WithClient(claimClient)
            .WithContactInsurancePolicy(claimPolicy)
            .WithRenderingProviders([renderingProvider])
            .WithDiagnosticCodes(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders([referringProvider])
            .WithServiceLines([serviceLine])
            .RuleFor(x => x.SubmissionMethod, ClaimSubmissionMethod.Manual)
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Manual))
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.InsuranceClaimFailedToValidateCode);
        validationError.Details.Should().Be(Errors.InsuranceClaimFailedToValidateDetails);
        var errors = JsonConvert.DeserializeObject<FieldValidationError[]>(validationError.AdditionalDataJson);
        errors.Should().NotBeNull();
        errors.Length.Should().BeGreaterThan(0);

        var fieldNameErrors = new List<string>
        {
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.Address)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PayerPhoneNumberDetails)}",
        };

        foreach (var error in errors)
        {
            error.Should().NotBeNull();
            error.FieldName.Should().NotBeNullOrEmpty();
            var isErrorInList = fieldNameErrors.Contains(error.FieldName);
            fieldNameErrors.Should().Contain(error.FieldName);
            error.ErrorMessage.Should().NotBeNullOrEmpty();
        }

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);
    }

    [Fact]
    public async Task SubmitClaim_ShouldNotValidatePayerPhoneAndAddressIfElectronicSubmission()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        var memberContact = new ContactFaker(provider.Id)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());
        var payer = new InsurancePayerFaker()
            .RuleFor(s => s.PayerId, providerInsurancePayer.PayerId)
            .RuleFor(s => s.Name, providerInsurancePayer.Name)
            .RuleFor(s => s.ClearingHouse, providerInsurancePayer.ClearingHouse)
            .RuleFor(s => s.Professional, PayerTransactionAvailability.Enrollment)
            .Generate();
        DataContext.InsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var claimClient = new ClaimClientFaker(provider.Id, contact).Generate();
        var renderingProvider = new ClaimRenderingProviderFaker(provider.Id, staffMember).Generate();
        var referringProvider = new ClaimReferringProviderFaker(provider.Id).Generate();
        var facility = new ClaimFacilityFaker(provider.Id).Generate();
        var claimBillingDetail = new ClaimBillingDetailFaker(provider.Id, defaultBillingProfile).Generate();
        var serviceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate();
        var claimPolicy = new ClaimContactInsurancePolicyFaker(provider.Id, contact.Id, policy)
            .RuleFor(s => s.Address, x => null)
            .RuleFor(x => x.PayerPhoneNumberDetails, x => null)
            .Generate();

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(claimBillingDetail)
            .WithFacility(facility)
            .WithClient(claimClient)
            .WithContactInsurancePolicy(claimPolicy)
            .WithRenderingProviders([renderingProvider])
            .WithDiagnosticCodes(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders([referringProvider])
            .WithServiceLines([serviceLine])
            .RuleFor(x => x.SubmissionMethod, ClaimSubmissionMethod.Electronic)
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var uploadedClaim = new ClaimDtoFaker()
            .RuleFor(c => c.RemoteClaimId, Base36GuidEncoder.Encode(detail.Id))
            .Generate();
        var claimUploadResponse = new ClaimUploadResponseFaker([uploadedClaim])
            .Generate();
        claimMdHttpClientMock
            .Setup(x => x.UploadBatchFiles(It.IsAny<ClaimUploadRequest>()))
            .ReturnsAsync(claimUploadResponse);

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Submitted);
        dbClaim.ClaimHeader.StatusReason.Should().BeNullOrEmpty();
    }

    [Fact]
    public async Task SubmitElectronicClaim_WithRejectedStatus_ShouldClearAnyExistingErrors()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var providerInsurancePayer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(providerInsurancePayer.ToDataModel());
        var payer = new InsurancePayerFaker()
            .RuleFor(s => s.PayerId, providerInsurancePayer.PayerId)
            .RuleFor(s => s.Name, providerInsurancePayer.Name)
            .RuleFor(s => s.ClearingHouse, providerInsurancePayer.ClearingHouse)
            .RuleFor(s => s.Professional, PayerTransactionAvailability.Enrollment)
            .Generate();
        DataContext.InsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.Amount, 150)
            .RuleFor(x => x.Status, ClaimStatus.Rejected)
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());

        var existingErrors = new InsuranceClaimErrorFaker(provider.Id, detail)
            .Generate(3);
        DataContext.InsuranceClaimErrors.AddRange(existingErrors.Select(s => s.ToDataModel()));

        await DataContext.SaveChangesAsync();

        var uploadedClaim = new ClaimDtoFaker()
            .RuleFor(c => c.RemoteClaimId, Base36GuidEncoder.Encode(detail.Id))
            .Generate();
        var claimUploadResponse = new ClaimUploadResponseFaker([uploadedClaim])
            .Generate();
        claimMdHttpClientMock
            .Setup(x => x.UploadBatchFiles(It.IsAny<ClaimUploadRequest>()))
            .ReturnsAsync(claimUploadResponse);

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/submit"
            )
            .WithPayload(new SubmitUSProfessionalClaimRequest(ClaimSubmissionMethod.Electronic))
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Submitted);

        var dbClaimErrors = await DataContext.InsuranceClaimErrors.AsNoTracking()
            .Where(x => x.InsuranceClaimId == detail.Id)
            .ToListAsync();
        dbClaimErrors.Count.Should().Be(3);
        dbClaimErrors.Should().OnlyContain(x => x.Status == ClaimErrorStatus.Inactive);
    }
}
