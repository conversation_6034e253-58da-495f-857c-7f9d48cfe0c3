using System.Net;
using AutoBogus;
using Bogus;
using carepatron.api.Contracts.Requests.Insurance;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Pagination;
using carepatron.infra.sql.Models.Insurance;
using Microsoft.EntityFrameworkCore;
using tests.common.Data.Mappers;
using tests.component.Builders;

namespace tests.component.Features.Insurance;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class PayersTests(ComponentTestFixture fixture) : BaseTestClass(fixture)
{
    [Fact]
    public async Task GetPayers_ShouldFetchSuccessfully()
    {
        var payers = new ProviderInsurancePayerFaker(ProviderId)
            .Generate(5)
            .Select(x => x.ToDataModel())
            .ToList();

        DataContext.ProviderInsurancePayers.AddRange(payers);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get,
                $"api/providers/{ProviderId}/payers?offset=1&limit=2"
            )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ProviderInsurancePayer>>();
        result.Items.Count.Should().Be(2);
        result.Pagination.HasMore.Should().BeTrue();
        foreach (var item in result.Items)
        {
            var model = payers.FirstOrDefault(x => x.Id == item.Id);
            item.ProviderId.Should().Be(ProviderId);
            item.Address.Should().BeEquivalentTo(model.Address);
            item.CoverageType.Should().Be(model.CoverageType);
            item.Name.Should().Be(model.Name);
            item.PhoneNumber.Should().Be(model.PhoneNumber);
            item.OtherCoverageTypeName.Should().Be(model.OtherCoverageTypeName);
            item.PayerId.Should().Be(model.PayerId);
        }
    }

    [Fact]
    public async Task Get_ProviderInsurancePayer_ShouldFetchSuccessfully()
    {
        var payer = new ProviderInsurancePayerFaker(ProviderId).Generate().ToDataModel();
        DataContext.ProviderInsurancePayers.Add(payer);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get,
                $"api/providers/{ProviderId}/payers/{payer.Id}"
            )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<ProviderInsurancePayer>();
        result.Should().NotBeNull();
        result.Should().BeOfType<ProviderInsurancePayer>();
        result.Address.Should().BeEquivalentTo(payer.Address);
        result.CoverageType.Should().Be(payer.CoverageType);
        result.Name.Should().Be(payer.Name);
        result.PhoneNumber.Should().Be(payer.PhoneNumber);
        result.OtherCoverageTypeName.Should().Be(payer.OtherCoverageTypeName);
        result.PayerId.Should().Be(payer.PayerId);
        result.Id.Should().Be(payer.Id);
    }

    [Fact]
    public async Task Create_ProviderInsurancePayer_ShouldCreateSuccessfully()
    {
        var payer = new ProviderInsurancePayerFaker(ProviderId)
            .RuleFor(x => x.CoverageType, InsuranceCoverageType.Medicare)
            .Generate();

        var payerRequest = new SaveInsurancePayerRequest
        {
            PayerId = payer.PayerId,
            Name = payer.Name,
            CoverageType = payer.CoverageType,
            Address = payer.Address,
            PhoneNumber = payer.PhoneNumber,
            OtherCoverageTypeName = payer.OtherCoverageTypeName
        };

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post,
                $"api/providers/{ProviderId}/payers"
            )
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payerRequest)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<ProviderInsurancePayer>();

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        result.PayerId.Should().Be(payerRequest.PayerId);
        result.Name.Should().Be(payerRequest.Name);
        result.CoverageType.Should().Be(payerRequest.CoverageType);
        result.PhoneNumber.Should().Be(payerRequest.PhoneNumber);
        result.Address.Should().BeEquivalentTo(payerRequest.Address);
        result.OtherCoverageTypeName.Should().BeNullOrEmpty();
    }

    [Fact]
    public async Task Create_ProviderInsurancePayer_WithOtherCoverage_ShouldCreateSuccessfullyWithOtherCoverageTypeName()
    {
        var payer = new ProviderInsurancePayerFaker(ProviderId)
            .RuleFor(x => x.CoverageType, InsuranceCoverageType.Other)
            .Generate();

        var payerRequest = new SaveInsurancePayerRequest
        {
            PayerId = payer.PayerId,
            Name = payer.Name,
            CoverageType = payer.CoverageType,
            Address = payer.Address,
            PhoneNumber = payer.PhoneNumber,
            OtherCoverageTypeName = payer.OtherCoverageTypeName
        };

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post,
                $"api/providers/{ProviderId}/payers"
            )
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payerRequest)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<ProviderInsurancePayer>();

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        result.PayerId.Should().Be(payerRequest.PayerId);
        result.Name.Should().Be(payerRequest.Name);
        result.CoverageType.Should().Be(payerRequest.CoverageType);
        result.PhoneNumber.Should().Be(payerRequest.PhoneNumber);
        result.Address.Should().BeEquivalentTo(payerRequest.Address);
        result.OtherCoverageTypeName.Should().Be(payerRequest.OtherCoverageTypeName);
    }

    [Theory]
    [InlineData("", "Test Name")]
    [InlineData("Test ID", "")]
    public async Task Create_ProviderInsurancePayer_ShouldFailValidation(
        string payerId,
        string name
    )
    {
        var payerRequest = new SaveInsurancePayerRequest
        {
            PayerId = payerId,
            Name = name,
            CoverageType = InsuranceCoverageType.Medicare,
        };

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post,
                $"api/providers/{ProviderId}/payers"
            )
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payerRequest)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task Update_ProviderInsurancePayer_ShouldUpdateSuccessfully()
    {
        var payer = new ProviderInsurancePayerFaker(ProviderId)
            .RuleFor(x => x.CoverageType, InsuranceCoverageType.Medicare)
            .Generate()
            .ToDataModel();
        DataContext.ProviderInsurancePayers.Add(payer);
        await DataContext.SaveChangesAsync();

        var payerRequest = new SaveInsurancePayerRequest
        {
            PayerId = "123",
            Name = "New Name",
            CoverageType = InsuranceCoverageType.Medicare,
            Address = payer.Address,
            PhoneNumber = "**********",
            OtherCoverageTypeName = payer.OtherCoverageTypeName
        };

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put,
                $"api/providers/{ProviderId}/payers/{payer.Id}"
            )
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payerRequest)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<ProviderInsurancePayer>();

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        result.PayerId.Should().Be(payerRequest.PayerId);
        result.Name.Should().Be(payerRequest.Name);
        result.CoverageType.Should().Be(payerRequest.CoverageType);
        result.Address.Should().BeEquivalentTo(payerRequest.Address);
        result.PhoneNumber.Should().Be(payerRequest.PhoneNumber);
        result.OtherCoverageTypeName.Should().BeNullOrEmpty();
    }

    [Fact]
    public async Task Update_ProviderInsurancePayer_WithOtherCoverage_ShouldUpdateSuccessfullyWithOtherCoverageTypeName()
    {
        var payer = new ProviderInsurancePayerFaker(ProviderId)
            .RuleFor(x => x.CoverageType, InsuranceCoverageType.Other)
            .Generate()
            .ToDataModel();
        DataContext.ProviderInsurancePayers.Add(payer);
        await DataContext.SaveChangesAsync();

        var payerRequest = new SaveInsurancePayerRequest
        {
            PayerId = "123",
            Name = "New Name",
            CoverageType = InsuranceCoverageType.Other,
            Address = payer.Address,
            PhoneNumber = "**********",
            OtherCoverageTypeName = "Other Coverage"
        };

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put,
                $"api/providers/{ProviderId}/payers/{payer.Id}"
            )
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payerRequest)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<ProviderInsurancePayer>();

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        result.PayerId.Should().Be(payerRequest.PayerId);
        result.Name.Should().Be(payerRequest.Name);
        result.CoverageType.Should().Be(payerRequest.CoverageType);
        result.Address.Should().BeEquivalentTo(payerRequest.Address);
        result.PhoneNumber.Should().Be(payerRequest.PhoneNumber);
        result.OtherCoverageTypeName.Should().Be(payerRequest.OtherCoverageTypeName);
    }

    [Theory]
    [InlineData("", "Test Name")]
    [InlineData("Test ID", "")]
    public async Task Update_ProviderInsurancePayer_ShouldFailValidation(
        string payerId,
        string name
    )
    {
        var payer = new ProviderInsurancePayerFaker(ProviderId).Generate().ToDataModel();
        DataContext.ProviderInsurancePayers.Add(payer);
        await DataContext.SaveChangesAsync();

        var payerRequest = new SaveInsurancePayerRequest
        {
            PayerId = payerId,
            Name = name,
            CoverageType = InsuranceCoverageType.Medicare,
            PhoneNumber = "**********"
        };

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put,
                $"api/providers/{ProviderId}/payers/{payer.Id}"
            )
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payerRequest)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    [Fact]
    public async Task Update_ProviderInsurancePayer_ShouldFailIfPayerNotFound()
    {
        var payer = new ProviderInsurancePayerFaker(ProviderId).Generate();

        var payerRequest = new SaveInsurancePayerRequest
        {
            PayerId = payer.PayerId,
            Name = payer.Name,
            CoverageType = payer.CoverageType,
            PhoneNumber = "**********"
        };

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put,
                $"api/providers/{ProviderId}/payers/{payer.Id}"
            )
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payerRequest)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<ValidationError>();

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        result
            .Should()
            .BeEquivalentTo(
                new
                {
                    Code = Errors.PayerNotFoundCode,
                    Details = Errors.PayerNotFoundDetail,
                    ValidationType = ValidationType.NotFound
                },
                opt => opt.ExcludingMissingMembers()
            );
    }

    [Fact]
    public async Task DeletePayer_ShouldDeleteSuccessfully()
    {
        var payer = new ProviderInsurancePayerFaker(ProviderId).Generate().ToDataModel();
        DataContext.ProviderInsurancePayers.AddRange(payer);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Delete,
                $"api/providers/{ProviderId}/payers/{payer.Id}"
            )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);

        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var deletedPayer = await DataContext.ProviderInsurancePayers
            .AsNoTracking()
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == payer.Id);
        deletedPayer.DeletedAtUtc.Should().NotBeNull();
    }

    [Fact]
    public async Task GetAvailablePayers_Without_Filters_ShouldFetchSuccessfully()
    {
        var payers = CreateAvailablePayerDataModels(10);
        await DataContext.InsurancePayers.AddRangeAsync(payers);

        var importedPayer = CreateAvailablePayerDataModel();
        await DataContext.InsurancePayers.AddAsync(importedPayer);

        var providerPayer = new ProviderInsurancePayerFaker(ProviderId)
            .ForPayer(importedPayer)
            .Generate()
            .ToDataModel();

        await DataContext.AddAsync(providerPayer);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get,
                $"api/providers/{ProviderId}/payers/available"
            )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<AvailableInsurancePayer>>();
        result.Should().NotBeNull();
        result.Items.Should().NotBeNullOrEmpty();
        result.Items.All(x => x.PayerId == importedPayer.PayerId).Should().BeFalse();

        // Verify AddedToWorkspace property - should be false for payers not added to workspace
        result.Items.Where(x => x.PayerId != importedPayer.PayerId).Should().AllSatisfy(x => x.AddedToWorkspace.Should().BeFalse());
    }


    [Fact]
    public async Task GetAvailablePayers_ShouldFetchSuccessfully()
    {
        var payers = CreateAvailablePayerDataModels(10);
        await DataContext.InsurancePayers.AddRangeAsync(payers);

        var payerState = payers.Take(2).SelectMany(x => x.States).ToArray();
        var searchTerm = payers.First().PayerId[0..5];

        var importedPayer = new AutoFaker<InsurancePayerDataModel>()
            .RuleFor(x => x.PayerId, f => f.Random.AlphaNumeric(5) + searchTerm)
            .RuleFor(x => x.States, f => [f.Address.StateAbbr(), f.Address.StateAbbr()])
            .RuleFor(x => x.Professional, PayerTransactionAvailability.True)
            .RuleFor(x => x.ClearingHouse, ClearingHouseType.ManagedClaimMd)
            .Generate();
        await DataContext.InsurancePayers.AddAsync(importedPayer);


        var providerPayer = new ProviderInsurancePayerFaker(ProviderId)
            .ForPayer(importedPayer)
            .Generate()
            .ToDataModel();
        await DataContext.AddAsync(providerPayer);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get,
                $"api/providers/{ProviderId}/payers/available?state={payerState[0]}&state={payerState[1]}&searchTerm={searchTerm}"
            )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<AvailableInsurancePayer>>();
        result.Should().NotBeNull();
        result.Items.Should().NotBeNullOrEmpty();
        result.Items.All(x => x.Name.Contains(searchTerm) || x.PayerId.Contains(searchTerm)).Should().BeTrue();
        result.Items.All(x => x.States.Contains(payerState[0]) || x.States.Contains(payerState[1])).Should().BeTrue();
        result.Items.All(x => x.PayerId == importedPayer.PayerId).Should().BeFalse();

        // Verify AddedToWorkspace property - should be false for available payers not added to workspace
        result.Items.Where(x => x.PayerId != importedPayer.PayerId).Should().AllSatisfy(x => x.AddedToWorkspace.Should().BeFalse());
    }

    [Fact]
    public async Task GetAvailablePayers_ShouldExcludeNotAvailableTransactionPayer()
    {
        var payerPrefix = "TESTPAYER";
        var payers = new AutoFaker<InsurancePayerDataModel>()
            .RuleFor(x => x.PayerId, f => payerPrefix + f.Random.AlphaNumeric(5))
            .RuleFor(x => x.States, f => [f.Address.StateAbbr(), f.Address.StateAbbr()])
            .RuleFor(x => x.ERA, PayerTransactionAvailability.False)
            .RuleFor(x => x.Eligibility, PayerTransactionAvailability.False)
            .RuleFor(x => x.Professional, PayerTransactionAvailability.False)
            .Generate(10)
            .ToList();
        var payers2 = new AutoFaker<InsurancePayerDataModel>()
            .RuleFor(x => x.PayerId, f => payerPrefix + f.Random.AlphaNumeric(10))
            .RuleFor(x => x.States, f => [f.Address.StateAbbr(), f.Address.StateAbbr()])
            .RuleFor(x => x.ERA, PayerTransactionAvailability.True)
            .Generate(5)
            .ToList();
        await DataContext.InsurancePayers.AddRangeAsync(payers);
        await DataContext.InsurancePayers.AddRangeAsync(payers2);

        var searchTerm = payers.First().PayerId[0..5];

        var importedPayer = CreateAvailablePayerDataModel(searchTerm);
        await DataContext.InsurancePayers.AddAsync(importedPayer);


        var providerPayer = new ProviderInsurancePayerFaker(ProviderId)
            .ForPayer(importedPayer)
            .Generate()
            .ToDataModel();
        await DataContext.AddAsync(providerPayer);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get,
                $"api/providers/{ProviderId}/payers/available?searchTerm={searchTerm}"
            )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsurancePayer>>();
        result.Items.Should().AllSatisfy(item =>
        {
            (item.ERA != PayerTransactionAvailability.False
             || item.Eligibility != PayerTransactionAvailability.False
             || item.Professional != PayerTransactionAvailability.True)
                .Should()
                .BeTrue();
        });
    }

    [Fact]
    public async Task GetAvailablePayers_AlreadyImportedPayer_IsAddedToWorkspaces()
    {
        var payers = CreateAvailablePayerDataModels(1);
        await DataContext.InsurancePayers.AddRangeAsync(payers);

        var searchTerm = payers.First().PayerId[0..5];

        var importedPayer = CreateAvailablePayerDataModel(searchTerm);

        await DataContext.InsurancePayers.AddAsync(importedPayer);

        var providerPayer = new ProviderInsurancePayerFaker(ProviderId)
            .ForPayer(importedPayer)
            .Generate()
            .ToDataModel();
        await DataContext.AddAsync(providerPayer);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get,
                $"api/providers/{ProviderId}/payers/available?searchTerm={searchTerm}"
            )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<AvailableInsurancePayer>>();
        result.Items.First(x => x.PayerId == importedPayer.PayerId).AddedToWorkspace.Should().BeTrue();
    }

    [Fact]
    public async Task GetAvailablePayers_FiltersByNumber()
    {
        var payers = CreateAvailablePayerDataModels(1);
        await DataContext.InsurancePayers.AddRangeAsync(payers);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get,
                $"api/providers/{ProviderId}/payers/available?searchTerm={payers.First().PayerId}"
            )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<AvailableInsurancePayer>>();
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(1);
        var payer = result.Items.First();
        var dbPayer = await DataContext.InsurancePayers
            .AsNoTracking()
            .IgnoreQueryFilters()
            .FirstAsync(x => x.PayerId == payer.PayerId);
        payer.Should().BeEquivalentTo(new
        {
            PayerId = dbPayer.PayerId,
            ClearingHouse = dbPayer.ClearingHouse,
            Name = dbPayer.Name,
            PayerType = dbPayer.PayerType,
            States = dbPayer.States,
            PhoneNumber = dbPayer.PhoneNumber,
            Address = dbPayer.Address,
            Eligibility = dbPayer.Eligibility,
            Professional = dbPayer.Professional,
            ERA = dbPayer.ERA,
            Attachment = dbPayer.Attachment,
            AddedToWorkspace = false // Should be false since this payer is not added to workspace
        });
    }

    [Fact]
    public async Task GetAvailablePayers_FiltersByName()
    {
        var payers = CreateAvailablePayerDataModels(1);
        await DataContext.InsurancePayers.AddRangeAsync(payers);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get,
                $"api/providers/{ProviderId}/payers/available?searchTerm={payers.First().Name.Substring(2, 10)}"
            )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<AvailableInsurancePayer>>();
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(1);
        var payer = result.Items.First();
        var dbPayer = await DataContext.InsurancePayers
            .AsNoTracking()
            .IgnoreQueryFilters()
            .FirstAsync(x => x.PayerId == payer.PayerId);
        payer.Should().BeEquivalentTo(new
        {
            PayerId = dbPayer.PayerId,
            ClearingHouse = dbPayer.ClearingHouse,
            Name = dbPayer.Name,
            PayerType = dbPayer.PayerType,
            States = dbPayer.States,
            PhoneNumber = dbPayer.PhoneNumber,
            Address = dbPayer.Address,
            Eligibility = dbPayer.Eligibility,
            Professional = dbPayer.Professional,
            ERA = dbPayer.ERA,
            Attachment = dbPayer.Attachment,
            AddedToWorkspace = false // Should be false since this payer is not added to workspace
        });
    }

    [Fact]
    public async Task ImportPayers_ShouldImportSuccessfully()
    {
        var payers = CreateAvailablePayerDataModels(10);

        await DataContext.InsurancePayers.AddRangeAsync(payers);
        await DataContext.SaveChangesAsync();

        var payload = payers.Select(x => new ClearingHousePayer
        {
            ClearingHouse = x.ClearingHouse,
            PayerId = x.PayerId
        });

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post,
                $"api/providers/{ProviderId}/payers/import"
            )
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(new ImportInsurancePayersRequest { Payers = payload.ToArray() })
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);


        var dbPayers = await DataContext.ProviderInsurancePayers
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();
        dbPayers.Should().NotBeNull();
        dbPayers.Should().HaveCount(payers.Count);
        dbPayers.Select(x => x.PayerId).Should().BeEquivalentTo(payers.Select(p => p.PayerId));
        dbPayers.Select(x => x.ClearingHouse).Should().BeEquivalentTo(payers.Select(p => p.ClearingHouse));
        dbPayers.Select(x => x.ProviderId).Should().AllBeEquivalentTo(ProviderId);
        dbPayers.Select(x => x.Name).Should().BeEquivalentTo(payers.Select(x => x.Name));

        var result = await response.Content.ReadAsAsync<ProviderInsurancePayer[]>();
        result.Should().BeEquivalentTo(dbPayers.Select(x => new
        {
            Id = x.Id,
            ProviderId = ProviderId,
            PayerId = x.PayerId,
            Name = x.Name,
            ClearingHouse = x.ClearingHouse,
            PhoneNumber = x.PhoneNumber,
            Address = x.Address,
            CoverageType = x.CoverageType,
            OtherCoverageTypeName = x.OtherCoverageTypeName
        }));
    }

    [Fact]
    public async Task ImportPayers_WithExistingPayer_ShouldImportSuccessfully()
    {
        var payers = CreateAvailablePayerDataModels(10);

        await DataContext.InsurancePayers.AddRangeAsync(payers);

        var providerPayer = new ProviderInsurancePayerFaker(ProviderId)
            .ForPayer(payers.First())
            .Generate()
            .ToDataModel();
        await DataContext.ProviderInsurancePayers.AddAsync(providerPayer);

        await DataContext.SaveChangesAsync();

        var payload = payers.Select(x => new ClearingHousePayer
        {
            ClearingHouse = x.ClearingHouse,
            PayerId = x.PayerId
        });

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post,
                $"api/providers/{ProviderId}/payers/import"
            )
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(new ImportInsurancePayersRequest { Payers = payload.ToArray() })
            .Create();


        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);


        var dbPayers = await DataContext.ProviderInsurancePayers
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();
        dbPayers.Should().NotBeNull();
        dbPayers.Should().HaveCount(payers.Count);
        dbPayers.Select(x => x.PayerId).Should().BeEquivalentTo(payers.Select(p => p.PayerId));
        dbPayers.Select(x => x.ClearingHouse).Should().BeEquivalentTo(payers.Select(p => p.ClearingHouse));
        dbPayers.Select(x => x.ProviderId).Should().AllBeEquivalentTo(ProviderId);
        dbPayers.Select(x => x.Name).Should().BeEquivalentTo(payers.Select(x => x.Name));
    }

    [Fact]
    public async Task GetAvailablePayers_ShouldPopulateAddedToWorkspaceCorrectly()
    {
        // Arrange: Create some insurance payers
        var faker = new Faker();
        var prefix = faker.Random.AlphaNumeric(5);
        var availablePayers = CreateAvailablePayerDataModels(3, prefix);

        await DataContext.InsurancePayers.AddRangeAsync(availablePayers);

        // Add one of the payers to the provider's workspace
        var addedPayer = availablePayers.First();
        var providerPayer = new ProviderInsurancePayerFaker(ProviderId)
            .ForPayer(addedPayer)
            .Generate()
            .ToDataModel();
        await DataContext.ProviderInsurancePayers.AddAsync(providerPayer);
        await DataContext.SaveChangesAsync();

        // Act: Get available payers
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get,
                $"api/providers/{ProviderId}/payers/available?searchTerm={prefix}"
            )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<AvailableInsurancePayer>>();

        // Assert: Verify AddedToWorkspace property is correctly populated
        result.Should().NotBeNull();
        result.Items.Should().NotBeEmpty();

        // The payer that was added to workspace should have AddedToWorkspace = true
        var payerInWorkspace = result.Items.FirstOrDefault(x => x.PayerId == addedPayer.PayerId);
        payerInWorkspace.Should().NotBeNull();
        payerInWorkspace.AddedToWorkspace.Should().BeTrue();

        // Other payers should have AddedToWorkspace = false
        var payersNotInWorkspace = result.Items.Where(x => x.PayerId != addedPayer.PayerId);
        payersNotInWorkspace.Should().AllSatisfy(x => x.AddedToWorkspace.Should().BeFalse());
    }

    private InsurancePayerDataModel CreateAvailablePayerDataModel(string prefix = null) => CreateAvailablePayerDataModels(1, prefix).Single();

    private IList<InsurancePayerDataModel> CreateAvailablePayerDataModels(int count = 1, string prefix = null)
    {
        return new AutoFaker<InsurancePayerDataModel>()
            .RuleFor(x => x.PayerId, f => prefix is not null ? prefix + f.Random.AlphaNumeric(5) : f.Random.AlphaNumeric(10))
            .RuleFor(x => x.Name, f => f.Random.AlphaNumeric(20))
            .RuleFor(x => x.States, f => [f.Address.StateAbbr(), f.Address.StateAbbr()])
            .RuleFor(x => x.ERA, PayerTransactionAvailability.True)
            .RuleFor(x => x.Eligibility, PayerTransactionAvailability.True)
            .RuleFor(x => x.Professional, PayerTransactionAvailability.True)
            .RuleFor(x => x.ClearingHouse, ClearingHouseType.ManagedClaimMd)
            .Generate(count)
            .ToList();
    }

    [Fact]
    public async Task ImportPayers_WithExistingPayer_WithSamePayerID_ShouldUpdateData()
    {
        var payers = new AutoFaker<InsurancePayerDataModel>()
            .RuleFor(x => x.PayerId, f => f.Random.AlphaNumeric(10))
            .RuleFor(x => x.PayerType, "test payer type")
            .RuleFor(x => x.ClearingHouse, ClearingHouseType.ManagedClaimMd)
            .Generate(2)
            .ToArray();

        await DataContext.InsurancePayers.AddRangeAsync(payers);
        var providerPayer = new ProviderInsurancePayerFaker(ProviderId)
            .RuleFor(x => x.PayerId, f => payers.First().PayerId)
            .RuleFor(x => x.ClearingHouse, ClearingHouseType.None)
            .RuleFor(x => x.CoverageType, InsuranceCoverageType.Medicare)
            .Generate()
            .ToDataModel();
        await DataContext.ProviderInsurancePayers.AddAsync(providerPayer);
        await DataContext.SaveChangesAsync();

        var payload = payers.Select(x => new ClearingHousePayer
        {
            ClearingHouse = x.ClearingHouse,
            PayerId = x.PayerId
        });

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post,
                $"api/providers/{ProviderId}/payers/import"
            )
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(new ImportInsurancePayersRequest { Payers = payload.ToArray() })
            .Create();


        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);


        var dbPayers = await DataContext.ProviderInsurancePayers
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();
        dbPayers.Should().NotBeNull();
        dbPayers.Should().HaveCount(payers.Length);
        var updatedPayer = dbPayers.FirstOrDefault(x => x.PayerId == providerPayer.PayerId);
        updatedPayer.Should().NotBeNull();
        updatedPayer.Id.Should().Be(providerPayer.Id);
        updatedPayer.Address.ToString().Should().Be(providerPayer.Address.ToString());
        updatedPayer.PhoneNumber.Should().Be(providerPayer.PhoneNumber);
        updatedPayer.Name.Should().Be(payers.First().Name);
        updatedPayer.ClearingHouse.Should().Be(payers.First().ClearingHouse);
        updatedPayer.CoverageType.Should().Be(Enum.TryParse<InsuranceCoverageType>(payers.First().PayerType, out var payerType) ? payerType : InsuranceCoverageType.Other);
        updatedPayer.OtherCoverageTypeName.Should().Be(payers.First().PayerType);
    }


    [Fact]
    public async Task ImportPayers_WithExistingPayer_WithSamePayerID_WithClearingHousePayer_ShouldNotUpdateData()
    {
        var payers = new AutoFaker<InsurancePayerDataModel>()
            .RuleFor(x => x.PayerId, f => f.Random.AlphaNumeric(10))
            .RuleFor(x => x.ClearingHouse, ClearingHouseType.ManagedClaimMd)
            .Generate(2)
            .ToArray();

        await DataContext.InsurancePayers.AddRangeAsync(payers);
        var providerPayer = new ProviderInsurancePayerFaker(ProviderId)
            .RuleFor(x => x.PayerId, f => payers.First().PayerId)
            .Generate()
            .ToDataModel();
        await DataContext.ProviderInsurancePayers.AddAsync(providerPayer);
        await DataContext.SaveChangesAsync();

        var payload = payers.Select(x => new ClearingHousePayer
        {
            ClearingHouse = x.ClearingHouse,
            PayerId = x.PayerId
        });

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post,
                $"api/providers/{ProviderId}/payers/import"
            )
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(new ImportInsurancePayersRequest { Payers = payload.ToArray() })
            .Create();


        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);


        var dbPayers = await DataContext.ProviderInsurancePayers
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();
        dbPayers.Should().NotBeNull();
        dbPayers.Should().HaveCount(payers.Length);
        var updatedPayer = dbPayers.FirstOrDefault(x => x.PayerId == providerPayer.PayerId);
        updatedPayer.Should().NotBeNull();
        updatedPayer.Id.Should().Be(providerPayer.Id);
        updatedPayer.Address.ToString().Should().Be(providerPayer.Address.ToString());
        updatedPayer.PhoneNumber.Should().Be(providerPayer.PhoneNumber);
        updatedPayer.Name.Should().Be(providerPayer.Name);
        updatedPayer.ClearingHouse.Should().Be(providerPayer.ClearingHouse);
        updatedPayer.CoverageType.Should().Be(providerPayer.CoverageType);
        updatedPayer.OtherCoverageTypeName.Should().Be(providerPayer.OtherCoverageTypeName);
    }
}