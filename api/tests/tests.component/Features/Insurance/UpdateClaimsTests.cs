using System.Net;
using Bogus;
using carepatron.core.Application.Insurance.Events;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Models.Execution;
using carepatron.infra.sql.Models.Insurance;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using tests.common.Builders.DataModels;
using tests.common.Data.Mappers;
using tests.component.Builders;

namespace tests.component.Features.Insurance;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class UpdateClaimsTests(ComponentTestFixture fixture) : BaseClaimsTests(fixture)
{
    [Fact]
    public async Task UpdateClaim_ShouldUpdateSuccessfully()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.AmountPaid, 20m)
            .RuleFor(x => x.ClientControlNumber, "legacy-control-number")
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());

        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = details.ToSaveRequest();
        payload.AmountPaid = faker.Finance.Amount(100, 200, 2);
        payload.Client.FirstName = faker.Person.FirstName;
        payload.Client.LastName = faker.Person.LastName;
        payload.BillingDetail.Name = faker.Random.Words(3);
        payload.BillingDetail.Address.City = faker.Address.City();
        payload.BillingDetail.Address.Country = faker.Address.Country();
        payload.ContactInsurancePolicy.PolicyHolderFirstName = faker.Person.FirstName;
        payload.ContactInsurancePolicy.PolicyHolderLastName = faker.Person.LastName;
        payload.ServiceFacility.Address.City = faker.Address.City();
        payload.ServiceFacility.Address.Country = faker.Address.Country();
        payload.Incident.AutoAccidentState = faker.Lorem.Sentence();
        payload.RenderingProviders[0].FirstName = faker.Person.FirstName;
        payload.RenderingProviders[0].LastName = faker.Person.LastName;
        payload.ReferringProviders[0].FirstName = faker.Person.FirstName;
        payload.ReferringProviders[0].LastName = faker.Person.LastName;
        payload.ServiceLines[0].Amount = faker.Finance.Amount(100, 200, 2);
        payload.ServiceLines[0].TaxAmount = faker.Finance.Amount(1, 50);
        payload.ServiceLines[0].Detail = faker.Lorem.Sentence();
        payload.ServiceLines[0].Description = faker.Lorem.Sentence();
        payload.ServiceLines[0].ServiceId = Guid.NewGuid();
        payload.AdditionalClaimInformation = faker.Lorem.Sentence(wordCount: 5);

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{details.Id}/us-professional/"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();

        // todo.... use BeEquivalentTo and break into smaller validations we can reuse
        result.AmountPaid.Should().Be(payload.AmountPaid);
        result.Client.FirstName.Should().Be(payload.Client.FirstName);
        result.Client.LastName.Should().Be(payload.Client.LastName);
        result.BillingDetail.Address.City.Should().Be(payload.BillingDetail.Address.City);
        result.BillingDetail.Address.Country.Should().Be(payload.BillingDetail.Address.Country);
        result.BillingDetail.Id.Should().Be(payload.BillingDetail.Id.Value);
        result.BillingDetail.Name.Should().Be(payload.BillingDetail.Name);
        result.BillingDetail.TaxNumber.Should().Be(payload.BillingDetail.TaxNumber);
        result.BillingDetail.NationalProviderId.Should().Be(payload.BillingDetail.NationalProviderId);
        result.ContactInsurancePolicy.PolicyHolderFirstName.Should().Be(payload.ContactInsurancePolicy.PolicyHolderFirstName);
        result.ContactInsurancePolicy.PolicyHolderLastName.Should().Be(payload.ContactInsurancePolicy.PolicyHolderLastName);
        result.ServiceFacility.Address.City.Should().Be(payload.ServiceFacility.Address.City);
        result.ServiceFacility.Address.Country.Should().Be(payload.ServiceFacility.Address.Country);
        result.Incident.AutoAccidentState.Should().Be(payload.Incident.AutoAccidentState);
        result.RenderingProviders[0].FirstName.Should().Be(payload.RenderingProviders[0].FirstName);
        result.RenderingProviders[0].LastName.Should().Be(payload.RenderingProviders[0].LastName);
        result.ReferringProviders[0].FirstName.Should().Be(payload.ReferringProviders[0].FirstName);
        result.ReferringProviders[0].LastName.Should().Be(payload.ReferringProviders[0].LastName);
        result.ServiceLines[0].Amount.Should().Be(payload.ServiceLines[0].Amount);
        result.ServiceLines[0].TaxAmount = faker.Random.Decimal(1, 50);
        result.AdditionalClaimInformation.Should().Be(payload.AdditionalClaimInformation);
        result.ServiceLines.ForEach(line =>
        {
            var claimLine = payload.ServiceLines.First(s => s.Id == line.Id);
            line.POSCode.Should().Be(claimLine.POSCode);
            line.Modifiers.Should().BeEquivalentTo(claimLine.Modifiers);
            line.DiagnosticCodeReferences.Should().BeEquivalentTo(claimLine.DiagnosticCodeReferences);
            line.Description.Should().Be(claimLine.Description);
            line.Detail.Should().Be(claimLine.Detail);
            line.ServiceId.Should().Be(claimLine.ServiceId);
        });

        var claimEvents = this.Fixture.GetPublishedEventsOfType<USProfessionalClaimUpdatedEvent>();
        claimEvents.Should().HaveCountGreaterThan(0);

        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.Id);
        dbInsuranceClaim.Should().NotBeNull();
        dbInsuranceClaim.AmountPaid.Should().Be(payload.AmountPaid);

        // Ensure client control number is set and lookup saved 
        var lookup = await DataContext.ClearingHouseIdLookups
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == dbInsuranceClaim.ContactId);
        lookup.Should().NotBeNull();
        var control = long.TryParse(dbInsuranceClaim.ClientControlNumber, out var controlNumber).Should().BeTrue();
        controlNumber.Should().Be(lookup.Identity);
    }

    [Fact]
    public async Task UpdateClaim_ShouldUpdateSuccessfullyEvenIfContactDeleted()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());

        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        // Delete contact
        var deleteRequest = new HttpRequestMessageBuilder(HttpMethod.Delete, $"api/providers/{provider.Id}/contacts/{contact.Id}")
            .WithBearerAuthorization(token)
            .Create();
        var deleteResult = await ClientApi.SendAsync(deleteRequest);
        deleteResult.StatusCode.Should().Be(HttpStatusCode.OK);

        var payload = details.ToSaveRequest();
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{details.Id}/us-professional/"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
    }

    [Fact]
    public async Task UpdateClaim_ShouldBeAbleToUpdateChildProperty()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        await DataContext.SaveChangesAsync();

        var payload = new USProfessionalClaimFaker(provider.Id)
            .WithClient(detail.Client)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .Generate()
            .ToSaveRequest();
        payload.ContactInsurancePolicy.PolicyHolderFirstName = faker.Person.FirstName;
        payload.ContactInsurancePolicy.PolicyHolderLastName = faker.Person.LastName;

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.ContactInsurancePolicy.PolicyHolderFirstName.Should().Be(payload.ContactInsurancePolicy.PolicyHolderFirstName);
        result.ContactInsurancePolicy.PolicyHolderLastName.Should().Be(payload.ContactInsurancePolicy.PolicyHolderLastName);
    }

    [Fact]
    public async Task UpdateClaim_ShouldSetClientControlNumberIfNotPreviouslySet()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.ClientControlNumber, x => null)
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());

        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = details.ToSaveRequest();
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{details.Id}/us-professional/"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == details.Id);
        dbInsuranceClaim.Should().NotBeNull();

        // Ensure client control number is set and lookup saved 
        var lookup = await DataContext.ClearingHouseIdLookups
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == dbInsuranceClaim.ContactId);
        lookup.Should().NotBeNull();
        var control = long.TryParse(dbInsuranceClaim.ClientControlNumber, out var controlNumber).Should().BeTrue();
        controlNumber.Should().Be(lookup.Identity);
    }

    [Fact]
    public async Task UpdateClaim_ShouldValidateIfNotDraft()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var payload = detail.ToSaveRequest();
        payload.Status = ClaimStatus.Rejected;
        payload.Client.FirstName = null;
        payload.Client.LastName = null;
        payload.BillingDetail.Address.City = faker.Address.City();
        payload.BillingDetail.Address.Country = faker.Address.Country();
        payload.ContactInsurancePolicy.PolicyHolderFirstName = faker.Person.FirstName;
        payload.ContactInsurancePolicy.PolicyHolderLastName = faker.Person.LastName;
        payload.ServiceFacility.Address.City = faker.Address.City();
        payload.ServiceFacility.Address.Country = faker.Address.Country();
        payload.Incident.AutoAccidentState = faker.Lorem.Sentence();
        payload.RenderingProviders[0].FirstName = faker.Person.FirstName;
        payload.RenderingProviders[0].LastName = faker.Person.LastName;
        payload.ReferringProviders[0].FirstName = faker.Person.FirstName;
        payload.ReferringProviders[0].LastName = faker.Person.LastName;
        payload.ServiceLines[0].Amount = faker.Random.Decimal(100, 200);
        payload.ServiceLines[0].TaxAmount = faker.Random.Decimal(1, 50);

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
    }

    [Fact]
    public async Task UpdateClaim_ShouldNotValidateIfDraftEvenIfPropertyIsProvided()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .WithFacility(provider.Id)
            .Generate();
        detail.Client.FirstName = null;
        detail.Client.LastName = null;
        detail.ServiceFacility.Name = null;
        detail.ServiceFacility.Address.StreetAddress = null;
        detail.Client.FirstName = null;
        detail.Client.LastName = null;
        detail.ServiceFacility.Name = null;
        detail.ServiceFacility.Address.StreetAddress = null;

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var payload = new USProfessionalClaimFaker(provider.Id)
            .WithClient(detail.Client)
            .Generate()
            .ToSaveRequest();

        payload.Client.FirstName = null;
        payload.Client.LastName = null;

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
    }

    [Fact]
    public async Task UpdateClaim_ShouldAddServiceLinesOrOtherFields()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var claimServiceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. claimServiceLines])
            .RuleFor(x => x.AmountPaid, 20)
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var payload = detail.ToSaveRequest();
        var serviceLines = payload.ServiceLines.ToList();
        serviceLines.Add(new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate().ToSaveRequest());
        payload.ServiceLines = [.. serviceLines];

        var diagnosticCodes = payload.DiagnosticCodes.ToList();
        diagnosticCodes.Add(new ClaimDiagnosticCodeFaker(provider.Id).Generate().ToSaveRequest());
        payload.DiagnosticCodes = [.. diagnosticCodes];

        var referringProviders = payload.ReferringProviders.ToList();
        referringProviders.Add(new ClaimReferringProviderFaker(provider.Id).Generate().ToSaveRequest());
        payload.ReferringProviders = [.. referringProviders];

        var renderingProviders = payload.RenderingProviders.ToList();
        renderingProviders.Add(new ClaimRenderingProviderFaker(provider.Id).Generate().ToSaveRequest());
        payload.RenderingProviders = [.. renderingProviders];

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        var r = await response.Content.ReadAsStringAsync();
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.ServiceLines.Length.Should().Be(2);
        result.DiagnosticCodes.Length.Should().Be(2);
        result.ReferringProviders.Length.Should().Be(2);
        result.RenderingProviders.Length.Should().Be(2);
        result.AmountPaid.Should().Be(billableItem.SelfPayAmount + billableItem.InsurancePaid);
    }

    [Fact]
    public async Task UpdateClaim_ShouldRemoveServiceLinesOrOtherFields()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        // Create billable items for service lines
        var billableItem1 = CreateBillableTestData(contact, provider.Id);
        var billableItem2 = CreateBillableTestData(contact, provider.Id);

        // Create multiple service lines
        var modifiedServiceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id).Generate();
        var toBeRemovedServiceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem2.Id).Generate();
        var serviceLines = new List<ClaimServiceLine>
        {
            modifiedServiceLine,
            toBeRemovedServiceLine
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .RuleFor(x => x.AmountPaid, 20)
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var payload = detail.ToSaveRequest();
        payload.ServiceLines = [modifiedServiceLine.ToSaveRequest()];
        payload.DiagnosticCodes = [];
        payload.ReferringProviders = [];
        payload.RenderingProviders = [];

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.ServiceLines.Length.Should().Be(1);
        result.DiagnosticCodes.Length.Should().Be(0);
        result.ReferringProviders.Length.Should().Be(0);
        result.RenderingProviders.Length.Should().Be(0);
        result.AmountPaid.Should().Be(billableItem1.SelfPayAmount + billableItem1.InsurancePaid);

        var claimEvents = Fixture.GetPublishedEventsOfType<USProfessionalClaimUpdatedEvent>();
        claimEvents.Should().HaveCountGreaterThan(0);
        claimEvents.First().Entity.ServiceLines.Should().HaveCount(1);
        claimEvents.First().Entity.ServiceLines.Single().Id.Should().Be(modifiedServiceLine.Id);
        claimEvents.First().Previous.ServiceLines.Should().HaveCount(2);
        claimEvents.First().Previous.ServiceLines.ForEach(item => { serviceLines.Select(s => s.Id).Should().Contain(item.Id); });
    }

    [Fact]
    public async Task UpdateClaim_ShouldUpdateMappedTasks()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var task1 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact.Id).WithStaff(staffMember.Id).WithProvider(provider.Id).Create();
        DataContext.Tasks.Add(task1);
        var task2 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact.Id).WithStaff(staffMember.Id).WithProvider(provider.Id).Create();
        DataContext.Tasks.Add(task2);
        var task3 = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact.Id).WithStaff(staffMember.Id).WithProvider(provider.Id).Create();
        DataContext.Tasks.Add(task3);

        // Create billable items for service lines
        var billableItem1 = CreateBillableTestData(contact, provider.Id, null, task1);
        var billableItem2 = CreateBillableTestData(contact, provider.Id, null, task2);
        var billableItem3 = CreateBillableTestData(contact, provider.Id, null, task3);

        // Create multiple service lines
        var existingServiceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id).Generate();
        var toBeRemovedServiceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem2.Id).Generate();
        var toBeAddedServiceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem3.Id).Generate();
        var serviceLines = new List<ClaimServiceLine>
        {
            existingServiceLine,
            toBeRemovedServiceLine
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());

        var claimTasks = new List<InsuranceClaimTaskDataModel>
        {
            new(detail.Id, task1.Id),
            new(detail.Id, task2.Id)
        };
        DataContext.InsuranceClaimTasks.AddRange(claimTasks);

        await DataContext.SaveChangesAsync();

        var payload = detail.ToSaveRequest();
        payload.ServiceLines = [existingServiceLine.ToSaveRequest(), toBeAddedServiceLine.ToSaveRequest()];

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();

        var dbInsuranceClaimTasks = await DataContext.InsuranceClaimTasks
            .AsNoTracking()
            .Where(x => x.InsuranceClaimId == result.Id)
            .ToListAsync();
        dbInsuranceClaimTasks.Count.Should().Be(2);
        var dbTaskIds = dbInsuranceClaimTasks.Select(x => x.TaskId);
        dbTaskIds.Should().Contain(task1.Id);
        dbTaskIds.Should().Contain(task3.Id);
        dbTaskIds.Should().NotContain(task2.Id);
    }

    [Fact]
    public async Task UpdateClaim_ShoulRecalculateClaimHeaderSnapshottedValues()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        // Create billable items for service lines
        var billableItem1 = CreateBillableTestData(contact, provider.Id, null);
        var billableItem2 = CreateBillableTestData(contact, provider.Id, null);

        // Create multiple service lines
        var existingServiceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id).Generate();
        var toBeAddedServiceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id).Generate();
        var serviceLines = new List<ClaimServiceLine>
        {
            existingServiceLine
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());

        await DataContext.SaveChangesAsync();

        var payload = detail.ToSaveRequest();
        payload.ServiceLines = [existingServiceLine.ToSaveRequest(), toBeAddedServiceLine.ToSaveRequest()];

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();

        var expectedServiceLines = new List<ClaimServiceLine>
        {
            existingServiceLine,
            toBeAddedServiceLine
        };
        var dbInsuranceClaimHeader = await DataContext.InsuranceClaims
            .AsNoTracking()
            .Where(x => x.Id == result.Id)
            .FirstOrDefaultAsync();
        dbInsuranceClaimHeader.Should().NotBeNull();
        dbInsuranceClaimHeader.Amount.Should().Be(expectedServiceLines.Sum(x => x.Amount));
        dbInsuranceClaimHeader.FromDate.Should().Be(expectedServiceLines.Min(x => x.Date));
        dbInsuranceClaimHeader.ToDate.Should().Be(expectedServiceLines.Max(x => x.Date));
    }

    [Fact]
    public async Task UpdateClaim_ShoulRecalculateClaimHeaderEvenIfServiceLinesEmpty()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        // Create billable items for service lines
        var billableItem1 = CreateBillableTestData(contact, provider.Id, null);
        var billableItem2 = CreateBillableTestData(contact, provider.Id, null);

        // Create multiple service lines
        var existingServiceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id).Generate();
        var toBeAddedServiceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id).Generate();
        var serviceLines = new List<ClaimServiceLine>
        {
            existingServiceLine
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());

        await DataContext.SaveChangesAsync();

        var payload = detail.ToSaveRequest();
        payload.ServiceLines = [];

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();

        var expectedServiceLines = new List<ClaimServiceLine>
        {
            existingServiceLine,
            toBeAddedServiceLine
        };
        var dbInsuranceClaimHeader = await DataContext.InsuranceClaims
            .AsNoTracking()
            .Where(x => x.Id == result.Id)
            .FirstOrDefaultAsync();
        dbInsuranceClaimHeader.Should().NotBeNull();
        dbInsuranceClaimHeader.Amount.Should().Be(0);
        dbInsuranceClaimHeader.FromDate.Should().Be(detail.FromDate);
        dbInsuranceClaimHeader.ToDate.Should().Be(detail.ToDate);
    }

    [Fact]
    public async Task UpdateClaim_ForbiddenIfNoPermissions()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin(noPermissions: true);

        var contact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
        ]);

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/"
            )
            .WithPayload(detail.ToSaveRequest())
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Code.Should().Be(Errors.UnauthorisedErrorCode);
        result.Details.Should().Be(Errors.UnauthorisedErrorDetails);
    }

    [Fact]
    public async Task UpdateClaim_ShouldBeAbleToRemoveInsurancePolicy()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = detail.ToSaveRequest();
        payload.ContactInsurancePolicy = null;

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.ContactInsurancePolicy.Should().BeNull();

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional
            .AsNoTracking()
            .Include(s => s.ClaimHeader)
            .ThenInclude(s => s.ContactInsurancePolicy)
            .Where(x => x.Id == result.Id)
            .FirstOrDefaultAsync();
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.ContactInsurancePolicy.Should().BeNull();
    }

    [Fact]
    public async Task UpdateClaim_ShouldBeAbleToRemoveBillingProfile()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = detail.ToSaveRequest();
        payload.BillingDetail = null;

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.BillingDetail.Should().BeNull();

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional
            .AsNoTracking()
            .Include(s => s.BillingDetail)
            .Where(x => x.Id == result.Id)
            .FirstOrDefaultAsync();
        dbClaim.Should().NotBeNull();
        dbClaim.BillingDetail.Should().BeNull();
    }

    [Fact]
    public async Task UpdateClaim_ShouldSetSubmissionMethodToElectronic()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());

        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        var insurancePayer = await CreateTestInsurancePayer();
        var updatedPayer = new ProviderInsurancePayerFaker(provider.Id)
            .RuleFor(x => x.PayerId, insurancePayer.PayerId)
            .Generate();
        DataContext.ProviderInsurancePayers.Add(updatedPayer.ToDataModel());

        await DataContext.SaveChangesAsync();

        // Update payer to a payer that supports electronic submission
        var payload = details.ToSaveRequest();
        payload.ContactInsurancePolicy.PayerId = updatedPayer.Id;
        payload.ContactInsurancePolicy.PayerName = updatedPayer.Name;
        payload.ContactInsurancePolicy.PayerNumber = updatedPayer.PayerId;

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{details.Id}/us-professional/"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.SubmissionMethod.Should().Be(ClaimSubmissionMethod.Electronic);
    }

    [Fact]
    public async Task UpdateClaim_ShouldNotThrowExceptionEvenDataIsNull()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        // Create billable items for service lines
        var billableItem1 = CreateBillableTestData(contact, provider.Id, null);

        // Create multiple service lines
        var existingServiceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id)
            .RuleFor(x => x.DiagnosticCodeReferences, (string[])null)
            .RuleFor(x => x.Modifiers, (string[])null)
            .Generate();
        var serviceLines = new List<ClaimServiceLine>
        {
            existingServiceLine
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());

        await DataContext.SaveChangesAsync();

        var payload = detail.ToSaveRequest();
        payload.ServiceLines = [null];
        payload.DiagnosticCodes = [null];
        payload.ReferringProviders = [null];
        payload.RenderingProviders = [null];
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }

    [Fact]
    public async Task UpdateClaim_ShouldNotUpdateStatus()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .RuleFor(x => x.Status, ClaimStatus.Submitted)
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());
        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = details.ToSaveRequest();
        payload.Status = ClaimStatus.Draft;

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{details.Id}/us-professional/"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();

        // Ensure status has not been updated
        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.Id);
        dbInsuranceClaim.Should().NotBeNull();
        dbInsuranceClaim.Status.Should().Be(ClaimStatus.Submitted);
    }
}