using System.Net;
using Bogus;
using carepatron.core.Application.Billables.Models;
using carepatron.core.Application.History.Models;
using carepatron.core.Application.Insurance;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Invoices;
using Microsoft.EntityFrameworkCore;
using tests.common.Data.Mappers;
using tests.component.Builders;

namespace tests.component.Features.Insurance;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class UpdateClaimStatusTests(ComponentTestFixture fixture) : BaseClaimsTests(fixture)
{
    [Fact]
    public async Task UpdateClaimStatus_ShouldUpdateSuccessfully()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());

        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = new { Status = ClaimStatus.Accepted };
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/claims/{details.Id}/status"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == details.Id);
        dbInsuranceClaim.Should().NotBeNull();
        dbInsuranceClaim.Status.Should().Be(ClaimStatus.Accepted);
        dbInsuranceClaim.StatusReason.Should().BeNull();

        var historyDetail = ClaimHistoryModel.Create(details);
        historyDetail.Status = ClaimStatus.Accepted;
        await CheckHistoryRecordExists(
            details.Id,
            HistoryAction.ClaimStatusChanged,
            new ClaimStatusChangedHistoryActionDetail(historyDetail)
        );
    }

    [Fact]
    public async Task UpdateClaimStatus_ShouldReturnErrorIfClaimNotFound()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();
        var payload = new { Status = ClaimStatus.Accepted };
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/claims/{Guid.NewGuid()}/status"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);

        var errorResponse = await response.Content.ReadAsAsync<ValidationError>();
        errorResponse.Should().NotBeNull();
        errorResponse.Code.Should().Be(Errors.NotFoundErrorCode);
        errorResponse.Details.Should().Be(Errors.NotFoundErrorDetails);
    }

    [Fact]
    public async Task UpdateClaimStatus_ShouldFailUpdateToPaidIfClaimHasNoPaymentAllocations()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());

        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = new { Status = ClaimStatus.Paid };
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/claims/{details.Id}/status"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var errorResponse = await response.Content.ReadAsAsync<ValidationError>();
        errorResponse.Should().NotBeNull();
        errorResponse.Code.Should().Be(Errors.InsuranceClaimNoAllocatedPaymentsCode);
        errorResponse.Details.Should().Be(Errors.InsuranceClaimNoAllocatedPaymentsDetails);
    }

    [Fact]
    public async Task UpdateClaimStatus_ShouldStatusToPaidIfFullPaymentExists()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id)
              .RuleFor(x => x.Amount, billableItem.Price)
              .RuleFor(x => x.TaxAmount, billableItem.TaxAmount)
              .Generate()
        };
        var payment = new PaymentFaker(provider.Id)
            .RuleFor(x => x.Amount, billableItem.Price + billableItem.TaxAmount)
            .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
            .RuleFor(x => x.InvoiceId, x => null)
            .Generate();
        DataContext.Payments.Add(payment.ToDataModel());
        var paymentAllocation = new PaymentAllocationFaker(provider.Id, contact.Id)
            .WithClaimServiceLine(serviceLines[0])
            .WithPayment(payment)
            .WithAmount(billableItem.Price + billableItem.TaxAmount)
            .WithBillableItem(billableItem)
            .Generate()
            .ToDataModel();
        DataContext.PaymentAllocations.Add(paymentAllocation);

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());
        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = new { Status = ClaimStatus.Paid };
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/claims/{details.Id}/status"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == details.Id);
        dbInsuranceClaim.Should().NotBeNull();
        dbInsuranceClaim.Status.Should().Be(ClaimStatus.Paid);
        dbInsuranceClaim.StatusReason.Should().BeNull();

        var historyDetail = ClaimHistoryModel.Create(details);
        historyDetail.Status = ClaimStatus.Paid;
        await CheckHistoryRecordExists(
            details.Id,
            HistoryAction.ClaimStatusChanged,
            new ClaimStatusChangedHistoryActionDetail(historyDetail)
        );
    }

    [Fact]
    public async Task UpdateClaimStatus_ShouldStatusToPartiallyPaidIfOnlyPartialPaymentExists()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };
        var payment = new PaymentFaker(provider.Id)
            .RuleFor(x => x.Amount, billableItem.Price + billableItem.TaxAmount)
            .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
            .RuleFor(x => x.InvoiceId, x => null)
            .Generate();
        DataContext.Payments.Add(payment.ToDataModel());
        var paymentAllocation = new PaymentAllocationFaker(provider.Id, contact.Id)
            .WithClaimServiceLine(serviceLines[0])
            .WithPayment(payment)
            .WithAmount((billableItem.Price + billableItem.TaxAmount) / 2)
            .WithBillableItem(billableItem)
            .Generate()
            .ToDataModel();
        DataContext.PaymentAllocations.Add(paymentAllocation);

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());
        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = new { Status = ClaimStatus.Paid };
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/claims/{details.Id}/status"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == details.Id);
        dbInsuranceClaim.Should().NotBeNull();
        dbInsuranceClaim.Status.Should().Be(ClaimStatus.PartiallyPaid);
        dbInsuranceClaim.StatusReason.Should().BeNull();

        var historyDetail = ClaimHistoryModel.Create(details);
        historyDetail.Status = ClaimStatus.PartiallyPaid;
        await CheckHistoryRecordExists(
            details.Id,
            HistoryAction.ClaimStatusChanged,
            new ClaimStatusChangedHistoryActionDetail(historyDetail)
        );
    }
    
    [Theory]
    [InlineData(ClaimStatus.Submitted)]
    [InlineData(ClaimStatus.Accepted)]
    [InlineData(ClaimStatus.Paid)]
    [InlineData(ClaimStatus.PartiallyPaid)]
    [InlineData(ClaimStatus.Denied)]
    public async Task UpdateClaimStatus_WithElectronicSubmitClaim_WithInvalidStatus_ShouldReturnBadRequest(ClaimStatus status)
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .WithStatus(status)
            .RuleFor(x => x.SubmissionMethod, ClaimSubmissionMethod.Electronic)
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());

        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = new { Status = ClaimStatus.Accepted };
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/claims/{details.Id}/status"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        var errorResponse = await response.Content.ReadAsAsync<ValidationError>();
        errorResponse.Should().BeEquivalentTo(InsuranceErrors.ClaimStatusCannotBeChangedForElectronicClaims);
    }
    
    [Fact]
    public async Task UpdateClaimStatus_WithElectronicSubmitClaim_FromRejectedToDraft_ShouldSuccess()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .WithStatus(ClaimStatus.Rejected)
            .RuleFor(x => x.SubmissionMethod, ClaimSubmissionMethod.Electronic)
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());

        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = new { Status = ClaimStatus.Draft };
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/claims/{details.Id}/status"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);
    }
    
    [Fact]
    public async Task UpdateStatus_Electronic_AllowMarkingAsPaid()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id)
              .RuleFor(x => x.Amount, billableItem.Price)
              .RuleFor(x => x.TaxAmount, billableItem.TaxAmount)
              .Generate()
        };
        
        // Add a full payment
        var payment = new PaymentFaker(provider.Id)
            .RuleFor(x => x.Amount, billableItem.Price + billableItem.TaxAmount)
            .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
            .RuleFor(x => x.InvoiceId, x => null)
            .Generate();
        DataContext.Payments.Add(payment.ToDataModel());
        
        var paymentAllocation = new PaymentAllocationFaker(provider.Id, contact.Id)
            .WithClaimServiceLine(serviceLines[0])
            .WithPayment(payment)
            .WithAmount(billableItem.Price + billableItem.TaxAmount)
            .WithBillableItem(billableItem)
            .Generate()
            .ToDataModel();
        DataContext.PaymentAllocations.Add(paymentAllocation);

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .WithStatus(ClaimStatus.Accepted)
            .RuleFor(x => x.SubmissionMethod, ClaimSubmissionMethod.Electronic)
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());

        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var payload = new { Status = ClaimStatus.Paid };
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/claims/{details.Id}/status"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == details.Id);
        dbInsuranceClaim.Should().NotBeNull();
        dbInsuranceClaim.Status.Should().Be(ClaimStatus.Paid);
        
        var historyDetail = ClaimHistoryModel.Create(details);
        historyDetail.Status = ClaimStatus.Paid;
        await CheckHistoryRecordExists(
            details.Id,
            HistoryAction.ClaimStatusChanged,
            new ClaimStatusChangedHistoryActionDetail(historyDetail)
        );
    }
}
