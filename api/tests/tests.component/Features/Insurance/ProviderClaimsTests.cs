using System.Net;
using Bogus;
using carepatron.api.Contracts.Requests.Insurance;
using carepatron.api.Contracts.Requests.Trash;
using carepatron.core.Application.Insurance.Events;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Trash.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Pagination;
using carepatron.infra.sql.Models.Insurance;
using carepatron.infra.sql.Models.Tasks;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using tests.common.Builders.DataModels;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.component.Builders;

namespace tests.component.Features.Insurance;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class ProviderClaimsTests : BaseClaimsTests
{
    public ProviderClaimsTests(ComponentTestFixture fixture) : base(fixture)
    {
    }

    [Fact]
    public async Task ListContactClaims_ForbiddenIfNoPermission()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin(noPermissions: true);

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();
        for (var i = 0; i < 5; i++)
        {
            var billableItem = CreateBillableTestData(contact, provider.Id);
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
            };
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .WithServiceLines([.. serviceLines])
                .Generate();
            details.Add(detail);
            claims.Add(detail.ToClaimHeaderDataModel());
        }
        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange(details.Select(s => s.ToDataModel()));
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims?offset=0&limit=3"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Code.Should().Be(Errors.UnauthorisedErrorCode);
        result.Details.Should().Be(Errors.UnauthorisedErrorDetails);
    }

    [Fact]
    public async Task ListContactClaims_ShouldFetchSuccessfully()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();
        var detailDbModels = new List<InsuranceClaimUSProfessionalDataModel>();
        for (var i = 0; i < 5; i++)
        {
            var billableItem = CreateBillableTestData(contact, provider.Id);
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
            };
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .WithServiceLines([.. serviceLines])
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();
            details.Add(detail);
            claims.Add(detail.ToClaimHeaderDataModel());
            detailDbModels.Add(detail.ToDataModel());
        }
        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange([.. detailDbModels]);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims?offset=0&limit=3"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaim>>();
        result.Items.Count.Should().Be(3);
        result.Pagination.HasMore.Should().BeTrue();

        result.Items.ForEach(item =>
        {
            var detail = details.First(d => d.Id == item.Id);
            item.Should().BeEquivalentTo(new
            {
                detail.Id,
                detail.ProviderId,
                detail.ContactId,
                detail.Type,
                detail.Status,
                detail.SubmissionMethod,
                detail.Number,
                detail.Amount,
                detail.FromDate,
                detail.ToDate,
                detail.CurrencyCode,
                detail.LastSubmittedDateTimeUtc,
            });
            item.Payer.Should().BeEquivalentTo(new
            {
                Id = payer.Id,
                Name = payer.Name,
                PayerNumber = payer.PayerId
            });
        });
    }

    [Fact]
    public async Task ListContactClaims_ShouldFilterByStatus()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();

        // There will be 3 Draft claims
        for (var i = 0; i < 5; i++)
        {
            var billableItem = CreateBillableTestData(contact, provider.Id);
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
            };
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .WithServiceLines([.. serviceLines])
                .RuleFor(x => x.Status, i % 2 == 0 ? ClaimStatus.Draft : ClaimStatus.Submitted)
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();
            details.Add(detail);
            claims.Add(detail.ToClaimHeaderDataModel());
        }
        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange(details.Select(s => s.ToDataModel()));
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims?offset={QueryConstants.DefaultOffset}&limit={QueryConstants.DefaultLimit}&status=Draft"
            )
            .WithBearerAuthorization(token)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimReference>>();
        result.Items.Count.Should().Be(3);
    }

    [Fact]
    public async Task ListContactClaims_ShouldFilterByDateRangeOnServiceLinesDate()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();

        // There will be 3 claims with service line date that are 5 days ago
        for (var i = 0; i < 5; i++)
        {
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .RuleFor(x => x.ServiceLines, [
                    new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id)
                        .RuleFor(x => x.Date, i % 2 == 0 ? DateTime.UtcNow.AddDays(-5).ToDateOnly() : DateTime.UtcNow.ToDateOnly())
                        .Generate()
                ])
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();
            details.Add(detail);
            claims.Add(detail.ToClaimHeaderDataModel());
        }
        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange(details.Select(s => s.ToDataModel()));
        await DataContext.SaveChangesAsync();

        var fromDate = DateTime.UtcNow.AddDays(-5).ToISOString();
        var toDate = DateTime.UtcNow.AddDays(-1).ToISOString();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims?offset={QueryConstants.DefaultOffset}&limit={QueryConstants.DefaultLimit}&fromDate={fromDate}&toDate={toDate}"
            )
            .WithBearerAuthorization(token)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimReference>>();
        result.Items.Count.Should().Be(3);
    }

    [Fact]
    public async Task ListContactClaims_ShouldFilterByTaskIds()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();
        var tasks = new List<TaskDataModel>();
        var claimTasks = new List<InsuranceClaimTaskDataModel>();

        // There will be 3 claims with tasks mapped
        for (var i = 0; i < 5; i++)
        {
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .RuleFor(x => x.ServiceLines, [])
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();
            details.Add(detail);
            claims.Add(detail.ToClaimHeaderDataModel());

            if (i < 3)
            {
                var task = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact.Id).WithStaff(PersonId).WithProvider(provider.Id).Create();
                tasks.Add(task);

                var insuranceClaimTask = new InsuranceClaimTaskDataModel(detail.Id, task.Id);
                claimTasks.Add(insuranceClaimTask);
            }
        }

        DataContext.InsuranceClaimTasks.AddRange(claimTasks);
        DataContext.Tasks.AddRange(tasks);
        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange(details.Select(s => s.ToDataModel()));
        await DataContext.SaveChangesAsync();

        var taskIdsQueryString = string.Join('&', tasks.Select(task => $"taskIds={task.Id}"));
        var url = $"api/providers/{provider.Id}/contacts/{contact.Id}/claims?offset={QueryConstants.DefaultOffset}&limit={QueryConstants.DefaultLimit}&{taskIdsQueryString}";
        var request = new HttpRequestMessageBuilder(HttpMethod.Get, url)
            .WithBearerAuthorization(token)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimReference>>();
        result.Items.Count.Should().Be(3);
    }

    [Fact]
    public async Task ListContactClaims_ShouldFilterBySearchTerm()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();

        for (var i = 0; i < 5; i++)
        {
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .RuleFor(x => x.ServiceLines, [])
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();
            details.Add(detail);
            claims.Add(detail.ToClaimHeaderDataModel());
        }

        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange(details.Select(s => s.ToDataModel()));
        await DataContext.SaveChangesAsync();

        var searchedNumber = claims[0].Number;
        var url = $"api/providers/{provider.Id}/contacts/{contact.Id}/claims?offset={QueryConstants.DefaultOffset}&limit={QueryConstants.DefaultLimit}&searchTerm={searchedNumber}";
        var request = new HttpRequestMessageBuilder(HttpMethod.Get, url)
            .WithBearerAuthorization(token)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimReference>>();
        result.Items.Count.Should().Be(1);
        result.Items[0].Number.Should().Be(searchedNumber);
    }

    [Fact]
    public async Task ListClaims_ShouldFetchSuccessfully()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();
        var detailDbModels = new List<InsuranceClaimUSProfessionalDataModel>();
        for (var i = 0; i < 5; i++)
        {
            var billableItem = CreateBillableTestData(contact, provider.Id);
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
            };
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .WithServiceLines([.. serviceLines])
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();
            details.Add(detail);
            claims.Add(detail.ToClaimHeaderDataModel());
            detailDbModels.Add(detail.ToDataModel());
        }
        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange([.. detailDbModels]);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/claims?offset=0&limit=3"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimListEntry>>();
        result.Items.Count.Should().Be(3);
        result.Pagination.HasMore.Should().BeTrue();

        result.Items.ForEach(item =>
        {
            var detail = details.First(d => d.Id == item.Id);
            item.Should().BeEquivalentTo(new
            {
                detail.Id,
                detail.ProviderId,
                detail.ContactId,
                detail.Type,
                detail.Status,
                detail.SubmissionMethod,
                detail.Number,
                detail.Amount,
                detail.FromDate,
                detail.ToDate,
                detail.CurrencyCode,
                detail.LastSubmittedDateTimeUtc,
            });
            item.Payer.Should().BeEquivalentTo(new
            {
                Id = payer.Id,
                Name = payer.Name,
                PayerNumber = payer.PayerId
            });
            item.Staff.Single().Should().BeEquivalentTo(new
            {
                staffPerson.Id,
                staffPerson.FullName,
                staffPerson.FirstName,
                staffPerson.LastName
            });
            item.Client.Should().BeEquivalentTo(new
            {
                contact.Id,
                contact.FullName,
                contact.FirstName,
                contact.LastName
            });
        });
    }

    [Fact]
    public async Task ListClaims_FiltersByPayerNumber()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();

        // 5 draft claims
        for (var i = 0; i < 5; i++)
        {
            var billableItem = CreateBillableTestData(contact, provider.Id);
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
            };
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, i == 0 ? null : policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .WithServiceLines([.. serviceLines])
                .RuleFor(x => x.Status, ClaimStatus.Draft)
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();

            if (i == 0)
            {
                detail.ContactInsurancePolicy.PayerNumber = "123456";
            }

            details.Add(detail);
            var claimHeader = detail.ToClaimHeaderDataModel();
            claims.Add(claimHeader);
        }
        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange(details.Select(s => s.ToDataModel()));
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/claims?offset={QueryConstants.DefaultOffset}&limit={QueryConstants.DefaultLimit}&payerIds=123456"
            )
            .WithBearerAuthorization(token)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimListEntry>>();
        result.Items.Count.Should().Be(1);
    }

    [Fact]
    public async Task ListClaims_FiltersByStatus()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();

        for (var i = 0; i < 5; i++)
        {
            var billableItem = CreateBillableTestData(contact, provider.Id);
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
            };
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, i == 0 ? null : policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .WithServiceLines([.. serviceLines])
                .RuleFor(x => x.Status, i == 0 ? ClaimStatus.Submitted : ClaimStatus.Draft)
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();

            details.Add(detail);
            var claimHeader = detail.ToClaimHeaderDataModel();
            claims.Add(claimHeader);
        }
        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange(details.Select(s => s.ToDataModel()));
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/claims?offset={QueryConstants.DefaultOffset}&limit={QueryConstants.DefaultLimit}&status={ClaimStatus.Submitted}"
            )
            .WithBearerAuthorization(token)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimListEntry>>();
        result.Items.Count.Should().Be(1);
        result.Items.First().Id.Should().Be(claims.First().Id);
    }

    [Fact]
    public async Task ListClaims_WhenSearchTermIsProvider_FiltersByClaimNumber()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();

        // 5 draft claims
        for (var i = 0; i < 5; i++)
        {
            var billableItem = CreateBillableTestData(contact, provider.Id);
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
            };
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, i == 0 ? null : policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .WithServiceLines([.. serviceLines])
                .RuleFor(x => x.Status, ClaimStatus.Draft)
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();

            if (i == 0)
            {
                detail.Number = "123456";
            }

            details.Add(detail);
            var claimHeader = detail.ToClaimHeaderDataModel();
            claims.Add(claimHeader);
        }
        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange(details.Select(s => s.ToDataModel()));
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/claims?offset={QueryConstants.DefaultOffset}&limit={QueryConstants.DefaultLimit}&SearchTerm=12345"
            )
            .WithBearerAuthorization(token)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimListEntry>>();
        result.Items.Count.Should().Be(1);
    }

    [Fact]
    public async Task ListClaims_FiltersByContactId()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var selectedContactId = Guid.NewGuid();

        for (var i = 0; i < 2; i++)
        {
            var contact = new ContactFaker(provider.Id).Generate();

            if (i == 0)
            {
                contact.Id = selectedContactId;
            }

            var memberContact = new ContactFaker(provider.Id).Generate();
            DataContext.Contacts.AddRange([
                contact.ToDataModel(),
                memberContact.ToDataModel()
            ]);

            var staffPerson = new PersonFaker().Generate();
            var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
            DataContext.Persons.Add(staffPerson.ToDataModel());
            DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

            var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
            DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

            var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
                .RuleFor(x => x.Payer, payer)
                .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
                {
                    Type = InsurancePolicyHolderType.Client,
                    Contact = memberContact.ToPolicyHolder(),
                    RelationshipType = "Client"
                }).Generate();
            DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

            var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
            defaultBillingProfile.IsDefault = true;
            DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

            var billableItem = CreateBillableTestData(contact, provider.Id);
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
            };
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, i == 0 ? null : policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .WithServiceLines([.. serviceLines])
                .RuleFor(x => x.Status, ClaimStatus.Draft)
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();

            var claimHeader = detail.ToClaimHeaderDataModel();
            DataContext.InsuranceClaims.Add(claimHeader);
            DataContext.InsuranceClaimsUSProfessional.AddRange(detail.ToDataModel());
        }
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/claims?offset={QueryConstants.DefaultOffset}&limit={QueryConstants.DefaultLimit}&contactIds={selectedContactId}"
            )
            .WithBearerAuthorization(token)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimListEntry>>();
        result.Items.Count.Should().Be(1);
        result.Items.First().Client.Id.Should().Be(selectedContactId);
    }

    [Fact]
    public async Task ListClaims_FiltersByStaffId()
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var selectedStaffId = Guid.NewGuid();

        for (var i = 0; i < 2; i++)
        {
            var contact = new ContactFaker(provider.Id).Generate();
            var memberContact = new ContactFaker(provider.Id).Generate();
            DataContext.Contacts.AddRange([
                contact.ToDataModel(),
                memberContact.ToDataModel()
            ]);

            var staffPerson = new PersonFaker().Generate();

            if (i == 0)
            {
                staffPerson.Id = selectedStaffId;
            }

            var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
            DataContext.Persons.Add(staffPerson.ToDataModel());
            DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

            var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
            DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

            var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
                .RuleFor(x => x.Payer, payer)
                .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
                {
                    Type = InsurancePolicyHolderType.Client,
                    Contact = memberContact.ToPolicyHolder(),
                    RelationshipType = "Client"
                }).Generate();
            DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

            var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
            defaultBillingProfile.IsDefault = true;
            DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

            var billableItem = CreateBillableTestData(contact, provider.Id);
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
            };
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, i == 0 ? null : policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .WithServiceLines([.. serviceLines])
                .RuleFor(x => x.Status, ClaimStatus.Draft)
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();

            var claimHeader = detail.ToClaimHeaderDataModel();
            DataContext.InsuranceClaims.Add(claimHeader);
            DataContext.InsuranceClaimsUSProfessional.AddRange(detail.ToDataModel());
        }
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/claims?offset={QueryConstants.DefaultOffset}&limit={QueryConstants.DefaultLimit}&staffIds={selectedStaffId}"
            )
            .WithBearerAuthorization(token)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimListEntry>>();
        result.Items.Count.Should().Be(1);
        result.Items.First().Staff.Any((x) => x.Id == selectedStaffId).Should().BeTrue();
    }

    [Theory]
    [InlineData("asc(number)", 0)]
    [InlineData("desc(number)", 1)]
    [InlineData("asc(fromDate)", 0)]
    [InlineData("desc(fromDate)", 1)]
    [InlineData("asc(lastSubmittedDateTimeUtc)", 0)]
    [InlineData("desc(lastSubmittedDateTimeUtc)", 1)]
    public async Task ListClaims_SortsBy(string sortQuery, int expectedIndex)
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();

        for (var i = 0; i < 2; i++)
        {
            var billableItem = CreateBillableTestData(contact, provider.Id);
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
            };
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, i == 0 ? null : policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .WithServiceLines([.. serviceLines])
                .RuleFor(x => x.Status, ClaimStatus.Submitted)
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();

            if (i == 0)
            {
                detail.Number = "000001";
                detail.FromDate = DateTime.UtcNow.AddDays(-1).ToDateOnly();
                detail.LastSubmittedDateTimeUtc = DateTime.UtcNow.AddDays(-1);
            }
            else
            {
                detail.FromDate = DateTime.UtcNow.ToDateOnly();
                detail.Number = "000002";
            }

            details.Add(detail);
            var claimHeader = detail.ToClaimHeaderDataModel();
            claims.Add(claimHeader);
        }
        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange(details.Select(s => s.ToDataModel()));
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/claims?offset={QueryConstants.DefaultOffset}&limit={QueryConstants.DefaultLimit}&sort={sortQuery}"
            )
            .WithBearerAuthorization(token)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimListEntry>>();
        result.Items[expectedIndex].Id.Should().Be(claims.First().Id);
    }

    [Theory]
    [InlineData("asc(fromDate)", 1)]
    [InlineData("desc(fromDate)", 0)]
    public async Task ListClaims_WhenFromDateIsNotProvided_ShouldSortByCreatedDate(string sortQuery, int expectedIndex)
    {
        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var details = new List<InsuranceClaimUSProfessional>();
        var claims = new List<InsuranceClaimDataModel>();

        for (var i = 0; i < 2; i++)
        {
            var billableItem = CreateBillableTestData(contact, provider.Id);
            var serviceLines = new List<ClaimServiceLine>
            {
                new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
            };
            var detail = new USProfessionalClaimFaker(provider.Id)
                .WithBillingDetail(provider.Id, defaultBillingProfile)
                .WithClient(provider.Id, contact)
                .WithContactInsurancePolicy(provider.Id, contact.Id, i == 0 ? null : policy)
                .WithDiagnosticCodes(provider.Id)
                .WithFacility(provider.Id)
                .WithIncident(provider.Id)
                .WithReferringProviders(provider.Id)
                .WithRenderingProviders(provider.Id, staffMember)
                .WithServiceLines([.. serviceLines])
                .RuleFor(x => x.Status, ClaimStatus.Submitted)
                .RuleFor(x => x.ContactId, contact.Id)
                .Generate();

            if (i == 0)
            {
                detail.Number = "000001";
                detail.FromDate = DateTime.UtcNow.AddDays(1).ToDateOnly();
            }
            else
            {
                detail.CreatedDateTimeUtc = DateTime.UtcNow;
                detail.FromDate = detail.CreatedDateTimeUtc.ToDateOnly();
                detail.Number = "000002";
            }

            details.Add(detail);
            var claimHeader = detail.ToClaimHeaderDataModel();
            claims.Add(claimHeader);
        }
        DataContext.InsuranceClaims.AddRange(claims);
        DataContext.InsuranceClaimsUSProfessional.AddRange(details.Select(s => s.ToDataModel()));
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/claims?offset={QueryConstants.DefaultOffset}&limit={QueryConstants.DefaultLimit}&sort={sortQuery}"
            )
            .WithBearerAuthorization(token)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InsuranceClaimListEntry>>();
        result.Items[expectedIndex].Id.Should().Be(claims.First().Id);
    }

    [Fact]
    public async Task UpdateClaimStatus_ShouldUpdateStatusSuccessfully()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());

        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/claims/{details.Id}/status"
            )
            .WithBearerAuthorization(token)
            .WithPayload(new UpdateClaimStatusRequest(ClaimStatus.Closed))
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == details.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Closed);

        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == details.Id);
        dbInsuranceClaim.Should().NotBeNull();
        dbInsuranceClaim.Status.Should().Be(ClaimStatus.Closed);
    }
    
    [Fact]
    public async Task UpdateClaimStatus_WithPaidStatusAndAllocations_ShouldUpdateStatusSuccessfully()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        
        // Creating allocated payment
        var payment = new PaymentFaker(provider.Id)
            .RuleFor(x => x.InvoiceId, f => (Guid?)null)
            .Generate();
        var paymentAllocationDataModels = serviceLines.Select(x => new PaymentAllocationFaker(provider.Id, contact.Id)
            .WithClaimServiceLine(x)
            .WithBillableItem(billableItem)
            .WithPayment(payment)
            .WithAmount(x.Amount + x.TaxAmount)
            .Generate().ToDataModel()
        ).ToArray();
        
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());
        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());
        DataContext.Payments.Add(payment.ToDataModel());
        DataContext.PaymentAllocations.AddRange(paymentAllocationDataModels);

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/claims/{details.Id}/status"
            )
            .WithBearerAuthorization(token)
            .WithPayload(new UpdateClaimStatusRequest(ClaimStatus.Paid))
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == details.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Paid);

        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == details.Id);
        dbInsuranceClaim.Should().NotBeNull();
        dbInsuranceClaim.Status.Should().Be(ClaimStatus.Paid);
    }
    
    [Fact]
    public async Task UpdateClaimStatus_WithPaidStatusWithoutAllocations_ShouldReturnBadRequest()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var details = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        
        DataContext.InsuranceClaimsUSProfessional.Add(details.ToDataModel());
        DataContext.InsuranceClaims.Add(details.ToClaimHeaderDataModel());

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Put, $"api/providers/{provider.Id}/claims/{details.Id}/status"
            )
            .WithBearerAuthorization(token)
            .WithPayload(new UpdateClaimStatusRequest(ClaimStatus.Paid))
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == details.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);

        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == details.Id);
        dbInsuranceClaim.Should().NotBeNull();
        dbInsuranceClaim.Status.Should().Be(ClaimStatus.Draft);
    }

    [Fact]
    public async Task GetClaim_ShouldFetchSuccessfully()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/claims/{detail.Id}"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaim>();
        result.Should().NotBeNull();

        result.Should().BeEquivalentTo(new
        {
            detail.Id,
            detail.ProviderId,
            detail.ContactId,
            detail.Type,
            detail.Status,
            detail.SubmissionMethod,
            detail.Number,
            detail.Amount,
            detail.FromDate,
            detail.ToDate,
            detail.CurrencyCode,
            detail.LastSubmittedDateTimeUtc,
        });
    }

    [Fact]
    public async Task GetClaim_AfterDeletedSourceData_ShouldFetchSuccessfully()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        detail.RenderingProviders.ForEach(x => x.StaffMember = null);

        var claimHeader = detail.ToClaimHeaderDataModel();
        claimHeader.ContactInsurancePolicy.ContactInsurancePolicy = null;
        DataContext.InsuranceClaims.Add(claimHeader);
        var claimDetail = detail.ToDataModel();

        DataContext.InsuranceClaimsUSProfessional.Add(claimDetail);
        await DataContext.SaveChangesAsync();
        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/claims/{detail.Id}"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaim>();
        result.Should().NotBeNull();

        result.Id.Should().Be(detail.Id);
        result.SubmissionMethod.Should().Be(detail.SubmissionMethod);
        result.Status.Should().Be(detail.Status);
        result.Type.Should().Be(detail.Type);
        result.Payer.Name.Should().BeEquivalentTo(detail.ContactInsurancePolicy.PayerName);
        result.Payer.PayerNumber.Should().BeEquivalentTo(detail.ContactInsurancePolicy.PayerNumber);
    }


    [Fact]
    public async Task GetClaim_ShouldReturnNotFoundIfClaimNotFound()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Get, $"api/providers/{provider.Id}/claims/{Guid.NewGuid()}"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

}
