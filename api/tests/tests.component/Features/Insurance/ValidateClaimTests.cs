using System.Net;
using Bogus;
using carepatron.core.Application.History.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Models.Execution;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Mocks;
using tests.component.Builders;

namespace tests.component.Features.Insurance;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class ValidateClaimsTests : BaseClaimsTests
{
    public ValidateClaimsTests(ComponentTestFixture fixture) : base(fixture)
    {
        var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
        fileStorageRepositoryMock.UseRealImplementation();
    }

    [Fact]
    public async Task ValidateClaim_ShouldUpdateStatusSuccessfully()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/validate"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Validated);
        dbClaim.ClaimHeader.StatusReason.Should().BeNullOrEmpty();

        var historyDetail = ClaimHistoryModel.Create(detail);
        historyDetail.Status = ClaimStatus.Validated;
        await CheckHistoryRecordExists(
            detail.Id,
            HistoryAction.ClaimStatusChanged,
            new ClaimStatusChangedHistoryActionDetail(historyDetail)
        );
    }

    [Fact]
    public async Task ValidateClaim_ShouldReturnFieldValidationErrors()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var address = new AddressFaker()
            .RuleFor(x => x.StreetAddress, x => null)
            .Generate();
        var claimClient = new ClaimClientFaker(provider.Id, contact)
            .RuleFor(x => x.FirstName, x => null)
            .RuleFor(x => x.LastName, x => null)
            .RuleFor(x => x.MiddleName, x => null)
            .RuleFor(x => x.DateOfBirth, x => null)
            .RuleFor(x => x.PhoneNumber, x => null)
            .RuleFor(x => x.Sex, x => null)
            .RuleFor(x => x.Address, x => address)
            .Generate();
        var renderingProvider = new ClaimRenderingProviderFaker(provider.Id, staffMember)
            .RuleFor(x => x.FirstName, x => null)
            .RuleFor(x => x.LastName, x => null)
            .RuleFor(x => x.MiddleName, x => null)
            .Generate();
        var referringProvider = new ClaimReferringProviderFaker(provider.Id)
            .RuleFor(x => x.FirstName, x => null)
            .RuleFor(x => x.LastName, x => null)
            .RuleFor(x => x.MiddleName, x => null)
            .RuleFor(x => x.Qualifier, x => null)
            .Generate();
        var facility = new ClaimFacilityFaker(provider.Id)
            .RuleFor(x => x.Name, x => null)
            .RuleFor(x => x.Address, x => address)
            .Generate();
        var claimPolicy = new ClaimContactInsurancePolicyFaker(provider.Id, contact.Id, policy)
            .RuleFor(x => x.PolicyHolderFirstName, x => null)
            .RuleFor(x => x.PolicyHolderLastName, x => null)
            .RuleFor(x => x.PolicyHolderMiddleName, x => null)
            .RuleFor(x => x.PolicyHolderDateOfBirth, x => null)
            .RuleFor(x => x.PolicyHolderPhoneNumber, x => null)
            .RuleFor(x => x.PolicyHolderGroupId, x => null)
            .RuleFor(x => x.PolicyHolderMemberId, x => null)
            .RuleFor(x => x.CoverageType, x => null)
            .Generate();
        var claimBillingDetail = new ClaimBillingDetailFaker(provider.Id, defaultBillingProfile)
            .RuleFor(x => x.TaxNumberType, x => null)
            .RuleFor(x => x.TaxNumber, x => null)
            .RuleFor(x => x.TaxonomyCode, x => null)
            .RuleFor(x => x.NationalProviderId, x => null)
            .RuleFor(x => x.Address, x => address)
            .Generate();
        var serviceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id)
            .RuleFor(x => x.Units, x => 0)
            .RuleFor(x => x.Amount, x => 0)
            .Generate();

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(claimBillingDetail)
            .WithFacility(facility)
            .WithClient(claimClient)
            .WithContactInsurancePolicy(claimPolicy)
            .WithRenderingProviders([renderingProvider])
            .WithDiagnosticCodes(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders([referringProvider])
            .WithServiceLines([serviceLine])
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/validate"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.InsuranceClaimFailedToValidateCode);
        validationError.Details.Should().Be(Errors.InsuranceClaimFailedToValidateDetails);
        var errors = JsonConvert.DeserializeObject<FieldValidationError[]>(validationError.AdditionalDataJson);
        errors.Should().NotBeNull();
        errors.Length.Should().BeGreaterThan(0);

        var fieldNameErrors = new List<string>
        {
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.FirstName)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.LastName)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.MiddleName)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.DateOfBirth)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.PhoneNumber)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.Sex)}",
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.Address)}.{nameof(Address.StreetAddress)}",
            $"{nameof(InsuranceClaimUSProfessional.RenderingProviders)}[0].{nameof(ClaimRenderingProvider.FirstName)}",
            $"{nameof(InsuranceClaimUSProfessional.RenderingProviders)}[0].{nameof(ClaimRenderingProvider.LastName)}",
            $"{nameof(InsuranceClaimUSProfessional.RenderingProviders)}[0].{nameof(ClaimRenderingProvider.MiddleName)}",
            $"{nameof(InsuranceClaimUSProfessional.ReferringProviders)}[0].{nameof(ClaimReferringProvider.FirstName)}",
            $"{nameof(InsuranceClaimUSProfessional.ReferringProviders)}[0].{nameof(ClaimReferringProvider.LastName)}",
            $"{nameof(InsuranceClaimUSProfessional.ReferringProviders)}[0].{nameof(ClaimReferringProvider.MiddleName)}",
            $"{nameof(InsuranceClaimUSProfessional.ReferringProviders)}[0].{nameof(ClaimReferringProvider.Qualifier)}",
            $"{nameof(InsuranceClaimUSProfessional.ServiceFacility)}.{nameof(ClaimFacility.Name)}",
            $"{nameof(InsuranceClaimUSProfessional.ServiceFacility)}.{nameof(ClaimFacility.Address)}.{nameof(Address.StreetAddress)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderFirstName)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderLastName)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderMiddleName)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderDateOfBirth)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderPhoneNumber)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderGroupId)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PolicyHolderMemberId)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.CoverageType)}",
            $"{nameof(InsuranceClaimUSProfessional.BillingDetail)}.{nameof(ClaimBillingDetail.TaxNumberType)}",
            $"{nameof(InsuranceClaimUSProfessional.BillingDetail)}.{nameof(ClaimBillingDetail.TaxNumber)}",
            $"{nameof(InsuranceClaimUSProfessional.BillingDetail)}.{nameof(ClaimBillingDetail.TaxonomyCode)}",
            $"{nameof(InsuranceClaimUSProfessional.BillingDetail)}.{nameof(ClaimBillingDetail.NationalProviderId)}",
            $"{nameof(InsuranceClaimUSProfessional.BillingDetail)}.{nameof(ClaimBillingDetail.Address)}.{nameof(Address.StreetAddress)}",
            $"{nameof(InsuranceClaimUSProfessional.ServiceLines)}[0].{nameof(ClaimServiceLine.Units)}",
            $"{nameof(InsuranceClaimUSProfessional.ServiceLines)}[0].{nameof(ClaimServiceLine.Amount)}"
        };

        foreach (var error in errors)
        {
            error.Should().NotBeNull();
            error.FieldName.Should().NotBeNullOrEmpty();
            var isErrorInList = fieldNameErrors.Contains(error.FieldName);
            fieldNameErrors.Should().Contain(error.FieldName);
            error.ErrorMessage.Should().NotBeNullOrEmpty();
        }

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);
    }

    [Fact]
    public async Task ValidateClaim_ShouldValidateWithMissingAddresses()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        var memberContact = new ContactFaker(provider.Id)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var claimClient = new ClaimClientFaker(provider.Id, contact)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        var renderingProvider = new ClaimRenderingProviderFaker(provider.Id, staffMember).Generate();
        var referringProvider = new ClaimReferringProviderFaker(provider.Id).Generate();
        var facility = new ClaimFacilityFaker(provider.Id)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        var claimPolicy = new ClaimContactInsurancePolicyFaker(provider.Id, contact.Id, policy)
            .RuleFor(s => s.Address, x => null)
            .Generate();
        var claimBillingDetail = new ClaimBillingDetailFaker(provider.Id, defaultBillingProfile)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        var serviceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id)
            .Generate();

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(claimBillingDetail)
            .WithFacility(facility)
            .WithClient(claimClient)
            .WithContactInsurancePolicy(claimPolicy)
            .WithRenderingProviders([renderingProvider])
            .WithDiagnosticCodes(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders([referringProvider])
            .WithServiceLines([serviceLine])
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/validate"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.InsuranceClaimFailedToValidateCode);
        validationError.Details.Should().Be(Errors.InsuranceClaimFailedToValidateDetails);
        var errors = JsonConvert.DeserializeObject<FieldValidationError[]>(validationError.AdditionalDataJson);
        errors.Should().NotBeNull();
        errors.Length.Should().BeGreaterThan(0);

        var fieldNameErrors = new List<string>
        {
            $"{nameof(InsuranceClaimUSProfessional.Client)}.{nameof(ClaimClient.Address)}",
            $"{nameof(InsuranceClaimUSProfessional.ServiceFacility)}.{nameof(ClaimFacility.Address)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.Address)}",
            $"{nameof(InsuranceClaimUSProfessional.BillingDetail)}.{nameof(ClaimBillingDetail.Address)}",
        };

        foreach (var error in errors)
        {
            error.Should().NotBeNull();
            error.FieldName.Should().NotBeNullOrEmpty();
            var isErrorInList = fieldNameErrors.Contains(error.FieldName);
            fieldNameErrors.Should().Contain(error.FieldName);
            error.ErrorMessage.Should().NotBeNullOrEmpty();
        }

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);
    }

    [Fact]
    public async Task ValidateClaim_ShouldClearClaimSubmissionErrors()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());

        var claimSubmissionError = new InsuranceClaimErrorFaker(provider.Id, detail)
            .Generate();
        DataContext.InsuranceClaimErrors.Add(claimSubmissionError.ToDataModel());

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/validate"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Validated);
        dbClaim.ClaimHeader.StatusReason.Should().BeNullOrEmpty();

        var dbClaimErrors = await DataContext.InsuranceClaimErrors.AsNoTracking()
            .Where(x => x.InsuranceClaimId == detail.Id)
            .ToListAsync();
        dbClaimErrors.Should().NotBeNull();
        dbClaimErrors.Count.Should().Be(1);
        dbClaimErrors[0].Status.Should().Be(ClaimErrorStatus.Inactive);
    }

    [Fact]
    public async Task ValidateClaim_ShouldValidatePayerPhoneAndAddressIfManualSubmission()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        var memberContact = new ContactFaker(provider.Id)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var claimClient = new ClaimClientFaker(provider.Id, contact).Generate();
        var renderingProvider = new ClaimRenderingProviderFaker(provider.Id, staffMember).Generate();
        var referringProvider = new ClaimReferringProviderFaker(provider.Id).Generate();
        var facility = new ClaimFacilityFaker(provider.Id).Generate();
        var claimBillingDetail = new ClaimBillingDetailFaker(provider.Id, defaultBillingProfile).Generate();
        var serviceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate();
        var claimPolicy = new ClaimContactInsurancePolicyFaker(provider.Id, contact.Id, policy)
            .RuleFor(s => s.Address, x => null)
            .RuleFor(x => x.PayerPhoneNumberDetails, x => null)
            .Generate();

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(claimBillingDetail)
            .WithFacility(facility)
            .WithClient(claimClient)
            .WithContactInsurancePolicy(claimPolicy)
            .WithRenderingProviders([renderingProvider])
            .WithDiagnosticCodes(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders([referringProvider])
            .WithServiceLines([serviceLine])
            .RuleFor(x => x.SubmissionMethod, ClaimSubmissionMethod.Manual)
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/validate"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var validationError = await response.Content.ReadAsAsync<ValidationError>();
        validationError.Should().NotBeNull();
        validationError.Code.Should().Be(Errors.InsuranceClaimFailedToValidateCode);
        validationError.Details.Should().Be(Errors.InsuranceClaimFailedToValidateDetails);
        var errors = JsonConvert.DeserializeObject<FieldValidationError[]>(validationError.AdditionalDataJson);
        errors.Should().NotBeNull();
        errors.Length.Should().BeGreaterThan(0);

        var fieldNameErrors = new List<string>
        {
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.Address)}",
            $"{nameof(InsuranceClaimUSProfessional.ContactInsurancePolicy)}.{nameof(ClaimContactInsurancePolicy.PayerPhoneNumberDetails)}",
        };

        foreach (var error in errors)
        {
            error.Should().NotBeNull();
            error.FieldName.Should().NotBeNullOrEmpty();
            var isErrorInList = fieldNameErrors.Contains(error.FieldName);
            fieldNameErrors.Should().Contain(error.FieldName);
            error.ErrorMessage.Should().NotBeNullOrEmpty();
        }

        // Since there are validation errors, the status should remain Draft
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Draft);
    }

    [Fact]
    public async Task ValidateClaim_ShouldNotValidatePayerPhoneAndAddressIfElectronicSubmission()
    {
        var (_, provider, token) = await SetupProviderAndLogin();

        var faker = new Faker();
        var contact = new ContactFaker(provider.Id)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        var memberContact = new ContactFaker(provider.Id)
            .RuleFor(x => x.Address, x => null)
            .Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, memberContact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        var claimClient = new ClaimClientFaker(provider.Id, contact).Generate();
        var renderingProvider = new ClaimRenderingProviderFaker(provider.Id, staffMember).Generate();
        var referringProvider = new ClaimReferringProviderFaker(provider.Id).Generate();
        var facility = new ClaimFacilityFaker(provider.Id).Generate();
        var claimBillingDetail = new ClaimBillingDetailFaker(provider.Id, defaultBillingProfile).Generate();
        var serviceLine = new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate();
        var claimPolicy = new ClaimContactInsurancePolicyFaker(provider.Id, contact.Id, policy)
            .RuleFor(s => s.Address, x => null)
            .RuleFor(x => x.PayerPhoneNumberDetails, x => null)
            .Generate();

        var detail = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(claimBillingDetail)
            .WithFacility(facility)
            .WithClient(claimClient)
            .WithContactInsurancePolicy(claimPolicy)
            .WithRenderingProviders([renderingProvider])
            .WithDiagnosticCodes(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders([referringProvider])
            .WithServiceLines([serviceLine])
            .RuleFor(x => x.SubmissionMethod, ClaimSubmissionMethod.Electronic)
            .Generate();

        DataContext.InsuranceClaims.Add(detail.ToClaimHeaderDataModel());
        DataContext.InsuranceClaimsUSProfessional.Add(detail.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/{detail.Id}/us-professional/validate"
            )
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional.AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == detail.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(ClaimStatus.Validated);
        dbClaim.ClaimHeader.StatusReason.Should().BeNullOrEmpty();

        var historyDetail = ClaimHistoryModel.Create(detail);
        historyDetail.Status = ClaimStatus.Validated;
        await CheckHistoryRecordExists(
            detail.Id,
            HistoryAction.ClaimStatusChanged,
            new ClaimStatusChangedHistoryActionDetail(historyDetail)
        );
    }
}
