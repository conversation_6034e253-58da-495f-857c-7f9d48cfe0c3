using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Models.Pagination;
using System.Data.Entity;
using System.Net;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.component.Builders;

namespace tests.component.Features.Insurance;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class ContactInsuranceSelfPayTests(ComponentTestFixture fixture) : BaseTestClass(fixture)
{
    [Fact]
    public async Task GetSelfPay_WhenInsuranceIsEnabled_ShouldReturnServiceSelfPay()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy.Id
            )
            .RuleFor(x => x.Copay, 52)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?amount={100}&sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        result.TotalCount.Should().Be(1);

        var firstItem = result.Items.First();
        firstItem.ProviderId.Should().Be(ProviderId);
        firstItem.ContactId.Should().Be(contact.Id);
        firstItem.ServiceId.Should().Be(providerItem.Id);
        firstItem.PolicyId.Should().Be(policy.Id);
        firstItem.SelfPay.Should().Be(serviceCoverage.Copay);
    }

    [Fact]
    public async Task GetSelfPay_WhenServiceCoverageIsEnabledAndNoCopay_ShouldReturnPolicyCoPay()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .RuleFor(s => s.Copay, 50)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy.Id
            )
            .RuleFor(x => x.Copay, (decimal?)null)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?amount={100}&sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        var firstItem = result.Items.First();
        firstItem.SelfPay.Should().Be(policy.Copay);
    }

    [Fact]
    public async Task GetSelfPay_WhenCoinsuranceIsSet_ShouldCalculateSelfPay()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy.Id
            )
            .RuleFor(x => x.Copay, (decimal?)null)
            .RuleFor(x => x.Coinsurance, 0.1m)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var amount = 100;

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?amount={amount}&sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        var firstItem = result.Items.First();

        var expectedSelfPayAmount = amount * serviceCoverage.Coinsurance.Value;
        firstItem.SelfPay.Should().Be(expectedSelfPayAmount);
    }

    [Fact]
    public async Task GetSelfPay_WhenCopayIsZero_ShouldReturnZeroSelfPay()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy.Id
            )
            .RuleFor(x => x.Copay, 0m)
            .RuleFor(x => x.Coinsurance, (decimal?)null)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var amount = 100;

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?amount={amount}&sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        var firstItem = result.Items.First();

        firstItem.SelfPay.Should().Be(serviceCoverage.Copay.Value);
    }

    [Fact]
    public async Task GetSelfPay_WhenCoinsuranceIsZero_ShouldReturnZeroSelfPay()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy.Id
            )
            .RuleFor(x => x.Copay, (decimal?)null)
            .RuleFor(x => x.Coinsurance, 0m)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var amount = 100;

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?amount={amount}&sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        var firstItem = result.Items.First();

        var expectedSelfPayAmount = amount * serviceCoverage.Coinsurance.Value;
        firstItem.SelfPay.Should().Be(expectedSelfPayAmount);
    }

    [Fact]
    public async Task GetSelfPay_WhenMultiplePolicies_ShouldReturnLowestSelfPayFirst()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy1 = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy1.ToDataModel());

        var policy2 = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Secondary)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy2.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage1 = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy1.Id
            )
            .RuleFor(x => x.Copay, 25)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage1);

        var serviceCoverage2 = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy2.Id
            )
            .RuleFor(x => x.Copay, 50)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage2);

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?amount=100&sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        var firstItem = result.Items.First();
        firstItem.PolicyId.Should().Be(policy1.Id);
        firstItem.SelfPay.Should().Be(serviceCoverage1.Copay);
    }

    [Fact]
    public async Task GetSelfPay_WhenNoAmount_ShouldUseServicePrice()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .RuleFor(s => s.Copay, (decimal?)null)
            .RuleFor(s => s.Coinsurance, (decimal?)null)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .RuleFor(x => x.Price, 200)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy.Id
            )
            .RuleFor(x => x.Copay, (decimal?)null)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        var firstItem = result.Items.First();
        firstItem.SelfPay.Should().Be(providerItem.Price);
    }

    [Fact]
    public async Task GetSelfPay_WhenCopayIsLargerThanAmount_ShouldUseTheAmount()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy.Id
            )
            .RuleFor(x => x.Copay, 200)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var amount = 100;

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?amount={amount}&sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        var firstItem = result.Items.First();
        firstItem.SelfPay.Should().Be(amount);
    }

    [Fact]
    public async Task GetSelfPay_WhenCoinsuranceIsLargerThanAmount_ShouldUseTheAmount()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy.Id
            )
            .RuleFor(x => x.Copay, (decimal?)null)
            .RuleFor(x => x.Coinsurance, 2) // This means 200%
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var amount = 100;

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?amount={amount}&sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        var firstItem = result.Items.First();
        firstItem.SelfPay.Should().Be(amount);
    }

    [Fact]
    public async Task GetSelfPay_WhenServiceCoverageIsDisabled_ShouldReturnEmpty()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy.Id
            )
            .RuleFor(x => x.Copay, 50)
            .RuleFor(x => x.Enabled, false)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        result.TotalCount.Should().Be(0);
    }

    [Fact]
    public async Task GetSelfPay_WhenInsuranceIsDisabled_ShouldReturnEmpty()
    {
        var contact = new ContactFaker(ProviderId).Generate();
        DataContext.Contacts.Add(contact.ToDataModel());

        var contactInsuranceSettings = new ContactInsuranceSettingsFaker(
                ProviderId,
                contact.Id
            )
            .Generate();
        DataContext.ContactInsuranceSettings.Add(contactInsuranceSettings.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(ProviderId).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy.Id
            )
            .RuleFor(x => x.Copay, 50)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        result.TotalCount.Should().Be(0);
    }

    [Fact]
    public async Task GetSelfPay_WhenPolicyIsArchived_ShouldReturnEmpty()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Archived)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
                providerItem.Id,
                policy.Id
            )
            .RuleFor(x => x.Copay, 50)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        result.TotalCount.Should().Be(0);
    }

    [Fact]
    public async Task GetSelfPay_WhenOutsidePolicyDate_ShouldReturnEmpty()
    {
        var (contact, payer) = await SetupContactAndPayer();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id, payer)
            .RuleFor(s => s.Status, InsurancePolicyStatus.Verified)
            .RuleFor(s => s.InsuranceType, InsuranceType.Primary)
            .RuleFor(s => s.PolicyStartDate, DateTime.Now.AddDays(-5).ToDateOnly())
            .RuleFor(s => s.PolicyEndDate, DateTime.Now.AddDays(-1).ToDateOnly())
            .Generate();
        DataContext.ContactInsurancePolicies.Add(policy.ToDataModel());

        var providerItem = new ProviderItemFaker(ProviderId)
            .Generate();
        DataContext.Items.Add(providerItem.ToDataModel());

        var serviceCoverage = new InsuranceServiceCoverageDataModelFaker(
            providerItem.Id,
            policy.Id
        )
            .RuleFor(x => x.Copay, 50)
            .Generate();
        DataContext.InsuranceServiceCoverages.Add(serviceCoverage);

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(
            HttpMethod.Get, $"api/providers/{ProviderId}/contacts/{contact.Id}/insurance/self-pay?sessionDate={DateTime.Now.ToDateOnly():o}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactInsuranceSelfPay>>();
        result.TotalCount.Should().Be(0);
    }

    private async Task<(Contact contact, ProviderInsurancePayer payer)> SetupContactAndPayer()
    {
        var contact = new ContactFaker(ProviderId).Generate();
        DataContext.Contacts.Add(contact.ToDataModel());

        var contactInsuranceSettings = new ContactInsuranceSettingsFaker(
                ProviderId,
                contact.Id
            )
            .WithInsuranceEnabled()
            .Generate();
        DataContext.ContactInsuranceSettings.Add(contactInsuranceSettings.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(ProviderId).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        await DataContext.SaveChangesAsync();

        return (contact, payer);
    }
}
