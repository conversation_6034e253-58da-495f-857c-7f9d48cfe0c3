using System.Threading;
using carepatron.core.Application.Billables.Models;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Invoices.Extensions;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Application.Refunds.Models;
using carepatron.core.Application.Workspace.Providers.Models;
using carepatron.core.Common;
using carepatron.core.Models.Invoices;
using carepatron.core.Models.Pagination;
using carepatron.core.Models.Payments.Models;
using carepatron.core.Models.Permissions;
using carepatron.core.Repositories.Payments;
using carepatron.infra.sql.Models.BillableItem;
using carepatron.infra.sql.Models.Contact;
using carepatron.infra.sql.Models.Permissions;
using Microsoft.EntityFrameworkCore;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Extensions;
using tests.component.Builders;
using tests.component.Extensions;
using Payment = carepatron.core.Application.Payments.Models.Payment;

namespace tests.component.Features.Payments;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class GetPaymentsTests(ComponentTestFixture testFixture) : BaseTestClass(testFixture)
{
    public static TheoryData<DateTime?, DateTime?, int> DateRanges = new()
    {
        { null, null, 1 },
        { DateTime.UtcNow.AddDays(-1), null, 1 },
        { null, DateTime.UtcNow.AddDays(1), 1 },
        { DateTime.UtcNow.AddDays(1), null, 0 },
        { null, DateTime.UtcNow.AddDays(-1), 0 },
        { DateTime.UtcNow.AddDays(-1), DateTime.UtcNow.AddDays(1), 1 },
    };

    [Theory]
    [MemberData(nameof(DateRanges))]
    public async Task GetPayments_ShouldFilter_By_Date(DateTime? fromDate, DateTime? toDate, int expectedCount)
    {
        var (contact1, paidInvoice, payment, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData();

        await DataContext.AddRangeAsync(contact1.ToDataModel(), paidInvoice.ToDataModel(), payment.ToDataModel(), paymentIntent.ToDataModel(), invoiceBillable, paymentAllocation.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/contacts/{contact1.Id}/payments?fromDate={fromDate:yyyy-MM-ddTHH:mm:ssZ}&toDate={toDate:yyyy-MM-ddTHH:mm:ssZ}")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactPayment>>();
        result.Items.Should().HaveCount(expectedCount);
    }


    public static TheoryData<string, string, int> PaymentSearchFilters = new()
    {
        { "ref", "reference-001", 1 },
        { "reference-002", "reference-002", 1 },
        { "reference-004", "reference-003", 0 }
    };

    [Theory]
    [MemberData(nameof(PaymentSearchFilters))]
    public async Task GetPayments_WhenSearchTermIsProvided_OnlyReturnsMatchingReference(string searchTermQuery, string reference, int expectedCount)
    {
        var (contact1, paidInvoice, payment, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData(null, null, reference);

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/contacts/{contact1.Id}/payments?searchTerm={searchTermQuery}")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactPayment>>();
        result.Items.Should().HaveCount(expectedCount);
    }

    public static TheoryData<string[]> PaymentProviderFilters = new()
    {
        { new string[] { "Stripe" } },
        { new string[] { "Manual" } },
        { new string[] { "Stripe", "Manual" } },
        { new string[] { "Stripe", "Other" } }
    };

    [Theory]
    [MemberData(nameof(PaymentProviderFilters))]
    public async Task GetPayments_ShouldFilter_By_PaymentProviders(string[] paymentProviders)
    {
        var (contact1, paidInvoice, payment, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData(paymentProviders.First());
        var (contact2, paidInvoice2, payment2, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData("Other");

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            contact2.ToDataModel(),
            paidInvoice2.ToDataModel(),
            payment2.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var paymentProviderQuery = string.Join("&paymentProvider=", paymentProviders);
        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/contacts/{contact1.Id}/payments?paymentProvider={paymentProviderQuery}")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactPayment>>();
        result.Items.All(x => paymentProviders.Contains(x.PaymentProvider)).Should().BeTrue();
    }

    [Fact]
    public async Task GetPayments_ShouldFilter_Unallocated()
    {
        var (contact1, paidInvoice, allocatedPayment, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData();
        var (_contact1, paidInvoice2, unallocatedPayment, paymentIntent2, invoiceBillable2, unallocatedPaymentAllocation) = CreateTestPaymentData("Stripe", contact1);
        unallocatedPaymentAllocation.Amount = 50;

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            paidInvoice.ToDataModel(),
            allocatedPayment.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            unallocatedPayment.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            unallocatedPaymentAllocation.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/contacts/{contact1.Id}/payments?isUnallocated=true")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactPayment>>();
        result.Items.Count.Should().Be(1);
        result.Items.First().Id.Should().Be(unallocatedPayment.Id);
    }
    
    [Fact]
    public async Task GetPayments_WithTrashedPayment_ShouldOnlyShowActivePayments()
    {
        var (contact, paidInvoice, activePayment, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData();
        var (_, paidInvoice2, trashedPayment, paymentIntent2, invoiceBillable2, unallocatedPaymentAllocation) = CreateTestPaymentData(contact: contact);
        
        var trashedPaymentDataModel = trashedPayment.ToDataModel();
        trashedPaymentDataModel.DeletedAtUtc = DateTime.UtcNow;

        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            paidInvoice.ToDataModel(),
            activePayment.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            trashedPaymentDataModel,
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            unallocatedPaymentAllocation.ToDataModel()
        );
        
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, ContactPaymentsUrl(contact.Id))
            .WithBearerAuthorization(IdentityToken)
            .Create();
        
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactPayment>>();

        result.Items.Should().ContainSingle();
        result.Items.Single().Id.Should().Be(activePayment.Id);
    }

    [Theory]
    [InlineData(PaymentTypes.Card, PaymentTypes.Card, 1)]
    [InlineData(PaymentTypes.CreditBalance, PaymentTypes.CreditBalance, 1)]
    [InlineData(PaymentTypes.Cash, PaymentTypes.Cash, 3)]
    [InlineData("Alipay", "Other", 1)]
    public async Task ListPayments_FiltersByType(string type, string queryType, int expectedCount)
    {
        var (contact1, paidInvoice, payment1, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData(PaymentProviders.Manual, null, null, null, null, type);
        var (contact2, paidInvoice2, payment2, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData();
        var (contact3, paidInvoice3, payment3, paymentIntent3, invoiceBillable3, paymentAllocation3) = CreateTestPaymentData();

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            contact2.ToDataModel(),
            contact3.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment1.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            payment2.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel(),
            paidInvoice3.ToDataModel(),
            payment3.ToDataModel(),
            paymentIntent3.ToDataModel(),
            invoiceBillable3,
            paymentAllocation3.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/payments?contactIds={contact1.Id}&contactIds={contact2.Id}&contactIds={contact3.Id}&type={queryType}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<PaymentListEntry>>();
        result.Items.Count.Should().Be(expectedCount);

        foreach (var item in result.Items)
        {
            item.Type.Should().Be(type);
        }
    }

    [Theory]
    [InlineData(PaymentProviders.Manual, PaymentProviders.Manual, 1)]
    [InlineData(PaymentProviders.Stripe, PaymentProviders.Stripe, 3)]
    [InlineData(PaymentProviders.Stripe, PaymentProviders.Manual, 0)]
    public async Task ListPayments_FiltersByPaymentProvider(string paymentProvider, string queryPaymentProvider, int expectedCount)
    {
        var (contact1, paidInvoice, payment1, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData(paymentProvider);
        var (contact2, paidInvoice2, payment2, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData();
        var (contact3, paidInvoice3, payment3, paymentIntent3, invoiceBillable3, paymentAllocation3) = CreateTestPaymentData();

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            contact2.ToDataModel(),
            contact3.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment1.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            payment2.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel(),
            paidInvoice3.ToDataModel(),
            payment3.ToDataModel(),
            paymentIntent3.ToDataModel(),
            invoiceBillable3,
            paymentAllocation3.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/payments?contactIds={contact1.Id}&contactIds={contact2.Id}&contactIds={contact3.Id}&paymentProvider={queryPaymentProvider}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<PaymentListEntry>>();
        result.Items.Count.Should().Be(expectedCount);

        foreach (var item in result.Items)
        {
            item.PaymentProvider.Should().Be(paymentProvider);
        }
    }


    [Theory]
    [InlineData("amount=[gt]100&amount=[lt]200", 150, 1)]
    [InlineData("amount=[gt]200", 201, 1)]
    [InlineData("amount=[lt]200", 100, 3)]
    [InlineData("amount=[eq]150", 150, 1)]
    [InlineData("amount=150", 150, 1)]
    public async Task ListPayments_FiltersByAmount(string queryParam, decimal amount, int expectedCount)
    {
        var (contact1, paidInvoice, payment1, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData(null, null, null, null, null, null, amount);
        var (contact2, paidInvoice2, payment2, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData();
        var (contact3, paidInvoice3, payment3, paymentIntent3, invoiceBillable3, paymentAllocation3) = CreateTestPaymentData();

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            contact2.ToDataModel(),
            contact3.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment1.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            payment2.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel(),
            paidInvoice3.ToDataModel(),
            payment3.ToDataModel(),
            paymentIntent3.ToDataModel(),
            invoiceBillable3,
            paymentAllocation3.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/payments?{queryParam}&contactIds={contact1.Id}&contactIds={contact2.Id}&contactIds={contact3.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<PaymentListEntry>>();
        result.Items.Count.Should().Be(expectedCount);
    }

    public static TheoryData<string?, DateTime, int> PaymentDates = new()
    {
        { null, DateTime.UtcNow.AddDays(1), 0 },
        { "desc(paymentDate)", DateTime.UtcNow.AddDays(1), 0 },
        { "desc(paymentDate)", DateTime.UtcNow.AddDays(-1), 2 },
        { "asc(paymentDate)", DateTime.UtcNow.AddDays(1), 2 },
        { "asc(paymentDate)", DateTime.UtcNow.AddDays(-1), 0 }
    };

    [Theory]
    [MemberData(nameof(PaymentDates))]
    public async Task ListPayments_SortsByPaymentDate(string sortQuery, DateTime paymentDate, int expectedIndex)
    {
        var (contact1, paidInvoice, payment1, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData(null, null, null, null, paymentDate);
        var (contact2, paidInvoice2, payment2, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData();
        var (contact3, paidInvoice3, payment3, paymentIntent3, invoiceBillable3, paymentAllocation3) = CreateTestPaymentData();

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            contact2.ToDataModel(),
            contact3.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment1.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            payment2.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel(),
            paidInvoice3.ToDataModel(),
            payment3.ToDataModel(),
            paymentIntent3.ToDataModel(),
            invoiceBillable3,
            paymentAllocation3.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var url = $"/api/providers/{ProviderId}/payments?contactIds={contact1.Id}&contactIds={contact2.Id}&contactIds={contact3.Id}";

        if (sortQuery != null)
        {
            url += $"&sort={sortQuery}";
        }

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, url)
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<PaymentListEntry>>();
        result.Items[expectedIndex].Id.Should().Be(payment1.Id);
    }


    [Fact]
    public async Task ListPayments_WhenStaffMemberHasAssignedPermission_OnlyReturnAssignedClientPayments()
    {
        var person = new PersonFaker()
            .Generate();
        var staff = new ProviderStaffFaker(ProviderId, person.Id, ProviderRole.Staff).Generate().ToDataModel();
        var permissions = new ProviderPermissionsDataModel
        {
            ProviderId = ProviderId,
            PersonId = person.Id,
            InvoicesView = StandardPermission.Assigned
        };

        var assignedContact = new ContactFaker()
            .WithAssignedStaff(person)
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate();

        var contactAssignment = new ContactAssignedStaffDataModel
        {
            ContactId = assignedContact.Id,
            PersonId = person.Id
        };

        var (contact1, paidInvoice, payment1, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData(null, assignedContact);
        var (contact2, paidInvoice2, payment2, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData();
        var (contact3, paidInvoice3, payment3, paymentIntent3, invoiceBillable3, paymentAllocation3) = CreateTestPaymentData();

        await DataContext.AddRangeAsync(
            person.ToDataModel(),
            staff,
            permissions,
            contact1.ToDataModel(),
            contact2.ToDataModel(),
            contact3.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment1.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            payment2.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel(),
            paidInvoice3.ToDataModel(),
            payment3.ToDataModel(),
            paymentIntent3.ToDataModel(),
            invoiceBillable3,
            paymentAllocation3.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"/api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(person.PersonAsJwtToken())
            .Create();
        var response = await ClientApi.SendAsync(request);

        var result = await response.Content.ReadAsAsync<PaginatedResult<PaymentListEntry>>();
        result.Items.Count.Should().Be(1);
        result.Items.First().ContactId.Should().Be(assignedContact.Id);
    }

    [Fact]
    public async Task ListPayments_FiltersByStatus()
    {
        var (contact1, paidInvoice, payment1, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData(null, null, null, PayoutStatus.InTransit);
        var (contact2, paidInvoice2, payment2, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData();
        var (contact3, paidInvoice3, payment3, paymentIntent3, invoiceBillable3, paymentAllocation3) = CreateTestPaymentData(null, null, null, PayoutStatus.Pending);

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            contact2.ToDataModel(),
            contact3.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment1.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            payment2.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel(),
            paidInvoice3.ToDataModel(),
            payment3.ToDataModel(),
            paymentIntent3.ToDataModel(),
            invoiceBillable3,
            paymentAllocation3.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/payments?status={PayoutStatus.InTransit}&status={PayoutStatus.Pending}&contactIds={contact1.Id}&contactIds={contact2.Id}&contactIds={contact3.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<PaymentListEntry>>();
        result.Items.Count.Should().Be(2);
        var resultIds = result.Items.Select(x => x.Id).ToArray();
        resultIds.Should().Contain(payment1.Id);
        resultIds.Should().Contain(payment3.Id);
    }

    [Fact]
    public async Task ListPayments_FiltersBySearchTerm_ReturnsPaymentsMatchingTerm()
    {
        var (contact1, paidInvoice, payment1, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData();
        var (contact2, paidInvoice2, payment2, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData();

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            contact2.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment1.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            payment2.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/payments?searchTerm={payment1.Reference}&contactIds={contact1.Id}&contactIds={contact2.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<PaymentListEntry>>();
        result.Items.Count.Should().Be(1);
        result.Items.First().Id.Should().Be(payment1.Id);
    }

    [Fact]
    public async Task ListPayments_FiltersByDate()
    {
        var (contact1, paidInvoice, payment1, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData(null, null, null, null, DateTime.UtcNow.AddDays(-14));
        var (contact2, paidInvoice2, payment2, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData();
        var (contact3, paidInvoice3, payment3, paymentIntent3, invoiceBillable3, paymentAllocation3) = CreateTestPaymentData(null, null, null, null, DateTime.UtcNow.AddDays(-7));

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            contact2.ToDataModel(),
            contact3.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment1.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            payment2.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel(),
            paidInvoice3.ToDataModel(),
            payment3.ToDataModel(),
            paymentIntent3.ToDataModel(),
            invoiceBillable3,
            paymentAllocation3.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/payments?fromDate={DateTime.UtcNow.AddDays(-10):yyyy-MM-ddTHH:mm:ssZ}&toDate={DateTime.UtcNow.AddDays(30):yyyy-MM-ddTHH:mm:ssZ}&contactIds={contact1.Id}&contactIds={contact2.Id}&contactIds={contact3.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<PaymentListEntry>>();
        result.Items.Count.Should().Be(2);

        var expectedPaymentIds = new Guid[] { payment2.Id, payment3.Id };
        foreach (var item in result.Items)
        {
            expectedPaymentIds.Should().Contain(item.Id);
        }
    }

    [Fact]
    public async Task ListPayments_FiltersByContactIds()
    {
        var (contact1, paidInvoice, payment1, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData();
        var (contact2, paidInvoice2, payment2, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData();
        var (contact3, paidInvoice3, payment3, paymentIntent3, invoiceBillable3, paymentAllocation3) = CreateTestPaymentData();

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            contact2.ToDataModel(),
            contact3.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment1.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            payment2.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel(),
            paidInvoice3.ToDataModel(),
            payment3.ToDataModel(),
            paymentIntent3.ToDataModel(),
            invoiceBillable3,
            paymentAllocation3.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/payments?contactIds={contact1.Id}&contactIds={contact2.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<PaymentListEntry>>();
        result.Items.Count.Should().Be(2);

        var expectedPaymentIds = new Guid[] { payment1.Id, payment2.Id };
        foreach (var item in result.Items)
        {
            expectedPaymentIds.Should().Contain(item.Id);
        }
    }

    [Fact]
    public async Task ListPayments_WhenOffsetIsProvided_ShouldSkipPreviousPayments()
    {
        var (contact1, paidInvoice, payment1, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData();
        var (contact2, paidInvoice2, payment2, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData();

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            contact2.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment1.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            payment2.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/payments?offset=2&contactIds={contact1.Id}&contactIds={contact2.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<PaymentListEntry>>();
        result.Items.Count.Should().Be(0);
        result.Pagination.HasMore.Should().BeFalse();
    }
    
    [Fact]
    public async Task ListPayments_WithTrashedPayments_ShouldOnlyShowActivePayments()
    {
        var (contact, paidInvoice, activePayment, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData();
        var (_, paidInvoice2, trashedPayment, paymentIntent2, invoiceBillable2, paymentAllocation2) = CreateTestPaymentData(contact: contact);

        var trashedPaymentDataModel = trashedPayment.ToDataModel();
        trashedPaymentDataModel.DeletedAtUtc = DateTime.UtcNow;
        
        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            paidInvoice.ToDataModel(),
            activePayment.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            trashedPaymentDataModel,
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            paymentAllocation2.ToDataModel()
        );
        
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, PaymentListUrl("contactIds={contact.Id}"))
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<PaymentListEntry>>();
        
        result.Items.Should().ContainSingle();
        result.Items.Single().Id.Should().Be(activePayment.Id);
    }

    private (Contact contact,
        Invoice invoice,
        Payment payment,
        PaymentIntent paymentIntent,
        BillableDataModel billable,
        PaymentAllocation paymentAllocation)
        CreateTestPaymentData(string? paymentProvider = PaymentProviders.Stripe,
            Contact? contact = null,
            string? reference = null,
            PayoutStatus? payoutStatus = PayoutStatus.Paid,
            DateTime? paymentDate = null,
            string? paymentType = null,
            decimal? chargeAmount = null
        )
    {
        var contact1 = contact ?? new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate();

        var invoiceBillableItem = new BillableItemFaker(ProviderId, contact1.Id)
            .RuleFor(x => x.Price, 100)
            .RuleFor(x => x.ServiceId, (Guid?)null)
            .Generate()
            .ToDataModel();

        var invoiceBillable = new BillableDataModelFaker(ProviderId, contact1.Id)
            .RuleFor(x => x.Type, BillableType.Invoice)
            .RuleFor(x => x.BillableItems, new[] { invoiceBillableItem })
            .Generate();

        var paidLineItem = new InvoiceLineItemFaker()
            .WithBillableItemId(invoiceBillableItem.Id)
            .RuleFor(x => x.Price, invoiceBillableItem.Price)
            .RuleFor(x => x.Units, 1)
            .Generate();

        var paidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact1.Id)
            .RuleFor(x => x.Status, InvoiceStatus.Paid)
            .RuleFor(x => x.TaxExclusivePrice, 100)
            .Generate();

        paidInvoice.LineItems = new[] { paidLineItem };
        paidInvoice.CalculateTotals();

        var paymentIntent = new PaymentIntentFaker(ProviderId)
            .WithInvoice(paidInvoice)
            .RuleFor(x => x.Fee, 0)
            .Generate();

        var payment = new PaymentFaker(ProviderId)
            .WithPaymentIntent(paymentIntent)
            .RuleFor(x => x.PaymentDate, paymentDate ?? DateTime.UtcNow)
            .RuleFor(x => x.PayoutStatus, payoutStatus)
            .RuleFor(x => x.PaymentProvider, paymentProvider)
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.NZD)
            .RuleFor(x => x.Amount, CurrencyHandler.Get(CurrencyCodes.NZD).ToStripeAmount(paidInvoice.TaxExclusivePrice))
            .RuleFor(x => x.ChargeAmount, chargeAmount ?? paidInvoice.TaxExclusivePrice)
            .RuleFor(x => x.Type, paymentType ?? PaymentTypes.Cash)
            .Generate();

        payment.Reference = string.IsNullOrEmpty(reference) ? payment.Reference : reference;

        var paymentAllocation = new PaymentAllocationFaker(payment.ProviderId, payment.ContactId)
            .RuleFor(x => x.Amount, payment.ChargeAmount)
            .RuleFor(x => x.PaymentId, payment.Id)
            .RuleFor(x => x.BillableItemId, invoiceBillableItem.Id)
            .RuleFor(x => x.InvoiceLineItemId, paidLineItem.Id)
            .Generate();

        return (contact1, paidInvoice, payment, paymentIntent, invoiceBillable, paymentAllocation);
    }

    [Fact]
    public async Task GetPaymentDetail_Verify_AllProperties_ShouldSuccess()
    {
        var (contact1, paidInvoice, payment, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData();
        payment.RefundStatus = PaymentRefundStatus.PartiallyRefunded;
        paymentAllocation.Amount /= 2;
        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel()
        );
        var refund = new RefundFaker(ProviderId, contact1.Id)
            .RuleFor(x => x.PaymentId, payment.Id)
            .RuleFor(x => x.Status, RefundStatus.Succeeded)
            .RuleFor(x => x.Amount, payment.ChargeAmount / 2)
            .RuleFor(x => x.FailureReason, (string)null)
            .RuleFor(x => x.ReducePaymentAllocation, true)
            .Generate();
        await DataContext.AddAsync(refund);
        var contactCreditRepository = ResolveService<IContactCreditAdjustmentRepository>();
        var contactCredit = new ContactCreditAdjustmentFaker(ProviderId, contact1.Id)
            .RuleFor(x => x.Amount, 10)
            .RuleFor(x => x.CurrencyCode, payment.CurrencyCode)
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.InvoiceId, paidInvoice.Id)
            .RuleFor(x => x.PaymentId, payment.Id)
            .RuleFor(x => x.Type, ContactCreditAdjustmentTransactionType.Credit)
            .Generate();
        await DataContext.SaveChangesAsync();
        await contactCreditRepository.Create(contactCredit, default);
        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/contacts/{contact1.Id}/payments/{payment.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaymentDetail>();

        var availableToAllocate = payment.ChargeAmount - (payment.IsClientChargedFee ? payment.Fee : 0) - contactCredit.Amount - refund.Amount;
        result.Should().BeEquivalentTo(new
        {
            payment.Id,
            payment.PaymentDate,
            payment.Fee,
            payment.ChargeAmount,
            payment.IsClientChargedFee,
            PaymentMethod = payment.Type,
            payment.PaymentProvider,
            payment.CurrencyCode,
            payment.RefundStatus,
            Unallocated = availableToAllocate - paymentAllocation.Amount,
            AvailableToAllocate = availableToAllocate,
            payment.PayerName,
            payment.Reference,
            payment.IsBillingV2
        }, opt => opt.ExcludingMissingMembers().UsingDateTimePrecision(TimeSpan.FromSeconds(1)));

        result.Refunds.Should().BeEquivalentTo([
            new RefundReference
            {
                Id = refund.Id,
                Amount = refund.Amount,
                CurrencyCode = refund.CurrencyCode,
                Reason = refund.Reason,
                Status = refund.Status,
                FailureReason = refund.FailureReason,
                CreatedDateTimeUtc = refund.CreatedDateTimeUtc,
                ReducePaymentAllocation = refund.ReducePaymentAllocation
            }
        ], opt => opt.ExcludingMissingMembers().UsingDateTimePrecision(TimeSpan.FromSeconds(1)));
        result.IssuedCredits.Should().BeEquivalentTo([
            new ContactCreditAdjustment
            {
                Id = contactCredit.Id,
                Amount = contactCredit.Amount,
                CurrencyCode = contactCredit.CurrencyCode,
                Description = contactCredit.Description,
                IssuedDateUtc = contactCredit.IssuedDateUtc,
                ProviderId = contactCredit.ProviderId,
                ContactId = contactCredit.ContactId,
                InvoiceId = contactCredit.InvoiceId,
                PaymentId = contactCredit.PaymentId,
                Reason = contactCredit.Reason,
                Type = contactCredit.Type
            }
        ], opt => opt.ExcludingMissingMembers().UsingDateTimePrecision(TimeSpan.FromSeconds(1)));
        result.Allocations.Should().BeEquivalentTo([
            new PaymentAllocationDetail
            {
                PaymentId = payment.Id,
                BillableItemId = invoiceBillable.BillableItems.First().Id,
                ProviderId = payment.ProviderId,
                ContactId = payment.ContactId,
                Date = paidInvoice.LineItems.First().Date,
                Amount = invoiceBillable.BillableItems.First().Amount + invoiceBillable.BillableItems.First().TaxAmount,
                Paid = invoiceBillable.BillableItems.First().Paid,
                Allocated = paymentAllocation.Amount,
                Description = paidInvoice.LineItems.First().Description,
                Detail = paidInvoice.LineItems.First().Detail,
                ClaimLineId = null,
                Claim = null,
                InvoiceLineItemId = paidInvoice.LineItems.First().Id,
                Invoice = new InvoiceReference
                {
                    InvoiceId = paidInvoice.Id,
                    InvoiceStatus = paidInvoice.Status,
                    IssueDate = paidInvoice.IssueDate,
                    InvoiceNumber = paidInvoice.Number,
                    IssuedCredits = [contactCredit]
                }
            }
        ], opt => opt.ExcludingMissingMembers().UsingDateTimePrecision(TimeSpan.FromSeconds(1)));
    }

    [Fact]
    public async Task GetPaymentDetail_ForInsurance()
    {
        var contact = new ContactFaker(ProviderId).Generate();
        var item = new ProviderItemFaker().RuleFor(x => x.Price, 50).Generate();
        var billableItem = new BillableItemFaker(ProviderId, contact.Id)
            .RuleFor(x => x.Price, item.Price)
            .RuleFor(x => x.Amount, item.Price)
            .RuleFor(x => x.ServiceId, item.Id)
            .RuleFor(x => x.TaxAmount, 0)
            .RuleFor(x => x.TaxRate, 0)
            .RuleFor(x => x.SelfPayAmount, 0)
            .RuleFor(x => x.IsInsuranceEnabled, true)
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.USD)
            .Generate();
        var billable = new BillableFaker(ProviderId, contact.Id)
            .RuleFor(x => x.ContactId, contact.Id)
            .RuleFor(x => x.Items, [ billableItem ])
            .Generate();
        var payment = new PaymentFaker(ProviderId)
            .RuleFor(x => x.ContactId, contact.Id)
            .RuleFor(x => x.CurrencyCode, billable.CurrencyCode)
            .RuleFor(x => x.PaymentDate, DateTime.UtcNow)
            .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
            .RuleFor(x => x.PaymentProvider, PaymentProviders.Insurance)
            .RuleFor(x => x.Type, PaymentTypes.Insurance)
            .RuleFor(x => x.InvoiceId, (Guid?)null)
            .RuleFor(x => x.ChargeAmount, 200)
            .RuleFor(x => x.PayerType, PayerType.Insurance)
            .Generate();
        var claimLine = new ClaimServiceLineFaker(ProviderId, contact.Id)
            .RuleFor(x => x.BillableItemId, billableItem.Id)
            .RuleFor(x => x.Amount, billableItem.Amount)
            .Generate();
        var insuranceClaim = new USProfessionalClaimFaker(ProviderId)
            .RuleFor(x => x.ContactId, contact.Id)
            .RuleFor(x => x.ServiceLines, [claimLine])
            .Generate();
        var allocation = new PaymentAllocationFaker(ProviderId, contact.Id)
            .RuleFor(x => x.BillableItemId, billableItem.Id)
            .RuleFor(x => x.Amount, billableItem.SelfPayAmount)
            .RuleFor(x => x.PaymentId, payment.Id)
            .RuleFor(x => x.InvoiceLineItemId, (Guid?)null)
            .RuleFor(x => x.ClaimLineId, claimLine.Id)
            .Generate();

        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            payment.ToDataModel(),
            billable.ToDataModel(),
            insuranceClaim.ToClaimHeaderDataModel(),
            allocation.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/contacts/{contact.Id}/payments/{payment.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaymentDetail>();

        var dbBillable = await DataContext.Billables
            .AsNoTracking()
            .Include(x => x.BillableItems)
            .FirstOrDefaultAsync(x => x.Id == billable.Id);
        var dbBillableItem = dbBillable.BillableItems.First();

        var availableToAllocate = payment.ChargeAmount - (payment.IsClientChargedFee ? payment.Fee : 0);
        result.Should().BeEquivalentTo(new
        {
            payment.Id,
            payment.PaymentDate,
            payment.Fee,
            payment.ChargeAmount,
            payment.IsClientChargedFee,
            PaymentMethod = payment.Type,
            payment.PaymentProvider,
            payment.CurrencyCode,
            payment.RefundStatus,
            Unallocated = availableToAllocate - allocation.Amount,
            AvailableToAllocate = availableToAllocate,
            payment.IsBillingV2
        }, opt => opt.ExcludingMissingMembers().UsingDateTimePrecision(TimeSpan.FromSeconds(1)));

        result.Refunds.Should().BeEmpty();
        result.IssuedCredits.Should().BeEmpty();
        result.Allocations.Should().BeEquivalentTo([
            new PaymentAllocationDetail
            {
                PaymentId = payment.Id,
                BillableItemId = billableItem.Id,
                ProviderId = payment.ProviderId,
                ContactId = payment.ContactId,
                Date = dbBillableItem.Date,
                Amount = dbBillableItem.InsuranceAmount,
                Paid = dbBillableItem.InsurancePaid,
                Allocated = allocation.Amount,
                Description = dbBillableItem.Description,
                Detail = dbBillableItem.Detail,
                ClaimLineId = claimLine.Id,
                Claim = new BillableClaimReference()
                {
                    Id = insuranceClaim.Id,
                    Date = billableItem.Date,
                    Number =  insuranceClaim.Number,
                    Type = insuranceClaim.Type,
                    SubmissionMethod =  insuranceClaim.SubmissionMethod,
                    ClaimStatus =   insuranceClaim.Status
                }
            }
        ], opt => opt.ExcludingMissingMembers().UsingDateTimePrecision(TimeSpan.FromSeconds(1)));
    }


    [Fact]
    public async Task ListPayment_Verify_Properties()
    {
        var (contact1, paidInvoice, allocatedPayment, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData();
        var (_contact1, paidInvoice2, unallocatedPayment, paymentIntent2, invoiceBillable2, unallocatedPaymentAllocation) = CreateTestPaymentData("Stripe", contact1);
        unallocatedPaymentAllocation.Amount = 50;

        await DataContext.AddRangeAsync(
            contact1.ToDataModel(),
            paidInvoice.ToDataModel(),
            allocatedPayment.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            paidInvoice2.ToDataModel(),
            unallocatedPayment.ToDataModel(),
            paymentIntent2.ToDataModel(),
            invoiceBillable2,
            unallocatedPaymentAllocation.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/contacts/{contact1.Id}/payments?isUnallocated=true")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<PaginatedResult<ContactPayment>>();
        result.Items.Count.Should().Be(1);
        var resultPayment = result.Items.First();
        resultPayment.Should().BeEquivalentTo(new
        {
            Id = unallocatedPayment.Id,
            ProviderId = unallocatedPayment.ProviderId,
            ContactId = unallocatedPayment.ContactId,
            PaymentDate = unallocatedPayment.PaymentDate,
            Type = unallocatedPayment.Type,
            Amount = resultPayment.Amount,
            Fee = unallocatedPayment.Fee,
            IsClientChargedFee = unallocatedPayment.IsClientChargedFee,
            DisplayAmount = resultPayment.DisplayAmount,
            UnallocatedAmount = resultPayment.UnallocatedAmount,
            CurrencyCode = unallocatedPayment.CurrencyCode,
            PaymentProvider = unallocatedPayment.PaymentProvider,
            RefundStatus = unallocatedPayment.RefundStatus,
            Invoices = (InvoiceReference[])[new InvoiceReference
                {
                    InvoiceId = paidInvoice2.Id,
                    InvoiceStatus = paidInvoice2.Status,
                    IssueDate = paidInvoice2.IssueDate,
                    InvoiceNumber = paidInvoice2.Number,
                    IssuedCredits = [],
                }
            ],
            Refunds = Array.Empty<RefundReference>(),
            IssuedCredits = Array.Empty<InvoiceReference>(),
            IsBillingV2 = resultPayment.IsBillingV2,
        }, opt => opt.UsingSimpleDateTimePrecision());
    }

    [Fact]
    public async Task GetPayment_WithAllocations_ShouldCalculateAllocations()
    {
        // Ensure all payment models calculate the correct allocation amounts
        // Calculations are currently done across models, mappers and DB projections
        // so need to be done in a component test until consolidated.
        
        const decimal chargeAmount = 200M;
        const decimal paymentAllocationAmount = 50M;
        const decimal refundAmount = 25M;
        const decimal creditAmount = 10M;
        const decimal expectedUnallocatedAmount = chargeAmount - paymentAllocationAmount - refundAmount - creditAmount;

        var (payment, contact) = await SaveAllocatedPaymentData(
            chargeAmount: chargeAmount,
            paymentAllocationAmount: paymentAllocationAmount,
            refundAmount: refundAmount,
            creditAmount: creditAmount);
        
        var paymentRepository = ResolveService<IPaymentRepository>();
        
        var paymentResult = await paymentRepository.GetPayment(ProviderId, payment.Id);
        var paymentDetailResult = await paymentRepository.GetDetail(ProviderId, contact.Id, payment.Id);
        var contactPaymentResult = (await paymentRepository.GetPaymentEntries(ProviderId, contact.Id, null, false, null, null, null, 0, 100, CancellationToken.None))
            .Items.Single(x => x.Id == payment.Id);
        
        // Verify allocated calculations
        paymentResult.AvailableToAllocate.Should().Be(chargeAmount);
        contactPaymentResult.AvailableToAllocate.Should().Be(chargeAmount);
        // Verify Payment Detail. Note the property has different meanings in the detail model
        paymentDetailResult.AvailableToAllocate.Should().Be(chargeAmount - refundAmount - creditAmount);
        
        // Verify Unallocated calculations
        new[]
        {
            paymentResult.UnallocatedAmount, 
            contactPaymentResult.UnallocatedAmount,
            paymentDetailResult.Unallocated
        }.Should().AllBeEquivalentTo(expectedUnallocatedAmount);
    }

    private async Task<(Payment, Contact)> SaveAllocatedPaymentData(
        decimal chargeAmount,
        decimal paymentAllocationAmount,
        decimal refundAmount,
        decimal creditAmount)
    {
        var (contact, paidInvoice, payment, paymentIntent, invoiceBillable, paymentAllocation) = CreateTestPaymentData(
            chargeAmount: chargeAmount);
        
        payment.RefundStatus = PaymentRefundStatus.PartiallyRefunded;
        paymentAllocation.Amount = paymentAllocationAmount;
        
        var refund = new RefundFaker(ProviderId, contact.Id)
            .RuleFor(x => x.PaymentId, payment.Id)
            .RuleFor(x => x.Status, RefundStatus.Succeeded)
            .RuleFor(x => x.Amount, refundAmount)
            .RuleFor(x => x.FailureReason, (string)null)
            .RuleFor(x => x.ReducePaymentAllocation, true)
            .Generate();
        
        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            paidInvoice.ToDataModel(),
            payment.ToDataModel(),
            paymentIntent.ToDataModel(),
            invoiceBillable,
            paymentAllocation.ToDataModel(),
            refund
        );
        
        await DataContext.SaveChangesAsync();
        
        var contactCredit = new ContactCreditAdjustmentFaker(ProviderId, contact.Id)
            .RuleFor(x => x.Amount, creditAmount)
            .RuleFor(x => x.CurrencyCode, payment.CurrencyCode)
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.InvoiceId, paidInvoice.Id)
            .RuleFor(x => x.PaymentId, payment.Id)
            .RuleFor(x => x.Type, ContactCreditAdjustmentTransactionType.Credit)
            .Generate();

        await ResolveService<IContactCreditAdjustmentRepository>().Create(contactCredit);

        return (payment, contact);
    }

    private string ContactPaymentsUrl(Guid contactId, string queryString = null) => $"/api/providers/{ProviderId}/contacts/{contactId}/payments?{queryString}";
    
    private string PaymentListUrl(string queryString = null) => $"/api/providers/{ProviderId}/payments?{queryString}";
}