using Bogus;
using carepatron.api.Contracts.Requests.Invoices;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Application.Workspace.Providers.Models;
using carepatron.core.Common;
using carepatron.core.Models.Invoices;
using carepatron.core.Models.Permissions;
using carepatron.infra.sql.Models.Contact;
using carepatron.infra.sql.Models.Permissions;
using Microsoft.EntityFrameworkCore;
using System.Net;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Invoices.Extensions;
using carepatron.core.Localisation.SharedMessages;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Extensions;
using tests.component.Builders;
using tests.component.Extensions;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Billables.Models;
using carepatron.api.Contracts.Requests.Insurance;
using carepatron.core.Application.Insurance.Models;
using Payment = carepatron.core.Application.Payments.Models.Payment;
using carepatron.core.Application.History.Models;
using System.Text.Json;
using Newtonsoft.Json;

namespace tests.component.Features.Payments;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class CreatePaymentTests(ComponentTestFixture testFixture)
    : BaseTestClass(testFixture)
{
    [Fact]
    public async Task CreatePayment_Anonymous_ReturnsUnauthorized()
    {
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithPayload(new CreatePaymentRequest(Guid.NewGuid(), Guid.NewGuid(), Guid.NewGuid(), DateTime.UtcNow, PaymentTypes.Cash))
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Theory]
    [InlineData(StandardPermission.None, true, HttpStatusCode.Forbidden)]
    [InlineData(StandardPermission.Assigned, false, HttpStatusCode.Forbidden)]
    [InlineData(StandardPermission.Assigned, true, HttpStatusCode.OK)]
    [InlineData(StandardPermission.Everything, false, HttpStatusCode.OK)]
    public async Task CreatePayment_ValidatesPermissions(
        StandardPermission invoicesEditPermission,
        bool isContactAssigned,
        HttpStatusCode responseStatus)
    {
        var person = new PersonFaker().Generate().ToDataModel();
        var staff = new ProviderStaffFaker(ProviderId, person.Id, ProviderRole.Staff).Generate().ToDataModel();
        var permissions = new ProviderPermissionsDataModel
        {
            ProviderId = ProviderId,
            PersonId = person.Id,
            InvoicesEdit = invoicesEditPermission
        };

        var contact = new ContactFaker(ProviderId).Generate().ToDataModel();
        var invoice = new InvoiceFaker(ProviderId).WithContactId(contact.Id).Generate().ToDataModel();

        DataContext.AddRange(person, staff, permissions, contact, invoice);
        if (isContactAssigned)
        {
            DataContext.Add(new ContactAssignedStaffDataModel { ContactId = contact.Id, PersonId = person.Id });
        }

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(person.PersonAsJwtToken())
            .WithPayload(new CreatePaymentRequest(invoice.ProviderId, invoice.ContactId, invoice.Id, DateTime.UtcNow, PaymentTypes.Cash))
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(responseStatus);
    }

    [Fact]
    public async Task CreatePayment_ValidatesInvoiceProvider()
    {
        var provider = new ProviderFaker().Generate().ToDataModel();
        var contact = new ContactFaker(provider.Id).Generate().ToDataModel();
        var invoice = new InvoiceFaker(provider.Id).WithContactId(contact.Id).Generate().ToDataModel();

        DataContext.AddRange(provider, contact, invoice);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(new CreatePaymentRequest(invoice.ProviderId, invoice.ContactId, invoice.Id, DateTime.UtcNow, PaymentTypes.Cash))
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.NotFound);
    }

    [Theory]
    [InlineData(InvoiceStatus.Invalid, HttpStatusCode.BadRequest)]
    [InlineData(InvoiceStatus.Unpaid, HttpStatusCode.OK)]
    [InlineData(InvoiceStatus.Sent, HttpStatusCode.OK)]
    [InlineData(InvoiceStatus.Paid, HttpStatusCode.BadRequest)]
    [InlineData(InvoiceStatus.Void, HttpStatusCode.BadRequest)]
    [InlineData(InvoiceStatus.Processing, HttpStatusCode.BadRequest)]
    public async Task CreatePayment_ValidatesInvoiceStatus(InvoiceStatus invoiceStatus, HttpStatusCode responseStatus)
    {
        var contact = new ContactFaker(ProviderId).Generate().ToDataModel();
        var invoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .RuleFor(x => x.Status, invoiceStatus)
            .Generate().ToDataModel();

        await DataContext.AddRangeAsync(contact, invoice);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(new CreatePaymentRequest(invoice.ProviderId, invoice.ContactId, invoice.Id, DateTime.UtcNow, PaymentTypes.Cash))
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(responseStatus);
    }

    [Theory]
    [InlineData(PaymentIntentStatus.Unknown, HttpStatusCode.OK)]
    [InlineData(PaymentIntentStatus.Open, HttpStatusCode.OK)]
    [InlineData(PaymentIntentStatus.Processing, HttpStatusCode.BadRequest)]
    // TODO: The unique index ix_payment_intents_invoice_id prevents creating a new payment intent
    //   right now we allow to have multiple payment intent over invoice, not sure if this is related
    //   user can cancel the payment and pay with credit
    [InlineData(PaymentIntentStatus.Canceled, HttpStatusCode.OK)]
    [InlineData(PaymentIntentStatus.Succeeded, HttpStatusCode.BadRequest)]
    [InlineData(PaymentIntentStatus.Failed, HttpStatusCode.OK)]
    public async Task CreatePayment_ValidatesPaymentIntentStatus(PaymentIntentStatus paymentIntentStatus, HttpStatusCode responseStatus)
    {
        var contact = new ContactFaker(ProviderId).Generate().ToDataModel();
        var invoice = new InvoiceFaker(ProviderId).WithContactId(contact.Id).Generate().ToDataModel();
        var paymentIntent = new PaymentIntentFaker(ProviderId)
            .WithInvoice(invoice)
            .RuleFor(x => x.Status, paymentIntentStatus)
            .Generate().ToDataModel();

        await DataContext.AddRangeAsync(contact, invoice, paymentIntent);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(new CreatePaymentRequest(invoice.ProviderId, invoice.ContactId, invoice.Id, DateTime.UtcNow, PaymentTypes.Cash))
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(responseStatus);
    }

    [Fact]
    public async Task CreatePayment_SavesPaymentAndCreditInvoice_AndPaymentAllocation()
    {
        var contact = new ContactFaker(ProviderId).Generate().ToDataModel();
        var invoice = new InvoiceFaker(ProviderId).WithContactId(contact.Id).Generate();

        foreach (var lineItem in invoice.LineItems)
        {
            lineItem.Units = 1;
            lineItem.Price = -50;
        }

        var billableItems = invoice.LineItems.Select(x =>
        {
            var billableItem = new BillableItemFaker(ProviderId, contact.Id).Generate();
            x.BillableItemId = billableItem.Id;
            return billableItem.ToDataModel();
        }).ToList();

        var billable = new BillableDataModelFaker(ProviderId, contact.Id)
            .RuleFor(x => x.BillableItems, billableItems)
            .Generate();

        await DataContext.AddRangeAsync(contact, invoice.CalculateTotals().ToDataModel(), billable);
        await DataContext.SaveChangesAsync();

        var requestPayload = new CreatePaymentRequest(invoice.ProviderId, invoice.ContactId, invoice.Id, DateTime.UtcNow, PaymentTypes.Cash);
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestPayload)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var paymentAmount = invoice.TaxExclusivePrice + invoice.TaxPrice;
        var stripeAmount = CurrencyHandler.Get(invoice.CurrencyCode).ToStripeAmount(paymentAmount);
        var truncatedPaymentDate = requestPayload.PaymentDate.Truncate(TimeSpan.TicksPerSecond);

        var result = await response.Content.ReadAsAsync<Payment>();
        result.Id.Should().NotBeEmpty();
        result.InvoiceId.Should().Be(invoice.Id);
        result.Amount.Should().Be(stripeAmount);
        result.ChargeAmount.Should().Be(paymentAmount);
        result.TransferAmount.Should().Be(paymentAmount);
        result.Fee.Should().Be(0);
        result.IsClientChargedFee.Should().BeFalse();
        result.CurrencyCode.Should().Be(invoice.CurrencyCode);
        result.PaymentProvider.Should().Be(PaymentProviders.CustomerBalance);
        result.Type.Should().Be(requestPayload.Type);
        result.PaymentDate.Should().Be(truncatedPaymentDate);

        var createdPayment = await DataContext.Payments.AsNoTracking()
            .Include(x => x.Allocations)
            .FirstOrDefaultAsync(x => x.InvoicePayment.InvoiceId == invoice.Id);
        createdPayment.Should().BeEquivalentTo(result, opt => opt.ExcludingMissingMembers());
        createdPayment.Allocations.Should().BeEquivalentTo(invoice.LineItems.Select(x => new { InvoiceLineItemId = x.Id, x.BillableItemId }));
        createdPayment.PaymentProvider.Should().Be(PaymentProviders.CustomerBalance);
        var updatedInvoice = await DataContext.Invoices.AsNoTracking().FirstOrDefaultAsync(x => x.Id == invoice.Id);
        updatedInvoice.Status.Should().Be(InvoiceStatus.Paid);
        updatedInvoice.PaymentDate.Should().Be(truncatedPaymentDate);

        var creditAdjustment = await DataContext.ContactCreditAdjustments.AsNoTracking().FirstOrDefaultAsync(x => x.ContactId == invoice.ContactId);
        creditAdjustment.Should().NotBeNull();
        creditAdjustment.Amount.Should().Be(invoice.Total * -1);
        creditAdjustment.Type.Should().Be(ContactCreditAdjustmentTransactionType.Credit);
        creditAdjustment.Reason.Should().Be(ContactCreditAdjustmentTransactionReason.CreditNote);
        var localizer = ResolveService<IStringLocalizer<SharedMessages>>();
        var expectedReason = localizer[nameof(SharedMessages.InvoiceCreditService_PaymentCredit), invoice.Number].Value;
        creditAdjustment.Description.Should().Be(expectedReason);
    }

    [Fact]
    public async Task CreatePayment_WithExistingAllocation_ShouldNotOverAllocate()
    {
        // Arrange
        var contact = new ContactFaker(ProviderId).Generate().ToDataModel();

        var paidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .RuleFor(x => x.Status, InvoiceStatus.Paid)
            .RuleFor(x => x.TaxExclusivePrice, 100)
            .Generate()
            .ToDataModel();

        var paymentIntent = new PaymentIntentFaker(ProviderId)
            .WithInvoice(paidInvoice)
            .Generate();

        var payment = new PaymentFaker(ProviderId)
            .WithPaymentIntent(paymentIntent)
            .RuleFor(x => x.PaymentDate, DateTime.UtcNow)
            .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
            .RuleFor(x => x.PaymentProvider, PaymentProviders.Stripe)
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.NZD)
            .Generate()
            .ToDataModel();

        var unpaidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .Generate()
            .ToDataModel();

        var billableItems = unpaidInvoice.InvoiceLineItems.Select(x =>
        {
            var billableItem = new BillableItemFaker(ProviderId, contact.Id).Generate();
            x.BillableItemId = billableItem.Id;
            return billableItem.ToDataModel();
        }).ToList();

        var billable = new BillableDataModelFaker(ProviderId, contact.Id)
            .RuleFor(x => x.BillableItems, billableItems)
            .Generate();

        // create allocation to unpaid invoice
        var paymentAllocation = new PaymentAllocationFaker(ProviderId, contact.Id)
            .RuleFor(x => x.BillableItemId, billableItems.First().Id)
            .RuleFor(x => x.InvoiceLineItemId, unpaidInvoice.InvoiceLineItems.First().Id)
            .RuleFor(x => x.Amount, unpaidInvoice.InvoiceLineItems.First().Price * unpaidInvoice.InvoiceLineItems.First().Units / 2)
            .RuleFor(x => x.PaymentId, payment.Id)
            .Generate()
            .ToDataModel();

        paidInvoice.DeletedAtUtc = DateTime.UtcNow;

        await DataContext.AddRangeAsync(contact,
            unpaidInvoice,
            billable,
            paidInvoice,
            paymentIntent.ToDataModel(),
            payment,
            paymentAllocation);

        await DataContext.SaveChangesAsync();

        // Act  
        var requestPayload = new CreatePaymentRequest(unpaidInvoice.ProviderId, unpaidInvoice.ContactId, unpaidInvoice.Id, DateTime.UtcNow, PaymentTypes.Cash);
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestPayload)
            .Create();

        // Assert
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var paymentAmount = unpaidInvoice.TaxExclusivePrice + unpaidInvoice.TaxPrice - paymentAllocation.Amount;

        var result = await response.Content.ReadAsAsync<Payment>();
        result.Id.Should().NotBeEmpty();
        result.InvoiceId.Should().Be(unpaidInvoice.Id);
        result.ChargeAmount.Should().Be(paymentAmount);
        result.Fee.Should().Be(0);
        result.IsClientChargedFee.Should().BeFalse();
        result.CurrencyCode.Should().Be(unpaidInvoice.CurrencyCode);
        result.PaymentProvider.Should().Be(PaymentProviders.Manual);
        result.Type.Should().Be(requestPayload.Type);

        var createdAllocationForLineItems = await DataContext.PaymentAllocations.AsNoTracking()
            .Where(x => x.PaymentId == result.Id).Include(paymentAllocationDataModel => paymentAllocationDataModel.InvoiceLineItem)
            .ToArrayAsync();

        createdAllocationForLineItems.Should().NotBeEmpty();

        foreach (var allocation in createdAllocationForLineItems)
        {
            var expectedCreatedAllocationAmountForLineItem = CurrencyHandler.Get(allocation.InvoiceLineItem.CurrencyCode)
                .Round(allocation.InvoiceLineItem.Price * allocation.InvoiceLineItem.Units * (1 + allocation.InvoiceLineItem.TaxRates.Sum(r => r.Rate) / 100));

            if (allocation.InvoiceLineItem.Id == paymentAllocation.InvoiceLineItemId)
            {
                expectedCreatedAllocationAmountForLineItem -= paymentAllocation.Amount;
            }

            allocation.Amount.Should().Be(expectedCreatedAllocationAmountForLineItem);
        }
    }

    [Theory]
    [InlineData(-80, -100, 100, -80)]
    [InlineData(50, -50, 50, 50)]
    public async Task CreatePayment_ForCredits_ShouldNotBeCutOff(decimal price1, decimal price2, decimal price3, decimal chargeAmount)
    {
        // Arrange
        var contact = new ContactFaker(ProviderId).Generate();

        var providerItems = new ProviderItemFaker()
            .Generate(3)
            .ToArray();

        providerItems[0].Price = price1;
        providerItems[1].Price = price2;
        providerItems[2].Price = price3;

        var task = new TaskFaker()
            .WithContacts([contact], TaskContactStatus.Attended)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.ItemsSnapshot, providerItems)
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var taskBillableItems = task.ItemsSnapshot.Select(item =>
            new BillableItemFaker(ProviderId, contact.Id)
                .RuleFor(x => x.Id, Guid.NewGuid)
                .RuleFor(x => x.Price, item.Price)
                .RuleFor(x => x.ServiceId, item.Id)
                .Generate()
                .ToDataModel()
            ).ToArray();

        var taskBillable = new BillableDataModelFaker(ProviderId, contact.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.TaskId, task.Id)
            .RuleFor(x => x.Type, BillableType.Task)
            .RuleFor(x => x.SyncToken, task.UpdatedDateTimeUtc.Ticks)
            .RuleFor(x => x.BillableItems, taskBillableItems)
            .Generate();

        var invoiceLineItems = taskBillableItems.Select(item =>
            {
                return new InvoiceLineItemFaker()
                    .RuleFor(x => x.BillableItemId, item.Id)
                    .RuleFor(x => x.Price, item.Price)
                    .RuleFor(x => x.Units, 1)
                    .Generate();
            }).ToArray();

        var unpaidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .RuleFor(x => x.LineItems, invoiceLineItems)
            .Generate()
            .ToDataModel();


        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            task,
            taskBillable,
            unpaidInvoice);

        await DataContext.SaveChangesAsync();

        // Act  
        var requestPayload = new CreatePaymentRequest(unpaidInvoice.ProviderId, unpaidInvoice.ContactId, unpaidInvoice.Id, DateTime.UtcNow, PaymentTypes.CreditBalance);
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestPayload)
            .Create();

        // Assert
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Payment>();
        result.ChargeAmount.Should().Be(chargeAmount);
        result.Id.Should().NotBeEmpty();
        result.InvoiceId.Should().Be(unpaidInvoice.Id);

        var dbPayment = await DataContext.Payments.AsNoTracking()
            .Include(x => x.Allocations)
            .FirstOrDefaultAsync(x => x.Id == result.Id);

        dbPayment.Should().NotBeNull();
        dbPayment.Allocations.Should().NotBeEmpty();
        dbPayment.Allocations.Count.Should().Be(3);
        dbPayment.Allocations.Sum(x => x.Amount).Should().Be(chargeAmount);

        foreach (var allocation in dbPayment.Allocations)
        {
            var lineItem = invoiceLineItems.FirstOrDefault(x => x.BillableItemId == allocation.BillableItemId);
            lineItem.Should().NotBeNull();
            allocation.Amount.Should().Be(lineItem.Price);
        }
    }

    [Fact]
    public async Task CreatePayment_WhenInvoiceIsFullyPaidByCredit_ShouldCreateAllocationForCreditsUsed()
    {
        // Arrange
        var contact = new ContactFaker(ProviderId).Generate();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.Price, 100)
            .Generate();

        var task = new TaskFaker()
            .WithContacts([contact], TaskContactStatus.Attended)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.ItemsSnapshot, [providerItem])
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var taskBillableItems = task.ItemsSnapshot.Select(item =>
            new BillableItemFaker(ProviderId, contact.Id)
                .RuleFor(x => x.Id, Guid.NewGuid)
                .RuleFor(x => x.Price, item.Price)
                .RuleFor(x => x.ServiceId, item.Id)
                .Generate()
                .ToDataModel()
            ).ToArray();

        var taskBillable = new BillableDataModelFaker(ProviderId, contact.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.TaskId, task.Id)
            .RuleFor(x => x.Type, BillableType.Task)
            .RuleFor(x => x.BillableItems, taskBillableItems)
            .Generate();

        var invoiceLineItems = taskBillableItems.Select(item =>
            {
                return new InvoiceLineItemFaker()
                    .RuleFor(x => x.BillableItemId, item.Id)
                    .RuleFor(x => x.Price, item.Price)
                    .RuleFor(x => x.Units, 1)
                    .Generate();
            }).ToArray();

        var unpaidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .RuleFor(x => x.LineItems, invoiceLineItems)
            .RuleFor(x => x.CreditsUsed, providerItem.Price)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            task,
            taskBillable,
            unpaidInvoice);

        await DataContext.SaveChangesAsync();

        // Act  
        var requestPayload = new CreatePaymentRequest(unpaidInvoice.ProviderId, unpaidInvoice.ContactId, unpaidInvoice.Id, DateTime.UtcNow, PaymentTypes.CreditBalance);
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestPayload)
            .Create();

        // Assert
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Payment>();
        result.Id.Should().NotBeEmpty();
        result.InvoiceId.Should().Be(unpaidInvoice.Id);

        var dbPayment = await DataContext.Payments.AsNoTracking()
            .Include(x => x.Allocations)
            .FirstOrDefaultAsync(x => x.Id == result.Id);

        dbPayment.Should().NotBeNull();
        dbPayment.ChargeAmount.Should().Be(providerItem.Price);
        dbPayment.Allocations.Should().NotBeEmpty();
        dbPayment.Allocations.Count.Should().Be(1);
        dbPayment.Allocations.Sum(x => x.Amount).Should().Be(providerItem.Price);

        var paymentCount = await DataContext.Payments.AsNoTracking()
            .Where(p => p.ContactId == contact.Id)
            .CountAsync();
        paymentCount.Should().Be(1);
    }

    [Fact]
    public async Task CreatePayment_FreeServices_ShouldNotCreatePaymentOrAllocations()
    {
        // Arrange
        var contact = new ContactFaker(ProviderId).Generate();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.Price, 0)
            .Generate();

        var task = new TaskFaker()
            .WithContacts([contact], TaskContactStatus.Attended)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.ItemsSnapshot, [providerItem])
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var invoiceLineItems = task.ItemsSnapshot.Select(item =>
            {
                return new InvoiceLineItemFaker()
                    .RuleFor(x => x.Price, item.Price)
                    .RuleFor(x => x.Units, 1)
                    .Generate();
            }).ToArray();

        var unpaidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .RuleFor(x => x.LineItems, invoiceLineItems)
            .RuleFor(x => x.CreditsUsed, providerItem.Price)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            task,
            unpaidInvoice);

        await DataContext.SaveChangesAsync();

        // Act  
        var requestPayload = new CreatePaymentRequest(unpaidInvoice.ProviderId, unpaidInvoice.ContactId, unpaidInvoice.Id, DateTime.UtcNow, PaymentTypes.CreditBalance);
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestPayload)
            .Create();

        // Assert
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Payment>();
        result.Id.Should().NotBeEmpty();

        var dbPayment = await DataContext.Payments.AsNoTracking()
            .Include(x => x.Allocations)
            .FirstOrDefaultAsync(x => x.Id == result.Id);
        dbPayment.Should().BeNull();

        var dbInvoice = await DataContext.Invoices.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == unpaidInvoice.Id);

        dbInvoice.Status.Should().Be(InvoiceStatus.Paid);
        dbInvoice.PaymentDate.Should().Be(requestPayload.PaymentDate.Truncate(TimeSpan.TicksPerSecond));

        var paymentCount = await DataContext.Payments.AsNoTracking()
            .Where(p => p.ContactId == contact.Id)
            .CountAsync();
        paymentCount.Should().Be(0);
    }

    [Fact]
    public async Task CreatePayment_WhenInvoiceLineItemsCancelsOutEachOther_ShouldCreatePaymentAndAllocations()
    {
        // Arrange
        var contact = new ContactFaker(ProviderId).Generate();

        var providerItems = new ProviderItemFaker()
            .Generate(2)
            .ToArray();

        providerItems[0].Price = 100;
        providerItems[1].Price = -100;

        var task = new TaskFaker()
            .WithContacts([contact], TaskContactStatus.Attended)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.ItemsSnapshot, providerItems)
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var taskBillableItems = task.ItemsSnapshot.Select(item =>
            new BillableItemFaker(ProviderId, contact.Id)
                .RuleFor(x => x.Id, Guid.NewGuid)
                .RuleFor(x => x.Price, item.Price)
                .RuleFor(x => x.ServiceId, item.Id)
                .Generate()
                .ToDataModel()
            ).ToArray();

        var taskBillable = new BillableDataModelFaker(ProviderId, contact.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.TaskId, task.Id)
            .RuleFor(x => x.Type, BillableType.Task)
            .RuleFor(x => x.SyncToken, task.UpdatedDateTimeUtc.Ticks)
            .RuleFor(x => x.BillableItems, taskBillableItems)
            .Generate();

        var invoiceLineItems = taskBillableItems.Select(item =>
            {
                return new InvoiceLineItemFaker()
                    .RuleFor(x => x.BillableItemId, item.Id)
                    .RuleFor(x => x.Price, item.Price)
                    .RuleFor(x => x.Units, 1)
                    .Generate();
            }).ToArray();

        var unpaidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .RuleFor(x => x.LineItems, invoiceLineItems)
            .Generate();

        unpaidInvoice.CalculateTotals();

        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            task,
            taskBillable,
            unpaidInvoice.ToDataModel());

        await DataContext.SaveChangesAsync();

        // Act  
        var requestPayload = new CreatePaymentRequest(unpaidInvoice.ProviderId, unpaidInvoice.ContactId, unpaidInvoice.Id, DateTime.UtcNow, PaymentTypes.CreditBalance);
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestPayload)
            .Create();

        // Assert
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Payment>();
        result.Id.Should().NotBeEmpty();
        result.InvoiceId.Should().Be(unpaidInvoice.Id);
        result.ChargeAmount.Should().Be(0);
        result.Amount.Should().Be(0);

        var dbPayment = await DataContext.Payments.AsNoTracking()
            .Include(x => x.Allocations)
            .FirstOrDefaultAsync(x => x.Id == result.Id);

        dbPayment.Should().NotBeNull();
        dbPayment.ChargeAmount.Should().Be(0);
        dbPayment.Amount.Should().Be(0);
        dbPayment.Allocations.Should().NotBeEmpty();
        dbPayment.Allocations.Count.Should().Be(2);

        foreach (var allocation in dbPayment.Allocations)
        {
            var lineItem = invoiceLineItems.FirstOrDefault(x => x.BillableItemId == allocation.BillableItemId);
            lineItem.Should().NotBeNull();
            allocation.Amount.Should().Be(lineItem.Price);
        }

        var dbInvoice = await DataContext.Invoices.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == unpaidInvoice.Id);
        dbInvoice.Status.Should().Be(InvoiceStatus.Paid);

        var paymentCount = await DataContext.Payments.AsNoTracking()
            .Where(p => p.ContactId == contact.Id)
            .CountAsync();
        paymentCount.Should().Be(1);
    }

    [Fact]
    public async Task CreatePayment_ManualPayment_ShouldSetPaymentIntentToSucceeded()
    {
        var contact = new ContactFaker(ProviderId).Generate().ToDataModel();
        var invoice = new InvoiceFaker(ProviderId).WithContactId(contact.Id).Generate();
        var paymentIntent = new PaymentIntentFaker(ProviderId)
            .WithInvoice(invoice)
            .RuleFor(x => x.Status, PaymentIntentStatus.Open)
            .Generate()
            .ToDataModel();

        foreach (var lineItem in invoice.LineItems)
        {
            lineItem.Units = 1;
            lineItem.Price = -50;
        }

        var billableItems = invoice.LineItems.Select(x =>
        {
            var billableItem = new BillableItemFaker(ProviderId, contact.Id).Generate();
            x.BillableItemId = billableItem.Id;
            return billableItem.ToDataModel();
        }).ToList();

        var billable = new BillableDataModelFaker(ProviderId, contact.Id)
            .RuleFor(x => x.BillableItems, billableItems)
            .Generate();

        await DataContext.AddRangeAsync(
            contact,
            invoice.CalculateTotals().ToDataModel(),
            billable,
            paymentIntent
        );
        await DataContext.SaveChangesAsync();

        var requestPayload = new CreatePaymentRequest(invoice.ProviderId, invoice.ContactId, invoice.Id, DateTime.UtcNow, PaymentTypes.Cash);
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/payments")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestPayload)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Payment>();
        result.Id.Should().NotBeEmpty();
        result.InvoiceId.Should().Be(invoice.Id);

        var dbPaymentIntent = await DataContext.PaymentIntents
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == paymentIntent.Id);
        dbPaymentIntent.Status.Should().Be(PaymentIntentStatus.Succeeded);
    }

    [Fact]
    public async Task CreatePayment_SavesPaymentAndAllocations_ForManualClaimPayments()
    {
        var faker = new Faker();
        var expectedReference = faker.Random.AlphaNumeric(10);
        var expectedPayerName = faker.Company.CompanyName();
        var contact = new ContactFaker(ProviderId).Generate().ToDataModel();
        var items = new ProviderItemFaker().Generate(3).ToArray();
        var billableItems = items.Select(x => new BillableItemFaker(ProviderId, contact.Id).Generate()).ToList();
        var billableItemDataModels = billableItems.Select(x => x.ToDataModel()).ToList();
        billableItemDataModels.ForEach(x => x.Price /= 2);
        var billable = new BillableDataModelFaker(ProviderId, contact.Id)
            .RuleFor(x => x.BillableItems, billableItemDataModels)
            .Generate();

        await DataContext.AddRangeAsync(contact, billable);
        await DataContext.SaveChangesAsync();

        var payload = new ManualInsurancePaymentRequest(
            DateTime.UtcNow,
            null,
            billableItems.Sum(s => s.Price * s.Units),
            expectedPayerName,
            expectedReference,
            billable.CurrencyCode,
            billableItems.Select(s => new ClaimPaymentAllocation(s.Id, s.SelfPayAmount, null)).ToArray()
        );
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/contacts/{contact.Id}/payments/insurance")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payload)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var stripeAmount = CurrencyHandler.Get(billable.CurrencyCode).ToStripeAmount(payload.Amount);

        var result = await response.Content.ReadAsAsync<Payment>();
        result.Id.Should().NotBeEmpty();

        var createdPayment = await DataContext.Payments.AsNoTracking()
            .Include(x => x.Allocations)
            .FirstOrDefaultAsync(x => x.Id == result.Id);
        result.Amount.Should().Be(stripeAmount);
        result.ChargeAmount.Should().Be(payload.Amount);
        result.TransferAmount.Should().Be(0);
        result.Fee.Should().Be(0);
        result.IsClientChargedFee.Should().BeFalse();
        result.CurrencyCode.Should().Be(payload.CurrencyCode);
        result.PaymentProvider.Should().Be(PaymentProviders.Insurance);
        result.Type.Should().Be(PaymentTypes.Insurance);
        result.Reference.Should().Be(expectedReference);
        result.PayerName.Should().Be(expectedPayerName);
        result.PayoutDateUtc.Should().BeNull();
        result.PaymentDate.Should().BeCloseTo(payload.PaymentDate, TimeSpan.FromSeconds(60));

        var allocations = createdPayment.Allocations;
        allocations.Should().NotBeEmpty();
        allocations.Count.Should().Be(3);
        foreach (var allocation in allocations)
        {
            var billableItem = billableItems.FirstOrDefault(x => x.Id == allocation.BillableItemId);
            billableItem.Should().NotBeNull();
            allocation.Amount.Should().Be(billableItem.SelfPayAmount);
        }

        var dbBillableItems = await DataContext.BillableItems.AsNoTracking()
            .Where(x => x.BillableId == billable.Id)
            .ToArrayAsync();
        foreach (var dbBillableItem in dbBillableItems)
        {
            var allocation = allocations.FirstOrDefault(x => x.BillableItemId == dbBillableItem.Id);
            dbBillableItem.InsurancePaid.Should().Be(allocation.Amount);
        }
    }

    [Fact]
    public async Task CreatePayment_ForInsuranceClaims_ShouldUpdateClaimStatusToFullyPaid()
    {
        var faker = new Faker();
        var expectedReference = faker.Random.AlphaNumeric(10);
        var expectedPayerName = faker.Company.CompanyName();
        var contact = new ContactFaker(ProviderId).Generate();
        var items = new ProviderItemFaker()
            .RuleFor(x => x.Price, 100)
            .Generate(1).ToArray();
        var billableItems = items.Select(item => new BillableItemFaker(ProviderId, contact.Id)
            .RuleFor(x => x.IsInsuranceEnabled, true)
            .RuleFor(x => x.Price, item.Price)
            .RuleFor(x => x.TaxAmount, 0)
            .RuleFor(x => x.TaxRate, 0)
            .Generate()
        ).ToArray();
        var billableItemDataModels = billableItems.Select(x => x.ToDataModel()).ToList();
        billableItemDataModels.ForEach(x =>
        {
            x.SelfPayAmount = 10;
        });
        var billable = new BillableDataModelFaker(ProviderId, contact.Id)
            .RuleFor(x => x.BillableItems, billableItemDataModels)
            .Generate();

        var claimServiceLines = billableItemDataModels.Select(i => new ClaimServiceLineFaker(ProviderId, contact.Id, i.Id)
            .RuleFor(x => x.Amount, i.Amount)
            .RuleFor(x => x.TaxAmount, i.TaxAmount)
            .RuleFor(x => x.BillableItemId, i.Id)
            .Generate()
        ).ToArray();
        var claimId = Guid.NewGuid();
        var claim = new USProfessionalClaimFaker(ProviderId)
            .WithClient(new ClaimClientFaker(ProviderId, contact).Generate())
            .WithServiceLines(claimServiceLines)
            .WithAmountPaid(billableItems)
            .RuleFor(x => x.Id, claimId)
            .Generate();

        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            claim.ToDataModel(claimId),
            claim.ToClaimHeaderDataModel(claimId),
            billable
        );
        await DataContext.SaveChangesAsync();

        // Payload will create allocations for ALL
        // claim service lines with full amounts.
        // This should result in a fully paid claim
        var payload = new ManualInsurancePaymentRequest(
            DateTime.UtcNow,
            claim.Id,
            claimServiceLines.Sum(s => s.Amount),
            expectedPayerName,
            expectedReference,
            billable.CurrencyCode,
            [.. claimServiceLines.Select(s => new ClaimPaymentAllocation(
                s.BillableItemId.Value,
                s.Amount - billableItemDataModels.FirstOrDefault(b => b.Id == s.BillableItemId)?.SelfPayAmount ?? 0,
                s.Id
            ))]
        );
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/contacts/{contact.Id}/payments/insurance")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payload)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var stripeAmount = CurrencyHandler.Get(billable.CurrencyCode).ToStripeAmount(payload.Amount);

        var result = await response.Content.ReadAsAsync<Payment>();
        result.Id.Should().NotBeEmpty();

        var dbClaim = await DataContext.InsuranceClaims.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Status.Should().Be(ClaimStatus.Paid);

        var dbClaimHistory = await DataContext.EntityHistory.AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == claim.Id && x.EntityType == EntityType.USProfessionalInsuranceClaim && x.Action == HistoryAction.ClaimManualPayment);
        dbClaimHistory.Should().NotBeNull();
        dbClaimHistory.EntityType.Should().Be(EntityType.USProfessionalInsuranceClaim);
        dbClaimHistory.Action.Should().Be(HistoryAction.ClaimManualPayment);
        dbClaimHistory.Details.Should().NotBeNull();
        dbClaimHistory.Details.Should().BeOfType<JsonDocument>();
        var details = JsonConvert.DeserializeObject<ClaimManualPaymentHistoryActionDetail>(dbClaimHistory.Details.RootElement.GetRawText());
        var historyModel = ClaimHistoryModel.Create(claim);
        historyModel.Status = ClaimStatus.Paid;
        details.Should().NotBeNull().And.BeEquivalentTo(
            new ClaimManualPaymentHistoryActionDetail(
                historyModel,
                new ClaimPaymentHistoryModel
                {
                    Id = result.Id,
                    Amount = payload.Amount,
                    Reference = expectedReference,
                    CurrencyCode = billable.CurrencyCode
                }
            )
        );
    }

    [Fact]
    public async Task CreatePayment_ForUnpaidInsuranceClaim_ShouldUpdateClaimStatusToPartiallyPaid()
    {
        var faker = new Faker();
        var expectedReference = faker.Random.AlphaNumeric(10);
        var expectedPayerName = faker.Company.CompanyName();
        var contact = new ContactFaker(ProviderId).Generate();

        var billable = CreateSimpleInsuranceBillable(ProviderId, contact);
        var claim = CreateUnpaidInsuranceClaim(ProviderId, contact, billable.Items.ToArray());

        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            billable.ToDataModel(),
            claim.ToDataModel(claim.Id),
            claim.ToClaimHeaderDataModel(claim.Id)
        );
        await DataContext.SaveChangesAsync();

        // Payload will create allocations for ALL
        // claim service lines with half amounts.
        // This should result in a partially paid claim
        var payload = new ManualInsurancePaymentRequest(
            DateTime.UtcNow,
            claim.Id,
            claim.Amount / 2,
            expectedPayerName,
            expectedReference,
            billable.CurrencyCode,
            [.. claim.ServiceLines.Select(s => new ClaimPaymentAllocation(
                s.BillableItemId.Value,
                s.Amount / 2,
                s.Id
            ))]
        );
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/contacts/{contact.Id}/payments/insurance")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payload)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Payment>();
        result.Id.Should().NotBeEmpty();

        var dbClaim = await DataContext.InsuranceClaims.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Status.Should().Be(ClaimStatus.PartiallyPaid);

        var dbClaimHistory = await DataContext.EntityHistory.AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == claim.Id && x.EntityType == EntityType.USProfessionalInsuranceClaim && x.Action == HistoryAction.ClaimManualPayment);
        dbClaimHistory.Should().NotBeNull();
        dbClaimHistory.EntityType.Should().Be(EntityType.USProfessionalInsuranceClaim);
        dbClaimHistory.Action.Should().Be(HistoryAction.ClaimManualPayment);
        dbClaimHistory.Details.Should().NotBeNull();
        dbClaimHistory.Details.Should().BeOfType<JsonDocument>();
        var details = JsonConvert.DeserializeObject<ClaimManualPaymentHistoryActionDetail>(dbClaimHistory.Details.RootElement.GetRawText());
        var historyModel = ClaimHistoryModel.Create(claim);
        historyModel.Status = ClaimStatus.PartiallyPaid;
        details.Should().NotBeNull().And.BeEquivalentTo(
            new ClaimManualPaymentHistoryActionDetail(
                historyModel,
                new ClaimPaymentHistoryModel
                {
                    Id = result.Id,
                    Amount = payload.Amount,
                    Reference = expectedReference,
                    CurrencyCode = billable.CurrencyCode
                }
            )
        );
    }

    [Fact]
    public async Task CreatePayment_ForInsuranceClaims_ShouldUpdateClaimStatusToPartiallyPaid_IfNotAllItemsAreAllocated()
    {
        var faker = new Faker();
        var expectedReference = faker.Random.AlphaNumeric(10);
        var expectedPayerName = faker.Company.CompanyName();
        var contact = new ContactFaker(ProviderId).Generate();

        var billable = CreateSimpleInsuranceBillable(ProviderId, contact, items: 2);
        var claim = CreateUnpaidInsuranceClaim(ProviderId, contact, billable.Items.ToArray());

        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            billable.ToDataModel(),
            claim.ToDataModel(claim.Id),
            claim.ToClaimHeaderDataModel(claim.Id)
        );
        await DataContext.SaveChangesAsync();

        var allocationServiceLine = claim.ServiceLines.First();
        var payload = new ManualInsurancePaymentRequest(
            DateTime.UtcNow,
            claim.Id,
            claim.Amount / 2,
            expectedPayerName,
            expectedReference,
            billable.CurrencyCode,
            [
                new ClaimPaymentAllocation(
                    allocationServiceLine.BillableItemId.Value,
                    allocationServiceLine.Amount,
                    allocationServiceLine.Id)
            ]
        );
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/contacts/{contact.Id}/payments/insurance")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payload)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<Payment>();
        result.Id.Should().NotBeEmpty();

        var dbClaim = await DataContext.InsuranceClaims.AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Status.Should().Be(ClaimStatus.PartiallyPaid);

        var dbClaimHistory = await DataContext.EntityHistory.AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == claim.Id && x.EntityType == EntityType.USProfessionalInsuranceClaim && x.Action == HistoryAction.ClaimManualPayment);
        dbClaimHistory.Should().NotBeNull();
        dbClaimHistory.EntityType.Should().Be(EntityType.USProfessionalInsuranceClaim);
        dbClaimHistory.Action.Should().Be(HistoryAction.ClaimManualPayment);
        dbClaimHistory.Details.Should().NotBeNull();
        dbClaimHistory.Details.Should().BeOfType<JsonDocument>();
        var details = JsonConvert.DeserializeObject<ClaimManualPaymentHistoryActionDetail>(dbClaimHistory.Details.RootElement.GetRawText());
        var historyModel = ClaimHistoryModel.Create(claim);
        historyModel.Status = ClaimStatus.PartiallyPaid;
        details.Should().NotBeNull().And.BeEquivalentTo(
            new ClaimManualPaymentHistoryActionDetail(
                historyModel,
                new ClaimPaymentHistoryModel
                {
                    Id = result.Id,
                    Amount = payload.Amount,
                    Reference = expectedReference,
                    CurrencyCode = billable.CurrencyCode
                }
            )
        );
    }

    [Fact]
    public async Task CreatePayment_ForInsuranceClaims_ShouldReduceUnclaimedAmountsForBillableItems()
    {
        var faker = new Faker();
        var expectedReference = faker.Random.AlphaNumeric(10);
        var expectedPayerName = faker.Company.CompanyName();
        var contact = new ContactFaker(ProviderId).Generate();
        var items = new ProviderItemFaker().RuleFor(s => s.Price, 100).Generate(2).ToArray();

        // Billable item prices here are 100 and self pay of 10.
        // Expected insurance amount here is 90
        var billableItems = items.Select(item =>
            new BillableItemFaker(ProviderId, contact.Id)
                .RuleFor(x => x.Price, item.Price)
                .RuleFor(x => x.Units, 1)
                .RuleFor(x => x.TaxRates, [])
                .RuleFor(x => x.IsInsuranceEnabled, true)
                .Generate()
            )
            .ToList();
        var billableItemDataModels = billableItems.Select(x => x.ToDataModel()).ToList();
        billableItemDataModels.ForEach(x => x.SelfPayAmount = 10);
        var billable = new BillableDataModelFaker(ProviderId, contact.Id)
            .RuleFor(x => x.BillableItems, billableItemDataModels)
            .Generate();

        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            billable
        );
        await DataContext.SaveChangesAsync();

        // For each billable item, we are allocating half of the insurance amount
        // which should be $45 each
        var payload = new ManualInsurancePaymentRequest(
            DateTime.UtcNow,
            null,
            billableItems.Sum(s => s.Price * s.Units),
            expectedPayerName,
            expectedReference,
            billable.CurrencyCode,
            [.. billableItemDataModels.Select(s => new ClaimPaymentAllocation(
                s.Id,
                (s.Price - s.SelfPayAmount) / 2,
                null
            ))]
        );
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/contacts/{contact.Id}/payments/insurance")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(payload)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var stripeAmount = CurrencyHandler.Get(billable.CurrencyCode).ToStripeAmount(payload.Amount);

        var result = await response.Content.ReadAsAsync<Payment>();
        result.Id.Should().NotBeEmpty();

        var dbBillableItems = await DataContext.BillableItems.AsNoTracking()
            .Where(x => x.BillableId == billable.Id)
            .ToArrayAsync();
        foreach (var dbBillableItem in dbBillableItems)
        {
            dbBillableItem.Unclaimed.Should().Be(45);
        }
    }
    
    
    private InsuranceClaimUSProfessional CreateUnpaidInsuranceClaim(Guid providerId, Contact contact, BillableItem[] billableItems)
    {
        var claimServiceLines = billableItems
            .Select(i => new ClaimServiceLineFaker(ProviderId, contact.Id).WithBillableItem(i).Generate())
            .ToArray();
        
        return new USProfessionalClaimFaker(ProviderId)
            .WithClient(new ClaimClientFaker(ProviderId, contact).Generate())
            .WithServiceLines(claimServiceLines)
            .WithAmountPaid(billableItems.ToArray())
            .Generate();
    }

    private Billable CreateSimpleInsuranceBillable(Guid providerId, Contact contact, int items = 3)
    {
        return new BillableFaker(providerId, contact.Id)
            .RuleFor(x => x.Items, (_, b) => new BillableItemFaker(providerId, contact.Id)
                .RuleFor(x => x.BillableId, b.Id)
                .RuleFor(x => x.IsInsuranceEnabled, true)
                .RuleFor(x => x.Price, 100)
                .RuleFor(x => x.SelfPayAmount, 0)
                .Generate(items).AsEnumerable())
            .Generate();
    }

}
