using carepatron.core.Application.Billables.Models;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Common;
using carepatron.core.Models.Invoices;
using carepatron.infra.sql.Models.BillableItem;
using carepatron.infra.sql.Models.Tasks;
using tests.common.Data.Mappers;
using tests.component.Builders;
using Payment = carepatron.core.Application.Billables.Models.Payment;

namespace tests.component.Features.Payments;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class GetUnallocatedPaymentTests(ComponentTestFixture testFixture) : BaseTestClass(testFixture)
{
    [Fact]
    public async Task GetUnallocatedPayment_Should_Return_Unpaid()
    {
        var setupTax = new ItemTaxRateFaker().Generate(1).ToArray();

        var contact1 = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate();

        var providerItems = new ProviderItemFaker()
            .RuleFor(x => x.Price, f => f.PickRandom(100, 200, 300))
            .RuleFor(x => x.IsSalesTaxIncluded, true)
            .Generate(3)
            .ToArray();
        
        var processingTask = new TaskFaker()
            .WithContacts([contact1], TaskContactStatus.Attended)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.ItemsSnapshot, providerItems)
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();
        var voidTask = new TaskFaker()
            .WithContacts([contact1], TaskContactStatus.Attended)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.ItemsSnapshot, providerItems)
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();
        
        var unpaidTask = new TaskFaker()
            .WithContacts([contact1], TaskContactStatus.Attended)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.ItemsSnapshot, providerItems)
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var paidTask = new TaskFaker()
            .WithContacts([contact1], TaskContactStatus.Attended)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.ItemsSnapshot, providerItems)
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var unchargedTask = new TaskFaker()
            .WithContacts([contact1], TaskContactStatus.Attended)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.ItemsSnapshot, providerItems)
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel()
            .WithStaff([new TaskStaffDataModel { PersonId = PersonId }]);

        var paidBillableItems = paidTask.ItemsSnapshot.Select(item =>
        {
            return new BillableItemFaker(ProviderId, contact1.Id)
                .RuleFor(x => x.Id, Guid.NewGuid)
                .RuleFor(x => x.Price, item.Price)
                .RuleFor(x => x.ServiceId, item.Id)
                .RuleFor(x => x.TaxRates, setupTax)
                .Generate()
                .ToDataModel();
        }).ToArray();

        var paidBillable = new BillableDataModel
        {
            Id = Guid.NewGuid(),
            TaskId = paidTask.Id,
            ContactId = contact1.Id,
            ProviderId = paidTask.ProviderId,
            TotalOwed = paidTask.ItemsSnapshot.Sum(x => x.Price),
            Date = paidTask.EndDate.WithoutSecsAndMs(),
            Type = BillableType.Task,
            BillableItems = paidBillableItems,
        };

        var paidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact1.Id)
            .RuleFor(x => x.Status, InvoiceStatus.Paid)
            .RuleFor(x => x.TaxExclusivePrice, paidTask.ItemsSnapshot.Sum(x => x.Price))
            .Generate();

        var paymentIntent = new PaymentIntentFaker(ProviderId)
            .WithInvoice(paidInvoice)
            .Generate();

        var payment = new PaymentFaker(ProviderId)
            .WithPaymentIntent(paymentIntent)
            .RuleFor(x => x.PaymentDate, DateTime.UtcNow)
            .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
            .RuleFor(x => x.PaymentProvider, PaymentProviders.Stripe)
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.NZD)
            .Generate();

        var paidLineItems = paidBillableItems.Select(x =>
        {
            return new InvoiceLineItemFaker()
                .WithBillableItemId(x.Id)
                .RuleFor(x => x.Id, Guid.NewGuid)
                .RuleFor(x => x.InvoiceId, paidInvoice.Id)
                .RuleFor(x => x.Price, x.Price)
                .RuleFor(x => x.Units, 1)
                .RuleFor(x => x.TaxRates, setupTax)
                .Generate();
        }).ToArray();
        paidInvoice.LineItems = paidLineItems;

        var paymentAllocations = paidLineItems.Select(x => new PaymentAllocationFaker(ProviderId, contact1.Id)
                .RuleFor(x => x.BillableItemId, x.BillableItemId)
                .RuleFor(x => x.InvoiceLineItemId, x.Id)
                .RuleFor(x => x.Amount, CurrencyHandler.Get(x.CurrencyCode).Round(x.Price * x.Units * (1 + setupTax.First().Rate / 100)))
                .RuleFor(x => x.PaymentId, payment.Id)
                .Generate()
                .ToDataModel())
            .ToArray();

        var unpaidBillableItem = new BillableItemFaker(ProviderId, contact1.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.Price, 100)
            .RuleFor(x => x.ServiceId, (Guid?)null)
            .RuleFor(x => x.TaxRates, setupTax)
            .Generate()
            .ToDataModel();

        var unpaidBillable = new BillableDataModel
        {
            Id = Guid.NewGuid(),
            TaskId = unpaidTask.Id,
            ContactId = contact1.Id,
            ProviderId = paidTask.ProviderId,
            TotalOwed = 100,
            Date = paidTask.EndDate.WithoutSecsAndMs(),
            Type = BillableType.Invoice,
            BillableItems = [unpaidBillableItem],
        };
        
        var voidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact1.Id)
            .RuleFor(x => x.Status, InvoiceStatus.Void)
            .RuleFor(x => x.TaxExclusivePrice, 100)
            .Generate();
        
        var voidBillableItem = new BillableItemFaker(ProviderId, contact1.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.Price, 100)
            .RuleFor(x => x.ServiceId, (Guid?)null)
            .RuleFor(x => x.TaxRates, setupTax)
            .Generate()
            .ToDataModel();
        var voidBillable = new BillableDataModel
        {
            Id = Guid.NewGuid(),
            TaskId = voidTask.Id,
            ContactId = contact1.Id,
            ProviderId = paidTask.ProviderId,
            TotalOwed = 100,
            Date = paidTask.EndDate.WithoutSecsAndMs(),
            Type = BillableType.Invoice,
            BillableItems = [voidBillableItem],
        };
        var voidLineItem = new InvoiceLineItemFaker()
            .WithBillableItemId(voidBillableItem.Id)
            .RuleFor(x => x.InvoiceId, voidInvoice.Id)
            .RuleFor(x => x.Price, 100)
            .RuleFor(x => x.TaxRates, setupTax)
            .RuleFor(x => x.Units, 1)
            .Generate();
        voidInvoice.LineItems = [voidLineItem];
        
        var processingInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact1.Id)
            .RuleFor(x => x.Status, InvoiceStatus.Processing)
            .RuleFor(x => x.TaxExclusivePrice, 100)
            .Generate();
        var processingBillableItem = new BillableItemFaker(ProviderId, contact1.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.Price, 100)
            .RuleFor(x => x.ServiceId, (Guid?)null)
            .RuleFor(x => x.TaxRates, setupTax)
            .Generate()
            .ToDataModel();
        var processingBillable = new BillableDataModel
        {
            Id = Guid.NewGuid(),
            TaskId = processingTask.Id,
            ContactId = contact1.Id,
            ProviderId = paidTask.ProviderId,
            TotalOwed = 100,
            Date = paidTask.EndDate.WithoutSecsAndMs(),
            Type = BillableType.Invoice,
            BillableItems = [processingBillableItem],
        };
        var processingLineItem = new InvoiceLineItemFaker()
            .WithBillableItemId(processingBillableItem.Id)
            .RuleFor(x => x.InvoiceId, processingInvoice.Id)
            .RuleFor(x => x.Price, 100)
            .RuleFor(x => x.TaxRates, setupTax)
            .RuleFor(x => x.Units, 1)
            .Generate();
        processingInvoice.LineItems = [processingLineItem];

        var unpaidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact1.Id)
            .RuleFor(x => x.Status, InvoiceStatus.Unpaid)
            .RuleFor(x => x.TaxExclusivePrice, 100)
            .Generate();
        var unpaidLineItem = new InvoiceLineItemFaker()
            .WithBillableItemId(unpaidBillableItem.Id)
            .RuleFor(x => x.InvoiceId, unpaidInvoice.Id)
            .RuleFor(x => x.Price, 100)
            .RuleFor(x => x.TaxRates, setupTax)
            .RuleFor(x => x.Units, 1)
            .Generate();
        unpaidInvoice.LineItems = [unpaidLineItem];


        var removedBillableItem = new BillableItemFaker(ProviderId, contact1.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.Price, 0)
            .RuleFor(x => x.ServiceId, (Guid?)null)
            .RuleFor(x => x.TaxRates, setupTax)
            .Generate()
            .ToDataModel();

        var billableForRemovedItem = new BillableDataModel
        {
            Id = Guid.NewGuid(),
            TaskId = null,
            ContactId = contact1.Id,
            ProviderId = paidTask.ProviderId,
            TotalOwed = 100,
            Date = paidTask.EndDate.WithoutSecsAndMs(),
            Type = BillableType.Invoice,
            BillableItems = [removedBillableItem],
        };

        var invoiceForRemovedItem = new InvoiceFaker(ProviderId)
            .WithContactId(contact1.Id)
            .RuleFor(x => x.Status, InvoiceStatus.Unpaid)
            .RuleFor(x => x.TaxExclusivePrice, 100)
            .Generate();

        var lineForRemovedItem = new InvoiceLineItemFaker()
            .WithBillableItemId(removedBillableItem.Id)
            .RuleFor(x => x.InvoiceId, invoiceForRemovedItem.Id)
            .RuleFor(x => x.Price, 100)
            .RuleFor(x => x.TaxRates, setupTax)
            .RuleFor(x => x.Units, 1)
            .Generate();
        invoiceForRemovedItem.LineItems = [lineForRemovedItem];

        var unchargedBillableItem = providerItems.Select(i =>
        {
            return new BillableItemFaker(ProviderId, contact1.Id)
                .RuleFor(x => x.Id, Guid.NewGuid())
                .RuleFor(x => x.Price, i.Price)
                .RuleFor(x => x.ServiceId, i.Id)
                .RuleFor(x => x.Description, i.Title)
                .RuleFor(x => x.Detail, i.Description)
                .RuleFor(x => x.TaxRates, setupTax)
                .Generate()
                .ToDataModel();
        }).ToArray();

        var unchargedBillable = new BillableDataModel
        {
            Id = Guid.NewGuid(),
            TaskId = unchargedTask.Id,
            ContactId = contact1.Id,
            ProviderId = unchargedTask.ProviderId,
            TotalOwed = 100,
            Date = paidTask.EndDate.WithoutSecsAndMs(),
            Type = BillableType.Invoice,
            BillableItems = unchargedBillableItem,
        };

        await DataContext.AddRangeAsync(contact1.ToDataModel(), paidTask, unpaidTask, unchargedTask, paidInvoice.ToDataModel(), payment.ToDataModel(), paymentIntent.ToDataModel(), paidBillable, unchargedBillable,
            unpaidBillable, unpaidInvoice.ToDataModel(), voidInvoice.ToDataModel(), processingInvoice.ToDataModel(), voidBillable, processingBillable);
        await DataContext.PaymentAllocations.AddRangeAsync(paymentAllocations);
        await DataContext.AddRangeAsync(billableForRemovedItem, invoiceForRemovedItem.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
                $"/api/providers/{ProviderId}/contacts/{contact1.Id}/payments/unallocated?payer=selfpay")
            .WithBearerAuthorization(IdentityToken)
            .Create();
        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<InvoiceUnallocatedItem[]>();
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveCount(2);

        result.Should().BeEquivalentTo([
            new InvoiceUnallocatedItem
            {
                BillableItemId = unpaidBillableItem.Id,
                Date = unpaidLineItem.Date,
                Amount = unpaidLineItem.Amount + unpaidLineItem.TaxAmount,
                Paid = 0,
                Description = unpaidLineItem.Description,
                Detail = unpaidLineItem.Detail,
                InvoiceLineItemId = unpaidLineItem.Id,
                CurrencyCode = unpaidLineItem.CurrencyCode,
                Invoice = new InvoiceReference
                {
                    InvoiceId = unpaidInvoice.Id,
                    InvoiceStatus = unpaidInvoice.Status,
                    IssueDate = unpaidInvoice.IssueDate,
                    InvoiceNumber = unpaidInvoice.Number,
                    IssuedCredits = []
                }
            },
            new InvoiceUnallocatedItem
            {
                BillableItemId = removedBillableItem.Id,
                Date = lineForRemovedItem.Date,
                Amount = lineForRemovedItem.Amount + lineForRemovedItem.TaxAmount,
                Paid = 0,
                Description = lineForRemovedItem.Description,
                Detail = lineForRemovedItem.Detail,
                InvoiceLineItemId = lineForRemovedItem.Id,
                CurrencyCode = unpaidLineItem.CurrencyCode,
                Invoice = new InvoiceReference
                {
                    InvoiceId = invoiceForRemovedItem.Id,
                    InvoiceStatus = invoiceForRemovedItem.Status,
                    IssueDate = invoiceForRemovedItem.IssueDate,
                    InvoiceNumber = invoiceForRemovedItem.Number,
                    IssuedCredits = []
                }
            }
        ]);
    }

    [Fact]
    public async Task GetUnallocatedPayments_ForInsurance()
    {
        var contact = new ContactFaker(ProviderId).Generate();
        var billableWithClaim = CreateBillableWithItem(contact.Id);
        var claim = CreateClaimsWithServiceLine(contact, billableWithClaim.Items.First());
        var payment = CreatePaymentFromBillableItem(contact, billableWithClaim.Items.First(), claim.ServiceLines);

        var billableWithoutClaim = CreateBillableWithItem(contact.Id);
        var paymentWithoutClaim = CreatePaymentFromBillableItem(contact, billableWithoutClaim.Items.First());

        var billableWithMultipleClaims = CreateBillableWithItem(contact.Id);
        var firstClaimWithSameBillable = CreateClaimsWithServiceLine(contact, billableWithMultipleClaims.Items.First());
        var secondClaimWithSameBillable = CreateClaimsWithServiceLine(contact, billableWithMultipleClaims.Items.First());
        var paymentWithMultipleClaims = CreatePaymentFromBillableItem(
            contact,
            billableWithMultipleClaims.Items.First(), 
            [.. firstClaimWithSameBillable.ServiceLines, .. secondClaimWithSameBillable.ServiceLines]);
        
        await DataContext.AddRangeAsync(
            contact.ToDataModel(),
            billableWithClaim.ToDataModel(),
            billableWithoutClaim.ToDataModel(),
            billableWithMultipleClaims.ToDataModel(),
            payment.ToDataModel(),
            paymentWithoutClaim.ToDataModel(),
            paymentWithMultipleClaims.ToDataModel(),
            claim.ToClaimHeaderDataModel(),
            firstClaimWithSameBillable.ToClaimHeaderDataModel(),
            secondClaimWithSameBillable.ToClaimHeaderDataModel()
        );
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get,
            $"/api/providers/{ProviderId}/contacts/{contact.Id}/payments/unallocated?payer=Insurance&currencyCode={CurrencyCodes.USD}"
        )
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        var result = await response.Content.ReadAsAsync<InsuranceUnallocatedItem[]>();
        result.Should().NotBeNullOrEmpty();
        result.Should().HaveCount(4);
        
        var resultWithClaim = result.Single(x => x.BillableItemId == billableWithClaim.Items.First().Id);
        var resultWithoutClaim = result.Single(x => x.BillableItemId == billableWithoutClaim.Items.First().Id);
        var resultFirstClaim = result.Single(x => x.ClaimLineId == firstClaimWithSameBillable.ServiceLines.First().Id);
        var resultSecondClaim = result.Single(x => x.ClaimLineId == secondClaimWithSameBillable.ServiceLines.First().Id);
        
        AssertUnallocatedInsuranceItem(resultWithClaim, billableWithClaim.Items.First(), claim);
        AssertUnallocatedInsuranceItem(resultWithoutClaim, billableWithoutClaim.Items.First());
        AssertUnallocatedInsuranceItem(resultFirstClaim, billableWithMultipleClaims.Items.First(), firstClaimWithSameBillable);
        AssertUnallocatedInsuranceItem(resultSecondClaim, billableWithMultipleClaims.Items.First(), secondClaimWithSameBillable);
    }

    private void AssertUnallocatedInsuranceItem(InsuranceUnallocatedItem result, BillableItem billableItem, InsuranceClaim claim = null)
    {
        billableItem.Should().NotBeNull();
        result.BillableItemId = billableItem.Id;
        result.Date.Should().Be(billableItem.Date);
        result.CurrencyCode.Should().Be(billableItem.CurrencyCode);
        result.Description.Should().Be(billableItem.Description);
        result.Detail.Should().Be(billableItem.Detail);
        result.Amount.Should().Be(billableItem.InsuranceAmount);
        result.Paid.Should().Be(0);
        if (claim is not null)
        {
            result.ClaimLineId.Should().Be(claim.ServiceLines.First().Id);
            result.Claim.Id.Should().Be(claim.Id);
            result.Claim.ClaimStatus.Should().Be(claim.Status);
            result.Claim.Number.Should().Be(claim.Number);
            result.Claim.Type.Should().Be(claim.Type);
            result.Claim.SubmissionMethod.Should().Be(claim.SubmissionMethod);
        }
    }

    private carepatron.core.Application.Payments.Models.Payment CreatePaymentFromBillableItem(
        Contact contact, BillableItem billableItem, ClaimServiceLine[] claimLines = null)
    {
        var claimLinesIds =  claimLines?.Select(x => (Guid?) x.Id).ToArray() ?? [ null ];
        var allocations = claimLinesIds.Select(claimLineId =>
                new PaymentAllocationFaker(ProviderId, contact.Id)
                    .RuleFor(x => x.BillableItemId, billableItem.Id)
                    .RuleFor(x => x.Amount, billableItem.Amount)
                    .RuleFor(x => x.InvoiceLineItemId, (Guid?)null)
                    .RuleFor(x => x.ClaimLineId, claimLineId)
                    .Generate()
            );
            var payment = new PaymentFaker(ProviderId)
                .RuleFor(x => x.CurrencyCode, billableItem.CurrencyCode)
                .RuleFor(x => x.PaymentDate, DateTime.UtcNow)
                .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
                .RuleFor(x => x.PaymentProvider, PaymentProviders.Manual)
                .RuleFor(x => x.Type, PaymentTypes.Cash)
                .RuleFor(x => x.InvoiceId, (Guid?)null)
                .RuleFor(x => x.Allocations, allocations)
                .Generate();
            return payment;
    }

    private InsuranceClaimUSProfessional CreateClaimsWithServiceLine(Contact contact, BillableItem billableItem)
    {
        var claimServiceLine = new ClaimServiceLineFaker(ProviderId, contact.Id)
            .RuleFor(x => x.ServiceId, billableItem.ServiceId)
            .RuleFor(x => x.BillableItemId, billableItem.Id)
            .RuleFor(x => x.Amount, billableItem.Price)
            .RuleFor(x => x.Units, 1)
            .RuleFor(x => x.TaxAmount, 0)
            .RuleFor(x => x.CurrencyCode, billableItem.CurrencyCode)
            .Generate();
        var claim = new USProfessionalClaimFaker(ProviderId)
            .WithClient(ProviderId, contact)
            .WithServiceLines([claimServiceLine])
            .Generate();
        return claim;
    }

    private Billable CreateBillableWithItem(Guid contactId)
    {
        var billableItem = new BillableItemFaker(ProviderId, contactId)
            .RuleFor(x => x.Price, 50m)
            .RuleFor(x => x.IsInsuranceEnabled, true)
            .RuleFor(x => x.ServiceId, Guid.NewGuid())
            .RuleFor(x => x.SelfPayAmount, (f,x) => CurrencyHandler.Get(x.CurrencyCode).Round(x.Price / 2))
            .Generate();
        var billable = new BillableFaker(ProviderId, contactId)
            .RuleFor(x => x.Items, [billableItem])
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.USD)
            .Generate();
        return billable;
    }
}
