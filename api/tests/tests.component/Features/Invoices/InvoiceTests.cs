using System.Net;
using Bogus;
using carepatron.api.Contracts.Requests.Invoices;
using carepatron.core.Application.Billables.Models;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Invoices.Events;
using carepatron.core.Application.Invoices.Extensions;
using carepatron.core.Application.Invoices.Models;
using carepatron.core.Application.Invoices.Services;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Schema.Services;
using carepatron.core.Application.Tasks.Extensions;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.TaxRates.Models;
using carepatron.core.Application.Trash.Models;
using carepatron.core.Application.Workspace.Billing.Models;
using carepatron.core.Common;
using carepatron.core.Localisation.SharedMessages;
using carepatron.core.Models.Invoices;
using carepatron.core.Models.Pagination;
using carepatron.core.Models.Reminders;
using carepatron.core.Repositories.Invoices;
using carepatron.core.Repositories.Provider;
using carepatron.core.Utilities;
using carepatron.infra.sql.Models.Contact;
using carepatron.infra.sql.Models.Invoices;
using carepatron.infra.sql.Models.Payment;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using tests.common.Builders.DataModels;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Extensions;
using tests.component.Builders;
using tests.component.Builders.Requests;

namespace tests.component.Features.Invoices;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class InvoiceTests(ComponentTestFixture testFixture) : BaseTestClass(testFixture)
{
    [Fact]
    public async Task Create_Test()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var clientModelB = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var billTo = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var taxRates = new ProviderTaxRateFaker()
            .WithProviderId(ProviderId)
            .Generate(3)
            .ToArray();

        await DataContext.AddRangeAsync(clientModelA.ToDataModel(), clientModelB.ToDataModel(), billTo.ToDataModel());
        await DataContext.SaveChangesAsync();

        var personalSetting = new PersonalSettingsFaker(PersonId)
            .RuleFor(x => x.Locale, "en-US")
            .RuleFor(x => x.TimeZone, "Asia/Singapore")
            .Generate();

        await DataContext.AddAsync(personalSetting.ToDataModel());
        await DataContext.SaveChangesAsync();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithStaffIds([PersonId])
            .WithContactIds([clientModelA.Id, clientModelB.Id])
            .WithBillToId(billTo.Id)
            .WithTaxRates(taxRates)
            .Create();

        Fixture.ClearEvents();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<Invoice[]>();
        clientResult.Should().HaveCount(2);

        var events = Fixture.GetPublishedEventsOfType<InvoiceCreatedEvent>();
        events.Should().HaveCount(clientResult.Length).And.BeEquivalentTo(clientResult.Select(x =>
            new InvoiceCreatedEvent(new InvoiceEventModel(x.Id, ProviderId, x.LineItems.Select(l =>
                new InvoiceLineItemEventModel(l.Id, l.BillableItemId)).ToArray(), IsBillingV2: true))));

        var dbModels = await DataContext.Invoices.AsNoTracking().Where(x => x.ProviderId == ProviderId).ToListAsync();
        dbModels.Select(i => i.Number).Should().BeEquivalentTo("000001", "000002");
        dbModels.Select(i => i.BillToId).Should().BeEquivalentTo([billTo.Id, billTo.Id]);

        var template = InvoiceTemplateSettings.Default();
        var parserFactory = ResolveService<IInvoiceTemplateParserFactory>();
        var parser = parserFactory.Create("en", "UTC");
        var schemaService = ResolveService<ISchemaService>();
        var schemas = await schemaService.GetDataSchemas([SchemaTypes.ContactSchema, SchemaTypes.ProviderSchema, SchemaTypes.StaffSchema, SchemaTypes.ProviderBillingSchema], ProviderId);

        var clientADetail = parser.ParseContactDetail(template, clientModelA, schemas);
        var clientBDetail = parser.ParseContactDetail(template, clientModelB, schemas);
        var billToDetail = parser.ParseBillToDetail(template, billTo, clientModelA.Id, schemas);
        var providerDetail = parser.ParseProviderDetail(template, Data.CurrentProvider, Data.CurrentProviderBillingSettings, schemas);

        dbModels.Select(x => x.ContactDetail).Should().BeEquivalentTo(clientADetail, clientBDetail);
        dbModels.Select(x => x.BillToDetail).Should().BeEquivalentTo(billToDetail, billToDetail);
        dbModels.Select(x => x.ProviderDetail).Should().AllBe(providerDetail);

        var providerStaffRepository = ResolveService<IProviderStaffRepository>();
        var staff = await providerStaffRepository.GetStaffMember(ProviderId, PersonId);
        var staffDetail = parser.ParseStaffDetail(template, staff, schemas);
        dbModels.Select(x => x.StaffDetail).Should().AllBe(staffDetail);
    }

    [Fact]
    public async Task Create_With_LineItem_Date_Test()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        DataContext.Add(clientModelA);
        await DataContext.SaveChangesAsync();

        var lineItem = InvoiceLineItemRequestBuilder.Any().Create();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithContactIds([clientModelA.Id])
            .WithLineItems(lineItem)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);


        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<Invoice[]>();
        clientResult.Should().HaveCount(1);

        var dbModel = await DataContext.Invoices.Include(invoiceDataModel => invoiceDataModel.InvoiceLineItems).SingleAsync(x => x.ProviderId == ProviderId);
        dbModel.Number.Should().Be("000001");

        var dbLineItem = dbModel.InvoiceLineItems.Single();
        dbLineItem.Id.Should().NotBeEmpty();
        dbLineItem.Date.Should().Be(lineItem.Date);
        dbLineItem.Detail.Should().Be(lineItem.Detail);
        dbLineItem.SalesTaxRate.Should().Be(dbLineItem.TaxRates.Sum(s => s.Rate));
        dbLineItem.POSCode.Should().Be(InvoiceLineItemRequestBuilder.DefaultPOSCode);
        dbLineItem.Should().BeEquivalentTo(lineItem, opts => opts
            .ExcludingMissingMembers()
            .Excluding(x => x.Id)
            .Excluding(x => x.Date)
            .Excluding(x => x.SalesTaxRate));
    }

    [Fact]
    public async Task Create_Test_WithTemplate()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var persistedInvoiceTemplate = new InvoiceTemplateSettingsFaker(ProviderId, true)
            .RuleFor(x => x.Name, "Foo")
            .RuleFor(x => x.BillToDetailTemplate, new SchemaTemplateDefinition("Baz"))
            .RuleFor(x => x.TemplateType, InvoiceLayout.Modern)
            .RuleFor(x => x.ColorHex, "#000000")
            .RuleFor(x => x.ShowCodes, false)
            .Generate()
            .ToDataModel();

        DataContext.Add(persistedInvoiceTemplate);
        DataContext.Add(clientModelA);
        await DataContext.SaveChangesAsync();

        var lineItem = InvoiceLineItemRequestBuilder.Any().Create();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithContactIds([clientModelA.Id])
            .WithLineItems(lineItem)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);


        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<Invoice[]>();
        clientResult.Should().HaveCount(1);

        var dbModel = await DataContext.Invoices.Include(invoiceDataModel => invoiceDataModel.InvoiceLineItems).SingleAsync(x => x.ProviderId == ProviderId);

        dbModel.ColorHex.Should().Be(persistedInvoiceTemplate.ColorHex);
        dbModel.Layout.Should().Be(persistedInvoiceTemplate.TemplateType);
        dbModel.ShowCodes.Should().Be(persistedInvoiceTemplate.ShowCodes);
        dbModel.ShowUnits.Should().Be(persistedInvoiceTemplate.ShowUnits);
        dbModel.ShowLineItemTax.Should().Be(persistedInvoiceTemplate.ShowLineItemTax);
    }

    [Fact]
    public async Task Create_Test_With_Number()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var clientModelB = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        DataContext.Add(clientModelA);
        DataContext.Add(clientModelB);
        await DataContext.SaveChangesAsync();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithContactIds(new[] { clientModelA.Id, clientModelB.Id })
            .WithInvoiceNumber("  ABC-123  ")
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<Invoice[]>();
        clientResult.Should().HaveCount(2);

        var dbModels = await DataContext.Invoices.Where(x => x.ProviderId == ProviderId).ToListAsync();
        dbModels.Select(i => i.Number).Should().BeEquivalentTo("ABC-123", "ABC-124");
    }


    [Theory]
    [InlineData(" EndsInSpace001 ", null, "EndsInSpace002")]
    [InlineData(" EndsInSpace001 ", "  EndsInSpace003 ", "EndsInSpace003")]
    public async Task CreateInvoice_InvoiceNumberGeneration_HandlesWhitespaceNumbers(string existingInvoiceNumber, string postedInvoiceNumber, string expectedInvoiceNumber)
    {
        var client = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var invoice = new InvoiceFaker(ProviderId)
            .RuleFor(x => x.Number, existingInvoiceNumber)
            .Generate()
            .ToDataModel();

        DataContext.AddRange(client, invoice);
        await DataContext.SaveChangesAsync();

        var createInvoiceRequest = CreateInvoiceRequestBuilder.Any()
            .WithInvoiceNumber(postedInvoiceNumber)
            .WithProviderId(ProviderId)
            .WithContactIds(new[] { client.Id })
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var resultInvoice = await result.Content.ReadAsAsync<Invoice[]>();
        resultInvoice.Should().HaveCount(1);
        resultInvoice[0].Number.Should().Be(expectedInvoiceNumber);

        var dbInvoice = await DataContext.Invoices.Where(x => x.Id == resultInvoice[0].Id).SingleAsync();
        dbInvoice.Number.Should().Be(expectedInvoiceNumber);
    }


    [Fact]
    public async Task Create_WithFullDateTimeValuesForDateOnlyFields_TruncatesTime()
    {
        var now = DateTime.UtcNow;

        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();
        DataContext.Add(clientModelA);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(new
            {
                ProviderId,
                ContactIds = new[] { clientModelA.Id },
                ServiceDate = now,
                IssueDate = now.AddDays(1),
                DueDate = now.AddDays(2),
                LineItems = new[]
                {
                    new InvoiceLineItemRequest
                    {
                        CurrencyCode = "NZD",
                        DiscountRate = 0,
                        Price = 100,
                        SalesTaxRate = 12,
                        Date = now.ToDateOnly()
                    }
                }
            })
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<Invoice[]>();
        clientResult.Should().HaveCount(1);

        var dbInvoice = await DataContext.Invoices.FirstAsync(x => x.ProviderId == ProviderId);

        var expectedDates = new
        {
            ServiceDate = now.ToDateOnly(),
            IssueDate = now.AddDays(1).ToDateOnly(),
            DueDate = now.AddDays(2).ToDateOnly(),
            LineItems = new[]
            {
                new { Date = now.ToDateOnly() }
            }
        };

        clientResult.First().Should().BeEquivalentTo(expectedDates, opts => opts.ExcludingMissingMembers());
        dbInvoice.Should().BeEquivalentTo(expectedDates, opts => opts.ExcludingMissingMembers());
    }

    [Fact]
    public async Task Create_FallbackToBillingSettings()
    {
        var client = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();
        DataContext.Add(client);
        await DataContext.SaveChangesAsync();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithContactIds(new[] { client.Id })
            .WithInvoiceNumber("ABC-123")
            .WithCurrencyCode(null)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var response = (await result.Content.ReadAsAsync<Invoice[]>()).First();

        var dbModel = await DataContext.Invoices.FirstAsync(x => x.ProviderId == ProviderId && x.Id == response.Id);
        dbModel.CurrencyCode.Should().Be(Data.CurrentProviderBillingSettings.CurrencyCode);
    }

    [Fact]
    public async Task Create_With_Zero_Price_LineItem()
    {
        var contactModel = new ContactFaker(ProviderId)
            .Generate()
            .ToDataModel();

        DataContext.Add(contactModel);
        await DataContext.SaveChangesAsync();

        var invoiceModel = new InvoiceFaker(ProviderId)
            .Generate();

        var invoiceLineItemModel = new InvoiceLineItemFaker()
            .WithPrice(0)
            .Generate()
            .ToDataModel(invoiceModel);

        var createInvoiceLineItemRequest = InvoiceLineItemRequestBuilder
            .FromInvoiceLineItem(invoiceLineItemModel)
            .Create();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithContactIds(new[] { contactModel.Id })
            .WithLineItems(createInvoiceLineItemRequest)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);
        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<Invoice[]>();
        clientResult.Should().HaveCount(1);

        var invoiceDbModel = await DataContext.Invoices.Include(invoiceDataModel => invoiceDataModel.InvoiceLineItems).SingleAsync(x => x.ProviderId == ProviderId);
        invoiceDbModel.Number.Should().Be("000001");

        var invoiceDbLineItemModel = invoiceDbModel.InvoiceLineItems.Single();
        invoiceDbLineItemModel.Id.Should().NotBeEmpty();
        invoiceDbLineItemModel.SalesTaxRate.Should().Be(invoiceLineItemModel.TaxRates.Sum(s => s.Rate));
        invoiceDbLineItemModel.Date.Should().Be(createInvoiceLineItemRequest.Date);
        invoiceDbLineItemModel.Should().BeEquivalentTo(createInvoiceLineItemRequest, opts => opts
            .ExcludingMissingMembers()
            .Excluding(x => x.Id)
            .Excluding(x => x.Date)
            .Excluding(x => x.SalesTaxRate));
        invoiceDbLineItemModel.Price.Should().Be(0);
    }

    [Fact]
    public async Task Get_Test()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var clientModelB = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var invoiceFaker = new InvoiceFaker(ProviderId);

        var invoiceA = invoiceFaker
            .WithContactId(clientModelA.Id)
            .Generate().ToDataModel();

        var invoiceB = invoiceFaker
            .WithContactId(clientModelB.Id)
            .Generate().ToDataModel();

        DataContext.AddRange(clientModelA, clientModelB, invoiceA, invoiceB);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<PaginatedResult<Invoice>>();
        clientResult.Items.Should().HaveCount(2);
        clientResult.TotalCount.Should().Be(2);

        var clientInvoiceA = clientResult.Items.FirstOrDefault(x => x.Id == invoiceA.Id);
        var clientInvoiceB = clientResult.Items.FirstOrDefault(x => x.Id == invoiceB.Id);

        clientInvoiceA.Should().BeEquivalentTo(invoiceA,
            options => options.Excluding(x => x.CreatedDateTimeUtc).Excluding(x => x.LastUpdatedDateTimeUtc).Excluding(x => x.History).Excluding(x => x.IssuedCredits).ExcludingMissingMembers());
        clientInvoiceB.Should().BeEquivalentTo(invoiceB,
            options => options.Excluding(x => x.CreatedDateTimeUtc).Excluding(x => x.LastUpdatedDateTimeUtc).Excluding(x => x.History).Excluding(x => x.IssuedCredits).ExcludingMissingMembers());
    }

    [Fact]
    public async Task Get_Test_TotalCount_Should_Respect_Filter()
    {
        // Arrange
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var clientModelB = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var clientAInvoices = new InvoiceFaker(ProviderId)
            .RuleFor(x => x.ContactId, clientModelA.Id)
            .Generate(3)
            .ToDataModel();

        var clientBInvoices = new InvoiceFaker(ProviderId)
            .RuleFor(x => x.ContactId, clientModelB.Id)
            .Generate()
            .ToDataModel();

        DataContext.Add(clientModelA);
        DataContext.Add(clientModelB);
        DataContext.AddRange(clientAInvoices);
        DataContext.AddRange(clientBInvoices);
        await DataContext.SaveChangesAsync();

        // Act  
        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices?contactIds={clientModelA.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);

        //Assert
        result.StatusCode.Should().Be(HttpStatusCode.OK);
        var clientResult = await result.Content.ReadAsAsync<PaginatedResult<Invoice>>();
        clientResult.Items.Should().HaveCount(3);
        clientResult.TotalCount.Should().Be(3);
        clientResult.Items.Should().AllSatisfy(invoice => invoice.ContactId.Should().Be(clientModelA.Id));
    }

    [Fact]
    public async Task Get_Single_Invoice_Test()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var invoiceA = new InvoiceFaker(ProviderId)
            .WithContactId(clientModelA.Id)
            .Generate()
            .ToDataModel();

        DataContext.AddRange(clientModelA, invoiceA);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices/{invoiceA.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var invoice = await result.Content.ReadAsAsync<Invoice>();
        var invoiceDetails = await result.Content.ReadAsAsync<InvoiceListEntry>();

        invoice.Should().BeEquivalentTo(invoiceA, options =>
        {
            options.Excluding(x => x.IssueDate);
            options.Excluding(x => x.ServiceDate);
            options.Excluding(x => x.History);
            options.Excluding(x => x.CreatedDateTimeUtc);
            options.Excluding(x => x.LastUpdatedDateTimeUtc);
            return options.ExcludingMissingMembers();
        });

        invoiceDetails.Contact.Should().BeEquivalentTo(clientModelA, options => options.ExcludingMissingMembers());
    }

    [Fact]
    public async Task UpdateInvoice_WithReminderAndNewLineItems_ShouldUpdateInvoiceAndReminder()
    {
        var contact = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var existingInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .RuleFor(x => x.DueDate, DateTime.UtcNow.AddDays(5).ToDateOnly())
            .Generate()
            .ToDataModel();

        var invoiceStaff = new InvoiceStaffDataModel(existingInvoice.Id, PersonId);
        var currencyHandler = CurrencyHandler.Get(existingInvoice.CurrencyCode);

        var existingReminder = ReminderJobDataModelBuilder.ForInvoice(existingInvoice)
            .Create();

        DataContext.Add(contact.ToDataModel());
        DataContext.AddRange(existingInvoice, invoiceStaff);
        DataContext.Add(existingReminder);
        await DataContext.SaveChangesAsync();

        var existingLineItem = InvoiceLineItemRequestBuilder
            .FromInvoiceLineItem(existingInvoice.InvoiceLineItems.FirstOrDefault())
            .Create();

        var newLineItem = InvoiceLineItemRequestBuilder.Any().Create();
        var newTitle = $"{Guid.NewGuid()}-updated";

        var taxRate = new ProviderTaxRateFaker()
            .Generate();

        newLineItem.TaxRates = [taxRate];

        var newLineItemTaxPrice = newLineItem.Price * taxRate.Rate / 100;

        var editInvoiceRequest = EditInvoiceRequestBuilder
            .FromInvoiceDataModel(existingInvoice)
            .WithTitle(newTitle)
            .WithDueDate(DateTime.UtcNow.AddDays(7).ToDateOnly())
            .WithLineItems(existingLineItem, newLineItem)
            .Create();


        var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/invoices/{existingInvoice.Id}")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(editInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<Invoice>();
        var salesTaxRateTotal = existingLineItem.TaxRates.Sum(s => s.Rate);
        clientResult.Should()
            .BeEquivalentTo(new
            {
                existingInvoice.Id,
                existingInvoice.Number,
                editInvoiceRequest.Title,
                editInvoiceRequest.DueDate,
                Price = currencyHandler.Round(existingLineItem.Price + newLineItem.Price + existingLineItem.Price * salesTaxRateTotal / 100 + newLineItemTaxPrice),
                TaxPrice = currencyHandler.Round(existingLineItem.Price * salesTaxRateTotal / 100 + newLineItemTaxPrice),
                LineItems = new[]
                {
                    new { existingLineItem.Description, existingLineItem.Detail },
                    new { newLineItem.Description, newLineItem.Detail }
                }
            }, opts => opts.ExcludingMissingMembers());

        var dbInvoice = await DataContext.Invoices.AsNoTracking()
            .Include(invoiceDataModel => invoiceDataModel.InvoiceLineItems)
            .ThenInclude(x => x.TaxRates)
            .FirstAsync(x => x.Id == existingInvoice.Id);
        var parserFactory = ResolveService<IInvoiceTemplateParserFactory>();
        var parser = parserFactory.Create("en", "UTC");
        var template = InvoiceTemplateSettings.Default();
        var schemaService = ResolveService<ISchemaService>();
        var schemas = await schemaService.GetDataSchemas([SchemaTypes.ContactSchema, SchemaTypes.ProviderSchema, SchemaTypes.StaffSchema, SchemaTypes.ProviderBillingSchema], ProviderId);
        var clientDetail = parser.ParseContactDetail(template, contact, schemas);
        var providerDetail = parser.ParseProviderDetail(template, Data.CurrentProvider, Data.CurrentProviderBillingSettings, schemas);
        var providerStaffRepository = ResolveService<IProviderStaffRepository>();
        var staff = await providerStaffRepository.GetStaffMember(ProviderId, PersonId);
        var staffDetail = parser.ParseStaffDetail(template, staff, schemas);
        var billToDetail = parser.ParseBillToDetail(template, contact, contact.Id, schemas);
        dbInvoice.Should().BeEquivalentTo(new
        {
            ContactDetail = clientDetail,
            ProviderDetail = providerDetail,
            StaffDetail = staffDetail,
            BillToDetail = billToDetail
        }, opts => opts.ExcludingMissingMembers());
    }

    [Fact]
    public async Task SendInvoice_WithUnpaidStatus_ShouldUpdateStatusAndCreateReminder()
    {
        var issueDate = DateTime.UtcNow.AddDays(1).ToDateOnly();
        var dueDate = DateTime.UtcNow.AddDays(5).ToDateOnly();

        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var existingInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(clientModelA.Id)
            .RuleFor(x => x.Status, InvoiceStatus.Unpaid)
            .RuleFor(x => x.DueDate, dueDate)
            .RuleFor(x => x.IssueDate, issueDate)
            .Generate()
            .ToDataModel();

        var existingReminder = ReminderJobDataModelBuilder.ForInvoice(existingInvoice)
            .Create();

        DataContext.Add(clientModelA);
        DataContext.Add(existingInvoice);
        DataContext.Add(existingReminder.ReminderSettings);
        await DataContext.SaveChangesAsync();
        var expectedEmailUpdate = new Email(new Faker().Internet.Email());
        using (var formContent = SendInvoiceRequestBuilder.Any().WithEmail(expectedEmailUpdate).CreateAsFormContent())
        {
            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices/{existingInvoice.Id}/send")
                .WithBearerAuthorization(IdentityToken)
                .WithContent(formContent)
                .Create();

            var result = await ClientApi.SendAsync(request);
            result.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        var dbInvoice = await DataContext.Invoices.AsNoTracking().FirstAsync(i => i.Id == existingInvoice.Id);
        dbInvoice.Should().BeEquivalentTo(new
            {
                Status = InvoiceStatus.Sent,
                History = new[]
                {
                    new
                    {
                        Email = expectedEmailUpdate.ToString()
                    }
                }
            },
            opts => opts.ExcludingMissingMembers());

        var dbReminder = await DataContext.ReminderJobs.AsNoTracking().FirstAsync(i => i.EntityId == existingInvoice.Id);
        dbReminder.Should().BeEquivalentTo(new
            {
                EntityType = ReminderEntityType.Invoice,
                SendDateTimeUtc = dueDate.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc),
                StartDateTimeUtc = issueDate.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc)
            },
            opts => opts.ExcludingMissingMembers());
    }

    [Fact]
    public async Task SendInvoice_WithPayment_RemainsAsPaid()
    {
        var localizer = ResolveService<IStringLocalizer<SharedMessages>>();

        var issueDate = DateTime.UtcNow.AddDays(1).ToDateOnly();
        var dueDate = DateTime.UtcNow.AddDays(5).ToDateOnly();

        var providerItem = new ProviderItemFaker().RuleFor(x => x.ProviderId, ProviderId).Generate();
        var client = new ContactFaker(ProviderId).WithIsClient(true).Generate();
        var task = new TaskFaker().WithContacts([client], TaskContactStatus.Attended)
            .RuleFor(x => x.ItemsSnapshot, [providerItem])
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.Type, TaskType.ClientEvent)
            .Generate();
        var billables = task.AsBillables(localizer, []).ToDataModel();

        var invoice = new InvoiceFaker(ProviderId)
            .WithContactId(client.Id)
            .WithPayment()
            .RuleFor(x => x.DueDate, dueDate)
            .RuleFor(x => x.IssueDate, issueDate)
            .RuleFor(x => x.LineItems, new InvoiceLineItemFaker()
                .ForBillableItem(billables.First().BillableItems.First())
                .Generate(1).ToArray())
            .Generate()
            .ToDataModel();

        var paymentIntent = new PaymentIntentFaker(ProviderId).WithInvoice(invoice).Generate();
        var payment = new PaymentFaker(ProviderId).WithPaymentIntent(paymentIntent).Generate();

        var paymentAllocation = new PaymentAllocationFaker(ProviderId, client.Id)
            .RuleFor(x => x.BillableItemId, billables.First().BillableItems.First().Id)
            .RuleFor(x => x.InvoiceLineItemId, invoice.InvoiceLineItems.First().Id)
            .RuleFor(x => x.Amount, invoice.InvoiceLineItems.First().Price)
            .RuleFor(x => x.PaymentId, payment.Id)
            .Generate();

        DataContext.AddRange(billables);
        DataContext.AddRange(client.ToDataModel(), task.ToDataModel(), providerItem.ToDataModel(), invoice, paymentIntent.ToDataModel(), payment.ToDataModel(), paymentAllocation.ToDataModel());

        await DataContext.SaveChangesAsync();

        var expectedEmailUpdate = new Email(new Faker().Internet.Email());
        using (var formContent = SendInvoiceRequestBuilder.Any().WithEmail(expectedEmailUpdate).CreateAsFormContent())
        {
            var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices/{invoice.Id}/send")
                .WithBearerAuthorization(IdentityToken)
                .WithContent(formContent)
                .Create();

            var result = await ClientApi.SendAsync(request);
            result.StatusCode.Should().Be(HttpStatusCode.OK);
        }

        var dbInvoice = await DataContext.Invoices.AsNoTracking().FirstAsync(i => i.Id == invoice.Id);
        dbInvoice.Should().BeEquivalentTo(new
        {
            Status = InvoiceStatus.Paid,
            History = new[]
            {
                new
                {
                    Email = expectedEmailUpdate.ToString()
                }
            }
        }, opts => opts.ExcludingMissingMembers());

        var dbAllocations = await DataContext.PaymentAllocations
            .AsNoTracking()
            .Where(pa =>
                pa.InvoiceLineItemId == invoice.InvoiceLineItems.First().Id
                && pa.PaymentId == payment.Id
                && pa.BillableItemId == billables.First().BillableItems.First().Id
            ).ToListAsync();
        dbAllocations.Should().NotBeEmpty();
    }

    [Theory]
    [InlineData("desc(issueDate)")]
    [InlineData("asc(issueDate)")]
    public async Task Get_invoice_should_sort_by_issue_date(string sortOrder)
    {
        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(10)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var issueDate = DateTime.UtcNow.ToDateOnly();
        foreach (var contact in contacts)
        {
            var invoicesDateNow = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, issueDate)
                .RuleFor(x => x.Status, InvoiceStatus.Unpaid)
                .Generate(3)
                .ToDataModel();
            await DataContext.AddRangeAsync(invoicesDateNow);

            var previousInvoices = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, (faker, _) => issueDate.AddDays(-1 * faker.PickRandom(5, 10, 15)))
                .RuleFor(x => x.Status, InvoiceStatus.Unpaid)
                .Generate(5)
                .ToDataModel();
            await DataContext.AddRangeAsync(previousInvoices);
        }

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices?sort={sortOrder}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InvoiceListEntry>>();
        result.Items.Should().NotBeNullOrEmpty();

        var sort = SortUtilities.Transform(sortOrder);
        if (sort.Primary.Direction == SortDirection.Asc)
            result.Items.Should().BeInAscendingOrder(x => x.IssueDate);
        else result.Items.Should().BeInDescendingOrder(x => x.IssueDate);
    }

    [Theory]
    [InlineData("desc(dueDate)")]
    [InlineData("asc(dueDate)")]
    public async Task Get_invoice_should_sort_by_issueDate(string sortOrder)
    {
        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(10)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var issueDate = DateTime.UtcNow.ToDateOnly();
        foreach (var contact in contacts)
        {
            var invoicesDateNow = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, issueDate)
                .RuleFor(x => x.DueDate, issueDate.AddDays(15))
                .Generate(3)
                .ToDataModel();
            await DataContext.AddRangeAsync(invoicesDateNow);

            var previousInvoices = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, (faker, _) => issueDate.AddDays(-1 * faker.PickRandom(5, 10, 15)))
                .RuleFor(x => x.DueDate, (_, x) => x.IssueDate.AddDays(15))
                .Generate(5)
                .ToDataModel();
            await DataContext.AddRangeAsync(previousInvoices);
        }

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices?sort={sortOrder}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InvoiceListEntry>>();
        result.Items.Should().NotBeNullOrEmpty();

        var sort = SortUtilities.Transform(sortOrder);
        if (sort.Primary.Direction == SortDirection.Asc)
            result.Items.Should().BeInAscendingOrder(x => x.DueDate);
        else result.Items.Should().BeInDescendingOrder(x => x.DueDate);
    }

    [Theory]
    [InlineData("desc(paymentDate)")]
    [InlineData("asc(paymentDate)")]
    public async Task Get_invoice_should_sort_by_paymentDate(string sortOrder)
    {
        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(10)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var issueDate = DateTime.UtcNow.ToDateOnly();
        foreach (var contact in contacts)
        {
            var invoicesDateNow = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, issueDate)
                .RuleFor(x => x.DueDate, issueDate.AddDays(15))
                .RuleFor(x => x.PaymentDate, issueDate.AddDays(7).ToDateTime(new TimeOnly(8, 0), DateTimeKind.Utc))
                .Generate(3)
                .ToDataModel();
            await DataContext.AddRangeAsync(invoicesDateNow);

            var previousInvoices = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, (faker, _) => issueDate.AddDays(-1 * faker.PickRandom(5, 10, 15)))
                .RuleFor(x => x.DueDate, (_, x) => x.IssueDate.AddDays(15))
                .RuleFor(x => x.PaymentDate, (_, x) => x.IssueDate.AddDays(15).ToDateTime(new TimeOnly(8, 0), DateTimeKind.Utc))
                .Generate(5)
                .ToDataModel();
            await DataContext.AddRangeAsync(previousInvoices);
        }

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices?sort={sortOrder}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InvoiceListEntry>>();
        result.Items.Should().NotBeNullOrEmpty();

        var sort = SortUtilities.Transform(sortOrder);
        if (sort.Primary.Direction == SortDirection.Asc)
            result.Items.Should().BeInAscendingOrder(x => x.PaymentDate);
        else result.Items.Should().BeInDescendingOrder(x => x.PaymentDate);
    }

    [Fact]
    public async Task Get_invoice_filter_by_staff_test()
    {
        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(10)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var issueDate = DateTime.UtcNow.ToDateOnly();
        var staffInvoices = new List<InvoiceDataModel>();
        foreach (var contact in contacts)
        {
            var invoicesDateNow = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, issueDate)
                .RuleFor(x => x.DueDate, issueDate.AddDays(15))
                .Generate(3)
                .ToDataModel()
                .WithProviderStaff(PersonId);
            staffInvoices.AddRange(invoicesDateNow);

            await DataContext.AddRangeAsync(invoicesDateNow);

            var previousInvoices = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, (faker, x) => issueDate.AddDays(-1 * faker.PickRandom(5, 10, 15)))
                .RuleFor(x => x.DueDate, (faker, x) => x.IssueDate.AddDays(15))
                .Generate(5)
                .ToDataModel()
                .WithProviderStaff(Data.StaffMember.Id);

            await DataContext.AddRangeAsync(previousInvoices);
        }

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices?staffIds={PersonId}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InvoiceListEntry>>();
        result.Items.Should().NotBeNullOrEmpty();

        var nonCurrentPersonInvoices = result.Items.Where(x => x.Staff.Any(y => y.Id != PersonId)).ToArray();
        nonCurrentPersonInvoices.Should().BeEmpty();
        result.Items.Should().HaveCount(staffInvoices.Count);
    }

    [Fact]
    public async Task Get_invoice_filter_by_multiple_staff_test()
    {
        var persons = new ProviderStaffMemberFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(3)
            .ToDataModel()
            .ToArray();

        await DataContext.Persons.AddRangeAsync(persons);

        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(10)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var issueDate = DateTime.UtcNow.ToDateOnly();
        var staffInvoices = new List<InvoiceDataModel>();
        foreach (var contact in contacts)
        {
            var invoicesDateNow = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, issueDate)
                .RuleFor(x => x.DueDate, issueDate.AddDays(15))
                .RuleFor(x => x.PaymentDate, (faker, x) => x.IssueDate.AddDays(15).ToDateTime(new TimeOnly(8, 0), DateTimeKind.Utc))
                .Generate(3)
                .ToDataModel()
                .WithProviderStaff(persons[0].Id, persons[1].Id);

            staffInvoices.AddRange(invoicesDateNow);
            await DataContext.AddRangeAsync(invoicesDateNow);

            var invoicesDateNow2 = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, issueDate)
                .RuleFor(x => x.DueDate, issueDate.AddDays(15))
                .RuleFor(x => x.PaymentDate, (faker, x) => x.IssueDate.AddDays(15).ToDateTime(new TimeOnly(8, 0), DateTimeKind.Utc))
                .Generate()
                .ToDataModel()
                .WithProviderStaff(persons[2].Id);

            staffInvoices.Add(invoicesDateNow2);
            await DataContext.AddRangeAsync(invoicesDateNow2);

            var previousInvoices = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, (faker, x) => issueDate.AddDays(-1 * faker.PickRandom(5, 10, 15)))
                .RuleFor(x => x.DueDate, (faker, x) => x.IssueDate.AddDays(15))
                .RuleFor(x => x.PaymentDate, (faker, x) => x.IssueDate.AddDays(15).ToDateTime(new TimeOnly(8, 0), DateTimeKind.Utc))
                .Generate(5)
                .ToDataModel()
                .WithProviderStaff(Data.StaffMember.Id, PersonId);

            await DataContext.AddRangeAsync(previousInvoices);
        }

        await DataContext.SaveChangesAsync();

        var staffIds = persons.Select(x => x.Id).ToArray();
        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices?staffIds={staffIds[0]}&staffIds={staffIds[1]}&staffIds={staffIds[2]}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InvoiceListEntry>>();
        result.Items.Should().NotBeNullOrEmpty();

        var nonCurrentPersonInvoices = result.Items.Where(x => x.Staff.Any(y => !staffIds.Contains(y.Id))).ToArray();
        nonCurrentPersonInvoices.Should().BeEmpty();
        result.Items.Should().HaveCount(staffInvoices.Count);
    }

    [Fact]
    public async Task Get_invoice_filter_by_client_test()
    {
        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(10)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var issueDate = DateTime.UtcNow.ToDateOnly();
        var contactInvoices = new List<InvoiceDataModel>();
        foreach (var contact in contacts)
        {
            var invoicesDateNow = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, issueDate)
                .RuleFor(x => x.DueDate, issueDate.AddDays(15))
                .Generate(3)
                .ToDataModel()
                .WithProviderStaff(PersonId);
            contactInvoices.AddRange(invoicesDateNow);
            await DataContext.AddRangeAsync(invoicesDateNow);
        }

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices?contactIds={contacts[0].Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InvoiceListEntry>>();
        result.Items.Should().NotBeNullOrEmpty();
        var nonCurrentContactInvoices = result.Items.Where(x => x.ContactId != contacts[0].Id).ToArray();
        nonCurrentContactInvoices.Should().BeEmpty();
        result.Items.Should().HaveCount(3);
    }

    [Fact]
    public async Task Get_invoice_filter_by_multiple_client_test()
    {
        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(10)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var issueDate = DateTime.UtcNow.ToDateOnly();
        foreach (var contact in contacts)
        {
            var invoicesDateNow = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, issueDate)
                .RuleFor(x => x.DueDate, issueDate.AddDays(15))
                .Generate(3)
                .ToDataModel()
                .WithProviderStaff(PersonId);
            await DataContext.AddRangeAsync(invoicesDateNow);
        }

        await DataContext.SaveChangesAsync();

        var contactIds = new[]
        {
            contacts[0].Id,
            contacts[1].Id,
            contacts[2].Id
        };
        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices?contactIds={contactIds[0]}&contactIds={contactIds[1]}&contactIds={contactIds[2]}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InvoiceListEntry>>();
        result.Items.Should().NotBeNullOrEmpty();
        var nonCurrentContactInvoices = result.Items.Where(x => !contactIds.Contains(x.ContactId)).ToArray();
        nonCurrentContactInvoices.Should().BeEmpty();
        result.Items.Should().HaveCount(9);
    }

    [Theory]
    [InlineData(InvoiceStatus.Paid, InvoiceStatus.Sent)]
    [InlineData(InvoiceStatus.Sent, InvoiceStatus.Unpaid)]
    [InlineData(InvoiceStatus.Unpaid, InvoiceStatus.Void)]
    [InlineData(InvoiceStatus.Void, InvoiceStatus.Paid)]
    [InlineData(InvoiceStatus.Processing, InvoiceStatus.Paid)]
    public async Task Get_invoice_filter_by_status_test(InvoiceStatus status, InvoiceStatus additionalStatus)
    {
        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(10)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var issueDate = DateTime.UtcNow.ToDateOnly();
        foreach (var contact in contacts)
        {
            var invoicesDateNow = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, issueDate)
                .RuleFor(x => x.DueDate, issueDate.AddDays(15))
                .RuleFor(x => x.Status, status)
                .Generate(3)
                .ToDataModel()
                .WithProviderStaff(PersonId);
            await DataContext.AddRangeAsync(invoicesDateNow);

            var additionalInvoices = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, issueDate)
                .RuleFor(x => x.DueDate, issueDate.AddDays(15))
                .RuleFor(x => x.Status, additionalStatus)
                .Generate(3)
                .ToDataModel()
                .WithProviderStaff(PersonId);
            await DataContext.AddRangeAsync(additionalInvoices);
        }

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices?status={status}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InvoiceListEntry>>();
        result.Items.Should().NotBeNullOrEmpty();
        var nonCurrentStatusInvoices = result.Items.Where(x => x.Status != status).ToArray();
        nonCurrentStatusInvoices.Should().BeEmpty();
        result.Items.Should().HaveCount(30);
    }

    [Fact]
    public async Task Get_invoices_filter_by_date_range_test()
    {
        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(10)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var issueDateFrom = DateTime.UtcNow.ToDateOnly();
        var issueDateTo = DateTime.UtcNow.AddDays(15).ToDateOnly();

        var previousIssueDateFrom = DateTime.UtcNow.AddDays(-30).ToDateOnly();
        var previousIssueDateTo = DateTime.UtcNow.AddDays(-45).ToDateOnly();
        foreach (var contact in contacts)
        {
            var invoicesDateNow = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, (faker, x) => faker.Date.BetweenDateOnly(issueDateFrom, issueDateTo))
                .RuleFor(x => x.DueDate, issueDateFrom.AddDays(15))
                .Generate(3)
                .ToDataModel()
                .WithProviderStaff(PersonId);
            await DataContext.AddRangeAsync(invoicesDateNow);

            var previousInvoices = new InvoiceFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.ContactId, contact.Id)
                .RuleFor(x => x.IssueDate, (faker, x) => faker.Date.BetweenDateOnly(previousIssueDateFrom, previousIssueDateTo))
                .RuleFor(x => x.DueDate, issueDateFrom.AddDays(15))
                .Generate(3)
                .ToDataModel()
                .WithProviderStaff(PersonId);
            await DataContext.AddRangeAsync(previousInvoices);
        }

        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices?fromDate={issueDateFrom.ToDateString()}&toDate={issueDateTo.ToDateString()}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<PaginatedResult<InvoiceListEntry>>();
        result.Items.Should().NotBeNullOrEmpty();
        foreach (var item in result.Items)
        {
            item.DueDate.Should().BeOnOrAfter(issueDateFrom).And.BeOnOrBefore(issueDateTo);
            item.LineItems.Should().NotBeNullOrEmpty();
        }
    }

    [Fact]
    public async Task Create_With_LineItem_TaxRates_Test()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        DataContext.Add(clientModelA);
        await DataContext.SaveChangesAsync();

        var tax10 = new ProviderTaxRateFaker()
            .RuleFor(x => x.Name, "Sales Tax")
            .RuleFor(x => x.Rate, 10)
            .Generate();

        var tax15 = new ProviderTaxRateFaker()
            .RuleFor(x => x.Name, "GST")
            .RuleFor(x => x.Rate, 15)
            .Generate();

        var tax20 = new ProviderTaxRateFaker()
            .RuleFor(x => x.Name, "VAT")
            .RuleFor(x => x.Rate, 20)
            .Generate();

        var customTax5 = new ItemTaxRateFaker()
            .RuleFor(x => x.Name, "Custom Tax")
            .RuleFor(x => x.Rate, 5)
            .RuleFor(x => x.TaxRateId, (Guid?)null)
            .RuleFor(x => x.Id, (Guid?)null)
            .Generate();

        var customTax7 = new ItemTaxRateFaker()
            .RuleFor(x => x.Name, "Custom Tax")
            .RuleFor(x => x.Rate, 7)
            .RuleFor(x => x.TaxRateId, (Guid?)null)
            .RuleFor(x => x.Id, (Guid?)null)
            .Generate();
        var lineItem1 = InvoiceLineItemRequestBuilder.Any()
            .WithPrice(100)
            .WithUnits(1)
            .WithTaxRates(tax15, tax20, customTax5)
            .Create();

        var lineItem2 = InvoiceLineItemRequestBuilder.Any()
            .WithPrice(200)
            .WithUnits(1)
            .WithTaxRates(tax10, tax20, customTax5, customTax7)
            .Create();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithContactIds([clientModelA.Id])
            .WithLineItems(lineItem1, lineItem2)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<InvoiceListEntry[]>();
        clientResult.Should().HaveCount(1);
        clientResult[0].TaxGroups.Should().HaveCount(5);
        var taxGroups = clientResult[0].TaxGroups;
        taxGroups.Select(x => x.Rate).Should()
            .BeEquivalentTo([tax15.Rate, tax20.Rate, tax10.Rate, customTax5.Rate, customTax7.Rate]);
        taxGroups.Select(x => x.TaxAmount).Should()
            .BeEquivalentTo([
                lineItem1.Price * (tax15.Rate / 100), (lineItem1.Price + lineItem2.Price) * (tax20.Rate / 100), lineItem2.Price * (tax10.Rate / 100),
                (lineItem1.Price + lineItem2.Price) * (customTax5.Rate / 100), lineItem2.Price * (customTax7.Rate / 100)
            ]);
        taxGroups.Select(x => x.Name).Should().BeEquivalentTo(tax10.Name, tax20.Name, tax15.Name, "Tax", "Tax");
        taxGroups.Select(x => x.Amount).Should().BeEquivalentTo(new[] { lineItem1.Price, lineItem1.Price + lineItem2.Price, lineItem2.Price, lineItem1.Price + lineItem2.Price, lineItem2.Price });
        var dbInvoice = await DataContext.Invoices.AsNoTracking()
            .Include(invoiceDataModel => invoiceDataModel.InvoiceLineItems)
            .ThenInclude(x => x.TaxRates)
            .FirstAsync(x => x.Id == clientResult[0].Id);
        dbInvoice.InvoiceLineItems.Should().HaveCount(2);
        dbInvoice.InvoiceLineItems.SelectMany(x => x.TaxRates).Select(x => x.Rate).Should().BeEquivalentTo(lineItem1.TaxRates.Select(x => x.Rate).Concat(lineItem2.TaxRates.Select(x => x.Rate)));
        dbInvoice.InvoiceLineItems.SelectMany(x => x.TaxRates).Select(x => x.Name).Should().BeEquivalentTo(lineItem1.TaxRates.Select(x => x.Name).Concat(lineItem2.TaxRates.Select(x => x.Name)));
    }

    [Fact]
    public async Task Create_With_LineItem_Without_TaxRates_Test()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        DataContext.Add(clientModelA);
        await DataContext.SaveChangesAsync();

        var lineItem1 = InvoiceLineItemRequestBuilder.Any()
            .WithPrice(100)
            .WithUnits(1)
            .Create();

        var lineItem2 = InvoiceLineItemRequestBuilder.Any()
            .WithPrice(200)
            .WithUnits(1)
            .Create();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithContactIds([clientModelA.Id])
            .WithLineItems(lineItem1, lineItem2)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<InvoiceListEntry[]>();
        clientResult.Should().HaveCount(1);
        var invoice = clientResult.First();
        invoice.TaxExclusivePrice.Should().Be(lineItem1.Price + lineItem2.Price);
    }

    [Fact]
    public async Task Create_With_LineItem_WithSingle_TaxRate_Test()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        DataContext.Add(clientModelA);
        await DataContext.SaveChangesAsync();

        var tax10 = new ProviderTaxRateFaker()
                .RuleFor(x => x.Name, "Sales Tax")
                .RuleFor(x => x.Rate, 10)
                .Generate()
            ;

        var lineItem1 = InvoiceLineItemRequestBuilder.Any()
            .WithPrice(100)
            .WithUnits(1)
            .WithTaxRates(tax10)
            .Create();

        var lineItem2 = InvoiceLineItemRequestBuilder.Any()
            .WithPrice(200)
            .WithUnits(1)
            .WithTaxRates(tax10)
            .Create();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithContactIds([clientModelA.Id])
            .WithLineItems(lineItem1, lineItem2)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<InvoiceListEntry[]>();
        clientResult.Should().HaveCount(1);
        clientResult[0].TaxGroups.Should().HaveCount(1);
        var taxGroups = clientResult[0].TaxGroups;
        taxGroups.Select(x => x.Rate).Should()
            .BeEquivalentTo([tax10.Rate]);
        taxGroups.Select(x => x.TaxAmount).Should()
            .BeEquivalentTo([(lineItem1.Price + lineItem2.Price) * tax10.Rate / 100]);
        taxGroups.Select(x => x.Name).Should().BeEquivalentTo(tax10.Name);
        taxGroups.Select(x => x.Amount).Should().BeEquivalentTo(new[] { lineItem1.Price + lineItem2.Price });
        var dbInvoice = await DataContext.Invoices.AsNoTracking()
            .Include(invoiceDataModel => invoiceDataModel.InvoiceLineItems)
            .ThenInclude(x => x.TaxRates)
            .FirstAsync(x => x.Id == clientResult[0].Id);
        dbInvoice.InvoiceLineItems.Should().HaveCount(2);
        dbInvoice.InvoiceLineItems.SelectMany(x => x.TaxRates).Select(x => x.Rate).Should().BeEquivalentTo(lineItem1.TaxRates.Select(x => x.Rate).Concat(lineItem2.TaxRates.Select(x => x.Rate)));
        dbInvoice.InvoiceLineItems.SelectMany(x => x.TaxRates).Select(x => x.Name).Should().BeEquivalentTo(lineItem1.TaxRates.Select(x => x.Name).Concat(lineItem2.TaxRates.Select(x => x.Name)));
    }

    [Fact]
    public async Task UpdateInvoice_WithTaxRates_Test()
    {
        var contact = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var existingInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .RuleFor(x => x.DueDate, DateTime.UtcNow.AddDays(5).ToDateOnly())
            .Generate()
            .ToDataModel();
        var invoiceStaff = new InvoiceStaffDataModel(existingInvoice.Id, PersonId);
        var currencyHandler = CurrencyHandler.Get(existingInvoice.CurrencyCode);

        DataContext.Add(contact.ToDataModel());
        DataContext.AddRange(existingInvoice, invoiceStaff);
        await DataContext.SaveChangesAsync();

        var existingLineItem = InvoiceLineItemRequestBuilder
            .FromInvoiceLineItem(existingInvoice.InvoiceLineItems.FirstOrDefault())
            .Create();

        var newLineItem = InvoiceLineItemRequestBuilder.Any().Create();
        var newTitle = $"{Guid.NewGuid()}-updated";

        var taxRates = new ProviderTaxRateFaker()
            .Generate(2);

        newLineItem.TaxRates = [.. taxRates];

        var newLineItemTaxPrice = newLineItem.Price * taxRates.Sum(x => x.Rate) / 100;

        var editInvoiceRequest = EditInvoiceRequestBuilder
            .FromInvoiceDataModel(existingInvoice)
            .WithTitle(newTitle)
            .WithDueDate(DateTime.UtcNow.AddDays(7).ToDateOnly())
            .WithLineItems(existingLineItem, newLineItem)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/invoices/{existingInvoice.Id}")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(editInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<InvoiceListEntry>();
        var salesTaxRateTotal = existingLineItem.TaxRates.Sum(s => s.Rate);
        clientResult.TaxGroups.Should().BeEquivalentTo(taxRates.Select(x => new TaxGroup
        {
            Id = x.Id,
            Rate = x.Rate,
            Name = x.Name,
            TaxAmount = currencyHandler.Round(x.Rate * newLineItem.Price * newLineItem.Units / 100),
            Amount = currencyHandler.Round(newLineItem.Price)
        }).Append(new TaxGroup
        {
            Id = existingLineItem.TaxRates.First().Id,
            Amount = currencyHandler.Round(existingLineItem.Price * existingLineItem.Units),
            TaxAmount = currencyHandler.Round(existingLineItem.Price * existingLineItem.Units * (salesTaxRateTotal / 100)),
            Name = existingLineItem.TaxRates.First().Name,
            Rate = salesTaxRateTotal
        }));

        clientResult.Should()
            .BeEquivalentTo(new
            {
                existingInvoice.Id,
                existingInvoice.Number,
                editInvoiceRequest.Title,
                editInvoiceRequest.DueDate,
                Price = currencyHandler.Round(existingLineItem.Price + newLineItem.Price + existingLineItem.Price * salesTaxRateTotal / 100 + newLineItemTaxPrice),
                TaxPrice = currencyHandler.Round(existingLineItem.Price * salesTaxRateTotal / 100 + newLineItemTaxPrice),
                LineItems = new[]
                {
                    new { existingLineItem.Description },
                    new { newLineItem.Description }
                }
            }, opts => opts.ExcludingMissingMembers());
    }


    [Fact]
    public async Task UpdateInvoice_TrimsNumberWhitespace()
    {
        var contact = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var existingInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .Generate()
            .ToDataModel();

        var invoiceStaff = new InvoiceStaffDataModel(existingInvoice.Id, PersonId);
        var currencyHandler = CurrencyHandler.Get(existingInvoice.CurrencyCode);

        DataContext.AddRange(contact.ToDataModel(), existingInvoice, invoiceStaff);
        await DataContext.SaveChangesAsync();

        var editInvoiceRequest = EditInvoiceRequestBuilder
            .FromInvoiceDataModel(existingInvoice)
            .WithNumber("  123  ")
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/invoices/{existingInvoice.Id}")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(editInvoiceRequest)
            .Create();

        var response = await ClientApi.SendAsync(request);

        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InvoiceListEntry>();
        result.Number.Should().Be("123");

        var dbInvoice = await DataContext.Invoices.AsNoTracking().Where(i => i.Id == result.Id).SingleAsync();
        dbInvoice.Number.Should().Be("123");
    }

    [Fact]
    public async Task Get_Invoices_WithTaxRates_ShouldReturnTaxGroup()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var invoiceA = new InvoiceFaker(ProviderId)
            .WithContactId(clientModelA.Id)
            .Generate();

        var currencyHandler = CurrencyHandler.Get(invoiceA.CurrencyCode);

        DataContext.Add(clientModelA);
        DataContext.Add(invoiceA.ToDataModel());
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<PaginatedResult<InvoiceListEntry>>();
        clientResult.Items.Should().HaveCount(1);

        var clientInvoiceA = clientResult.Items.FirstOrDefault(x => x.Id == invoiceA.Id);
        var taxGroups = invoiceA.TaxGroups;
        clientInvoiceA.TaxGroups.Should().BeEquivalentTo(taxGroups);
        clientInvoiceA.Total.Should().Be(invoiceA.LineItems.Sum(x => currencyHandler.Round(x.Amount + x.TaxRates.Sum(y => y.Rate / 100 * x.Amount))));
    }

    [Fact]
    public async Task Get_OnlinePaymentInvoice_Invoices_WithTaxRates_ShouldReturnTaxGroup()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var invoiceA = new InvoiceFaker(ProviderId)
            .WithContactId(clientModelA.Id)
            .Generate();

        var currencyHandler = CurrencyHandler.Get(invoiceA.CurrencyCode);

        DataContext.Add(clientModelA);
        DataContext.Add(invoiceA.ToDataModel());
        await DataContext.SaveChangesAsync();
        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/invoices/public/{invoiceA.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientInvoiceA = await result.Content.ReadAsAsync<OnlinePaymentInvoice>();
        var taxGroups = invoiceA.TaxGroups;
        clientInvoiceA.Invoice.TaxGroups.Should().BeEquivalentTo(taxGroups);
        clientInvoiceA.Invoice.Total.Should().Be(invoiceA.LineItems.Sum(x => x.Amount + currencyHandler.Round(x.TaxRates.Sum(y => y.Rate / 100 * x.Amount))));
    }

    [Fact]
    public async Task Create_With_LineItem_Without_TaxRates_ShouldReturnTaxGroup()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        DataContext.Add(clientModelA);
        await DataContext.SaveChangesAsync();

        var lineItem1 = InvoiceLineItemRequestBuilder.Any()
            .WithPrice(100)
            .WithTaxRates(new ItemTaxRate { TaxRateId = null, Name = null, Rate = 10 })
            .WithUnits(5)
            .Create();

        var lineItem2 = InvoiceLineItemRequestBuilder.Any()
            .WithPrice(200)
            .WithTaxRates(new ItemTaxRate { TaxRateId = null, Name = null, Rate = 10 })
            .WithUnits(2)
            .Create();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithContactIds([clientModelA.Id])
            .WithLineItems(lineItem1, lineItem2)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<InvoiceListEntry[]>();
        clientResult.Should().HaveCount(1);
        var invoice = clientResult.First();
        invoice.TaxExclusivePrice.Should().Be(lineItem1.Price * lineItem1.Units + lineItem2.Price * lineItem2.Units);
        invoice.Total.Should().Be(lineItem1.Price * lineItem1.Units * (1 + lineItem1.TaxRates.Sum(s => s.Rate) / 100) +
                                  lineItem2.Price * lineItem2.Units * (1 + lineItem1.TaxRates.Sum(s => s.Rate) / 100));
        invoice.TaxGroups.Should().NotBeNullOrEmpty();
        invoice.TaxGroups.Should().BeEquivalentTo([
            new
            {
                Id = (Guid?)null,
                Name = "Tax",
                Rate = 10,
                TaxAmount = 90,
                Amount = 900
            }
        ], opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task Rounding_Invoice_Tests()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        await DataContext.AddRangeAsync(clientModelA.ToDataModel());
        await DataContext.SaveChangesAsync();
        var taxRate = new ProviderTaxRateFaker()
            .WithRate(4)
            .Generate();

        var lineItem1 = InvoiceLineItemRequestBuilder.Any()
            .WithPrice(99.9m)
            .WithUnits(1)
            .WithTaxRates(taxRate)
            .Create();

        var lineItem2 = InvoiceLineItemRequestBuilder.Any()
            .WithPrice(99.9m)
            .WithUnits(1)
            .WithTaxRates(taxRate)
            .Create();


        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithStaffIds([PersonId])
            .WithContactIds([clientModelA.Id])
            .WithLineItems(lineItem1, lineItem2)
            .Create();
        // if we do rounding at invoice level, result should be 207.79 
        var amount = CurrencyHandler.Get(CurrencyCodes.USD).Round(createInvoiceRequest.LineItems.Sum(x => x.Price * (1 + x.TaxRates.First().Rate / 100)));
        amount.Should().Be(207.79m);

        // if we do rounding at line item level, result will be 207.80
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<Invoice[]>();
        clientResult.First().Total.Should().Be(207.80m);
        var dbInvoice = await DataContext.Invoices.AsNoTracking()
            .FirstAsync(x => x.Id == clientResult.First().Id);
        dbInvoice.TaxExclusivePrice.Should().Be(lineItem1.Price + lineItem2.Price);
        dbInvoice.TaxPrice.Should().Be(8m);
    }

    [Fact]
    public async Task Create_Sets_Detail_To_Empty_If_Hidden_In_Template_Test()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var clientModelB = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var billTo = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var taxRates = new ProviderTaxRateFaker()
            .WithProviderId(ProviderId)
            .Generate(3)
            .ToArray();

        var persistedInvoiceTemplate = new InvoiceTemplateSettingsFaker(ProviderId, true)
            .RuleFor(x => x.BillToDetailTemplate, new SchemaTemplateDefinition
            {
                Template = "Bill to detail",
                IsEnabled = false
            })
            .RuleFor(x => x.ContactDetailTemplate, new SchemaTemplateDefinition
            {
                Template = "Contact detail"
            })
            .Generate();

        await DataContext.AddRangeAsync(
            persistedInvoiceTemplate.ToDataModel(),
            clientModelA.ToDataModel(),
            clientModelB.ToDataModel(),
            billTo.ToDataModel()
        );

        await DataContext.SaveChangesAsync();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithStaffIds([PersonId])
            .WithContactIds([clientModelA.Id, clientModelB.Id])
            .WithBillToId(billTo.Id)
            .WithTaxRates(taxRates)
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);


        var clientResult = await result.Content.ReadAsAsync<Invoice[]>();

        clientResult.First().BillToDetail.Should().Be("");
        clientResult.First().ContactDetail.Should().Be("Contact detail");
    }

    [Fact]
    public async Task Delete_With_Stripe_Payment_Test()
    {
        // Arrange
        var clientModel = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var invoice = new InvoiceFaker()
            .RuleFor(_ => _.ProviderId, ProviderId)
            .RuleFor(_ => _.ContactId, clientModel.Id)
            .RuleFor(_ => _.Status, InvoiceStatus.Paid)
            .RuleFor(_ => _.DueDate, (faker, _) => _.IssueDate.AddDays(7))
            .Generate();

        var paymentIntent = new PaymentIntentFaker(ProviderId)
            .WithInvoice(invoice)
            .Generate();

        var payment = new PaymentFaker(ProviderId)
            .WithPaymentIntent(paymentIntent)
            .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
            .RuleFor(x => x.PaymentProvider, PaymentProviders.Stripe)
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.NZD)
            .Generate();

        await DataContext.AddRangeAsync(
            clientModel.ToDataModel(),
            invoice.ToDataModel(),
            paymentIntent.ToDataModel(),
            payment.ToDataModel()
        );
        await DataContext.SaveChangesAsync();


        Fixture.ClearEvents();

        // Act
        var request = new HttpRequestMessageBuilder(HttpMethod.Delete, $"api/providers/{ProviderId}/invoices/{invoice.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);
        var events = Fixture.GetPublishedEventsOfType<InvoiceTrashedEvent>();

        // Asssert
        result.IsSuccessStatusCode.Should().BeTrue();
        result.StatusCode.Should().Be(HttpStatusCode.OK);
        events.Should().HaveCount(1).And.BeEquivalentTo([
            new InvoiceTrashedEvent(new InvoiceEventModel(invoice.Id, ProviderId, invoice.LineItems.Select(x => new InvoiceLineItemEventModel(x.Id, x.BillableItemId)).ToArray(), IsBillingV2: true))
        ]);
        var id = await result.Content.ReadAsAsync<Guid>();
        id.Should().Be(invoice.Id);
    }

    [Fact]
    public async Task DeleteInvoice_WhenCreditsApplied_ShouldIssueCreditAdjustment()
    {
        // Arrange
        var clientModel = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var invoice = new InvoiceFaker()
            .RuleFor(_ => _.ProviderId, ProviderId)
            .RuleFor(_ => _.ContactId, clientModel.Id)
            .RuleFor(_ => _.DueDate, (faker, _) => _.IssueDate.AddDays(7))
            .RuleFor(_ => _.CreditsUsed, 100)
            .Generate();

        await DataContext.AddRangeAsync(
            clientModel.ToDataModel(),
            invoice.ToDataModel()
        );
        await DataContext.SaveChangesAsync();
        Fixture.ClearEvents();

        // Act
        var request = new HttpRequestMessageBuilder(HttpMethod.Delete, $"api/providers/{ProviderId}/invoices/{invoice.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);
        var events = Fixture.GetPublishedEventsOfType<InvoiceTrashedEvent>();

        // Asssert
        result.StatusCode.Should().Be(HttpStatusCode.OK);
        var lineItemsEventModel = invoice.LineItems.Select(x => new InvoiceLineItemEventModel(x.Id, x.BillableItemId)).ToArray();
        events.Should().HaveCount(1).And.BeEquivalentTo([new InvoiceTrashedEvent(new InvoiceEventModel(invoice.Id, ProviderId, lineItemsEventModel, IsBillingV2: true))]);
        var id = await result.Content.ReadAsAsync<Guid>();
        id.Should().Be(invoice.Id);
        var createdCreditAdjustment = await DataContext.ContactCreditAdjustments
            .Where(x => x.ProviderId == ProviderId && x.ContactId == clientModel.Id && x.InvoiceId == invoice.Id)
            .FirstOrDefaultAsync();
        var localizer = ResolveService<IStringLocalizer<SharedMessages>>();
        createdCreditAdjustment.Should().NotBeNull();
        createdCreditAdjustment.Amount.Should().Be(invoice.CreditsUsed);
        createdCreditAdjustment.Type.Should().Be(ContactCreditAdjustmentTransactionType.Credit);
        createdCreditAdjustment.Description.Should().Be(localizer[nameof(SharedMessages.InvoiceCreditService_InvoiceRemoved), invoice.Number]);
        createdCreditAdjustment.Reason.Should().Be(ContactCreditAdjustmentTransactionReason.InvoiceRemoved);
    }

    [Fact]
    public async Task CreateInvoice_Should_AppliedCredit()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();
        var contactCreditAdjustment = new ContactCreditAdjustmentFaker(ProviderId, clientModelA.Id)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.NZD)
            .RuleFor(x => x.Type, ContactCreditAdjustmentTransactionType.Credit)
            .Generate()
            .ToDataModel();

        var creditSummary = new ContactCreditSummaryDataModel
        {
            ContactId = clientModelA.Id,
            TotalCredits = 100,
            ProviderId = ProviderId,
            CurrencyCode = CurrencyCodes.NZD
        };
        await DataContext.AddRangeAsync(clientModelA, contactCreditAdjustment, creditSummary);
        await DataContext.SaveChangesAsync();

        var lineItem = InvoiceLineItemRequestBuilder.Any().Create();

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithContactIds([clientModelA.Id])
            .WithLineItems(lineItem)
            .UseCredits()
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<Invoice[]>();
        clientResult.Should().HaveCount(1);

        var dbModel = await DataContext.Invoices
            .Include(invoiceDataModel => invoiceDataModel.InvoiceLineItems)
            .SingleAsync(x => x.ProviderId == ProviderId && x.ContactId == clientModelA.Id);

        dbModel.CreditsUsed.Should().Be(100);

        var dbCreditAdjustments = await DataContext.ContactCreditAdjustments
            .Where(x => x.ProviderId == ProviderId && x.ContactId == clientModelA.Id).ToListAsync();
        dbCreditAdjustments.Count.Should().Be(2, "One for the adjustment and one for the credit usage");
        dbCreditAdjustments.Should().ContainSingle(x => x.Amount == 100 && x.Type == ContactCreditAdjustmentTransactionType.Credit, "Credit adjustment should be created");
        dbCreditAdjustments.Should().ContainSingle(x => x.Amount == -100 && x.Type == ContactCreditAdjustmentTransactionType.Debit, "Credit usage should be created");

        var dbCreditSummary = await DataContext.ContactCreditSummaries
            .AsNoTracking()
            .SingleAsync(x => x.ProviderId == ProviderId && x.ContactId == clientModelA.Id && x.CurrencyCode == CurrencyCodes.NZD);
        dbCreditSummary.TotalCredits.Should().Be(0);
    }

    [Fact]
    public async Task CreateInvoice_ShouldNotIssueCredit_IfInvoiceIsNegative()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var contactCreditAdjustment = new ContactCreditAdjustmentFaker(ProviderId, clientModelA.Id)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.NZD)
            .RuleFor(x => x.Type, ContactCreditAdjustmentTransactionType.Credit)
            .Generate()
            .ToDataModel();

        var creditSummary = new ContactCreditSummaryDataModel
        {
            ContactId = clientModelA.Id,
            TotalCredits = 100,
            ProviderId = ProviderId,
            CurrencyCode = CurrencyCodes.NZD
        };
        await DataContext.AddRangeAsync(clientModelA, contactCreditAdjustment, creditSummary);
        await DataContext.SaveChangesAsync();

        var lineItem = InvoiceLineItemRequestBuilder.Any().Create();
        lineItem.Price = -50;

        var createInvoiceRequest = CreateInvoiceRequestBuilder
            .Any()
            .WithProviderId(ProviderId)
            .WithContactIds([clientModelA.Id])
            .WithLineItems(lineItem)
            .UseCredits()
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(createInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<Invoice[]>();
        clientResult.Should().HaveCount(1);

        var dbModel = await DataContext.Invoices
            .Include(invoiceDataModel => invoiceDataModel.InvoiceLineItems)
            .SingleAsync(x => x.ProviderId == ProviderId && x.ContactId == clientModelA.Id);

        dbModel.CreditsUsed.Should().Be(0);

        var dbCreditAdjustments = await DataContext.ContactCreditAdjustments
            .Where(x => x.ProviderId == ProviderId && x.ContactId == clientModelA.Id).ToListAsync();
        dbCreditAdjustments.Count.Should().Be(1, "Only the original adjustment");

        var dbCreditSummary = await DataContext.ContactCreditSummaries
            .AsNoTracking()
            .SingleAsync(x => x.ProviderId == ProviderId && x.ContactId == clientModelA.Id && x.CurrencyCode == CurrencyCodes.NZD);
        dbCreditSummary.TotalCredits.Should().Be(100, "because the credits have not been used or applied");
    }

    [Fact]
    public async Task GetInvoice_Return_Correct_AmountDue()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var invoiceA = new InvoiceFaker(ProviderId)
            .WithContactId(clientModelA.Id)
            .RuleFor(x => x.CreditsUsed, 100)
            .RuleFor(x => x.TaxExclusivePrice, 500)
            .Generate()
            .ToDataModel();

        var invoiceBillableItems = invoiceA.InvoiceLineItems.Select(x => new BillableItemFaker(ProviderId, clientModelA.Id)
            .RuleFor(y => y.BillableId, invoiceA.Id)
            .RuleFor(y => y.Amount, x.Price)
            .Generate()
            .ToDataModel()
        ).ToArray();

        var invoiceBillable = new BillableDataModelFaker(ProviderId, clientModelA.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.Type, BillableType.Invoice)
            .RuleFor(x => x.BillableItems, invoiceBillableItems)
            .RuleFor(x => x.InvoiceId, invoiceA.Id)
            .Generate();

        var paidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(clientModelA.Id)
            .RuleFor(x => x.Status, InvoiceStatus.Paid)
            .RuleFor(x => x.TaxExclusivePrice, 100)
            .Generate()
            .ToDataModel();

        var paymentIntent = new PaymentIntentFaker(ProviderId)
            .WithInvoice(paidInvoice)
            .Generate();

        var payment = new PaymentFaker(ProviderId)
            .WithPaymentIntent(paymentIntent)
            .RuleFor(x => x.PaymentDate, DateTime.UtcNow)
            .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
            .RuleFor(x => x.PaymentProvider, PaymentProviders.Stripe)
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.NZD)
            .Generate()
            .ToDataModel();

        // allocate payment to a diff invoice ~ first line item of invoice a
        var paymentAllocation = new PaymentAllocationFaker(ProviderId, clientModelA.Id)
            .RuleFor(x => x.BillableItemId, invoiceBillableItems.First().Id)
            .RuleFor(x => x.InvoiceLineItemId, invoiceA.InvoiceLineItems.First().Id)
            .RuleFor(x => x.Amount, invoiceA.InvoiceLineItems.First().Price)
            .RuleFor(x => x.PaymentId, payment.Id)
            .Generate()
            .ToDataModel();

        paidInvoice.DeletedAtUtc = DateTime.UtcNow;

        DataContext.AddRange(clientModelA, invoiceA, invoiceBillable, paidInvoice, paymentIntent.ToDataModel(), payment, paymentAllocation);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices/{invoiceA.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);
        var invoice = await result.Content.ReadAsAsync<Invoice>();
        invoice.AmountDue.Should().Be(invoice.Total - invoice.CreditsUsed - paymentAllocation.Amount);

        invoice.Should().BeEquivalentTo(new
        {
           Id = invoice.Id,
           ProviderId = invoice.ProviderId,
           ContactId = invoice.ContactId,
           BillToId = invoice.BillToId,
           TaskIds = invoice.TaskIds,
           Number = invoice.Number,
           ServiceDate = invoice.ServiceDate,
           IssueDate = invoice.IssueDate,
           Status = invoice.Status,
           DueDate = invoice.DueDate,
           History = invoice.History,
           TaxPrice = invoice.TaxPrice,
           TaxExclusivePrice = invoice.TaxExclusivePrice,
           Title = invoice.Title,
           Description = invoice.Description,
           PaymentDate = invoice.PaymentDate,
           CurrencyCode = invoice.CurrencyCode,
           TaxName = invoice.TaxName,
           TaxNumber = invoice.TaxNumber,
           POSONumber = invoice.POSONumber,
           Theme = invoice.Theme,
           ServiceReceiptIds = invoice.ServiceReceiptIds,
           VoidedDate = invoice.VoidedDate,
           CreditsUsed = invoice.CreditsUsed,
           LineItems = invoice.LineItems.Select(x => new InvoiceLineItem
           {
               Id = x.Id,
               InvoiceId = x.InvoiceId,
               OrderIndex = x.OrderIndex,
               BillableItemId = x.BillableItemId,
               CurrencyCode = x.CurrencyCode,
               Description = x.Description,
               Detail = x.Detail,
               Code = x.Code,
               POSCode = x.POSCode,
               Price = x.Price,
               Date = x.Date,
               Units = x.Units,
               TaxRates = x.TaxRates.Select( x => new ItemTaxRate
               {
                   TaxRateId = x.Id,
                   Name = x.Name,
                   Rate = x.Rate
               }).ToArray(),
               DiscountRate = x.DiscountRate,

           }).ToArray(),
           ContactDetail = invoice.ContactDetail,
           BillToDetail = invoice.BillToDetail,
           ProviderDetail = invoice.ProviderDetail,
           StaffDetail = invoice.StaffDetail,
           AmountPaid = invoice.AmountPaid,
           TaxGroups = invoice.TaxGroups,
           StaffIds = invoice.StaffIds,
        });
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public async Task Delete_Should_Soft_Delete_and_Trash(bool withDeletedContact)
    {
        // Arrange
        var clientModel = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var invoice = new InvoiceFaker()
            .RuleFor(_ => _.ProviderId, ProviderId)
            .RuleFor(_ => _.ContactId, clientModel.Id)
            .RuleFor(_ => _.Status, InvoiceStatus.Paid)
            .RuleFor(_ => _.DueDate, (faker, _) => _.IssueDate.AddDays(7))
            .Generate();

        var paymentIntent = new PaymentIntentFaker(ProviderId)
            .WithInvoice(invoice)
            .Generate();

        var payment = new PaymentFaker(ProviderId)
            .WithPaymentIntent(paymentIntent)
            .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
            .RuleFor(x => x.PaymentProvider, PaymentProviders.Stripe)
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.NZD)
            .Generate();

        if (!withDeletedContact)
        {
            DataContext.Add(clientModel.ToDataModel());
        }

        await DataContext.AddRangeAsync(
            invoice.ToDataModel(),
            paymentIntent.ToDataModel(),
            payment.ToDataModel()
        );
        await DataContext.SaveChangesAsync();

        // Act
        var request = new HttpRequestMessageBuilder(HttpMethod.Delete, $"api/providers/{ProviderId}/invoices/{invoice.Id}")
            .WithFeatureFlags(FeatureFlags.TrashAndRestore)
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);

        // Asssert
        result.IsSuccessStatusCode.Should().BeTrue();
        result.StatusCode.Should().Be(HttpStatusCode.OK);
        var id = await result.Content.ReadAsAsync<Guid>();
        id.Should().Be(invoice.Id);

        var dbInvoice = await DataContext.Invoices
            .IgnoreQueryFilters()
            .AsNoTracking()
            .FirstAsync(x => x.Id == invoice.Id);

        dbInvoice.Should().NotBeNull();
        dbInvoice.DeletedAtUtc.Should().NotBeNull();

        var trashItem = await DataContext.TrashItems
            .AsNoTracking()
            .Include(x => x.TrashItemMetadataDatas)
            .FirstAsync(x => x.Type == TrashType.Invoice && x.EntityId == invoice.Id);

        trashItem.Should().NotBeNull();
        trashItem.DeletedByPersonId.Should().Be(PersonId);
        trashItem.Type.Should().Be(TrashType.Invoice);
        trashItem.FromContactId.Should().Be(withDeletedContact ? null : clientModel.Id);

        //assert metadata
        trashItem.TrashItemMetadataDatas.Should().HaveCount(1);
        trashItem.TrashItemMetadataDatas.First().Metadata.Should()
            .BeEquivalentTo(new Dictionary<string, object> { { "status", "Paid" } });
    }

    [Fact]
    public async Task DeleteInvoice_ShouldRemoved_PaymentAllocation()
    {
        var contact = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate();

        var providerItems = new ProviderItemFaker()
            .RuleFor(x => x.Price, f => f.PickRandom(100, 200, 300))
            .Generate(3)
            .ToArray();

        var task = new TaskFaker()
            .WithContacts([contact], TaskContactStatus.Attended)
            .RuleFor(x => x.Type, TaskType.ClientEvent)
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.ItemsSnapshot, providerItems)
            .Generate();

        var localizer = ResolveService<IStringLocalizer<SharedMessages>>();
        var billable = task.AsBillables(localizer, []).ToDataModel().Single();

        var invoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .RuleFor(x => x.Status, InvoiceStatus.Paid)
            .Generate();

        var lineitemFaker = new InvoiceLineItemFaker(invoice);
        var lineItems = billable.BillableItems.Select(i =>
            lineitemFaker
                .ForBillableItem(i)
                .Generate()
        );
        invoice.LineItems = lineItems.ToArray();
        invoice.CalculateTotals();

        var paymentIntent = new PaymentIntentFaker(ProviderId)
            .WithInvoice(invoice)
            .Generate();

        var payment = new PaymentFaker(ProviderId)
            .WithPaymentIntent(paymentIntent)
            .Generate();

        var paymentDataModel = payment.ToDataModel();
        paymentDataModel.Allocations = invoice.LineItems
            .Select(i => new PaymentAllocationDataModel
            {
                Id = Guid.NewGuid(),
                Amount = i.Amount + i.TaxAmount,
                BillableItemId = i.BillableItemId.Value,
                InvoiceLineItemId = i.Id,
                ProviderId = ProviderId,
                ContactId = contact.Id
            }).ToArray();

        DataContext.AddRange(contact.ToDataModel(), paymentDataModel, paymentIntent.ToDataModel(), invoice.ToDataModel(), billable, task.ToDataModel());
        await DataContext.SaveChangesAsync();
        Fixture.ClearEvents();

        // Act
        var request = new HttpRequestMessageBuilder(HttpMethod.Delete, $"api/providers/{ProviderId}/invoices/{invoice.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);
        var events = Fixture.GetPublishedEventsOfType<InvoiceTrashedEvent>();

        // Asssert
        result.StatusCode.Should().Be(HttpStatusCode.OK);
        var invoiceLineItemIds = invoice.LineItems.Select(x => x.Id).ToList();
        var allocationDb = await DataContext.PaymentAllocations
            .AsNoTracking()
            .Where(x => invoiceLineItemIds.Contains(x.InvoiceLineItemId.Value))
            .ToListAsync();
        allocationDb.Should().BeEmpty();
    }

    [Fact]
    public async Task UpdateInvoice_ShouldUpdateInvoiceLineItemsSuccessfully()
    {
        var contact = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate();

        var providerItems = new ProviderItemFaker()
            .RuleFor(x => x.Price, f => f.PickRandom(100, 200, 300))
            .Generate(3)
            .ToArray();

        var task = new TaskFaker()
            .WithContacts([contact], TaskContactStatus.Attended)
            .RuleFor(x => x.Type, TaskType.ClientEvent)
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.ItemsSnapshot, providerItems)
            .Generate();

        var localizer = ResolveService<IStringLocalizer<SharedMessages>>();
        var billable = task.AsBillables(localizer, []).ToDataModel().Single();

        var existingInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(contact.Id)
            .RuleFor(x => x.DueDate, DateTime.UtcNow.AddDays(5).ToDateOnly())
            .RuleFor(x => x.LineItems, [])
            .Generate();

        var invoiceStaff = new InvoiceStaffDataModel(existingInvoice.Id, PersonId);
        var currencyHandler = CurrencyHandler.Get(existingInvoice.CurrencyCode);

        var payment = new PaymentFaker(ProviderId)
            .RuleFor(x => x.InvoiceId, (Guid?)null)
            .Generate();

        var billableItem1 = billable.BillableItems.ToList()[0];
        var billableItem2 = billable.BillableItems.ToList()[1];
        var billableItem3 = billable.BillableItems.ToList()[2];

        var existingLineItem = new InvoiceLineItemFaker(existingInvoice, [])
            .RuleFor(x => x.Price, currencyHandler.Round(100))
            .RuleFor(x => x.Units, 1)
            .RuleFor(x => x.BillableItemId, billableItem1.Id)
            .RuleFor(x => x.TaxRates,
            [
                new() { Rate = 10, Name = "Tax1" }
            ])
            .Generate();
        DataContext.Add(existingLineItem.ToDataModel(existingInvoice));
        var allocation = new PaymentAllocationFaker(ProviderId, contact.Id)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.InvoiceLineItemId, existingLineItem.Id)
            .RuleFor(x => x.PaymentId, payment.Id)
            .RuleFor(x => x.BillableItemId, billableItem1.Id)
            .Generate()
            .ToDataModel();
        DataContext.Add(allocation);
        var existingLineItemRequest = InvoiceLineItemRequestBuilder
            .FromInvoiceLineItem(existingLineItem.ToDataModel(existingInvoice))
            .Create();
        existingLineItemRequest.TaxRates =
        [
            new() { Rate = 10, Name = "Tax1" },
            new() { Rate = 20, Name = "Tax2" }
        ];

        var toBeRemovedLineItem = new InvoiceLineItemFaker(existingInvoice, [])
            .RuleFor(x => x.Price, currencyHandler.Round(200))
            .RuleFor(x => x.BillableItemId, billableItem2.Id)
            .RuleFor(x => x.Units, 1)
            .Generate();
        DataContext.Add(toBeRemovedLineItem.ToDataModel(existingInvoice));
        var toBeRemovedAllocation = new PaymentAllocationFaker(ProviderId, contact.Id)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.InvoiceLineItemId, toBeRemovedLineItem.Id)
            .RuleFor(x => x.BillableItemId, billableItem2.Id)
            .RuleFor(x => x.PaymentId, payment.Id)
            .Generate()
            .ToDataModel();
        DataContext.Add(toBeRemovedAllocation);

        var toBeAddedLineItem = new InvoiceLineItemFaker(existingInvoice, [])
            .RuleFor(x => x.Price, currencyHandler.Round(300))
            .RuleFor(x => x.Units, 1)
            .RuleFor(x => x.BillableItemId, billableItem3.Id)
            .Generate();
        var toBeAddedLineItemRequest = InvoiceLineItemRequestBuilder
            .FromInvoiceLineItem(toBeAddedLineItem.ToDataModel(existingInvoice))
            .Create();

        DataContext.AddRange([
            task.ToDataModel(),
            billable,
            payment.ToDataModel(),
            contact.ToDataModel(),
            existingInvoice.ToDataModel(),
            invoiceStaff
        ]);
        await DataContext.SaveChangesAsync();

        var editInvoiceRequest = EditInvoiceRequestBuilder
            .FromInvoiceDataModel(existingInvoice.ToDataModel())
            .WithLineItems([existingLineItemRequest, toBeAddedLineItemRequest])
            .Create();

        var request = new HttpRequestMessageBuilder(HttpMethod.Put, $"api/providers/{ProviderId}/invoices/{existingInvoice.Id}")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(editInvoiceRequest)
            .Create();

        var result = await ClientApi.SendAsync(request);
        result.StatusCode.Should().Be(HttpStatusCode.OK);

        var clientResult = await result.Content.ReadAsAsync<Invoice>();
        clientResult.LineItems.Should().HaveCount(2);

        var dbInvoice = await DataContext.Invoices.AsNoTracking()
            .Include(i => i.InvoiceLineItems)
            .ThenInclude(x => x.TaxRates)
            .Include(i => i.InvoiceLineItems)
            .ThenInclude(x => x.Allocations)
            .FirstAsync(x => x.Id == existingInvoice.Id);
        var dbExistingLineItem = dbInvoice.InvoiceLineItems.First(x => x.Id == existingLineItem.Id);
        dbExistingLineItem.Allocations.Should().HaveCount(1);
        dbExistingLineItem.TaxRates.Should().HaveCount(2);

        dbInvoice.InvoiceLineItems.Should().HaveCount(2);
        dbInvoice.InvoiceLineItems.Should().Contain(x => x.Id == existingLineItem.Id);
        dbInvoice.InvoiceLineItems.Should().Contain(x => x.Price == toBeAddedLineItem.Price);

        // Removed line item should have its allocation removed as well
        var dbPaymentAllocation = await DataContext.PaymentAllocations.AsNoTracking()
            .Where(x => x.InvoiceLineItemId == toBeRemovedAllocation.Id)
            .ToListAsync();
        dbPaymentAllocation.Should().BeNullOrEmpty();
    }

    [Fact]
    public async Task ListInvoice_Verify_AllProperties()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var invoiceA = new InvoiceFaker(ProviderId)
            .WithContactId(clientModelA.Id)
            .WithBillToId(clientModelA.Id)
            .RuleFor(x => x.CreditsUsed, 100)
            .RuleFor(x => x.TaxExclusivePrice, 500)
            .Generate()
            .ToDataModel()
            .WithProviderStaff(PersonId);

        var invoiceBillableItems = invoiceA.InvoiceLineItems.Select(x => new BillableItemFaker(ProviderId, clientModelA.Id)
            .RuleFor(y => y.BillableId, invoiceA.Id)
            .RuleFor(y => y.Amount, x.Price)
            .Generate()
            .ToDataModel()
        ).ToArray();

        var invoiceBillable = new BillableDataModelFaker(ProviderId, clientModelA.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.Type, BillableType.Invoice)
            .RuleFor(x => x.BillableItems, invoiceBillableItems)
            .RuleFor(x => x.InvoiceId, invoiceA.Id)
            .Generate();


        var paymentIntent = new PaymentIntentFaker(ProviderId)
            .WithInvoice(invoiceA)
            .Generate();

        var payment = new PaymentFaker(ProviderId)
            .WithPaymentIntent(paymentIntent)
            .RuleFor(x => x.PaymentDate, DateTime.UtcNow)
            .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
            .RuleFor(x => x.PaymentProvider, PaymentProviders.Stripe)
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.NZD)
            .Generate()
            .ToDataModel();

        // allocate payment to a diff invoice ~ first line item of invoice a
        var paymentAllocation = new PaymentAllocationFaker(ProviderId, clientModelA.Id)
            .RuleFor(x => x.BillableItemId, invoiceBillableItems.First().Id)
            .RuleFor(x => x.InvoiceLineItemId, invoiceA.InvoiceLineItems.First().Id)
            .RuleFor(x => x.Amount, invoiceA.InvoiceLineItems.First().Price)
            .RuleFor(x => x.PaymentId, payment.Id)
            .Generate()
            .ToDataModel();


        DataContext.AddRange(clientModelA, invoiceA, invoiceBillable, paymentIntent.ToDataModel(), payment, paymentAllocation);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);
        var invoiceTemplateSettingsRepository = ResolveService<IInvoiceTemplateSettingsRepository>();
        var defaultTemplate = await invoiceTemplateSettingsRepository.GetDefault(ProviderId);

        result.StatusCode.Should().Be(HttpStatusCode.OK);
        var response = await result.Content.ReadAsAsync<PaginatedResult<InvoiceListEntry>>();
        var invoice = response.Items.First();
        invoice.Should().BeEquivalentTo(new
        {
            Id = invoiceA.Id,
            ProviderId = invoiceA.ProviderId,
            ContactId = invoiceA.ContactId,
            BillToId = invoiceA.BillToId,
            TaskIds = Array.Empty<Guid>(),
            Number = invoiceA.Number,
            ServiceDate = invoiceA.ServiceDate,
            IssueDate = invoiceA.IssueDate,
            Status = invoiceA.Status,
            DueDate = invoiceA.DueDate,
            History = invoiceA.History,
            TaxPrice = invoiceA.TaxPrice,
            TaxExclusivePrice = invoiceA.TaxExclusivePrice,
            Title = invoiceA.Title,
            Description = invoiceA.Description,
            PaymentDate = invoiceA.PaymentDate,
            CurrencyCode = invoiceA.CurrencyCode,
            TaxName = invoiceA.TaxName,
            TaxNumber = invoiceA.TaxNumber,
            POSONumber = invoiceA.POSONumber,
            Theme = new InvoiceTheme
            {
                Layout = defaultTemplate?.TemplateType ?? InvoiceLayout.Simple,
                LogoId = defaultTemplate?.Logo?.Id ?? (Guid.TryParse(Data.CurrentProvider.LogoId, out var logoId) ? logoId : (Guid?)null),
                ColorHex = defaultTemplate?.ColorHex ?? Data.CurrentProvider?.PrimaryColorHex,
                ShowCodes = defaultTemplate?.ShowCodes ?? true,
                ShowUnits = defaultTemplate?.ShowUnits ?? true,
                ShowLineItemTax = defaultTemplate?.ShowLineItemTax ?? true
            },
            ServiceReceiptIds = Array.Empty<Guid>(),
            VoidedDate = invoiceA.VoidedDate,
            CreditsUsed = invoiceA.CreditsUsed,
            LineItems = invoiceA.InvoiceLineItems.Select(x => new InvoiceLineItem
            {
                Id = x.Id,
                InvoiceId = x.InvoiceId,
                OrderIndex = x.OrderIndex,
                BillableItemId = x.BillableItemId,
                Price = x.Price,
                Date = x.Date,
                Units = x.Units,
                TaxRates = x.TaxRates.DefaultIfEmpty()
                    .ToArray(),
                DiscountRate = x.DiscountRate,
                Description = x.Description,
                Detail = x.Detail,
                Code = x.Code,
                POSCode = x.POSCode,
                CurrencyCode = x.CurrencyCode,
            }).ToArray(),
            ContactDetail = invoiceA.ContactDetail,
            BillToDetail = invoiceA.BillToDetail,
            ProviderDetail = invoiceA.ProviderDetail,
            StaffDetail = invoiceA.StaffDetail,
            AmountPaid = paymentAllocation.Amount,
            Contact = new InvoiceListEntryContact
            {
                Id = clientModelA.Id,
                Name = clientModelA.FullName,
                FirstName = clientModelA.FirstName,
                LastName = clientModelA.LastName,
                PreferredName = clientModelA.PreferredName,
                MiddleNames = clientModelA.MiddleNames,
                BusinessName = clientModelA.BusinessName,
                Email = clientModelA.Email,
                PhoneNumber = clientModelA.PhoneNumber,
                PersonId = clientModelA.PersonId,
                IsClient = clientModelA.IsClient,
            },
            BillTo = new InvoiceListEntryContact
            {
                Id = clientModelA.Id,
                Name = clientModelA.FullName,
                FirstName = clientModelA.FirstName,
                LastName = clientModelA.LastName,
                PreferredName = clientModelA.PreferredName,
                MiddleNames = clientModelA.MiddleNames,
                BusinessName = clientModelA.BusinessName,
                Email = clientModelA.Email,
                PhoneNumber = clientModelA.PhoneNumber,
                PersonId = clientModelA.PersonId,
                IsClient = clientModelA.IsClient,
            },
            Staff = new InvoiceListEntryStaff[]
            {
                new()
                {
                    Id = PersonId,
                    FirstName = Data.CurrentUser.FirstName,
                    LastName = Data.CurrentUser.LastName,
                    PhoneNumber = Data.CurrentUser.PhoneNumber,
                    Email = Data.CurrentUser.Email,
                }
            },
            Payments = new[]
            {
                new InvoiceListEntryPayment
                {
                    Id = payment.Id,
                    Amount = payment.Amount,
                    Fee = payment.Fee,
                    IsClientChargedFee = payment.IsClientChargedFee,
                    PayoutDateUtc = payment.PayoutDateUtc,
                    PaymentDate = payment.PaymentDate,
                    Type = payment.Type,
                    PayoutStatus = payment.PayoutStatus,
                    PayoutAmount = payment.PayoutAmount,
                    PayoutCurrencyCode = payment.PayoutCurrencyCode,
                    PaymentProvider = payment.PaymentProvider,
                    Refunds = [],

                }
            },
        }, opt => opt.ExcludingMissingMembers().UsingSimpleDateTimePrecision());
    }
    
       [Fact]
    public async Task GetInvoice_Verify_AllProperties()
    {
        var clientModelA = new ContactFaker(ProviderId)
            .WithIsClient(true)
            .Generate()
            .ToDataModel();

        var invoiceA = new InvoiceFaker(ProviderId)
            .WithContactId(clientModelA.Id)
            .RuleFor(x => x.CreditsUsed, 100)
            .RuleFor(x => x.TaxExclusivePrice, 500)
            .Generate()
            .ToDataModel();

        var invoiceBillableItems = invoiceA.InvoiceLineItems.Select(x => new BillableItemFaker(ProviderId, clientModelA.Id)
            .RuleFor(y => y.BillableId, invoiceA.Id)
            .RuleFor(y => y.Amount, x.Price)
            .Generate()
            .ToDataModel()
        ).ToArray();

        var invoiceBillable = new BillableDataModelFaker(ProviderId, clientModelA.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.Type, BillableType.Invoice)
            .RuleFor(x => x.BillableItems, invoiceBillableItems)
            .RuleFor(x => x.InvoiceId, invoiceA.Id)
            .Generate();

        var paidInvoice = new InvoiceFaker(ProviderId)
            .WithContactId(clientModelA.Id)
            .RuleFor(x => x.Status, InvoiceStatus.Paid)
            .RuleFor(x => x.TaxExclusivePrice, 100)
            .Generate()
            .ToDataModel();

        var paymentIntent = new PaymentIntentFaker(ProviderId)
            .WithInvoice(paidInvoice)
            .Generate();

        var payment = new PaymentFaker(ProviderId)
            .WithPaymentIntent(paymentIntent)
            .RuleFor(x => x.PaymentDate, DateTime.UtcNow)
            .RuleFor(x => x.PayoutStatus, PayoutStatus.Paid)
            .RuleFor(x => x.PaymentProvider, PaymentProviders.Stripe)
            .RuleFor(x => x.CurrencyCode, CurrencyCodes.NZD)
            .Generate()
            .ToDataModel();

        // allocate payment to a diff invoice ~ first line item of invoice a
        var paymentAllocation = new PaymentAllocationFaker(ProviderId, clientModelA.Id)
            .RuleFor(x => x.BillableItemId, invoiceBillableItems.First().Id)
            .RuleFor(x => x.InvoiceLineItemId, invoiceA.InvoiceLineItems.First().Id)
            .RuleFor(x => x.Amount, invoiceA.InvoiceLineItems.First().Price)
            .RuleFor(x => x.PaymentId, payment.Id)
            .Generate()
            .ToDataModel();

        paidInvoice.DeletedAtUtc = DateTime.UtcNow;

        DataContext.AddRange(clientModelA, invoiceA, invoiceBillable, paidInvoice, paymentIntent.ToDataModel(), payment, paymentAllocation);
        await DataContext.SaveChangesAsync();

        var request = new HttpRequestMessageBuilder(HttpMethod.Get, $"api/providers/{ProviderId}/invoices/{invoiceA.Id}")
            .WithBearerAuthorization(IdentityToken)
            .Create();

        var result = await ClientApi.SendAsync(request);

        result.StatusCode.Should().Be(HttpStatusCode.OK);
        var invoice = await result.Content.ReadAsAsync<Invoice>();
        invoice.Should().BeEquivalentTo(new
        {
            Id = invoice.Id,
            ProviderId = invoice.ProviderId,
            ContactId = invoice.ContactId,
            BillToId = invoice.BillToId,
            TaskIds = invoice.TaskIds,
            Number = invoice.Number,
            ServiceDate = invoice.ServiceDate,
            IssueDate = invoice.IssueDate,
            Status = invoice.Status,
            DueDate = invoice.DueDate,
            History = invoice.History,
            TaxPrice = invoice.TaxPrice,
            TaxExclusivePrice = invoice.TaxExclusivePrice,
            Title = invoice.Title,
            Description = invoice.Description,
            PaymentDate = invoice.PaymentDate,
            CurrencyCode = invoice.CurrencyCode,
            TaxName = invoice.TaxName,
            TaxNumber = invoice.TaxNumber,
            POSONumber = invoice.POSONumber,
            Theme = invoice.Theme,
            ServiceReceiptIds = invoice.ServiceReceiptIds,
            VoidedDate = invoice.VoidedDate,
            CreditsUsed = invoice.CreditsUsed,
            LineItems = invoice.LineItems.Select(x => new InvoiceLineItem
                {
                    Id = x.Id,
                    InvoiceId = x.InvoiceId,
                    OrderIndex = x.OrderIndex,
                    BillableItemId = x.BillableItemId,
                    CurrencyCode = x.CurrencyCode,
                    Description = x.Description,
                    Detail = x.Detail,
                    Code = x.Code,
                    POSCode = x.POSCode,
                    Price = x.Price,
                    Date = x.Date,
                    Units = x.Units,
                    TaxRates = x.TaxRates.Select(x => new ItemTaxRate
                        {
                            TaxRateId = x.Id,
                            Name = x.Name,
                            Rate = x.Rate
                        })
                        .ToArray(),
                    DiscountRate = x.DiscountRate,

                })
                .ToArray(),
            ContactDetail = invoice.ContactDetail,
            BillToDetail = invoice.BillToDetail,
            ProviderDetail = invoice.ProviderDetail,
            StaffDetail = invoice.StaffDetail,
            AmountPaid = invoice.AmountPaid,
            TaxGroups = invoice.TaxGroups,
            StaffIds = invoice.StaffIds,
        });
    }
}