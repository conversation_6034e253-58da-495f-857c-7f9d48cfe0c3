﻿using carepatron.api;
using carepatron.api.Infrastructure.Identity.ApiKey;
using carepatron.api.Infrastructure.Identity.Token;
using carepatron.api.Infrastructure.Identity.WebHooks;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Services;
using carepatron.core.Events;
using carepatron.core.Events.Handlers;
using carepatron.core.Services;
using carepatron.infra.cognito.Configuration;
using carepatron.infra.importer.pms.InversionOfControl;
using Messaging.Aws.Sqs;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using tests.component.Authentication;

namespace tests.component.Setup
{
    public class TestStartup : Startup
    {
        private readonly IConfiguration configuration;

        public TestStartup(IConfiguration configuration, IWebHostEnvironment hostingEnvironment) : base(configuration, hostingEnvironment)
        {
            this.configuration = configuration;
        }

        public override IServiceCollection AddAuthentication(IServiceCollection services, CognitoConfig config)
        {
            services.AddAuthentication(options =>
                {
                    options.DefaultAuthenticateScheme = "Bearer";
                    options.DefaultChallengeScheme = "Bearer";
                })
                .AddTestAuth(o => { })
                .AddCarepatronTokenAuthentication(o => { })
                .AddApiKeyAuthentication(o => { })
                .AddGooglePubSubTokenAuthentication(o => { });

            return services;
        }

        public override void ConfigureServices(IServiceCollection services)
        {
            base.ConfigureServices(services);
            services.RegisterImporterPmsInfra(configuration);
            services.UseAwsQueue([typeof(ClaimMD.Module.Queues.ClaimMdMessageQueue).Assembly], Configuration, true);

            services
                .AddScoped<IEventHandlerDelegator, EventHandlerDelegator>()
                .AddScoped<IImportContactsService, ImportContactsService>()
                .AddScoped<IImportContactMappingService, ImportContactMappingService>()
                .Scan(scan =>
                    scan.FromAssemblyOf<IEventHandlerDelegator>()
                        .AddClasses(x => x.AssignableTo(typeof(IEventHandler<>)))
                        .AsSelf()
                        .AsImplementedInterfaces()
                        .WithScopedLifetime());
        }
    }
}