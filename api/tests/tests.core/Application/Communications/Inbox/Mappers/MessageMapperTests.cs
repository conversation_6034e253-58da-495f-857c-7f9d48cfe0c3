﻿using Bogus;
using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Application.Communications.Inbox.Processor.Implementations;
using carepatron.core.Constants;
using carepatron.core.Extensions;
using carepatron.infra.google.Extensions;
using carepatron.infra.google.Mappers;
using Google.Apis.Gmail.v1.Data;
using Google.Apis.Oauth2.v2.Data;
using tests.common.Data.Datasets;

namespace tests.core.Application.Communications.Inbox.Mappers
{
    [Trait(nameof(CodeOwner), CodeOwner.Communications)]
    public class MessageMapperTests
    {
        private readonly List<SimplePerson> persons = new();
        private ListSendAsResponse sendAsInfo = new();
        private Userinfo userInfo = new();

        private readonly string messageRfcId = new Faker().Random.Hash(32);

        private readonly MessageMapper mapper;

        public MessageMapperTests()
        {
            persons = new SimplePersonFaker()
                .Generate(2);

            sendAsInfo = new GoogleListSendAsResponseFaker()
                .WithSendAs(persons[0])
                .Generate();

            userInfo = new GoogleUserInfoFaker()
                .WithPerson(persons[1])
                .Generate();

            mapper = new(new IncomingMessageBodyProcessor(), new OutgoingMessageBodyProcessor());
        }

        [Theory]
        [InlineData("2023-11-17T02:59:00.000Z", "2023-11-17T02:59:00.000Z")]
        [InlineData("2023-11-16T02:59:00.000Z", "2023-11-16T02:59:00.000Z")]
        public void MessageMapper_Can_Map_Download_Timestamp(string toValue, string expectedDownloadTimestamp)
        {
            var message = new GmailMessageFaker()
                .Generate();

            var to = DateTime.Parse(toValue).ToUniversalTime();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, to);

            var expected = DateTime.Parse(expectedDownloadTimestamp).ToUniversalTime();

            inboxMessage.DownloadTimestamp.Should().Be(expected);
        }

        [Theory]
        [InlineData("2023-11-17T02:59:00.000Z", "2023-11-17T02:59:00.000Z")]
        [InlineData("2023-11-16T02:59:00.000Z", "2023-11-16T02:59:00.000Z")]
        public void MessageMapper_Can_Map_Status_and_StatusChangedAt_Timestamp(string toValue, string expectedDownloadTimestamp)
        {
            var message = new GmailMessageFaker()
                .Generate();

            var to = DateTime.Parse(toValue).ToUniversalTime();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, to);

            var expected = DateTime.Parse(expectedDownloadTimestamp).ToUniversalTime();

            inboxMessage.Status.Should().Be(MessageStatus.Default);
            inboxMessage.StatusChangedAt.Should().Be(expected);
        }

        [Fact]
        public void MessageMapper_Can_Map_To_With_SendAs_As_Default()
        {
            var message = new GmailMessageFaker()
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.To.Should().BeEquivalentTo(new RecipientDetail(sendAsInfo.SendAs[0].DisplayName, sendAsInfo.SendAs[0].SendAsEmail));
        }

        [Fact]
        public void MessageMapper_Can_Map_To_With_Empty_SendAs()
        {
            var message = new GmailMessageFaker()
                .Generate();

            sendAsInfo = new Mock<ListSendAsResponse>()
                .SetupProperty(x => x.SendAs, new List<SendAs>())
                .Object;

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.To.Should().BeEquivalentTo(new RecipientDetail(userInfo.Name, userInfo.Email));
        }

        [Fact]
        public void MessageMapper_Can_Map_To_With_Empty_UserInfo()
        {
            var message = new GmailMessageFaker()
                .Generate();

            userInfo = new GoogleUserInfoFaker()
                .WithPerson(persons[1])
                .WithEmptyName()
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.To.Should().BeEquivalentTo(new RecipientDetail(sendAsInfo.SendAs[0].DisplayName, sendAsInfo.SendAs[0].SendAsEmail));
        }

        [Fact]
        public void MessageMapper_Can_Map_To_With_Email_Only()
        {
            var message = new GmailMessageFaker()
                .Generate();

            sendAsInfo = new GoogleListSendAsResponseFaker()
                .WithSendAs(persons[0], true)
                .Generate();

            userInfo = new GoogleUserInfoFaker()
                .WithPerson(persons[1])
                .WithEmptyName()
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.To.Should().BeEquivalentTo(new RecipientDetail(sendAsInfo.SendAs[0].SendAsEmail));
        }

        [Theory]
        [InlineData(1700189940000, "2023-11-17T02:59:00.000Z")]
        [InlineData(null, "2023-11-17T03:00:00.000Z")]
        public void MessageMapper_Can_Map_Date_CreatedAt_InternalDate(long? internalDate, string expectedCreatedAt)
        {
            var date = DateTime.Parse(expectedCreatedAt).ToUniversalTime();

            var message = new GmailMessageFaker()
                .WithInternalDate(internalDate)
                .WithPayload(new GmailMessagePayloadFaker()
                    .WithDateMessagePartHeader(date)
                    .Generate())
                .Generate();

            var expected = DateTime.Parse(expectedCreatedAt).ToUniversalTime();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);
            inboxMessage.CreatedAt.Should().Be(expected);
        }

        [Theory]
        [InlineData(1700189940000, "2023-11-17T03:00:00.000Z", "2023-11-17T02:59:00.000Z")]
        [InlineData(null, "2023-11-17T03:00:00.000Z", "2023-11-17T03:00:00.000Z")]
        public void MessageMapper_Can_Map_CreatedAt_Date_Fallback(long? internalDate, string dateHeaderValue, string expectedCreatedAt)
        {
            var date = DateTime.Parse(dateHeaderValue).ToUniversalTime();

            var message = new GmailMessageFaker()
                .WithInternalDate(internalDate)
                .WithPayload(new GmailMessagePayloadFaker()
                    .WithDateMessagePartHeader(date)
                    .Generate())
                .Generate();

            var expected = DateTime.Parse(expectedCreatedAt).ToUniversalTime();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);
            inboxMessage.CreatedAt.Should().Be(expected);
        }

        [Fact]
        public void MessageMapper_Can_Map_Message_Fields()
        {
            var message = new GmailMessageFaker()
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);
            inboxMessage.ExternalSourceMessageId.Should().Be(message.Id);
            inboxMessage.ExternalConversationId.Should().Be(message.ThreadId);
            inboxMessage.MessagePreview.Should().Be(message.Snippet);
        }

        [Fact]
        public void MessageMapper_Can_Map_Identity_Fields()
        {
            var inboxId = Guid.NewGuid();

            var message = new GmailMessageFaker()
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), inboxId, message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.ExternalSource.Should().Be(ExternalSource.Gmail);
            inboxMessage.InboxId.Should().Be(inboxId);
            inboxMessage.ConversationId.Should().BeNull();
            inboxMessage.IsRead.Should().BeFalse();
        }

        [Theory]
        [InlineData("<<EMAIL>>", "<EMAIL>", "<EMAIL>")]
        [InlineData("<EMAIL>", "<EMAIL>", "<EMAIL>")]
        [InlineData("test <<EMAIL>>", "test", "<EMAIL>")]
        [InlineData("\"test\" <<EMAIL>>", "test", "<EMAIL>")]
        [InlineData("\"github/Hello\" <<EMAIL>>", "github/Hello", "<EMAIL>")]
        [InlineData("\"Dr. Chasity J. Carswell, DNP, MSN, APRN, FNP-C, PMHNP-BC\" <<EMAIL>>", "Dr. Chasity J. Carswell, DNP, MSN, APRN, FNP-C, PMHNP-BC", "<EMAIL>")]
        public void MessageMapper_Can_Map_From(string from, string expectedDisplayName, string expectedAccountId)
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithFromMessagePartHeader(from).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.From.AccountId.Should().Be(expectedAccountId);
            inboxMessage.From.DisplayName.Should().Be(expectedDisplayName);
        }

        [Theory]
        [InlineData("<<EMAIL>>", 1)]
        [InlineData("<EMAIL>", 1)]
        [InlineData("test <<EMAIL>>", 1)]
        [InlineData("\"test\" <<EMAIL>>", 1)]
        [InlineData("\"github/Hello\" <<EMAIL>>", 1)]
        [InlineData("\"github/Hello\" <<EMAIL>>, abc <<EMAIL>>", 2)]
        [InlineData("<EMAIL>, <EMAIL>, <EMAIL>", 3)]
        [InlineData("test <<EMAIL>>, xyz <<EMAIL>>, <EMAIL>", 3)]
        [InlineData("\"test\" <<EMAIL>>, <EMAIL>, <EMAIL>", 3)]
        [InlineData("\"github/Hello\" <<EMAIL>>, \"xyz\" <<EMAIL>>, <EMAIL>", 3)]
        [InlineData("\"Dr. Chasity J. Carswell, DNP, MSN, APRN, FNP-C, PMHNP-BC\" <<EMAIL>>" +
            ", abc <<EMAIL>>" +
            ", <EMAIL>" +
            ", \"test\" <test@carepatron>", 4)]
        public void MessageMapper_Can_Map_Recipients_To(string to, int expectedCount)
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithToMessagePartHeader(to).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.Recipients.To.Length.Should().Be(expectedCount);
        }

        [Theory]
        [InlineData("<<EMAIL>>", 1)]
        [InlineData("<EMAIL>", 1)]
        [InlineData("test <<EMAIL>>", 1)]
        [InlineData("\"test\" <<EMAIL>>", 1)]
        [InlineData("\"github/Hello\" <<EMAIL>>", 1)]
        [InlineData("\"github/Hello\" <<EMAIL>>, abc <<EMAIL>>", 2)]
        [InlineData("<EMAIL>, <EMAIL>, <EMAIL>", 3)]
        [InlineData("test <<EMAIL>>, xyz <<EMAIL>>, <EMAIL>", 3)]
        [InlineData("\"test\" <<EMAIL>>, <EMAIL>, <EMAIL>", 3)]
        [InlineData("\"github/Hello\" <<EMAIL>>, \"xyz\" <<EMAIL>>, <EMAIL>", 3)]
        [InlineData("\"Dr. Chasity J. Carswell, DNP, MSN, APRN, FNP-C, PMHNP-BC\" <<EMAIL>>" +
            ", abc <<EMAIL>>" +
            ", <EMAIL>" +
            ", \"test\" <test@carepatron>", 4)]
        public void MessageMapper_Can_Map_Recipients_Cc(string cc, int expectedCount)
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithCcMessagePartHeader(cc).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.Recipients.Cc.Length.Should().Be(expectedCount);
        }

        [Theory]
        [InlineData("<<EMAIL>>", 1)]
        [InlineData("<EMAIL>", 1)]
        [InlineData("test <<EMAIL>>", 1)]
        [InlineData("\"test\" <<EMAIL>>", 1)]
        [InlineData("\"github/Hello\" <<EMAIL>>", 1)]
        [InlineData("\"github/Hello\" <<EMAIL>>, abc <<EMAIL>>", 2)]
        [InlineData("<EMAIL>, <EMAIL>, <EMAIL>", 3)]
        [InlineData("test <<EMAIL>>, xyz <<EMAIL>>, <EMAIL>", 3)]
        [InlineData("\"test\" <<EMAIL>>, <EMAIL>, <EMAIL>", 3)]
        [InlineData("\"github/Hello\" <<EMAIL>>, \"xyz\" <<EMAIL>>, <EMAIL>", 3)]
        [InlineData("\"Dr. Chasity J. Carswell, DNP, MSN, APRN, FNP-C, PMHNP-BC\" <<EMAIL>>" +
            ", abc <<EMAIL>>" +
            ", <EMAIL>" +
            ", \"test\" <test@carepatron>", 4)]
        public void MessageMapper_Can_Map_Recipients_Bcc(string bcc, int expectedCount)
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithBccMessagePartHeader(bcc).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.Recipients.Bcc.Length.Should().Be(expectedCount);
        }

        [Theory]
        [InlineData("This is a test subject", "This is a test subject")]
        [InlineData("", "")]
        [InlineData(null, "")]
        public void MessageMapper_Can_Map_Subject(string subject, string expectedSubject)
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithSubjectMessagePartHeader(subject).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.MessageSubject.Should().Be(expectedSubject);
        }

        [Fact]
        public void MessageMapper_Can_Map_Subject_Without_Subject_Header()
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithoutSubjectMessagePartHeader().Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.MessageSubject.Should().Be(string.Empty);
        }

        [Theory]
        [InlineData("PGRpdiBkaXI9Imx0ciI+VGVzdCBFbWFpbCAtIEh0bWwgQm9keTwvZGl2Pg==", "<div dir=\"ltr\">Test Email - Html Body</div>", "VGVzdCBFbWFpbCAtIEh0bWwgQm9keQ==", "Test Email - Html Body", MessageType.Html)]
        [InlineData("", "", "", "", MessageType.PlainText)]
        [InlineData(null, "", null, "", MessageType.PlainText)]
        public void MessageMapper_Can_Map_Html_Body(string base64body, string expectedMessageBody, string base64plainbody, string expectedMessageBodyPlain, MessageType expectedMessageType)
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithHtmlBodyMessagePart(base64body, base64plainbody).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);
            inboxMessage.MessageType.Should().Be(expectedMessageType);
            inboxMessage.MessageBody.Should().Be(expectedMessageBody);
            inboxMessage.MessageBodyPlainText.Should().Be(expectedMessageBodyPlain);
        }

        [Theory]
        [InlineData("VGVzdCBFbWFpbCAtIFBsYWluIEJvZHk=", "Test Email - Plain Body", "Test Email - Plain Body", MessageType.PlainText)]
        [InlineData("", "", "", MessageType.PlainText)]
        [InlineData(null, "", "", MessageType.PlainText)]
        public void MessageMapper_Can_Map_Plain_Body(string base64body, string expectedMessageBody, string expectedMessageBodyPlain, MessageType expectedMessageType)
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithPlainBodyMessagePart(base64body).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);
            inboxMessage.MessageType.Should().Be(expectedMessageType);
            inboxMessage.MessageBody.Should().Be(expectedMessageBody);
            inboxMessage.MessageBodyPlainText.Should().Be(expectedMessageBodyPlain);
        }

        [Theory]
        [InlineData("VGVzdCBFbWFpbCAtIFBsYWluIEJvZHk=", "Test Email - Plain Body", "Test Email - Plain Body", MessageType.PlainText)]
        [InlineData("", "", "", MessageType.PlainText)]
        [InlineData(null, "", "", MessageType.PlainText)]
        public void MessageMapper_Can_Map_Single_Level_Plain_Body(string base64body, string expectedMessageBody, string expectedMessageBodyPlain, MessageType expectedMessageType)
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithPlainBodySingleLevelMessagePart(base64body).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);
            inboxMessage.MessageType.Should().Be(expectedMessageType);
            inboxMessage.MessageBody.Should().Be(expectedMessageBody);
            inboxMessage.MessageBodyPlainText.Should().Be(expectedMessageBodyPlain);
        }

        [Fact]
        public void MessageMapper_Can_Map_Valid_Multiple_Recipients()
        {
            var recipients = "\"Dr. Chasity J. Carswell, DNP, MSN, APRN, FNP-C, PMHNP-BC\" <<EMAIL>>" +
                ", abc <<EMAIL>>" +
                ", <EMAIL>" +
                ", \"test\" <test@carepatron>" +
                ", <<EMAIL>>";
            var firstRecipientName = "Dr. Chasity J. Carswell, DNP, MSN, APRN, FNP-C, PMHNP-BC";
            var firstRecipientAccountId = "<EMAIL>";
            var secondRecipientName = "abc";
            var secondRecipientAccountId = "<EMAIL>";
            var thirdRecipientName = "<EMAIL>";
            var thirdRecipientAccountId = "<EMAIL>";
            var fourthRecipientName = "test";
            var fourthRecipientAccountId = "test@carepatron";
            var fifthRecipientName = "<EMAIL>";
            var fifthRecipientAccountId = "<EMAIL>";

            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithToMessagePartHeader(recipients).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.Recipients.To[0].DisplayName.Should().Be(firstRecipientName);
            inboxMessage.Recipients.To[0].AccountId.Should().Be(firstRecipientAccountId);
            inboxMessage.Recipients.To[1].DisplayName.Should().Be(secondRecipientName);
            inboxMessage.Recipients.To[1].AccountId.Should().Be(secondRecipientAccountId);
            inboxMessage.Recipients.To[2].DisplayName.Should().Be(thirdRecipientName);
            inboxMessage.Recipients.To[2].AccountId.Should().Be(thirdRecipientAccountId);
            inboxMessage.Recipients.To[3].DisplayName.Should().Be(fourthRecipientName);
            inboxMessage.Recipients.To[3].AccountId.Should().Be(fourthRecipientAccountId);
            inboxMessage.Recipients.To[4].DisplayName.Should().Be(fifthRecipientName);
            inboxMessage.Recipients.To[4].AccountId.Should().Be(fifthRecipientAccountId);
        }

        [Theory]
        [InlineData("abc", "carepatron.txt", 2213, "plain/text", "txt")]
        [InlineData("def", "carepatron.pdf", 644543, "application/pdf", "pdf")]
        [InlineData("fgh", "care-patron.txt", 331221, "plain/text", "txt")]
        [InlineData("ijk", "care.pat.ron.pdf", 98703, "application/pdf", "pdf")]
        public void MessageMapper_Can_Map_Attachment_Details(string attachmentId, string fileName, int fileSize, string mimeType, string fileExtension)
        {
            var syncTimestamp = DateTime.UtcNow;

            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker()
                    .WithFileAttachmentMessagePart(attachmentId, fileName, fileSize, mimeType)
                    .Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, syncTimestamp);

            inboxMessage.MessageAttachments.Length.Should().Be(1);
            inboxMessage.MessageAttachments[0].Id.Should().NotBeEmpty();
            inboxMessage.MessageAttachments[0].MediaId.Should().NotBeEmpty();
            inboxMessage.MessageAttachments[0].InboxMessageId.Should().Be(inboxMessage.Id);
            inboxMessage.MessageAttachments[0].ExternalFileId.Should().Be(attachmentId);
            inboxMessage.MessageAttachments[0].FileName.Should().Be(fileName);
            inboxMessage.MessageAttachments[0].FileSize.Should().Be(fileSize);
            inboxMessage.MessageAttachments[0].ContentType.Should().Be(mimeType);
            inboxMessage.MessageAttachments[0].FileExtensions.Should().Be(fileExtension);
            inboxMessage.MessageAttachments[0].CreatedAt.Should().Be(syncTimestamp);
        }

        [Theory]
        [InlineData("abc", "carepatron.txt", 2213, "plain/text", "txt")]
        [InlineData("def", "carepatron.pdf", 644543, "application/pdf", "pdf")]
        [InlineData("fgh", "care-patron.txt", 331221, "plain/text", "txt")]
        [InlineData("ijk", "care.pat.ron.pdf", 98703, "application/pdf", "pdf")]
        public void MessageMapper_Can_Map_Attachment_Single_Level_Payload_Details(string attachmentId, string fileName, int fileSize, string mimeType, string fileExtension)
        {
            var syncTimestamp = DateTime.UtcNow;

            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker()
                    .WithFileAttachmentSingleLevelMessagePart(attachmentId, fileName, fileSize, mimeType)
                    .Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, syncTimestamp);

            inboxMessage.MessageAttachments.Length.Should().Be(1);
            inboxMessage.MessageAttachments[0].Id.Should().NotBeEmpty();
            inboxMessage.MessageAttachments[0].MediaId.Should().NotBeEmpty();
            inboxMessage.MessageAttachments[0].InboxMessageId.Should().Be(inboxMessage.Id);
            inboxMessage.MessageAttachments[0].ExternalFileId.Should().Be(attachmentId);
            inboxMessage.MessageAttachments[0].FileName.Should().Be(fileName);
            inboxMessage.MessageAttachments[0].FileSize.Should().Be(fileSize);
            inboxMessage.MessageAttachments[0].ContentType.Should().Be(mimeType);
            inboxMessage.MessageAttachments[0].FileExtensions.Should().Be(fileExtension);
            inboxMessage.MessageAttachments[0].CreatedAt.Should().Be(syncTimestamp);
        }

        [Theory]
        [InlineData("abc", "carepatron.txt", 2213, "plain/text", "txt")]
        [InlineData("def", "carepatron.pdf", 644543, "application/pdf", "pdf")]
        [InlineData("fgh", "care-patron.txt", 331221, "plain/text", "txt")]
        [InlineData("ijk", "care.pat.ron.pdf", 98703, "application/pdf", "pdf")]
        public void MessageMapper_Can_Map_Attachment_Multi_Level_Payload_Details(string attachmentId, string fileName, int fileSize, string mimeType, string fileExtension)
        {
            var syncTimestamp = DateTime.UtcNow;

            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker()
                    .WithFileAttachmentMultiLevelMessagePart(attachmentId, fileName, fileSize, mimeType)
                    .Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, syncTimestamp);

            inboxMessage.MessageAttachments.Length.Should().Be(1);
            inboxMessage.MessageAttachments[0].Id.Should().NotBeEmpty();
            inboxMessage.MessageAttachments[0].MediaId.Should().NotBeEmpty();
            inboxMessage.MessageAttachments[0].InboxMessageId.Should().Be(inboxMessage.Id);
            inboxMessage.MessageAttachments[0].ExternalFileId.Should().Be(attachmentId);
            inboxMessage.MessageAttachments[0].FileName.Should().Be(fileName);
            inboxMessage.MessageAttachments[0].FileSize.Should().Be(fileSize);
            inboxMessage.MessageAttachments[0].ContentType.Should().Be(mimeType);
            inboxMessage.MessageAttachments[0].FileExtensions.Should().Be(fileExtension);
            inboxMessage.MessageAttachments[0].CreatedAt.Should().Be(syncTimestamp);
        }

        [Fact]
        public void MessageMapper_Can_Map_Multiple_Attachments()
        {
            var attachmentAMimeType = "plain/text";
            var attachmentAFileName = "carepatron-a.txt";
            var attachmentASize = 2134121;
            var attachmentAId = "abc";
            var attachmentAFileExtension = "txt";

            var attachmentBMimeType = "application/pdf";
            var attachmentBFileName = "carepatron-b.pdf";
            var attachmentBSize = 1220193;
            var attachmentBId = "xyz";
            var attachmentBFileExtension = "pdf";

            var syncTimestamp = DateTime.UtcNow;

            var attachmentA = new GmailMessagePayloadFaker()
                .WithMimeType(attachmentAMimeType)
                .WithFileName(attachmentAFileName)
                .WithBody(new GmailMessageBodyFaker()
                    .WithAttachmentId(attachmentAId)
                    .WithData(null)
                    .WithSize(attachmentASize))
                .Generate();

            var attachmentB = new GmailMessagePayloadFaker()
                .WithMimeType(attachmentBMimeType)
                .WithFileName(attachmentBFileName)
                .WithBody(new GmailMessageBodyFaker()
                    .WithAttachmentId(attachmentBId)
                    .WithData(null)
                    .WithSize(attachmentBSize))
                .Generate();

            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker()
                    .WithParts(attachmentA, attachmentB)
                    .Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, syncTimestamp);

            inboxMessage.MessageAttachments.Length.Should().Be(2);

            var attachmentAMapped = inboxMessage.MessageAttachments[0];
            attachmentAMapped.Id.Should().NotBeEmpty();
            attachmentAMapped.MediaId.Should().NotBeEmpty();
            attachmentAMapped.InboxMessageId.Should().Be(inboxMessage.Id);
            attachmentAMapped.ExternalFileId.Should().Be(attachmentAId);
            attachmentAMapped.FileName.Should().Be(attachmentAFileName);
            attachmentAMapped.FileSize.Should().Be(attachmentASize);
            attachmentAMapped.ContentType.Should().Be(attachmentAMimeType);
            attachmentAMapped.FileExtensions.Should().Be(attachmentAFileExtension);
            attachmentAMapped.CreatedAt.Should().Be(syncTimestamp);

            var attachmentBMapped = inboxMessage.MessageAttachments[1];
            attachmentBMapped.Id.Should().NotBeEmpty();
            attachmentBMapped.MediaId.Should().NotBeEmpty();
            attachmentBMapped.InboxMessageId.Should().Be(inboxMessage.Id);
            attachmentBMapped.ExternalFileId.Should().Be(attachmentBId);
            attachmentBMapped.FileName.Should().Be(attachmentBFileName);
            attachmentBMapped.FileSize.Should().Be(attachmentBSize);
            attachmentBMapped.ContentType.Should().Be(attachmentBMimeType);
            attachmentBMapped.FileExtensions.Should().Be(attachmentBFileExtension);
            attachmentBMapped.CreatedAt.Should().Be(syncTimestamp);
        }

        [Theory]
        [InlineData("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
            4, "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>")]
        [InlineData("<EMAIL>", "<EMAIL>", "<EMAIL>, <EMAIL>", "<EMAIL>", "<EMAIL>",
            5, "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>")]
        [InlineData("<EMAIL>", "<EMAIL>", null, "<EMAIL>, <EMAIL>", "<EMAIL>",
            4, "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>")]
        [InlineData("<EMAIL>", "<EMAIL>", "<EMAIL>", null, null,
            2, "<EMAIL>,<EMAIL>")]
        [InlineData("<EMAIL>", "<EMAIL>", "<EMAIL>", null, null,
            1, "<EMAIL>")]
        [InlineData("<EMAIL>", "<EMAIL>", "<EMAIL>, <EMAIL>", null, null,
            3, "<EMAIL>,<EMAIL>,<EMAIL>")]
        public void MessageMapper_Can_Map_Conversations(string accountId,
            string from,
            string to,
            string cc,
            string bcc,
            int expectedParticipantsCount,
            string expectedParticipants)
        {
            var providerId = Guid.NewGuid();
            var inboxId = Guid.NewGuid();
            var syncTimestamp = DateTime.UtcNow;

            var person = new SimplePersonFaker()
                .RuleFor(x => x.Email, accountId.ToEmailOrNull())
                .Generate();

            sendAsInfo = new GoogleListSendAsResponseFaker()
                .WithSendAs(person)
                .Generate();

            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker()
                    .WithFromToCcBccMessagePartHeader(from, to, cc, bcc)
                    .Generate())
                .Generate();

            var inboxMessage = mapper.Map(providerId, inboxId, message, sendAsInfo, userInfo, syncTimestamp);

            var participants = expectedParticipants.Split(',');
            var conversationId = inboxMessage.Conversation.Id;

            inboxMessage.Conversation.Id.Should().NotBeEmpty();
            inboxMessage.Conversation.InboxId.Should().Be(inboxId);
            inboxMessage.Conversation.DateCreated.Should().Be(syncTimestamp);
            inboxMessage.Conversation.ConversationParticipants.Length.Should().Be(expectedParticipantsCount);
            inboxMessage.Conversation.ConversationParticipants.All(x => x.Id != Guid.Empty).Should().BeTrue();
            inboxMessage.Conversation.ConversationParticipants.All(x => x.ProviderId == providerId).Should().BeTrue();
            inboxMessage.Conversation.ConversationParticipants.All(x => x.ConversationId == conversationId).Should().BeTrue();
            inboxMessage.Conversation.ConversationParticipants.Select(x => x.AccountId).Should().BeEquivalentTo(participants, opt => opt.WithoutStrictOrdering());
        }

        [Fact]
        public void MessageMapper_Can_Map_Headers_Case_Insensitively()
        {
            var fromAddress = new Faker().CarePatron().Email();
            var toAddress = new Faker().CarePatron().Email();
            var subject = new Faker().Random.Words(10);
            var ccAddress = new Faker().CarePatron().Email();
            var bccAddress = new Faker().CarePatron().Email();
            var date = DateTime.Parse("2023-11-17T03:00:00.000Z").ToUniversalTime();

            var fromRecipientDetail = new RecipientDetailFaker()
                .WithAccountId(fromAddress)
                .WithDisplayName(fromAddress)
                .Generate();

            var toRecipientDetail = new RecipientDetailFaker()
                .WithAccountId(toAddress)
                .WithDisplayName(toAddress)
                .Generate();

            var ccRecipientDetail = new RecipientDetailFaker()
                .WithAccountId(ccAddress)
                .WithDisplayName(ccAddress)
                .Generate();

            var bccRecipientDetail = new RecipientDetailFaker()
                .WithAccountId(bccAddress)
                .WithDisplayName(bccAddress)
                .Generate();

            var headers = new List<MessagePartHeader>()
            {
                new GmailMessagePartHeaderFaker().WithHeaderValue("from", fromAddress).Generate(),
                new GmailMessagePartHeaderFaker().WithHeaderValue("tO", toAddress).Generate(),
                new GmailMessagePartHeaderFaker().WithHeaderValue("subject", subject).Generate(),
                new GmailMessagePartHeaderFaker().WithHeaderValue("Cc", ccAddress).Generate(),
                new GmailMessagePartHeaderFaker().WithHeaderValue("bCc", bccAddress).Generate(),
                new GmailMessagePartHeaderFaker().WithHeaderValue("daTe", date.ToString("R")).Generate(),
            };

            var message = new GmailMessageFaker()
                .WithInternalDate(null)
                .WithPayload(new GmailMessagePayloadFaker()
                    .RuleFor(x => x.Headers, headers)
                    .Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.From.Should().BeEquivalentTo(fromRecipientDetail);
            inboxMessage.MessageSubject.Should().Be(subject);
            inboxMessage.Recipients.To.Single().Should().BeEquivalentTo(toRecipientDetail);
            inboxMessage.Recipients.Cc.Single().Should().BeEquivalentTo(ccRecipientDetail);
            inboxMessage.Recipients.Bcc.Single().Should().BeEquivalentTo(bccRecipientDetail);
            inboxMessage.CreatedAt.Should().Be(date);
        }

        [Theory(Skip = "Temporarily skip for removal")]
        [InlineData("PGRpdiBkaXI9Imx0ciI-SGkgQ2xpZW50IEhvdyBtYXkgSSBoZWxwIHlvdSBvdXQ_PC9kaXY-PGJyPjxkaXYgY2xhc3M9ImdtYWlsX3F1b3RlIj48ZGl2IGRpcj0ibHRyIiBjbGFzcz0iZ21haWxfYXR0ciI-T24gTW9uLCAxMSBNYXIgMjAyNCBhdCAxNDoyNywgcmFscGggJmx0OzxhIGhyZWY9Im1haWx0bzpyZGV2dGVzdGluYm94QGdtYWlsLmNvbSI-cmRldnRlc3RpbmJveEBnbWFpbC5jb208L2E-Jmd0OyB3cm90ZTo8YnI-PC9kaXY-PGJsb2NrcXVvdGUgY2xhc3M9ImdtYWlsX3F1b3RlIiBzdHlsZT0ibWFyZ2luOjBweCAwcHggMHB4IDAuOGV4O2JvcmRlci1sZWZ0OjFweCBzb2xpZCByZ2IoMjA0LDIwNCwyMDQpO3BhZGRpbmctbGVmdDoxZXgiPjxkaXYgZGlyPSJsdHIiPkhlbGxvIFByYWMgSG93IGFyZSB5b3U_PC9kaXY-DQo8L2Jsb2NrcXVvdGU-PC9kaXY-DQo=",
            "<div dir=\"ltr\">Hi Client How may I help you out?</div><br><div class=\"gmail_quote\"><div dir=\"ltr\" class=\"gmail_attr\">On Mon, 11 Mar 2024 at 14:27, ralph &lt;<a href=\"mailto:<EMAIL>\"><EMAIL></a>&gt; wrote:<br></div><blockquote class=\"gmail_quote\" style=\"margin:0px 0px 0px 0.8ex;border-left:1px solid rgb(204,204,204);padding-left:1ex\"><div dir=\"ltr\">Hello Prac How are you?</div>\r\n</blockquote></div>",
            "<div dir=\"ltr\">Hi Client How may I help you out?</div><br>")]
        public void MessageMapper_Can_Map_Html_Body_Remove_Quoted_Strings(string base64Body,
            string htmlBody,
            string expectedMessageBody)
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithHtmlBodyMessagePart(base64Body, null).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.MessageBody.Should().Be(expectedMessageBody);
        }

        [Theory]
        [InlineData("PGRpdiBkaXI9Imx0ciI-VGhpcyBpcyBhIGZvcndhcmRlZCBtZXNzYWdlPGJyPjxicj48ZGl2IGNsYXNzPSJnbWFpbF9xdW90ZSI-PGRpdiBkaXI9Imx0ciIgY2xhc3M9ImdtYWlsX2F0dHIiPi0tLS0tLS0tLS0gRm9yd2FyZGVkIG1lc3NhZ2UgLS0tLS0tLS0tPGJyPkZyb206IDxzdHJvbmcgY2xhc3M9ImdtYWlsX3NlbmRlcm5hbWUiIGRpcj0iYXV0byI-*******************************************************************************************************************************************************-PGJyPkRhdGU6IE1vbiwgMTAgSnVuIDIwMjQgYXQgMTA6NTQ8YnI-*******************************************************************************************************************************************************************************************-PGRpdiBkaXI9Imx0ciI-VGhpcyBpcyBhIGZ3IHRlc3QgbWVzc2FnZTxicj48L2Rpdj4NCjwvZGl2PjwvZGl2Pg0K",
            "<div dir=\"ltr\">This is a forwarded message<br><br><div class=\"gmail_quote\"><div dir=\"ltr\" class=\"gmail_attr\">---------- Forwarded message ---------<br>From: <strong class=\"gmail_sendername\" dir=\"auto\">Ralph Mate</strong> <span dir=\"auto\">&lt;<a href=\"mailto:<EMAIL>\"><EMAIL></a>&gt;</span><br>Date: Mon, 10 Jun 2024 at 10:54<br>Subject: This is a fw test message<br>To: Ralph Mate &lt;<a href=\"mailto:<EMAIL>\"><EMAIL></a>&gt;<br></div><br><br><div dir=\"ltr\">This is a fw test message<br></div>\r\n</div></div>",
            "<div dir=\"ltr\">This is a forwarded message<br><br><div><div dir=\"ltr\">---------- Forwarded message ---------<br>From: <strong dir=\"auto\">Ralph Mate</strong> <span dir=\"auto\">&lt;<a><EMAIL></a>&gt;</span><br>Date: Mon, 10 Jun 2024 at 10:54<br>Subject: This is a fw test message<br>To: Ralph Mate &lt;<a><EMAIL></a>&gt;<br></div><br><br><div dir=\"ltr\">This is a fw test message<br></div>\n</div></div>")]
        public void MessageMapper_Can_Map_Html_Body_Persist_Forward_Quoted_Strings(string base64Body,
            string htmlBody,
            string expectedMessageBody)
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithHtmlBodyMessagePart(base64Body, null).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.MessageBody.Should().BeEquivalentTo(expectedMessageBody);
        }

        [Theory]
        [InlineData("INBOX", false)]
        [InlineData("SENT", true)]
        [InlineData("INBOX,SENT", true)]
        [InlineData("inbox,sent", true)]
        public void MessageMapper_Can_Mark_Read_Unread_On_Sent_Messages(string labelIds, bool expectedReadStatus)
        {
            var message = new GmailMessageFaker()
                .WithLabelIds(labelIds.Split(','))
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.IsRead.Should().Be(expectedReadStatus);
        }

        [Fact]
        public void MessageMapper_Can_Sanitize_Html()
        {
            var inputHtml = @"<script>alert('xss')</script><div onload=""alert('xss')"" style=""background-color: rgba(0, 0, 0, 1)"">Test<img src=""test.png"" style=""background-image: url(javascript:alert('xss')); margin: 10px""></div>";

            var base64Body = @"PHNjcmlwdD5hbGVydCgneHNzJyk8L3NjcmlwdD48ZGl2IG9ubG9hZD0iYWxlcnQoJ3hzcycpIiBzdHlsZT0iYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAxKSI+VGVzdDxpbWcgc3JjPSJ0ZXN0LnBuZyIgc3R5bGU9ImJhY2tncm91bmQtaW1hZ2U6IHVybChqYXZhc2NyaXB0OmFsZXJ0KCd4c3MnKSk7IG1hcmdpbjogMTBweCI+PC9kaXY+";

            var expectedHtml = "<div style=\"background-color: rgba(0, 0, 0, 1)\">Test<img src=\"test.png\" style=\"margin: 10px\"></div>";

            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithHtmlBodyMessagePart(base64Body, null).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.MessageBody.Should().Be(expectedHtml);
        }

        [Theory]
        [InlineData(MessageType.Html, MessageStatus.Default, $"{MessageExtensions.GmailLabelInbox},{MessageExtensions.GmailLabelSent}")]
        [InlineData(MessageType.PlainText, MessageStatus.Default, $"{MessageExtensions.GmailLabelInbox},{MessageExtensions.GmailLabelSent}")]
        [InlineData(MessageType.Html, MessageStatus.Archived, $"{MessageExtensions.GmailLabelSent}")]
        [InlineData(MessageType.PlainText, MessageStatus.Archived, $"{MessageExtensions.GmailLabelSent}")]
        [InlineData(MessageType.Html, MessageStatus.Deleted, $"{MessageExtensions.GmailLabelTrash},{MessageExtensions.GmailLabelSent}")]
        [InlineData(MessageType.PlainText, MessageStatus.Deleted, $"{MessageExtensions.GmailLabelTrash},{MessageExtensions.GmailLabelSent}")]
        public void MessageMapper_Can_CreateMessage_New(MessageType messageType, MessageStatus messageStatus, string expectedLabels)
        {
            var from = new SenderInfoFaker().Generate();
            var recipients = new RecipientFaker().Generate();

            var messageSubject = new Faker().Random.Words(5);
            var messageBody = new Faker().Random.Words(10);

            var message = mapper.CreateMessage(from, recipients, messageSubject, messageBody, messageRfcId, messageType, messageStatus);

            message.Raw.Should().NotBeEmpty();
            message.ThreadId.Should().BeNull();
            message.LabelIds.Should().BeEquivalentTo(expectedLabels.Split(','), opt => opt.WithoutStrictOrdering());
        }

        [Theory]
        [InlineData(MessageType.Html, MessageStatus.Default, $"{MessageExtensions.GmailLabelInbox},{MessageExtensions.GmailLabelSent}")]
        [InlineData(MessageType.PlainText, MessageStatus.Default, $"{MessageExtensions.GmailLabelInbox},{MessageExtensions.GmailLabelSent}")]
        [InlineData(MessageType.Html, MessageStatus.Archived, $"{MessageExtensions.GmailLabelSent}")]
        [InlineData(MessageType.PlainText, MessageStatus.Archived, $"{MessageExtensions.GmailLabelSent}")]
        [InlineData(MessageType.Html, MessageStatus.Deleted, $"{MessageExtensions.GmailLabelTrash},{MessageExtensions.GmailLabelSent}")]
        [InlineData(MessageType.PlainText, MessageStatus.Deleted, $"{MessageExtensions.GmailLabelTrash},{MessageExtensions.GmailLabelSent}")]
        public void MessageMapper_Can_CreateMessage_Reply(MessageType messageType, MessageStatus messageStatus, string expectedLabels)
        {
            var from = new SenderInfoFaker().Generate();
            var recipients = new RecipientFaker().Generate();

            var messageSubject = new Faker().Random.Words(5);
            var messageBody = new Faker().Random.Words(10);

            var replyToMessage = new GmailMessageFaker()
                .Generate();

            var message = mapper.CreateMessage(from, recipients, messageSubject, messageBody, messageRfcId, messageType, messageStatus, replyToMessage);

            message.Raw.Should().NotBeEmpty();
            message.ThreadId.Should().Be(replyToMessage.ThreadId);
            message.LabelIds.Should().BeEquivalentTo(expectedLabels.Split(','), opt => opt.WithoutStrictOrdering());
        }

        [Theory]
        [InlineData(MessageType.Html)]
        [InlineData(MessageType.PlainText)]
        public void MessageMapper_Can_CreateMessage_New_WithAttachments(MessageType messageType)
        {
            var from = new SenderInfoFaker().Generate();
            var recipients = new RecipientFaker().Generate();

            var messageSubject = new Faker().Random.Words(5);
            var messageBody = new Faker().Random.Words(10);

            var attachments = new MessageAttachmentStreamFaker()
                .WithFileStream()
                .Generate(5)
                .ToArray();

            var message = mapper.CreateMessage(from, recipients, messageSubject, messageBody, messageRfcId, messageType, MessageStatus.Default, null, attachments);

            message.Raw.Should().NotBeEmpty();
            message.ThreadId.Should().BeNull();
        }

        [Theory]
        [InlineData(MessageType.Html)]
        [InlineData(MessageType.PlainText)]
        public void MessageMapper_Can_CreateMessage_Reply_With_Attachments(MessageType messageType)
        {
            var from = new SenderInfoFaker().Generate();
            var recipients = new RecipientFaker().Generate();

            var messageSubject = new Faker().Random.Words(5);
            var messageBody = new Faker().Random.Words(10);

            var replyToMessage = new GmailMessageFaker()
                .Generate();

            var attachments = new MessageAttachmentStreamFaker()
                .WithFileStream()
                .Generate(5)
                .ToArray();

            var message = mapper.CreateMessage(from, recipients, messageSubject, messageBody, messageRfcId, messageType, MessageStatus.Default, replyToMessage, attachments);

            message.Raw.Should().NotBeEmpty();
            message.ThreadId.Should().Be(replyToMessage.ThreadId);
        }

        [Fact]
        public void MessageMapper_Can_Map_Email_Rfc_Mesage_Id()
        {
            var messageId = new Faker().Random.Hash(32);

            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithMessageIdHeader(messageId).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.EmailRfcMessageId.Should().Be(messageId);
        }

        [Fact]
        public void MessageMapper_Can_Drop_Message_Without_From_Header()
        {
            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithoutFromMessagePartHeader().Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), [message], sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.Should().BeEmpty();
        }

        [Fact]
        public void MessageMapper_Can_Handle_Messages_With_and_Without_From_Header()
        {
            var messageWithoutFrom = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithoutFromMessagePartHeader().Generate())
                .Generate();

            var messageId = new Faker().Random.Hash(32);

            var messageWithFrom = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithMessageIdHeader(messageId).Generate())
                .Generate();

            var inboxMessages = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), [messageWithoutFrom, messageWithFrom], sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessages.Should().HaveCount(1);
            inboxMessages.First().EmailRfcMessageId.Should().Be(messageId);
        }

        [Fact]
        public void MessageMapper_Can_Handle_Null_Messages()
        {
            var inboxMessages = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), (List<Message>)null, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessages.Should().BeEmpty();
        }

        [Fact]
        public void MessageMapper_Can_Handle_Null_Payload_Messages()
        {
            var message = new GmailMessageFaker()
                .WithPayload(null)
                .Generate();

            var inboxMessages = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), [message], sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessages.Should().BeEmpty();
        }

        [Fact]
        public void MessageMapper_Can_Handle_Null_Or_Empty_Payload_Headers_Messages()
        {
            var nullHeaderPart = new GmailMessagePayloadFaker()
                .RuleFor(x => x.Headers, (IList<MessagePartHeader>)null)
                .Generate();

            var nullHeaderMessage = new GmailMessageFaker()
                .WithPayload(nullHeaderPart)
                .Generate();

            var emptyHeaderPart = new GmailMessagePayloadFaker()
                .RuleFor(x => x.Headers, [])
                .Generate();

            var emptyHeaderMessage = new GmailMessageFaker()
                .WithPayload(emptyHeaderPart)
                .Generate();

            var inboxMessages = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), [nullHeaderMessage, emptyHeaderMessage], sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessages.Should().BeEmpty();
        }

        [Fact]
        public void MessageMapper_Can_Handle_Null_Or_Empty_LabelId_Messages()
        {
            var nullLabelIdMessage = new GmailMessageFaker()
                .WithLabelIds(null)
                .Generate();

            var emptyLabelIdMessage = new GmailMessageFaker()
                .WithLabelIds([])
                .Generate();

            var inboxMessages = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), [nullLabelIdMessage, emptyLabelIdMessage], sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessages.Should().HaveCount(2);
            inboxMessages.All(x => !x.IsRead).Should().BeTrue();
        }

        [Fact]
        public void MessageMapper_Can_Handle_Message_Headers_Case_Insensitive()
        {
            var from = "<EMAIL>";

            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithFromMessagePartHeader(from).Generate())
                .Generate();

            foreach (var header in message.Payload.Headers)
            {
                header.Name = header.Name.ToUpper();
            }

            var inboxMessages = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), [message], sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessages.Should().HaveCount(1);
            inboxMessages.All(x => x.From.AccountId == from).Should().BeTrue();
        }

        [Fact]
        public void MessageMapper_Can_Handle_Inline_Image()
        {
            var contentId = "ii_m03ylmy50";

            var inputHtml = $"<div dir=\"ltr\">This is an inline image  message<br><img src=\"cid:{contentId}\" alt=\"image.png\" width=\"542\" height=\"110\"><br></div>";

            var base64Body = "PGRpdiBkaXI9Imx0ciI-VGhpcyBpcyBhbiBpbmxpbmXCoGltYWdlICBtZXNzYWdlPGJyPjxpbWcgc3JjPSJjaWQ6aWlfbTAzeWxteTUwIiBhbHQ9ImltYWdlLnBuZyIgd2lkdGg9IjU0MiIgaGVpZ2h0PSIxMTAiPjxicj48L2Rpdj4NCg==";

            var htmlBodyMessageParts = new GmailMessagePayloadFaker()
                .WithHtmlBodyMessagePart(base64Body, null)
                .Generate();

            var attachmentParts = new GmailMessagePayloadFaker()
                .WithFileAttachmentMessagePart("ii_03ylmy50", "image.png", 542, "image/png", string.Concat('<', contentId, '>'))
                .Generate();

            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithParts(htmlBodyMessageParts, attachmentParts))
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            var attachment = inboxMessage.MessageAttachments.Single();

            var expectedHtml = $"<div dir=\"ltr\">This is an inline&nbsp;image  message<br><img src=\"\" alt=\"image.png\" width=\"542\" height=\"110\" cp-file-id=\"{attachment.Id}\"><br></div>";

            inboxMessage.MessageBody.Should().Be(expectedHtml);
            inboxMessage.MessageAttachments.Single().IsEmbedded.Should().BeTrue();
        }

        [Fact]
        public void MessageMapper_Can_Omit_UndisclosedRecipients_To()
        {
            var to = "undisclosed-recipients:;";

            var message = new GmailMessageFaker()
                .WithPayload(new GmailMessagePayloadFaker().WithToMessagePartHeader(to).Generate())
                .Generate();

            var inboxMessage = mapper.Map(Guid.NewGuid(), Guid.NewGuid(), message, sendAsInfo, userInfo, DateTime.UtcNow);

            inboxMessage.Recipients.To.Should().BeNull();
        }
    }
}