﻿using carepatron.core.Application.Communications.Inbox.Abstractions;
using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Application.Communications.Inbox.Queries;
using carepatron.core.Application.Communications.Inbox.Services;
using carepatron.core.Exceptions;
using carepatron.core.Paging.Models;
using Google.Cloud.AIPlatform.V1Beta1;

namespace tests.core.Application.Communications.Inbox.Queries
{
    public class GetConversationsQueryHandlerTests
    {
        private readonly Mock<IInboxRepository> inboxRepository;
        private readonly Mock<IIdentityContext> identityContext;
        private readonly Mock<IInboxService> inboxService;

        private static readonly Provider provider = new ProviderFaker().Generate();
        private static readonly Person person = new PersonFaker().Generate();

        private GetConversationsQueryHandler getConversationsQueryHandler;

        public GetConversationsQueryHandlerTests()
        {
            inboxRepository = new Mock<IInboxRepository>();
            identityContext = new Mock<IIdentityContext>();
            inboxService = new Mock<IInboxService>();

            var providerPermissions = new ProviderPermissionsFaker()
                .WithProviderId(provider.Id)
                .WithPersonId(person.Id)
                .Generate();

            identityContext.Setup(x => x.ProviderPermissions).Returns(providerPermissions);
        }

        [Fact]
        public async Task GetConversationsQueryHandler_Handle_NoInbox_Found()
        {
            var inboxId = Guid.NewGuid();

            var request = new GetConversationsQuery(identityContext.Object, inboxId, "inbox", null, ConversationSorting.Newest, ConversationFiltering.All, null, null, 0, false);

            inboxRepository.Setup(x => x.Get(inboxId)).ReturnsAsync((InboxInfo)null);

            getConversationsQueryHandler = new GetConversationsQueryHandler(inboxRepository.Object, inboxService.Object);

            var result = await getConversationsQueryHandler.Handle(request, CancellationToken.None);

            result.Result.Should().BeEquivalentTo(new TokenisedPaginatedResult<ConversationResponse>(Array.Empty<ConversationResponse>()));
        }

        [Theory]
        [InlineData("inbox", MessageStatus.Default)]
        [InlineData("archive", MessageStatus.Archived)]
        [InlineData("bin", MessageStatus.Deleted)]
        [InlineData("sent", MessageStatus.Sent)]
        [InlineData("draft", MessageStatus.Draft)]
        public async Task GetConversationsQueryHandler_Handle_NoMessages_Found(string folder, MessageStatus status)
        {
            var inboxId = Guid.NewGuid();

            var inboxInfo = new InboxInfoFaker()
                .WithHasConnectedAccount(true)
                .WithInboxId(inboxId)
                .WithPerson(person)
                .WithProviderId(provider.Id)
                .Generate();

            var request = new GetConversationsQuery(identityContext.Object, inboxId, folder, null, ConversationSorting.Newest, ConversationFiltering.All, null, null, 0, false);

            inboxRepository.Setup(x => x.Get(inboxId)).ReturnsAsync(inboxInfo);
            inboxService.Setup(x => x.GetInboxConversations(provider.Id, new[] { inboxId }, status, ConversationSorting.Newest, ConversationFiltering.All, It.IsAny<string>(), false, It.IsAny<TokenisedPaginationRequest>()))
                .ReturnsAsync(new TokenisedPaginatedResult<ConversationResponse>(Array.Empty<ConversationResponse>()));

            getConversationsQueryHandler = new GetConversationsQueryHandler(inboxRepository.Object, inboxService.Object);

            var result = await getConversationsQueryHandler.Handle(request, CancellationToken.None);

            result.Result.Items.Should().BeEmpty();
            result.Result.PaginationToken.Should().BeNull();
        }

        [Theory]
        [InlineData("inbox", MessageStatus.Default)]
        [InlineData("archive", MessageStatus.Archived)]
        [InlineData("bin", MessageStatus.Deleted)]
        [InlineData("sent", MessageStatus.Sent)]
        [InlineData("draft", MessageStatus.Draft)]
        public async Task GetConversationsQueryHandler_Handle_Message_Items(string folder, MessageStatus status)
        {
            var inboxId = Guid.NewGuid();
            var date = DateTime.Now;

            var inboxInfo = new InboxInfoFaker()
                .WithHasConnectedAccount(true)
                .WithInboxId(inboxId)
                .WithPerson(person)
                .WithProviderId(provider.Id)
                .Generate();

            var from = new RecipientDetailFaker()
                .WithAccountId(person.Email)
                .WithDisplayName(person.FullName)
                .Generate();

            var latestMessage = new InboxMessageFaker()
                .WithFrom(from)
                .WithCreatedAt(date)
                .Generate();

            var conversations = new ConversationResponseFaker()
                .WithMessage(latestMessage)
                .Generate(1)
                .ToArray();

            var request = new GetConversationsQuery(identityContext.Object, inboxId, folder, null, ConversationSorting.Newest, ConversationFiltering.All, null, null, 20, false);

            var paginatedResult = new TokenisedPaginatedResult<ConversationResponse>(conversations, null, conversations.Length);

            inboxRepository.Setup(x => x.Get(inboxId)).ReturnsAsync(inboxInfo);
            inboxService.Setup(x => x.GetInboxConversations(provider.Id, new[] { inboxId }, status, ConversationSorting.Newest, ConversationFiltering.All, It.IsAny<string>(), false, It.IsAny<TokenisedPaginationRequest>()))
                .ReturnsAsync(paginatedResult);

            getConversationsQueryHandler = new GetConversationsQueryHandler(inboxRepository.Object, inboxService.Object);

            var result = await getConversationsQueryHandler.Handle(request, CancellationToken.None);

            result.Result.Items.Should().BeEquivalentTo(new[]
            {
                conversations.First()
            });
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("empty")]
        public async Task GetConversationsQueryHandler_Can_Handle_Invalid_Folder(string folder)
        {
            var inbox = new InboxInfoFaker()
                .WithProviderId(provider.Id)
                .WithPerson(person)
                .Generate();

            inboxRepository.Setup(x => x.Get(inbox.Id)).ReturnsAsync(inbox);

            var request = new GetConversationsQuery(identityContext.Object, inbox.Id, folder, null, ConversationSorting.Newest, ConversationFiltering.All, null, null, 20, false);

            getConversationsQueryHandler = new GetConversationsQueryHandler(inboxRepository.Object, inboxService.Object);
            Func<Task> act = async () => { await getConversationsQueryHandler.Handle(request, CancellationToken.None); };

            await act.Should().ThrowAsync<UnknownInboxFolderException>();
        }

        [Theory]
        [InlineData("inbox", MessageStatus.Default)]
        [InlineData("archive", MessageStatus.Archived)]
        [InlineData("bin", MessageStatus.Deleted)]
        [InlineData("sent", MessageStatus.Sent)]
        [InlineData("draft", MessageStatus.Draft)]
        public async Task GetConversationsQueryHandler_Handle_Message_Items_With_Contact_Id(string folder, MessageStatus status)
        {
            var inboxId = Guid.NewGuid();
            var contactId = Guid.NewGuid();
            var date = DateTime.Now;

            var inboxInfo = new InboxInfoFaker()
                .WithHasConnectedAccount(true)
                .WithInboxId(inboxId)
                .WithPerson(person)
                .WithProviderId(provider.Id)
                .Generate();

            var from = new RecipientDetailFaker()
                .WithAccountId(person.Email)
                .WithDisplayName(person.FullName)
                .Generate();

            var latestMessage = new InboxMessageFaker()
                .WithFrom(from)
                .WithCreatedAt(date)
                .Generate();

            var conversations = new ConversationResponseFaker()
                .WithMessage(latestMessage)
                .Generate(1)
                .ToArray();

            var request = new GetConversationsQuery(identityContext.Object, null, folder, contactId, ConversationSorting.Newest, ConversationFiltering.All, null, null, 20, false);

            var paginatedResult = new TokenisedPaginatedResult<ConversationResponse>(conversations, null, conversations.Length);

            inboxRepository.Setup(x => x.Get(inboxId)).ReturnsAsync(inboxInfo);
            inboxService.Setup(x => x.GetContactConversations(provider.Id, contactId, status, ConversationSorting.Newest, ConversationFiltering.All, It.IsAny<string>(), false, It.IsAny<TokenisedPaginationRequest>()))
                .ReturnsAsync(paginatedResult);

            getConversationsQueryHandler = new GetConversationsQueryHandler(inboxRepository.Object, inboxService.Object);

            var result = await getConversationsQueryHandler.Handle(request, CancellationToken.None);

            result.Result.Items.Should().BeEquivalentTo(new[]
            {
                conversations.First()
            });
        }

        [Theory]
        [InlineData("inbox", MessageStatus.Default)]
        [InlineData("archive", MessageStatus.Archived)]
        [InlineData("bin", MessageStatus.Deleted)]
        [InlineData("sent", MessageStatus.Sent)]
        [InlineData("draft", MessageStatus.Draft)]
        public async Task GetConversationsQueryHandler_Handle_NoMessages_Found_With_Contact_Id(string folder, MessageStatus status)
        {
            var inboxId = Guid.NewGuid();
            var contactId = Guid.NewGuid();

            var inboxInfo = new InboxInfoFaker()
                .WithHasConnectedAccount(true)
                .WithInboxId(inboxId)
                .WithPerson(person)
                .WithProviderId(provider.Id)
                .Generate();

            var request = new GetConversationsQuery(identityContext.Object, null, folder, contactId, ConversationSorting.Newest, ConversationFiltering.All, null, null, 0, false);

            inboxRepository.Setup(x => x.Get(inboxId)).ReturnsAsync(inboxInfo);
            inboxService.Setup(x => x.GetContactConversations(provider.Id, contactId, status, ConversationSorting.Newest, ConversationFiltering.All, It.IsAny<string>(), false, It.IsAny<TokenisedPaginationRequest>()))
                .ReturnsAsync(new TokenisedPaginatedResult<ConversationResponse>(Array.Empty<ConversationResponse>()));

            getConversationsQueryHandler = new GetConversationsQueryHandler(inboxRepository.Object, inboxService.Object);

            var result = await getConversationsQueryHandler.Handle(request, CancellationToken.None);

            result.Result.Items.Should().BeEmpty();
            result.Result.PaginationToken.Should().BeNull();
        }
    }
}