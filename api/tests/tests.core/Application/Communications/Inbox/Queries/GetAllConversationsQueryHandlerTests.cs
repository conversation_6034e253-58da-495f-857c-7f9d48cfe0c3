﻿using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Application.Communications.Inbox.Queries;
using carepatron.core.Application.Communications.Inbox.Services;
using carepatron.core.Authorization.Constants;
using carepatron.core.Exceptions;
using carepatron.core.Paging.Models;

namespace tests.core.Application.Communications.Inbox.Queries
{
    public class GetAllConversationsQueryHandlerTests
    {
        private readonly Mock<IInboxService> inboxService = new();
        private readonly Mock<IIdentityContext> identityContext = new();

        private static readonly Provider provider = new ProviderFaker().Generate();
        private static readonly Person person = new PersonFaker().Generate();

        private GetAllConversationsQueryHandler getAllConversationsQueryHandler;

        public GetAllConversationsQueryHandlerTests()
        {
            var providerPermissions = new ProviderPermissionsFaker()
                .WithProviderId(provider.Id)
                .WithPersonId(person.Id)
                .Generate();

            identityContext.Setup(x => x.ProviderPermissions).Returns(providerPermissions);

            getAllConversationsQueryHandler = new(inboxService.Object);
        }

        [Theory]
        [InlineData("inbox", MessageStatus.Default)]
        [InlineData("archive", MessageStatus.Archived)]
        [InlineData("bin", MessageStatus.Deleted)]
        [InlineData("sent", MessageStatus.Sent)]
        [InlineData("draft", MessageStatus.Draft)]
        public async Task GetAllConversationsQueryHandler_Handle_NoMessages_Found(string folder, MessageStatus status)
        {
            var request = new GetAllConversationsQuery(identityContext.Object, folder, ConversationSorting.Newest, ConversationFiltering.All, null, null, 0, false);

            inboxService.Setup(x => x.GetAuthorizedInboxIds(provider.Id, person.Id, PolicyOperationValues.View))
                .ReturnsAsync([Guid.NewGuid()]);

            inboxService.Setup(x => x.GetInboxConversations(provider.Id, It.IsAny<Guid[]>(), status, ConversationSorting.Newest, ConversationFiltering.All, It.IsAny<string>(), false, It.IsAny<TokenisedPaginationRequest>()))
                .ReturnsAsync(new TokenisedPaginatedResult<ConversationResponse>(Array.Empty<ConversationResponse>()));

            var result = await getAllConversationsQueryHandler.Handle(request, CancellationToken.None);

            result.Result.Items.Should().BeEmpty();
            result.Result.PaginationToken.Should().BeNull();
        }

        [Theory]
        [InlineData("inbox", MessageStatus.Default)]
        [InlineData("archive", MessageStatus.Archived)]
        [InlineData("bin", MessageStatus.Deleted)]
        [InlineData("sent", MessageStatus.Sent)]
        [InlineData("draft", MessageStatus.Draft)]
        public async Task GetAllConversationsQueryHandler_Handle_Message_Items(string folder, MessageStatus status)
        {
            var inboxId = Guid.NewGuid();
            var date = DateTime.Now;

            var inboxInfo = new InboxInfoFaker()
                .WithHasConnectedAccount(true)
                .WithInboxId(inboxId)
                .WithPerson(person)
                .WithProviderId(provider.Id)
                .Generate();

            var from = new RecipientDetailFaker()
                .WithAccountId(person.Email)
                .WithDisplayName(person.FullName)
                .Generate();

            var latestMessage = new InboxMessageFaker()
                .WithFrom(from)
                .WithCreatedAt(date)
                .Generate();

            var conversations = new ConversationResponseFaker()
                .WithMessage(latestMessage)
                .Generate(1)
                .ToArray();

            var request = new GetAllConversationsQuery(identityContext.Object, folder, ConversationSorting.Newest, ConversationFiltering.All, null, null, 20, false);

            var paginatedResult = new TokenisedPaginatedResult<ConversationResponse>(conversations, null, conversations.Length);

            var authorizedInboxIds = new[] { inboxId };

            inboxService.Setup(x => x.GetAuthorizedInboxIds(provider.Id, person.Id, PolicyOperationValues.View))
                .ReturnsAsync(authorizedInboxIds);

            inboxService.Setup(x => x.GetInboxConversations(provider.Id, authorizedInboxIds, status, ConversationSorting.Newest, ConversationFiltering.All, It.IsAny<string>(), false, It.IsAny<TokenisedPaginationRequest>()))
                .ReturnsAsync(paginatedResult);

            var result = await getAllConversationsQueryHandler.Handle(request, CancellationToken.None);

            result.Result.Items.Should().BeEquivalentTo(new[]
            {
                conversations.First()
            });
        }

        [Theory]
        [InlineData(null)]
        [InlineData("")]
        [InlineData("empty")]
        public async Task GetAllConversationsQueryHandler_Can_Handle_Invalid_Folder(string folder)
        {
            var inbox = new InboxInfoFaker()
                .WithProviderId(provider.Id)
                .WithPerson(person)
                .Generate();

            var request = new GetAllConversationsQuery(identityContext.Object, folder, ConversationSorting.Newest, ConversationFiltering.All, null, null, 20, false);

            Func<Task> act = async () => { await getAllConversationsQueryHandler.Handle(request, CancellationToken.None); };

            await act.Should().ThrowAsync<UnknownInboxFolderException>();
        }
    }
}