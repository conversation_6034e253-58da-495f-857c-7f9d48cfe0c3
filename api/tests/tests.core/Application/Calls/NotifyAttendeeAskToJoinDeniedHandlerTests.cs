using carepatron.core.Application.Calls.Commands;
using carepatron.core.Application.Communications.Notifications.Models;
using Notifications.Sdk.Client.Abstract;

namespace tests.core.Application.Tasks.Commands;

public class NotifyAttendeeAskToJoinDeniedHandlerTests
{
    private readonly Mock<INotificationsService> _notificationsServiceMock = new();
    private readonly NotifyAttendeeAskToJoinDeniedHandler _sut;

    public NotifyAttendeeAskToJoinDeniedHandlerTests()
    {
        _sut = new NotifyAttendeeAskToJoinDeniedHandler(
            _notificationsServiceMock.Object
        );
    }

    [Fact]
    public async Task Should_Notify()
    {
        // Arrange
        var callId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var personId = Guid.NewGuid();
        var callAttendeeId = Guid.NewGuid();
        var callAttendeeName = "Test User";
        var joinedDateTime = DateTime.UtcNow;

        var command = new NotifyAttendeeAskToJoinDeniedCommand(
            callId,
            providerId,
            personId,
            callAttendeeId,
            callAttendeeName,
            joinedDateTime
        );

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        _notificationsServiceMock.Verify(
            x => x.Notify(
                providerId,
                It.Is<Guid[]>(ids => ids.Length == 1 && ids[0] == personId),
                It.Is<CallAttendeeDenied>(n =>
                    n.CallId == callId &&
                    n.CallAttendeeId == callAttendeeId &&
                    n.CallAttendeeName == callAttendeeName &&
                    n.JoinedDateTime == joinedDateTime
                )
            ),
            Times.Once
        );
    }
}
