using carepatron.core.Application.Calls.Commands;
using carepatron.core.Application.Communications.Notifications.Models;
using Notifications.Sdk.Client.Abstract;

namespace tests.core.Application.Tasks.Commands;

public class NotifyCallAttendeeJoinAttemptedCommandHandlerTests
{
    private readonly Mock<INotificationsService> _notificationsServiceMock = new();
    private readonly NotifyAttendeeJoinCallAttemptedCommandHandler _sut;

    public NotifyCallAttendeeJoinAttemptedCommandHandlerTests()
    {
        _sut = new NotifyAttendeeJoinCallAttemptedCommandHandler(
            _notificationsServiceMock.Object
        );
    }

    [Fact]
    public async Task Should_Notify()
    {
        // Arrange
        var callId = Guid.NewGuid();
        var providerId = Guid.NewGuid();
        var hostId = new[] { Guid.NewGuid() };
        var call = new carepatron.core.Models.Call.Call { Id = callId };

        var command = new NotifyAttendeeJoinCallAttemptedCommand(
            callId,
            providerId,
            hostId[0],
            "Test User",
            DateTime.UtcNow
        );

        // Act
        var result = await _sut.Handle(command, CancellationToken.None);

        // Assert
        result.IsSuccess.Should().BeTrue();
        _notificationsServiceMock.Verify(
            x => x.Notify(
                providerId,
                hostId,
                It.Is<CallAttendeeJoinAttempted>(n =>
                    n.CallId == callId &&
                    n.CallAttendeeName == "Test User" &&
                    n.JoinedDateTime == command.JoinedDateTime
                    
                )
            ),
            Times.Once
        );
    }
}
