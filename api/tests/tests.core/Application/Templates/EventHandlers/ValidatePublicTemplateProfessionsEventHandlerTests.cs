﻿using Bogus;
using carepatron.core.Application.Templates.EventHandlers;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Repositories.Templates;
using Microsoft.Extensions.Logging;
using Org.BouncyCastle.Crypto;
using tests.common.Mocks;

namespace tests.core.Application.Templates.EventHandlers;
public class ValidatePublicTemplateProfessionsEventHandlerTests
{
    private readonly ValidatePublicTemplateProfessionsEventHandler sut;
    private readonly Mock<IPersonRepository> personRepository = new();
    private readonly TempTemplateProfessionCsvRepositoryMock tempTemplateProfessionCsvRepository = new TempTemplateProfessionCsvRepositoryMock();
    private readonly Mock<ITempTemplateProfessionRepository> tempTemplateProfessionRepository = new();
    private readonly Mock<ILogger<carepatron.core.Application.Templates.EventHandlers.ValidatePublicTemplateProfessionsEventHandler>> logger = new();

    public ValidatePublicTemplateProfessionsEventHandlerTests()
    {
        sut = new(personRepository.Object,
            tempTemplateProfessionRepository.Object,
            tempTemplateProfessionCsvRepository.Object,
            logger.Object
            );
    }

    [Theory]
    [InlineData(30)]
    [InlineData(100)]
    public async Task ValidatePublicTemplateProfessionsEventHandler_WhenExpectedTotalNotMatch_Should_LogError(int expectedTotal)
    {
        var csvItemsCount = 500;

        var templateProfessions = new Faker<TempTemplateProfession>()
            .Generate(csvItemsCount)
            .ToArray();

        tempTemplateProfessionCsvRepository.Setup(x => x.GetTempTemplateProfessions(0, expectedTotal))
            .ReturnsAsync(templateProfessions);

        var evt = new EventMessage(EventType.ValidatePublicTemplateProfessions,
            new EventData<ValidatePublicTemplateProfessionsEventData>(
                new ValidatePublicTemplateProfessionsEventData(expectedTotal)
            ),
            null,
            Guid.NewGuid()
        );

        await sut.Handle(evt);

        logger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"Validation failed with csv items count {csvItemsCount} and expected count {expectedTotal}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ValidatePublicTemplateProfessionsEventHandler_WhenAllAreValidItems_Should_LogInfo()
    {
        var csvItemsCount = 500;

        var templateProfessions = new Faker<TempTemplateProfession>()
            .Generate(csvItemsCount)
            .ToArray();

        tempTemplateProfessionCsvRepository.Setup(x => x.GetTempTemplateProfessions(0, csvItemsCount))
            .ReturnsAsync(templateProfessions);

        var persons = templateProfessions
            .Select(tp => new PersonFaker().WithPersonId(tp.PersonId).Generate())
            .ToArray();

        personRepository.Setup(x => x.GetPersons(It.IsAny<Guid[]>()))
            .ReturnsAsync(persons);

        var templates = templateProfessions
            .Select(tp => new PublicTemplateFaker().RuleFor(x => x.Id, tp.PublicTemplateId).Generate())
            .ToArray();

        tempTemplateProfessionRepository.Setup(x => x.GetLivePublicTemplatesForBackup(It.IsAny<Guid[]>()))
            .ReturnsAsync((Guid[] ids) =>
            {
                return templates.Where(t => ids.Contains(t.Id))
                    .Select(x => (x.Id, x.Professions))
                    .ToList();
            });

        var evt = new EventMessage(EventType.ValidatePublicTemplateProfessions,
            new EventData<ValidatePublicTemplateProfessionsEventData>(
                new ValidatePublicTemplateProfessionsEventData(csvItemsCount)
            ),
            null,
            Guid.NewGuid()
        );

        await sut.Handle(evt);

        logger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"Validation success with valid items count {csvItemsCount} and expected count {csvItemsCount}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ValidatePublicTemplateProfessionsEventHandler_WhenSomeAreInvalid_Should_LogInfo()
    {
        var csvItemsCount = 2;
        var validItemCount = 1;

        var templateProfessions = new Faker<TempTemplateProfession>()
            .RuleFor(x => x.PublicTemplateId, f => f.Random.Uuid())
            .RuleFor(x => x.PersonId, f => f.Random.Uuid())
            .Generate(csvItemsCount)
            .ToArray();

        tempTemplateProfessionCsvRepository.Setup(x => x.GetTempTemplateProfessions(0, csvItemsCount))
            .ReturnsAsync(templateProfessions);

        var persons = new PersonFaker()
            .WithPersonId(templateProfessions[0].PersonId)
            .Generate(1)
            .ToArray();

        personRepository.Setup(x => x.GetPersons(It.IsAny<Guid[]>()))
            .ReturnsAsync(persons);

        var templates = new PublicTemplateFaker()
            .RuleFor(x => x.Id, templateProfessions[0].PublicTemplateId)
            .Generate(1)
            .ToArray();

        tempTemplateProfessionRepository.Setup(x => x.GetLivePublicTemplatesForBackup(It.IsAny<Guid[]>()))
            .ReturnsAsync((Guid[] ids) =>
            {
                return templates.Where(t => ids.Contains(t.Id))
                    .Select(x => (x.Id, x.Professions))
                    .ToList();
            });

        var evt = new EventMessage(EventType.ValidatePublicTemplateProfessions,
            new EventData<ValidatePublicTemplateProfessionsEventData>(
                new ValidatePublicTemplateProfessionsEventData(csvItemsCount)
            ),
            null,
            Guid.NewGuid()
        );

        await sut.Handle(evt);

        logger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"Validation failed with valid items count {validItemCount} and expected count {csvItemsCount}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);

        logger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"Author not found: {templateProfessions[1].PersonId} at index 1")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);

        logger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"Public template not found: {templateProfessions[1].PublicTemplateId} at index 1")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Theory]
    [InlineData(4285)] // Real CSV item count
    public async Task ValidatePublicTemplateProfessionsEventHandler_UseRealCsv_Success(int expectedTotalCount)
    {
        tempTemplateProfessionCsvRepository.UseRealImplementation();

        personRepository.Setup(x => x.GetPersons(It.IsAny<Guid[]>()))
            .ReturnsAsync((Guid[] personIds) =>
            {
                var persons = personIds
                    .Select(x => new PersonFaker().WithPersonId(x).Generate())
                    .ToArray();

                return persons;
            });

        tempTemplateProfessionRepository.Setup(x => x.GetLivePublicTemplatesForBackup(It.IsAny<Guid[]>()))
            .ReturnsAsync((Guid[] templateIds) =>
            {
                var templates = templateIds
                    .Where(t => templateIds.Contains(t))
                    .Select(x => new PublicTemplateFaker().RuleFor(x => x.Id, x).Generate())
                    .Select(x => (x.Id, x.Professions))
                    .ToList();

                return templates;
            });

        var evt = new EventMessage(EventType.ValidatePublicTemplateProfessions,
            new EventData<ValidatePublicTemplateProfessionsEventData>(
                new ValidatePublicTemplateProfessionsEventData(expectedTotalCount)
            ),
            null,
            Guid.NewGuid()
        );

        await sut.Handle(evt);

        logger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"Validation success with valid items count {expectedTotalCount} and expected count {expectedTotalCount}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);
    }

    [Theory]
    [InlineData(4285)] // Real CSV item count
    public async Task ValidatePublicTemplateProfessionsEventHandler_UseRealCsv_And_DoBackup_Success(int expectedTotalCount)
    {
        tempTemplateProfessionCsvRepository.UseRealImplementation();

        personRepository.Setup(x => x.GetPersons(It.IsAny<Guid[]>()))
            .ReturnsAsync((Guid[] personIds) =>
            {
                var persons = personIds
                    .Select(x => new PersonFaker().WithPersonId(x).Generate())
                    .ToArray();

                return persons;
            });

        var publicTemplates = new List<(Guid PublicTemplateId, string[] Professions)>();

        tempTemplateProfessionRepository.Setup(x => x.GetLivePublicTemplatesForBackup(It.IsAny<Guid[]>()))
            .ReturnsAsync((Guid[] templateIds) =>
            {
                var templates = templateIds
                    .Select(x => new PublicTemplateFaker().RuleFor(x => x.Id, x).Generate())
                    .Select(x => (x.Id, x.Professions))
                    .ToList();

                publicTemplates.AddRange(templates);

                return templates;
            });

        var evt = new EventMessage(EventType.ValidatePublicTemplateProfessions,
            new EventData<ValidatePublicTemplateProfessionsEventData>(
                new ValidatePublicTemplateProfessionsEventData(expectedTotalCount, true)
            ),
            null,
            Guid.NewGuid()
        );

        await sut.Handle(evt);

        logger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"Validation success with valid items count {expectedTotalCount} and expected count {expectedTotalCount}")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()),
            Times.Once);

        var templateIds = publicTemplates.Select(x => x.PublicTemplateId).ToArray();

        // Assert to verify that the temp table is purged before backing up
        tempTemplateProfessionRepository.Verify(x => x.PurgeTempPublicTemplateProfessions(templateIds), Times.Once);

        // Assert to verify that the public templates are backed up
        publicTemplates.ForEach(item =>
        {
            tempTemplateProfessionRepository.Verify(x => x.CreateTempPublicTemplateProfessions(item.PublicTemplateId, item.Professions), Times.Once);
        });
    }
}
