using carepatron.core.Application.Booking.Models;
using carepatron.core.Application.Booking.Service;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Insurance.Services;
using carepatron.core.Application.Schema.Services;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Common;
using carepatron.core.Constants;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.Localisation.SharedMessages;
using carepatron.core.Repositories.Billables;
using carepatron.core.Repositories.Booking;
using carepatron.core.Repositories.Item;
using carepatron.core.Repositories.Staff;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Repositories.TaxRates;
using carepatron.core.Services;
using Microsoft.Extensions.Localization;
using IContactService = carepatron.core.Application.Contacts.Abstractions.IContactService;

namespace tests.core.Application.Invoices.Commands;

[Trait(nameof(CodeOwner), CodeOwner.TasksAndScheduling)]
public class BookingServiceTests
{
    private readonly Mock<IStaffScheduleRepository> staffScheduleRepositoryMock = new();
    private readonly Mock<IItemRepository> itemRepositoryMock = new();
    private readonly Mock<IConnectedCalendarService> connectedCalendarServiceMock = new();
    private readonly Mock<ITaskRepository> taskRepositoryMock = new();
    private readonly Mock<IProviderRepository> providerRepositoryMock = new();
    private readonly Mock<IPersonRepository> personRepositoryMock = new();
    private readonly Mock<IContactRepository> contactRepositoryMock = new();
    private readonly Mock<IConnectedAppRepository> connectedAppRepositoryMock = new();
    private readonly Mock<IBookingRepository> bookingRepositoryMock = new();
    private readonly Mock<ICallService> callServiceMock = new();
    private readonly Mock<IStaffServicesRepository> staffServicesRepositoryMock = new();
    private readonly Mock<IDateTimeProvider> dateTimeProviderMock = new();
    private readonly Mock<IIntegrationEventPublisher> eventPublisherMock = new();
    private readonly Mock<ISchemaService> schemaServiceMock = new();
    private readonly Mock<IBillableRepository> billableRepositoryMock = new();
    private readonly Mock<IStringLocalizer<SharedMessages>> localizerMock = new();
    private readonly Mock<ITaxRateRepository> taxRateRepositoryMock = new();
    private readonly Mock<IInsuranceService> insuranceServiceMock = new();

    private BookingService sut;

    public BookingServiceTests()
    {
        sut = new BookingService(
            staffScheduleRepositoryMock.Object,
            itemRepositoryMock.Object,
            connectedCalendarServiceMock.Object,
            taskRepositoryMock.Object,
            providerRepositoryMock.Object,
            personRepositoryMock.Object,
            contactRepositoryMock.Object,
            connectedAppRepositoryMock.Object,
            bookingRepositoryMock.Object,
            callServiceMock.Object,
            staffServicesRepositoryMock.Object,
            dateTimeProviderMock.Object,
            eventPublisherMock.Object,
            schemaServiceMock.Object,
            billableRepositoryMock.Object,
            taxRateRepositoryMock.Object,
            localizerMock.Object,
            Mock.Of<IContactService>(),
            Mock.Of<ITaskService>(),
            insuranceServiceMock.Object,
            Mock.Of<ICalendarSubscriptionRepository>(),
            Mock.Of<ITaskStatusService>()
        );
    }

    [Theory]
    // easy matches and typos
    [InlineData("Alex", "alex", null, true)]
    [InlineData("Alex", "alexandra", null, true)]
    [InlineData("Beth", "Elizabeth", null, true)]
    [InlineData("Gus", "Angus", null, true)]
    [InlineData("Ben", "Benjamin", null, true)]
    [InlineData("  Chris  ", "Christian", null, true)]
    [InlineData("Chris", " Christopher ", null, true)]
    [InlineData("Will", "William", null, true)]
    [InlineData("Charlie", "Charles", null, true)]
    [InlineData("Robbie", "Robby", null, true)]
    [InlineData("Ash", "Ashley", null, true)]
    [InlineData("Johnathon", "Jonathan", null, true)]
    [InlineData("Stephanie", "Steph", null, true)]
    [InlineData("Zach", "Zachary", null, true)]
    
    // names that probably should match - but dont with current threshold. These require preferred name matching
    [InlineData("Jimmy", "James", null, false)] 
    [InlineData("Timmy", "Timothy", null, false)]
    [InlineData("jules", "Julian", null, false)]
    [InlineData("Eddie", "Edward", null, false)]
    [InlineData("ellie", "Elizabeth", null, false)]
    [InlineData("libby", "Elizabeth", null, false)]
    [InlineData("Lizzie", "Elizabeth", null, false)]
    [InlineData("Penny", "Penelope", null, false)]
    [InlineData("Joe", "Joseph", null, false)]
    [InlineData("Robbie", "Robert", null, false)]
    [InlineData("Robby", "Robert", null, false)]
    [InlineData("Bob", "Robert", null, false)]
    [InlineData("Jim", "James", null, false)]
    [InlineData("Richard", "Dick", null, false)] 

    //matches on preferred name
    [InlineData("ellie", "", "elly", true)]
    [InlineData("Robby", "", "Robbie", true)]
    [InlineData("Eddie", "", "ed", true)]

    // false matches - probably shouldnt match, but do.
    [InlineData("Rick", "Richard", null, true)]
    [InlineData("John", "Joan", null, true)]
    [InlineData("Jean", "Jenna", null, true)]
    [InlineData("Gabrielle", "Jill", null, true)]

    // shouldn't match
    [InlineData("Sammy", "Amy", null, false)]
    [InlineData("Adalyn", "Gracelyn", null, false)]
    [InlineData("Liz", "Elijah", null, false)]

    public async Task GetBookingContact_FuzzyMatches(string bookingFirstName, string contactFirstName, string contactPreferredName, bool expectedMatch)
    {
        var contact = new ContactFaker()
            .RuleFor(x => x.FirstName, contactFirstName)
            .RuleFor(x => x.PreferredName, contactPreferredName)
            .Generate();

        contactRepositoryMock.Setup(x => x.GetByEmail(contact.Email, contact.ProviderId)).ReturnsAsync([contact]);

        var bookingContactDetails = new BookingContactDetail
        {
            Email = contact.Email,
            FirstName = bookingFirstName
        };

        var result = await sut.GetBookingContact(bookingContactDetails, contact.ProviderId);
        if (expectedMatch)
        {
            result.Should().BeEquivalentTo(contact);
        }
        else
        {
            result.Should().BeNull();
        }
    }

    [Fact]
    public async Task GetBookingContact_MultipleMatches_ShouldReturnBestMatch()
    {
        var contactFaker = new ContactFaker();

        var contact1 = contactFaker
            .RuleFor(x => x.FirstName, "Elizabeth")
            .RuleFor(x => x.PreferredName, string.Empty)
            .Generate();

        var contact2 = contactFaker
            .RuleFor(x => x.ProviderId, contact1.ProviderId)
            .RuleFor(x => x.Email, contact1.Email)
            .RuleFor(x => x.FirstName, "Beth")
            .RuleFor(x => x.PreferredName, string.Empty)
            .Generate();

        var contact3 = contactFaker
            .RuleFor(x => x.ProviderId, contact1.ProviderId)
            .RuleFor(x => x.Email, contact1.Email)
            .RuleFor(x => x.FirstName, "Lizzie")
            .RuleFor(x => x.PreferredName, "beth ")
            .Generate();

        contactRepositoryMock.Setup(x => x.GetByEmail(contact1.Email, contact1.ProviderId))
            .ReturnsAsync([contact1, contact2, contact3]);

        var bookingContactDetails = new BookingContactDetail
        {
            Email = contact1.Email,
            FirstName = "beth "
        };

        var result = await sut.GetBookingContact(bookingContactDetails, contact1.ProviderId);

        result.Should().BeEquivalentTo(contact2);
    }
}