﻿using carepatron.core.Abstractions;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Application.Tasks.Commands;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Tasks.Services;
using carepatron.core.Events.Events;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.Models.Call;
using carepatron.core.Models.Media;
using carepatron.core.Models.Permissions;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Repositories.Call;
using carepatron.core.Repositories.SQS;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Services;
using tests.common.Builders.Models;
using tests.core.Builders;

namespace tests.core.Application.Tasks.Commands
{
    public class UpdateTaskCommandHandlerTests
    {
        private readonly UpdateTaskCommandHandler updateTaskCommandHandler;

        private readonly <PERSON>ck<IIdentityContext> identityContext;
        private readonly <PERSON>ck<ITaskRepository> taskRepository;
        private readonly <PERSON>ck<IContactRepository> contactRepository;
        private readonly <PERSON>ck<ICallRepository> callRepository;
        private readonly Mock<ISqsRepository> sqsRepository;
        private readonly Mock<ICallProvider> callProvider;
        private readonly Mock<ICallProvider> zoomCallProvider;
        private readonly Mock<IUnitOfWork> unitOfWork;
        private readonly Mock<ITaskService> taskService;
        private readonly Mock<ITaskStatusRepository> taskStatusRepository;

        private CallProviderFactory callProviderFactory;
        private CancellationTokenSource tokenSource;
        
        private Guid personId;
        private Guid providerId;

        public UpdateTaskCommandHandlerTests()
        {
            personId = Guid.NewGuid();
            providerId = Guid.NewGuid();

            identityContext = new Mock<IIdentityContext>();
            identityContext.Setup(x => x.PersonId).Returns(personId);
            identityContext.Setup(x => x.ProviderPermissions).Returns(new ProviderPermissions
            {
                SchedulingEdit = SchedulingPermission.Everything,
                ProviderId = providerId,
                PersonId = personId
            });

            taskRepository = new Mock<ITaskRepository>();
            contactRepository = new Mock<IContactRepository>();
            unitOfWork = new Mock<IUnitOfWork>();
            callRepository = new Mock<ICallRepository>();
            sqsRepository = new Mock<ISqsRepository>();
            callProvider = new Mock<ICallProvider>();
            zoomCallProvider = new Mock<ICallProvider>();
            callProviderFactory = (provider) => provider == CallProvider.Zoom
                ? zoomCallProvider.Object
                : callProvider.Object;
            taskService = new Mock<ITaskService>();

            tokenSource = new CancellationTokenSource();

            taskStatusRepository = new Mock<ITaskStatusRepository>();
            taskStatusRepository.Setup(x => x.Get(It.IsAny<Guid>(), It.IsAny<TaskAttendeeGroupStatus[]>()))
                .ReturnsAsync([]);

            var taskStatusService = new TaskStatusService(taskStatusRepository.Object);

            updateTaskCommandHandler = new UpdateTaskCommandHandler(taskRepository.Object,
                callRepository.Object,
                contactRepository.Object,
                new CallService(callRepository.Object, taskRepository.Object, callProviderFactory),
                unitOfWork.Object,
                Mock.Of<IIntegrationEventPublisher>(),
                taskService.Object,
                taskStatusService);
        }

        [Fact]
        public async Task UpdatesTask_With_No_Call()
        {
            // arrange
            var taskId = Guid.NewGuid();

            var oldTask = TaskModelBuilder.Any().WithId(taskId).Create();
            
            var command = UpdateTaskCommandBuilder.Any()
                .WithId(taskId)
                .WithIdentityContext(identityContext.Object)
                .Create();

            SaveTaskModel savedTask = null;

            taskRepository.Setup(x => x.Get(taskId)).ReturnsAsync(oldTask);

            taskRepository.Setup(x => x.Save(It.IsAny<SaveTaskModel>()))
                .Callback((SaveTaskModel model) => savedTask = model);

            sqsRepository.Setup(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>())).Returns(Task.CompletedTask);

            // act
            var result = await updateTaskCommandHandler.Handle(command, tokenSource.Token);

            // assert
            savedTask.Should().NotBeNull()
                .And.BeEquivalentTo(new
                {
                    command.Id,
                    oldTask.Type,
                    oldTask.ProviderId,
                    command.ParentId,
                    command.Title,
                    command.Description,
                    command.Location,
                    oldTask.TimeZone,
                    command.StartDate,
                    command.EndDate,
                    command.AllDay,
                    command.RRule,
                    command.ExDate,
                    command.OccurrenceEndDate,
                    ContactReminderConfigs = new ContactReminderConfig[0],
                    Files = new File[0],
                    Contacts =
                        (SimpleTaskContact[])
                        [
                            new ()
                            {
                                AboutContact = true,
                                AttendeeStatusId = nameof(TaskContactStatus.Confirmed),
                                ContactId = command.ContactIds[0],
                                TaskContactStatus = TaskContactStatus.Confirmed,
                                TaskId = command.Id,
                            }
                        ],
                    command.StaffIds,
                    command.Items,
                    oldTask.CreatedByPersonId,
                    LastUpdatedByPersonId = command.IdentityContext.PersonId,
                }, opts => opts.ExcludingMissingMembers());

            unitOfWork.Verify(_ => _.SaveUnitOfWork(It.IsAny<CancellationToken>()));
        }

        [Fact]
        public async Task UpdatesTask_With_Same_Call_Info()
        {
            // arrange
            var taskId = Guid.NewGuid();
            var callId = Guid.NewGuid();

            var call = CallModelBuilder.Any().WithId(callId).WithCallProvider(CallProvider.AWS).Create();

            var oldTask = TaskModelBuilder.Any().WithId(taskId).WithCallId(callId).Create();
            SaveTaskModel savedTask = null;

            var command = UpdateTaskCommandBuilder.Any()
                .WithId(taskId)
                .WithIdentityContext(identityContext.Object)
                .WithHasCall(true)
                .WithCallProvider(call.CallProvider)
                .WithMediaRegion(call.MediaRegion)
                .Create();

            taskRepository.Setup(x => x.Get(taskId)).ReturnsAsync(oldTask);
            taskRepository.Setup(x => x.Save(It.IsAny<SaveTaskModel>()))
                .Callback((SaveTaskModel model) => savedTask = model);

            callRepository.Setup(x => x.Get(callId)).ReturnsAsync(call);
            callProvider.Setup(x => x.UpdateCall(It.Is<SaveTaskModel>(x => x.Id == taskId), call, call.MediaRegion)).ReturnsAsync(call);

            sqsRepository.Setup(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>())).Returns(Task.CompletedTask);

            // act
            var result = await updateTaskCommandHandler.Handle(command, tokenSource.Token);

            // assert
            savedTask.Should().NotBeNull()
                .And.BeEquivalentTo(new
                {
                    command.Id,
                    oldTask.CallId
                }, opts => opts.ExcludingMissingMembers());
        }

        [Fact]
        public async Task UpdateTask_ShouldRecreateCall_WhenUpdateFails()
        {
            // arrange
            var taskId = Guid.NewGuid();
            var callId = Guid.NewGuid();

            var call = CallModelBuilder.Any().WithId(callId).WithCallProvider(CallProvider.AWS).Create();
            var newCall = CallModelBuilder.Any().WithCallProvider(CallProvider.AWS).Create();

            var oldTask = TaskModelBuilder.Any().WithId(taskId).WithCallId(callId).Create();
            SaveTaskModel savedTask = null;

            var command = UpdateTaskCommandBuilder.Any()
                .WithId(taskId)
                .WithIdentityContext(identityContext.Object)
                .WithHasCall(true)
                .WithCallProvider(call.CallProvider)
                .WithMediaRegion(call.MediaRegion)
                .Create();

            taskRepository.Setup(x => x.Get(taskId)).ReturnsAsync(oldTask);
            taskRepository.Setup(x => x.GetByCallId(callId)).ReturnsAsync(new List<SimpleTaskModel>());
            taskRepository.Setup(x => x.Save(It.IsAny<SaveTaskModel>()))
                .Callback((SaveTaskModel model) => savedTask = model);

            callRepository.Setup(x => x.Get(callId)).ReturnsAsync(call);
            callProvider.Setup(x => x.UpdateCall(It.Is<SaveTaskModel>(x => x.Id == taskId), call, call.MediaRegion)).ReturnsAsync((Call)null);
            callProvider.Setup(x => x.CreateCall(It.Is<SaveTaskModel>(x => x.Id == taskId), call.MediaRegion)).ReturnsAsync(newCall);
            callRepository.Setup(x => x.Create(It.Is<Call>(x => x.Id == newCall.Id))).ReturnsAsync(newCall);

            sqsRepository.Setup(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>())).Returns(Task.CompletedTask);

            // act
            var result = await updateTaskCommandHandler.Handle(command, tokenSource.Token);

            // assert
            callProvider.Verify(x => x.DeleteCall(call), Times.Once);
            callProvider.Verify(x => x.CreateCall(It.Is<SaveTaskModel>(x => x.Id == taskId), command.MediaRegion), Times.Once);
            callRepository.Verify(x => x.Delete(callId), Times.Once);
            callRepository.Verify(x => x.Create(newCall), Times.Once);
            savedTask.Should().NotBeNull()
                .And.BeEquivalentTo(new
                {
                    command.Id,
                    CallId = newCall.Id
                }, opts => opts.ExcludingMissingMembers());
        }

        [Fact]
        public async Task UpdateTask_ShouldCreateANewCall_WhenOldTaskHasNoCall_AndNewTaskHasACall()
        {
            // arrange
            var taskId = Guid.NewGuid();
            var callId = Guid.NewGuid();

            var call = CallModelBuilder.Any().WithId(callId).WithCallProvider(CallProvider.AWS).Create();

            var oldTask = TaskModelBuilder.Any().WithId(taskId).Create();
            SaveTaskModel savedTask = null;

            var command = UpdateTaskCommandBuilder.Any()
                .WithId(taskId)
                .WithIdentityContext(identityContext.Object)
                .WithHasCall(true)
                .WithCallProvider(call.CallProvider)
                .WithMediaRegion(call.MediaRegion)
                .Create();

            taskRepository.Setup(x => x.Get(taskId)).ReturnsAsync(oldTask);
            taskRepository.Setup(x => x.Save(It.IsAny<SaveTaskModel>()))
                .Callback((SaveTaskModel model) => savedTask = model);

            callProvider.Setup(x => x.CreateCall(It.Is<SaveTaskModel>(x => x.Id == taskId), call.MediaRegion)).ReturnsAsync(call);
            callRepository.Setup(x => x.Create(call)).ReturnsAsync(call);

            sqsRepository.Setup(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>())).Returns(Task.CompletedTask);

            // act
            var result = await updateTaskCommandHandler.Handle(command, tokenSource.Token);

            // assert
            result.Should().NotBeNull();

            callProvider.Verify(x => x.CreateCall(It.Is<SaveTaskModel>(x => x.CallId == call.Id), call.MediaRegion), Times.Once);
            callRepository.Verify(x => x.Create(call), Times.Once);
            savedTask.Should().NotBeNull()
               .And.BeEquivalentTo(new
               {
                   command.Id,
                   CallId = call.Id
               }, opts => opts.ExcludingMissingMembers());
        }

        [Fact]
        public async Task UpdateTask_ShouldRemoveCall_WhenNewTaskHasNoCall()
        {
            // arrange
            var taskId = Guid.NewGuid();
            var callId = Guid.NewGuid();

            var call = CallModelBuilder.Any().WithId(callId).WithCallProvider(CallProvider.AWS).Create();

            var oldTask = TaskModelBuilder.Any().WithId(taskId).WithCallId(call.Id).Create();
            SaveTaskModel savedTask = null;

            var command = UpdateTaskCommandBuilder.Any()
                .WithId(taskId)
                .WithIdentityContext(identityContext.Object)
                .WithHasCall(false)
                .Create();

            taskRepository.Setup(x => x.Get(taskId)).ReturnsAsync(oldTask);
            taskRepository.Setup(x => x.Save(It.IsAny<SaveTaskModel>()))
                .Callback((SaveTaskModel model) => savedTask = model);

            taskRepository.Setup(x => x.GetByCallId(callId)).ReturnsAsync(new List<SimpleTaskModel>());

            callRepository.Setup(x => x.Get(callId)).ReturnsAsync(call);

            sqsRepository.Setup(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>())).Returns(Task.CompletedTask);

            // act
            var result = await updateTaskCommandHandler.Handle(command, tokenSource.Token);

            // assert
            result.Should().NotBeNull();

            callRepository.Verify(x => x.Delete(callId), Times.Once);
            savedTask.Should().NotBeNull()
               .And.BeEquivalentTo(new
               {
                   command.Id,
                   CallId = (Guid?)null
               }, opts => opts.ExcludingMissingMembers());
        }

        [Fact]
        public async Task UpdateTask_ShouldRemoveAndCreateNewCall_WhenTasksHaveDifferentCallProviders()
        {
            // arrange
            var taskId = Guid.NewGuid();
            var callId = Guid.NewGuid();
            var personId = Guid.NewGuid();

            var person = PersonBuilder.Any().WithId(personId).Create();

            var call = CallModelBuilder.Any().WithId(callId).WithCallProvider(CallProvider.Zoom).Create();
            var newCall = CallModelBuilder.Any().WithCallProvider(CallProvider.AWS).Create();

            var oldTask = TaskModelBuilder.Any().WithId(taskId).WithCreatedByPersonId(personId).WithCallId(callId).Create();
            SaveTaskModel savedTask = null;

            var command = UpdateTaskCommandBuilder.Any()
                .WithId(taskId)
                .WithIdentityContext(identityContext.Object)
                .WithHasCall(true)
                .WithCallProvider(CallProvider.AWS)
                .WithStaffIds(personId)
                .Create();

            taskRepository.Setup(x => x.Get(taskId)).ReturnsAsync(oldTask);
            taskRepository.Setup(x => x.GetByCallId(callId)).ReturnsAsync(new List<SimpleTaskModel>());
            taskRepository.Setup(x => x.Save(It.IsAny<SaveTaskModel>()))
                .Callback((SaveTaskModel model) => savedTask = model);

            callRepository.Setup(x => x.Get(callId)).ReturnsAsync(call);
            callRepository.Setup(x => x.Create(It.Is<Call>(c => c.Id == newCall.Id))).ReturnsAsync(newCall);

            identityContext.Setup(x => x.PersonId).Returns(personId);

            callProvider.Setup(x => x.CreateCall(It.Is<SaveTaskModel>(x => x.Id == taskId), command.MediaRegion)).ReturnsAsync(newCall);
            zoomCallProvider.Setup(x => x.DeleteCall(It.Is<Call>(c => c.Id == callId)));

            sqsRepository.Setup(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>())).Returns(Task.CompletedTask);

            // act
            var result = await updateTaskCommandHandler.Handle(command, tokenSource.Token);

            // assert
            result.Should().NotBeNull();

            // assert
            zoomCallProvider.Verify(x => x.DeleteCall(call), Times.Once);
            callProvider.Verify(x => x.CreateCall(It.Is<SaveTaskModel>(x => x.Id == taskId), command.MediaRegion), Times.Once);
            callRepository.Verify(x => x.Delete(callId), Times.Once);
            callRepository.Verify(x => x.Create(newCall), Times.Once);
            savedTask.Should().NotBeNull()
               .And.BeEquivalentTo(new
               {
                   command.Id,
                   CallId = newCall.Id
               }, opts => opts.ExcludingMissingMembers());
        }

        [Fact]
        public async Task UpdateTask_ShouldNotDeleteCall_WhenCallIsUsedByDependentTasks()
        {
            // arrange
            var taskId = Guid.NewGuid();
            var callId = Guid.NewGuid();
            var personId = Guid.NewGuid();

            var person = PersonBuilder.Any().WithId(personId).Create();

            var call = CallModelBuilder.Any().WithId(callId).WithCallProvider(CallProvider.Zoom).Create();

            var task = TaskModelBuilder.Any().WithId(taskId).WithCallId(callId).Create();
            SaveTaskModel savedTask = null;

            var command = UpdateTaskCommandBuilder.Any()
                .WithId(taskId)
                .WithIdentityContext(identityContext.Object)
                .WithHasCall(false)
                .WithStaffIds(personId)
                .Create();

            taskRepository.Setup(x => x.Get(taskId)).ReturnsAsync(task);
            taskRepository.Setup(x => x.GetByCallId(callId)).ReturnsAsync(new[] { new SimpleTaskModel() });
            taskRepository.Setup(x => x.Save(It.IsAny<SaveTaskModel>()))
                .Callback((SaveTaskModel model) => savedTask = model);

            callRepository.Setup(x => x.Get(callId)).ReturnsAsync(call);

            identityContext.Setup(x => x.PersonId).Returns(personId);

            sqsRepository.Setup(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>())).Returns(Task.CompletedTask);

            // act
            var result = await updateTaskCommandHandler.Handle(command, tokenSource.Token);

            // assert
            result.Should().NotBeNull();

            // assert
            zoomCallProvider.Verify(x => x.DeleteCall(call), Times.Never);
            callRepository.Verify(x => x.Delete(callId), Times.Never);
            taskRepository.Verify(x => x.Save(It.Is<SaveTaskModel>(x => x.Id == task.Id)), Times.Once);
            savedTask.Should().NotBeNull()
               .And.BeEquivalentTo(new
               {
                   command.Id,
                   CallId = (Guid?)null
               }, opts => opts.ExcludingMissingMembers());
        }
    }
}