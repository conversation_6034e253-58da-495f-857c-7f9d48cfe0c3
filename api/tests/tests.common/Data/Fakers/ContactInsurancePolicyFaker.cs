using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Schema.Models.Properties;
using carepatron.core.Models.Common;

namespace tests.common.Data.Fakers;

public class ContactInsurancePolicyFaker : AutoFaker<ContactInsurancePolicy>
{
    public ContactInsurancePolicyFaker(
        Guid providerId,
        Guid contactId,
        ProviderInsurancePayer payer = null
    )
    {
        var currentPayer = payer ?? new ProviderInsurancePayerFaker(providerId).Generate();
        RuleFor(x => x.Id, x => Guid.NewGuid());
        RuleFor(x => x.ProviderId, x => providerId);
        RuleFor(x => x.ContactId, x => contactId);
        RuleFor(
            x => x.InsuranceType,
            x => x.PickRandomWithout(InsuranceType.Unknown, InsuranceType.Other)
        );
        RuleFor(x => x.Status, InsurancePolicyStatus.Verified);
        RuleFor(
            x => x.PolicyHolder,
            x =>
                new InsurancePolicyHolder
                {
                    Type = x.PickRandom<InsurancePolicyHolderType>(),
                    Contact = new InsurancePolicyHolderContactFaker(contactId).Generate()
                }
        );
        RuleFor(x => x.Payer, x => currentPayer);
        RuleFor(x => x.MemberId, x => x.Random.Replace("###-####"));
        RuleFor(x => x.GroupId, x => x.Random.Replace("###-####"));
        RuleFor(x => x.PlanId, x => x.Random.Replace("###-####"));
        RuleFor(x => x.PolicyStartDate, x => x.Date.PastDateOnly());
        RuleFor(x => x.PolicyEndDate, x => x.Date.FutureDateOnly());
    }

    public class InsurancePolicyHolderContactFaker : AutoFaker<InsurancePolicyHolderContact>
    {
        public InsurancePolicyHolderContactFaker(Guid contactId)
        {
            RuleFor(x => x.Id, x => contactId);
            RuleFor(x => x.FirstName, x => x.Person.FirstName);
            RuleFor(x => x.LastName, x => x.Person.LastName);
            RuleFor(
                x => x.Address,
                x =>
                    new Address
                    {
                        StreetAddress = x.Address.StreetAddress(),
                        City = x.Address.City(),
                        State = x.Address.State(),
                        ZipCode = x.Address.ZipCode(),
                        Country = x.Address.Country()
                    }
            );
            RuleFor(x => x.PhoneNumber, x => x.Person.Phone);
            RuleFor(_ => _.Sex, default(OptionSetValue));
            RuleFor(x => x.BirthDate, x => x.Date.PastDateOnly());
        }
    }
}
