﻿using carepatron.core.Application.Billables.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Constants;
using carepatron.core.Models.Invoices;
using FluentAssertions.Extensions;
using Payment = carepatron.core.Application.Payments.Models.Payment;

namespace tests.common.Data.Fakers
{
    public class PaymentFaker : AutoFaker<Payment>
    {
        public PaymentFaker(Guid? providerId = null)
        {
            RuleFor(x => x.ProviderId, f => providerId ?? Guid.NewGuid());
            RuleFor(x => x.PaymentIntentId, (Guid?)null);
            RuleFor(x => x.Amount, f => f.Random.Decimal(1, 100m));
            RuleFor(x => x.PaymentDate, f => f.Date.Recent().AsUtc());
            RuleFor(x => x.PaymentProvider, PaymentProviders.Manual);
            RuleFor(x => x.Type, PaymentTypes.Cash);
            RuleFor(x => x.PayerName, f => f.Company.CompanyName());
            RuleFor(x => x.Reference, f => f.Random.AlphaNumeric(10));
            RuleFor(x => x.PayoutDateUtc, (DateTime?)null);
            RuleFor(x => x.PayoutStatus, (PayoutStatus?)null);
            RuleFor(x => x.PayoutAmount, (decimal?)null);
            RuleFor(x => x.PayoutCurrencyCode, (string)null);
            RuleFor(x => x.Allocations, (PaymentAllocation[])null);
            RuleFor(x => x.IsBillingV2, true);
            RuleFor(x => x.PayerType, PayerType.SelfPay);
            RuleFor(x => x.UnallocatedAmount, 0M);
        }

        public PaymentFaker WithPaymentIntent(PaymentIntent intent)
        {
            RuleFor(x => x.PaymentIntentId, intent.Id);
            RuleFor(x => x.ProviderId, intent.ProviderId);
            RuleFor(x => x.InvoiceId, intent.InvoiceId);
            RuleFor(x => x.ContactId, intent.ContactId);
            RuleFor(x => x.Amount, intent.Amount);
            RuleFor(x => x.ChargeAmount, intent.ChargeAmount);
            RuleFor(x => x.TransferAmount, intent.TransferAmount);
            RuleFor(x => x.Fee, intent.Fee);
            RuleFor(x => x.IsClientChargedFee, intent.IsClientChargedFee);
            RuleFor(x => x.CurrencyCode, intent.CurrencyCode);
            RuleFor(x => x.Allocations, (PaymentAllocation[])null);
            return this;
        }

        public PaymentFaker WithPayout()
        {
            RuleFor(x => x.PayoutDateUtc, f => f.Date.Recent().AsUtc());
            RuleFor(x => x.PayoutStatus, PayoutStatus.Paid);
            RuleFor(x => x.PayoutAmount, (f, c) => c.Amount);
            RuleFor(x => x.PayoutCurrencyCode, CurrencyCodes.USD);

            return this;
        }
    };

    public class PaymentAllocationFaker : AutoFaker<PaymentAllocation>
    {
        public PaymentAllocationFaker(Guid? providerId = null, Guid? contactId = null)
        {
            RuleFor(x => x.Id, Guid.NewGuid());
            RuleFor(x => x.ProviderId, providerId ?? Guid.NewGuid());
            RuleFor(x => x.ContactId, contactId ?? Guid.NewGuid());
            RuleFor(x => x.Amount, f => f.Random.Decimal(1, 100m));
            RuleFor(x => x.ClaimLineId, (Guid?)null);
        }

        public PaymentAllocationFaker WithClaimServiceLine(ClaimServiceLine claimServiceLine)
        {
            RuleFor(x => x.InvoiceLineItemId, (Guid?)null);
            RuleFor(x => x.ClaimLineId, claimServiceLine.Id);
            return this;
        }
        
        public PaymentAllocationFaker WithPayment(Payment payment)
        {
            RuleFor(x => x.PaymentId, payment.Id);
            return this;
        }
        
        public PaymentAllocationFaker WithAmount(decimal amount)
        {
            RuleFor(x => x.Amount, amount);
            return this;
        }
        
        public PaymentAllocationFaker WithBillableItem(BillableItem billableItem)
        {
            RuleFor(x => x.BillableItemId, billableItem.Id);
            return this;
        }
    }
}
