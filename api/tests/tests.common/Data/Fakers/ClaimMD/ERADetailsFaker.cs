using ClaimMD.Module.Models.Http.Dtos;
using carepatron.core.Application.Insurance.Models;
using ClaimMD.Module.Models.Http.Requests;
using ClaimMD.Module.Models.Http.Responses;
using ClaimMD.Module.Utilities;
using tests.common.Data.Datasets;
using carepatron.core.Utilities;

namespace tests.common.Data.Fakers.ClaimMD;

public class ERADetailsRequestFaker : AutoFaker<ERADetailsRequest>
{
    public ERADetailsRequestFaker()
    {
        RuleFor(e => e.EraId, f => f.Random.Int(1).ToString());
    }
}
public class ERADetailsResponseFaker : AutoFaker<ERADetailsResponse>
{
    public ERADetailsResponseFaker(ERAClaimDto[] claims = null)
    {
        claims ??= [new ERAClaimDtoFaker().Generate()];
        RuleFor(e => e.PaidAmount, ClaimMdMapper.MapToDecimalString(claims.Sum(c => Convert.ToDecimal(c.TotalPaid))));
        RuleFor(e => e.ProviderAccount, f => f.Finance.Account());
        RuleFor(e => e.ProviderRouting, f => f.Finance.RoutingNumber());
        RuleFor(e => e.ProviderName, f => f.Company.CompanyName());
        RuleFor(e => e.ProviderTaxId, f => f.Random.Replace("###-##-####"));
        RuleFor(e => e.ProviderNpi, f => f.CarePatron().NPINumber());
        RuleFor(e => e.ProviderAddr1, f => f.Address.StreetAddress());
        RuleFor(e => e.ProviderCity, f => f.Address.City());
        RuleFor(e => e.ProviderState, f => f.Address.State());
        RuleFor(e => e.ProviderZip, f => f.Address.ZipCode());
        RuleFor(e => e.PayerAccount, f => f.Finance.Account());
        RuleFor(e => e.PayerId, f => f.Random.AlphaNumeric(10));
        RuleFor(e => e.PayerName, f => f.Company.CompanyName());
        RuleFor(e => e.PayerCompanyId, f => f.Random.Int(1000, 9999).ToString());
        RuleFor(e => e.PayerRouting, f => f.Finance.RoutingNumber());
        RuleFor(e => e.PayerAddr1, f => f.Address.StreetAddress());
        RuleFor(e => e.PayerCity, f => f.Address.City());
        RuleFor(e => e.PayerState, f => f.Address.State());
        RuleFor(e => e.PayerZip, f => f.Address.ZipCode());
        RuleFor(e => e.Claims, f => [.. claims]);
        RuleFor(e => e.CheckNumber, f => f.Finance.Account());
        RuleFor(e => e.EraId, f => f.Random.Int(1));
        RuleFor(e => e.PaidDate, f => f.Date.Past(1).ToString("yyyy-MM-dd"));
        RuleFor(e => e.PaymentMethod, f => f.PickRandom("ACH", "Check", "Wire"));
        RuleFor(e => e.PaymentFormat, f => f.PickRandom("XML", "PDF", "EDI"));
        RuleFor(e => e.EftSenderId, f => f.Random.AlphaNumeric(12));
        RuleFor(e => e.Errors, f => null);
    }

    public ERADetailsResponseFaker FromClaim(params InsuranceClaimUSProfessional[] claims)
    {
        var firstClaim = claims.First();

        RuleFor(x => x.ProviderNpi, firstClaim.BillingDetail.NationalProviderId);
        RuleFor(x => x.ProviderTaxId, firstClaim.BillingDetail.TaxNumber);
        RuleFor(x => x.Claims, claims.Select(claim => new ERAClaimDtoFaker()
            .RuleFor(x => x.PCN, claim.ClientControlNumber)
            .Generate())
            .ToList());

        return this;
    }
}

public class ERADetailsPDFResponseFaker : AutoFaker<ERADetailsPDFResponse>
{
    public ERADetailsPDFResponseFaker()
    {
        RuleFor(e => e.ProviderName, f => f.Company.CompanyName());
        RuleFor(e => e.ProviderNpi, f => f.CarePatron().NPINumber());
        RuleFor(e => e.PayerId, f => f.Random.AlphaNumeric(10));
        RuleFor(e => e.PayerName, f => f.Company.CompanyName());
        RuleFor(e => e.CheckNumber, f => f.Finance.Account());
        RuleFor(e => e.EraId, f => f.Random.Int(1));
        RuleFor(e => e.PaidDate, f => f.Date.Past(1).ToString("yyyy-MM-dd"));
        RuleFor(e => e.PaidAmount, f => f.Finance.Amount(10, 10000).ToString("F2"));
        RuleFor(e => e.Errors, f => null);
        RuleFor(e => e.Data, f => [Convert.ToBase64String(f.Random.Bytes(1024))]);
    }
}
