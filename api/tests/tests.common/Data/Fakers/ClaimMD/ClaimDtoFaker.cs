using carepatron.core.Application.Insurance.Models;
using carepatron.core.Extensions;
using tests.common.Data.Datasets;
using ClaimMD.Module;
using ClaimMD.Module.Utilities;
using static ClaimMD.Module.Constants;
using ClaimMD.Module.Models.Http.Dtos;
using carepatron.core.Utilities;

namespace tests.common.Data.Fakers.ClaimMD;

public class ClaimDtoFaker : AutoFaker<ClaimDto>
{
    public ClaimDtoFaker()
    {
        RuleFor(c => c.Status, f => f.Pick<PERSON>andom("Pending", "Approved", "Denied"));
        RuleFor(c => c.ClaimMdId, f => f.Random.Guid().ToString());
        RuleFor(c => c.ClaimId, f => f.Random.Guid().ToString());
        RuleFor(c => c.RemoteClaimId, Base36GuidEncoder.Encode(Guid.NewGuid()));
        RuleFor(c => c.BatchId, f => f.Random.AlphaNumeric(10));
        RuleFor(c => c.SenderName, f => f.Company.CompanyName());
        RuleFor(c => c.Messages, f => [new ClaimMessageDtoFaker().Generate()]);
        RuleFor(c => c.FileId, f => f.Random.Guid().ToString());
        RuleFor(c => c.InsuranceNumber, f => f.Finance.Account());
        RuleFor(c => c.BillTaxId, f => f.Random.AlphaNumeric(10));
        RuleFor(c => c.BillingNpi, f => f.CarePatron().NPINumber());
        RuleFor(c => c.PCN, f => f.Random.Long(1).ToString());
        RuleFor(c => c.TotalCharge, f => f.Finance.Amount(100, 10000).ToString("F2"));
        RuleFor(c => c.PayerId, f => f.Random.AlphaNumeric(8));
        RuleFor(c => c.FDOS, f => f.Date.Past(1).ToShortDateString());
        RuleFor(c => c.FileName, f => f.System.FileName());
        RuleFor(c => c.SenderId, f => f.Random.AlphaNumeric(10));
        RuleFor(c => c.SenderICN, f => f.Random.AlphaNumeric(12));
    }
}

public class ChargeAdjustmentDtoFaker : AutoFaker<ChargeAdjustmentDto>
{
    public ChargeAdjustmentDtoFaker()
    {
        RuleFor(c => c.Amount, f => f.Finance.Amount(0, 1000).ToString("F2"));
        RuleFor(c => c.Code, f => f.Random.AlphaNumeric(3));
        RuleFor(c => c.Group, f => f.PickRandom(
            ClaimAdjustmentGroupCodes.ContractualObligation,
            ClaimAdjustmentGroupCodes.OtherAdjustment,
            ClaimAdjustmentGroupCodes.PayerInitiatedReduction,
            ClaimAdjustmentGroupCodes.PatientResponsibility
        ));
    }

    public ChargeAdjustmentDtoFaker AsContractualObligation()
    {
        RuleFor(x => x.Group, ClaimAdjustmentGroupCodes.ContractualObligation);
        return this;
    }

    public ChargeAdjustmentDtoFaker AsPayerInitiatedReduction()
    {
        RuleFor(x => x.Group, ClaimAdjustmentGroupCodes.PayerInitiatedReduction);
        return this;
    }

    public ChargeAdjustmentDtoFaker AsOtherAdjustment()
    {
        RuleFor(x => x.Group, ClaimAdjustmentGroupCodes.OtherAdjustment);
        return this;
    }

    public ChargeAdjustmentDtoFaker AsPatientResponsibility()
    {
        RuleFor(x => x.Group, ClaimAdjustmentGroupCodes.PatientResponsibility);
        return this;
    }
}

public class ERAClaimChargeDtoFaker : AutoFaker<ERAClaimChargeDto>
{
    public ERAClaimChargeDtoFaker(Guid chargeId)
    {
        RuleFor(c => c.FromDate, f => f.Date.Past(1).ToString("yyyy-MM-dd"));
        RuleFor(c => c.ThruDate, f => f.Date.Past(1).ToString("yyyy-MM-dd"));
        RuleFor(c => c.Units, f => f.Random.Int(1, 10).ToString());
        RuleFor(c => c.ProcedureCode, f => f.Random.AlphaNumeric(5));
        RuleFor(c => c.Mod1, f => f.Random.AlphaNumeric(2));
        RuleFor(c => c.Mod2, f => f.Random.AlphaNumeric(2));
        RuleFor(c => c.Mod3, f => f.Random.AlphaNumeric(2));
        RuleFor(c => c.Mod4, f => f.Random.AlphaNumeric(2));
        RuleFor(c => c.Adjustments, f => []);
        RuleFor(c => c.ChgId, f => Base36GuidEncoder.Encode(chargeId));
        RuleFor(c => c.Pos, f => f.PickRandom("Office", "Hospital", "Clinic"));
        RuleFor(c => c.Charge, f => ClaimMdMapper.MapToDecimalString(f.Finance.Amount(100, 5000)));
        RuleFor(c => c.Paid, (_, c) => c.Charge);
        RuleFor(c => c.Allowed, (_, c) => c.Charge);
    }

    public ERAClaimChargeDtoFaker WithAdjustments(ChargeAdjustmentDto[] chargeAdjustments)
    {
        RuleFor(c => c.Adjustments, f => [.. chargeAdjustments]);

        return this;
    }

    public ERAClaimChargeDtoFaker FromServiceLine(ClaimServiceLine serviceLine)
    {
        var amount = ClaimMdMapper.MapToDecimalString(serviceLine.Amount + serviceLine.TaxAmount);

        RuleFor(c => c.ChgId, Base36GuidEncoder.Encode(serviceLine.Id));
        RuleFor(c => c.Charge, amount);
        RuleFor(c => c.Paid, amount);
        RuleFor(c => c.Allowed, amount);

        return this;
    }

    /// <summary>
    /// Recalculates the charge & allowed amounts based on the adjustments.
    /// </summary>
    public ERAClaimChargeDtoFaker Recalculate()
    {
        FinishWith((_, c) =>
        {
            var charge = Convert.ToDecimal(c.Paid) + c.Adjustments.Sum(a => Convert.ToDecimal(a.Amount));

            var disallowed = c.Adjustments
                .Where(a => a.Group != ClaimAdjustmentGroupCodes.PatientResponsibility)
                .Sum(a => Convert.ToDecimal(a.Amount));

            c.Charge = ClaimMdMapper.MapToDecimalString(charge);
            c.Allowed = ClaimMdMapper.MapToDecimalString(charge - disallowed);
        });

        return this;
    }
}

public class ERAClaimDtoFaker : AutoFaker<ERAClaimDto>
{
    public ERAClaimDtoFaker()
    {
        RuleFor(c => c.PCN, f => f.Random.Long(1).ToString());
        RuleFor(c => c.ProviderNpi, f => f.CarePatron().NPINumber());
        RuleFor(c => c.PatientLastName, f => f.Name.LastName());
        RuleFor(c => c.PatientMiddleName, f => f.Name.FirstName().Substring(0, 1));
        RuleFor(c => c.PatientFirstName, f => f.Name.FirstName());
        RuleFor(c => c.ThruDOS, f => f.Date.Past(1).ToString("yyyy-MM-dd"));
        RuleFor(c => c.FromDOS, f => f.Date.Past(2).ToString("yyyy-MM-dd"));
        RuleFor(c => c.PlaceOfService, f => f.PickRandom("Office", "Hospital", "Clinic"));
        RuleFor(c => c.StatusCode, f => f.PickRandom(ERAClaimStatusCode.ProcessedAsPrimary, ERAClaimStatusCode.ProcessedAsSecondary, ERAClaimStatusCode.ApprovedAsAmended));
        RuleFor(c => c.ClaimReceivedDate, f => f.Date.Past(1).ToString("yyyy-MM-dd"));
        RuleFor(c => c.PatientResponsibility, f => f.Finance.Amount(0, 1000).ToString("F2"));
        RuleFor(c => c.TotalCharge, f => f.Finance.Amount(100, 5000).ToString("F2"));
        RuleFor(c => c.TotalPaid, f => f.Finance.Amount(50, 5000).ToString("F2"));
        RuleFor(c => c.InsuranceNumber, f => f.Random.AlphaNumeric(8));
        RuleFor(c => c.PolicyHolderFirstName, f => f.Name.FirstName());
        RuleFor(c => c.PolicyHolderLastName, f => f.Name.LastName());
        RuleFor(c => c.PolicyHolderMiddleName, f => f.Name.FirstName().Substring(0, 1));
        RuleFor(c => c.PlanType, f => f.PickRandom("HMO", "PPO", "EPO"));
        RuleFor(c => c.FilingCode, f => f.Random.AlphaNumeric(5));
        RuleFor(c => c.CrossoverId, f => f.Random.AlphaNumeric(10));
        RuleFor(c => c.CrossoverCarrier, f => f.Company.CompanyName());
        RuleFor(c => c.FrequencyCode, f => f.Random.AlphaNumeric(2));
        RuleFor(c => c.PayerICN, f => f.Random.AlphaNumeric(15));
        RuleFor(c => c.Charges, f => []);
    }

    /// <summary>
    /// Adds the given charges to the claim & Recalculates the total charge and total paid amounts.
    /// </summary>
    public ERAClaimDtoFaker WithCharges(ERAClaimChargeDto[] charges)
    {
        RuleFor(c => c.Charges, f => [.. charges]);
        RuleFor(c => c.TotalCharge, ClaimMdMapper.MapToDecimalString(charges.Sum(c => Convert.ToDecimal(c.Charge))));
        RuleFor(c => c.TotalPaid, ClaimMdMapper.MapToDecimalString(charges.Sum(c => Convert.ToDecimal(c.Paid))));
        return this;
    }
}

public class ClaimDetailDtoFaker : AutoFaker<ClaimDetailDto>
{
    public ClaimDetailDtoFaker()
    {
        RuleFor(c => c.Status, f => f.PickRandom("Pending", "Approved", "Denied"));
        RuleFor(c => c.ClaimMdId, f => f.Random.Guid().ToString());
        RuleFor(c => c.ClaimId, f => f.Random.Guid().ToString());
        RuleFor(c => c.RemoteClaimId, f => f.Random.Guid().ToString());
        RuleFor(c => c.BatchId, f => f.Random.Guid().ToString());
        RuleFor(c => c.SenderName, f => f.Company.CompanyName());
        RuleFor(c => c.Messages, f => []);
        RuleFor(c => c.FileId, f => f.Random.Guid().ToString());
        RuleFor(c => c.InsuranceNumber, f => f.Random.Replace("########"));
        RuleFor(c => c.BillTaxId, f => f.Random.Replace("##-#######"));
        RuleFor(c => c.BillingNpi, f => f.CarePatron().NPINumber());
        RuleFor(c => c.PCN, f => f.Random.Long(1).ToString());
        RuleFor(c => c.TotalCharge, f => f.Finance.Amount(100, 5000).ToString());
        RuleFor(c => c.PayerId, f => f.Random.Replace("####"));
        RuleFor(c => c.BillingAddress1, f => f.Address.StreetAddress());
        RuleFor(c => c.BillingCity, f => f.Address.City());
        RuleFor(c => c.BillingName, f => f.Name.FullName());
        RuleFor(c => c.BillingPhone, f => f.Phone.PhoneNumber());
        RuleFor(c => c.BillingState, f => f.Address.StateAbbr());
        RuleFor(c => c.BillingZip, f => f.Address.ZipCode());
        RuleFor(c => c.ClaimForm, f => f.Lorem.Word());
        RuleFor(c => c.Diagnosis1, f => f.Random.Word());
        RuleFor(c => c.Diagnosis2, f => f.Random.Word());
        RuleFor(c => c.Diagnosis3, f => f.Random.Word());
        RuleFor(c => c.Diagnosis4, f => f.Random.Word());
        RuleFor(c => c.EmploymentRelated, f => f.PickRandom("Yes", "No"));
        RuleFor(c => c.PolicyHolderAddress1, f => f.Address.StreetAddress());
        RuleFor(c => c.PolicyHolderCity, f => f.Address.City());
        RuleFor(c => c.PolicyHolderState, f => f.Address.StateAbbr());
        RuleFor(c => c.PolicyHolderZip, f => f.Address.ZipCode());
        RuleFor(c => c.PolicyHolderDateOfBirth, f => f.Date.Past(50, DateTime.Now.AddYears(-18)).ToString("yyyy-MM-dd"));
        RuleFor(c => c.PolicyHolderGroup, f => f.Random.Replace("#######"));
        RuleFor(c => c.PolicyHolderSex, f => f.PickRandom("M", "F"));
        RuleFor(c => c.PatientFirstName, f => f.Name.FirstName());
        RuleFor(c => c.PatientLastName, f => f.Name.LastName());
        RuleFor(c => c.PatientRelationship, f => f.PickRandom("Self", "Spouse", "Child"));
        RuleFor(c => c.PatientSex, f => f.PickRandom("M", "F"));
        RuleFor(c => c.PatientState, f => f.Address.StateAbbr());
        RuleFor(c => c.PatientZip, f => f.Address.ZipCode());
        RuleFor(c => c.ProviderFirstName, f => f.Name.FirstName());
        RuleFor(c => c.ProviderLastName, f => f.Name.LastName());
        RuleFor(c => c.ProviderMiddleName, f => f.Name.FirstName());
        RuleFor(c => c.ProviderNpi, f => f.CarePatron().NPINumber());
        RuleFor(c => c.ProviderTaxonomy, f => f.Lorem.Word());
        RuleFor(c => c.ReferrerFirstName, f => f.Name.FirstName());
        RuleFor(c => c.ReferrerLastName, f => f.Name.LastName());
        RuleFor(c => c.ReferrerMiddleName, f => f.Name.FirstName());
        RuleFor(c => c.ReferrerNpi, f => f.CarePatron().NPINumber());
        RuleFor(c => c.RemoteFileId, f => f.Random.Guid().ToString());
    }
}

public class StatusUpdateClaimDtoFaker : AutoFaker<StatusUpdateClaimDto>
{
    public StatusUpdateClaimDtoFaker(string pcn = null, Guid? claimId = null)
    {
        RuleFor(x => x.ResponseTime, f => f.Date.Recent(1).ToString(ClaimMdDateHelper.Formats.DateTime12hAmPm));
        RuleFor(x => x.Messages, f => [new ClaimMessageDtoFaker().Generate()]);
        RuleFor(x => x.Status, f => Constants.ClaimStatusAcknowledged);
        RuleFor(x => x.ClaimMdId, f => f.Random.Guid().ToString());
        RuleFor(x => x.ClaimId, f => (claimId ?? f.Random.Guid()).ToString());
        RuleFor(x => x.RemoteClaimId, f => Base36GuidEncoder.Encode(claimId ?? f.Random.Guid()));
        RuleFor(x => x.BatchId, f => f.Random.Guid().ToString());
        RuleFor(x => x.SenderName, (f, c) => f.PickRandom(Constants.ClaimMdSenderId, c.PayerId));
        RuleFor(x => x.FileId, f => f.Random.Guid().ToString());
        RuleFor(x => x.InsuranceNumber, f => f.Random.String2(10, "**********"));
        RuleFor(x => x.BillTaxId, f => f.Random.String2(10, "**********"));
        RuleFor(x => x.BillingNpi, f => f.CarePatron().NPINumber());
        RuleFor(x => x.PCN, f => pcn ?? f.Random.Long(1).ToString());
        RuleFor(x => x.TotalCharge, f => f.Finance.Amount(100, 10000).ToString("F2"));
        RuleFor(x => x.PayerId, f => f.Random.String2(5, "**********"));
        RuleFor(x => x.SenderICN, f => f.Random.String2(12, "**********"));
        RuleFor(x => x.SenderId, f => f.Random.String2(10, "**********"));
    }
    
    public StatusUpdateClaimDtoFaker WithPayer(ProviderInsurancePayer payer)
    {
        RuleFor(x => x.PayerId, payer.PayerId);
        return this;
    }

    public StatusUpdateClaimDtoFaker AsRejected()
    {
        RuleFor(x => x.Status, f => Constants.ClaimStatusRejected);
        RuleFor(x => x.Messages, f => [new ClaimMessageDtoFaker().AsRejected().Generate()]);
        RuleFor(x => x.SenderICN, "");
        RuleFor(x => x.SenderId, Constants.ClaimMdSenderId);
        return this;
    }

    public StatusUpdateClaimDtoFaker AsDenied(string payerId)
    {
        RuleFor(x => x.Status, f => Constants.ClaimStatusRejected);
        RuleFor(x => x.Messages, f => [new ClaimMessageDtoFaker().AsDenied().Generate()]);
        RuleFor(x => x.SenderICN, f => f.Random.AlphaNumeric(10));
        RuleFor(x => x.SenderId, payerId);
        return this;
    }
}

public class UploadedClaimDtoFaker : AutoFaker<UploadedClaimDto>
{
    public UploadedClaimDtoFaker()
    {
        RuleFor(u => u.AcceptAssign, f => f.Random.Bool().ToString());
        RuleFor(u => u.AutoAccident, f => f.Random.Bool().ToString());
        RuleFor(u => u.BalanceDue, f => f.Finance.Amount().ToString());
        RuleFor(u => u.BillingTaxIdType, f => f.Random.Word());
        RuleFor(u => u.PolicyHolderFirstName, f => f.Name.FirstName());
        RuleFor(u => u.PolicyHolderLastName, f => f.Name.LastName());
        RuleFor(u => u.PolicyHolderMiddleName, f => f.Name.FirstName());
        RuleFor(u => u.BillingAddress1, f => f.Address.StreetAddress());
        RuleFor(u => u.BillingCity, f => f.Address.City());
        RuleFor(u => u.BillingName, f => f.Company.CompanyName());
        RuleFor(u => u.BillingPhone, f => f.Phone.PhoneNumber());
        RuleFor(u => u.BillingState, f => f.Address.StateAbbr());
        RuleFor(u => u.BillingZip, f => f.Address.ZipCode());
        RuleFor(u => u.Charges, f => new List<ClaimDetailChargeDto>());
        RuleFor(u => u.ClaimForm, f => f.Lorem.Word());
        RuleFor(u => u.Diagnosis1, f => f.Lorem.Word());
        RuleFor(u => u.Diagnosis2, f => f.Lorem.Word());
        RuleFor(u => u.Diagnosis3, f => f.Lorem.Word());
        RuleFor(u => u.Diagnosis4, f => f.Lorem.Word());
        RuleFor(u => u.Status, f => f.Random.Word());
        RuleFor(u => u.ClaimMdId, f => f.Random.Guid().ToString());
        RuleFor(u => u.ClaimId, f => f.Random.Guid().ToString());
        RuleFor(u => u.RemoteClaimId, f => Base36GuidEncoder.Encode(Guid.NewGuid()));
        RuleFor(u => u.BatchId, f => f.Random.Guid().ToString());
        RuleFor(u => u.SenderName, f => f.Company.CompanyName());
        RuleFor(u => u.Messages, f => []);
        RuleFor(u => u.FileId, f => f.Random.Guid().ToString());
        RuleFor(u => u.InsuranceNumber, f => f.Finance.Account());
        RuleFor(u => u.BillTaxId, f => f.Random.Replace("###-##-####"));
        RuleFor(u => u.BillingNpi, f => f.CarePatron().NPINumber());
        RuleFor(u => u.PCN, f => f.Random.Long(1).ToString());
        RuleFor(u => u.TotalCharge, f => f.Finance.Amount(100, 10000).ToString());
        RuleFor(u => u.PayerId, f => f.Random.AlphaNumeric(8));
    }
}
