using carepatron.core.Application.Insurance.Models;
using ClaimMD.Module;
using ClaimMD.Module.Models.Http.Dtos;

namespace tests.common.Data.Fakers.ClaimMD;

public class ClaimMessageDtoFaker : AutoFaker<ClaimMessageDto>
{
    public ClaimMessageDtoFaker()
    {
        RuleFor(m => m.Fields, f => f.Lorem.Sentence());
        RuleFor(m => m.Status, f => Constants.ClaimStatusAcknowledged);
        RuleFor(m => m.MesgId, f => f.Random.AlphaNumeric(10));
        RuleFor(m => m.Content, f => f.Lorem.Sentence());
        RuleFor(m => m.ResponseId, f => f.Random.Long(1, 10000000));
    }

    public ClaimMessageDtoFaker AsRejected()
    {
        RuleFor(m => m.Status, f => Constants.ClaimStatusRejected);
        return this;
    }

    public ClaimMessageDtoFaker AsDenied()
    {
        RuleFor(m => m.Status, f => Constants.ClaimStatusRejected);
        RuleFor(
            m => m.MesgId,
            f => $"{ClaimAdjustmentGroupCodes.OtherAdjustment}-{f.Random.Replace("###")}"
        );
        return this;
    }
}
