using carepatron.core.Application.Insurance.Models;
using ClaimMD.Module.Models.Http.Requests;
using ClaimMD.Module.Models.Http.Responses;
using tests.common.Data.Datasets;

namespace tests.common.Data.Fakers.ClaimMD;

public class ERAListRequestFaker : AutoFaker<ERAListRequest>
{
    public ERAListRequestFaker()
    {
        RuleFor(e => e.CheckDate, f => DateOnly.FromDateTime(f.Date.Past(1)));
        RuleFor(e => e.ReceivedDate, f => DateOnly.FromDateTime(f.Date.Past(1)));
        RuleFor(e => e.ReceivedAfterDate, f => DateOnly.FromDateTime(f.Date.Past(2)));
        RuleFor(e => e.CheckNumber, f => f.Finance.Account());
        RuleFor(e => e.CheckAmount, f => f.Finance.Amount(10, 10000));
        RuleFor(e => e.PayerId, f => f.Random.AlphaNumeric(10));
        RuleFor(e => e.NationalProviderId, x => x.CarePatron().NPINumber());
        RuleFor(e => e.TaxId, f => f.Random.Replace("###-##-####"));
        RuleFor(e => e.NewOnly, f => f.Random.Bool());
        RuleFor(e => e.EraId, f => f.Random.Int(1).ToString());
        RuleFor(e => e.Page, f => f.Random.Int(1, 100));
    }
}

public class ERAListItemDtoFaker : AutoFaker<ERAListItemDto>
{
    public ERAListItemDtoFaker()
    {
        RuleFor(e => e.PaidDate, f => f.Date.Past(1).ToString("yyyy-MM-dd"));
        RuleFor(e => e.DownloadTime, f => f.Date.Past(1).ToString("yyyy-MM-dd HH:mm:ss"));
        RuleFor(e => e.ReceivedTime, f => f.Date.Past(1).ToString("yyyy-MM-dd HH:mm:ss"));
        RuleFor(e => e.PaidAmount, f => f.Finance.Amount(10, 10000).ToString("F2"));
        RuleFor(e => e.PayerId, f => f.Random.AlphaNumeric(10));
        RuleFor(e => e.PayerName, f => f.Company.CompanyName());
        RuleFor(e => e.ProviderTaxId, f => f.Random.Replace("###-##-####"));
        RuleFor(e => e.ClaimMdProviderName, f => f.Name.FullName());
        RuleFor(e => e.ProviderName, f => f.Name.FullName());
        RuleFor(e => e.ProviderNpi, f => f.CarePatron().NPINumber());
        RuleFor(e => e.EraId, f => f.Random.Int(1).ToString());
        RuleFor(e => e.CheckType, f => f.PickRandom("Original", "Replacement", "Void"));
        RuleFor(e => e.CheckNumber, f => f.Finance.Account());
    }
    
    public ERAListItemDtoFaker FromClaim(InsuranceClaimUSProfessional claim)
    {
        RuleFor(x => x.ProviderNpi, claim.BillingDetail.NationalProviderId);
        RuleFor(x => x.ProviderTaxId, claim.BillingDetail.TaxNumber);
        
        return this;
    }
}

public class ERAListResponseFaker : AutoFaker<ERAListResponse>
{
    public ERAListResponseFaker(ERAListItemDto[] items = null)
    {
        items ??= [new ERAListItemDtoFaker().Generate()];
        RuleFor(e => e.LastEraId, f => f.Random.Int(1));
        RuleFor(e => e.Era, [.. items]);
        RuleFor(x => x.Errors, f => null);
    }
}
