using Bogus;
using carepatron.core.Application.Contacts.Models;

namespace tests.common.Data.Fakers
{
    public class ContactImportSummaryFaker : Faker<ContactImportSummary>
    {
        public ContactImportSummaryFaker(Guid providerId, Guid staffPersonId)
        {
            RuleFor(c => c.Id, f => f.Random.Guid());
            RuleFor(c => c.ProviderId, providerId);
            RuleFor(c => c.FileId, f=>f.Random.Guid());
            RuleFor(c => c.OriginalFileName, (string)null);
            RuleFor(c => c.FileName, f => f.Random.Words(1) + ".zip");
            RuleFor(c => c.FileSize, f => f.Random.Long(1, 1000000));
            RuleFor(c => c.FileExtension, f => ".zip");
            RuleFor(c => c.ImportType, ImportType.Standard);
            RuleFor(c => c.IsContact, false);
            RuleFor(c => c.LastStatusSeenBy, f => new Guid[] { f.Random.Guid() });
            RuleFor(c => c.CreatedByPersonId, f => staffPersonId);
        }
    }
}
