using Bogus;
using carepatron.core.Application.Billables.Models;
using carepatron.core.Application.Billing.Models;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Workspace.Providers.Models;
using carepatron.core.Extensions;
using carepatron.core.Models.Common;
using carepatron.core.Utilities;
using tests.common.Data.Datasets;
using Person = carepatron.core.Application.Users.Models.Person;

namespace tests.common.Data.Fakers;

public class ClaimClientFaker : AutoFaker<ClaimClient>
{
    public ClaimClientFaker(Guid? providerId = null, Contact contact = null)
    {
        var fakeContact = contact ?? new ContactFaker(providerId).Generate();
        var contactSex = ContactUtilities.GetSexValueFromOptionSetValue(contact?.Sex);
        RuleFor(x => x.Id, x => Guid.NewGuid());
        RuleFor(x => x.Contact, fakeContact);
        RuleFor(x => x.ProviderId, providerId);
        RuleFor(x => x.FirstName, x => contact.FirstName ?? x.Person.FirstName);
        RuleFor(x => x.LastName, x => contact.LastName ?? x.Person.LastName);
        RuleFor(x => x.MiddleName, x => contact.MiddleNames ?? x.Person.LastName);
        RuleFor(x => x.Address, contact.Address ?? new AddressFaker().Generate());
        RuleFor(x => x.DateOfBirth, x => contact.BirthDate ?? x.Date.PastDateOnly());
        RuleFor(x => x.PhoneCountryCode, x => null);
        RuleFor(x => x.PhoneNumber, x => null);
        RuleFor(x => x.PhoneNumberDetails, x => new PhoneNumberFaker().Generate());
        RuleFor(x => x.Sex, x => contactSex ?? x.PickRandom(new List<Sex> { Sex.Male, Sex.Female }));
    }
}

public class ClaimIncidentFaker : AutoFaker<ClaimIncident>
{
    public ClaimIncidentFaker(Guid? providerId = null)
    {
        RuleFor(x => x.Id, x => Guid.NewGuid());
        RuleFor(x => x.ProviderId, providerId);
        RuleFor(x => x.IsConditionRelatedToEmployment, x => x.Random.Bool());
        RuleFor(x => x.IsConditionRelatedToAutoAccident, x => x.Random.Bool());
        RuleFor(x => x.IsConditionRelatedToOtherAccident, x => x.Random.Bool());
        RuleFor(x => x.AutoAccidentState, x => x.Lorem.Word());
        RuleFor(x => x.CurrentIllnessDate, x => x.Date.PastDateOnly());
        RuleFor(x => x.CurrentIllnessQualifier, x => x.Lorem.Word());
        RuleFor(x => x.OtherAssociatedDate, x => x.Date.PastDateOnly());
        RuleFor(x => x.OtherAssociatedQualifier, x => x.Lorem.Word());
        RuleFor(x => x.UnableToWorkFrom, x => x.Date.PastDateOnly());
        RuleFor(x => x.UnableToWorkTo, x => x.Date.PastDateOnly());
        RuleFor(x => x.HospitalizationFrom, x => x.Date.PastDateOnly());
        RuleFor(x => x.HospitalizationTo, x => x.Date.PastDateOnly());
    }
}

public class ClaimFacilityFaker : AutoFaker<ClaimFacility>
{
    public ClaimFacilityFaker(Guid? providerId = null)
    {
        RuleFor(x => x.Id, x => Guid.NewGuid());
        RuleFor(x => x.ProviderId, x => providerId);
        RuleFor(x => x.ProviderLocationId, x => Guid.NewGuid());
        RuleFor(x => x.Name, x => x.Lorem.Word());
        RuleFor(x => x.PlaceOfService, x => x.Lorem.Word());
        RuleFor(x => x.Address, new AddressFaker().Generate());
        RuleFor(x => x.NationalProviderId, x => x.CarePatron().NPINumber());
        RuleFor(x => x.OtherIdQualifier, "StateLicenseNumber");
        RuleFor(x => x.OtherId, x => x.Lorem.Word());
    }
}

public class ClaimBillingDetailFaker : AutoFaker<ClaimBillingDetail>
{
    public ClaimBillingDetailFaker(Guid? providerId = null, ProviderBillingProfile billingProfile = null)
    {
        var fakeProfile = billingProfile ?? new ProviderBillingProfileFaker(providerId.Value).Generate();
        RuleFor(x => x.Id, x => Guid.NewGuid());
        RuleFor(x => x.BillingProfile, fakeProfile);
        RuleFor(x => x.ProviderId, providerId);
        RuleFor(x => x.Name, fakeProfile.Name);
        RuleFor(x => x.Type, BillingProfileType.Organisation);
        RuleFor(x => x.TaxNumberType, "SSN");
        RuleFor(x => x.NationalProviderId, x => x.CarePatron().NPINumber());
        RuleFor(x => x.TaxNumber, x => x.Random.AlphaNumeric(10));
        RuleFor(x => x.TaxonomyCode, x => x.Random.AlphaNumeric(10));
        RuleFor(x => x.Address, new AddressFaker().Generate());
        RuleFor(x => x.OtherIdQualifier, x => x.Lorem.Word());
        RuleFor(x => x.OtherId, x => x.Random.AlphaNumeric(10));
        RuleFor(x => x.PhoneCountryCode, x => null);
        RuleFor(x => x.PhoneNumber, x => null);
        RuleFor(x => x.PhoneNumberDetails, x => new PhoneNumberFaker().Generate());
    }
}

public class ClaimContactInsurancePolicyFaker : AutoFaker<ClaimContactInsurancePolicy>
{
    public ClaimContactInsurancePolicyFaker(Guid? providerId = null, Guid? contactId = null, ContactInsurancePolicy policy = null)
    {
        var faker = new Faker();
        var fakePolicy = policy ?? new ContactInsurancePolicyFaker(providerId.Value, contactId.Value).Generate();
        var payer = policy?.Payer ?? new ProviderInsurancePayerFaker(providerId.Value).Generate();

        var phoneNumber = new PhoneNumberFaker().Generate();

        RuleFor(x => x.Id, x => Guid.NewGuid());
        RuleFor(x => x.ContactInsurancePolicy, fakePolicy);
        RuleFor(x => x.ProviderId, providerId);
        RuleFor(x => x.InsuranceType, x => x.PickRandomWithout(InsuranceType.Unknown, InsuranceType.Other));
        RuleFor(x => x.PolicyHolderMemberId, x => policy?.MemberId ?? x.Random.Replace("###-####"));
        RuleFor(x => x.PolicyHolderGroupId, x => policy?.GroupId ?? x.Random.Replace("###-####"));
        RuleFor(x => x.PolicyHolderSex, x => x.PickRandom(new List<Sex> { Sex.Male, Sex.Female }));
        RuleFor(x => x.PolicyHolderPhoneCountryCode, x => null);
        RuleFor(x => x.PolicyHolderPhoneNumber, x => null);
        RuleFor(x => x.PolicyHolderPhoneNumberDetails, x => phoneNumber);
        RuleFor(x => x.PolicyHolderFirstName, x => policy?.PolicyHolder?.Contact?.FirstName ?? x.Person.FirstName);
        RuleFor(x => x.PolicyHolderMiddleName, x => policy?.PolicyHolder?.Contact?.MiddleNames ?? x.Random.Word());
        RuleFor(x => x.PolicyHolderLastName, x => policy?.PolicyHolder?.Contact?.LastName ?? x.Person.LastName);
        RuleFor(x => x.PolicyHolderDateOfBirth, x => policy?.PolicyHolder?.Contact?.BirthDate ?? x.Date.PastDateOnly());
        RuleFor(x => x.Address, new AddressFaker().Generate());
        RuleFor(x => x.PolicyHolderRelationshipType, x => x.PickRandomWithout(InsurancePolicyHolderType.Unknown));
        RuleFor(x => x.PolicyHolderAddress, policy?.PolicyHolder?.Contact?.Address);

        RuleFor(x => x.PayerId, x => policy?.Payer?.Id); // remain null if not passed in.
        RuleFor(x => x.PayerName, x => payer.Name);
        RuleFor(x => x.PayerNumber, x => payer.PayerId);
        RuleFor(x => x.CoverageType, x => payer.CoverageType);
        RuleFor(x => x.OtherCoverageTypeName, x => payer.OtherCoverageTypeName);
        RuleFor(x => x.PayerPhoneCountryCode, x => null);
        RuleFor(x => x.PayerPhoneNumber, x => null);
        RuleFor(x => x.PayerPhoneNumberDetails, payer.PhoneNumber);
    }
}

public class ClaimServiceLineFaker : AutoFaker<ClaimServiceLine>
{
    public ClaimServiceLineFaker(Guid? providerId = null, Guid? contactId = null, Guid? billableItemId = null)
    {
        RuleFor(x => x.Id, x => Guid.NewGuid());
        RuleFor(x => x.BillableItemId, billableItemId);
        RuleFor(x => x.ProviderId, providerId);
        RuleFor(x => x.Units, x => x.Random.Number(1, 10));
        RuleFor(x => x.Amount, x => Math.Round(x.Random.Decimal(100, 200), 2));
        RuleFor(x => x.TaxAmount, x => Math.Round(x.Random.Decimal(1, 50), 2));
        RuleFor(x => x.CurrencyCode, "USD");
        RuleFor(x => x.POSCode, x => x.Random.Number(1, 100).ToString());
        RuleFor(x => x.Modifiers, x => [x.Random.Number(1, 100).ToString(), x.Random.Number(1, 100).ToString()]);
        RuleFor(x => x.DiagnosticCodeReferences,
            x => [x.Random.Number(1, 100).ToString(), x.Random.Number(1, 100).ToString()]);
        RuleFor(x => x.EPSDT, x => x.PickRandom<ClaimServiceLineEPSDT>());
        RuleFor(x => x.FamilyPlanningService, x => x.Random.Bool());
        RuleFor(x => x.Date, x => x.Date.PastDateOnly());
        RuleFor(x => x.Code, x => x.Lorem.Word());
        RuleFor(x => x.Description, x => x.Lorem.Sentence());
        RuleFor(x => x.Detail, x => x.Lorem.Sentence());
        RuleFor(x => x.ServiceId, x => Guid.NewGuid());
        RuleFor(x => x.Emergency, x => x.Random.Bool());
        RuleFor(x => x.SupplementalInfo, x => x.Lorem.Sentence());
        RuleFor(x => x.OrderIndex, x => 1);
    }

    public ClaimServiceLineFaker WithBillableItem(BillableItem billableItem)
    {
        RuleFor(x => x.BillableItemId, billableItem.Id);
        RuleFor(x => x.Units, billableItem.Units);
        RuleFor(x => x.Amount, billableItem.Amount);
        RuleFor(x => x.TaxAmount, billableItem.TaxAmount);
        return this;
    }
}

public class ClaimReferringProviderFaker : AutoFaker<ClaimReferringProvider>
{
    public ClaimReferringProviderFaker(Guid? providerId = null)
    {
        RuleFor(x => x.Id, x => Guid.NewGuid());
        RuleFor(x => x.NationalProviderId, x => x.CarePatron().NPINumber());
        RuleFor(x => x.Qualifier, x => x.PickRandom<ClaimProviderQualifier>());
        RuleFor(x => x.OtherIdQualifier, x => x.Lorem.Word());
        RuleFor(x => x.ProviderId, providerId);
        RuleFor(x => x.OtherId, x => x.Random.AlphaNumeric(10));
        RuleFor(x => x.FirstName, x => x.Person.FirstName);
        RuleFor(x => x.LastName, x => x.Person.LastName);
        RuleFor(x => x.MiddleName, x => x.Person.LastName);
        RuleFor(x => x.IncludeReferrerInformation, true);
    }
}

public class ClaimRenderingProviderFaker : AutoFaker<ClaimRenderingProvider>
{
    public ClaimRenderingProviderFaker(Guid? providerId = null, ClaimProviderStaff staff = null)
    {
        RuleFor(x => x.Id, x => Guid.NewGuid());
        RuleFor(x => x.NationalProviderId, x => staff?.NationalProviderId ?? x.CarePatron().NPINumber());
        RuleFor(x => x.OtherIdQualifier, x => x.Lorem.Word());
        RuleFor(x => x.OtherId, x => x.Random.AlphaNumeric(10));
        RuleFor(x => x.FirstName, x => staff?.FirstName ?? x.Person.FirstName);
        RuleFor(x => x.MiddleName, x => staff?.MiddleName ?? x.Random.Word());
        RuleFor(x => x.LastName, x => staff?.LastName ?? x.Person.LastName);
        RuleFor(x => x.ProviderId, providerId);
        RuleFor(x => x.StaffMember, staff);
        RuleFor(x => x.OrderIndex, x => 1);
    }
}

public class ClaimDiagnosticCodeFaker : AutoFaker<ClaimDiagnosticCode>
{
    public ClaimDiagnosticCodeFaker(Guid providerId)
    {
        RuleFor(x => x.Id, x => Guid.NewGuid());
        RuleFor(x => x.ProviderId, providerId);
        RuleFor(x => x.Code, x => x.DiagnosisCodes().ICDCode().Code);
        RuleFor(x => x.Description, x => x.Lorem.Word());
        RuleFor(x => x.Reference, x => x.Random.Number(1, 100));
        RuleFor(x => x.OnsetDateFrom, x => x.Date.PastDateOnly());
        RuleFor(x => x.OnsetDateTo, x => x.Date.PastDateOnly());
    }
}

public class ClaimProviderStaffFaker : AutoFaker<ClaimProviderStaff>
{
    public ClaimProviderStaffFaker(Guid? providerId = null, Person person = null, ProviderRole role = ProviderRole.Admin)
    {
        RuleFor(x => x.Role, role);
        RuleFor(x => x.LicenseNumber, x => x.Random.Replace("???######"));
        RuleFor(x => x.NationalProviderId, x => x.CarePatron().NPINumber());
        RuleFor(x => x.TaxonomyCode, x => x.Random.Replace("???######"));
        RuleFor(x => x.ColorHex, x => x.Commerce.Color());
        RuleFor(x => x.CreatedDateTimeUtc, DateTime.UtcNow);
        RuleFor(x => x.UpdatedDateTimeUtc, DateTime.UtcNow);
        RuleFor(x => x.Email, x => person?.Email ?? x.CarePatron().Email());
        RuleFor(x => x.Person, person);
        RuleFor(x => x.PersonId, person?.Id ?? Guid.NewGuid());
        RuleFor(x => x.ProviderId, providerId ?? Guid.NewGuid());
    }

    public ClaimProviderStaffFaker WithPerson(Person person)
    {
        RuleFor(x => x.PersonId, person.Id);
        return this;
    }
}

public class InsuranceClaimContactReferenceFaker : AutoFaker<InsuranceClaimContactReference>
{
    public InsuranceClaimContactReferenceFaker(Contact contact)
    {
        RuleFor(x => x.Id, x => contact.Id);
    }
}

public class USProfessionalClaimFaker : AutoFaker<InsuranceClaimUSProfessional>
{
    public USProfessionalClaimFaker(Guid providerId)
    {
        RuleFor(x => x.ProviderId, x => providerId);
        RuleFor(x => x.Status, ClaimStatus.Draft);
        RuleFor(x => x.CurrencyCode, "USD");
        RuleFor(x => x.SubmissionMethod, ClaimSubmissionMethod.Manual);
        RuleFor(x => x.Number, x => x.Random.Number(100000, 999999).ToString());
        RuleFor(x => x.FromDate, f => DateTime.Now.ToDateOnly());
        RuleFor(x => x.ToDate, (f, c) => c.FromDate?.AddMonths(1));
        RuleFor(x => x.Amount, x => x.Random.Decimal(100, 5000));
        RuleFor(x => x.AmountPaid, 0);
        RuleFor(x => x.Lab, x => x.Random.Bool());
        RuleFor(x => x.LabCharges, 0);
        RuleFor(x => x.Type, ClaimType.USProfessional);
        RuleFor(x => x.OriginalReferenceNumber, x => x.Random.AlphaNumeric(10));
        RuleFor(x => x.PatientsAccountNumber, x => x.Random.AlphaNumeric(10));
        RuleFor(x => x.PriorAuthorizationNumber, x => x.Random.AlphaNumeric(10));
        RuleFor(x => x.ResubmissionCode, x => x.Random.AlphaNumeric(10));
        RuleFor(x => x.BalancePaid, 0);
        RuleFor(x => x.Client, x => null);
        RuleFor(x => x.Incident, x => null);
        RuleFor(x => x.ServiceFacility, x => null);
        RuleFor(x => x.ServiceLines, x => null);
        RuleFor(x => x.ReferringProviders, x => null);
        RuleFor(x => x.RenderingProviders, x => null);
        RuleFor(x => x.BillingDetail, x => null);
        RuleFor(x => x.ContactInsurancePolicy, x => null);
        RuleFor(x => x.DiagnosticCodes, x => null);
        RuleFor(x => x.Contact, x => null);
        RuleFor(x => x.LastSubmittedDateTimeUtc, (DateTime?)null);
        RuleFor(x => x.CreatedDateTimeUtc, DateTime.UtcNow);
        RuleFor(x => x.UpdatedDateTimeUtc, DateTime.UtcNow);
    }

    public USProfessionalClaimFaker WithClient(Guid? providerId = null, Contact contact = null)
    {
        RuleFor(x => x.ContactId, x => contact.Id);
        RuleFor(x => x.ClientControlNumber, x => x.Random.Long(1).ToString());
        RuleFor(x => x.Client, new ClaimClientFaker(providerId, contact).Generate());
        RuleFor(x => x.Contact, new InsuranceClaimContactReferenceFaker(contact).Generate());
        return this;
    }

    public USProfessionalClaimFaker WithClient(ClaimClient claimClient)
    {
        RuleFor(x => x.Client, claimClient);
        RuleFor(x => x.ClientControlNumber, x => x.Random.Long(1).ToString());
        return this;
    }

    public USProfessionalClaimFaker WithIncident(Guid? providerId = null)
    {
        RuleFor(x => x.Incident, new ClaimIncidentFaker(providerId).Generate());
        return this;
    }

    public USProfessionalClaimFaker WithIncident(ClaimIncident claimIncident)
    {
        RuleFor(x => x.Incident, claimIncident);
        return this;
    }

    public USProfessionalClaimFaker WithFacility(Guid? providerId = null)
    {
        RuleFor(x => x.ServiceFacility, new ClaimFacilityFaker(providerId).Generate());
        return this;
    }

    public USProfessionalClaimFaker WithFacility(ClaimFacility claimFacility)
    {
        RuleFor(x => x.ServiceFacility, claimFacility);
        return this;
    }

    public USProfessionalClaimFaker WithResubmissionCode(string resubmissionCode)
    {
        RuleFor(x => x.ResubmissionCode, resubmissionCode);
        RuleFor(x => x.OriginalReferenceNumber, f => f.Random.AlphaNumeric(10));
        return this;
    }
    
    public USProfessionalClaimFaker AsPaid()
    {
        RuleFor(x => x.AmountPaid, (_, obj) => obj.Amount);
        RuleFor(x => x.BalancePaid, (_, obj) => obj.Amount);
        RuleFor(x => x.Status, ClaimStatus.Paid);
        return this;
    }
    
    public USProfessionalClaimFaker AsPartiallyPaid()
    {
        RuleFor(x => x.AmountPaid, (_, obj) => obj.Amount / 2);
        RuleFor(x => x.BalancePaid, (_, obj) => obj.Amount / 2);
        RuleFor(x => x.Status, ClaimStatus.PartiallyPaid);
        return this;
    }

    [Obsolete]
    public USProfessionalClaimFaker WithServiceLines(Guid? providerId = null, Guid? contactId = null, BillableItem billableItem = null)
    {
        // todo - maybe adjust these setups a litle.
        // - pass BillableItem(s) into ClaimServiceLineFaker to generate service lines.
        // - pass generated services into claim faker. 
        RuleFor(x => x.ServiceLines, [new ClaimServiceLineFaker(providerId, contactId, billableItem?.Id).Generate()]);
        return this;
    }

    public USProfessionalClaimFaker WithServiceLines(params ClaimServiceLine[] serviceLines)
    {
        RuleFor(x => x.ServiceLines, serviceLines);
        RuleFor(x => x.Amount, serviceLines.Sum(x => x.Amount + x.TaxAmount));
        return this;
    }

    public USProfessionalClaimFaker WithReferringProviders(Guid? providerId = null)
    {
        RuleFor(x => x.ReferringProviders, [new ClaimReferringProviderFaker(providerId).Generate()]);
        return this;
    }

    public USProfessionalClaimFaker WithReferringProviders(ClaimReferringProvider[] providers)
    {
        RuleFor(x => x.ReferringProviders, providers);
        return this;
    }

    public USProfessionalClaimFaker WithRenderingProviders(Guid? providerId = null, ClaimProviderStaff staff = null)
    {
        RuleFor(x => x.RenderingProviders, [new ClaimRenderingProviderFaker(providerId, staff).Generate()]);
        return this;
    }

    public USProfessionalClaimFaker WithRenderingProviders(ClaimRenderingProvider[] providers)
    {
        RuleFor(x => x.RenderingProviders, providers);
        return this;
    }

    public USProfessionalClaimFaker WithBillingDetail(Guid? providerId = null, ProviderBillingProfile billingProfile = null)
    {
        RuleFor(x => x.BillingDetail, new ClaimBillingDetailFaker(providerId, billingProfile).Generate());
        return this;
    }

    public USProfessionalClaimFaker WithBillingDetail(ClaimBillingDetail claimBillingProvider)
    {
        RuleFor(x => x.BillingDetail, claimBillingProvider);
        return this;
    }

    public USProfessionalClaimFaker WithContactInsurancePolicy(Guid? providerId = null, Guid? contactId = null, ContactInsurancePolicy policy = null)
    {
        RuleFor(x => x.ContactInsurancePolicy, new ClaimContactInsurancePolicyFaker(providerId, contactId, policy).Generate());
        return this;
    }

    public USProfessionalClaimFaker WithContactInsurancePolicy(ClaimContactInsurancePolicy claimContactInsurancePolicy)
    {
        RuleFor(x => x.ContactInsurancePolicy, claimContactInsurancePolicy);
        return this;
    }

    public USProfessionalClaimFaker WithDiagnosticCodes(Guid? providerId = null)
    {
        RuleFor(x => x.DiagnosticCodes, [new ClaimDiagnosticCodeFaker(providerId.Value).Generate()]);
        return this;
    }

    public USProfessionalClaimFaker WithDiagnosticCodes(ClaimDiagnosticCode[] claimDiagnosticCodes)
    {
        RuleFor(x => x.DiagnosticCodes, claimDiagnosticCodes);
        return this;
    }

    public USProfessionalClaimFaker WithStatus(ClaimStatus claimStatus)
    {
        RuleFor(x => x.Status, claimStatus);
        return this;
    }


    public USProfessionalClaimFaker WithInsurancePayer(ProviderInsurancePayer payer)
    {
        RuleFor(x => x.Payer, new InsurancePayerReference(payer));
        return this;
    }

    public USProfessionalClaimFaker WithAmountPaid(params BillableItem[] billableItems)
    {
        RuleFor(x => x.AmountPaid, billableItems.Sum(y => y.SelfPayAmount + y.InsurancePaid));
        return this;
    }
}
