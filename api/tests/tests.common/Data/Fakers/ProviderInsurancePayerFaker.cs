using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Payments.Models;
using carepatron.infra.sql.Models.Insurance;

namespace tests.common.Data.Fakers
{
    public class ProviderInsurancePayerFaker : AutoFaker<ProviderInsurancePayer>
    {
        public ProviderInsurancePayerFaker(Guid providerId)
        {
            RuleFor(x => x.Id, x => Guid.NewGuid());
            RuleFor(x => x.ProviderId, x => providerId);
            RuleFor(x => x.PayerId, x => x.Random.AlphaNumeric(5));
            RuleFor(x => x.Name, x => x.Company.CompanyName());
            RuleFor(x => x.Address, new AddressFaker().Generate());
            RuleFor(x => x.PhoneNumber, x => x.Phone.PhoneNumber());
            RuleFor(x => x.ClearingHouse, ClearingHouseType.ManagedClaimMd);
            RuleFor(
                x => x.CoverageType,
                x => x.PickRandomWithout(InsuranceCoverageType.Unknown, InsuranceCoverageType.Other)
            );
            RuleFor(
                x => x.OtherCoverageTypeName,
                (f, v) =>
                    v.CoverageType == InsuranceCoverageType.Other ? f.Random.Words(3) : string.Empty
            );
        }
        
        public ProviderInsurancePayerFaker ForPayer(InsurancePayerDataModel payer)
        {
            RuleFor(x => x.PayerId, payer.PayerId);
            RuleFor(x => x.Name, payer.Name);
            RuleFor(x => x.ClearingHouse, payer.ClearingHouse);
                
            return this;
        }
    }

    public class InsurancePayerReferenceFaker : AutoFaker<InsurancePayerReference>
    {
        public InsurancePayerReferenceFaker(Guid? id = null)
        {
            RuleFor(x => x.Id, x => id ?? Guid.NewGuid());
            RuleFor(x => x.Name, x => x.Company.CompanyName());
            RuleFor(x => x.PayerNumber, x => x.Random.AlphaNumeric(5));
        }
    }
}
