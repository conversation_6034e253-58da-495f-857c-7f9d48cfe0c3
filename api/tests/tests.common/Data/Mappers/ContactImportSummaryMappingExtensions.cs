using carepatron.core.Application.Contacts.Models;
using carepatron.infra.sql.Models.Contact;

namespace tests.common.Data.Mappers
{
    public static class ContactImportSummaryMappingExtensions
    {
        public static ContactImportSummaryDataModel[] ToDataModel(this IEnumerable<ContactImportSummary> entities)
        {
            return entities.Select(ToDataModel).ToArray();
        }
        
        public static ContactImportSummaryDataModel ToDataModel(this ContactImportSummary entity)
        {
            return new ContactImportSummaryDataModel
            {
                Id = entity.Id,
                ProviderId = entity.ProviderId,
                FileId = entity.FileId,
                OriginalFileName = entity.OriginalFileName,
                FileName = entity.FileName,
                FileSize = entity.FileSize,
                FileExtension = entity.FileExtension,
                CreatedDateTimeUtc = entity.CreatedDateTimeUtc,
                LastStatusSeenBy = entity.LastStatusSeenBy,
                CompletedDateTimeUtc = entity.CompletedDateTimeUtc,
                Status = entity.Status,
                ImportType = entity.ImportType,
                IsContact = entity.IsContact,
                CreatedByPersonId = entity.CreatedByPersonId,
                UpdatedDateTime = entity.UpdatedDateTime
            };
        }

    }

}