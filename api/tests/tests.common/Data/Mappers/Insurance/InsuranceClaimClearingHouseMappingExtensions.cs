using carepatron.core.Application.ClearingHouse.Models;
using carepatron.infra.sql.Models.ClearingHouse;

namespace tests.common.Data.Mappers.Insurance;

public static class InsuranceClaimClearingHouseMappingExtensions
{
    public static ClearingHouseLogDataModel ToDataModel(
        this ClearingHouseLog domain,
        DateTime? createdDateTimeUtc = null,
        DateTime? updatedDateTimeUtc = null
    )
    {
        if (domain == null)
            return null;

        var defaultDateTime = DateTime.UtcNow;

        return new ClearingHouseLogDataModel
        {
            ExternalId = domain.ExternalId,
            ClearingHouse = domain.ClearingHouse,
            Type = domain.Type,
            Status = domain.Status,
            RawResponse = domain.RawResponse,
            CreatedDateTimeUtc = createdDateTimeUtc ?? defaultDateTime,
            UpdatedDateTimeUtc = updatedDateTimeUtc ?? defaultDateTime
        };
    }
}
