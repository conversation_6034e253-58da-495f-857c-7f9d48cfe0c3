﻿using carepatron.core.Application.Billables.Models;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.TaxRates.Models;
using carepatron.core.Common;
using carepatron.core.Models.Invoices;
using carepatron.infra.sql.Models.Invoices;

namespace tests.common.Data.Mappers
{
    public static class InvoiceMappingExtensions
    {
        public static InvoiceDataModel ToDataModel(this Invoice invoice)
        {
            invoice.TaskIds ??= [];
            invoice.TaskIds = invoice.TaskId.HasValue ? invoice.TaskIds.Append(invoice.TaskId.Value).ToArray() : invoice.TaskIds;
            var currencyHandler = CurrencyHandler.Get(invoice.CurrencyCode);
            return new InvoiceDataModel()
            {
                Id = invoice.Id,
                ProviderId = invoice.ProviderId,
                ContactId = invoice.ContactId,
                BillToId = invoice.BillToId,
                Title = invoice.Title,
                Number = invoice.Number,
                Description = invoice.Description,
                DueDate = invoice.DueDate,
                IssueDate = invoice.IssueDate,
                PaymentDate = invoice.PaymentDate,
                POSONumber = invoice.POSONumber,
                ServiceDate = invoice.ServiceDate,
                TaskId = invoice.TaskId,
                TaxExclusivePrice = invoice.LineItems.Sum(x => x.Amount),
                TaxPrice = invoice.LineItems.Sum(x => currencyHandler.Round(x.TaxRates.Sum(y => y.Rate) / 100 * x.Amount)),
                TaxName = invoice.TaxName,
                TaxNumber = invoice.TaxNumber,
                CurrencyCode = invoice.CurrencyCode,
                Status = invoice.Status,
                History = invoice.History,
                IsBillingV2 = invoice.IsBillingV2,
                InvoiceLineItems = (invoice?.LineItems?.Select(li => li.ToDataModel(invoice)) ?? Enumerable.Empty<InvoiceLineItemDataModel>()).ToList(),
                LogoId = invoice.Theme.LogoId,
                ColorHex = invoice.Theme.ColorHex,
                Layout = invoice.Theme.Layout,
                ShowCodes = invoice.Theme.ShowCodes,
                ShowLineItemTax = invoice.Theme.ShowLineItemTax,
                ShowUnits = invoice.Theme.ShowUnits,
                CreditsUsed = invoice.CreditsUsed,
                CreatedDateTimeUtc = DateTime.UtcNow,
                LastUpdatedDateTimeUtc = DateTime.UtcNow,
                TaskInvoices = invoice.TaskIds.Distinct().Select(taskId => new InvoiceTaskDataModel(invoice.Id, taskId)).ToArray(),
            };
        }

        public static InvoiceDataModel WithProviderStaff(this InvoiceDataModel invoice, params InvoiceStaffDataModel[] staff)
        {
            invoice.Staff = staff;
            return invoice;
        }

        public static InvoiceDataModel WithProviderStaff(this InvoiceDataModel invoice, params Guid[] staff)
        {
            invoice.WithProviderStaff(staff.Select(x => new InvoiceStaffDataModel(invoice.Id, x)).ToArray());
            return invoice;
        }

        public static IEnumerable<InvoiceDataModel> WithProviderStaff(this IEnumerable<InvoiceDataModel> invoices, params Guid[] staff)
        {
            foreach (var invoice in invoices)
            {
                invoice.WithProviderStaff(staff.Select(x => new InvoiceStaffDataModel(invoice.Id, x)).ToArray());
                yield return invoice;
            }
        }

        public static InvoiceLineItemDataModel ToDataModel(this InvoiceLineItem lineItem, Invoice invoice)
        {
            return new InvoiceLineItemDataModel()
            {
                Id = lineItem.Id,
                InvoiceId = invoice.Id,
                CurrencyCode = lineItem.CurrencyCode,
                Price = lineItem.Price,
                SalesTaxRate = lineItem.TaxRates.Any() ? lineItem.TaxRates.Sum(x => x.Rate) : lineItem.SalesTaxRate,
                DiscountRate = lineItem.DiscountRate,
                Description = lineItem.Description,
                Detail = lineItem.Detail,
                Code = lineItem.Code,
                Date = lineItem.Date,
                POSCode = lineItem.POSCode,
                Units = lineItem.Units,
                TaxRates = lineItem.TaxRates.CloneTaxRates(),
                BillableItemId = lineItem.BillableItemId,
            };
        }


        public static InvoiceLineItemDataModel ToDataModel(this InvoiceLineItem lineItem)
        {
            return new InvoiceLineItemDataModel()
            {
                Id = lineItem.Id,
                InvoiceId = lineItem.InvoiceId,
                CurrencyCode = lineItem.CurrencyCode,
                Price = lineItem.Price,
                SalesTaxRate = lineItem.TaxRates.Any() ? lineItem.TaxRates.Sum(x => x.Rate) : lineItem.SalesTaxRate,
                DiscountRate = lineItem.DiscountRate,
                Description = lineItem.Description,
                Detail = lineItem.Detail,
                Code = lineItem.Code,
                Date = lineItem.Date,
                POSCode = lineItem.POSCode,
                Units = lineItem.Units,
                TaxRates = lineItem.TaxRates.CloneTaxRates(),
                BillableItemId = lineItem.BillableItemId,
            };
        }

        public static IList<InvoiceDataModel> ToDataModel(this IEnumerable<Invoice> invoices)
        {
            return invoices.Select(ToDataModel).ToList();
        }

        public static SimpleInvoice ToSimpleInvoice(this Invoice invoice)
        {
            return new SimpleInvoice()
            {
                Id = invoice.Id,
                ProviderId = invoice.ProviderId,
                ContactId = invoice.ContactId,
                Title = invoice.Title,
                Number = invoice.Number,
                Description = invoice.Description,
                DueDate = invoice.DueDate,
                IssueDate = invoice.IssueDate,
                PaymentDate = invoice.PaymentDate,
                POSONumber = invoice.POSONumber,
                ServiceDate = invoice.ServiceDate,
                TaskId = invoice.TaskId,
                TaxPrice = invoice.TaxPrice,
                TaxName = invoice.TaxName,
                TaxNumber = invoice.TaxNumber,
                CurrencyCode = invoice.CurrencyCode,
                Status = invoice.Status,
                History = invoice.History,
                ServiceReceiptIds = invoice.ServiceReceiptIds,
                VoidedDate = invoice.VoidedDate,
                IsBillingV2 = invoice.IsBillingV2
            };
        }

        public static InvoiceListEntry ToInvoiceListEntry(this Invoice invoice, Contact contact)
        {
            return new InvoiceListEntry()
            {
                Id = invoice.Id,
                ProviderId = invoice.ProviderId,
                ContactId = invoice.ContactId,
                Title = invoice.Title,
                Number = invoice.Number,
                Description = invoice.Description,
                DueDate = invoice.DueDate,
                IssueDate = invoice.IssueDate,
                PaymentDate = invoice.PaymentDate,
                POSONumber = invoice.POSONumber,
                ServiceDate = invoice.ServiceDate,
                TaskId = invoice.TaskId,
                TaxName = invoice.TaxName,
                TaxNumber = invoice.TaxNumber,
                CurrencyCode = invoice.CurrencyCode,
                Status = invoice.Status,
                History = invoice.History,
                ServiceReceiptIds = invoice.ServiceReceiptIds,
                IsBillingV2 = invoice.IsBillingV2,
                VoidedDate = invoice.VoidedDate,
                Contact = new InvoiceListEntryContact()
                {
                    Id = contact.Id,
                    Name = contact.FullName,
                    Email = contact.Email,
                    BusinessName = contact.BusinessName,
                    FirstName = contact.FirstName,
                    IsClient = contact.IsClient,
                    LastName = contact.LastName,
                    MiddleNames = contact.MiddleNames,
                    PersonId = contact.PersonId,
                    PhoneNumber = contact.PhoneNumber,
                },
            };
        }

        public static BillableInvoiceReference ToBillableInvoiceReference(this Invoice invoice)
        {
            return new BillableInvoiceReference
            {
                Id = invoice.Id,
                Number = invoice.Number,
                IssueDate = invoice.IssueDate,
                DueDate = invoice.DueDate,
                Status = invoice.Status,
            };
        }

        public static decimal Total(this InvoiceLineItemDataModel lineItem) => CurrencyHandler.Get(lineItem.CurrencyCode).Round(lineItem.Price * lineItem.Units * (1 + lineItem.SalesTaxRate / 100));
    }
}