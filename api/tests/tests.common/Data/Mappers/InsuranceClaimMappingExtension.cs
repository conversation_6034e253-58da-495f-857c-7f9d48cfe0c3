using carepatron.core.Application.Insurance.Models;
using carepatron.infra.sql.Models.ClearingHouse;
using carepatron.infra.sql.Models.Insurance;

namespace tests.common.Data.Mappers;

public static class ClaimClientMappingExtension
{
    public static InsuranceClaimClientDataModel ToDataModel(this ClaimClient model)
    {
        return new InsuranceClaimClientDataModel
        {
            Id = model.Id,
            ProviderId = model.ProviderId,
            ContactId = model.Contact.Id,
            FirstName = model.FirstName,
            LastName = model.LastName,
            MiddleName = model.MiddleName,
            Address = model.Address,
            DateOfBirth = model.DateOfBirth,
            PhoneNumber = model.PhoneNumberDetails,
            Sex = model.Sex
        };
    }
}

public static class ClaimIncidentMappingExtension
{
    public static InsuranceClaimIncidentDataModel ToDataModel(this ClaimIncident model)
    {
        return new InsuranceClaimIncidentDataModel()
        {
            IsConditionRelatedToAutoAccident = model.IsConditionRelatedToAutoAccident,
            IsConditionRelatedToEmployment = model.IsConditionRelatedToEmployment,
            IsConditionRelatedToOtherAccident = model.IsConditionRelatedToOtherAccident,
            AutoAccidentState = model.AutoAccidentState,
            CurrentIllnessDate = model.CurrentIllnessDate,
            CurrentIllnessQualifier = model.CurrentIllnessQualifier,
            OtherAssociatedDate = model.OtherAssociatedDate,
            OtherAssociatedQualifier = model.OtherAssociatedQualifier,
            UnableToWorkFrom = model.UnableToWorkFrom,
            UnableToWorkTo = model.UnableToWorkTo,
            HospitalizationFrom = model.HospitalizationFrom,
            HospitalizationTo = model.HospitalizationTo
        };
    }
}

public static class ClaimFacilityMappingExtension
{
    public static InsuranceClaimFacilityDataModel ToDataModel(this ClaimFacility model)
    {
        return new InsuranceClaimFacilityDataModel()
        {
            Id = model.Id,
            ProviderId = model.ProviderId,
            Address = model.Address,
            ProviderLocationId = model.ProviderLocationId,
            Name = model.Name,
            PlaceOfService = model.PlaceOfService,
            NationalProviderId = model.NationalProviderId,
            OtherIdQualifier = model.OtherIdQualifier,
            OtherId = model.OtherId
        };
    }
}

public static class ClaimBillingDetailMappingExtension
{
    public static InsuranceClaimBillingDetailDataModel ToDataModel(this ClaimBillingDetail model)
    {
        return new InsuranceClaimBillingDetailDataModel()
        {
            Id = model.Id,
            ProviderId = model.ProviderId,
            Address = model.Address,
            Name = model.Name,
            NationalProviderId = model.NationalProviderId,
            OtherIdQualifier = model.OtherIdQualifier,
            OtherId = model.OtherId,
            TaxNumber = model.TaxNumber,
            TaxNumberType = model.TaxNumberType,
            TaxonomyCode = model.TaxonomyCode,
            BillingProfileId = model.BillingProfile.Id,
            PhoneNumber = model.PhoneNumberDetails,
            Type = model.Type
        };
    }
}

public static class ClaimContactInsurancePolicyMappingExtension
{
    public static InsuranceClaimContactInsurancePolicyDataModel ToDataModel(
        this ClaimContactInsurancePolicy model
    )
    {
        return new InsuranceClaimContactInsurancePolicyDataModel()
        {
            Id = model.Id,
            ProviderId = model.ProviderId,
            Address = model.Address,
            PayerName = model.PayerName,
            PayerId = model.PayerId,
            PayerNumber = model.PayerNumber,
            InsuranceType = model.InsuranceType,
            ContactInsurancePolicyId = model.ContactInsurancePolicy.Id,
            PayerPhoneNumber = model.PayerPhoneNumberDetails,
            CoverageType = model.CoverageType,
            OtherCoverageTypeName = model.OtherCoverageTypeName,
            PolicyHolderMemberId = model.PolicyHolderMemberId,
            PolicyHolderGroupId = model.PolicyHolderGroupId,
            PolicyHolderSex = model.PolicyHolderSex,
            PolicyHolderPhoneNumber = model.PolicyHolderPhoneNumberDetails,
            PolicyHolderFirstName = model.PolicyHolderFirstName,
            PolicyHolderMiddleName = model.PolicyHolderMiddleName,
            PolicyHolderLastName = model.PolicyHolderLastName,
            PolicyHolderDateOfBirth = model.PolicyHolderDateOfBirth,
            PolicyHolderRelationshipType = model.PolicyHolderRelationshipType,
            PolicyHolderAddress = model.PolicyHolderAddress
        };
    }
}

public static class ClaimServiceLineMappingExtension
{
    public static InsuranceClaimServiceLineDataModel ToDataModel(this ClaimServiceLine model)
    {
        return new InsuranceClaimServiceLineDataModel()
        {
            Id = model.Id,
            ProviderId = model.ProviderId,
            BillableItemId = model.BillableItemId,
            Units = model.Units,
            Amount = model.Amount,
            TaxAmount = model.TaxAmount,
            CurrencyCode = model.CurrencyCode,
            DiagnosticCodeReferences = model.DiagnosticCodeReferences,
            EPSDT = model.EPSDT,
            FamilyPlanningService = model.FamilyPlanningService,
            Date = model.Date,
            Code = model.Code,
            Modifiers = model.Modifiers,
            POSCode = model.POSCode,
            Description = model.Description,
            Detail = model.Detail,
            ServiceId = model.ServiceId,
            SupplementalInfo = model.SupplementalInfo,
            Emergency = model.Emergency
        };
    }
}

public static class ClaimReferringProviderMappingExtension
{
    public static InsuranceClaimReferringProviderDataModel ToDataModel(
        this ClaimReferringProvider model
    )
    {
        return new InsuranceClaimReferringProviderDataModel()
        {
            Id = model.Id,
            ProviderId = model.ProviderId,
            NationalProviderId = model.NationalProviderId,
            OtherIdQualifier = model.OtherIdQualifier,
            OtherId = model.OtherId,
            FirstName = model.FirstName,
            LastName = model.LastName,
            MiddleName = model.MiddleName,
            Qualifier = model.Qualifier,
            IncludeReferrerInformation = model.IncludeReferrerInformation
        };
    }
}

public static class ClaimRenderingProviderMappingExtension
{
    public static InsuranceClaimRenderingProviderDataModel ToDataModel(
        this ClaimRenderingProvider model
    )
    {
        return new InsuranceClaimRenderingProviderDataModel()
        {
            Id = model.Id,
            ProviderId = model.ProviderId,
            NationalProviderId = model.NationalProviderId,
            OtherIdQualifier = model.OtherIdQualifier,
            OtherId = model.OtherId,
            FirstName = model.FirstName,
            LastName = model.LastName,
            MiddleName = model.MiddleName,
            StaffProviderId = model.StaffMember?.ProviderId,
            StaffPersonId = model.StaffMember?.PersonId
        };
    }
}

public static class ClaimDiagnosticCodeMappingExtension
{
    public static InsuranceClaimDiagnosticCodeDataModel ToDataModel(this ClaimDiagnosticCode model)
    {
        return new InsuranceClaimDiagnosticCodeDataModel()
        {
            Id = model.Id,
            ProviderId = model.ProviderId,
            Code = model.Code,
            Description = model.Description,
            Reference = model.Reference,
            OnsetDateFrom = model.OnsetDateFrom,
            OnsetDateTo = model.OnsetDateTo
        };
    }
}

public static class InsuranceClaimMappingExtension
{
    public static InsuranceClaimUSProfessionalDataModel ToDataModel(
        this InsuranceClaimUSProfessional model,
        Guid? existingId = null
    )
    {
        return new InsuranceClaimUSProfessionalDataModel()
        {
            Id = existingId ?? model.Id,
            ProviderId = model.ProviderId,
            Lab = model.Lab,
            LabCharges = model.LabCharges,
            ExportId = model.ExportId,
            IsExportValid = model.IsExportValid,
            PrintId = model.PrintId,
            IsPrintValid = model.IsPrintValid,
            OriginalReferenceNumber = model.OriginalReferenceNumber,
            PatientsAccountNumber = model.PatientsAccountNumber,
            PriorAuthorizationNumber = model.PriorAuthorizationNumber,
            ResubmissionCode = model.ResubmissionCode,
            Incident = model.Incident?.ToDataModel(),
            ServiceFacility = model.ServiceFacility?.ToDataModel(),
            BillingDetail = model.BillingDetail?.ToDataModel(),
            ReferringProviders = model.ReferringProviders?.Select(s => s.ToDataModel()).ToArray(),
            DiagnosticCodes = model.DiagnosticCodes?.Select(s => s.ToDataModel()).ToArray(),
            AdditionalClaimInformation = model.AdditionalClaimInformation,
            CreatedDateTimeUtc = model.CreatedDateTimeUtc,
            UpdatedDateTimeUtc = model.UpdatedDateTimeUtc
        };
    }

    public static InsuranceClaimDataModel ToClaimHeaderDataModel(
        this InsuranceClaimUSProfessional model,
        Guid? existingId = null
    )
    {
        return new InsuranceClaimDataModel()
        {
            Id = existingId ?? model.Id,
            ProviderId = model.ProviderId,
            Status = model.Status,
            Number = model.Number,
            ContactId = model.ContactId == Guid.Empty ? model.Client.Contact.Id : model.ContactId,
            Type = model.Type,
            SubmissionMethod = model.SubmissionMethod,
            Payments = [],
            LastSubmittedDateTimeUtc = model.LastSubmittedDateTimeUtc,
            FromDate = model.FromDate,
            ToDate = model.ToDate,
            CurrencyCode = model.CurrencyCode,
            CreatedDateTimeUtc = model.CreatedDateTimeUtc,
            UpdatedDateTimeUtc = model.UpdatedDateTimeUtc,
            Client = model.Client?.ToDataModel(),
            ContactInsurancePolicy = model.ContactInsurancePolicy?.ToDataModel(),
            ServiceLines = model.ServiceLines?.Select(s => s.ToDataModel()).ToArray(),
            RenderingProviders = model.RenderingProviders?.Select(s => s.ToDataModel()).ToArray(),
            Amount = model.Amount,
            AmountPaid = model.AmountPaid,
            ClientControlNumber = model.ClientControlNumber
        };
    }
    public static ClearingHouseIdLookupDataModel ToClearingHouseIdLookupDataModel(
        this InsuranceClaimUSProfessional model
    )
    {
        return new ClearingHouseIdLookupDataModel()
        {
            Identity = long.Parse(model.ClientControlNumber),
            EntityId = model.ContactId,
        };
    }
}
