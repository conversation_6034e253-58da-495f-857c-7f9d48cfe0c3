using carepatron.core.Application.Insurance.Models;
using carepatron.infra.sql.Models.Insurance;

namespace tests.common.Data.Mappers;

public static class InsuranceRemittancePaymentMappingExtensions
{
    public static InsuranceRemittancePaymentDataModel ToDataModel(this InsuranceRemittancePayment domain)
    {
        return new InsuranceRemittancePaymentDataModel()
        {
            Id = domain.Id,
            ProviderId = domain.ProviderId,
            InsuranceRemittanceAdviceId = domain.InsuranceRemittanceAdvice.Id,
            PaymentId = domain.Payment.Id
        };
    }
}