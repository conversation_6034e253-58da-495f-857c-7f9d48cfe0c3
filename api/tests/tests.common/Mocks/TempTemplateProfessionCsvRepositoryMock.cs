﻿using carepatron.core.Repositories.Templates;
using Moq;

namespace tests.common.Mocks
{
    public class TempTemplateProfessionCsvRepositoryMock : Mock<ITempTemplateProfessionCsvRepository>
    {
        private readonly TempTemplateProfessionCsvRepository tempTemplateProfessionCsvRepository = new();

        public void UseRealImplementation()
        {
            Setup(x => x.GetTempTemplateProfessions(It.IsAny<int>(), It.IsAny<int>()))
                .Returns((int offset, int limit) =>
                {
                    return tempTemplateProfessionCsvRepository.GetTempTemplateProfessions(offset, limit);
                });
        }
    }
}
