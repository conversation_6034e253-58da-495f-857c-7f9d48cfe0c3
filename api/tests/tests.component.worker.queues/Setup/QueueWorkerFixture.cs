using System.Collections.Generic;
using System.Linq;
using Agents.Module;
using Bogus;
using carepatron.core.Abstractions;
using carepatron.core.Application.Communications.Inbox.Abstractions;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Configuration;
using carepatron.core.Events.Events;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.Models.Call;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Models.Storage;
using carepatron.core.Repositories.Files;
using carepatron.core.Repositories.SQS;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Repositories.Templates;
using carepatron.core.Scheduler.Abstractions;
using carepatron.core.Services;
using carepatron.workers.queue;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Notifications.Sdk.Client.Abstract;
using tests.common.Extensions;
using tests.common.Fixtures;
using tests.common.Mocks;
using tests.common.StripeFakes;

namespace tests.component.worker.queues.Setup;

public class QueueWorkerFixture : BaseWorkerFixture
{
    public Mock<ISqsRepository> SqsRepository { get; set; }
    public Mock<INotificationsService> NotificationsService { get; private set;}

    public QueueWorkerFixture()
    {
        CreateTestServer(
            Program.CreateHostBuilder(Array.Empty<string>()),
            services =>
            {
                var stripeFixture = new StripeTestFixture();
                services.AddSingleton(stripeFixture);
                services.AddStripeServiceMocks(stripeFixture);

                services.AddScoped<ICallService, CallService>();
                services.AddScopedMock<ISqsRepository, Mock<ISqsRepository>>();
                services.AddScopedMock<ICallProvider, Mock<ICallProvider>>();
                services.AddScopedMock<IConnectedInboxRepository, GmailInboxRepositoryMock>();
                services.AddScopedMock<IFileStorageRepository, FileStorageRepositoryMock>();
                services.AddScopedMock<ITempTemplateProfessionCsvRepository, TempTemplateProfessionCsvRepositoryMock>();
                services.AddScopedMock<IConnectedCalendarRepository, ConnectedCalendarRepositoryMock>();
                services.AddScopedMock<IScheduler<EventMessage>, TaskQueueTargettingSchedulerMock>();
                services.AddScopedMock<IIntegrationEventPublisher, EventPublisherMock>();
                services.AddSingleton<ConnectedCalendarServiceMock>();
                services.AddScoped<IConnectedCalendarService>(ctx => ctx.GetService<ConnectedCalendarServiceMock>().Object);

                services.AddScopedMock<INotificationsService, Mock<INotificationsService>>();

                services.AddScopedMock<IFileStorageService, GoogleFileStorageServiceMock>();
                services.AddScopedMock<IFileStorageService, AwsFileStorageServiceMock>();

                services.AddScopedMock<IAiTemplatesConfiguration, AiTemplatesConfigurationMock>();

                services.AddScopedMock<IAgentClient, AgentClientMock>();

                services.AddScoped<IEnumerable<IConnectedInboxRepository>>(ctx =>
                    [ctx.GetService<GmailInboxRepositoryMock>().Object]);
                services.AddScoped((Func<IServiceProvider, FileStorageServiceFactory>)(serviceProvider =>
                    provider =>
                    {
                        switch (provider)
                        {
                            case StorageProvider.Google:
                                return serviceProvider.GetService<GoogleFileStorageServiceMock>().Object;
                            case StorageProvider.AWS:
                            default:
                                return serviceProvider.GetService<AwsFileStorageServiceMock>().Object;
                        }
                    }));


                services.AddScoped((Func<IServiceProvider, CallProviderFactory>)(serviceProvider => (CallProvider provider) =>
                {
                    return serviceProvider.GetRequiredService<ICallProvider>();
                }));
            }

        );

        SqsRepository = TestServer.Services.GetService<Mock<ISqsRepository>>();
        NotificationsService = TestServer.Services.GetService<Mock<INotificationsService>>();
    }

    public void QueueMessage<T>(QueueType type, T body)
    {
        var queueMessage = new QueueMessage(
            type,
            null,
            messageId: new Faker().Random.Guid().ToString(),
            null,
            data: body
        );
        SqsRepository
            .Setup(x => x.GetMessages(type, It.IsAny<int>(), It.IsAny<bool>()))
            .ReturnsAsync([queueMessage]);

    }

    public void QueueMessage<T>(QueueType type, T[] messages)
    {
        var queueMessages = messages.Select(body =>
            new QueueMessage(
                type,
                null,
                messageId: new Faker().Random.Guid().ToString(),
                null,
                data: body
            )
        ).ToArray();
        SqsRepository
            .Setup(x => x.GetMessages(type, It.IsAny<int>(), It.IsAny<bool>()))
            .ReturnsAsync(queueMessages);

    }
}