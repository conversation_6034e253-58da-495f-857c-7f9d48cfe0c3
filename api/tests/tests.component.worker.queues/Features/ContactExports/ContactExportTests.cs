using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using Bogus;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Schema.Models.Properties;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Models.Permissions;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Utilities;
using carepatron.infra.s3.Factory;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Fixtures;
using tests.common.Mocks;
using tests.component.worker.queues.Setup;

namespace tests.component.worker.queues.Features.ContactExports
{
    [Trait(nameof(CodeOwner), CodeOwner.Clients)]
    public class ContactExportTests : WorkerComponentTestBase<QueueWorkerFixture>
    {
        public ContactExportTests(QueueWorkerFixture fixture) : base(fixture)
        {
            var fileStorageRepositoryMock = Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock.UseRealImplementation();
        }

        [Fact]
        public async Task ContactExportNotification_Should_Succeed()
        {
            var exportSummary = new ContactExportSummaryFaker(ProviderId, PersonId)
                .RuleFor(x => x.Status, ExportSummaryStatus.Successful)
                .Generate()
                .ToDataModel();

            await DataContext.AddAsync(exportSummary);

            await DataContext.SaveChangesAsync();

            var person = await DataContext.Persons.Where(i => i.Id == PersonId).FirstOrDefaultAsync();

            var evtData = new ExportContactsCompletedEvent(
                exportSummary.Id,
                exportSummary.CreatedByPersonId,
                exportSummary.Status
            );

            var ev = new EventMessage(
                EventType.ExportContactsCompleted,
                new EventData<ExportContactsCompletedEvent>(evtData),
                $"{ProviderId}-export-contacts",
                PersonId
            );

            Fixture.QueueMessage(QueueType.Task, ev);

            await Fixture.Run();
        }

        [Fact]
        public async Task ContactExport_ByContactIds_Should_Succeed_Test()
        {
            var contacts = new ContactFaker(ProviderId).Generate(3).ToDataModel();

            var exportSummary = new ContactExportSummaryFaker(ProviderId, PersonId)
                .RuleFor(x => x.ContactIds, new[] { contacts[0].Id, contacts[1].Id })
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(contacts);
            await DataContext.AddAsync(exportSummary);
            await DataContext.SaveChangesAsync();

            var evtData = new ExportContactsEvent(
                exportSummary.Id,
                PersonId,
                StandardPermission.None
            );

            var eventMessage = new EventMessage(
                EventType.ExportContacts,
                new EventData<ExportContactsEvent>(evtData),
                $"{ProviderId}-export-contacts",
                PersonId
            );
            Fixture.QueueMessage(QueueType.Task, eventMessage);
            await Fixture.Run();

            var summary = await DataContext.ContactExportSummaries.AsNoTracking().FirstOrDefaultAsync(x => x.Id == exportSummary.Id);
            summary.Status.Should().Be(ExportSummaryStatus.Successful);
            summary.FileId.Should().NotBeEmpty().And.NotBeNull();

            var csvData = await ReadCsv(summary.FileId.Value);
            csvData.Should().HaveCount(2);

            var contactIds = csvData.Select(x => x["Id"]).ToList();
            contactIds.Should().Contain(contacts[0].Id.ToString());
            contactIds.Should().Contain(contacts[1].Id.ToString());
        }

        [Fact]
        public async Task ContactExport_By_BulkFilter_Should_Succeed_Test()
        {
            var tagFaker = new TagFaker();
            var tags = tagFaker.WithProviderId(ProviderId).Generate(2);

            var contactFaker = new ContactFaker(ProviderId);
            var contact1 = contactFaker
                .RuleFor(x => x.FirstName, "John")
                .RuleFor(x => x.LastName, "Smith")
                .Generate()
                .ToDataModel();
            var contact2 = contactFaker
                .RuleFor(x => x.FirstName, "John")
                .RuleFor(x => x.LastName, "Johnson")
                .Generate()
                .ToDataModel();
            var contact3 = contactFaker
                .WithTags(tags[0])
                .RuleFor(x => x.FirstName, "John")
                .RuleFor(x => x.LastName, "Williams")
                .Generate()
                .ToDataModel();
            var contact4 = contactFaker
                .WithTags([.. tags])
                .WithAssignedStaff(Data.StaffMember)
                .WithStatus("Active")
                .RuleFor(x => x.FirstName, "John")
                .RuleFor(x => x.LastName, "Brown")
                .Generate()
                .ToDataModel();
            var contact5 = contactFaker
                .WithTags(tags[1])
                .WithAssignedStaff(Data.StaffMember)
                 .WithStatus("Inactive")
                .RuleFor(x => x.FirstName, "John")
                .RuleFor(x => x.LastName, "Jones")
                .Generate()
                .ToDataModel();
            var contact6 = contactFaker
                .RuleFor(x => x.FirstName, "Jane")
                .RuleFor(x => x.LastName, "Taylor")
                .Generate()
                .ToDataModel();

            var bulkFilter = new BulkContactFilter
            {
                AllClients = true,
                SearchTerm = "John",
                Tag = [.. tags.Select(t => t.Id)],
                AssignedStaff = new[] { Data.StaffMember.Id },
                Statuses = new[] { "Active" }
            };

            var exportSummary = new ContactExportSummaryFaker(ProviderId, PersonId)
                .RuleFor(x => x.BulkContactFilter, bulkFilter)
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(tags.ToDataModel());
            await DataContext.AddRangeAsync(contact1, contact2, contact3, contact4, contact5, contact6);
            await DataContext.AddAsync(exportSummary);
            await DataContext.SaveChangesAsync();

            var evtData = new ExportContactsEvent(
                exportSummary.Id,
                PersonId,
                StandardPermission.None
            );

            var eventMessage = new EventMessage(
                EventType.ExportContacts,
                new EventData<ExportContactsEvent>(evtData),
                $"{ProviderId}-export-contacts",
                PersonId
            );
            Fixture.QueueMessage(QueueType.Task, eventMessage);
            await Fixture.Run();

            var summary = await DataContext.ContactExportSummaries.AsNoTracking().FirstOrDefaultAsync(x => x.Id == exportSummary.Id);
            summary.Status.Should().Be(ExportSummaryStatus.Successful);
            summary.FileId.Should().NotBeEmpty().And.NotBeNull();

            var csvData = await ReadCsv(summary.FileId.Value);
            csvData.Should().HaveCount(1);

            csvData[0]["Id"].Should().Be(contact4.Id.ToString());
        }

        [Fact]
        public async Task ContactExport_Should_Fail_If_No_Contacts_Test()
        {
            var exportSummary = new ContactExportSummaryFaker(ProviderId, PersonId)
                .RuleFor(x => x.ContactIds, new[] { Guid.NewGuid() })
                .Generate()
                .ToDataModel();

            await DataContext.AddAsync(exportSummary);
            await DataContext.SaveChangesAsync();

            var evtData = new ExportContactsEvent(
                exportSummary.Id,
                PersonId,
                StandardPermission.None
            );

            var eventMessage = new EventMessage(
                EventType.ExportContacts,
                new EventData<ExportContactsEvent>(evtData),
                $"{ProviderId}-export-contacts",
                PersonId
            );
            Fixture.QueueMessage(QueueType.Task, eventMessage);
            await Fixture.Run();

            var summary = await DataContext.ContactExportSummaries.AsNoTracking().FirstOrDefaultAsync(x => x.Id == exportSummary.Id);
            summary.Status.Should().Be(ExportSummaryStatus.Failed);
            summary.FileId.Should().BeNull();
        }

        [Fact]
        public async Task ContactExport_Should_Mark_As_Failed_When_No_Contacts_Test()
        {
            var exportSummary = new ContactExportSummaryFaker(ProviderId, PersonId)
                .RuleFor(x => x.ContactIds, new[] { Guid.NewGuid() })
                .Generate()
                .ToDataModel();

            await DataContext.AddAsync(exportSummary);
            await DataContext.SaveChangesAsync();

            var evtData = new ExportContactsEvent(
                exportSummary.Id,
                PersonId,
                StandardPermission.None
            );

            var eventMessage = new EventMessage(
                EventType.ExportContacts,
                new EventData<ExportContactsEvent>(evtData),
                $"{ProviderId}-export-contacts",
                PersonId
            );
            Fixture.QueueMessage(QueueType.Task, eventMessage);
            await Fixture.Run();

            var summary = await DataContext.ContactExportSummaries.AsNoTracking().FirstOrDefaultAsync(x => x.Id == exportSummary.Id);
            summary.Status.Should().Be(ExportSummaryStatus.Failed);
            summary.FileId.Should().BeNull();
        }

        [Fact]
        public async Task ContactExport_Should_Export_Correct_Values_Test()
        {
            var customFields = GetCustomFields();
            var dataSchema = new DataSchemaFaker(SchemaTypes.ContactSchema, ProviderId, customFields)
                .Generate()
                .ToDataModel();

            var layoutSchema = new LayoutSchemaFaker(SchemaTypes.ContactSchemaLayoutView,
                    SchemaTypes.ContactSchema,
                    ProviderId,
                    [
                        new LayoutContainer
                        {
                            Heading = "Name",
                            Width = 6,
                            Elements =
                            [
                                new LayoutControl { Property = "FirstName", Width = 4 },
                                new LayoutControl { Property = "MiddleNames", Width = 4 },
                                new LayoutControl { Property = "LastName", Width = 4 },
                            ]
                        },
                        new LayoutContainer
                        {
                            Heading = "Contact Details",
                            Width = 6,
                            Elements =
                            [
                                new LayoutControl { Property = "PhoneNumber", Width = 4 },
                                new LayoutControl { Property = "Email", Width = 4 }
                            ]
                        },
                        new LayoutContainer
                        {
                            Heading = "Custom Section 1",
                            Width = 6,
                            Elements =
                            [
                                new LayoutControl { Property = "some-address", Width = 4 },
                                new LayoutControl { Property = "some-string-1", Width = 4 },
                                new LayoutControl { Property = "some-string-2", Width = 4 },
                                new LayoutControl { Property = "some-string-3", Width = 4 },
                                new LayoutControl { Property = "some-email", Width = 4 },
                            ]
                        },
                        new LayoutContainer
                        {
                            Heading = "Custom Section 2",
                            Width = 6,
                            Elements =
                            [
                                new LayoutControl { Property = "some-boolean", Width = 4 },
                                new LayoutControl { Property = "some-date", Width = 4 },
                                new LayoutControl { Property = "some-date-range", Width = 4 },
                            ]
                        }
                    ])
                .Generate()
                .ToDataModel();


            var address = new AddressFaker().Generate();
            var faker = new Faker();
            var email = faker.Internet.Email();

            var contact = new ContactFaker(ProviderId)
                .WithEthnicities("Green", "Blue")
                .WithFields(new Dictionary<string, object>()
                {
                    { "some-boolean", true },
                    { "some-date", new DateOnly(2021, 5, 10) },
                    {
                        "some-date-range", new DateRangePropertyValue
                        {
                            StartDate = new DateTime(2021, 5, 10),
                            EndDate = new DateTime(2021, 7, 12)
                        }
                    },
                    { "some-option", "Option 1" },
                    { "some-option-multi", new[] { "Option 1", "Option 2" } },
                    { "some-option-v2", new OptionSetValue { Id = "1", DisplayName = "Option 1 v2" } },
                    {
                        "some-option-multi-v2", new[]
                        {
                            new OptionSetValue { Id = "1", DisplayName = "Option 1 v2" },
                            new OptionSetValue { Id = "2", DisplayName = "Option 2 v2" }
                        }
                    },
                    { "some-address", address },
                    { "some-string-1", "String 1" },
                    { "some-string-2", null },
                    { "some-string-3", "Some value 3" },
                    { "some-email", email },
                    { "some-number", 123 },
                    { "some-phone-number", "123456789" },
                    { "LastName", "Super Foo Bar" }
                })
                .Generate();

            var exportSummary = new ContactExportSummaryFaker(ProviderId, PersonId)
                .RuleFor(x => x.ContactIds, new[] { contact.Id })
                .Generate()
                .ToDataModel();

            await DataContext.AddRangeAsync(contact.ToDataModel(), exportSummary, dataSchema, layoutSchema);
            await DataContext.SaveChangesAsync();

            var evtData = new ExportContactsEvent(
                exportSummary.Id,
                PersonId,
                StandardPermission.None
            );

            var eventMessage = new EventMessage(
                EventType.ExportContacts,
                new EventData<ExportContactsEvent>(evtData),
                $"{ProviderId}-export-contacts",
                PersonId
            );
            Fixture.QueueMessage(QueueType.Task, eventMessage);
            await Fixture.Run();

            var summary = await DataContext.ContactExportSummaries
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == exportSummary.Id);
            summary.Status.Should().Be(ExportSummaryStatus.Successful);
            summary.FileId.Should().NotBeEmpty().And.NotBeNull();

            var csvData = await ReadCsv(summary.FileId.Value);
            csvData.Should().HaveCount(1);

            var contactCsv = csvData[0];
            contactCsv["Id"].Should().Be(contact.Id.ToString());
            contactCsv["First name"].Should().Be(contact.FirstName);
            contactCsv["Last Name as Phone Number"].Should().Be(contact.LastName);
            contactCsv["Preferred name"].Should().Be(contact.PreferredName);
            contactCsv["Date of birth"].Should().Be(contact.BirthDate.Value.ToString("yyyy-MM-dd"));
            contactCsv["Gender"].Should().Be(contact.Gender);
            contactCsv["Ethnicity"].Should().Be("Green,Blue");
            contactCsv["Phone number"].Should().Be(contact.PhoneNumber);
            contactCsv["Email"].Should().Be(contact.Email.ToString());
            contactCsv["Address"].Should().Be(contact.Address);
            contactCsv["Identification Number"].Should().Be(contact.IdentificationNumber);
            contactCsv["Occupation"].Should().Be(contact.Occupation);
            contactCsv["Employment Status"].Should().Be(contact.EmploymentStatus);
            contactCsv["Living Arrangements"].Should().Be(contact.LivingArrangements);
            contactCsv["Relationship Status"].Should().Be(contact.RelationshipStatus);
            contactCsv["Status"].Should().Be(contact.Status);

            contactCsv["Some Date"].Should().Be(new DateOnly(2021, 5, 10).ToString("yyyy-MM-dd"));
            contactCsv["Some Email"].ToLower().Should().Be(email.ToLower());
            contactCsv["Some Number"].Should().Be("123");
            contactCsv["Some Option"].Should().Be("Option 1");
            contactCsv["Some Option Multi"].Should().Be("Option 1,Option 2");
            contactCsv["Some Option v2"].Should().Be("Option 1 v2");
            contactCsv["Some Option Multi v2"].Should().Be("Option 1 v2,Option 2 v2");
            contactCsv["Some Address"].Should().Be(address.ToString());
            contactCsv["Some Boolean"].Should().Be("True");
            contactCsv["Some String 1"].Should().Be("String 1");
            contactCsv["Some String 2"].Should().BeEmpty();
            contactCsv["Some String 1 1"].Should().Be("Some value 3");
            contactCsv["Some Date Range"].Should().Be($"{new DateTime(2021, 5, 10).ToString("yyyy-MM-dd")} - {new DateTime(2021, 7, 12).ToString("yyyy-MM-dd")}");
            contactCsv["Some Phone Number"].Should().Be("123456789");

            // assert columns order
            var headers = contactCsv.Keys.Take(14);
            headers.Should().ContainInOrder(
                "Id", "First name", "Middle name", "Last Name as Phone Number",
                "Phone number", "Email",
                "Some Address", "Some String 1", "Some String 2", "Some String 1 1", "Some Email",
                "Some Boolean", "Some Date", "Some Date Range"
            );
        }

        private Dictionary<string, Property> GetCustomFields()
        {
            return new Dictionary<string, Property>
            {
                { "some-boolean", new BooleanProperty() { DisplayName = "Some Boolean" } },
                { "some-date", new DateProperty() { DisplayName = "Some Date" } },
                { "some-date-range", new DateRangeProperty() { DisplayName = "Some Date Range" } },
                { "some-option", new OptionSetProperty() { DisplayName = "Some Option" } },
                { "some-option-multi", new OptionSetProperty() { DisplayName = "Some Option Multi" , Multiple = true} },
                { "some-option-v2", new OptionSetV2Property() { DisplayName = "Some Option v2" } },
                { "some-option-multi-v2", new OptionSetV2Property() { DisplayName = "Some Option Multi v2", Multiple = true } },
                { "some-address", new AddressProperty() { DisplayName = "Some Address" } },
                { "some-string-1", new StringProperty() { DisplayName = "Some String 1" } },
                { "some-string-2", new StringProperty() { DisplayName = "Some String 2" } },
                { "some-string-3", new StringProperty() { DisplayName = "Some String 1" } },
                { "some-email", new EmailProperty() { DisplayName = "Some Email" } },
                { "some-number", new NumberProperty() { DisplayName = "Some Number" } },
                { "some-phone-number", new PhoneNumberProperty() { DisplayName = "Some Phone Number" } },
                { "LastName", new PhoneNumberProperty() { DisplayName = "Last Name as Phone Number" } },
            };
        }

        private async Task<List<Dictionary<string, string>>> ReadCsv(Guid fileId, int maxRows = 10)
        {
            var zipData = await DownloadFile(GetFileUri(fileId));
            using (MemoryStream zipStream = new MemoryStream(zipData))
            {
                using (ZipArchive archive = new ZipArchive(zipStream))
                {
                    ZipArchiveEntry csvEntry = archive.GetEntry("carepatron-clients.csv");
                    if (csvEntry != null)
                    {
                        using var memStream = new MemoryStream();
                        csvEntry.Open().CopyTo(memStream);
                        memStream.Seek(0, SeekOrigin.Begin);

                        var (_, rows) = SpreadsheetUtilities.ParseToContactImportRows(memStream, "file.csv", maxRows);
                        return rows;
                    }
                }
            }
            return null;
        }

        private async Task<byte[]> DownloadFile(Uri uri)
        {
            using var client = new HttpClient();
            return await client.GetByteArrayAsync(uri);
        }

        private Uri GetFileUri(Guid fileId)
        {
            var s3Factory = Fixture.ResolveService<IAmazonS3ClientFactory>();
            var s3Config = s3Factory.GetBucketDefinition(FileLocationType.ClientExport);
            var host = s3Config.ServiceURL;
            if (host.EndsWith("/"))
            {
                host = host.Substring(0, host.Length - 1);
            }
            return new Uri($"{host}/{s3Config.BucketName}/{fileId}");
        }
    }
}