﻿using Bogus;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Models.QueueMessages;
using FluentAssertions;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using System.Linq;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Fixtures;
using tests.common.Mocks;
using tests.component.worker.queues.Setup;

namespace tests.component.worker.queues.Features.Templates;

[Trait(nameof(CodeOwner), CodeOwner.NotesAndDocuments)]
public class ValidatePublicTemplateProfessionsTests : WorkerComponentTestBase<QueueWorkerFixture>
{
    private readonly TempTemplateProfessionCsvRepositoryMock templateProfessionRepositoryMock;

    public ValidatePublicTemplateProfessionsTests(QueueWorkerFixture fixture) : base(fixture)
    {
        templateProfessionRepositoryMock =
            Fixture.TestServer.Services.GetService<TempTemplateProfessionCsvRepositoryMock>();
    }

    [Fact]
    public async Task ValidatePublicTemplateProfessionsTests_ValidateOnly_ShouldProcessQueueMessage()
    {
        var oldProfessions = new[]
        {
            "Social Worker",
            "General Practitioner"
        };

        var csvCount = 5;

        var publicTemplates = new PublicTemplateFaker()
            .RuleFor(x => x.Professions, oldProfessions)
            .Generate(csvCount)
            .ToDataModel();

        var newAuthor = new PersonFaker()
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(publicTemplates);
        await DataContext.AddRangeAsync(newAuthor);
        await DataContext.SaveChangesAsync();

        var templateProfessions = publicTemplates
            .Select(x => new Faker<TempTemplateProfession>().RuleFor(x => x.PublicTemplateId, x.Id)
                .RuleFor(x => x.PersonId, newAuthor.Id).Generate())
            .ToArray();

        templateProfessionRepositoryMock.Setup(x => x.GetTempTemplateProfessions(0, csvCount))
            .ReturnsAsync(templateProfessions);

        var ev = new EventMessage(
            EventType.ValidatePublicTemplateProfessions,
            new EventData<ValidatePublicTemplateProfessionsEventData>(
                new ValidatePublicTemplateProfessionsEventData(csvCount)
            ),
            null,
            Guid.NewGuid()
        );

        Fixture.QueueMessage(QueueType.Task, ev);
        await Fixture.Run();

        var tempTemplateProfessions = await DataContext.TempPublicTemplateProfessions
            .AsNoTracking()
            .AnyAsync(x => publicTemplates.Select(y => y.Id).Contains(x.PublicTemplateId));

        tempTemplateProfessions.Should().BeFalse();
    }

    [Fact]
    public async Task ValidatePublicTemplateProfessionsTests_ValidateAndBackupOnly_ShouldProcessQueueMessage()
    {
        var oldProfessions = new[]
        {
            "Social Worker",
            "General Practitioner"
        };

        var csvCount = 5;

        var publicTemplates = new PublicTemplateFaker()
            .RuleFor(x => x.Professions, oldProfessions)
            .Generate(csvCount)
            .ToDataModel();

        var newAuthor = new PersonFaker()
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(publicTemplates);
        await DataContext.AddRangeAsync(newAuthor);
        await DataContext.SaveChangesAsync();

        var templateProfessions = publicTemplates
            .Select(x => new Faker<TempTemplateProfession>().RuleFor(x => x.PublicTemplateId, x.Id)
                .RuleFor(x => x.PersonId, newAuthor.Id).Generate())
            .ToArray();

        templateProfessionRepositoryMock.Setup(x => x.GetTempTemplateProfessions(0, csvCount))
            .ReturnsAsync(templateProfessions);

        var ev = new EventMessage(
            EventType.ValidatePublicTemplateProfessions,
            new EventData<ValidatePublicTemplateProfessionsEventData>(
                new ValidatePublicTemplateProfessionsEventData(csvCount, true)
            ),
            null,
            Guid.NewGuid()
        );

        Fixture.QueueMessage(QueueType.Task, ev);
        await Fixture.Run();

        var tempTemplateProfessions = await DataContext.TempPublicTemplateProfessions
            .AsNoTracking()
            .Where(x => publicTemplates.Select(y => y.Id).Contains(x.PublicTemplateId))
            .ToArrayAsync();

        tempTemplateProfessions.Should().NotBeEmpty();

        publicTemplates.ForEach(x =>
        {
            var professions = tempTemplateProfessions
                .Where(y => y.PublicTemplateId == x.Id)
                .Select(y => y.Profession)
                .ToArray();

            x.Professions.Select(x => x.Profession).Should().BeEquivalentTo(professions, opt => opt.WithoutStrictOrdering());
        });
    }

    [Fact]
    public async Task ValidatePublicTemplateProfessionsTests_ValidateOnly_EmptyProfessions_ShouldProcessQueueMessage()
    {
        var csvCount = 5;

        var publicTemplates = new PublicTemplateFaker()
            .RuleFor(x => x.Professions, [])
            .Generate(csvCount)
            .ToDataModel();

        var newAuthor = new PersonFaker()
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(publicTemplates);
        await DataContext.AddRangeAsync(newAuthor);
        await DataContext.SaveChangesAsync();

        var templateProfessions = publicTemplates
            .Select(x => new Faker<TempTemplateProfession>().RuleFor(x => x.PublicTemplateId, x.Id)
                .RuleFor(x => x.PersonId, newAuthor.Id).Generate())
            .ToArray();

        templateProfessionRepositoryMock.Setup(x => x.GetTempTemplateProfessions(0, csvCount))
            .ReturnsAsync(templateProfessions);

        var ev = new EventMessage(
            EventType.ValidatePublicTemplateProfessions,
            new EventData<ValidatePublicTemplateProfessionsEventData>(
                new ValidatePublicTemplateProfessionsEventData(csvCount, true)
            ),
            null,
            Guid.NewGuid()
        );

        Fixture.QueueMessage(QueueType.Task, ev);
        await Fixture.Run();

        var tempTemplateProfessions = await DataContext.TempPublicTemplateProfessions
            .AsNoTracking()
            .Where(x => publicTemplates.Select(y => y.Id).Contains(x.PublicTemplateId))
            .ToArrayAsync();

        tempTemplateProfessions.Should().BeEmpty();
    }
}