﻿using carepatron.core.Application.Templates.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Extensions;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Repositories.SQS;
using carepatron.infra.sql.Models.Templates;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using System.Linq;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Fixtures;
using tests.common.Mocks;
using tests.component.worker.queues.Setup;

namespace tests.component.worker.queues.Features.Templates;

[Trait(nameof(CodeOwner), CodeOwner.NotesAndDocuments)]
public class UpdatePublicTemplateProfessionsTests : WorkerComponentTestBase<QueueWorkerFixture>
{
    private readonly TempTemplateProfessionCsvRepositoryMock _templateProfessionRepositoryMock;
    private readonly Mock<ISqsRepository> sqsRepositoryMock;

    public UpdatePublicTemplateProfessionsTests(QueueWorkerFixture fixture) : base(fixture)
    {
        _templateProfessionRepositoryMock =
            Fixture.TestServer.Services.GetService<TempTemplateProfessionCsvRepositoryMock>();

        sqsRepositoryMock = Fixture.TestServer.Services.GetService<Mock<ISqsRepository>>();

        sqsRepositoryMock.Invocations.Clear();
    }

    [Fact]
    public async Task Should_Update_Profession_And_Author_If_Dry_Run_False_Test()
    {
        var oldProfessions = new[]
        {
            "Social Worker",
            "General Practitioner"
        };

        var publicTemplate = new PublicTemplateFaker()
            .RuleFor(x => x.Professions, oldProfessions)
            .Generate()
            .ToDataModel();

        var newAuthor = new PersonFaker()
            .Generate()
            .ToDataModel();

        var backUp = oldProfessions
            .Select(x => new TempPublicTemplateProfessionDataModel(publicTemplate.Id, x))
            .ToList();

        await DataContext.AddRangeAsync(backUp);
        await DataContext.AddRangeAsync(publicTemplate, newAuthor);
        await DataContext.SaveChangesAsync();

        var professions = new[]
        {
            "Social worker",
            "Behavioral health Therapy",
            "Psychology",
            "Addiction Counselor",
            "Clinical Psychologist",
            "Counselor",
            "Rehabilitation Counselor",
            "Therapist"
        };

        var tempTemplateProfession = new TempTemplateProfession
        {
            Professions = professions.Select(x => x.ToPascalCase()).ToArray(),
            FullName = newAuthor.FullName,
            PersonId = newAuthor.Id,
            PublicTemplateId = publicTemplate.Id
        };

        _templateProfessionRepositoryMock.Setup(x => x.GetTempTemplateProfessions(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync([tempTemplateProfession]);

        // Act
        var ev = new EventMessage(
            EventType.UpdatePublicTemplateProfessions,
            new EventData<UpdatePublicTemplateProfessionsEventData>(
                new UpdatePublicTemplateProfessionsEventData(0, 0, 20, false)
            ),
            null,
            Guid.NewGuid()
        );

        Fixture.QueueMessage(QueueType.Task, ev);
        await Fixture.Run();

        // Assert
        publicTemplate = await DataContext.PublicTemplates.Include(x => x.Professions).AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == publicTemplate.Id);

        publicTemplate.Should().NotBeNull();

        publicTemplate.Author.Name.Should().Be(newAuthor.FullName);
        publicTemplate.Author.PersonId.Should().Be(newAuthor.Id);
        publicTemplate.Professions.Select(x => x.Profession).Should().BeEquivalentTo(tempTemplateProfession.Professions, opt => opt.WithoutStrictOrdering());

        sqsRepositoryMock.Verify(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>()), Times.Once);
    }

    [Fact]
    public async Task Should_Not_Queue_Next_Message_If_No_Records_Test()
    {
        var oldProfessions = new[]
        {
            "Social Worker",
            "General Practitioner"
        };

        var oldPerson = new PersonFaker()
            .Generate()
            .ToDataModel();

        var oldAuthor = new Author
        {
            PersonId = oldPerson.Id,
            Name = oldPerson.FullName,
            ProviderId = ProviderId,
            ProviderName = "test"
        };

        var publicTemplate = new PublicTemplateFaker()
            .RuleFor(x => x.Professions, oldProfessions)
            .RuleFor(x => x.Author, oldAuthor)
            .Generate()
            .ToDataModel();

        var newAuthor = new PersonFaker()
            .Generate()
            .ToDataModel();

        var backUp = oldProfessions
            .Select(x => new TempPublicTemplateProfessionDataModel(publicTemplate.Id, x))
            .ToList();

        await DataContext.AddRangeAsync(backUp);
        await DataContext.AddRangeAsync(publicTemplate, oldPerson, newAuthor);
        await DataContext.SaveChangesAsync();

        var professions = new[]
        {
            "Social worker",
            "Behavioral health Therapy",
            "Psychology",
            "Addiction Counselor",
            "Clinical Psychologist",
            "Counselor",
            "Rehabilitation Counselor",
            "Therapist"
        };

        var tempTemplateProfession = new TempTemplateProfession
        {
            Professions = professions.Select(x => x.ToPascalCase()).ToArray(),
            FullName = newAuthor.FullName,
            PersonId = newAuthor.Id,
            PublicTemplateId = publicTemplate.Id
        };

        _templateProfessionRepositoryMock.Setup(x => x.GetTempTemplateProfessions(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync([]);

        // Act
        var ev = new EventMessage(
            EventType.UpdatePublicTemplateProfessions,
            new EventData<UpdatePublicTemplateProfessionsEventData>(
                new UpdatePublicTemplateProfessionsEventData(2, 0, 20, false)
            ),
            null,
            Guid.NewGuid()
        );

        Fixture.QueueMessage(QueueType.Task, ev);
        await Fixture.Run();

        // Assert
        publicTemplate = await DataContext.PublicTemplates.Include(x => x.Professions).AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == publicTemplate.Id);

        publicTemplate.Should().NotBeNull();

        publicTemplate.Author.Name.Should().Be(oldPerson.FullName);
        publicTemplate.Author.PersonId.Should().Be(oldPerson.Id);
        publicTemplate.Professions.Select(x => x.Profession).Should().BeEquivalentTo(oldProfessions, opt => opt.WithoutStrictOrdering());

        sqsRepositoryMock.Verify(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>()), Times.Never);
    }

    [Fact]
    public async Task Should_Update_Profession_And_Author_If_Dry_Run_False_Range_Test()
    {
        var oldProfessions = new[]
        {
            "Social Worker",
            "General Practitioner"
        };

        var publicTemplate = new PublicTemplateFaker()
            .RuleFor(x => x.Professions, oldProfessions)
            .Generate()
            .ToDataModel();

        var newAuthor = new PersonFaker()
            .Generate()
            .ToDataModel();

        var backUp = oldProfessions
            .Select(x => new TempPublicTemplateProfessionDataModel(publicTemplate.Id, x))
            .ToList();

        await DataContext.AddRangeAsync(backUp);
        await DataContext.AddRangeAsync(publicTemplate, newAuthor);
        await DataContext.SaveChangesAsync();

        var professions = new[]
        {
            "Social worker",
            "Behavioral health Therapy",
            "Psychology",
            "Addiction Counselor",
            "Clinical Psychologist",
            "Counselor",
            "Rehabilitation Counselor",
            "Therapist"
        };

        var tempTemplateProfession = new TempTemplateProfession
        {
            Professions = professions.Select(x => x.ToPascalCase()).ToArray(),
            FullName = newAuthor.FullName,
            PersonId = newAuthor.Id,
            PublicTemplateId = publicTemplate.Id
        };

        _templateProfessionRepositoryMock.Setup(x => x.GetTempTemplateProfessions(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync([tempTemplateProfession]);

        // Act
        var ev = new EventMessage(
            EventType.UpdatePublicTemplateProfessions,
            new EventData<UpdatePublicTemplateProfessionsEventData>(
                new UpdatePublicTemplateProfessionsEventData(0, 1, 0, false)
            ),
            null,
            Guid.NewGuid()
        );

        Fixture.QueueMessage(QueueType.Task, ev);
        await Fixture.Run();

        // Assert
        publicTemplate = await DataContext.PublicTemplates.Include(x => x.Professions).AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == publicTemplate.Id);

        publicTemplate.Should().NotBeNull();

        publicTemplate.Author.Name.Should().Be(newAuthor.FullName);
        publicTemplate.Author.PersonId.Should().Be(newAuthor.Id);
        publicTemplate.Professions.Select(x => x.Profession).Should().BeEquivalentTo(tempTemplateProfession.Professions, opt => opt.WithoutStrictOrdering());

        sqsRepositoryMock.Verify(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>()), Times.Never);
    }

    [Fact]
    public async Task Should_Not_Update_Profession_And_Author_If_Dry_Run_True_Test()
    {
        var oldProfessions = new[]
        {
            "GeneralPractitioner",
            "SocialWorker",
        };

        var oldPerson = new PersonFaker()
            .Generate()
            .ToDataModel();

        var oldAuthor = new Author
        {
            PersonId = oldPerson.Id,
            Name = oldPerson.FullName,
            ProviderId = ProviderId,
            ProviderName = "test"
        };

        var publicTemplate = new PublicTemplateFaker()
            .RuleFor(x => x.Professions, oldProfessions)
            .RuleFor(x => x.Author, oldAuthor)
            .Generate()
            .ToDataModel();

        var newPerson = new PersonFaker()
            .Generate()
            .ToDataModel();

        var backUp = oldProfessions
            .Select(x => new TempPublicTemplateProfessionDataModel(publicTemplate.Id, x))
            .ToList();

        await DataContext.AddRangeAsync(backUp);
        await DataContext.AddRangeAsync(publicTemplate, oldPerson, newPerson);
        await DataContext.SaveChangesAsync();

        var newProfessions = new[]
        {
            "Social worker",
            "Behavioral health Therapy",
            "Psychology",
            "Addiction Counselor",
            "Clinical Psychologist",
            "Counselor",
            "Rehabilitation Counselor",
            "Therapist"
        };

        var tempTemplateProfession = new TempTemplateProfession
        {
            Professions = newProfessions,
            FullName = newPerson.FullName,
            PersonId = newPerson.Id,
            PublicTemplateId = publicTemplate.Id
        };

        _templateProfessionRepositoryMock.Setup(x => x.GetTempTemplateProfessions(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync([tempTemplateProfession]);

        // Act
        var ev = new EventMessage(
            EventType.UpdatePublicTemplateProfessions,
            new EventData<UpdatePublicTemplateProfessionsEventData>(
                new UpdatePublicTemplateProfessionsEventData(0, 0,20, true)
            ),
            null,
            Guid.NewGuid()
        );

        Fixture.QueueMessage(QueueType.Task, ev);
        await Fixture.Run();

        publicTemplate = await DataContext.PublicTemplates.Include(x => x.Professions).AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == publicTemplate.Id);

        publicTemplate.Author.Name.Should().Be(oldPerson.FullName);
        publicTemplate.Author.PersonId.Should().Be(oldPerson.Id);
        publicTemplate.Professions.Select(x => x.Profession).Should().BeEquivalentTo(oldProfessions, opt => opt.WithoutStrictOrdering());

        sqsRepositoryMock.Verify(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>()), Times.Never);
    }

    [Fact]
    public async Task Should_Not_Update_If_New_Author_Does_Not_Exist_Test()
    {
        var oldProfessions = new[]
        {
            "GeneralPractitioner",
            "SocialWorker",
        };

        var oldPerson = new PersonFaker()
            .Generate()
            .ToDataModel();

        var oldAuthor = new Author
        {
            PersonId = oldPerson.Id,
            Name = oldPerson.FullName,
            ProviderId = ProviderId,
            ProviderName = "test"
        };

        var publicTemplate = new PublicTemplateFaker()
            .RuleFor(x => x.Professions, oldProfessions)
            .RuleFor(x => x.Author, oldAuthor)
            .Generate()
            .ToDataModel();

        var newAuthor = new PersonFaker()
            .Generate();

        var backUp = oldProfessions
            .Select(x => new TempPublicTemplateProfessionDataModel(publicTemplate.Id, x))
            .ToList();

        await DataContext.AddRangeAsync(backUp);
        await DataContext.AddRangeAsync(publicTemplate, oldPerson);
        await DataContext.SaveChangesAsync();

        var newProfessions = new[]
        {
            "Social worker",
            "Behavioral health Therapy",
            "Psychology",
            "Addiction Counselor",
            "Clinical Psychologist",
            "Counselor",
            "Rehabilitation Counselor",
            "Therapist"
        };

        var tempTemplateProfession = new TempTemplateProfession
        {
            Professions = newProfessions,
            FullName = newAuthor.FullName,
            PersonId = newAuthor.Id,
            PublicTemplateId = publicTemplate.Id
        };

        _templateProfessionRepositoryMock.Setup(x => x.GetTempTemplateProfessions(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync([tempTemplateProfession]);

        // Act
        var ev = new EventMessage(
            EventType.UpdatePublicTemplateProfessions,
            new EventData<UpdatePublicTemplateProfessionsEventData>(
                new UpdatePublicTemplateProfessionsEventData(0, 0, 20, true)
            ),
            null,
            Guid.NewGuid()
        );

        Fixture.QueueMessage(QueueType.Task, ev);
        await Fixture.Run();

        publicTemplate = await DataContext.PublicTemplates.Include(x => x.Professions).AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == publicTemplate.Id);

        publicTemplate.Author.Name.Should().Be(oldPerson.FullName);
        publicTemplate.Author.PersonId.Should().Be(oldPerson.Id);
        publicTemplate.Professions.Select(x => x.Profession).Should().BeEquivalentTo(oldProfessions, opt => opt.WithoutStrictOrdering());

        sqsRepositoryMock.Verify(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>()), Times.Never);
    }

    [Fact]
    public async Task Should_Not_Update_If_Template_Does_Not_Exist_Test()
    {
        var oldProfessions = new[]
        {
            "GeneralPractitioner",
            "SocialWorker",
        };

        var oldPerson = new PersonFaker()
            .Generate()
            .ToDataModel();

        var oldAuthor = new Author
        {
            PersonId = oldPerson.Id,
            Name = oldPerson.FullName,
            ProviderId = ProviderId,
            ProviderName = "test"
        };

        var publicTemplate = new PublicTemplateFaker()
            .RuleFor(x => x.Professions, oldProfessions)
            .RuleFor(x => x.Author, oldAuthor)
            .Generate()
            .ToDataModel();

        var newAuthor = new PersonFaker()
            .Generate()
            .ToDataModel();

        var backUp = oldProfessions
            .Select(x => new TempPublicTemplateProfessionDataModel(publicTemplate.Id, x))
            .ToList();

        await DataContext.AddRangeAsync(backUp);
        await DataContext.AddRangeAsync(oldPerson, newAuthor);
        await DataContext.SaveChangesAsync();

        var newProfessions = new[]
        {
            "Random Profession"
        };

        var tempTemplateProfession = new TempTemplateProfession
        {
            Professions = newProfessions,
            FullName = newAuthor.FullName,
            PersonId = newAuthor.Id,
            PublicTemplateId = publicTemplate.Id
        };

        _templateProfessionRepositoryMock.Setup(x => x.GetTempTemplateProfessions(It.IsAny<int>(), It.IsAny<int>()))
            .ReturnsAsync([tempTemplateProfession]);

        // Act
        var ev = new EventMessage(
            EventType.UpdatePublicTemplateProfessions,
            new EventData<UpdatePublicTemplateProfessionsEventData>(
                new UpdatePublicTemplateProfessionsEventData(0, 0, 20, true)
            ),
            null,
            Guid.NewGuid()
        );

        Fixture.QueueMessage(QueueType.Task, ev);
        await Fixture.Run();

        // Assert
        publicTemplate = await DataContext.PublicTemplates.Include(x => x.Professions).AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == publicTemplate.Id);

        publicTemplate.Should().BeNull();

        sqsRepositoryMock.Verify(x => x.SendMessage(It.IsAny<QueueType>(), It.IsAny<EventMessage>()), Times.Never);
    }
}