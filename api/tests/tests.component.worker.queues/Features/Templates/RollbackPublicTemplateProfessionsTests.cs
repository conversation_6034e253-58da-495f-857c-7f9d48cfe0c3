﻿using System.Linq;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Extensions;
using carepatron.core.Models.QueueMessages;
using carepatron.infra.sql.Models.Templates;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Fixtures;
using tests.common.Mocks;
using tests.component.worker.queues.Setup;

namespace tests.component.worker.queues.Features.Templates;

[Trait(nameof(CodeOwner), CodeOwner.NotesAndDocuments)]
public class RollbackPublicTemplateProfessionsTests : WorkerComponentTestBase<QueueWorkerFixture>
{
    private readonly TempTemplateProfessionCsvRepositoryMock _templateProfessionRepositoryMock;

    public RollbackPublicTemplateProfessionsTests(QueueWorkerFixture fixture) : base(fixture)
    {
        _templateProfessionRepositoryMock =
            Fixture.TestServer.Services.GetService<TempTemplateProfessionCsvRepositoryMock>();
    }

    [Fact]
    public async Task Should_Rollback_Profession_If_Dry_Run_False_Test()
    {
        // Arrange
        var currentProfessions = new[]
        {
            "SocialWorker",
            "BehavioralHealthTherapy",
            "Psychology",
            "AddictionCounselor",
            "ClinicalPsychologist",
            "Counselor",
            "RehabilitationCounselor",
            "Therapist"
        };

        var publicTemplate = new PublicTemplateFaker()
            .RuleFor(x => x.Professions, currentProfessions)
            .Generate()
            .ToDataModel();
        
        var backupProfessions = new[]
        {
            "SocialWorker",
            "GeneralPractitioner"
        };

        var tempPublicTemplateProfessions = backupProfessions
            .Select(x => new TempPublicTemplateProfessionDataModel(publicTemplate.Id, x))
            .ToList();

        await DataContext.AddRangeAsync(tempPublicTemplateProfessions);
        await DataContext.AddRangeAsync(publicTemplate);
        await DataContext.SaveChangesAsync();

        // Act
        var ev = new EventMessage(
            EventType.RollbackPublicTemplateProfessions,
            new EventData<RollbackPublicTemplateProfessionsEventData>(
                new RollbackPublicTemplateProfessionsEventData([publicTemplate.Id], true)
            ),
            null,
            Guid.NewGuid()
        );

        Fixture.QueueMessage(QueueType.Task, ev);
        await Fixture.Run();

        // Assert
        publicTemplate = await DataContext.PublicTemplates.Include(x => x.Professions).AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == publicTemplate.Id);

        publicTemplate.Should().NotBeNull();

        publicTemplate.Professions.Select(x => x.Profession).Should()
            .BeEquivalentTo(backupProfessions, opt => opt.WithoutStrictOrdering());
    }
}