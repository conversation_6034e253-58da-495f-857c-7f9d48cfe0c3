using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using carepatron.core.Application.Exports.Events;
using carepatron.core.Application.Exports.Models;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Common;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Extensions;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Utilities;
using carepatron.infra.sql.Models.Tasks;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Fixtures;
using tests.common.Mocks;
using tests.component.worker.queues.Setup;

namespace tests.component.worker.queues.Features.Exports;

[Trait(nameof(CodeOwner), CodeOwner.Clients)]
public class AppointmentExportTests : WorkerComponentTestBase<QueueWorkerFixture>
{
    private FileStorageRepositoryMock fileStorageRepositoryMock;

    public AppointmentExportTests(QueueWorkerFixture testFixture) : base(testFixture)
    {
        fileStorageRepositoryMock = Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
        fileStorageRepositoryMock.UseRealImplementation();
    }

    [Fact]
    public async Task AppointmentExport_should_create_appointment_export()
    {
        string timeZone = "Asia/Manila";
        var startDateTime = new DateTime(2024, 9, 15)
            .ToStartOfDay()
            .AsZonedDateTimeToUtc(timeZone);

        var endDateTime = new DateTime(2024, 9, 21)
            .ToEndOfDay()
            .AsZonedDateTimeToUtc(timeZone);

        var export = new ExportFaker(ProviderId, PersonId)
            .RuleFor(x => x.Type, ExportType.Appointments)
            .RuleFor(x => x.Status, ExportStatus.Pending)
            .RuleFor(x => x.Filter, new CalendarBulkFilter()
            {
                All = true,
                StartDate = startDateTime,
                EndDate = endDateTime,
                TimeZone = timeZone,
                Type = ExportType.Appointments
            })
            .Generate();
        await DataContext.AddAsync(export.ToDataModel());

        var services = new ProviderItemFaker()
            .Generate(3)
            .ToArray();

        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(3)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var tasks = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, x => x.Date.Between(startDateTime, endDateTime))
            .RuleFor(x => x.EndDate, (faker, x) => x.StartDate.AddMinutes(30))
            .RuleFor(x => x.ItemsSnapshot, services)
            .RuleFor(x => x.StaffIds, [PersonId])
            .RuleFor(x => x.Invoices, [])
            .Generate(10);

        var tasksDataModel = tasks.ToDataModel().ToList();
        tasksDataModel.ForEach(x => x.WithContact(contacts.Select(x => new TaskContactDataModel() { ContactId = x.Id }).ToList()));
        await DataContext.AddRangeAsync(tasksDataModel);

        foreach (var task in tasksDataModel)
        {
            foreach (var taskContact in task.Contacts)
            {
                var invoice = new InvoiceFaker(ProviderId)
                    .RuleFor(x => x.ContactId, taskContact.ContactId)
                    .RuleFor(x => x.TaskId, taskContact.TaskId)
                    .Generate()
                    .ToDataModel();

                var note = new NoteFaker(ProviderId, PersonId)
                    .RuleFor(x => x.TaskId, task.Id)
                    .RuleFor(x => x.ContactId, taskContact.ContactId)
                    .Generate()
                    .ToDataModel();

                await DataContext.AddRangeAsync(invoice, note);
            }
        }

        var recurringStartDateTime = new DateTime(2024, 9, 9)
            .ToStartOfDay()
            .AsZonedDateTimeToUtc(timeZone);

        var recurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, x => recurringStartDateTime)
            .RuleFor(x => x.EndDate, (faker, x) => x.StartDate.AddMinutes(30))
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY;COUNT=5"))
            .Generate()
            .ToDataModel();
        await DataContext.AddRangeAsync(recurringTask);
        await DataContext.SaveChangesAsync();

        var ev = new EventMessage(
            EventType.ExportJob,
            new EventData<ExportCreatedJobEvent>(export),
            $"{ProviderId}-data-export-contacts",
            PersonId
        );

        Fixture.QueueMessage(QueueType.Task, ev);

        await Fixture.Run();

        var exportDb = await DataContext.Exports
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == export.Id);
        exportDb.Should().NotBeNull();
        exportDb.FileId.Should().NotBeEmpty();

        var stream = await fileStorageRepositoryMock.GetObjectStream(exportDb.FileId.Value, FileLocationType.Export);
        stream.Should().NotBeNull();

        var expectedTask = tasks.First();
        var content = await ReadCsv(stream, exportDb.Type);
        contacts.Should().AllSatisfy(x => content.Should().ContainEquivalentOf(new
        {
            StartDate = expectedTask.StartDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
            EndDate = expectedTask.EndDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
            expectedTask.Title,
            expectedTask.Location,
            expectedTask.Description,
            Client = x.FullName,
            Status = x.Status.ToString(),
            Staff = string.Join(Environment.NewLine, expectedTask.Staff.Select(x => x.FullName)),
            Services = string.Join(Environment.NewLine, expectedTask.ItemsSnapshot.Select(x =>
            {
                string[] details = [x.Name, x.Code, x.Price.ToString()];
                return string.Join(" - ", details.Where(y => !string.IsNullOrEmpty(y)));
            }).ToArray()),
            ClientIdentificationNumber = x.IdentificationNumber,
            expectedTask.TimeZone
        }, opt => opt.ExcludingMissingMembers()));
    }

    [Fact]
    public async Task AppointmentExport_should_have_correct_status_notes_and_invoices()
    {
        string timeZone = "Asia/Manila";
        var startDateTime = new DateTime(2024, 9, 15)
            .ToStartOfDay()
            .AsZonedDateTimeToUtc(timeZone);

        var endDateTime = new DateTime(2024, 9, 21)
            .ToEndOfDay()
            .AsZonedDateTimeToUtc(timeZone);

        var export = new ExportFaker(ProviderId, PersonId)
            .RuleFor(x => x.Type, ExportType.Appointments)
            .RuleFor(x => x.Status, ExportStatus.Pending)
            .RuleFor(x => x.Filter, new CalendarBulkFilter()
            {
                All = true,
                StartDate = startDateTime,
                EndDate = endDateTime,
                TimeZone = timeZone,
                Type = ExportType.Appointments
            })
            .Generate();
        await DataContext.AddAsync(export.ToDataModel());

        var services = new ProviderItemFaker()
            .Generate(3)
            .ToArray();

        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(3)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var contact1 = contacts[0];
        var contact2 = contacts[1];
        var contact3 = contacts[2];

        var taskModel = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, x => x.Date.Between(startDateTime, endDateTime))
            .RuleFor(x => x.EndDate, (faker, x) => x.StartDate.AddMinutes(30))
            .RuleFor(x => x.ItemsSnapshot, services)
            .RuleFor(x => x.StaffIds, [PersonId])
            .RuleFor(x => x.Invoices, [])
            .Generate();

        var taskDataModel = taskModel.ToDataModel();
        taskDataModel.WithContact(new List<TaskContactDataModel>
        {
            new TaskContactDataModel { ContactId = contact1.Id, TaskContactStatus = TaskContactStatus.Confirmed },
            new TaskContactDataModel { ContactId = contact2.Id, TaskContactStatus = TaskContactStatus.Attended },
            new TaskContactDataModel { ContactId = contact3.Id, TaskContactStatus = TaskContactStatus.Cancelled }
        });
        await DataContext.AddRangeAsync(taskDataModel);


        var invoiceForContact1 = new InvoiceFaker(ProviderId)
            .RuleFor(x => x.ContactId, contact1.Id)
            .RuleFor(x => x.TaskId, taskModel.Id)
            .Generate()
            .ToDataModel();

        var noteForContact2 = new NoteFaker(ProviderId, PersonId)
            .RuleFor(x => x.ContactId, contact2.Id)
            .RuleFor(x => x.TaskId, taskModel.Id)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(invoiceForContact1, noteForContact2);

        await DataContext.SaveChangesAsync();

        var ev = new EventMessage(
            EventType.ExportJob,
            new EventData<ExportCreatedJobEvent>(export),
            $"{ProviderId}-data-export-contacts",
            PersonId
        );

        Fixture.QueueMessage(QueueType.Task, ev);

        await Fixture.Run();

        var exportDb = await DataContext.Exports
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == export.Id);
        exportDb.Should().NotBeNull();
        exportDb.FileId.Should().NotBeEmpty();

        var stream = await fileStorageRepositoryMock.GetObjectStream(exportDb.FileId.Value, FileLocationType.Export);
        stream.Should().NotBeNull();

        var content = await ReadCsv(stream, exportDb.Type);

        var uriProvider = ResolveService<IUriProvider>();

        content.Should().ContainEquivalentOf(new
        {
            Client = contact1.FullName,
            Notes = string.Empty,
            Invoices = $"{invoiceForContact1.Number} - {invoiceForContact1.Status}",
            Status = TaskContactStatus.Confirmed.ToString()
        }, opt => opt.ExcludingMissingMembers());

        content.Should().ContainEquivalentOf(new
        {
            Client = contact1.FullName,
            Notes = uriProvider.ComposeNoteUri(noteForContact2.Id, contact1.Id).ToString(),
            Invoices = string.Empty,
            Status = TaskContactStatus.Attended.ToString()
        }, opt => opt.ExcludingMissingMembers());

        content.Should().ContainEquivalentOf(new
        {
            Client = contact3.FullName,
            Notes = string.Empty,
            Invoices = string.Empty,
            Status = TaskContactStatus.Cancelled.ToString()
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task AppointmentExport_should_be_sorted()
    {
        string timeZone = "Asia/Manila";
        var startDateTime = new DateTime(2024, 9, 15)
            .ToStartOfDay()
            .AsZonedDateTimeToUtc(timeZone);

        var endDateTime = new DateTime(2024, 9, 21)
            .ToEndOfDay()
            .AsZonedDateTimeToUtc(timeZone);
        
        var staffMemberId = Data.StaffMember.Id;

        var export = new ExportFaker(ProviderId, PersonId)
            .RuleFor(x => x.Type, ExportType.Appointments)
            .RuleFor(x => x.Status, ExportStatus.Pending)
            .RuleFor(x => x.Filter, new CalendarBulkFilter()
            {
                All = true,
                StartDate = startDateTime,
                EndDate = endDateTime,
                TimeZone = timeZone,
                Type = ExportType.Appointments,
                StaffIds = [staffMemberId]
            })
            .Generate();
        await DataContext.AddAsync(export.ToDataModel());

        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(2)
            .ToDataModel();
        await DataContext.AddRangeAsync(contacts);

        var taskModel1 = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2024, 9, 20)
                .ToStartOfDay()
                .AsZonedDateTimeToUtc(timeZone))
            .RuleFor(x => x.EndDate, (faker, x) => x.StartDate.AddMinutes(30))
            .RuleFor(x => x.StaffIds, [staffMemberId])
            .RuleFor(x => x.Invoices, [])
            .RuleFor(x => x.Notes, [])
            .RuleFor(x => x.TimeZone, timeZone)
            .Generate()
            .ToDataModel();

        var taskModel2 = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2024, 9, 19)
                .ToStartOfDay()
                .AsZonedDateTimeToUtc(timeZone))
            .RuleFor(x => x.EndDate, (faker, x) => x.StartDate.AddMinutes(30))
            .RuleFor(x => x.StaffIds, [staffMemberId])
            .RuleFor(x => x.Invoices, [])
            .RuleFor(x => x.Notes, [])
            .RuleFor(x => x.RRule, new RRule("FREQ=DAILY;COUNT=5"))
            .RuleFor(x => x.TimeZone, timeZone)
            .Generate()
            .ToDataModel()
            .WithContact(contacts.Select(x => new TaskContactDataModel() { ContactId = x.Id }).ToList());

        var taskModel3 = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2024, 9, 19)
                .ToStartOfDay().AddSeconds(1)
                .AsZonedDateTimeToUtc(timeZone))
            .RuleFor(x => x.EndDate, (faker, x) => x.StartDate.AddMinutes(30))
            .RuleFor(x => x.StaffIds, [staffMemberId])
            .RuleFor(x => x.Invoices, [])
            .RuleFor(x => x.Notes, [])
            .RuleFor(x => x.TimeZone, timeZone)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(taskModel1, taskModel2, taskModel3);

        await DataContext.SaveChangesAsync();

        var ev = new EventMessage(
            EventType.ExportJob,
            new EventData<ExportCreatedJobEvent>(export),
            $"{ProviderId}-data-export-contacts",
            staffMemberId
        );

        Fixture.QueueMessage(QueueType.Task, ev);

        await Fixture.Run();

        var exportDb = await DataContext.Exports
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == export.Id);
        exportDb.Should().NotBeNull();
        exportDb.FileId.Should().NotBeEmpty();

        var stream = await fileStorageRepositoryMock.GetObjectStream(exportDb.FileId.Value, FileLocationType.Export);
        stream.Should().NotBeNull();

        var content = await ReadCsv(stream, exportDb.Type);
        content.Should().HaveCount(8);

        var expected = new[]
        {
            // (daily) start date is 2024-09-19 for client 1
            new
            {
                StartDate = taskModel2.StartDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                EndDate = taskModel2.EndDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                Client = contacts[0].FullName,
            },
            // (daily) start date is 2024-09-19 for client 2
            new
            {
                StartDate = taskModel2.StartDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                EndDate = taskModel2.EndDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                Client = contacts[1].FullName,
            },
            // start date is 2024-09-19 
            new
            {
                StartDate = taskModel3.StartDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                EndDate = taskModel3.EndDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                Client = string.Empty,
            },
            // start date is 2024-09-20
            new
            {
                StartDate = taskModel1.StartDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                EndDate = taskModel1.EndDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                Client = string.Empty,
            },
            // (daily) start date is 2024-09-20 for client 1
            new
            {
                StartDate = taskModel2.StartDate.AddDays(1).ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                EndDate = taskModel2.EndDate.AddDays(1).ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                Client = contacts[0].FullName,
            },
            // (daily) start date is 2024-09-20 for client 2
            new
            {
                StartDate = taskModel2.StartDate.AddDays(1).ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                EndDate = taskModel2.EndDate.AddDays(1).ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                Client = contacts[1].FullName,
            },
            // (daily) start date is 2024-09-21 for client 1
            new
            {
                StartDate = taskModel2.StartDate.AddDays(2).ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                EndDate = taskModel2.EndDate.AddDays(2).ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                Client = contacts[0].FullName,
            },
            // (daily) start date is 2024-09-21 for client 2
            new
            {
                StartDate = taskModel2.StartDate.AddDays(2).ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                EndDate = taskModel2.EndDate.AddDays(2).ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
                Client = contacts[1].FullName,
            }
        };

        content.Should().BeEquivalentTo(expected, opt => opt.ExcludingMissingMembers()); 
    }

    [Fact]
    public async Task AppointmentExport_should_have_correct_attendee_status()
    {
        string timeZone = "Asia/Manila";
        var startDateTime = new DateTime(2024, 9, 15)
            .ToStartOfDay()
            .AsZonedDateTimeToUtc(timeZone);

        var endDateTime = new DateTime(2024, 9, 21)
            .ToEndOfDay()
            .AsZonedDateTimeToUtc(timeZone);

        var export = new ExportFaker(ProviderId, PersonId)
            .RuleFor(x => x.Type, ExportType.Appointments)
            .RuleFor(x => x.Status, ExportStatus.Pending)
            .RuleFor(x => x.Filter, new CalendarBulkFilter()
            {
                All = true,
                StartDate = startDateTime,
                EndDate = endDateTime,
                TimeZone = timeZone,
                Type = ExportType.Appointments
            })
            .Generate();

        var contact = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var customStatus = new TaskAttendeeStatusFaker()
            .RuleFor(x => x.Id, f => f.Random.AlphaNumeric(10))
            .RuleFor(x => x.Name,  f => f.Lorem.Sentence())
            .RuleFor(x => x.Group, TaskAttendeeGroupStatus.DidNotComplete)
            .Generate()
            .ToDataModel(ProviderId);

        var tasksFaker = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, x => x.Date.Between(startDateTime, endDateTime))
            .RuleFor(x => x.EndDate, (_, x) => x.StartDate.AddMinutes(30))
            .RuleFor(x => x.StaffIds, [PersonId]);

        var task1 = tasksFaker.Generate().ToDataModel().WithContact(
        [
            new() { ContactId = contact.Id, TaskContactStatus = TaskContactStatus.DidNotAttend }
        ]);
        var task2 = tasksFaker.Generate().ToDataModel().WithContact(
        [
            new() { ContactId = contact.Id, AttendeeStatusId = customStatus.Id }
        ]);

        await DataContext.AddAsync(export.ToDataModel());
        await DataContext.AddRangeAsync(contact);
        await DataContext.AddRangeAsync(task1, task2);
        await DataContext.SaveChangesAsync();

        var ev = new EventMessage(
            EventType.ExportJob,
            new EventData<ExportCreatedJobEvent>(export),
            $"{ProviderId}-data-export-contacts",
            PersonId
        );

        Fixture.QueueMessage(QueueType.Task, ev);

        await Fixture.Run();

        var exportDb = await DataContext.Exports
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == export.Id);
        exportDb.Should().NotBeNull();
        exportDb.FileId.Should().NotBeEmpty();

        var stream = await fileStorageRepositoryMock.GetObjectStream(exportDb.FileId.Value, FileLocationType.Export);
        stream.Should().NotBeNull();

        var content = await ReadCsv(stream, exportDb.Type);

        content.Should().ContainEquivalentOf(new
        {
            StartDate = task1.StartDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
            EndDate = task1.EndDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
            Client = contact.FullName,
            Status = nameof(TaskContactStatus.DidNotAttend)
        }, opt => opt.ExcludingMissingMembers());
        
        content.Should().ContainEquivalentOf(new
        {
            StartDate = task1.StartDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
            EndDate = task1.EndDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
            Client = contact.FullName,
            Status = customStatus.Name,
        }, opt => opt.ExcludingMissingMembers());
    }

    private async Task<List<Dictionary<string, string>>> ReadCsv(Stream stream, ExportType type)
    {
        var binaryReader = new BinaryReader(stream);
        var streamBytes = binaryReader.ReadBytes((int)stream.Length);

        using MemoryStream zipStream = new MemoryStream(streamBytes);
        using ZipArchive archive = new ZipArchive(zipStream);

        ZipArchiveEntry csvEntry = archive.GetEntry($"carepatron-{type}.csv");
        if (csvEntry is null) return null;

        using var memStream = new MemoryStream();
        csvEntry.Open().CopyTo(memStream);
        memStream.Seek(0, SeekOrigin.Begin);

        var (_, rows) = SpreadsheetUtilities.ParseToContactImportRows(memStream, "file.csv", int.MaxValue);
        return rows;
    }
}