using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Bogus;
using carepatron.core.Application.Calendar.Events;
using carepatron.core.Application.Calendar.Models;
using carepatron.core.Application.ConnectedApps.Models;
using carepatron.core.Application.Tasks.Events;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Users.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Exceptions;
using carepatron.core.Extensions;
using carepatron.core.Models.QueueMessages;
using carepatron.infra.sql.Models.Items;
using carepatron.infra.sql.Models.Tasks;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using tests.common.Data.Datasets;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Fixtures;
using tests.common.Mocks;
using tests.component.worker.queues.Setup;

namespace tests.component.worker.queues.Features.Calendar;

[Trait(nameof(CodeOwner), CodeOwner.TasksAndScheduling)]
public class GoogleCalendarSyncTests : WorkerComponentTestBase<QueueWorkerFixture>
{
    public GoogleCalendarSyncTests(QueueWorkerFixture fixture) : base(fixture)
    {
        var mockConnectedCalendarService = Fixture.TestServer.Services.GetService<ConnectedCalendarServiceMock>();
        mockConnectedCalendarService.UseRealImplementation();
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Create_Tasks()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.SaveChangesAsync();

        var events = new List<ExternalTask>
        {
            CreateExternalTask(),
            CreateExternalTask(),
            CreateExternalTask(null, null, true)
        };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        await AssertTasks(events, calendarSubscription.CalendarId);
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Update_And_Delete_Tasks()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var tasks = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .Generate(3)
            .ToDataModel()
            .ToList();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(tasks);
        await DataContext.SaveChangesAsync();

        var events = new List<ExternalTask>
        {
            CreateExternalTask(null, tasks[0].ExternalId),
            CreateExternalTask(null, tasks[1].ExternalId),
            CreateExternalTask(null, tasks[2].ExternalId, true)
        };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        await AssertTasks(events, calendarSubscription.CalendarId);
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Update_Parent_ExDate_When_Instance_Has_Been_Cancelled()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var startDate = new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc);

        var existingExternalTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, startDate)
            .RuleFor(x => x.EndDate, startDate.AddHours(1))
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.RRule, new RRule("FREQ=DAILY"))
            .RuleFor(x => x.AllDay, false)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(existingExternalTask.ToDataModel());
        await DataContext.SaveChangesAsync();

        var mapper = Fixture.TestServer.Services.GetRequiredService<IMapper>();
        var parentAsExternalTask = mapper.Map<ExternalTask>(existingExternalTask);
        parentAsExternalTask.ExternalStatus = "confirmed";

        var cancelledTask = CreateExternalTask(null, null, true);
        cancelledTask.StartDate = startDate.AddDays(4);
        cancelledTask.EndDate = cancelledTask.StartDate.AddHours(1);
        cancelledTask.ParentExternalId = existingExternalTask.ExternalId;

        var events = new List<ExternalTask> { cancelledTask, parentAsExternalTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var parentTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == existingExternalTask.Id);

        parentTaskDb.Should().NotBeNull();

        var expectedExDate = cancelledTask.StartDate.ToExDateFormat();
        parentTaskDb.ExDate.Should().Be(expectedExDate);
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Create_Recurring_Task_Based_Off_Of_Existing_Task()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var originalRecurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY;UNTIL=20220921T000000Z"))
            .RuleFor(x => x.ExDate, "20220912T000000Z")
            .RuleFor(x => x.OccurrenceEndDate, new DateTime(2022, 9, 21, 0, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(originalRecurringTask);
        await DataContext.SaveChangesAsync();

        var newRecurringTask = CreateExternalTask();
        newRecurringTask.ExternalId = $"{originalRecurringTask.ExternalId}_{new Faker().Random.Hash(10)}";
        newRecurringTask.StartDate = new DateTime(2022, 9, 21, 9, 0, 0, DateTimeKind.Utc);
        newRecurringTask.EndDate = new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc);
        newRecurringTask.TimeZone = "Pacific/Auckland";
        newRecurringTask.RRule = "FREQ=WEEKLY";
        newRecurringTask.ExDate = "20220915T000000Z";

        var events = new List<ExternalTask> { newRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var newRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newRecurringTask.ExternalId);

        AssertTask(newRecurringTask, newRecurringTaskDb);

        newRecurringTaskDb.RRule.Should().Be(newRecurringTask.RRule);
        newRecurringTaskDb.ExDate.Should().Be("20220915T000000Z");
        newRecurringTaskDb.ExternalCalendarId.Should().Be(calendarSubscription.CalendarId);
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Create_Recurring_Task_Based_Off_Of_Another_External_Task()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.SaveChangesAsync();

        var originalRecurringTask = CreateExternalTask();
        originalRecurringTask.StartDate = new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.EndDate = new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.TimeZone = "Pacific/Auckland";
        originalRecurringTask.RRule = "FREQ=WEEKLY;UNTIL=20220921T000000Z";
        originalRecurringTask.ExDate = "20220912T000000Z";
        originalRecurringTask.OccurrenceEndDate = new DateTime(2022, 9, 21, 0, 0, 0, DateTimeKind.Utc);

        var newRecurringTask = CreateExternalTask();
        newRecurringTask.ExternalId = $"{originalRecurringTask.ExternalId}_{new Faker().Random.Hash(10)}";
        newRecurringTask.StartDate = new DateTime(2022, 9, 21, 9, 0, 0, DateTimeKind.Utc);
        newRecurringTask.EndDate = new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc);
        newRecurringTask.TimeZone = "Pacific/Auckland";
        newRecurringTask.RRule = "FREQ=WEEKLY";
        newRecurringTask.ExDate = "20220915T000000Z";

        var events = new List<ExternalTask> { originalRecurringTask, newRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var originalRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == originalRecurringTask.ExternalId);

        var newRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newRecurringTask.ExternalId);

        AssertTask(originalRecurringTask, originalRecurringTaskDb);
        originalRecurringTaskDb.RRule.Should().Be(originalRecurringTask.RRule);
        originalRecurringTaskDb.ExDate.Should().Be("20220912T000000Z");
        originalRecurringTaskDb.OccurrenceEndDate.Should().Be(originalRecurringTask.OccurrenceEndDate);
        originalRecurringTaskDb.ExternalCalendarId.Should().Be(calendarSubscription.CalendarId);

        AssertTask(newRecurringTask, newRecurringTaskDb);
        newRecurringTaskDb.RRule.Should().Be(newRecurringTask.RRule);
        newRecurringTaskDb.ExDate.Should().Be("20220915T000000Z");
        newRecurringTaskDb.ExternalCalendarId.Should().Be(calendarSubscription.CalendarId);
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Create_Recurring_Exception_Task_Based_Off_Of_Existing_Task()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var originalRecurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 1, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY"))
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(originalRecurringTask);
        await DataContext.SaveChangesAsync();

        var newExceptionTask = CreateExternalTask();
        newExceptionTask.ParentExternalId = originalRecurringTask.ExternalId;
        newExceptionTask.StartDate = new DateTime(2022, 9, 16, 9, 0, 0, DateTimeKind.Utc);
        newExceptionTask.EndDate = new DateTime(2022, 9, 16, 10, 0, 0, DateTimeKind.Utc);
        newExceptionTask.TimeZone = "Pacific/Auckland";

        var events = new List<ExternalTask> { newExceptionTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var originalRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == originalRecurringTask.ExternalId);

        var newExceptionTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newExceptionTask.ExternalId);

        originalRecurringTaskDb.Should().NotBeNull();
        originalRecurringTaskDb.ExDate.Should().Be("20220916T090000Z");

        AssertTask(newExceptionTask, newExceptionTaskDb);
        newExceptionTaskDb.ParentId.Should().Be(originalRecurringTaskDb.Id);
        newExceptionTaskDb.ExternalCalendarId = calendarSubscription.CalendarId;
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Create_Recurring_Exception_Task_Based_Off_Of_External_Task()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.SaveChangesAsync();

        var originalRecurringTask = CreateExternalTask();
        originalRecurringTask.StartDate = new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.EndDate = new DateTime(2022, 9, 1, 10, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.TimeZone = "Pacific/Auckland";
        originalRecurringTask.RRule = "FREQ=WEEKLY";
        originalRecurringTask.OccurrenceEndDate = new DateTime(2022, 9, 21, 0, 0, 0, DateTimeKind.Utc);

        var newExceptionTask = CreateExternalTask();
        newExceptionTask.ParentExternalId = originalRecurringTask.ExternalId;
        newExceptionTask.StartDate = new DateTime(2022, 9, 16, 9, 0, 0, DateTimeKind.Utc);
        newExceptionTask.EndDate = new DateTime(2022, 9, 16, 10, 0, 0, DateTimeKind.Utc);
        newExceptionTask.TimeZone = "Pacific/Auckland";

        var events = new List<ExternalTask> { originalRecurringTask, newExceptionTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var originalRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == originalRecurringTask.ExternalId);

        var newExceptionTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newExceptionTask.ExternalId);

        originalRecurringTaskDb.Should().NotBeNull();
        originalRecurringTaskDb.ExDate.Should().Be("20220916T090000Z");

        AssertTask(newExceptionTask, newExceptionTaskDb);
        newExceptionTaskDb.ParentId.Should().Be(originalRecurringTaskDb.Id);
        newExceptionTaskDb.ExternalCalendarId = calendarSubscription.CalendarId;
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Create_Recurring_Exception_Task_With_Adjusted_ExDate_Due_To_DST()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var originalRecurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2024, 11, 1, 22, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2024, 11, 1, 22, 30, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "America/Chicago")
            .RuleFor(x => x.RRule, new RRule("FREQ=DAILY"))
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.AllDay, false)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(originalRecurringTask);
        await DataContext.SaveChangesAsync();

        var newExceptionTask = CreateExternalTask();
        newExceptionTask.ParentExternalId = originalRecurringTask.ExternalId;
        newExceptionTask.StartDate = new DateTime(2024, 12, 13, 22, 0, 0, DateTimeKind.Utc);
        newExceptionTask.EndDate = new DateTime(2024, 12, 13, 22, 30, 0, DateTimeKind.Utc);
        newExceptionTask.TimeZone = originalRecurringTask.TimeZone;

        var events = new List<ExternalTask> { newExceptionTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var originalRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == originalRecurringTask.ExternalId);

        var newExceptionTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newExceptionTask.ExternalId);

        originalRecurringTaskDb.Should().NotBeNull();
        originalRecurringTaskDb.ExDate.Should().Be("20241213T230000Z");

        AssertTask(newExceptionTask, newExceptionTaskDb);
        newExceptionTaskDb.ParentId.Should().Be(originalRecurringTaskDb.Id);
        newExceptionTaskDb.ExternalCalendarId = calendarSubscription.CalendarId;
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Update_Existing_Tasks()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, f => f.Random.Hash(10))
            .Generate();

        var tasks = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate(3)
            .ToDataModel()
            .ToList();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(tasks);
        await DataContext.SaveChangesAsync();

        var events = new List<ExternalTask>
        {
            CreateExternalTask(tasks[0].Id),
            CreateExternalTask(tasks[1].Id),
            CreateExternalTask(tasks[2].Id, null, true),
        };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var tasksDb = await DataContext.Tasks.AsNoTracking().Where(x => x.ProviderId == ProviderId).ToListAsync();

        // TODO: revist assertion
        foreach (var evt in events)
        {
            var task = tasksDb.FirstOrDefault(x => x.Id == evt.Id);
            task.Should().NotBeNull();
            task.Type.Should().Be(TaskType.Standard);

            if (evt.ExternalStatus == "cancelled")
            {
                task.Title.Should().NotBe(evt.Title);
                task.Description.Should().NotBe(evt.Description);
                task.StartDate.Should().NotBe(evt.StartDate);
                task.EndDate.Should().NotBe(evt.EndDate);
            }
            else
            {
                task.Title.Should().Be(evt.Title);
                task.Description.Should().Be(evt.Description);
                task.StartDate.Should().BeCloseTo(evt.StartDate, TimeSpan.FromSeconds(1));
                task.EndDate.Should().BeCloseTo(evt.EndDate, TimeSpan.FromSeconds(1));
            }
        }
    }


    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Update_Existing_Task_IsFree()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, f => f.Random.Hash(10))
            .Generate();

        var tasks = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.IsFree, false)
            .Generate(2)
            .ToDataModel()
            .ToList();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(tasks);
        await DataContext.SaveChangesAsync();

        var events = new List<ExternalTask>
        {
            CreateExternalTask(tasks[0].Id),
            CreateExternalTask(tasks[1].Id, isFree: true),
        };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var tasksDb = await DataContext.Tasks.AsNoTracking().Where(x => x.ProviderId == ProviderId).ToListAsync();

        tasksDb.Should().ContainEquivalentOf(new { tasks[0].Id, IsFree = false }, opt => opt.ExcludingMissingMembers());
        tasksDb.Should().ContainEquivalentOf(new { tasks[1].Id, IsFree = true }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Update_Existing_Recurring_Task()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, f => f.Random.Hash(10))
            .Generate();

        var recurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY;UNTIL=20220921T000000Z"))
            .RuleFor(x => x.ExDate, "20220912T000000Z,20220913T000000Z")
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(recurringTask);
        await DataContext.SaveChangesAsync();

        var externalRecurringTask = CreateExternalTask(recurringTask.Id);
        externalRecurringTask.StartDate = new DateTime(2022, 9, 1, 13, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.EndDate = new DateTime(2022, 9, 29, 14, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.RRule = "FREQ=WEEKLY;UNTIL=20220919T000000Z";
        externalRecurringTask.OccurrenceEndDate = new DateTime(2022, 9, 19, 0, 0, 0, DateTimeKind.Utc);

        var events = new List<ExternalTask> { externalRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var recurringTaskDb = await DataContext.Tasks.AsNoTracking().FirstOrDefaultAsync(x => x.Id == recurringTask.Id);

        AssertTask(externalRecurringTask, recurringTaskDb, recurringTask.Type);
        recurringTaskDb.RRule.Should().Be(externalRecurringTask.RRule);
        recurringTaskDb.OccurrenceEndDate.Should().Be(externalRecurringTask.OccurrenceEndDate);
        // exdates time should not be adjusted
        recurringTaskDb.ExDate.Should().Be("20220912T000000Z,20220913T000000Z");
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Create_Recurring_Task_Based_Off_Of_Existing_Task()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, f => f.Random.Hash(10))
            .Generate();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var contactPerson = new PersonFaker().Generate().ToDataModel();

        var taskContact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, contactPerson.Id)
            .RuleFor(x => x.Email, contactPerson.Email)
            .RuleFor(x => x.PhoneNumber, contactPerson.PhoneNumber)
            .RuleFor(x => x.FirstName, contactPerson.FirstName)
            .RuleFor(x => x.LastName, contactPerson.LastName)
            .Generate()
            .ToDataModel();

        var recurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY;UNTIL=20220921T000000Z"))
            .RuleFor(x => x.ExDate, "20220912T000000Z,20220913T000000Z")
            .Generate()
            .ToDataModel()
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    ProviderId = ProviderId
                }
            })
            .WithContact(new List<TaskContactDataModel> { new TaskContactDataModel() { ContactId = taskContact.Id } })
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel() { PersonId = PersonId } });

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            providerItem,
            contactPerson,
            taskContact,
            recurringTask);
        await DataContext.SaveChangesAsync();

        var uniqueTitle = Guid.NewGuid().ToString();
        var externalRecurringTask = CreateExternalTask(Guid.NewGuid());
        externalRecurringTask.Title = uniqueTitle;
        externalRecurringTask.Metadata = new ExternalTaskMetadata { TaskId = recurringTask.Id };
        externalRecurringTask.StartDate = new DateTime(2022, 9, 1, 13, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.EndDate = new DateTime(2022, 9, 29, 14, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.RRule = "FREQ=WEEKLY;UNTIL=20220919T000000Z";
        externalRecurringTask.OccurrenceEndDate = new DateTime(2022, 9, 19, 0, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.ExDate = "20220906T000000Z,20220907T000000Z";
        externalRecurringTask.Metadata = new ExternalTaskMetadata { TaskId = recurringTask.Id };

        var events = new List<ExternalTask> { externalRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        // this recurring is a new recurring event caused by "This and following events"
        // using title for filtering since we don't know any reference to it
        var newRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ProviderId == ProviderId && x.Title == uniqueTitle);

        AssertTask(externalRecurringTask, newRecurringTaskDb, externalRecurringTask.Type);
        newRecurringTaskDb.RRule.Should().Be(externalRecurringTask.RRule);
        newRecurringTaskDb.OccurrenceEndDate.Should().Be(externalRecurringTask.OccurrenceEndDate);
        // we're not copying exdate from the original recurring task
        newRecurringTaskDb.ExDate.Should().BeNull();
        newRecurringTaskDb.Type.Should().Be(recurringTask.Type);
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Not_Create_Recurring_Task_Based_Off_Of_Soft_Deleted_Task()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, f => f.Random.Hash(10))
            .Generate();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var contactPerson = new PersonFaker().Generate().ToDataModel();

        var taskContact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, contactPerson.Id)
            .RuleFor(x => x.Email, contactPerson.Email)
            .RuleFor(x => x.PhoneNumber, contactPerson.PhoneNumber)
            .RuleFor(x => x.FirstName, contactPerson.FirstName)
            .RuleFor(x => x.LastName, contactPerson.LastName)
            .Generate()
            .ToDataModel();

        var recurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY;UNTIL=20220921T000000Z"))
            .RuleFor(x => x.ExDate, "20220912T000000Z,20220913T000000Z")
            .Generate()
            .ToDataModel()
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    ProviderId = ProviderId
                }
            })
            .WithContact(new List<TaskContactDataModel> { new TaskContactDataModel() { ContactId = taskContact.Id } })
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel() { PersonId = PersonId } });
        recurringTask.DeletedAtUtc = DateTime.UtcNow;

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            providerItem,
            contactPerson,
            taskContact,
            recurringTask);
        await DataContext.SaveChangesAsync();

        var uniqueTitle = Guid.NewGuid().ToString();
        var externalRecurringTask = CreateExternalTask(Guid.NewGuid());
        externalRecurringTask.Title = uniqueTitle;
        externalRecurringTask.Metadata = new ExternalTaskMetadata { TaskId = recurringTask.Id };
        externalRecurringTask.StartDate = new DateTime(2022, 9, 1, 13, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.EndDate = new DateTime(2022, 9, 29, 14, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.RRule = "FREQ=WEEKLY;UNTIL=20220919T000000Z";
        externalRecurringTask.OccurrenceEndDate = new DateTime(2022, 9, 19, 0, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.ExDate = "20220906T000000Z,20220907T000000Z";

        var events = new List<ExternalTask> { externalRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        // this recurring is a new recurring event caused by "This and following events"
        // using title for filtering since we don't know any reference to it
        var newRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ProviderId == ProviderId && x.Title == uniqueTitle);

        newRecurringTaskDb.Should().BeNull();
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Create_Recurring_Exception_Task_Based_Off_Of_Existing_Task()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, f => f.Random.Hash(10))
            .Generate();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var contactPerson = new PersonFaker().Generate().ToDataModel();

        var taskContact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, contactPerson.Id)
            .RuleFor(x => x.Email, contactPerson.Email)
            .RuleFor(x => x.PhoneNumber, contactPerson.PhoneNumber)
            .RuleFor(x => x.FirstName, contactPerson.FirstName)
            .RuleFor(x => x.LastName, contactPerson.LastName)
            .Generate()
            .ToDataModel();

        var recurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY;UNTIL=20220921T000000Z"))
            .RuleFor(x => x.ExDate, "20220912T000000Z,20220913T000000Z")
            .Generate()
            .ToDataModel()
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    ProviderId = ProviderId
                }
            })
            .WithContact(new List<TaskContactDataModel> { new TaskContactDataModel() { ContactId = taskContact.Id } })
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel() { PersonId = PersonId } });

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            providerItem,
            contactPerson,
            taskContact,
            recurringTask);
        await DataContext.SaveChangesAsync();

        var exceptionTask = CreateExternalTask(recurringTask.Id);
        exceptionTask.StartDate = new DateTime(2022, 9, 1, 13, 0, 0, DateTimeKind.Utc);
        exceptionTask.EndDate = new DateTime(2022, 9, 29, 14, 0, 0, DateTimeKind.Utc);

        var events = new List<ExternalTask> { exceptionTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        // this is an exception event caused by choosing "This event"
        // using title for filtering since we don't know any reference to it
        var newExceptionTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ProviderId == ProviderId && x.ParentId == recurringTask.Id);

        var parentTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == recurringTask.Id);

        AssertTask(exceptionTask, newExceptionTaskDb, exceptionTask.Type);
        newExceptionTaskDb.RRule.Should().BeNull();
        newExceptionTaskDb.OccurrenceEndDate.Should().BeNull();
        newExceptionTaskDb.ExDate.Should().BeNull();

        parentTaskDb.ExDate.Should().Be("20220912T000000Z,20220913T000000Z,20220901T090000Z");
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Not_Delete_Task_If_Cancelled()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, f => f.Random.Hash(10))
            .Generate();

        var task = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(task);
        await DataContext.SaveChangesAsync();

        var events = new List<ExternalTask>
        {
            CreateExternalTask(task.Id, null, true),
        };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var taskDb = await DataContext.Tasks.AsNoTracking().FirstOrDefaultAsync(x => x.Id == task.Id);
        taskDb.Should().NotBeNull();
    }

    [Fact]
    public async Task GoogleCalendarSync_Should_Update_Sync_Token()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.SaveChangesAsync();

        var events = new List<ExternalTask>
        {
            CreateExternalTask(),
            CreateExternalTask(),
            CreateExternalTask(null, null, true)
        };

        var syncToken = new Faker().Random.Hash(10);

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray(), NextSyncToken = syncToken });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        await AssertTasks(events, calendarSubscription.CalendarId);

        var calendarSubscriptionDb = await DataContext.CalendarSubscriptions
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == calendarSubscription.Id);

        calendarSubscriptionDb.LastSyncToken.Should().Be(syncToken);
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Create_Recurring_Exception_Task_With_Adjusted_ExDate_Due_To_DST()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var existingExternalTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, new DateTime(2024, 11, 1, 22, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2024, 11, 1, 22, 30, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "America/Chicago")
            .RuleFor(x => x.RRule, new RRule("FREQ=DAILY"))
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.AllDay, false)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(existingExternalTask.ToDataModel());
        await DataContext.SaveChangesAsync();

        var mapper = Fixture.TestServer.Services.GetRequiredService<IMapper>();
        var parentAsExternalTask = mapper.Map<ExternalTask>(existingExternalTask);
        parentAsExternalTask.ExternalStatus = "confirmed";

        var cancelledTask = CreateExternalTask(null, null, true);
        cancelledTask.StartDate = new DateTime(2024, 12, 13, 22, 0, 0, DateTimeKind.Utc);
        cancelledTask.EndDate = new DateTime(2024, 12, 13, 22, 30, 0, DateTimeKind.Utc);
        cancelledTask.TimeZone = existingExternalTask.TimeZone;
        cancelledTask.ParentExternalId = existingExternalTask.ExternalId;

        var events = new List<ExternalTask> { cancelledTask, parentAsExternalTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var parentTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == existingExternalTask.Id);

        parentTaskDb.Should().NotBeNull();

        parentTaskDb.ExDate.Should().Be("20241213T230000Z");
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Update_Not_Override_LocationsSnapshot()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, f => f.Random.Hash(10))
            .Generate();

        var providerLocation = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var locationSnapshot = new TaskLocationSnapshotFaker(providerLocation.Id).Generate();

        var task = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.LocationsSnapshot, [locationSnapshot])
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(task);
        await DataContext.AddRangeAsync(providerLocation);
        await DataContext.SaveChangesAsync();

        var externalTask = CreateExternalTask(task.Id);
        externalTask.LocationsSnapshot = [new TaskLocationSnapshotFaker(Guid.NewGuid()).Generate()];

        SetupGoogleService(new ExternalTasksResult { Items = [externalTask] });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var tasksDb = await DataContext.Tasks.AsNoTracking().FirstOrDefaultAsync(x => x.Id == task.Id);

        tasksDb.LocationsSnapshot.Should().BeEquivalentTo([locationSnapshot]);
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Create_Recurring_Task_With_LocationsSnapshot_Based_Off_Of_Existing_Task()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, f => f.Random.Hash(10))
            .Generate();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var contactPerson = new PersonFaker().Generate().ToDataModel();

        var taskContact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, contactPerson.Id)
            .RuleFor(x => x.Email, contactPerson.Email)
            .RuleFor(x => x.PhoneNumber, contactPerson.PhoneNumber)
            .RuleFor(x => x.FirstName, contactPerson.FirstName)
            .RuleFor(x => x.LastName, contactPerson.LastName)
            .Generate()
            .ToDataModel();

        var providerLocation = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var locationSnapshot = new TaskLocationSnapshotFaker(providerLocation.Id).Generate();

        var recurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY;UNTIL=20220921T000000Z"))
            .RuleFor(x => x.ExDate, "20220912T000000Z,20220913T000000Z")
            .RuleFor(x => x.LocationsSnapshot, [locationSnapshot])
            .Generate()
            .ToDataModel()
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    ProviderId = ProviderId
                }
            })
            .WithContact(new List<TaskContactDataModel> { new TaskContactDataModel() { ContactId = taskContact.Id } })
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel() { PersonId = PersonId } });

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            providerItem,
            contactPerson,
            taskContact,
            recurringTask,
            providerLocation);
        await DataContext.SaveChangesAsync();

        var uniqueTitle = Guid.NewGuid().ToString();
        var externalRecurringTask = CreateExternalTask(Guid.NewGuid());
        externalRecurringTask.Title = uniqueTitle;
        externalRecurringTask.Metadata = new ExternalTaskMetadata { TaskId = recurringTask.Id };
        externalRecurringTask.StartDate = new DateTime(2022, 9, 1, 13, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.EndDate = new DateTime(2022, 9, 29, 14, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.RRule = "FREQ=WEEKLY;UNTIL=20220919T000000Z";
        externalRecurringTask.OccurrenceEndDate = new DateTime(2022, 9, 19, 0, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.ExDate = "20220906T000000Z,20220907T000000Z";
        externalRecurringTask.Metadata = new ExternalTaskMetadata { TaskId = recurringTask.Id };
        externalRecurringTask.LocationsSnapshot = [new TaskLocationSnapshotFaker(Guid.NewGuid()).Generate()];

        var events = new List<ExternalTask> { externalRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var previousParentTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == recurringTask.Id);

        previousParentTaskDb.Should().NotBeNull();
        previousParentTaskDb.LocationsSnapshot.Should().BeEquivalentTo([locationSnapshot]);

        // this recurring is a new recurring event caused by "This and following events"
        // using title for filtering since we don't know any reference to it
        var newRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ProviderId == ProviderId && x.Title == uniqueTitle);

        newRecurringTaskDb.Should().NotBeNull();
        newRecurringTaskDb.LocationsSnapshot.Should().BeEquivalentTo(previousParentTaskDb.LocationsSnapshot);
    }

    private async Task Run(ExternalCalendarSyncEventData data)
    {
        var eventMessage = new EventMessage(
            EventType.ExternalCalendarSync,
            new EventData<ExternalCalendarSyncEventData>(data),
            data.CalendarSubscriptionId.ToString(),
            PersonId
        );

        Fixture.QueueMessage(QueueType.Task, eventMessage);
        await Fixture.Run();
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Create_Recurring_Exception_Task_With_LocationsSnapshots_Based_Off_Of_Existing_Task()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, f => f.Random.Hash(10))
            .Generate();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var contactPerson = new PersonFaker().Generate().ToDataModel();

        var providerLocation = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var locationSnapshot = new TaskLocationSnapshotFaker(providerLocation.Id).Generate();

        var taskContact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, contactPerson.Id)
            .RuleFor(x => x.Email, contactPerson.Email)
            .RuleFor(x => x.PhoneNumber, contactPerson.PhoneNumber)
            .RuleFor(x => x.FirstName, contactPerson.FirstName)
            .RuleFor(x => x.LastName, contactPerson.LastName)
            .Generate()
            .ToDataModel();

        var recurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY;UNTIL=20220921T000000Z"))
            .RuleFor(x => x.ExDate, "20220912T000000Z,20220913T000000Z")
            .RuleFor(x => x.LocationsSnapshot, [locationSnapshot])
            .Generate()
            .ToDataModel()
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    ProviderId = ProviderId
                }
            })
            .WithContact(new List<TaskContactDataModel> { new TaskContactDataModel() { ContactId = taskContact.Id } })
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel() { PersonId = PersonId } });

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            providerItem,
            contactPerson,
            taskContact,
            recurringTask,
            providerLocation);
        await DataContext.SaveChangesAsync();

        var exceptionTask = CreateExternalTask(recurringTask.Id);
        exceptionTask.StartDate = new DateTime(2022, 9, 1, 13, 0, 0, DateTimeKind.Utc);
        exceptionTask.EndDate = new DateTime(2022, 9, 29, 14, 0, 0, DateTimeKind.Utc);
        exceptionTask.LocationsSnapshot = [new TaskLocationSnapshotFaker(Guid.NewGuid()).Generate()];

        var events = new List<ExternalTask> { exceptionTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        // this is an exception event caused by choosing "This event"
        // using title for filtering since we don't know any reference to it
        var newExceptionTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ProviderId == ProviderId && x.ParentId == recurringTask.Id);

        var parentTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == recurringTask.Id);

        parentTaskDb.Should().NotBeNull();
        parentTaskDb.LocationsSnapshot.Should().BeEquivalentTo([locationSnapshot]);

        newExceptionTaskDb.Should().NotBeNull();
        newExceptionTaskDb.LocationsSnapshot.Should().BeEquivalentTo(parentTaskDb.LocationsSnapshot);
    }

    [Fact]
    public async Task GoogleCalendarSync_Should_Publish_CalendarInitialSyncCompletedEvent()
    {
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.InitialSyncStatus, InitialSyncStatus.Pending)
            .Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.SaveChangesAsync();

        var syncToken = new Faker().Random.Hash(10);

        SetupGoogleService(new ExternalTasksResult { Items = [], NextSyncToken = syncToken });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var eventPublisher = ResolveService<EventPublisherMock>();
        eventPublisher.SingleAs<CalendarInitialSyncCompletedEvent>(x => x.CalendarSubscriptionId == calendarSubscription.Id)
            .CalendarSubscriptionId.Should().Be(calendarSubscription.Id);
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Sync_Attendees_For_New_Events()
    {
        var faker = new Faker();
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var email1 = faker.Internet.CarePatronEmail();
        var email2 = faker.Internet.CarePatronEmail();

        var existingExternalContact = new ExternalContactFaker(ProviderId, email1).Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            existingExternalContact.ToDataModel());

        await DataContext.SaveChangesAsync();

        var externalTask = CreateExternalTask();

        AddAttendees(externalTask, email1, email2);

        ExternalTask[] events = [externalTask];

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var externalTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .Include(x => x.ExternalContacts)
            .FirstOrDefaultAsync(x => x.ExternalId == externalTask.ExternalId);

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        var taskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == externalTaskDb.Id)
            .ToListAsync();

        externalContacts.Should().HaveCount(2);
        externalContacts.Should().BeEquivalentTo([
            new { Email = email1, },
            new { Email = email2, }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        taskExternalContacts.Should().HaveCount(2);
        taskExternalContacts.Should().BeEquivalentTo([
            new { ExternalContactId = externalContacts[0].Id },
            new { ExternalContactId = externalContacts[1].Id }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Sync_Attendees_For_Existing_Events()
    {
        var faker = new Faker();
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var email1 = faker.Internet.CarePatronEmail();
        var email2 = faker.Internet.CarePatronEmail();

        var existingExternalContact = new ExternalContactFaker(ProviderId, email1).Generate();

        var existingTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .Generate()
            .ToDataModel()
            .WithExternalContact(existingExternalContact.Id);

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            existingExternalContact.ToDataModel(),
            existingTask);

        await DataContext.SaveChangesAsync();

        var externalTask = CreateExternalTask(null, existingTask.ExternalId);

        AddAttendees(externalTask, email2);

        ExternalTask[] events = [externalTask];

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var externalTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .Include(x => x.ExternalContacts)
            .FirstOrDefaultAsync(x => x.ExternalId == externalTask.ExternalId);

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        var taskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == externalTaskDb.Id)
            .ToListAsync();

        externalContacts.Should().HaveCount(2);
        externalContacts.Should().BeEquivalentTo([
            new { Email = email1, },
            new { Email = email2, }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        var expectedExternalContact = externalContacts.FirstOrDefault(x => x.Email == email2);
        var expectedTaskExternalContact = taskExternalContacts.FirstOrDefault(x => x.ExternalContactId == expectedExternalContact.Id);

        taskExternalContacts.Should().HaveCount(1);
        taskExternalContacts.Should().BeEquivalentTo([
            new { expectedTaskExternalContact.ExternalContactId }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Sync_Attendees_For_Deleted_Events()
    {
        var faker = new Faker();
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var email = faker.Internet.CarePatronEmail();

        var existingExternalContact = new ExternalContactFaker(ProviderId, email).Generate();

        var existingTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .Generate()
            .ToDataModel()
            .WithExternalContact(existingExternalContact.Id);

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            existingExternalContact.ToDataModel(),
            existingTask);

        await DataContext.SaveChangesAsync();

        var externalTask = CreateExternalTask(null, existingTask.ExternalId, true);

        ExternalTask[] events = [externalTask];

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var externalTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .Include(x => x.ExternalContacts)
            .FirstOrDefaultAsync(x => x.ExternalId == externalTask.ExternalId);

        externalTaskDb.Should().BeNull();

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        var taskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == existingTask.Id)
            .ToListAsync();

        externalContacts.Should().HaveCount(1);
        externalContacts.Should().BeEquivalentTo([
            new { Email = email, }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        taskExternalContacts.Should().BeEmpty();
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Sync_Attendees_For_Recurring_Task_Based_Off_Of_Existing_Task()
    {
        var faker = new Faker();

        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var existingExternalContacts = new ExternalContactFaker(ProviderId).Generate(2).ToDataModel();

        var originalRecurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY;UNTIL=20220921T000000Z"))
            .RuleFor(x => x.ExDate, "20220912T000000Z")
            .RuleFor(x => x.OccurrenceEndDate, new DateTime(2022, 9, 21, 0, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .Generate()
            .ToDataModel()
            .WithExternalContact(existingExternalContacts.Select(x => x.Id).ToArray());

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(originalRecurringTask);
        await DataContext.AddRangeAsync(existingExternalContacts);
        await DataContext.SaveChangesAsync();

        var newRecurringTask = CreateExternalTask();
        newRecurringTask.ExternalId = $"{originalRecurringTask.ExternalId}_{new Faker().Random.Hash(10)}";
        newRecurringTask.StartDate = new DateTime(2022, 9, 21, 9, 0, 0, DateTimeKind.Utc);
        newRecurringTask.EndDate = new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc);
        newRecurringTask.TimeZone = "Pacific/Auckland";
        newRecurringTask.RRule = "FREQ=WEEKLY";
        newRecurringTask.ExDate = "20220915T000000Z";

        var anotherEmail = faker.Internet.CarePatronEmail();

        var sameExternalContact = existingExternalContacts[0];

        AddAttendees(newRecurringTask, sameExternalContact.Email, anotherEmail);

        var events = new List<ExternalTask> { newRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var newRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newRecurringTask.ExternalId);

        newRecurringTaskDb.Should().NotBeNull();

        var originalParentTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == originalRecurringTask.Id);

        originalParentTaskDb.Should().NotBeNull();

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        externalContacts.Should().HaveCount(3);
        externalContacts.Should().BeEquivalentTo([
            .. existingExternalContacts.Select(x => new { x.Email }),
            new { Email = anotherEmail, }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        var newRecurringTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == newRecurringTaskDb.Id)
            .ToListAsync();

        var newlyAddedExternalContact = externalContacts.FirstOrDefault(x => x.Email == anotherEmail);
        newlyAddedExternalContact.Should().NotBeNull();

        newRecurringTaskExternalContacts.Should().HaveCount(2);
        newRecurringTaskExternalContacts.Should().BeEquivalentTo([
            new { ExternalContactId = sameExternalContact.Id },
            new { ExternalContactId = newlyAddedExternalContact.Id }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        var originalParentTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == originalParentTaskDb.Id)
            .ToListAsync();

        originalParentTaskExternalContacts.Should().HaveCount(2);
        originalParentTaskExternalContacts.Should().BeEquivalentTo(
            existingExternalContacts.Select(x => new { ExternalContactId = x.Id }), 
            opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Sync_Attendees_For_Recurring_Task_Based_Off_Of_Another_External_Task()
    {
        var faker = new Faker();

        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var existingExternalContact = new ExternalContactFaker(ProviderId).Generate().ToDataModel();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel(), existingExternalContact);
        await DataContext.SaveChangesAsync();

        var sameEmail = faker.Internet.CarePatronEmail();
        var email1 = faker.Internet.CarePatronEmail();
        var email2 = faker.Internet.CarePatronEmail();

        var originalRecurringTask = CreateExternalTask();
        originalRecurringTask.StartDate = new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.EndDate = new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.TimeZone = "Pacific/Auckland";
        originalRecurringTask.RRule = "FREQ=WEEKLY;UNTIL=20220921T000000Z";
        originalRecurringTask.ExDate = "20220912T000000Z";
        originalRecurringTask.OccurrenceEndDate = new DateTime(2022, 9, 21, 0, 0, 0, DateTimeKind.Utc);
        AddAttendees(originalRecurringTask, existingExternalContact.Email, sameEmail, email1);

        var newRecurringTask = CreateExternalTask();
        newRecurringTask.ExternalId = $"{originalRecurringTask.ExternalId}_{new Faker().Random.Hash(10)}";
        newRecurringTask.StartDate = new DateTime(2022, 9, 21, 9, 0, 0, DateTimeKind.Utc);
        newRecurringTask.EndDate = new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc);
        newRecurringTask.TimeZone = "Pacific/Auckland";
        newRecurringTask.RRule = "FREQ=WEEKLY";
        newRecurringTask.ExDate = "20220915T000000Z";
        AddAttendees(newRecurringTask, existingExternalContact.Email, sameEmail, email2);

        var events = new List<ExternalTask> { originalRecurringTask, newRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var originalRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == originalRecurringTask.ExternalId);

        originalRecurringTaskDb.Should().NotBeNull();

        var newRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newRecurringTask.ExternalId);

        newRecurringTaskDb.Should().NotBeNull();

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        externalContacts.Should().HaveCount(4);
        externalContacts.Should().BeEquivalentTo([
            new { existingExternalContact.Email },
            new { Email = sameEmail },
            new { Email = email1 },
            new { Email = email2 }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        var externalContactForSameEmail = externalContacts.FirstOrDefault(x => x.Email == sameEmail);
        var externalContactForEmail1 = externalContacts.FirstOrDefault(x => x.Email == email1);
        var externalContactForEmail2 = externalContacts.FirstOrDefault(x => x.Email == email2);

        var originalRecurringTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == originalRecurringTaskDb.Id)
            .ToListAsync();

        originalRecurringTaskExternalContacts.Should().HaveCount(3);
        originalRecurringTaskExternalContacts.Should().BeEquivalentTo([
            new { ExternalContactId = existingExternalContact.Id },
            new { ExternalContactId = externalContactForSameEmail.Id },
            new { ExternalContactId = externalContactForEmail1.Id }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        var newRecurringTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == newRecurringTaskDb.Id)
            .ToListAsync();

        newRecurringTaskExternalContacts.Should().HaveCount(3);
        newRecurringTaskExternalContacts.Should().BeEquivalentTo([
            new { ExternalContactId = existingExternalContact.Id },
            new { ExternalContactId = externalContactForSameEmail.Id },
            new { ExternalContactId = externalContactForEmail2.Id }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Sync_Attendees_For_Recurring_Exception_Task_Based_Off_Of_Existing_Task()
    {
        var faker = new Faker();

        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var existingExternalContact = new ExternalContactFaker(ProviderId).Generate().ToDataModel();

        var originalRecurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 1, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY"))
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .Generate()
            .ToDataModel()
            .WithExternalContact(existingExternalContact.Id);

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(existingExternalContact);
        await DataContext.AddRangeAsync(originalRecurringTask);
        await DataContext.SaveChangesAsync();

        var newExceptionTask = CreateExternalTask();
        newExceptionTask.ParentExternalId = originalRecurringTask.ExternalId;
        newExceptionTask.StartDate = new DateTime(2022, 9, 16, 9, 0, 0, DateTimeKind.Utc);
        newExceptionTask.EndDate = new DateTime(2022, 9, 16, 10, 0, 0, DateTimeKind.Utc);
        newExceptionTask.TimeZone = "Pacific/Auckland";

        var newEmail = faker.Internet.CarePatronEmail();
        AddAttendees(newExceptionTask, existingExternalContact.Email, newEmail);

        var events = new List<ExternalTask> { newExceptionTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var originalRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == originalRecurringTask.ExternalId);
        originalRecurringTaskDb.Should().NotBeNull();

        var newExceptionTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newExceptionTask.ExternalId);

        newExceptionTaskDb.Should().NotBeNull();

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        externalContacts.Should().HaveCount(2);
        externalContacts.Should().BeEquivalentTo([
            new { existingExternalContact.Email },
            new { Email = newEmail }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        var originalRecurringTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == originalRecurringTaskDb.Id)
            .ToListAsync();

        originalRecurringTaskExternalContacts.Should().HaveCount(1);
        originalRecurringTaskExternalContacts.Should().BeEquivalentTo([
            new { ExternalContactId = existingExternalContact.Id }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        var newExceptionTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == newExceptionTaskDb.Id)
            .ToListAsync();

        var extarnalContactForNewEmail = externalContacts.FirstOrDefault(x => x.Email == newEmail);

        newExceptionTaskExternalContacts.Should().HaveCount(2);
        newExceptionTaskExternalContacts.Should().BeEquivalentTo([
            new { ExternalContactId = existingExternalContact.Id },
            new { ExternalContactId = extarnalContactForNewEmail.Id }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }
    
    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_Sync_Attendees_For_Recurring_Exception_Task_Based_Off_Of_External_Task()
    {
        var faker = new Faker();
        
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();
        
        var existingExternalContact = new ExternalContactFaker(ProviderId).Generate().ToDataModel();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel(), existingExternalContact);
        await DataContext.SaveChangesAsync();
        
        var sameEmail = faker.Internet.CarePatronEmail();
        var email1 = faker.Internet.CarePatronEmail();
        var email2 = faker.Internet.CarePatronEmail();

        var originalRecurringTask = CreateExternalTask();
        originalRecurringTask.StartDate = new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.EndDate = new DateTime(2022, 9, 1, 10, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.TimeZone = "Pacific/Auckland";
        originalRecurringTask.RRule = "FREQ=WEEKLY";
        originalRecurringTask.OccurrenceEndDate = new DateTime(2022, 9, 21, 0, 0, 0, DateTimeKind.Utc);
        AddAttendees(originalRecurringTask, existingExternalContact.Email, sameEmail, email1);

        var newExceptionTask = CreateExternalTask();
        newExceptionTask.ParentExternalId = originalRecurringTask.ExternalId;
        newExceptionTask.StartDate = new DateTime(2022, 9, 16, 9, 0, 0, DateTimeKind.Utc);
        newExceptionTask.EndDate = new DateTime(2022, 9, 16, 10, 0, 0, DateTimeKind.Utc);
        newExceptionTask.TimeZone = "Pacific/Auckland";
        AddAttendees(newExceptionTask, existingExternalContact.Email, sameEmail, email2);

        var events = new List<ExternalTask> { originalRecurringTask, newExceptionTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var originalRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == originalRecurringTask.ExternalId);
        
        originalRecurringTaskDb.Should().NotBeNull();

        var newExceptionTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newExceptionTask.ExternalId);
        
        newExceptionTaskDb.Should().NotBeNull();
        
        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();
        
        externalContacts.Should().HaveCount(4);
        externalContacts.Should().BeEquivalentTo([
            new { existingExternalContact.Email },
            new { Email = sameEmail },
            new { Email = email1 },
            new { Email = email2 }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        var externalContactForSameEmail = externalContacts.FirstOrDefault(x => x.Email == sameEmail);
        var externalContactForEmail1 = externalContacts.FirstOrDefault(x => x.Email == email1);
        var externalContactForEmail2 = externalContacts.FirstOrDefault(x => x.Email == email2);
        
        var originalRecurringTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == originalRecurringTaskDb.Id)
            .ToListAsync();
        
        originalRecurringTaskExternalContacts.Should().HaveCount(3);
        originalRecurringTaskExternalContacts.Should().BeEquivalentTo([
            new { ExternalContactId = existingExternalContact.Id },
            new { ExternalContactId = externalContactForSameEmail.Id },
            new { ExternalContactId = externalContactForEmail1.Id }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
        
        var newExceptionTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == newExceptionTaskDb.Id)
            .ToListAsync();
        
        newExceptionTaskExternalContacts.Should().HaveCount(3);
        newExceptionTaskExternalContacts.Should().BeEquivalentTo([
            new { ExternalContactId = existingExternalContact.Id },
            new { ExternalContactId = externalContactForSameEmail.Id },
            new { ExternalContactId = externalContactForEmail2.Id }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_AutoLink_Attendees_For_New_Events()
    {
        var faker = new Faker();
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var anotherEmail = faker.Internet.CarePatronEmail();

        var contact = new ContactFaker(ProviderId).Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            contact.ToDataModel());

        await DataContext.SaveChangesAsync();

        var externalTask = CreateExternalTask();

        AddAttendees(externalTask, contact.Email, anotherEmail);

        ExternalTask[] events = [externalTask];

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var externalTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .Include(x => x.ExternalContacts)
            .FirstOrDefaultAsync(x => x.ExternalId == externalTask.ExternalId);

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        var taskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == externalTaskDb.Id)
            .ToListAsync();

        externalContacts.Should().HaveCount(2);
        externalContacts.Should().Contain(x => x.Email == anotherEmail);
        externalContacts.Should().Contain(x => x.Email == contact.Email && x.ContactId == contact.Id);

        taskExternalContacts.Should().HaveCount(2);
        taskExternalContacts.Should().BeEquivalentTo(externalContacts.Select(x => new { ExternalContactId = x.Id }),
            opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_AutoLink_Attendees_With_Existing_External_Contacts_To_Contacts_For_New_Events()
    {
        var faker = new Faker();
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var externalContact1 = new ExternalContactFaker(ProviderId, faker.Internet.CarePatronEmail()).Generate();
        var externalContact2 = new ExternalContactFaker(ProviderId, faker.Internet.CarePatronEmail()).Generate();

        var contact1 = new ContactFaker(ProviderId)
            .RuleFor(x => x.Email, externalContact1.Email)
            .Generate();

        var contact2 = new ContactFaker(ProviderId)
            .RuleFor(x => x.Email, externalContact2.Email)
            .Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            externalContact1.ToDataModel(),
            externalContact2.ToDataModel(),
            contact1.ToDataModel(),
            contact2.ToDataModel());

        await DataContext.SaveChangesAsync();

        var externalTask = CreateExternalTask();

        AddAttendees(externalTask, externalContact1.Email, externalContact2.Email);

        ExternalTask[] events = [externalTask];

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var externalTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .Include(x => x.ExternalContacts)
            .FirstOrDefaultAsync(x => x.ExternalId == externalTask.ExternalId);

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        var taskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == externalTaskDb.Id)
            .ToListAsync();

        externalContacts.Should().HaveCount(2);
        externalContacts.Should().BeEquivalentTo([
            new { externalContact1.Id, externalContact1.Email, ContactId = contact1.Id },
            new { externalContact2.Id, externalContact2.Email, ContactId = contact2.Id }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        taskExternalContacts.Should().HaveCount(2);
        taskExternalContacts.Should().BeEquivalentTo(externalContacts.Select(x => new { ExternalContactId = x.Id }),
            opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_AutoLink_Attendees_For_Existing_Events()
    {
        var faker = new Faker();
        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var anotherEmail = faker.Internet.CarePatronEmail();

        var contact = new ContactFaker(ProviderId).Generate();
        
        var anotherContactEmail1 = new ContactEmailFaker().Generate();
        var anotherContactEmail2 = new ContactEmailFaker().Generate();

        var anotherContact = new ContactFaker(ProviderId)
            .RuleFor(x => x.Email, anotherContactEmail1.Email)
            .RuleFor(x => x.Emails, [anotherContactEmail1, anotherContactEmail2])
            .Generate()
            .ToDataModel();

        var existingTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            contact.ToDataModel(),
            anotherContact,
            existingTask);

        await DataContext.SaveChangesAsync();

        var externalTask = CreateExternalTask(null, existingTask.ExternalId);

        AddAttendees(externalTask, contact.Email, anotherEmail, anotherContactEmail2.Email);

        ExternalTask[] events = [externalTask];

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var externalTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .Include(x => x.ExternalContacts)
            .FirstOrDefaultAsync(x => x.ExternalId == externalTask.ExternalId);

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        var taskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == externalTaskDb.Id)
            .ToListAsync();

        externalContacts.Should().HaveCount(3);
        externalContacts.Should().Contain(x => x.Email == anotherEmail);
        externalContacts.Should().Contain(x => x.Email == contact.Email && x.ContactId == contact.Id);
        externalContacts.Should().Contain(x => x.Email == anotherContactEmail2.Email && x.ContactId == anotherContact.Id);

        taskExternalContacts.Should().HaveCount(3);
        taskExternalContacts.Should().BeEquivalentTo(externalContacts.Select(x => new { ExternalContactId = x.Id }),
            opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_AutoLink_Attendees_For_Recurring_Task_Based_Off_Of_Existing_Task()
    {
        var faker = new Faker();

        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var contact = new ContactFaker(ProviderId)
            .Generate()
            .ToDataModel();

        var originalRecurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY;UNTIL=20220921T000000Z"))
            .RuleFor(x => x.ExDate, "20220912T000000Z")
            .RuleFor(x => x.OccurrenceEndDate, new DateTime(2022, 9, 21, 0, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(originalRecurringTask);
        await DataContext.AddRangeAsync(contact);
        await DataContext.SaveChangesAsync();

        var newRecurringTask = CreateExternalTask();
        newRecurringTask.ExternalId = $"{originalRecurringTask.ExternalId}_{new Faker().Random.Hash(10)}";
        newRecurringTask.StartDate = new DateTime(2022, 9, 21, 9, 0, 0, DateTimeKind.Utc);
        newRecurringTask.EndDate = new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc);
        newRecurringTask.TimeZone = "Pacific/Auckland";
        newRecurringTask.RRule = "FREQ=WEEKLY";
        newRecurringTask.ExDate = "20220915T000000Z";

        var anotherEmail = faker.Internet.CarePatronEmail();

        AddAttendees(newRecurringTask, contact.Email, anotherEmail);

        var events = new List<ExternalTask> { newRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var newRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newRecurringTask.ExternalId);

        newRecurringTaskDb.Should().NotBeNull();

        var originalParentTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == originalRecurringTask.Id);

        originalParentTaskDb.Should().NotBeNull();

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        externalContacts.Should().HaveCount(2);
        externalContacts.Should().ContainSingle(x => x.Email ==  anotherEmail);
        externalContacts.Should().ContainSingle(x => x.Email ==  contact.Email && x.ContactId == contact.Id);

        var newRecurringTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == newRecurringTaskDb.Id)
            .ToListAsync();

        var newlyAddedExternalContact = externalContacts.FirstOrDefault(x => x.Email == anotherEmail);
        newlyAddedExternalContact.Should().NotBeNull();

        newRecurringTaskExternalContacts.Should().HaveCount(2);
        newRecurringTaskExternalContacts.Should().BeEquivalentTo(externalContacts.Select(x => new { ExternalContactId = x.Id }).ToArray(),
            opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        var originalParentTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == originalParentTaskDb.Id)
            .ToListAsync();

        originalParentTaskExternalContacts.Should().BeEmpty();
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_AutoLink_Attendees_For_Recurring_Task_Based_Off_Of_Another_External_Task()
    {
        var faker = new Faker();

        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var contact = new ContactFaker(ProviderId).Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel(), contact.ToDataModel());
        await DataContext.SaveChangesAsync();

        var anotherEmail = faker.Internet.CarePatronEmail();

        var originalRecurringTask = CreateExternalTask();
        originalRecurringTask.StartDate = new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.EndDate = new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.TimeZone = "Pacific/Auckland";
        originalRecurringTask.RRule = "FREQ=WEEKLY;UNTIL=20220921T000000Z";
        originalRecurringTask.ExDate = "20220912T000000Z";
        originalRecurringTask.OccurrenceEndDate = new DateTime(2022, 9, 21, 0, 0, 0, DateTimeKind.Utc);

        var newRecurringTask = CreateExternalTask();
        newRecurringTask.ExternalId = $"{originalRecurringTask.ExternalId}_{new Faker().Random.Hash(10)}";
        newRecurringTask.StartDate = new DateTime(2022, 9, 21, 9, 0, 0, DateTimeKind.Utc);
        newRecurringTask.EndDate = new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc);
        newRecurringTask.TimeZone = "Pacific/Auckland";
        newRecurringTask.RRule = "FREQ=WEEKLY";
        newRecurringTask.ExDate = "20220915T000000Z";
        AddAttendees(newRecurringTask, contact.Email, anotherEmail);

        var events = new List<ExternalTask> { originalRecurringTask, newRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var originalRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == originalRecurringTask.ExternalId);

        originalRecurringTaskDb.Should().NotBeNull();

        var newRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newRecurringTask.ExternalId);

        newRecurringTaskDb.Should().NotBeNull();

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        externalContacts.Should().HaveCount(2);
        externalContacts.Should().ContainSingle(x => x.Email == anotherEmail);
        externalContacts.Should().ContainSingle(x => x.Email == contact.Email && x.ContactId == contact.Id);

        var externalContactForAnotherEmail = externalContacts.FirstOrDefault(x => x.Email == anotherEmail);
        var externalContactForExistingContact = externalContacts.FirstOrDefault(x => x.ContactId == contact.Id);

        var originalRecurringTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == originalRecurringTaskDb.Id)
            .ToListAsync();

        originalRecurringTaskExternalContacts.Should().BeEmpty();

        var newRecurringTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == newRecurringTaskDb.Id)
            .ToListAsync();

        newRecurringTaskExternalContacts.Should().HaveCount(2);
        newRecurringTaskExternalContacts.Should().BeEquivalentTo(externalContacts.Select(x => new { ExternalContactId = x.Id }).ToArray(),
            opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_AutoLink_Attendees_For_Recurring_Exception_Task_Based_Off_Of_Existing_Task()
    {
        var faker = new Faker();

        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var contact = new ContactFaker(ProviderId).Generate();

        var originalRecurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 1, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY"))
            .RuleFor(x => x.Type, TaskType.External)
            .RuleFor(x => x.ExternalId, x => x.Random.Hash(10))
            .RuleFor(x => x.ExternalCalendarId, calendarSubscription.CalendarId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel());
        await DataContext.AddRangeAsync(contact.ToDataModel());
        await DataContext.AddRangeAsync(originalRecurringTask);
        await DataContext.SaveChangesAsync();

        var newExceptionTask = CreateExternalTask();
        newExceptionTask.ParentExternalId = originalRecurringTask.ExternalId;
        newExceptionTask.StartDate = new DateTime(2022, 9, 16, 9, 0, 0, DateTimeKind.Utc);
        newExceptionTask.EndDate = new DateTime(2022, 9, 16, 10, 0, 0, DateTimeKind.Utc);
        newExceptionTask.TimeZone = "Pacific/Auckland";

        var anotherEmail = faker.Internet.CarePatronEmail();
        AddAttendees(newExceptionTask, contact.Email, anotherEmail);

        var events = new List<ExternalTask> { newExceptionTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var originalRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == originalRecurringTask.ExternalId);
        originalRecurringTaskDb.Should().NotBeNull();

        var newExceptionTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newExceptionTask.ExternalId);

        newExceptionTaskDb.Should().NotBeNull();

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        externalContacts.Should().HaveCount(2);
        externalContacts.Should().ContainSingle(x => x.Email == anotherEmail);
        externalContacts.Should().ContainSingle(x => x.Email == contact.Email && x.ContactId == contact.Id);

        var originalRecurringTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == originalRecurringTaskDb.Id)
            .ToListAsync();

        originalRecurringTaskExternalContacts.Should().BeEmpty();

        var newExceptionTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == newExceptionTaskDb.Id)
            .ToListAsync();

        newExceptionTaskExternalContacts.Should().HaveCount(2);
        newExceptionTaskExternalContacts.Should().BeEquivalentTo(externalContacts.Select(x => new { ExternalContactId = x.Id }).ToArray(),
            opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task GoogleCalendarSync_External_Events_Should_AutoLink_Attendees_For_Recurring_Exception_Task_Based_Off_Of_External_Task()
    {
        var faker = new Faker();

        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .Generate();

        var contact = new ContactFaker(ProviderId).Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(), calendarSubscription.ToDataModel(), contact.ToDataModel());
        await DataContext.SaveChangesAsync();

        var anotherEmail = faker.Internet.CarePatronEmail();

        var originalRecurringTask = CreateExternalTask();
        originalRecurringTask.StartDate = new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.EndDate = new DateTime(2022, 9, 1, 10, 0, 0, DateTimeKind.Utc);
        originalRecurringTask.TimeZone = "Pacific/Auckland";
        originalRecurringTask.RRule = "FREQ=WEEKLY";
        originalRecurringTask.OccurrenceEndDate = new DateTime(2022, 9, 21, 0, 0, 0, DateTimeKind.Utc);

        var newExceptionTask = CreateExternalTask();
        newExceptionTask.ParentExternalId = originalRecurringTask.ExternalId;
        newExceptionTask.StartDate = new DateTime(2022, 9, 16, 9, 0, 0, DateTimeKind.Utc);
        newExceptionTask.EndDate = new DateTime(2022, 9, 16, 10, 0, 0, DateTimeKind.Utc);
        newExceptionTask.TimeZone = "Pacific/Auckland";
        AddAttendees(newExceptionTask, contact.Email, anotherEmail);

        var events = new List<ExternalTask> { originalRecurringTask, newExceptionTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var originalRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == originalRecurringTask.ExternalId);

        originalRecurringTaskDb.Should().NotBeNull();

        var newExceptionTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ExternalId == newExceptionTask.ExternalId);

        newExceptionTaskDb.Should().NotBeNull();

        var externalContacts = await DataContext.ExternalContacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToListAsync();

        externalContacts.Should().HaveCount(2);
        externalContacts.Should().ContainSingle(x => x.Email == anotherEmail);
        externalContacts.Should().ContainSingle(x => x.Email == contact.Email && x.ContactId == contact.Id);

        var originalRecurringTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == originalRecurringTaskDb.Id)
            .ToListAsync();

        originalRecurringTaskExternalContacts.Should().BeEmpty();

        var newExceptionTaskExternalContacts = await DataContext.TaskExternalContacts
            .AsNoTracking()
            .Where(x => x.TaskId == newExceptionTaskDb.Id)
            .ToListAsync();

        newExceptionTaskExternalContacts.Should().HaveCount(2);
        newExceptionTaskExternalContacts.Should().BeEquivalentTo(externalContacts.Select(x => new { ExternalContactId = x.Id }).ToArray(), opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Create_Recurring_Task_Based_Off_Of_Existing_Task_With_Attendee_Status()
    {
        var confirmedStatus = Guid.NewGuid().ToString();

        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, f => f.Random.Hash(10))
            .Generate();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var contactPerson = new PersonFaker().Generate().ToDataModel();

        var taskContact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, contactPerson.Id)
            .RuleFor(x => x.Email, contactPerson.Email)
            .RuleFor(x => x.PhoneNumber, contactPerson.PhoneNumber)
            .RuleFor(x => x.FirstName, contactPerson.FirstName)
            .RuleFor(x => x.LastName, contactPerson.LastName)
            .Generate()
            .ToDataModel();

        var recurringTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.StartDate, new DateTime(2022, 9, 1, 9, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.EndDate, new DateTime(2022, 9, 29, 10, 0, 0, DateTimeKind.Utc))
            .RuleFor(x => x.TimeZone, "Pacific/Auckland")
            .RuleFor(x => x.RRule, new RRule("FREQ=WEEKLY;UNTIL=20220921T000000Z"))
            .RuleFor(x => x.ExDate, "20220912T000000Z,20220913T000000Z")
            .Generate()
            .ToDataModel()
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new()
                {
                    Id = providerItem.Id,
                    ProviderId = ProviderId
                }
            })
            .WithContact(new List<TaskContactDataModel> { new() { ContactId = taskContact.Id, AttendeeStatusId = confirmedStatus } })
            .WithStaff(new List<TaskStaffDataModel> { new() { PersonId = PersonId } });

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(),
            providerItem,
            contactPerson,
            taskContact,
            recurringTask);
        await DataContext.SaveChangesAsync();

        var uniqueTitle = Guid.NewGuid().ToString();
        var externalRecurringTask = CreateExternalTask(Guid.NewGuid());
        externalRecurringTask.Title = uniqueTitle;
        externalRecurringTask.Metadata = new ExternalTaskMetadata { TaskId = recurringTask.Id };
        externalRecurringTask.StartDate = new DateTime(2022, 9, 1, 13, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.EndDate = new DateTime(2022, 9, 29, 14, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.RRule = "FREQ=WEEKLY;UNTIL=20220919T000000Z";
        externalRecurringTask.OccurrenceEndDate = new DateTime(2022, 9, 19, 0, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.ExDate = "20220906T000000Z,20220907T000000Z";
        externalRecurringTask.Metadata = new ExternalTaskMetadata { TaskId = recurringTask.Id };

        var events = new List<ExternalTask> { externalRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        // this recurring is a new recurring event caused by "This and following events"
        // using title for filtering since we don't know any reference to it
        var newRecurringTaskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ProviderId == ProviderId && x.Title == uniqueTitle);

        newRecurringTaskDb.Should().NotBeNull();
        newRecurringTaskDb.Id.Should().NotBe(recurringTask.Id);

        var taskContactsDb = await DataContext.TaskContacts
            .AsNoTracking()
            .Where(x => x.TaskId == newRecurringTaskDb.Id)
            .ToArrayAsync();

        taskContactsDb.Should().HaveCount(1);
        taskContactsDb.First().AttendeeStatusId.Should().Be(confirmedStatus);
    }

    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Publish_Event_For_Non_Existing_Task()
    {
        var eventPublisherMock = ResolveService<EventPublisherMock>();
        eventPublisherMock.Invocations.Clear();
        eventPublisherMock.PublishedEvents.Clear();

        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, string.Empty)
            .RuleFor(x => x.InitialSyncStatus, InitialSyncStatus.InProgress)
            .Generate();

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel());
        await DataContext.SaveChangesAsync();

        var taskId = Guid.NewGuid();
        var externalRecurringTask = CreateExternalTask(taskId);
        externalRecurringTask.Metadata = new ExternalTaskMetadata { TaskId = taskId };
        externalRecurringTask.StartDate = new DateTime(2022, 9, 1, 13, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.EndDate = new DateTime(2022, 9, 29, 14, 0, 0, DateTimeKind.Utc);

        var events = new List<ExternalTask> { externalRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var tasksDb = await DataContext.Tasks
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId)
            .ToArrayAsync();

        tasksDb.Should().BeEmpty();

        var externalTaskDeletedEvents = eventPublisherMock.PublishedEvents
            .Where(e => e.DetailType == nameof(ExternalTaskDeletedEvent))
            .Select(e => (ExternalTaskDeletedEvent)e.Detail)
            .ToList();

        externalTaskDeletedEvents.Should().HaveCount(1);
    }
    
    [Fact]
    public async Task GoogleCalendarSync_Internal_Events_Should_Publish_Event_For_Existing_Task_But_Cancelled()
    {
        var eventPublisherMock = ResolveService<EventPublisherMock>();
        eventPublisherMock.Invocations.Clear();
        eventPublisherMock.PublishedEvents.Clear();

        var connectedApp = GetGoogleConnectedApp();
        var calendarSubscription = new CalendarSubscriptionFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ConnectedAppId, connectedApp.Id)
            .RuleFor(x => x.LastSyncToken, string.Empty)
            .RuleFor(x => x.InitialSyncStatus, InitialSyncStatus.InProgress)
            .Generate();
        
        var person = new PersonFaker().Generate().ToDataModel();
        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Email, person.Email)
            .RuleFor(x => x.PhoneNumber, person.PhoneNumber)
            .RuleFor(x => x.FirstName, person.FirstName)
            .RuleFor(x => x.LastName, person.LastName)
            .Generate()
            .ToDataModel();

        var existingTask = new TaskFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.Type, TaskType.ClientEvent)
            .Generate()
            .ToDataModel()
            .WithContact(new List<TaskContactDataModel> { new() { ContactId = contact.Id, AttendeeStatusId = nameof(TaskContactStatus.Cancelled) } })
            .WithStaff(new List<TaskStaffDataModel> { new() { PersonId = PersonId } });

        await DataContext.AddRangeAsync(connectedApp.ToDataModel(),
            calendarSubscription.ToDataModel(), person, contact, existingTask);
        await DataContext.SaveChangesAsync();

        var externalRecurringTask = CreateExternalTask(existingTask.Id);
        externalRecurringTask.Metadata = new ExternalTaskMetadata { TaskId = existingTask.Id };
        externalRecurringTask.StartDate = new DateTime(2022, 9, 1, 13, 0, 0, DateTimeKind.Utc);
        externalRecurringTask.EndDate = new DateTime(2022, 9, 29, 14, 0, 0, DateTimeKind.Utc);

        var events = new List<ExternalTask> { externalRecurringTask };

        SetupGoogleService(new ExternalTasksResult { Items = events.ToArray() });

        await Run(new ExternalCalendarSyncEventData(calendarSubscription.Id));

        var taskDb = await DataContext.Tasks
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId && x.Id == existingTask.Id)
            .ToArrayAsync();

        taskDb.Should().NotBeNull();

        var externalTaskDeletedEvents = eventPublisherMock.PublishedEvents
            .Where(e => e.DetailType == nameof(ExternalTaskDeletedEvent))
            .Select(e => (ExternalTaskDeletedEvent)e.Detail)
            .ToList();

        externalTaskDeletedEvents.Should().HaveCount(1);
    }

    
    private void SetupGoogleService(ExternalTasksResult eventsResult, bool invalidSyncToken = false)
    {
        var connectedCalendarRepo = Fixture.TestServer.Services.GetService<ConnectedCalendarRepositoryMock>();

        connectedCalendarRepo.Setup(x => x.GetCalendar(It.IsAny<ConnectedApp>(), It.IsAny<string>()))
            .ReturnsAsync(new CalendarResult
            {
                CalendarId = "Test Calendar",
                CalendarName = "Test Calendar",
                TimeZone = "Pacific/Auckland"
            });

        if (invalidSyncToken)
        {
            connectedCalendarRepo.Setup(x => x.GetEventsResult(It.IsAny<ConnectedApp>(), It.IsAny<SimplePerson>(), It.IsAny<GetEventsOptions>()))
                .ThrowsAsync(new InvalidExternalCalendarSyncToken("Invalid sync token"));
        }
        else
        {
            connectedCalendarRepo.Setup(x => x.GetEventsResult(It.IsAny<ConnectedApp>(), It.IsAny<SimplePerson>(), It.IsAny<GetEventsOptions>()))
                .ReturnsAsync(eventsResult);
        }
    }

    private ConnectedApp GetGoogleConnectedApp()
    {
        return new ConnectedAppFaker()
            .WithPerson(PersonId)
            .WithProvider(ProviderId)
            .WithProductCode(ConnectedAppProducts.Google)
            .Generate();
    }

    private ExternalTask CreateExternalTask(Guid? id = null, string externalId = null, bool isCancelled = false, bool isFree = false)
    {
        var task = new TaskFaker()
            .RuleFor(x => x.StartDate, f => f.Date.Future().ToUniversalTime())
            .RuleFor(x => x.EndDate, (f, t) => t.StartDate.AddHours(1).ToUniversalTime())
            .RuleFor(x => x.Title, f => f.Lorem.Word())
            .RuleFor(x => x.Description, f => f.Lorem.Sentence())
            .RuleFor(x => x.TimeZone, f => "Pacific/Auckland")
            .RuleFor(x => x.Type, id.HasValue ? TaskType.Standard : TaskType.External)
            .RuleFor(x => x.AllDay, false)
            .RuleFor(x => x.IsFree, isFree)
            .Generate();

        if (id.HasValue)
        {
            task.Id = id.Value;
        }
        else
        {
            task.ExternalId = externalId ?? new Faker().Random.Hash(10);
        }

        var mapper = Fixture.TestServer.Services.GetRequiredService<IMapper>();
        var externalTask = mapper.Map<ExternalTask>(task);
        externalTask.ExternalStatus = isCancelled ? "cancelled" : "confirmed";

        return externalTask;
    }

    private void AddAttendees(ExternalTask externalTask, params Email[] emails)
    {
        externalTask.ExternalContacts = emails.Select(x => new TaskExternalContact(x)).ToArray();
    }

    private async Task AssertTasks(List<ExternalTask> externalTasks, string externalCalendarId)
    {
        var tasksDb = await DataContext.Tasks
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId && x.ExternalCalendarId == externalCalendarId)
            .ToListAsync();

        var count = externalTasks.Count(x => x.ExternalStatus != "cancelled");
        tasksDb.Count.Should().Be(count);

        foreach (var task in externalTasks)
        {
            var existingTask = tasksDb.FirstOrDefault(x => x.ExternalId == task.ExternalId);
            if (task.ExternalStatus == "cancelled")
            {
                existingTask.Should().BeNull();
            }
            else
            {
                AssertTask(task, existingTask);
            }
        }
    }

    private void AssertTask(ExternalTask externalTask, TaskDataModel task, TaskType taskType = TaskType.External)
    {
        task.Should().NotBeNull();
        task.Type.Should().Be(taskType);
        task.Title.Should().Be(externalTask.Title);
        task.Description.Should().Be(externalTask.Description);
        task.StartDate.Should().BeCloseTo(externalTask.StartDate, TimeSpan.FromSeconds(1));
        task.EndDate.Should().BeCloseTo(externalTask.EndDate, TimeSpan.FromSeconds(1));
        task.TimeZone.Should().Be(externalTask.TimeZone);
        task.Location.Should().Be(externalTask.Location);
        task.VirtualLocationProduct.Should().Be(externalTask.VirtualLocationProduct);
        task.LocationType.Should().Be(externalTask.LocationType);
        task.AllDay.Should().Be(externalTask.AllDay);
    }
}