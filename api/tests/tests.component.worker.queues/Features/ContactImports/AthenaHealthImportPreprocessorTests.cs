using System.IO;
using System.Linq;
using Agents.Module.AgentRunners;
using Agents.Module.Exceptions;
using Agents.Sdk.Types;
using carepatron.core.Application.Contacts;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Utilities;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Fixtures;
using tests.common.Mocks;
using tests.component.worker.queues.Setup;

namespace tests.component.worker.queues.Features.ContactImports;

public class AthenaHealthImportPreprocessorTests : WorkerComponentTestBase<QueueWorkerFixture>
{
    private readonly FileStorageRepositoryMock _fileStorageRepositoryMock;

    public AthenaHealthImportPreprocessorTests(QueueWorkerFixture testFixture) : base(testFixture)
    {
        _fileStorageRepositoryMock = Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
        _fileStorageRepositoryMock.UseRealImplementation();
    }

    private readonly Guid _fileImportId = new("9f8b5a8e-3f73-45a7-a4a7-f7a3f0a561bc");

    private string _agentOutput = @"
{
    ""mappings"": [],
    ""suggestedCustomFields"": [],
    ""suggestedLayoutContainers"": []
}";

    [Fact]
    public async Task AthenaHealthImportPreprocessor_Test()
    {
        MockAgentOutput(_agentOutput);
        var dataSchema = new DataSchemaFaker(SchemaTypes.ContactSchema, ProviderId).Generate();

        var layoutSchema = new LayoutSchemaFaker(SchemaTypes.ContactSchemaLayoutView,
                SchemaTypes.ContactSchema,
                ProviderId,
                ContactCoreSchema.CurrentSchemaContext.GetLayoutElements())
            .Generate();

        var importSummary = new ContactImportSummaryFaker(ProviderId, PersonId)
            .RuleFor(x => x.FileId, _fileImportId)
            .RuleFor(x => x.Status, ImportSummaryStatus.Draft)
            .RuleFor(x => x.ImportType, ImportType.Advanced)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(importSummary, dataSchema.ToDataModel(), layoutSchema.ToDataModel());

        await DataContext.SaveChangesAsync();

        var eventData = new PreprocessImportContactsEvent(importSummary.Id, ExternalContactImportSource.AI_AthenaHealth);

        var message = new EventMessage(
            EventType.PreprocessImportContacts,
            new EventData<PreprocessImportContactsEvent>(eventData),
            $"{ProviderId}-import-contacts-preprocessing",
            PersonId
        );

        Fixture.QueueMessage(QueueType.Task, message);
        await Fixture.Run();

        // assert import summary
        var importSummaryDb = await DataContext.ContactImportSummaries
            .AsNoTracking()
            .Where(x => x.Id == importSummary.Id)
            .FirstOrDefaultAsync();

        importSummaryDb.Should().NotBeNull();
        importSummaryDb.FileId.Should().NotBe(_fileImportId); // replaced by csv
        importSummaryDb.FileName.Should().Be($"{importSummaryDb.FileId}.csv");
        importSummaryDb.Status.Should().Be(ImportSummaryStatus.ReadyForMapping);
        importSummaryDb.SchemaFileId.Should().HaveValue();

        // assert generated csv file
        using var csvFileStream = await GetFileStream(importSummaryDb.FileId);

        var (_, rows) = SpreadsheetUtilities.ParseToContactImportRows(csvFileStream, importSummaryDb.FileName, 10);

        var user1 = rows.FirstOrDefault(x => x.ContainsKey("FirstName") && x["FirstName"] == "Ikura");
        var user2 = rows.FirstOrDefault(x => x.ContainsKey("FirstName") && x["FirstName"] == "Alan");
        
        user1.Should().NotBeNull();
        user2.Should().NotBeNull();
        
        // assert uploaded files
        var files = await _fileStorageRepositoryMock.ListPathFiles(importSummary.Id.ToString(),
            20,
            null,
            FileLocationType.ClientImport,
            false);
        
        files.Should().HaveCount(3);
        
        var user1Id = Guid.Parse(user1[ContactsConstants.ImportContactTemporaryIdKey]);
        var user2Id = Guid.Parse(user2[ContactsConstants.ImportContactTemporaryIdKey]);
        
        var user1Path = $"{importSummary.Id}/{user1Id}/Files";
        files.Should().ContainSingle(x => x.Equals($"{user1Path}/Assessment.txt"));
        
        var user2Path = $"{importSummary.Id}/{user2Id}/Files";
        files.Should().ContainSingle(x => x.Equals($"{user2Path}/Assessment.txt"));
        files.Should().ContainSingle(x => x.Equals($"{user2Path}/Final Assessment.txt"));
        
        // start import process
        ImportContactsOption[] importOptions =
        [
            new(nameof(Contact.FirstName), "First Name", null, ImportContactsFieldOption.WholeField, null, false),
            new(nameof(Contact.LastName), "Last Name", null, ImportContactsFieldOption.WholeField, null, false),
        ];
        
        message = new EventMessage(
            EventType.ImportContactsFromAiCsv,
            new
            {
                Data = new
                {
                    ProviderId,
                    PersonId,
                    ContactImportSummaryId = importSummary.Id,
                    MappedColumns = importOptions,
                    DataSchema = dataSchema,
                },
            },
            Guid.NewGuid().ToString(),
            PersonId
        );
        
        Fixture.QueueMessage(QueueType.Task, message);
        await Fixture.Run();
        
        // assert contacts
        Guid[] expectedContactIds =
        [
            user1Id, user2Id
        ];
        
        var contacts = await DataContext.Contacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId && expectedContactIds.Contains(x.Id))
            .ToListAsync();
        
        contacts.Should().HaveCount(2);
        
        // assert contact files
        var contactFiles = await DataContext.ContactFiles
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId && x.ContactId.HasValue && expectedContactIds.Contains(x.ContactId.Value))
            .ToListAsync();
        
        contactFiles.Should().HaveCount(3);
        
        var user1Files = contactFiles.Where(x => x.ContactId == user1Id).ToArray();
        user1Files.Should().BeEquivalentTo([
            new
            {
                FileName = "Assessment.txt",
                FileExtension = "txt",
                CreatedByPersonId = PersonId,
            }
        ], opt => opt.ExcludingMissingMembers());
        
        var user2Files = contactFiles.Where(x => x.ContactId == user2Id).ToArray();
        user2Files.Should().BeEquivalentTo([
            new
            {
                FileName = "Assessment.txt",
                FileExtension = "txt",
                CreatedByPersonId = PersonId,
            },
            new
            {
                FileName = "Final Assessment.txt",
                FileExtension = "txt",
                CreatedByPersonId = PersonId,
            }
        ], opt => opt.ExcludingMissingMembers());
        
        // assert clean up file storage - moved to different directories
        files = await _fileStorageRepositoryMock.ListPathFiles(importSummary.Id.ToString(),
            20,
            null,
            FileLocationType.ClientImport,
            false);
        
        files.Should().BeEmpty();
        
        // assert new directories exist
        foreach (var file in contactFiles)
        {
            using var fileInfo = await _fileStorageRepositoryMock.GetObjectStream(file.Id, FileLocationType.Files);
            fileInfo.Should().NotBeNull();
        }
    }

    private async Task<MemoryStream> GetFileStream(Guid fileId)
    {
        await using var fileStream = await _fileStorageRepositoryMock.GetObjectStream(fileId, FileLocationType.ClientImport);

        var memoryStream = new MemoryStream();
        await fileStream.CopyToAsync(memoryStream);
        memoryStream.Seek(0, SeekOrigin.Begin);

        return memoryStream;
    }
    
    private void MockAgentOutput(string jsonString = "", bool withError = false)
    {
        ResolveService<AgentClientMock>().Reset();

        ResolveService<AgentClientMock>()
            .Setup(x => x.Run(It.IsAny<Agent>(), It.IsAny<UserInput[]>()))
            .ReturnsAsync((Agent agent, UserInput[] messages) =>
            {
                return new AgentResponse<string>()
                {
                    Output = jsonString,
                    Error = withError ? new OutputSchemaException(jsonString) : null
                };
            });
    }
}