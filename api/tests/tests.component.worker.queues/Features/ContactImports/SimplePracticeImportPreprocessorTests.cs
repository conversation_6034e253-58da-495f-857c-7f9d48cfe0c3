using System.IO;
using System.Linq;
using Agents.Module.AgentRunners;
using Agents.Module.Exceptions;
using Agents.Sdk.Types;
using carepatron.core.Application.Contacts;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Utilities;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Newtonsoft.Json;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Fixtures;
using tests.common.Mocks;
using tests.component.worker.queues.Setup;

namespace tests.component.worker.queues.Features.ContactImports;

public class SimplePracticeImportPreprocessorTests : WorkerComponentTestBase<QueueWorkerFixture>
{
    private readonly FileStorageRepositoryMock _fileStorageRepositoryMock;

    public SimplePracticeImportPreprocessorTests(QueueWorkerFixture testFixture) : base(testFixture)
    {
        _fileStorageRepositoryMock = Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
        _fileStorageRepositoryMock.UseRealImplementation();
    }

    private readonly Guid _fileImportId = new("3cb6bfcd-d4e5-4352-8943-28fca03d3245");

    private string _agentOutput = @"
{
    ""mappings"": [
        {
            ""carepatronFieldName"": ""FirstName"",
            ""spreadsheetFieldName"": ""Full name"",
            ""fieldOptions"": ""FirstPart"",
            ""delimiter"": "" ""
        },
        {
            ""carepatronFieldName"": ""MiddleNames"",
            ""spreadsheetFieldName"": ""Middle Name"",
            ""fieldOptions"": ""MiddlePart"",
            ""delimiter"": "" ""
        },
        {
            ""carepatronFieldName"": ""LastName"",
            ""spreadsheetFieldName"": ""Full name"",
            ""fieldOptions"": ""LastPart"",
            ""delimiter"": "" ""
        },
        {
            ""carepatronFieldName"": ""Email"",
            ""spreadsheetFieldName"": ""E-mail Address"",
            ""fieldOptions"": ""WholeField""
        },
        {
            ""carepatronFieldName"": ""Address"",
            ""spreadsheetMultipleFieldNames"": [""Address street"", ""Address city"", ""Address state"", ""Address zip""],
            ""fieldOptions"": ""WholeField"",
            ""delimiter"": "", "",
            ""isMultiple"": true,
        }
    ],
    ""suggestedCustomFields"": [
        {
            ""columnMapping"": {
                ""carepatronFieldName"": ""Favorite Color"",
                ""spreadsheetMultipleFieldNames"": [""Favorite Color 1"", ""Favorite Color 2"", ""Favorite Color 3""],
                ""fieldOptions"": ""WholeField"",
                ""isMultiple"": true
            },
            ""layoutContainer"": ""Others"",
            ""property"": {
                ""freeSolo"": true,
                ""multiple"": true,
                ""type"": ""OptionSet"",
                ""options"": {
                    ""Red"": {
                        ""id"": ""Red"",
                        ""displayName"": ""Red"",
                        ""orderIndex"": 1
                    },
                    ""Blue"": {
                        ""id"": ""Blue"",
                        ""displayName"": ""Blue"",
                        ""orderIndex"": 2
                    }
                }
            }
        },
    ],
    ""suggestedLayoutContainers"": [
        {
            ""heading"": ""Others""
        },
        {
            ""heading"": ""Emergency Contacts""
        }
    ]
}";

    [Fact]
    public async Task SimplePracticeImportPreprocessor_Test()
    {
        MockAgentOutput(_agentOutput);
        var dataSchema = new DataSchemaFaker(SchemaTypes.ContactSchema, ProviderId).Generate();

        var layoutSchema = new LayoutSchemaFaker(SchemaTypes.ContactSchemaLayoutView,
                SchemaTypes.ContactSchema,
                ProviderId,
                ContactCoreSchema.CurrentSchemaContext.GetLayoutElements())
            .Generate();

        var importSummary = new ContactImportSummaryFaker(ProviderId, PersonId)
            .RuleFor(x => x.FileId, _fileImportId)
            .RuleFor(x => x.Status, ImportSummaryStatus.Draft)
            .RuleFor(x => x.ImportType, ImportType.Advanced)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(importSummary, dataSchema.ToDataModel(), layoutSchema.ToDataModel());

        await DataContext.SaveChangesAsync();

        var eventData = new PreprocessImportContactsEvent(importSummary.Id, ExternalContactImportSource.AI_SimplePractice);

        var message = new EventMessage(
            EventType.PreprocessImportContacts,
            new EventData<PreprocessImportContactsEvent>(eventData),
            $"{ProviderId}-import-contacts-preprocessing",
            PersonId
        );

        Fixture.QueueMessage(QueueType.Task, message);
        await Fixture.Run();

        // assert import summary
        var importSummaryDb = await DataContext.ContactImportSummaries
            .AsNoTracking()
            .Where(x => x.Id == importSummary.Id)
            .FirstOrDefaultAsync();

        importSummaryDb.Should().NotBeNull();
        importSummaryDb.FileId.Should().NotBe(_fileImportId); // replaced by csv
        importSummaryDb.FileName.Should().Be($"{importSummaryDb.FileId}.csv");
        importSummaryDb.Status.Should().Be(ImportSummaryStatus.ReadyForMapping);
        importSummaryDb.SchemaFileId.Should().HaveValue();

        // assert generated csv file
        using var csvFileStream = await GetFileStream(importSummaryDb.FileId);

        var (_, rows) = SpreadsheetUtilities.ParseToContactImportRows(csvFileStream, importSummaryDb.FileName, 10);

        var user1 = rows.FirstOrDefault(x => x.ContainsKey("Formatted Name") && x["Formatted Name"] == "Karen Appleseed");
        var user2 = rows.FirstOrDefault(x => x.ContainsKey("Formatted Name") && x["Formatted Name"] == "Jamie Appleseed");
        var user3 = rows.FirstOrDefault(x => x.ContainsKey("Formatted Name") && x["Formatted Name"] == "AAA AAA");

        user1.Should().NotBeNull();
        user2.Should().NotBeNull();
        user3.Should().NotBeNull();

        // assert uploaded files
        var files = await _fileStorageRepositoryMock.ListPathFiles(importSummary.Id.ToString(),
            20,
            null,
            FileLocationType.ClientImport,
            false);

        files.Should().HaveCount(7);

        var user1Id = Guid.Parse(user1[ContactsConstants.ImportContactTemporaryIdKey]);
        var user2Id = Guid.Parse(user2[ContactsConstants.ImportContactTemporaryIdKey]);
        var user3Id = Guid.Parse(user3[ContactsConstants.ImportContactTemporaryIdKey]);

        var user3Path = $"{importSummary.Id}/{user3Id}/Files";
        files.Should().ContainSingle(x => x.Equals($"{user3Path}/Chart Note 2024-02-20 211105 544051811.pdf"));

        var user2Path = $"{importSummary.Id}/{user2Id}/Files";

        files.Should().ContainSingle(x => x.Equals($"{user2Path}/Chart Note 2024-02-20 193742 544036097.pdf"));
        files.Should().ContainSingle(x => x.Equals($"{user2Path}/Diagnosis and treatment plan - 29286951.pdf"));
        files.Should().ContainSingle(x => x.Equals($"{user2Path}/Progress Note 2024-02-19 140000 544036099.pdf"));
        files.Should().ContainSingle(x => x.Equals($"{user2Path}/Questionnaire - GAD - 60144168.pdf"));
        files.Should().ContainSingle(x => x.Equals($"{user2Path}/Questionnaire - GAD - 60144169.pdf"));
        files.Should().ContainSingle(x => x.Equals($"{user2Path}/Psychotherapy Note 2024-02-19 140000 544036100.pdf"));

        // start import process
        ImportContactsOption[] importOptions =
        [
            new(nameof(Contact.FirstName), "First Name", null, ImportContactsFieldOption.WholeField, null, false),
            new(nameof(Contact.LastName), "Last Name", null, ImportContactsFieldOption.WholeField, null, false),
            new(nameof(Contact.Email), "Email (AmericaOnline)", null, ImportContactsFieldOption.WholeField, null, false)
        ];

        message = new EventMessage(
            EventType.ImportContactsFromAiCsv,
            new
            {
                Data = new
                {
                    ProviderId,
                    PersonId,
                    ContactImportSummaryId = importSummary.Id,
                    MappedColumns = importOptions,
                    DataSchema = dataSchema,
                },
            },
            Guid.NewGuid().ToString(),
            PersonId
        );

        Fixture.QueueMessage(QueueType.Task, message);
        await Fixture.Run();

        // assert contacts
        Guid[] expectedContactIds =
        [
            user1Id, user2Id, user3Id
        ];

        var contacts = await DataContext.Contacts
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId && expectedContactIds.Contains(x.Id))
            .ToListAsync();

        contacts.Should().HaveCount(3);

        // assert contact files
        var contactFiles = await DataContext.ContactFiles
            .AsNoTracking()
            .Where(x => x.ProviderId == ProviderId && x.ContactId.HasValue && expectedContactIds.Contains(x.ContactId.Value))
            .ToListAsync();

        contactFiles.Should().HaveCount(7);

        var user3Files = contactFiles.Where(x => x.ContactId == user3Id).ToArray();
        user3Files.Should().BeEquivalentTo([
            new
            {
                FileName = "Chart Note 2024-02-20 211105 544051811.pdf",
                FileExtension = "pdf",
                CreatedByPersonId = PersonId,
            }
        ], opt => opt.ExcludingMissingMembers());

        var user2Files = contactFiles.Where(x => x.ContactId == user2Id).ToArray();
        user2Files.Should().BeEquivalentTo([
            new
            {
                FileName = "Chart Note 2024-02-20 193742 544036097.pdf",
                FileExtension = "pdf",
                CreatedByPersonId = PersonId,
            },
            new
            {
                FileName = "Diagnosis and treatment plan - 29286951.pdf",
                FileExtension = "pdf",
                CreatedByPersonId = PersonId,
            },
            new
            {
                FileName = "Progress Note 2024-02-19 140000 544036099.pdf",
                FileExtension = "pdf",
                CreatedByPersonId = PersonId,
            },
            new
            {
                FileName = "Questionnaire - GAD - 60144168.pdf",
                FileExtension = "pdf",
                CreatedByPersonId = PersonId,
            },
            new
            {
                FileName = "Questionnaire - GAD - 60144169.pdf",
                FileExtension = "pdf",
                CreatedByPersonId = PersonId,
            },
            new
            {
                FileName = "Psychotherapy Note 2024-02-19 140000 544036100.pdf",
                FileExtension = "pdf",
                CreatedByPersonId = PersonId,
            }
        ], opt => opt.ExcludingMissingMembers());

        // assert clean up file storage - moved to different directories
        files = await _fileStorageRepositoryMock.ListPathFiles(importSummary.Id.ToString(),
            20,
            null,
            FileLocationType.ClientImport,
            false);

        files.Should().BeEmpty();

        // assert new directories exist
        foreach (var file in contactFiles)
        {
            using var fileInfo = await _fileStorageRepositoryMock.GetObjectStream(file.Id, FileLocationType.Files);
            fileInfo.Should().NotBeNull();
        }

        //assert uploaded json file

        var schemaFileId = importSummaryDb.SchemaFileId.Value;
        using var schemaFileStream = await GetFileStream(schemaFileId);
        schemaFileStream.Should().NotBeNull();

        var jsonString = await new StreamReader(schemaFileStream).ReadToEndAsync();
        var mappingsResponse = JsonConvert.DeserializeObject<ImportContactMappingsResponse>(jsonString);
        mappingsResponse.Should().NotBeNull();

        mappingsResponse.Mappings.Should().HaveCount(5);
        mappingsResponse.Mappings.Should().BeEquivalentTo(
        [
            new ImportContactsOption { CarepatronFieldName = "FirstName", SpreadsheetFieldName = "Full name", FieldOptions = ImportContactsFieldOption.FirstPart, Delimiter = " ", IsMultiple = false },
            new ImportContactsOption { CarepatronFieldName = "MiddleNames", SpreadsheetFieldName = "Middle Name", FieldOptions = ImportContactsFieldOption.MiddlePart, Delimiter = " ", IsMultiple = false },
            new ImportContactsOption { CarepatronFieldName = "LastName", SpreadsheetFieldName = "Full name", FieldOptions = ImportContactsFieldOption.LastPart, Delimiter = " ", IsMultiple = false },
            new ImportContactsOption { CarepatronFieldName = "Email", SpreadsheetFieldName = "E-mail Address", FieldOptions = ImportContactsFieldOption.WholeField, Delimiter = null, IsMultiple = false },
            new ImportContactsOption
            {
                CarepatronFieldName = "Address",
                SpreadsheetMultipleFieldNames = new[] { "Address street", "Address city", "Address state", "Address zip" },
                FieldOptions = ImportContactsFieldOption.WholeField,
                Delimiter = ", ",
                IsMultiple = true
            }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        mappingsResponse.SuggestedCustomFields.Should().HaveCount(1);
        mappingsResponse.SuggestedCustomFields.Should().BeEquivalentTo(
        [
            new SuggestedCustomField
            {
                ColumnMapping = new ImportContactsOption
                {
                    CarepatronFieldName = "Favorite Color",
                    SpreadsheetMultipleFieldNames = new[] { "Favorite Color 1", "Favorite Color 2", "Favorite Color 3" },
                    FieldOptions = ImportContactsFieldOption.WholeField,
                    IsMultiple = true
                },
                LayoutContainer = "Others",
                Property = new
                {
                    freeSolo = true,
                    multiple = true,
                    type = "OptionSet",
                    options = new
                    {
                        Red = new { id = "Red", displayName = "Red", orderIndex = 1 },
                        Blue = new { id = "Blue", displayName = "Blue", orderIndex = 2 }
                    }
                }
            },
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        mappingsResponse.SuggestedLayoutContainers.Should().HaveCount(2);
        mappingsResponse.SuggestedLayoutContainers.Should().BeEquivalentTo(
        [
            new SuggestedLayoutContainer { Heading = "Others" },
            new SuggestedLayoutContainer { Heading = "Emergency Contacts" }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    private async Task<MemoryStream> GetFileStream(Guid fileId)
    {
        await using var fileStream = await _fileStorageRepositoryMock.GetObjectStream(fileId, FileLocationType.ClientImport);

        var memoryStream = new MemoryStream();
        await fileStream.CopyToAsync(memoryStream);
        memoryStream.Seek(0, SeekOrigin.Begin);

        return memoryStream;
    }

    private void MockAgentOutput(string jsonString = "", bool withError = false)
    {
        ResolveService<AgentClientMock>().Reset();

        ResolveService<AgentClientMock>()
            .Setup(x => x.Run(It.IsAny<Agent>(), It.IsAny<UserInput[]>()))
            .ReturnsAsync((Agent agent, UserInput[] messages) =>
            {
                return new AgentResponse<string>()
                {
                    Output = jsonString,
                    Error = withError ? new OutputSchemaException(jsonString) : null
                };
            });
    }
}