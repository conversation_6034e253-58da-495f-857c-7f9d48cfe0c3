﻿using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Schema.Models.Properties;
using carepatron.core.Events.Events;
using carepatron.core.Models.QueueMessages;
using carepatron.infra.sql.Models.Contact;
using carepatron.infra.sql.Models.Person;
using carepatron.infra.sql.Models.Provider;
using CsvHelper;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Newtonsoft.Json.Linq;
using tests.common.Data.Datasets;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Fixtures;
using tests.common.Mocks;
using tests.component.worker.queues.Setup;

namespace tests.component.worker.queues.Features.ContactImports
{
    [Trait(nameof(CodeOwner), CodeOwner.Clients)]
    public class ImportContactsFromCsvTests : WorkerComponentTestBase<QueueWorkerFixture>
    {
        public ImportContactsFromCsvTests(QueueWorkerFixture fixture)
            : base(fixture) { }

        private const string resourceFilePath = "./Resources/";
        private const string clientImportStatusesCsvFileName = "Client Import With Statuses.csv";
        private const string clientImportLanguagesCsvFileName = "Client Import With Languages.csv";
        private const string clientImportWithTagsCsvFileName= "Client Import With Tags.csv";
        private const string clientImportWithIssuesCsvFileName= "Client Import With Issues.csv";
        private const string clientImportWithDuplicatesCsvFileName= "Client Import With Duplicates.csv";
        private const string clientImportForAiMappingCsvFileName= "Client import for ai mapping.csv";

        [Fact]
        public async Task ImportContactsFromCsvEventHandler_should_import_contacts()
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData();

            var fullFilePath = Path.Combine(resourceFilePath, clientImportStatusesCsvFileName);
            using Stream fileStream = System.IO.File.OpenRead(fullFilePath);

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ReturnsAsync(fileStream);

            var message = CreateMessage(provider, person, importSummary, dataSchema);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();

            var contactsDb = await DataContext
                .Contacts.AsNoTracking()
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();

            var importSummaryDb = await DataContext
                .ContactImportSummaries.AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == importSummary.Id);

            contactsDb.Should().HaveCount(19);
            importSummaryDb.Status.Should().Be(ImportSummaryStatus.Successful);
        }
        
        [Fact]
        public async Task ImportContactsFromCsvEventHandler_should_import_contacts_with_isClient_false()
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData(isContact: true);

            var fullFilePath = Path.Combine(resourceFilePath, clientImportStatusesCsvFileName);
            using Stream fileStream = System.IO.File.OpenRead(fullFilePath);

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ReturnsAsync(fileStream);

            var message = CreateMessage(provider, person, importSummary, dataSchema);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();

            var contactsDb = await DataContext
                .Contacts.AsNoTracking()
                .Where(x => x.ProviderId == provider.Id && !x.IsClient)
                .ToListAsync();

            var importSummaryDb = await DataContext
                .ContactImportSummaries.AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == importSummary.Id);

            contactsDb.Should().HaveCount(19);
            importSummaryDb.Status.Should().Be(ImportSummaryStatus.Successful);
        }

        [Fact]
        public async Task ImportContactsFromCsvEventHandler_should_import_contacts_data()
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData();

            var fullFilePath = Path.Combine(resourceFilePath, clientImportStatusesCsvFileName);
            using Stream fileStream = System.IO.File.OpenRead(fullFilePath);

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ReturnsAsync(fileStream);

            var message = CreateMessage(provider, person, importSummary, dataSchema);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();

            var contactsDb = await DataContext
                .Contacts.AsNoTracking()
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();

            var importSummaryDb = await DataContext
                .ContactImportSummaries.AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == importSummary.Id);

            var expected = new[]
            {
                new
                {
                    FirstName = "David",
                    LastName = string.Empty,
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = (string)null,
                },
                new
                {
                    FirstName = "Brian",
                    LastName = "McKnight",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+642140943243",
                },
                new
                {
                    FirstName = "John",
                    LastName = "Baptist",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+642140938890",
                },
                new
                {
                    FirstName = "Karen",
                    LastName = "Long",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789901",
                },
                new
                {
                    FirstName = "Rupert",
                    LastName = "Spira",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789915",
                },
                new
                {
                    FirstName = "Hanabasami",
                    LastName = "Kyo",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789902",
                },
                new
                {
                    FirstName = "Eckhart",
                    LastName = "Tolle",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789903",
                },
                new
                {
                    FirstName = "Aaron",
                    LastName = "Abke",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789904",
                },
                new
                {
                    FirstName = "Ikura",
                    LastName = "Lilas",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789905",
                },
                new
                {
                    FirstName = "Hiroyuki",
                    LastName = "Sawano",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789916",
                },
                new
                {
                    FirstName = "Don",
                    LastName = "Wori",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789906",
                },
                new
                {
                    FirstName = "Ram",
                    LastName = "Dass",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789906",
                },
                new
                {
                    FirstName = "Taylor",
                    LastName = "Swift",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789907",
                },
                new
                {
                    FirstName = "Sam",
                    LastName = "Paul",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789908",
                },
                new
                {
                    FirstName = "Logan",
                    LastName = "Paul",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789909",
                },
                new
                {
                    FirstName = "Jaden",
                    LastName = "Smith",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789910",
                },
                new
                {
                    FirstName = "Justin",
                    LastName = "Bieber",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789911",
                },
                new
                {
                    FirstName = "Hailee",
                    LastName = "Steinfeld",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+6497789912",
                },
                new
                {
                    FirstName = "Bruno",
                    LastName = "Mars",
                    Email = new Email("<EMAIL>"),
                    PhoneNumber = "+**********",
                },
            };

            contactsDb
                .Should()
                .BeEquivalentTo(
                    expected,
                    opt => opt.ExcludingMissingMembers().WithoutStrictOrdering()
                );
        }

        [Fact]
        public async Task ImportContactsFromCsvEventHandler_should_import_contacts_when_status_is_running()
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData(
                ImportSummaryStatus.Running
            );

            var fullFilePath = Path.Combine(resourceFilePath, clientImportStatusesCsvFileName);
            using Stream fileStream = System.IO.File.OpenRead(fullFilePath);

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ReturnsAsync(fileStream);

            var message = CreateMessage(provider, person, importSummary, dataSchema);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();

            var contactsDb = await DataContext
                .Contacts.AsNoTracking()
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();

            var importSummaryDb = await DataContext
                .ContactImportSummaries.AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == importSummary.Id);

            contactsDb.Should().HaveCount(19);
            importSummaryDb.Status.Should().Be(ImportSummaryStatus.Successful);
        }

        [Fact]
        public async Task ImportContactsFromCsvEventHandler_should_not_import_when_status_is_successful()
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData(
                ImportSummaryStatus.Successful
            );

            var message = CreateMessage(provider, person, importSummary, dataSchema);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();

            var contactsDb = await DataContext
                .Contacts.AsNoTracking()
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();

            var importSummaryDb = await DataContext
                .ContactImportSummaries.AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == importSummary.Id);

            contactsDb.Should().HaveCount(0);
            importSummaryDb.Status.Should().Be(ImportSummaryStatus.Successful);
        }

        [Fact]
        public async Task ImportContactsFromCsvEventHandler_should_set_status_to_failed()
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData();

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ThrowsAsync(new Exception("Test exception"));

            var message = CreateMessage(provider, person, importSummary, dataSchema);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();

            var contactsDb = await DataContext
                .Contacts.AsNoTracking()
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();

            var importSummaryDb = await DataContext
                .ContactImportSummaries.AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == importSummary.Id);

            contactsDb.Should().HaveCount(0);
            importSummaryDb.Status.Should().Be(ImportSummaryStatus.Failed);
        }

        [Theory]
        [InlineData(10)]
        [InlineData(100)]
        [InlineData(10000, Skip = "Test is too slow for large datasets, should only run in local to test performance")]
        [InlineData(100000, Skip = "Test is too slow for large datasets, should only run in local to test performance")]
        public async Task ImportContactsFromCsvEventHandler_import_batch_test(int numberOfRecords)
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData();

            var contacts = new ContactFaker()
                .Generate(numberOfRecords)
                .Select(x => new { x.FirstName, x.LastName })
                .ToArray();

            using var mem = new MemoryStream();
            using var writer = new StreamWriter(mem);
            using var csvWriter = new CsvWriter(writer, CultureInfo.InvariantCulture);
            await csvWriter.WriteRecordsAsync(contacts);
            await writer.FlushAsync();
            var result = Encoding.UTF8.GetString(mem.ToArray());

            mem.Position = 0;

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ReturnsAsync(mem);

            var message = CreateMessage(provider, person, importSummary, dataSchema);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();

            var contactsDb = await DataContext
                .Contacts.AsNoTracking()
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();

            var importSummaryDb = await DataContext
                .ContactImportSummaries.AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == importSummary.Id);

            contactsDb.Should().HaveCount(numberOfRecords);
            importSummaryDb.Status.Should().Be(ImportSummaryStatus.Successful);
        }

        [Fact]
        public async Task ImportContactsFromCsvEventHandler_should_ignore_familiar_fields_that_are_neither_core_nor_custom()
        {
            Dictionary<string, Property> customFields = new()
            {
                { "My Custom Field 1", new StringProperty { DisplayName = "Custom Field 1" } }
            };
            
            var (provider, person, importSummary, dataSchema) = await SetupImportData();
            dataSchema.Properties = customFields;
            
            await DataContext.AddRangeAsync(dataSchema.ToDataModel());
            await DataContext.SaveChangesAsync();

            var fullFilePath = Path.Combine(resourceFilePath, clientImportLanguagesCsvFileName);
            using Stream fileStream = System.IO.File.OpenRead(fullFilePath);

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ReturnsAsync(fileStream);

            var languageOption = new ImportContactsOption(
                nameof(Contact.Language),
                "Language",
                null,
                ImportContactsFieldOption.WholeField,
                null,
                false
            );
            var languagesOption = new ImportContactsOption(
                nameof(Contact.Languages),
                "Languages",
                null,
                ImportContactsFieldOption.WholeField,
                null,
                false
            );
            var businessNameOption = new ImportContactsOption(
                nameof(Contact.BusinessName),
                "Business Name",
                null,
                ImportContactsFieldOption.WholeField,
                null,
                false
            );
            var customField1 = new ImportContactsOption(
                "My Custom Field 1",
                "My Custom Field 1",
                null,
                ImportContactsFieldOption.WholeField,
                null,
                false
            );
            var customField2 = new ImportContactsOption(
                "My Custom Field 2",
                "My Custom Field 2",
                null,
                ImportContactsFieldOption.WholeField,
                null,
                false
            );
            ImportContactsOption[] options = { languageOption, languagesOption, businessNameOption, customField1, customField2 };

            var message = CreateMessage(provider, person, importSummary, dataSchema, options: options);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();

            var contactsDb = await DataContext
                .Contacts.AsNoTracking()
                .Include(x => x.Languages)
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();

            var contact1 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            var contact2 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            var contact3 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            var contact4 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));

            contact1.Should().NotBeNull();
            contact1.BusinessName.Should().BeNullOrEmpty();
            contact1.Languages.Should().BeNullOrEmpty();
            contact1.Fields.Should().ContainKey("My Custom Field 1");
            contact1.Fields["My Custom Field 1"].Should().Be("123");
            contact1.Fields.Should().NotContainKey("My Custom Field 2");

            contact2.Should().NotBeNull();
            contact2.BusinessName.Should().BeNullOrEmpty();
            contact2.Languages.Should().BeNullOrEmpty();
            contact2.Fields.Should().ContainKey("My Custom Field 1");
            contact2.Fields["My Custom Field 1"].Should().Be("456");
            contact2.Fields.Should().NotContainKey("My Custom Field 2");
            
            contact3.Should().NotBeNull();
            contact3.BusinessName.Should().BeNullOrEmpty();
            contact3.Languages.Should().BeNullOrEmpty();
            contact3.Fields.Should().ContainKey("My Custom Field 1");
            contact3.Fields["My Custom Field 1"].Should().Be("789");
            contact3.Fields.Should().NotContainKey("My Custom Field 2");
            
            contact4.Should().NotBeNull();
            contact4.BusinessName.Should().BeNullOrEmpty();
            contact4.Languages.Should().BeNullOrEmpty();
            contact4.Fields.Should().ContainKey("My Custom Field 1");
            contact4.Fields["My Custom Field 1"].Should().Be("aaa");
            contact4.Fields.Should().NotContainKey("My Custom Field 2");
        }
        
        [Fact]
        public async Task ImportContactsFromCsvEventHandler_should_duplicate_tags()
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData();
            
            var tag1A = new TagFaker().WithProviderId(provider.Id).RuleFor(x => x.Title, "tag1").RuleFor(x => x.IsActive, true).Generate().ToDataModel();
            var tag1B = new TagFaker().WithProviderId(provider.Id).RuleFor(x => x.Title, "tag1").RuleFor(x => x.IsActive, true).Generate().ToDataModel();
            
            var tag2A = new TagFaker().WithProviderId(provider.Id).RuleFor(x => x.Title, "tag2").RuleFor(x => x.IsActive, false).Generate().ToDataModel();
            var tag2B = new TagFaker().WithProviderId(provider.Id).RuleFor(x => x.Title, "tag2").RuleFor(x => x.IsActive, true).Generate().ToDataModel();
            
            var tag3A = new TagFaker().WithProviderId(provider.Id).RuleFor(x => x.Title, "tag3").RuleFor(x => x.IsActive, false).Generate().ToDataModel();
            
            var tag3B = new TagFaker().WithProviderId(provider.Id).RuleFor(x => x.Title, "tag3").RuleFor(x => x.IsActive, false).Generate().ToDataModel();
            var tag99 = new TagFaker().WithProviderId(provider.Id).RuleFor(x => x.Title, "tag99").RuleFor(x => x.IsActive, false).Generate().ToDataModel();
            
            await DataContext.AddRangeAsync(tag1A, tag1B, tag2A, tag2B, tag3A, tag3B, tag99);
            
            await DataContext.AddRangeAsync(dataSchema.ToDataModel());
            await DataContext.SaveChangesAsync();

            var fullFilePath = Path.Combine(resourceFilePath, clientImportWithTagsCsvFileName);
            using Stream fileStream = System.IO.File.OpenRead(fullFilePath);

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ReturnsAsync(fileStream);

            var tagsOption = new ImportContactsOption(
                nameof(Contact.Tags),
                "Tags",
                ["Tags"],
                ImportContactsFieldOption.WholeField,
                null,
                true
            );
             
            ImportContactsOption[] options = { tagsOption };

            var message = CreateMessage(provider, person, importSummary, dataSchema, options: options);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();
            
            var providerTagsDb = await DataContext
                .Tags
                .AsNoTracking()
                .Where(x => x.ProviderId == provider.Id)
                .ToListAsync();
            
            var tag1Db = providerTagsDb.Where(x => x.Title == "tag1").ToList();
            tag1Db.Should().HaveCount(2);
            tag1Db.Should().OnlyContain(x => x.IsActive);
            
            var tag2Db = providerTagsDb.Where(x => x.Title == "tag2").ToList();
            tag2Db.Should().HaveCount(2);
            tag2Db.Should().OnlyContain(x => x.IsActive);
            
            var tag3Db = providerTagsDb.Where(x => x.Title == "tag3").ToList();
            tag3Db.Should().HaveCount(2);
            tag3Db.Should().OnlyContain(x => x.IsActive);
            
            var tag4Db = providerTagsDb.Where(x => x.Title == "tag4").ToList();
            tag4Db.Should().HaveCount(1);
            tag4Db.Should().OnlyContain(x => x.IsActive);
            
            var tag99Db = providerTagsDb.Where(x => x.Title == "tag99").ToList();
            tag99Db.Should().HaveCount(1);
            tag99Db.Should().OnlyContain(x => !x.IsActive);
            
            var contactsDb = await DataContext
                .Contacts
                .AsNoTracking()
                .Include(x => x.Tags)
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();
            
            var contact1 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            var contact2 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            var contact3 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            var contact4 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            var contact5 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            
            contact1.Should().NotBeNull();
            contact1.Tags.Should().HaveCount(1);
            contact1.Tags.Should().ContainSingle(x => x.Tag.Title == "tag1"); 
            
            contact2.Should().NotBeNull();
            contact2.Tags.Should().HaveCount(1);
            contact2.Tags.Should().ContainSingle(x => x.Tag.Title == "tag2");
            
            contact3.Should().NotBeNull();
            contact3.Tags.Should().HaveCount(1);
            contact3.Tags.Should().ContainSingle(x => x.Tag.Title == "tag3");
            
            contact4.Should().NotBeNull();
            contact4.Tags.Should().HaveCount(1);
            contact4.Tags.Should().ContainSingle(x => x.Tag.Title == "tag4");
            
            contact5.Should().NotBeNull();
            contact5.Tags.Should().BeNullOrEmpty();
        }

        [Fact]
        public async Task ImportContactsFromCsvEventHandler_should_handle_incorrect_mapping_for_tags()
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData();

            await DataContext.AddRangeAsync(dataSchema.ToDataModel());
            await DataContext.SaveChangesAsync();

            var fullFilePath = Path.Combine(resourceFilePath, clientImportWithTagsCsvFileName);
            using Stream fileStream = System.IO.File.OpenRead(fullFilePath);

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ReturnsAsync(fileStream);

            var incorrectOption = new ImportContactsOption(
                nameof(Contact.Tags),
                "Tags",
                null,
                ImportContactsFieldOption.WholeField,
                null,
                false
            );

            ImportContactsOption[] options = { incorrectOption };

            var message = CreateMessage(provider, person, importSummary, dataSchema, options: options);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();

            var providerTagsDb = await DataContext
                .Tags
                .AsNoTracking()
                .Where(x => x.ProviderId == provider.Id)
                .ToListAsync();

            providerTagsDb.Should().BeEmpty();

            var contactsDb = await DataContext
                .Contacts
                .AsNoTracking()
                .Include(x => x.Tags)
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();

            Email[] expectedEmails =
            [
                new Email("<EMAIL>"),
                new Email("<EMAIL>"),
                new Email("<EMAIL>"),
                new Email("<EMAIL>"),
                new Email("<EMAIL>"),
            ];

            expectedEmails.Should().AllSatisfy(x => contactsDb.Should().ContainSingle(y => y.Email == x));

            contactsDb.Should().AllSatisfy(x => x.Tags.Should().BeEmpty());
        }

        [Fact]
        public async Task ImportContactsFromCsvEventHandler_should_handle_unsupported_values()
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData();

            await DataContext.AddRangeAsync(dataSchema.ToDataModel());
            await DataContext.SaveChangesAsync();

            var fullFilePath = Path.Combine(resourceFilePath, clientImportWithIssuesCsvFileName);
            using Stream fileStream = System.IO.File.OpenRead(fullFilePath);

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ReturnsAsync(fileStream);

            var sexOption = new ImportContactsOption(
                nameof(Contact.Sex),
                "Sex",
                null,
                ImportContactsFieldOption.WholeField,
                null,
                false
            );
            var preferredLanguageOption = new ImportContactsOption(
                nameof(Contact.PreferredLanguage),
                "Language",
                null,
                ImportContactsFieldOption.WholeField,
                null,
                false
            );
            
            var assignedStaffOption = new ImportContactsOption(
                nameof(Contact.AssignedStaff),
                "Assiged Staff",
                null,
                ImportContactsFieldOption.WholeField,
                null,
                false
            );

            ImportContactsOption[] options = [sexOption, preferredLanguageOption, assignedStaffOption];

            var message = CreateMessage(provider, person, importSummary, dataSchema, options: options);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();

            var contactsDb = await DataContext
                .Contacts
                .AsNoTracking()
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();

            var contact1 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            var contact2 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            var contact3 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            var contact4 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));
            var contact5 = contactsDb.FirstOrDefault(x => x.Email == new Email("<EMAIL>"));

            contact1.Should().NotBeNull();
            contact1.Sex.Should().BeNull();
            contact1.PreferredLanguage.Should().BeNull();
            contact1.AssignedStaff.Should().BeNullOrEmpty();

            contact2.Should().NotBeNull();
            contact2.Sex.Should().BeNull();
            contact2.PreferredLanguage.Should().BeNull();
            contact2.AssignedStaff.Should().BeNullOrEmpty();

            contact3.Should().NotBeNull();
            contact3.Sex.Should().BeEquivalentTo(new { Id = "Unknown" }, opt => opt.ExcludingMissingMembers());
            contact3.PreferredLanguage.Should().BeEquivalentTo(new { Id = "en" }, opt => opt.ExcludingMissingMembers());
            contact3.AssignedStaff.Should().BeNullOrEmpty();

            contact4.Should().NotBeNull();
            contact4.Sex.Should().BeEquivalentTo(new { Id = "Unknown" }, opt => opt.ExcludingMissingMembers());
            contact4.PreferredLanguage.Should().BeEquivalentTo(new { Id = "zh" }, opt => opt.ExcludingMissingMembers());
            contact4.AssignedStaff.Should().BeNullOrEmpty();

            contact5.Should().NotBeNull();
            contact5.Sex.Should().BeEquivalentTo(new { Id = "Male" }, opt => opt.ExcludingMissingMembers());
            contact5.PreferredLanguage.Should().BeEquivalentTo(new { Id = "ja" }, opt => opt.ExcludingMissingMembers());
            contact5.AssignedStaff.Should().BeNullOrEmpty();
        }

        [Fact]
        public async Task ImportContactsFromCsvEventHandler_should_auto_detect_duplicates()
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData();

            // expected duplicate
            var client1 = new ContactFaker(provider.Id)
                .RuleFor(x => x.Email, new Email("<EMAIL>"))
                .Generate().ToDataModel(); 
            
            var client2 = new ContactFaker(provider.Id)
                .RuleFor(x => x.Email, f => f.CarePatron().Email())
                .Generate().ToDataModel();

            await DataContext.AddRangeAsync(dataSchema.ToDataModel(), client1, client2);
            await DataContext.SaveChangesAsync();

            var fullFilePath = Path.Combine(resourceFilePath, clientImportWithDuplicatesCsvFileName);
            using Stream fileStream = System.IO.File.OpenRead(fullFilePath);

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ReturnsAsync(fileStream);

            var message = CreateMessage(provider, person, importSummary, dataSchema);
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();

            var contactsDb = await DataContext
                .Contacts
                .AsNoTracking()
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();

            var contact1 = contactsDb.FirstOrDefault(x => x.FullName == "Client A"); // expected duplicate
            var contact2 = contactsDb.FirstOrDefault(x => x.FullName == "Client B"); // expected duplicate
            var contact3 = contactsDb.FirstOrDefault(x => x.FullName == "Client C"); // expected duplicate
            var contact4 = contactsDb.FirstOrDefault(x => x.FullName == "Client D");
            var contact5 = contactsDb.FirstOrDefault(x => x.FullName == "Client E");

            contact1.Should().NotBeNull();
            contact2.Should().NotBeNull();
            contact3.Should().NotBeNull();
            contact4.Should().NotBeNull();
            contact5.Should().NotBeNull();

            var duplicateContacts = await DataContext
                .DuplicateContacts
                .AsNoTracking()
                .Where(x => x.ProviderId == provider.Id)
                .ToListAsync();

            duplicateContacts.Should().BeEquivalentTo([
                new { ContactId = contact1.Id },
                new { ContactId = contact2.Id },
                new { ContactId = contact3.Id },
                new { ContactId = client1.Id },
            ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
        }

        [Fact]
        public async Task ImportContactsFromAiCsvEventHandler_should_import_contacts()
        {
            var (provider, person, importSummary, dataSchema) = await SetupImportData();
 
            string[] ageGroupValues =
            [
                "0-10",
                "10-20",
                "20-30",
                "30-40",
                "40-50",
                "50-60",
                "60+"
            ];
            dataSchema.Properties.Add("Age Group", new OptionSetV2Property()
            {
                Type = PropertyType.OptionSet,
                DisplayName = "Age Group",
                Options = ageGroupValues.Select((x, idx) => new OptionSetValue
                    {
                        Id = x,
                        DisplayName = x,
                        OrderIndex = idx + 1
                    })
                    .ToDictionary(x => x.Id, x => x)
            });
            dataSchema.Properties.Add("# of Children", new NumberProperty()
            {
                Type = PropertyType.Number,
                DisplayName = "# of Children",
            });
            dataSchema.Properties.Add("Has Medical Condition", new BooleanProperty()
            {
                Type = PropertyType.Boolean,
                DisplayName = "Has Medical Condition",
                TrueDisplayName = "Yes",
                FalseDisplayName = "No",
            });
            dataSchema.Properties.Add("Last checkup date", new DateProperty()
            {
                Type = PropertyType.Date,
                DisplayName = "Last checkup date",
            });
            dataSchema.Properties.Add("Emergency Contact Name", new StringProperty()
            {
                Type = PropertyType.String,
                DisplayName = "Emergency Contact Name",
            });
            dataSchema.Properties.Add("Emergency Contact Phone", new PhoneNumberProperty()
            {
                Type = PropertyType.Phone,
                DisplayName = "Emergency Contact Phone",
            });
            dataSchema.Properties.Add("Emergency Contact Email", new EmailProperty()
            {
                Type = PropertyType.Email,
                DisplayName = "Emergency Contact Email",
            });
            dataSchema.Properties.Add("Emergency Contact Address", new AddressProperty()
            {
                Type = PropertyType.Address,
                DisplayName = "Emergency Contact Address",
            });

            await DataContext.AddRangeAsync(dataSchema.ToDataModel());
            await DataContext.SaveChangesAsync();

            var fullFilePath = Path.Combine(resourceFilePath, clientImportForAiMappingCsvFileName);
            using Stream fileStream = System.IO.File.OpenRead(fullFilePath);

            var fileStorageRepositoryMock =
                Fixture.TestServer.Services.GetService<FileStorageRepositoryMock>();
            fileStorageRepositoryMock
                .Setup(x => x.GetObjectStream(It.IsAny<string>(), It.IsAny<FileLocationType>(), CancellationToken.None))
                .ReturnsAsync(fileStream);

            List<ImportContactsOption> importContactOptions = new();
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = nameof(Contact.FirstName),
                SpreadsheetFieldName = "Full Name",
                FieldOptions = ImportContactsFieldOption.FirstPart,
                Delimiter = " "
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = nameof(Contact.LastName),
                SpreadsheetFieldName = "Full Name",
                FieldOptions = ImportContactsFieldOption.LastPart,
                Delimiter = " "
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = nameof(Contact.Email),
                SpreadsheetMultipleFieldNames = ["E-mail", "Alternative Email"],
                FieldOptions = ImportContactsFieldOption.WholeField,
                IsMultiple = true
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = nameof(Contact.PhoneNumber),
                SpreadsheetMultipleFieldNames = ["Phone", "Alternative Phone"],
                FieldOptions = ImportContactsFieldOption.WholeField,
                IsMultiple = true
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = nameof(Contact.Address),
                SpreadsheetMultipleFieldNames = ["Address", "Alternative Address"],
                FieldOptions = ImportContactsFieldOption.WholeField,
                IsMultiple = true
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = "Age Group",
                SpreadsheetFieldName = "Age Group",
                FieldOptions = ImportContactsFieldOption.WholeField,
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = "# of Children",
                SpreadsheetFieldName = "# of Children",
                FieldOptions = ImportContactsFieldOption.WholeField,
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = "Has Medical Condition",
                SpreadsheetFieldName = "Has Medical Condition",
                FieldOptions = ImportContactsFieldOption.WholeField,
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = "Last checkup date",
                SpreadsheetFieldName = "Last checkup date",
                FieldOptions = ImportContactsFieldOption.WholeField,
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = "Emergency Contact Name",
                SpreadsheetFieldName = "Emergency Contact Name",
                FieldOptions = ImportContactsFieldOption.WholeField,
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = "Emergency Contact Phone",
                SpreadsheetFieldName = "Emergency Contact Phone",
                FieldOptions = ImportContactsFieldOption.WholeField,
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = "Emergency Contact Email",
                SpreadsheetFieldName = "Emergency Contact Email",
                FieldOptions = ImportContactsFieldOption.WholeField,
            });
            importContactOptions.Add(new ImportContactsOption
            {
                CarepatronFieldName = "Emergency Contact Address",
                SpreadsheetFieldName = "Emergency Contact Address",
                FieldOptions = ImportContactsFieldOption.WholeField,
            });

            var message = CreateMessage(provider,
                person,
                importSummary,
                null,
                EventType.ImportContactsFromAiCsv, false,
                options: importContactOptions.ToArray());
            
            Fixture.QueueMessage(QueueType.Task, message);
            await Fixture.Run();
            var importSummaryDb = await DataContext
                .ContactImportSummaries.AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == importSummary.Id);

          
            importSummaryDb.Status.Should().Be(ImportSummaryStatus.Successful);
            
            var contactsDb = await DataContext
                .Contacts.AsNoTracking()
                .Include(x => x.Emails)
                .Include(x => x.PhoneNumbers)
                .Include(x => x.Addresses)
                .Where(x => x.ProviderId == provider.Id && x.IsClient)
                .ToListAsync();
            
            contactsDb.Should().HaveCount(6);

            var contact1Email = new Email("<EMAIL>");
            var contact1OtherEmail = new Email("<EMAIL>");
            var contact1PhoneNumber = "******-1234";
            var contact1OtherPhoneNumber = "******-5678";
            var contact1Address = "123 Main St";
            var contact1OtherAddress = "456 Elm St";

            var contact1 = contactsDb.FirstOrDefault(x => x.Email == contact1Email || x.Emails.Select(e => e.Email).Any(contact1Email.Equals));
            contact1.Should().NotBeNull();
            contact1.FirstName.Should().Be("Tim");
            contact1.LastName.Should().Be("Adams");
            contact1.Email.Should().Match<Email>(x => x == contact1Email || x == contact1OtherEmail);
            contact1.Emails.Should().HaveCount(2);
            contact1.Emails.Should().ContainSingle(x => x.Email == contact1Email);
            contact1.Emails.Should().ContainSingle(x => x.Email == contact1OtherEmail);
            contact1.Emails.Should().ContainSingle(x => x.IsPrimary);
            contact1.PhoneNumber.Should().Match<string>(x => x == contact1PhoneNumber || x == contact1OtherPhoneNumber);
            contact1.PhoneNumbers.Should().HaveCount(2);
            contact1.PhoneNumbers.Should().ContainSingle(x => x.PhoneNumber == contact1PhoneNumber);
            contact1.PhoneNumbers.Should().ContainSingle(x => x.PhoneNumber == contact1OtherPhoneNumber);
            contact1.PhoneNumbers.Should().ContainSingle(x => x.IsPrimary);
            contact1.Addresses.Should().HaveCount(2);
            contact1.Addresses.Should().ContainSingle(x => x.Address == contact1Address);
            contact1.Addresses.Should().ContainSingle(x => x.Address == contact1OtherAddress);
            contact1.Addresses.Should().ContainSingle(x => x.IsPrimary);
            
            contact1.Fields.Should().ContainKey("Age Group");
            var ageGroupJObject = contact1.Fields["Age Group"] as JObject;
            ageGroupJObject.Should().NotBeNull();
            var ageGroup = ageGroupJObject.ToObject<OptionSetValue>();
            ageGroup.Should().NotBeNull();
            ageGroup.Id.Should().Be("30-40");
            
            contact1.Fields.Should().ContainKey("# of Children");
            var numberOfChildren = contact1.Fields["# of Children"];
            numberOfChildren.Should().Be(2);
            
            contact1.Fields.Should().ContainKey("Last checkup date");
            var lastCheckupDate = contact1.Fields["Last checkup date"];
            var lastCheckupDateString = "2023-01-15";
            var lastCheckupDateValue = DateTime.ParseExact(lastCheckupDateString, "yyyy-MM-dd", CultureInfo.InvariantCulture);
            lastCheckupDate.Should().Be(lastCheckupDateValue);
            
            contact1.Fields.Should().ContainKey("Has Medical Condition");
            var hasMedicalCondition = contact1.Fields["Has Medical Condition"];
            var hasMedicalConditionValue = (bool)hasMedicalCondition;
            hasMedicalConditionValue.Should().BeTrue();
            
            contact1.Fields.Should().ContainKey("Emergency Contact Name");
            var emergencyContactName = contact1.Fields["Emergency Contact Name"];
            emergencyContactName.Should().Be("John Doe");
            
            contact1.Fields.Should().ContainKey("Emergency Contact Email");
            var emergencyContactEmail = contact1.Fields["Emergency Contact Email"];
            emergencyContactEmail.Should().Be("<EMAIL>");
            
            contact1.Fields.Should().ContainKey("Emergency Contact Phone");
            var emergencyContactPhone = contact1.Fields["Emergency Contact Phone"];
            emergencyContactPhone.Should().Be("******-8765");
            
            contact1.Fields.Should().ContainKey("Emergency Contact Address");
            var emergencyContactAddress = contact1.Fields["Emergency Contact Address"] as JObject;
            emergencyContactAddress.Should().NotBeNull();
            var emergencyContactAddressValue = emergencyContactAddress.ToObject<Address>();
            emergencyContactAddressValue.Should().NotBeNull();
            emergencyContactAddressValue.StreetAddress.Should().Be("789 Oak St");
        }

        private async Task<(
            ProviderDataModel providerDataModel,
            PersonDataModel person,
            ContactImportSummaryDataModel contactImportSummary,
            DataSchema dataSchema
        )> SetupImportData(ImportSummaryStatus status = ImportSummaryStatus.Pending, bool isContact = false)
        {
            var provider = new ProviderFaker().Generate().ToDataModel();
            var person = new PersonFaker().Generate().ToDataModel();
            var importSummary = new ContactImportSummaryFaker(provider.Id, person.Id)
                .RuleFor(_ => _.Status, status)
                .RuleFor(c => c.FileName, f => f.Random.Words(1) + ".csv")
                .RuleFor(c => c.IsContact, isContact)
                .Generate()
                .ToDataModel();
            var dataSchemaObject = new DataSchemaFaker(
                SchemaTypes.ContactSchema,
                provider.Id
            ).Generate();

            await DataContext.AddRangeAsync(provider, person, importSummary);
            await DataContext.SaveChangesAsync();

            return (provider, person, importSummary, dataSchemaObject);
        }
        
        private EventMessage CreateMessage(
            ProviderDataModel provider,
            PersonDataModel person,
            ContactImportSummaryDataModel importSummary,
            DataSchema dataSchema,
            params ImportContactsOption[] options
        )
        {
            return CreateMessage(
                provider,
                person,
                importSummary,
                dataSchema,
                EventType.ImportContactsFromCsv,
                true,
                options
            );
        }

        private EventMessage CreateMessage(
            ProviderDataModel provider,
            PersonDataModel person,
            ContactImportSummaryDataModel importSummary,
            DataSchema dataSchema,
            EventType eventType,
            bool includeDefaultMappings,
            params ImportContactsOption[] options
        )
        {
            var messageData = new
            {
                Data = new
                {
                    ProviderId = provider.Id,
                    PersonId = person.Id,
                    ContactImportSummaryId = importSummary.Id,
                    MappedColumns = includeDefaultMappings ? GetMappedColumns(options): options,
                    DataSchema = dataSchema
                },
            };
            return new EventMessage(
                eventType,
                messageData,
                Guid.NewGuid().ToString(),
                person.Id
            );
        }

        private ImportContactsOption[] GetMappedColumns(params ImportContactsOption[] options)
        {
            ImportContactsOption[] columns = 
            [
                new ImportContactsOption(
                    "firstName",
                    "Name",
                    null,
                    ImportContactsFieldOption.FirstPart,
                    " ",
                    false
                ),
                new ImportContactsOption(
                    "lastName",
                    "Name",
                    null,
                    ImportContactsFieldOption.LastPart,
                    " ",
                    false
                ),
                new ImportContactsOption(
                    "phoneNumber",
                    "Phone",
                    null,
                    ImportContactsFieldOption.WholeField,
                    null,
                    false,
                    null,
                    "NZ"
                ),
                new ImportContactsOption(
                    "email",
                    "Email",
                    null,
                    ImportContactsFieldOption.WholeField,
                    null,
                    false
                ),
                new ImportContactsOption(
                    "address",
                    "Address",
                    null,
                    ImportContactsFieldOption.WholeField,
                    null,
                    false
                ),
            ];
            
            return columns.Concat(options).ToArray();
        }
    }
}
