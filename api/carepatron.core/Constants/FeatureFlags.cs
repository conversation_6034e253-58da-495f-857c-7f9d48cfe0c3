namespace carepatron.core.Constants;

public static class FeatureFlags
{
    public const string Internal = "internal";
    public const string TrashAndRestore = "trash-and-restore";
    public const string AgentProviderGoogleSpeechToText = "agent-provider-google-speech-to-text";
    public const string DeferredTranscriptionCompletion = "deferred-transcription-completion";
    public const string AutomatedWorkflows = "automated-workflows";
    public const string WorkflowTemplate = "workflow-template";
    public const string AiSuggestedActions = "ai-suggested-actions";
    public const string UseGeminiFlash25PreviewForTranscription = "use-gemini-flash-25-preview-for-transcription";
}