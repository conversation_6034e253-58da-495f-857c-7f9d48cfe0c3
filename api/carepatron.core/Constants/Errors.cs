﻿namespace carepatron.core.Constants;

public static class Errors
{
    public const string NotFoundErrorCode = "NotFound";
    public const string NotFoundErrorDetails = "Entity not found";

    public const string MustHaveAccessToClientCode = "MustHaveAccessToClient";
    public const string MustHaveAccessToClientDetails = "This action requires access to the client.";

    public const string CannotRegisterExistingUserCode = "CannotRegisterExistingUser";
    public const string CannotRegisterExistingUserDetails = "You cannot register an existing user.";

    public const string EmailFirstLastNameIsRequiredCode = "EmailFirstLastNameIsRequired";
    public const string EmailFirstLastNameIsRequiredDetails = "Email, first name, and last name is required.";

    public const string EmailIsRequiredCode = "EmailIsRequired";
    public const string EmailIsRequiredDetails = "Email is required.";

    public const string CannotInviteExistingUserDetails = "Cannot invite a user who already exists in the system.";

    public const string CannotInviteExistingProviderStaffCode = "CannotInviteExistingProviderStaffCode";
    public const string CannotInviteExistingProviderStaffDetails = "Cannot invite a user who already exists in the system and part of provider.";

    public const string EmailUpdateNotSupportedErrorCode = "CannotUpdateUsernameToExistingUserErrorCode";
    public const string EmailUpdateNotSupportedErrorDetail = "Cannot update to a username of another user within the system.";

    public const string DowngradingPricingPlanTooManyStaffErrorCode = "DowngradingPricingPlanTooManyStaffErrorCode";
    public const string DowngradingPricingPlanTooManyStaffErrorDetails = "Cannot downgrade to this pricing plan because there are too many staff members within the provider";

    public const string ProviderHasExistingBillingAccountErrorCode = "ProviderHasExistingBillingAccountErrorCode";
    public const string ProviderHasExistingBillingAccountErrorDetails = "The provider already has an existing billing account. Please update the account.";

    public const string StaffCountExceedsPricingPlanErrorCode = "StaffCountExceedsPricingPlanErrorCode";
    public const string StaffCountExceedsPricingPlanErrorDetails = "The staff count exceeds the maximum number of staff for this pricing plan.";

    public const string MustHaveAccessToNoteCode = "MustHaveAccessToNote";
    public const string MustHaveAccessToNoteDetails = "This action requires access to the note.";

    public const string CannotCreateNoteWithoutAccessibleRoleCode = "CannotCreateNoteWithoutAccessibleRoleCode";
    public const string CannotCreateNoteWithoutAccessibleRoleDetails = "Cannot create a note without roles that can access it.";

    public const string CannotCreateNoteWithoutContentCode = "CannotCreateNoteWithoutContentCode";
    public const string CannotCreateNoteWithoutContentDetails = "Cannot create a note without content.";

    public const string CannotUpdateNoteWithoutAccessibleRoleCode = "CannotUpdateNoteWithoutAccessibleRoleCode";
    public const string CannotUpdateNoteWithoutAccessibleRoleDetails = "Cannot update a note without roles that can access it.";

    public const string CannotUpdateNoteWithoutContentCode = "CannotUpdateNoteWithoutContentCode";
    public const string CannotUpdateNoteWithoutContentDetails = "Cannot update a note without content.";

    public const string CannotUseRoleWithNoteCode = "CannotUseRoleWithNoteCode";
    public const string CannotUseRoleWithNoteDetails = "Cannot use selected role(s) ase value for RolesAccessibleBy.";

    public const string CannotDeleteNoteCode = "CannotDeleteNote";
    public const string CannotDeleteNoteDetails = "Cannot delete a note you don't have access to.";

    public const string CallNotFoundCode = "CallNotFound";
    public const string CallNotFoundDetails = "Call does not exist";

    public const string CallExpiredCode = "CallExpired";
    public const string CallExpiredDetails = "Call has expired";

    public const string CannotJoinInactiveCallCode = "CannotJoinInactiveCall";
    public const string CannotJoinInactiveCallDetails = "You cannot join an inactive call";

    public const string CannotJoinCallUninvitedCode = "CannotJoinCallUninvited";
    public const string CannotJoinCallUninvitedDetails = "You cannot join this call because you have not been invited.";

    public const string CannotEndCallUninvitedCode = "CannotEndCallUninvited";
    public const string CannotEndCallUninvitedDetails = "You cannot end this call because you have not been invited.";

    public const string CannotAccessCallUninvitedCode = "CannotAccessCallUninvitedCode";
    public const string CannotAccessCallUninvitedDetails = "You cannot access this call because you have not been invited.";

    public const string CannotEndCallIfNotTheCreatorCode = "CannotEndCallIfNotTheCreator";
    public const string CannotEndCallIfNotTheCreatorDetails = "You cannot end this call because you did not create it.";

    public const string CannotResetCallIfItHasActiveAttendeeCode = "CannotResetCallIfNotItHasActiveAttendee";
    public const string CannotResetCallIfItHasActiveAttendeeDetails = "You cannot reset this call it already has an active attendee.";

    public const string CreateVideoCallMediaPipelineErrorCode = "CreateVideoCallMediaPipelineError";
    public const string CreateVideoCallMediaPipelineErrorDetails = "An error occured creating a media pipeline for the call.";

    public const string MissingProviderIdErrorCode = "MissingProviderId";
    public const string MissingProviderIdErrorDetails = "Missing provider identifier";

    public const string InvalidFieldIdErrorCode = "InvalidFieldId";
    public const string InvalidFieldIdErrorDetails = "The field id is not valid";

    public const string InvalidAiPromptIdErrorCode = "InvalidAiPromptId";
    public const string InvalidAiPromptIdErrorDetails = "The ai prompt id is not valid";

    public const string InvalidContextReference = "InvalidContextReference";
    public const string InvalidContextReferenceErrorDetails = "The context reference is not valid or you do not have access to the context reference.";

    public const string ServiceUnavailableErrorCode = "ServiceUnavailable";
    public const string ServiceUnavailableErrorDetails = "Service unavailable";

    public const string InvalidFormResponseIdErrorCode = "InvalidFormResponseId";
    public const string InvalidFormResponseIdErrorDetails = "The Form Response id is not valid";

    public const string MissingPersonIdErrorCode = "MissingPersonId";
    public const string MissingPersonIdErrorDetails = "Missing person identifier";

    public const string CannotAccessClientsForAnotherPersonErrorCode = "CannotAccessClientsForAnotherPerson";
    public const string CannotAccessClientsForAnotherPersonErrorDetails = "Cannot access the clients for another person.";

    public const string MustHaveAccessToFileCode = "MustHaveAccessToFile";
    public const string MustHaveAccessToFileDetails = "Cannot access file";

    public const string CannotDeleteFileAttachedToNoteCode = "CannotDeleteFileAttachedToNoteCode";
    public const string CannotDeleteFileAttachedToNoteDetails = "Cannot delete file attached to note";

    public const string ContactMustBeATrustedProviderErrorCode = "ContactMustBeATrustedProvider";
    public const string ContactMustBeATrustedProviderErrorDetails = "Contact's email is a provider user, they must be added as a trusted contact";

    public const string UnauthorisedAccessToProviderErrorCode = "UnauthorisedAccessToProvider";
    public const string UnauthorisedAccessToProviderErrorDetails = "You do not have the right access in this provider for this action.";

    public const string UnauthorisedErrorCode = "Unauthorised";
    public const string UnauthorisedErrorDetails = "Unauthorised";

    public const string ConflictErrorCode = "Conflict";
    public const string ConflictErrorDetails = "Conflict";

    public const string InvalidFileTypeErrorCode = "InvalidFileTypeErrorCode";
    public const string InvalidFileTypeErrorDetails = "Invalid file type";

    public const string InvalidColorHexErrorCode = "InvalidColorHexErrorCode";
    public const string InvalidColorHexErrorDetails = "Invalid color hex";

    public const string PaymentAlreadyPaidErrorCode = "PaymentIntentAlreadyPaid";
    public const string PaymentAlreadyPaidErrorDetails = "Cannot pay an already paid payment intent";

    public const string InvalidPaymentIntentErrorCode = "InvalidPaymentIntent";
    public const string InvalidPaymentIntentNoStripePaymentIntentIdErrorDetail = "Payment intent does not have a StripePaymentIntentId";
    public const string InvalidPaymentIntentStripePaymentIntentNotFoundErrorDetail = "No Stripe payment intent could be found";
    public const string InvalidPaymentIntentStatusErrorCode = "InvalidPaymentIntentStatus";
    public const string InvalidPaymentIntentStatusUpdateErrorDetailFormat = "Cannot update payment intent with status: {0}";
    public const string InvalidPaymentIntentStatusSyncErrorDetailFormat = "Cannot sync payment intent with status: {0}";
    public const string InvalidPaymentIntentStatusCancelErrorDetailFormat = "Cannot cancel payment intent with status: {0}";
    public const string InvalidPaymentIntentStatusPayErrorDetailFormat = "Cannot pay payment intent with status: {0}";
    public const string InvalidPaymentIntentStatusPayoutErrorDetail = "Payment intent status is processing. Try again later";

    public const string PaymentIntentSyncTimeoutErrorCode = "PaymentIntentSyncTimeout";
    public const string PaymentIntentSyncTimeoutErrorDetail = "Payment complete. Payment intent sync timed out.";

    public const string PaymentIntentProcessingErrorCode = "PaymentIntentProcessing";
    public const string PaymentIntentProcessingErrorDetails = "Cannot pay a payment intent that is processing";
    
    public const string PaymentNotPaidInStripeErrorCode = "PaymentNotPaidInStripeErrorCode";
    public const string PaymentNotPaidInStripeErrorDetails = "Payment has not succeeded in Stripe";

    public const string NoStripeAccountForProviderErrorCode = "NoStripeAccountForProviderErrorCode";
    public const string NoStripeAccountForProviderErrorDetails = "No Stripe account is associated with the provider, unable to create online payments.";

    public const string StripeAccountAlreadyExistsErrorCode = "StripeAccountAlreadyExistsErrorCode";
    public const string StripeAccountAlreadyExistsErrorDetails = "A Stripe account already exists for this provider.";

    public const string StripeMinimumPaymentErrorCode = "StripeMinimumPaymentErrorCode";
    public const string StripeMinimumPaymentErrorDetails = "A minimum amount is required for online payments";

    public const string StripeFeeConfigurationNotCompleteErrorCode = "StripeFeeConfigurationNotComplete";
    public const string StripeFeeConfigurationNotCompleteErrorDetails = "Stripe fee configuration is not complete.";

    public const string OnlinePaymentsNotSupportedInCountryErrorCode = "OnlinePaymentsNotSupportedInCountryErrorCode";
    public const string OnlinePaymentsNotSupportedInCountryErrorDetails = "Carepatron doesn't support this country for online payments.";

    public const string OnlinePaymentsUnsupportedCurrencyErrorCode = "OnlinePaymentsUnsupportedCurrency";
    public const string OnlinePaymentsUnsupportedCurrencyErrorDetails = "Carepatron doesn't support this currency for online payments.";

    public const string StripeAccountNotCompleteErrorCode = "StripeAccountNotCompleteErrorCode";
    public const string StripeAccountNotCompleteErrorDetails = "The Stripe account is not complete";

    public const string EnrolmentExpiredErrorCode = "EnrolmentExpiredErrorCode";
    public const string EnrolmentExpiredErrorDetails = "The enrolment has expired.";
    public const string EnrolmentNotFoundErrorCode = "EnrolmentNotFound";
    public const string EnrolmentNotFoundErrorDetails = "No client enrolment found.";

    public const string DuplicateInvoiceNumberErrorCode = "DuplicateInvoiceNumberErrorCode";
    public const string DuplicateInvoiceNumberErrorDetails = "An invoice with this number already exists.";

    public const string EmailNotVerifiedErrorCode = "EmailNotVerifiedErrorCode";
    public const string EmailNotVerifiedErrorDetails = "Unable to perform action, because your email's not verified.";

    public const string DuplicateContactFieldSettingErrorCode = "DuplicateContactFieldSettingErrorCode";
    public const string DuplicateContactFieldSettingErrorDetails = "Contact field sections must be unique, duplicate section name.";

    public const string DuplicateContactFieldSettingFieldErrorCode = "DuplicateContactFieldSettingFieldErrorCode";
    public const string DuplicateContactFieldSettingFieldErrorDetails = "Contact field names must be unique, duplicate field name.";

    public const string StripePaymentErrorCode = "StripePaymentErrorCode";

    public const string ZoomUserNotInAccountErrorCode = "ZoomUserNotInAccountErrorCode";
    public const string ZoomUserNotInAccountErrorDetails = "A user account for the staff member could not be found in Zoom.";
    public const string InvalidZoomTokenErrorCode = "InvalidZoomTokenErrorCode";
    public const string ZoomAccountNotFound = "ZoomAccountNotFound";
    public const string ZoomAccountNotFoundDetails = "No connected Zoom account could be found.";

    public const string ItemPriceNegativeCode = "ItemPriceNegative";
    public const string ItemPriceNegativeDetails = "The service item price must not be negative";

    public const string ItemDurationNegativeCode = "ItemDurationNegative";
    public const string ItemDurationNegativeDetails = "The service duration must not be negative";

    public const string ItemsContainsDuplicatesCode = "DuplicateItemsDetected";
    public const string ItemsContainsDuplicatesDetails = "Service items cannot contain duplicate items";

    public const string InvalidItemIdsCode = "InvalidItemIds";
    public const string InvalidItemIdsDetails = "Contains invalid item ids";

    public const string ServiceReceiptNumberAlreadyExistsErrorCode = "ServiceReceiptNumberAlreadyExists";
    public const string ServiceReceiptNumberAlreadyExistsErrorDetail = "A service receipt with this number already exists";
    public const string ServiceReceiptInvalidInvoiceIdErrorCode = "InvalidInvoiceId";
    public const string ServiceReceiptInvalidInvoiceIdErrorDetail = "One or more invoice ids are invalid and do not match existing invoices";
    public const string ServiceReceiptInvoiceOnReceiptErrorCode = "InvoiceAlreadyOnReceipt";
    public const string ServiceReceiptInvoiceOnReceiptErrorDetail = "One or move invoices are already included on another receipt";
    public const string ServiceReceiptInvoiceMustBePaidErrorCode = "InvoiceMustBePaid";
    public const string ServiceReceiptInvoiceMustBePaidErrorDetail = "One or more invoices are not in the 'Paid' state.";
    public const string ServiceReceiptSingleCurrencyOnlyErrorCode = "SingleCurrencyOnly";
    public const string ServiceReceiptSingleCurrencyOnlyErrorDetail = "All invoices must match the receipt currency";
    public const string ServiceReceiptInvalidStaffErrorCode = "InvalidStaff";
    public const string ServiceReceiptInvalidStaffErrorDetail = "One or more staff ids are not valid.";

    public const string ServiceReceiptLineItemDiagnosticCodeNotReferencedErrorCode = "DiagnosticCodeNotReferenced";
    public const string ServiceReceiptLineItemDiagnosticCodeNotReferencedErrorDetail = "Line items contain diagnostic codes not referenced on the receipt";
    public const string ServiceReceiptLineItemInvalidInvoiceLineReferenceErrorCode = "InvalidInvoiceLineReference";
    public const string ServiceReceiptLineItemInvalidInvoiceLineReferenceErrorDetail = "One or more invoice line item ids are not valid";

    public const string DefaultIsRequiredErrorCode = "IsRequiredValidationFailure";

    public const string RoleCannotBeSetToNoneErrorCode = "RoleCannotBeSetToNone";

    public const string TranscriptionIdTemplateIdRequiredCode = "TranscriptionIdTemplateIdRequired";
    public const string TranscriptionIdTemplateIdRequiredMessage = "If TranscriptionId is provided, TemplateId must also be provided.";

    public const string TemplateOrPublicTemplateIdRequiredCode = "TemplateOrPublicTemplateIdRequired";
    public const string TemplateOrPublicTemplateIdRequiredMessage = "If TranscriptionId is not provided, either TemplateId or PublicTemplateId must be provided.";

    public const string SelectedStaffRequiredCode = "SelectedStaffRequired";
    public const string SelectedStaffRequiredDetail = "Selected staff is required";

    public const string CannotDeleteMasterAdminStaffCode = "CannotDeleteMasterAdminStaff";
    public const string CannotDeleteMasterAdminStaffCodeDetail = "Cannot delete a master admin.";

    public const string PaymentMethodIdRequiredCode = "PaymentMethodIdRequiredCode";
    public const string PaymentMethodIdRequiredDetail = "Payment method id is required";

    public const string PaymentIntentIdRequiredCode = "PaymentIntentIdRequiredCode";
    public const string PaymentIntentIdRequiredDetail = "Payment intent id is required.";

    public const string PaymentIntentNotFoundCode = "PaymentIntentNotFoundCode";
    public const string PaymentIntentNotFoundDetail = "Payment intent not found.";

    public const string PaymentMethodNotFoundCode = "PaymentMethodNotFoundCode";
    public const string PaymentMethodNotFoundDetail = "Payment method not found.";

    public const string InvalidPaymentMethodCode = "InvalidPaymentMethod";
    public const string InvalidPaymentMethodDetail = "The payment method id is not valid.";

    public const string InvalidAudioFileCode = "InvalidAudioFile";

    public const string InvalidAudioFileCodeDetail =
        "The audio file may not contain any recognizable speech or might be empty. Please ensure the file is in a valid format and contains audible speech content.";

    public const string NoteFormFieldNotFoundCode = "NoteFormFieldNotFoundCode";
    public const string NoteFormFieldNotFoundDetail = "Note form field not found.";

    public const string ProviderHasSubscriptionCode = "ProviderHasSubscriptionCode";
    public const string ProviderHasSubscriptionDetail = "Provider has subscription.";

    public const string StripePaymentMethodNotFoundCode = "StripePaymentMethodNotFoundCode";
    public const string StripePaymentMethodNotFoundDetail = "Stripe Payment method not found.";

    public const string VendorServiceNotAvailableCode = "VendorServiceNotAvailable";
    public const string VendorServiceNotAvailableDetail = "Vendor Service not available.";

    public const string CopyS3ObjectFailedCode = "CopyS3ObjectFailed";
    public const string CopyS3ObjectFailedDetail = "Copying S3 Object Failed.";

    public const string UploadToS3FailedCode = "UploadToS3Failed";
    public const string UploadToS3FailedDetail = "Upload to S3 failed.";

    public const string StartTranscriptionFailedCode = "StartTranscriptionFailed";
    public const string StartTranscriptionFailedDetail = "Start transcription failed.";

    public const string InvalidSummarisationOutputCode = "InvalidSummarisationOutput";
    public const string InvalidSummarisationOutputDetail = "Invalid summarisation output.";

    public const string ContactNotFoundCode = "ContactNotFoundCode";
    public const string ContactNotFoundDetail = "Contact not found.";

    public const string TaskNotFoundCode = "TaskNotFoundCode";
    public const string TaskNotFoundDetail = "Task not found.";
    public const string TaskIsNotRecurringCode = "TaskIsNotRecurring";
    public const string TaskIsNotRecurringDetail = "The task is not a recurring task and cannot be split";
    public const string TaskCouldNotCreateChildCode = "CouldNotCreateChildTask";
    public const string TaskCouldNotCreateChildDetail = "An error occurred creating a child task";

    public const string BookingIntentNotFoundCode = "BookingIntentNotFoundCode";
    public const string BookingIntentNotFoundDetail = "Booking Intent not found.";

    public const string ServicePriceCannotBeZeroCode = "ServicePriceCannotBeZeroCode";
    public const string ServicePriceCannotBeZeroDetail = "Service price cannot be zero.";

    public const string EmptyTimeBlockErrorCode = "EmptyTimeBlockFailure";
    public const string EmptyTimeBlockErrorDetail = "Timeblock cannot be empty";

    public const string CancellationPolicyIsRequiredCode = "CancellationPolicyIsRequired";
    public const string CancellationPolicyIsRequiredDetails = "Cancellation Policy is required.";

    public const string PaymentPolicyIsRequiredCode = "PaymentPolicyIsRequired";
    public const string PaymentPolicyIsRequiredDetails = "Payment Policy is required.";

    public const string ProviderItemIsRequiredCode = "ProviderItemIsRequired";
    public const string ProviderItemIsRequiredDetails = "Item is required.";

    public const string BookingCannotBeInThePastCode = "BookingCannotBeInThePast";
    public const string BookingDateCannotBeInLessThan24HoursCode = "BookingDateCannotBeInLessThan24HoursCode";

    public const string EmptySharedWithCode = "EmptySharedWithCode";
    public const string EmptySharedWithDetails = "SharedWith collection is empty.";

    public const string EmptyFilesCode = "EmptyFilesCode";
    public const string EmptyFilesDetails = "Files collection is empty.";

    public const string InvalidAttendingValueCode = "InvalidAttendingValue";
    public const string InvalidAttendingStatusDetail = "Invalid attending value";

    public const string NoStripeCustomerIdErrorCode = "NoStripeCustomerId";
    public const string NoStripeCustomerIdErrorDetail = "A customer id could not be found";
    public const string NoStripeCustomerSecretErrorCode = "NoStripeCustomerSecret";
    public const string NoStripeCustomerSecretErrorDetail = "A customer client secret could not be found";

    public const string CannotCancelBeforeMinPeriodCode = "CANNOT_CANCEL_BEFORE_MIN_PERIOD";
    public const string CannotCancelBeforeMinPeriodDetail = "Cannot cancel within minimum cancellation period";

    public const string CognitoEmailAlreadyExistCode = "COGNITO_EMAIL_ALREADY_EXISTS";
    public const string CognitoEmailAlreadyExistDetail = "Email address already exists";
    public const string CognitoUserNotFoundErrorCode = "COGNITO_USER_NOT_FOUND";
    public const string CognitoUserNotFoundErrorDetail = "User email could not be found in cognito";

    public const string InvalidProviderIdErrorCode = "InvalidProviderId";
    public const string InvalidProviderIdErrorDetails = "The ProviderId is not valid";
    public const string InvalidPersonIdErrorCode = "InvalidPersonId";
    public const string InvalidPersonIdErrorDetails = "The PersonId is not valid.";
    public const string InvalidInitiatorPersonIdErrorCode = "InvalidInitiatorPersonId";
    public const string InvalidInitiatorPersonIdErrorDetails = "The InitiatorPersonId is not valid.";
    public const string InvalidNoteIdErrorCode = "InvalidNoteId";
    public const string InvalidNoteIdErrorDetails = "The NoteId is not valid.";

    public const string InvalidNoteTranscriptionErrorCode = "InvalidNoteTranscription";
    public const string InvalidNoteTranscriptionDetails = "Cannot create new transcription. Please delete existing transcription first.";

    public const string InvalidFileIdErrorCode = "InvalidFileId";
    public const string InvalidFileIdErrorDetails = "The FileId is not valid.";
    public const string InvalidAccessLevelErrorCode = "InvalidAccessLevel";
    public const string InvalidAccessLevelErrorDetails = "The AccessLevel is not valid.";
    public const string InvalidContactIdErrorCode = "InvalidContactId";
    public const string InvalidContactIdErrorDetails = "The ContactId is not valid.";
    public const string InvalidTemplateIdErrorCode = "InvalidTemplateId";
    public const string InvalidTemplateIdErrorDetails = "The TemplateId is not valid.";
    
    public const string InvalidFolderIdErrorCode = "InvalidFolderId";
    public const string InvalidFolderIdErrorDetails = "The FolderId is not valid.";

    public const string InvalidRecaptchaTokenCode = "INVALID_RECAPTCHA_TOKEN";
    public const string InvalidRecaptchaTokenDetail = "Invalid token";

    public const string InvalidInvoiceStatusCode = "INVALID_INVOICE_STATUS";
    public const string InvalidInvoiceStatusChangeDetailFormat = "Unable to change status from {0} to {1}.";
    public const string InvalidInvoiceStatusDetailFormat = "Invoice status is {0} and cannot be {1}";
    public const string InvoiceContactDeleted = "INVOICE_CONTACT_DELETED";
    public const string InvoiceContactDeletedDetail = "The invoice contact has been deleted and this invoice cannot be updated.";

    public const string InvalidStatusForCreatingPaymentIntentCode = "INVALID_STATUS_FOR_CREATING_PAYMENT_INTENT";
    public const string InvalidStatusForCreatingPaymentIntentDetail = "Unable to create payment intent when invoice is not 'Sent' or 'Unpaid'";

    public const string InvalidSetupIntentAccountStatusCode = "INVALID_SETUP_INTENT_ACCOUNT_STATUS";
    public const string InvalidSetupIntentAccountStatusDetail = "Cannot create setup intent when billing account status is not 'Active'";

    public const string InvalidEmailCode = "INVALID_EMAIL";
    public const string InvalidEmailDetails = "Email is invalid";

    public const string StripeCustomerIdDidNotMatchCode = "STRIPE_CUSTOMER_ID_DID_NOT_MATCH";
    public const string StripeCustomerIdDidNotMatchDetail = "Stripe customer id did not match.";

    public const string SchemaKeyDoesNotExistsCode = "SCHEMA_KEY_NOT_EXISTS";
    public const string SchemaKeyDoesNotExistsDetail = "Schema key does not exists";

    public const string SchemaKeyAlreadyExistsCode = "SCHEMA_KEY_EXISTS";
    public const string SchemaKeyAlreadyExistsDetail = "Schema key already exists";

    public const string NoteFormResponseAlreadyExistsCode = "NOTE_FORM_RESPONSE_EXISTS";
    public const string NoteFormResponseAlreadyExistsDetail = "Note form response already exists";

    public const string ElementPathOutOfBounceCode = "LAYOUT_PATH_OUT_OF_BOUNCE";
    public const string ElementPathOutOfBounceDetail = "Layout path out of bounce";

    public const string InvalidExternalIdCode = "INVALID_EXTERNAL_ID";
    public const string InvalidExternalIdDetail = "Invalid external id";

    public const string AttendeeNotFoundCode = "ATTENDEE_NOTFOUND";
    public const string AttendeeNotFoundDetails = "Attendee does not exist";

    public const string DuplicateHeadingNameCode = "DUPLICATE_HEADING_NAME";
    public const string DuplicateHeadingNameDetail = "Cannot create duplicate section name";

    public const string CannotDeleteCoreSectionCode = "CANNOT_DELETE_CORE_SECTION";
    public const string CannotDeleteCoreSectionDetail = "Cannot delete a core section";

    public const string InvoiceNotFoundCode = "InvoiceNotFound";
    public const string InvoiceNotFoundDetails = "Invoice not found";

    public const string ContactStatusDoesNotExistErrorCode = "ContactStatusDoesNotExist";
    public const string ContactStatusDoesNotExistErrorDetail = "Contact Status does not exist.";

    public const string ReaderMaxDepthHasBeenExceededCode = "ReaderMaxDepthHasBeenExceededCode";
    public const string ReaderMaxDepthHasBeenExceededDetails = "The reader's MaxDepth of {0} has been exceeded.";

    public const string NoPredictionAvailableCode = "NoPredictionAvailable";
    public const string NoPredictionAvailableDetails = "No prediction available.";

    public const string InvalidStatusChangeCode = "InvalidStatusChange";
    public const string InvalidStatusChangeDetail = "Invalid status change";

    public const string DeletedStatusNotDefinedCode = "DeletedStatusNotDefined";
    public const string DeletedStatusNotDefinedDetail = "Deleted status is not defined";

    public const string DeletedStatusStillDefinedFromOptionsCode = "DeletedStatusStillDefinedFromOptions";
    public const string DeletedStatusStillDefinedFromOptionsDetail = "Deleted status still defined from the options";

    public const string BookingPolicyDateRangeExceededCode = "BookingPolicyDateRangeExceeded";
    public const string BookingPolicyDateRangeExceededDetail = "Date range exceeds booking policy";

    public const string TimeZoneIsRequiredCode = "TimeZoneIsRequired";
    public const string TimeZoneIsRequiredDetail = "Time zone is required";

    public const string TimeZoneInvalidCode = "TimeZoneInvalid";
    public const string TimeZoneInvalidDetail = "Time zone is invalid";

    public const string LocaleIsInvalidCode = "LocaleInvalid";
    public const string LocaleIsInvalidDetail = "Locale is invalid";

    public const string PaymentNotFoundCode = "PaymentNotFound";
    public const string PaymentNotFoundDetail = "Payment not found";
    
    public const string PaymentInvalidProviderForDeleteCode = "PaymentInvalidProviderForDelete";
    public const string PaymentInvalidProviderForDeleteDetail = "Deleting payments from provider is not supported";
    
    public const string PaymentMustBeUnallocatedCode = "PaymentMustBeUnallocated";
    public const string PaymentMustBeUnallocatedDetail = "Payment must be unallocated";
    
    public const string PaymentCannotBeDeletedCode = "PaymentCannotBeDeleted";
    public const string PaymentCannotBeDeletedDetail = "Payment cannot be deleted";
    
    public const string PaymentMustNotBeRefundedCode = "PaymentMustNotBeRefunded";
    public const string PaymentMustNotBeRefundedDetail = "Payment must not be refunded";
    
    public const string PaymentMustBeBillingV2Code = "PaymentMustBeBillingV2";
    public const string PaymentMustBeBillingV2Detail = "Payment must not be billing v2";

    public const string InvalidFormFieldTypeErrorCode = "InvalidFormFieldType";
    public const string InvalidFormFieldTypeErrorDetails = "Invalid form field type";

    public const string UniqueConstraintViolationErrorCode = "UniqueConstraintViolation";

    public const string InvalidStatementDescriptorErrorCode = "InvalidStatementDescriptor";

    public const string InvalidStatementDescriptorErrorDetails =
        @"Statement descriptor must be between 5 and 22 characters long and contain only letters, numbers, spaces, and must not include < > \ ' "" *";

    public const string DestinationFolderNotDefinedCode = "DestinationFolderNotDefined";
    public const string DestinationFolderNotDefinedDetail = "Destination folder is not defined";

    public const string FilterFolderNotDefinedCode = "FilterFolderNotDefined";
    public const string FilterFolderNotDefinedDetail = "Filter folder is not defined";

    public const string SourceFolderNotDefinedCode = "SourceFolderNotDefined";
    public const string SourceFolderNotDefinedDetail = "Source folder is not defined";

    public const string UnknownInboxFolderCode = "UnknownInboxFolderCode";
    public const string UnknownInboxFolderDetail = "Unknown inbox folder";

    public const string InvalidDestinationAndFilterCode = "InvalidDestinationAndFilterCode";
    public const string InvalidDestinationAndFilterDetail = "Destination and filter folder must not be the same";

    public const string InvalidDestinationAndSourceCode = "InvalidDestinationAndSourceCode";
    public const string InvalidDestinationAndSourceDetail = "Destination and source folder must not be the same";

    public const string MustSpecifyInboxIdOrContactIdCode = "MustSpecifyInboxIdOrContactIdCode";
    public const string MustSpecifyInboxIdOrContactIdDetail = "Must specify an inbox id or contact id";

    public const string MustSpecifyAllOrInboxIdOrContactIdCode = "MustSpecifyAllOrInboxIdOrContactIdCode";
    public const string MustSpecifyAllOrInboxIdOrContactIdDetail = "Must specify ALL or must specify an inbox id or contact id";

    public const string BulkSendingExceedTheLimitCode = "BulkSendingExceedTheLimitCode";
    public const string BulkSendingExceedTheLimitDetail = "The number of recipients exceeds the maximum limit of 50.";

    public const string ConversationIdIsRequiredCode = "ConversationIdIsRequiredCode";
    public const string ConversationIdIsRequiredDetail = "Conversation Id is required";

    public const string InboxIdIsRequiredCode = "InboxIdIsRequiredCode";
    public const string InboxIdIsRequiredDetail = "Inbox Id is required";

    public const string FromInboxAccountIdIsRequiredCode = "FromInboxAccountIdIsRequiredCode";
    public const string FromInboxAccountIdIsRequiredDetail = "From Inbox Account Id is required";

    public const string ConversationIdAndMessageIdRequiredCode = "ConversationIdAndMessageIdIsRequiredCode";
    public const string ConversationIdAndMessageIdRequiredDetail = "Conversation Id and Message Id is required";

    public const string ScheduledCancellationDeadlineExpiredCode = "ScheduledCancellationDeadlineExpiredCode";
    public const string ScheduledCancellationDeadlineExpiredDetail = "The scheduled message can no longer be cancelled as the cancellation deadline has passed.";

    public const string MessageIdInvalidCode = "MessageIdInvalidCode";
    public const string MessageIdInvalidDetails = "Message Id is invalid";

    public const string MessageIsNotADraftInvalidCode = "MessageIsNotADraftInvalidCode";
    public const string MessageIsNotADraftInvalidDetails = "Message is not a draft";

    public const string ErrorSendingMessageCode = "ErrorSendingMessageCode";
    public const string ErrorSendingMessageDetails = "Error encountered during sending message";

    public const string MustHaveAtLeastOneUploadedAttachmentCode = "MustHaveAtLeastOneUploadedAttachmentCode";
    public const string MustHaveAtLeastOneUploadedAttachmentDetail = "Must have at least one uploaded attachment";

    public const string MaxFileLimitExceededCode = "MaxFileLimitExceeded";
    public const string MaxFileLimitExceededDetail = "A maximum of 10 files is allowed.";

    public const string MaxFileSizeExceededCode = "MaxFileSizeExceeded";
    public const string MaxFileSizeExceededDetail = "The total file size must not exceed 7MB.";

    public const string MaximumAttachmentsSizeReachedCode = "MaximumAttachmentsSizeReachedCode";
    public const string MaximumAttachmentsSizeReachedDetail = "Maximum attachments size reached";

    public const string MaximumAttachmentsLimitReachedCode = "MaximumAttachmentsLimitReachedCode";
    public const string MaximumAttachmentsLimitReachedDetail = "Maximum attachments limit reached";

    public const string InvalidAttachmentExtensionCode = "InvalidAttachmentExtensionCode";
    public const string InvalidAttachmentExtensionDetail = "Invalid attachment extension";

    public const string InvalidWebHookMessageDataCode = "InvalidWebHookMessageDataCode";
    public const string InvalidWebHookMessageDataDetail = "Invalid web hook message data";

    public const string InvalidInboxStaffRoleTypeCode = "InvalidInboxStaffRoleTypeCode";
    public const string InvalidInboxStaffRoleTypeDetail = "Invalid inbox staff role type";

    public const string MustSpecifyOneOwnerCode = "MustSpecifyOneOwnerCode";
    public const string MustSpecifyOneOwnerDetail = "Must specify one owner";

    public const string InvalidInboxStaffsCode = "InvalidInboxStaffsCode";
    public const string InvalidInboxStaffsDetail = "Invalid inbox staffs";

    public const string InvalidChatMessageDeletionCode = "InvalidChatMessageDeletion";
    public const string InvalidChatMessageDeletionDetail = "Cannot delete chat message from another person";

    public const string InvalidChatMessageUpdateCode = "InvalidChatMessageUpdate";
    public const string InvalidChatMessageUpdateDetail = "Cannot update chat message from another person";

    public const string InvalidChatMessageSendCode = "InvalidChatMessageSend";
    public const string InvalidChatMessageSendDetail = "Cannot send chat message from another person";

    public const string InvalidChatMessageAttachmentDeletionCode = "InvalidChatMessageAttachmentDeletion";
    public const string InvalidChatMessageAttachmentDeletionDetail = "Cannot delete chat message attachment from another person";

    public const string InsufficientChatGroupPermissionCode = "InsufficientChatGroupPermission";
    public const string InsufficientChatGroupPermissionDetail = "Insufficent chat group permission";

    public const string CannotLeaveChatGroupCode = "CannotLeaveChatGroupCode";
    public const string CannotLeaveChatGroupDetail = "Cannot leave chat group when in an owner role";

    public const string NotificationTemplateIdIsRequiredCode = "NotificationTemplateIdIsRequiredCode";
    public const string NotificationTemplateIdIsRequiredDetail = "Notification Template Id is required";

    public const string LockedNoteCannotBeDeletedCode = "LockedNoteCannotBeDeletedCode";
    public const string LockedNoteCannotBeDeletedDetail = "Locked note cannot be deleted.";

    public const string LockedNoteCannotBeUpdatedCode = "LockedNoteCannotBeUpdatedCode";
    public const string LockedNoteCannotBeUpdatedDetail = "Locked note cannot be updated.";

    public const string CannotReplaceNoteFromAnotherContactCode = "CannotReplaceNoteFromAnotherContact";
    public const string CannotReplaceNoteFromAnotherContactDetail = "Cannot replace note from another contact.";

    public const string CannotReplaceNoteFromAnotherProviderCode = "CannotReplaceNoteFromAnotherProvider";
    public const string CannotReplaceNoteFromAnotherProviderDetail = "Cannot replace note from another provider.";

    public const string InsufficientCalendarScopes = "InsufficientCalendarScopes";
    public const string InsufficientCalendarScopesDetail = "Insufficient calendar scopes";

    public const string InsufficientInboxScopes = "InsufficientInboxScopes";
    public const string InsufficientInboxScopesDetail = "Insufficient inbox scopes";

    public const string InsufficientCallScopes = "InsufficientCallScopes";
    public const string InsufficientCallScopesDetail = "Insufficient call scopes";

    public const string InboxAccountAlreadyConnected = "InboxAccountAlreadyConnected";
    public const string InboxAccountAlreadyConnectedDetail = "Inbox account already connected";

    public const string MustSelectAtLeastOneConnectedAppSetting = "MustSelectAtLeastOneConnectedAppSetting";
    public const string MustSelectAtLeastOneConnectedAppSettingDetail = "Must select at least one connected app setting";

    public const string PromotionCodeInvalidCode = "InvalidPromotionCode";
    public const string PromotionCodeInvalidDetails = "Promotion code is invalid.";

    public const string CannotUpdateExclusiveFieldCode = "CannotUpdateExclusiveField";
    public const string CannotUpdateExclusiveFieldDetail = "Cannot update an exclusive field";

    public const string PromotionCodeExpiredCode = "ExpiredPromotionCode";
    public const string PromotionCodeExpiredDetails = "Promotion code has already expired.";
    public const string RequiredAuthorizedContactIdsErrorCode = "RequiredAuthorizedContactIds";
    public const string RequiredAuthorizedContactIdsErrorDetail = "Authorized contact ids are required";
    public const string CannotDeletePublishedTemplateCode = "CannotDeletePublishedTemplate";
    public const string CannotDeletePublishedTemplateDetails = "Cannot delete published template.";

    public const string ProviderItemNotFoundCode = "ProviderItemNotFound";
    public const string ProviderItemNotFoundDetails = "Provider item not found.";

    public const string ReferralCodeMissingCode = "ReferralCodeMissingCode";
    public const string ReferralCodeMissingDetails = "Referral code is not provided.";
    public const string ReferralCodeIsRequired = "ReferralCodeIsRequired";
    public const string ReferralCodeIsRequiredDetails = "Referral code is invalid.";
    public const string ReferralCodeInvalidRecipientsCode = "ReferralCodeInvalidRecipientsCode";
    public const string ReferralCodeInvalidRecipientsDetails = "Missing recipients for referral.";

    public const string InvoiceTemplateFailedCreate = "InvoiceTemplateFailedCreateCode";
    public const string InvoiceTemplateFailedCreateDetails = "Failed to create invoice template.";
    public const string InvoiceTemplateMissingArgument = "InvoiceTemplateMissingArgumentCode";
    public const string InvoiceTemplateMissingArgumentDetails = "Invoice template not provided";
    public const string InvoiceTemplateMissingId = "InvoiceTemplateMissingIdCode";
    public const string InvoiceTemplateMissingIdDetails = "Invoice template Id not provided";
    public const string InvoiceTemplateNotFoundCode = "InvoiceTemplateNotFoundCode";
    public const string InvoiceTemplateNotFoundDetails = "Invoice template not found";

    public const string InvoiceTemplateInvalidIdCode = "InvoiceTemplateInvalidIdCode";
    public const string InvoiceTemplateInvalidIdDetails = "Invoice template Id is invalid";

    public const string CannotDeleteDefaultInvoiceTemplate = "CannotDeleteDefaultInvoiceTemplate";
    public const string CannotDeleteDefaultInvoiceTemplateDetails = "Default invoice template cannot be deleted.";
    public const string InvalidTemplateErrorCode = "InvalidTemplateFormat";
    public const string InvalidTemplateErrorDetail = "Template could not be parsed";

    public const string DemoStaffEditingRestrictedCode = "DemoStaffEditingRestricted";
    public const string DemoStaffEditingRestrictedDetails = "Demo staff editing is restricted.";

    public const string CannotDeleteInvoice = "CannotDeleteInvoice";
    public const string CannotDeleteInvoiceDetail = "Invoices paid through online payments cannot be deleted";

    public const string InvoiceContactEmptyErrorCode = "EmptyInvoiceContact";
    public const string InvoiceContactEmptyErrorDetails = "Invoice contact cannot be empty.";

    public const string InvoiceSingleCurrencyOnlyErrorCode = "SingleCurrencyOnly";
    public const string InvoiceSingleCurrencyOnlyErrorDetail = "All line items must have the same currency";

    public const string InvalidDateRangeCode = "InvalidDateRange";
    public const string InvalidDateRangeDetails = "Start date should be less than end date or vise versa.";

    public const string ConnectedAppChangeNotAllowedCode = "ConnectedAppChangeNotAllowedCode";
    public const string ConnectedAppChangeNotAllowedDetails = "Can not change the inbox.";
    public const string MissingProviderInsurancePayerIdCode = "MissingProviderPayerId";
    public const string MissingProviderInsurancePayerIdDetails = "Payer ID is not provided.";
    public const string MissingProviderInsurancePayerPhoneNumberCode = "MissingProviderInsurancePayerPhoneNumberCode";
    public const string MissingProviderInsurancePayerPhoneNumberDetails = "Phone number is not provided";
    public const string MissingPaymentMethodCode = "MissingPaymentMethod";
    public const string MissingPaymentMethodDetails = "Missing payment method on Stripe";
    public const string PayerNotFoundCode = "PayerNotFound";
    public const string PayerNotFoundDetail = "Payer not found.";
    public const string InvalidPayerIdCode = "InvalidPayerId";
    public const string InvalidPayerIdDetail = "Invalid or missing payer ID.";
    public const string PayerRequiredCode = "PayerRequired";
    public const string PayerRequiredDetail = "Payer is required.";
    public const string PayerInvalidCode = "PayerInvalidCode";
    public const string PayerInvalidDetail = "Payer is invalid.";
    public const string ProviderIdRequiredCode = "ProviderIdRequiredCode";
    public const string ProviderIdRequiredDetail = "Provider ID is required.";
    public const string BillingProfilesBillingTypeRequiredCode = "BillingProfilesBillingTypeRequiredCode";
    public const string BillingProfilesBillingTypeRequiredDetail = "Billing type is required.";
    public const string BillingProfilesNameRequiredCode = "BillingProfilesNameRequiredCode";
    public const string BillingProfilesNameRequiredDetail = "Name is required.";
    public const string BillingProfilesTaxNumberRequiredCode = "BillingProfilesTaxNumberRequiredCode";
    public const string BillingProfilesTaxNumberRequiredDetail = "Tax number is required.";
    public const string BillingProfilesTaxNumberTypeRequiredCode = "BillingProfilesTaxNumberTypeRequiredCode";
    public const string BillingProfilesTaxNumberTypeRequiredDetail = "Tax number type is required.";
    public const string ErrorInvalidTaxNumberCode = "ErrorInvalidTaxNumber";
    public const string ErrorInvalidTaxNumberDetail = "The tax number is not valid.";
    public const string InvalidTaxTypeCode = "InvalidTaxType";
    public const string InvalidTaxTypeDetail = "The tax type is not valid.";
    public const string BillingProfilesNationalProviderIdRequiredCode = "BillingProfilesNationalProviderIdRequiredCode";
    public const string BillingProfilesNationalProviderIdRequiredDetail = "National Provider ID is required.";
    public const string InvalidNationalProviderIdCode = "InvalidNationalProviderId";
    public const string InvalidNationalProviderIdDetail = "The NPI number is not valid.";
    public const string BillingProfileNotFoundCode = "BillingProfileNotFound";
    public const string BillingProfileNotFoundDetail = "Billing profile not found.";
    public const string BillingProfileAlreadyHasDefaultCode = "BillingProfileAlreadyHasDefaultCode";
    public const string BillingProfileAlreadyHasDefaultDetail = "Already has a default billing profile.";
    public const string CannotDeleteDefaultBillingProfileCode = "CannotDeleteDefaultBillingProfile";
    public const string CannotDeleteDefaultBillingProfileDetails = "Cannot delete default billing profile.";
    public const string BookingTimeslotNotAvailableCode = "BookingTimeslotNotAvailable";
    public const string BookingTimeslotNotAvailableDetails = "Booking timeslot is not available.";
    public const string BillableNotFoundCode = "BillableNotFound";
    public const string BillableNotFoundDetail = "Billable not found.";
    public const string InvalidIssueCreditAmountCode = "InvalidIssueCreditAmount";
    public const string InvalidIssueCreditAmountDetail = "The credit amount is not valid";

    public const string IsOnlineMustBeTrueCode = "IsOnlineMustBeTrue";
    public const string IsOnlineMustBeTrueCodeDetails = "IsOnline must be 'true' when IsOnlyOnline is enabled";
    public const string CannotTransferOwnershipToDemoStaffCode = "CannotTransferOwnershipToDemoStaff";
    public const string CannotTransferOwnershipToDemoStaffDetails = "Cannot transfer ownership to a demo staff member";
    public const string InvalidRefundRequestCode = "InvalidRefundRequest";
    public const string InvalidRefundRequestDetails = "Invalid refund request";
    public const string RefundFailedCode = "RefundFailed";
    public const string RefundFailedDetails = "A refund could not be created due to an unexpected error";
    public const string InvalidTemplateImportStatusForRetryCode = "InvalidTemplateImportStatusForRetry";
    public const string InvalidTemplateImportStatusForRetryDetails = "Invalid template import status for retry";

    public const string InvalidTranscriptionStatusToRetryCode = "InvalidTranscriptionStatusToRetry";
    public const string InvalidTranscriptionStatusToRetryDetails = "Invalid transcription status to retry";

    public const string InsuranceClaimNotFoundCode = "InsuranceClaimNotFound";
    public const string InsuranceClaimNotFoundDetails = "Insurance claim not found.";
    public const string InsuranceClaimDuplicateNumberCode = "InsuranceClaimDuplicateNumber";
    public const string InsuranceClaimDuplicateNumberDetails = "Insurance claim number already exists.";
    public const string InsuranceClaimInvalidStatusCode = "InsuranceClaimInvalidStatusCode";
    public const string InsuranceClaimInvalidStatusDetails = "Invalid status for insurance claim.";
    public const string InsuranceClaimNoAllocatedPaymentsCode = "InsuranceClaimNoAllocatedPayments";
    public const string InsuranceClaimNoAllocatedPaymentsDetails = "Cannot mark claim as paid with no allocated payments.";
    public const string InsuranceClaimFailedToUpdateStatusCode = "InsuranceClaimFailedToUpdateStatusCode";
    public const string InsuranceClaimFailedToUpdateStatusDetails = "Failed to update status for insurance claim.";
    public const string InsuranceClaimFailedToValidateCode = "ClaimValidationFailure";
    public const string InsuranceClaimFailedToValidateDetails = "Failed to validate insurance claim.";
    public const string ClearingHouseUnhandledErrorCode = "ClearingHouseUnhandledError";
    public const string ClearingHouseUnhandledErrorDetails = "Unhandled error from clearing house.";
    public const string InsurancePayerEnrollmentFailureCode = "InsurancePayerEnrollmentFailure";
    public const string InsurancePayerEnrollmentFailureDetails = "Failed to enroll insurance payer.";
    public const string EnrolmentTransactionNotAvailableCode = "EnrolmentTransactionNotAvailable";
    public const string EnrolmentTransactionNotAvailableDetails = "Insurance payer enrollment transaction not supported.";
    public const string ElectronicClaimsStatusCannotBeChangedCode = "ElectronicClaimsStatusCannotBeChanged";
    public const string ElectronicClaimsStatusCannotBeChangedDetails = "Cannot change status for claims that have been electronically submitted.";

    public static string RequiredFieldCode(string fieldName) => $"{fieldName}RequiredCode";
    public static string RequiredFieldDetails(string fieldName) => $"{fieldName} is required.";

    public static string MaxLengthExceededCode(string fieldName) => $"{fieldName}MaxLengthExceededCode";
    public static string MaxLengthExceededDetails(string fieldName, int length) => $"{fieldName} length must not exceed {length} characters.";

    public const string ConnectedAppHasIncompleteCalendarSubscriptionCode = "ConnectedAppHasIncompleteCalendarSubscription";
    public const string ConnectedAppHasIncompleteCalendarSubscriptionDetails = "Connected app has incomplete calendar subscription";

    public const string MultiplePrimaryEmailsFoundCode = "MultiplePrimaryEmailsFound";
    public const string MultiplePrimaryEmailsFoundDetails = "Multiple primary emails found.";
    public const string MultiplePrimaryPhoneNumbersFoundCode = "MultiplePrimaryPhoneNumbersFound";
    public const string MultiplePrimaryPhoneNumbersFoundDetails = "Multiple primary phone numbers found.";
    public const string MultiplePrimaryAddressesFoundCode = "MultiplePrimaryAddressesFound";
    public const string MultiplePrimaryAddressesFoundDetails = "Multiple primary addresses found";

    public const string InsuranceClaimCannotDeleteClaimCode = "InsuranceClaimCannotDeleteClaim";
    public const string InsuranceClaimCannotDelete_ElectronicSubmissionDetail = "Cannot delete electronically submitted claims in {0} status";

    public const string ContactsDoNotBelongToProviderCode = "ContactsDoNotBelongToProvider";
    public const string ContactsDoNotBelongToProviderDetail = "Contacts do not belong to the provider";

    public const string PaymentMethodRequiredCode = "PaymentMethodRequired";
    public const string PaymentMethodRequiredDetail = "Payment method is required";

    public const string InsuranceClaimFailedToExportCode = "InsuranceClaimFailedToExport";
    public const string InsuranceClaimFailedToExportDetail = "Exporting claim failed due to unexpected error";

    public const string InsuranceClaimFailedToGenerateExportUrlCode = "InsuranceClaimFailedToGenerateExportUrl";
    public const string InsuranceClaimFailedToGenerateExportUrlDetail = "Failed generating export url for claim";

    public const string TaskBillableSyncTimeoutErrorCode = "TaskBillableSyncTimeout";
    public const string TaskBillableSyncTimeoutErrorDetail = "Task billable sync timed out.";

    public const string FeatureModuleDisabledErrorCode = "FeatureModuleDisabledError";
    public const string FeatureModuleDisabledErrorDetails = "Feature module is disabled";
    public const string InvalidAllocationAmountCode = "InvalidAllocationAmount";
    public const string InvalidAllocationAmountDetail = "The total allocated amount cannot exceed the payment amount";

    public const string MultiplePrimaryLanguagesFoundCode = "MultiplePrimaryLanguagesFound";
    public const string MultiplePrimaryLanguagesFoundDetails = "Multiple primary languages found";
    public const string NegativeBalanceNotSupportedCode = "NegativeBalanceNotSupported";
    public const string NegativeBalanceNotSupportedDetails = "Negative balance is not supported";
    public const string CheckConstraintViolationErrorCode = "CheckConstraintViolation";
    public const string InvoiceInvalidCreditCode = "InvoiceInvalidCredit";
    public const string InvoiceInvalidCreditDetails = "Invalid credit amount, credit amount cannot exceed the invoice total";

    public const string InvalidRRuleErrorCode = "InvalidRRule";
    public const string InvalidRRuleErrorDetails = "Invalid rrule";

    public const string PublicFormsInvalidConfirmationCodeCode = "PublicFormsInvalidConfirmationCode";
    public const string PublicFormsInvalidConfirmationCodeDetails = "Invalid confirmation code";

    public const string ResetPasswordInvalidConfirmationCodeCode = "ResetPasswordInvalidConfirmationCode";
    public const string ResetPasswordInvalidConfirmationCodeDetails = "Invalid confirmation code";

    public const string UserNotFoundCode = "UserNotFound";
    public const string UserNotFoundDetails = "User not found";

    public const string MultipleDefaultIntegratedLocationCode = "MultipleDefaultIntegratedLocation";
    public const string MultipleDefaultIntegratedLocationDetails = "Multiple default integrated locations found";

    public const string ChatDraftAlreadyExistsForConversationCode = "ChatAlreadyExistsForConversation";
    public const string ChatDraftAlreadyExistsForConversationDetails = "Only 1 active draft allowed in chat. Use update endpoint to update existing draft.";

    public const string WorkspaceNameContainsSpecialCharactersCode = "WorkspaceNameContainsSpecialCharactersCode";
    public const string WorkspaceNameContainsSpecialCharactersDetails = "Workspace name cannot have special characters";

    public const string UnexpectedEndOfRequestCode = "UnexpectedEndOfRequest";
    
    public const string RequestCannotBeEmptyCode = "RequestCannotBeEmpty";
    public const string RequestCannotBeEmptyDetails = "Request cannot be empty";
    
    public const string ExternalContactNotFoundCode = "ExternalContactNotFound";
    public const string ExternalContactNotFoundDetails = "External contact not found";
    
    public const string LinkExternalContactEmailMismatchCode = "LinkExternalContactEmailMismatch";
    public const string LinkExternalContactEmailMismatchDetails = "Email address does not match the external contact";
    
    public const string CannotModifyEventFromReadOnlyCalendarCode = "CannotModifyEventFromReadOnlyCalendar";
    public const string CannotModifyEventFromReadOnlyCalendarDetails = "Cannot modify event from read-only calendar";
    public const string ClaimNotValidForElectronicSubmissionCode = "ClaimNotValidForElectronicSubmission";
    public const string ClaimNotValidForElectronicSubmissionDetails = "Claim is not valid for electronic submission";
    public const string ClaimFailedToSubmitToClearingHouseCode = "ClaimFailedToSubmitToClearingHouse";
    public const string ClaimFailedToSubmitToClearingHouseDetails = "Claim failed to submit to clearing house";
    public const string ClaimMissingClearingHouseCode = "ClaimMissingClearingHouse";
    public const string ClaimMissingClearingHouseDetails = "Claim is missing clearing house";
    public const string ClaimPayerEnrollmentRequiredCode = "ClaimPayerEnrollmentRequired";
    public const string ClaimPayerEnrollmentRequiredDetails = "Claim payer enrollment is required";
    public const string ClearingHouseGeneralErrorCode = "ClearingHouseGeneralError";
    public const string ClearingHouseGeneralErrorDetails = "An error occurred while communicating with the clearing house. Please try again later.";
    public const string ClearingHouseUnavailableCode = "ClearingHouseUnavailable";
    public const string ClearingHouseUnavailableDetails = "Clearing house is unavailable";
    public const string InvalidPolicyHolderRelationshipCode = "InvalidPolicyHolderRelationship";
    public const string InvalidPolicyHolderRelationshipDetails = "Invalid policy holder relationship";
    public const string InvalidSexCode = "InvalidSex";
    public const string InvalidSexDetails = "Unsupported sex value";
    
    public const string InvalidDeletedTaskStatusesCode = "InvalidDeletedTaskStatusesCode";
    public const string InvalidDeletedTaskStatusesDetails = "Invalid deleted task statuses";
    public const string InvalidTaskStatusChangeMapCode = "InvalidTaskStatusChangeMapCode";
    public const string InvalidTaskStatusChangeMapDetails = "Invalid task status change map";
    public const string InvalidTaskStatusChangeCode = "InvalidTaskStatusChangeCode";
    public const string InvalidTaskStatusChangeDetails = "Invalid task status change";

    public const string UnableToCancelContactImportSummaryCode = "UnableToCancelContactImportSummary";
    public const string UnableToCancelContactImportSummaryDetailsFormat = "Unable to cancel contact import summary with status {0}";
}
