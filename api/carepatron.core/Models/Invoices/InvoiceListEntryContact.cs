using carepatron.core.Models.Common;
using System;

namespace carepatron.core.Models.Invoices
{
    public class InvoiceListEntryContact
    {
        public Guid Id { get; set; }

        public string Name { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string PreferredName { get; set; }

        public string MiddleNames { get; set; }

        public string BusinessName { get; set; }

        public Email Email { get; set; }

        public string PhoneNumber { get; set; }

        public Guid? PersonId { get; set; }

        public bool IsClient { get; set; }
    }
}
