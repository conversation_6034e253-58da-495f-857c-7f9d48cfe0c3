using carepatron.core.Utilities;

namespace carepatron.core.Models.Common;

public record class PhoneNumber
{
    public string CountryCode { get; set; }
    public string Number { get; set; }

    public PhoneNumber() { }

    public PhoneNumber(string countryCode, string number)
    {
        CountryCode = countryCode;
        Number = number;
    }

    public static implicit operator PhoneNumber(string v)
    {
        var countryCode = PhoneNumberUtilities.GetPhoneCountryCodeFromPhoneNumber(v, null);
        return new() { Number = v, CountryCode = countryCode is null ? null : $"+{countryCode}" };
    }

    public string GetNumberOnly()
    {
        return !string.IsNullOrEmpty(CountryCode) ? Number.Replace(CountryCode, "") : Number;
    }
}
