using System;
using System.Linq;
using carepatron.core.Application.TaxRates.Models;
using carepatron.core.Common;
using carepatron.core.Extensions;

namespace carepatron.core.Application.Billables.Models;

public class BillableItem
{
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public Guid ContactId { get; set; }
    public Guid BillableId { get; set; }
    public Guid? ServiceId { get; set; }
    public DateOnly? Date { get; set; }
    public string Code { get; set; }
    public string POSCode { get; set; }
    public string Description { get; set; }
    public string Detail { get; set; }
    public string CurrencyCode { get; set; }
    public decimal Price { get; set; }
    public decimal Units { get; set; }
    public bool IsInsuranceEnabled { get; set; }
    public bool IsManuallyUpdated { get; set; }

    /// <summary>
    /// The total price of the line item.
    /// Tax exclusive
    /// Units * price
    /// </summary>
    public decimal Amount => CurrencyHandler.Get(CurrencyCode).Round(Units * Price);

    /// <summary>
    /// The taxes applied to this billable item
    /// </summary>
    public ItemTaxRate[] TaxRates { get; set; }

    /// <summary>
    /// The computed total tax rate. The sum of all TaxRates.
    /// </summary>
    public decimal TaxRate => TaxRates?.Sum(x => x.Rate) ?? 0;

    /// <summary>
    /// Computed tax amount. Amount * TaxRate
    /// </summary>
    public decimal TaxAmount => CurrencyHandler.Get(CurrencyCode).Round(Amount * (TaxRate / 100));

    /// <summary>
    /// Total write off amount
    /// Tax inclusive
    /// Mutable
    /// </summary>
    public decimal WriteOff { get; set; }

    /// <summary>
    /// The total owed.
    /// The total amount plus tax.
    /// Tax inclusive
    /// </summary>
    public decimal TotalOwed => Amount + TaxAmount;

    /// <summary>
    /// The amount the client is responsible for
    /// Editable by the user.
    /// Tax inclusive.
    /// </summary>
    public decimal SelfPayAmount { get; set; }

    /// <summary>
    /// The amount the client is responsible for
    /// Calculated, Total - SelfPay
    /// </summary>
    public decimal InsuranceAmount => TotalOwed - SelfPayAmount;

    /// <summary>
    /// The amount paid by invoice payments
    /// Calculated and set through payment allocations
    /// </summary>
    public decimal Paid { get; set; }

    public decimal Unpaid => SelfPayAmount - Paid - (InsuranceAmount > 0 ? 0 : WriteOff);

    /// <summary>
    /// The amount paid by claim payments
    /// Calculated and set through payment allocations
    /// </summary>
    public decimal InsurancePaid { get; set; }

    public decimal InsuranceUnpaid =>
        InsuranceAmount - InsurancePaid - (InsuranceAmount > 0 ? WriteOff : 0);

    /// <summary>
    /// The self pay amount outstanding to be added to invoices
    /// </summary>
    public decimal Uncharged { get; set; }
    public decimal UnchargedTax => Uncharged - UnchargedPrice;
    public decimal UnchargedPrice => TaxRate == -100 ? 0 :
        CurrencyHandler.Get(CurrencyCode).Round(Uncharged / (1 + TaxRate / 100));

    /// <summary>
    /// The insurance amount outstanding that needs to be added to claims.
    /// </summary>
    public decimal Unclaimed { get; set; }

    public bool IsDeleted { get; set; }

    public static BillableItem Create(Billable billable, BillableItem item, Guid providerId, bool isManuallyUpdated = true)
    {
        return new BillableItem
        {
            Id = item.Id,
            Units = item.Units,
            Price = item.Price,
            CurrencyCode = billable.CurrencyCode,
            SelfPayAmount = item.SelfPayAmount,
            TaxRates = item.TaxRates,
            WriteOff = item.WriteOff,
            IsInsuranceEnabled = item.IsInsuranceEnabled,
            Description = item.Description,
            Detail = item.Detail,
            ServiceId = item.ServiceId,
            ProviderId = providerId,
            BillableId = billable.Id,
            ContactId = billable.ContactId,
            Code = item.Code,
            Date = item.Date ?? billable.Date.ToDateOnly(),
            IsManuallyUpdated = isManuallyUpdated
        };
    }
}
