﻿using carepatron.core.Application.Files.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Files;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Files.Commands
{
    public class GeneratePresignedUrlCommandHandler : IMediatrCommandHandler<GeneratePresignedUrlCommand, string>
    {
        private readonly IFileStorageRepository fileStorageRepository;

        public GeneratePresignedUrlCommandHandler(IFileStorageRepository fileStorageRepository)
        {
            this.fileStorageRepository = fileStorageRepository;
        }

        public async Task<ExecutionResult<string>> Handle(GeneratePresignedUrlCommand request, CancellationToken cancellationToken)
        {
            var presignedUrl = fileStorageRepository.GeneratePresignedUrl(request.FileId, request.FileLocationType, request.FileName, request.Download);

            return presignedUrl;
        }
    }
}
