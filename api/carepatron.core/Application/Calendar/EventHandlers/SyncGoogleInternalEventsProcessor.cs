using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using carepatron.core.Application.Calendar.Models;
using carepatron.core.Application.ConnectedApps.Models;
using carepatron.core.Application.Tasks.Events;
using carepatron.core.Application.Tasks.Extensions;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Users.Models;
using carepatron.core.Extensions;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Models.User;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Utilities;
using Serilog;

namespace carepatron.core.Application.Calendar.EventHandlers;

public class SyncGoogleInternalEventsProcessor(IIntegrationEventPublisher integrationEventPublisher, ITaskRepository taskRepository, IMapper mapper)
{
    public async Task<(List<SaveTaskModel> tasksToCreate, List<SaveTaskModel> tasksToUpdate)> GetData(IEnumerable<ExternalTask> externalTasks,
        ConnectedApp connectedApp,
        CalendarSubscription calendarSubscription,
        SimplePerson person,
        PersonalSettings personalSettings)
    {
        var existingTasksDictionary = await GetExistingTasks(externalTasks);
        var userTimeZone = personalSettings?.TimeZone;

        List<SaveTaskModel> tasksToCreate = new();
        List<SaveTaskModel> tasksToUpdate = new();

        Dictionary<Guid, IEnumerable<ExternalTask>> cancelledExceptions = new();
        foreach (var externalTask in externalTasks)
        {
            if (string.IsNullOrWhiteSpace(externalTask.ParentExternalId) || externalTask.ExternalStatus != "cancelled") continue;

            if (!cancelledExceptions.ContainsKey(externalTask.Id))
                cancelledExceptions.Add(externalTask.Id, Array.Empty<ExternalTask>());

            cancelledExceptions[externalTask.Id] = cancelledExceptions[externalTask.Id].Append(externalTask);
        }

        foreach (var externalTask in externalTasks)
        {
            TaskModel existingTask = null;
            if (existingTasksDictionary.ContainsKey(externalTask.Id))
                existingTask = existingTasksDictionary[externalTask.Id];

            Log.ForContext("ExternalTask", new
                {
                    externalTask.Id,
                    externalTask.ExternalId,
                    externalTask.ExternalStatus,
                    externalTask.ParentExternalId,
                    externalTask.StartDate,
                    externalTask.OriginalStartDate,
                    externalTask.AllDay,
                    externalTask.TimeZone,
                    externalTask.RRule,
                    externalTask.ExDate,
                    MetadataTaskId = externalTask.Metadata?.TaskId,
                    ExistingTaskId = existingTask?.Id,
                    UserTimeZone = userTimeZone
                }, true)
                .Information($"{nameof(SyncGoogleInternalEventsProcessor)} - Processing external task");

            // no plan to handle deleted events for now.
            // if this task gets updated in carepatron, it will be re-created in google calendar again.
            if (externalTask.ExternalStatus == "cancelled")
            {
                continue;
            }

            // we normally expect for existing task here as it should originate from our system but it's also possible that the task is "created" from google calendar.
            // it happens when a user updates a carepatron recurring event in google calendar and selects "This and following events".
            // meaning the original recurring event will have its UNTIL in RRule and Occurrence End Date updated and there will be a new recurring event created by google.
            // for this new recurring event, google would make a copy of the original recurring event including the extended properties we set upon its creation.
            // from there we can check the extended properties (metadata) for the original task id.
            // here we will create that new recurring event into our system and delete the one in google calendar so we can link our task id to google event icaluid.
            if (existingTask is null)
            {
                if (string.IsNullOrWhiteSpace(externalTask.RRule)) continue;

                var metadataTaskId = externalTask.Metadata?.TaskId;
                if (!metadataTaskId.HasValue) continue;

                // check if the original task exists
                if (!existingTasksDictionary.ContainsKey(metadataTaskId.Value)) continue;

                var newTask = await BuildRecurringTask(externalTask, connectedApp, person.Id, userTimeZone);
                newTask.Type = existingTasksDictionary[metadataTaskId.Value].Type;
                tasksToCreate.Add(newTask);

                // we need to delete the event in gcal and re-create it with the new icaluid so that it's linked to the new task
                integrationEventPublisher.Add(new OutgoingEvent<ExternalTaskDeletedEvent>(new ExternalTaskDeletedEvent(
                    new ExternalTaskDeletedEventModel(connectedApp.ProviderId.Value, connectedApp.PersonId.Value, calendarSubscription.CalendarId, externalTask.ExternalId, null, TaskType.External, true)
                )));
                integrationEventPublisher.Add(new OutgoingEvent<TaskCreatedEvent>(new(newTask)));
                continue;
            }

            // this is a regular event
            if (string.IsNullOrWhiteSpace(existingTask.RRule))
            {
                var model = BuildTaskToUpdate(externalTask, existingTask, person.Id);
                if (!model.SameAs(existingTask))
                {
                    tasksToUpdate.Add(model);
                    integrationEventPublisher.Add(new OutgoingEvent<TaskUpdatedEvent>(new(existingTask, existingTask)));
                }

                continue;
            }

            // handle recurring events
            var isSingleInstance = string.IsNullOrWhiteSpace(externalTask.RRule);

            // this is a task "created" from google calendar when user updates a carepatron recurring event and selects "This event".
            // here it appears that this event is already in our system but it's not and that's because its Id is based on the original recurring task id (ICalUid) which google copied during its creation.
            if (isSingleInstance)
            {
                var (updatedParent, newTask) = BuildExceptionInstanceTask(existingTask, externalTask, connectedApp, person.Id);
                tasksToUpdate.Add(updatedParent);
                tasksToCreate.Add(newTask);

                // we want to update this task's ex date object so calculation during this loop is updated
                existingTask.ExDate = updatedParent.ExDate;

                // we need to delete the event in gcal and re-create it with the new icaluid so that it's linked to the new task
                integrationEventPublisher.Add(new OutgoingEvent<ExternalTaskDeletedEvent>(new ExternalTaskDeletedEvent(
                    new ExternalTaskDeletedEventModel(connectedApp.ProviderId.Value, connectedApp.PersonId.Value, calendarSubscription.CalendarId, externalTask.ExternalId, null, TaskType.External, true)
                )));
                integrationEventPublisher.Add(new OutgoingEvent<TaskCreatedEvent>(new(newTask)));
                integrationEventPublisher.Add(new OutgoingEvent<TaskUpdatedEvent>(new(existingTask, existingTask)));
            }
            // this is just a change in the recurring event that applies to all, meaning we just need to update the parent itself
            else
            {
                var model = BuildTaskToUpdate(externalTask, existingTask, person.Id);

                IEnumerable<ExternalTask> exceptions = Array.Empty<ExternalTask>();
                if (cancelledExceptions.TryGetValue(externalTask.Id, out var instances))
                    exceptions = instances;

                var exDateForExceptions = string.Empty;
                foreach (var instance in exceptions)
                {
                    var instanceStartDate = instance.OriginalStartDate ?? instance.StartDate;
                    if (instance.AllDay && DateTime.TryParse(instance.OriginalAllDayStartDate, out var parsedInstanceStartDate))
                        instanceStartDate = parsedInstanceStartDate.ToUniversalTime(model.TimeZone);

                    var exDate = CalendarUtilities.CalculateExDate(model.TimeZone, model.StartDate, instanceStartDate);
                    var formattedExDate = exDate.ToExDateFormat(instance.AllDay);
                    exDateForExceptions = string.IsNullOrWhiteSpace(exDateForExceptions) ? formattedExDate : $"{exDateForExceptions},{formattedExDate}";
                }

                var combinedExDate = string.IsNullOrEmpty(exDateForExceptions) ? existingTask.ExDate : $"{existingTask.ExDate},{exDateForExceptions}";

                // we just need to add ex dates here, NO NEED to adjust exdates time to match the instance's start time
                model.ExDate = CalendarUtilities.CleanUpExDates(combinedExDate);

                if (!model.SameAs(existingTask))
                {
                    // we want to update this task's ex date object so calculation during this loop is updated
                    existingTask.ExDate = model.ExDate;
                    tasksToUpdate.Add(model);
                    integrationEventPublisher.Add(new OutgoingEvent<TaskUpdatedEvent>(new(existingTask, existingTask)));
                }
            }
        }

        return (tasksToCreate, tasksToUpdate);
    }

    public async Task PublishEventsToBeDeleted(IEnumerable<ExternalTask> externalTasks, ConnectedApp connectedApp)
    {
        var existingTasksDictionary = await GetExistingTasks(externalTasks);

        var nonExistingTasks = externalTasks
            .Where(i =>
                (!existingTasksDictionary.ContainsKey(i.Id) && i.Metadata?.TaskId.HasValue == true && i.Id == i.Metadata.TaskId.Value) ||
                (existingTasksDictionary.ContainsKey(i.Id) && existingTasksDictionary[i.Id].IsAppointmentCancelled()))
            .Select(i => i.Id)
            .ToArray();

        foreach (var taskId in nonExistingTasks)
        {
            integrationEventPublisher.Add(new OutgoingEvent<ExternalTaskDeletedEvent>(new(
                new(connectedApp.ProviderId.Value, connectedApp.PersonId.Value, null, null, taskId, TaskType.ClientEvent, true)
            )));
        }
    }

    private async Task<Dictionary<Guid, TaskModel>> GetExistingTasks(IEnumerable<ExternalTask> externalTasks)
    {
        var taskIdsFromMetadata = externalTasks.Select(i => i.Metadata?.TaskId).Where(i => i.HasValue).Select(i => i.Value).ToArray();
        var taskIds = externalTasks.Select(i => i.Id).Concat(taskIdsFromMetadata).ToArray();
        var existingTasks = await taskRepository.GetTasks(taskIds);
        return existingTasks.ToDictionary(i => i.Id);
    }

    private SaveTaskModel BuildTaskToUpdate(ExternalTask externalTask, TaskModel existingTask, Guid personId)
    {
        var model = mapper.Map<SaveTaskModel>(existingTask);
        model = model.MapFromExternal(externalTask, personId);
        SetDatesForAllDay(model, externalTask, existingTask.TimeZone);

        // we don't want to change the timezone of the task if it's already set
        // so we don't affect email/sms notifications/reminders
        model.TimeZone = existingTask.TimeZone;

        return model;
    }

    private async Task<SaveTaskModel> BuildRecurringTask(ExternalTask externalTask, ConnectedApp connectedApp, Guid personId, string timeZone)
    {
        // this is a new recurring event caused by "This and following events"
        // and will be on its own but we need to copy a few things from its previous parent.
        // Note that we don't want to copy ex date from the previous parent to conform with the existing behavior.
        var newTask = mapper.Map<SaveTaskModel>(externalTask);
        newTask = newTask.MapFromExternal(externalTask, personId, true);
        newTask.Id = Guid.NewGuid();
        newTask.ProviderId = connectedApp.ProviderId.Value;
        newTask.RRule = externalTask.RRule;
        newTask.OccurrenceEndDate = externalTask.OccurrenceEndDate;
        newTask.ExDate = null;
        newTask.ExternalId = null;
        newTask.TimeZone = timeZone;

        var previousParentTaskId = externalTask.Metadata.TaskId;
        var previousParentTask = await taskRepository.Get(previousParentTaskId.Value);
        if (previousParentTask is not null)
        {
            CopyParentInfo(previousParentTask, newTask);
            newTask.TimeZone = previousParentTask.TimeZone;
        }

        if (string.IsNullOrEmpty(newTask.TimeZone)) newTask.TimeZone = externalTask.TimeZone;

        SetDatesForAllDay(newTask, externalTask, newTask.TimeZone);

        return newTask;
    }

    private (SaveTaskModel parentTask, SaveTaskModel newTask) BuildExceptionInstanceTask(TaskModel parentTaskModel, ExternalTask externalTask, ConnectedApp connectedApp, Guid personId)
    {
        var clonedParentTask = parentTaskModel.Clone();
        var parentTask = mapper.Map<SaveTaskModel>(clonedParentTask);
        var childTask = mapper.Map<SaveTaskModel>(clonedParentTask);
        var timeZone = externalTask.AllDay ? parentTaskModel.TimeZone : externalTask.TimeZone;

        var startDate = externalTask.OriginalStartDate ?? externalTask.StartDate;
        if (externalTask.AllDay && DateTime.TryParse(externalTask.OriginalAllDayStartDate, out var parsedDate))
        {
            startDate = parsedDate.ToUniversalTime(timeZone);
        }

        // ex date is based on task's (new instance) start date and existingTask's (parent) start time
        var exDate = CalendarUtilities.CalculateExDate(timeZone, parentTask.StartDate, startDate);
        var formattedExDate = exDate.ToExDateFormat(externalTask.AllDay);
        parentTask.ExDate = CalendarUtilities.CleanUpExDates(string.IsNullOrWhiteSpace(parentTaskModel.ExDate) ? formattedExDate : $"{parentTask.ExDate},{formattedExDate}");

        childTask = childTask.MapFromExternal(externalTask, personId, true);
        childTask.Id = Guid.NewGuid();
        childTask.ProviderId = connectedApp.ProviderId.Value;
        childTask.ExDate = null;
        childTask.RRule = null;
        childTask.OccurrenceEndDate = null;
        childTask.ParentId = parentTask.Id;
        childTask.TimeZone = timeZone;
        SetDatesForAllDay(childTask, externalTask, parentTask.TimeZone);
        CopyParentInfo(parentTaskModel, childTask);

        return (parentTask, childTask);
    }

    private void CopyParentInfo(TaskModel parentTask, SaveTaskModel model)
    {
        if (parentTask is null) return;

        model.Items = parentTask.ItemsSnapshot;
        model.LocationsSnapshot = parentTask.LocationsSnapshot;
        model.Contacts = parentTask.Contacts
            ?.Select(c => c.ToSimpleTaskContact(model.Id))
            .ToArray();
        model.StaffIds = parentTask.StaffIds;
    }

    private void SetDatesForAllDay(SaveTaskModel model, ExternalTask externalTask, string timeZone)
    {
        if (!externalTask.AllDay) return;

        // since all-day task is always UTC, we need to convert it to specified timezone
        model.StartDate = externalTask.StartDate.ToUniversalTime(timeZone);
        model.EndDate = model.StartDate.AddDays(1);
    }
}