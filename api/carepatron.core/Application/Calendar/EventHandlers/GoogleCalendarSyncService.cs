using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using carepatron.core.Abstractions;
using carepatron.core.Application.Calendar.Models;
using carepatron.core.Application.Calendar.Services;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Extensions;
using carepatron.core.Repositories.ConnectedApps;
using Serilog;
using carepatron.core.Repositories.Person;
using carepatron.core.Application.Users.Models;
using carepatron.core.Application.ConnectedApps.Models;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.Models.User;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Application.ConnectedApps.Abstractions.Settings;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Events.Events;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Events;

namespace carepatron.core.Application.Calendar.EventHandlers;

public class GoogleCalendarSyncService(IIntegrationEventPublisher integrationEventPublisher,
    IUnitOfWork unitOfWork,
    ICalendarSubscriptionRepository calendarSubscriptionRepository,
    IConnectedCalendarService connectedCalendarService,
    ICalendarService calendarService,
    IPersonRepository personRepository,
    ITaskRepository taskRepository,
    IContactRepository contactRepository,
    SyncGoogleExternalEventsProcessor syncGoogleExternalEventsProcessor,
    SyncGoogleInternalEventsProcessor syncGoogleInternalEventsProcessor) : ICalendarSyncService
{
    private const int MaxItemsPerPage = 250;
    private const int BatchSize = 300;

    public async Task Process(CalendarSubscription calendarSubscription)
    {
        unitOfWork.UseUnitOfWork();

        var connectedApp = calendarSubscription.ConnectedApp;

        var person = await personRepository.Get(connectedApp.PersonId.Value);
        if (person is null)
        {
            Log.Error("Person not found for calendar subscription {CalendarSubscriptionId}", calendarSubscription.Id);
            return;
        }

        var calendar = await connectedCalendarService.GetCalendar(connectedApp.Id, calendarSubscription.CalendarId);
        if (calendar is null)
        {
            Log.Error("Google Calendar not found for calendar subscription {CalendarSubscriptionId}", calendarSubscription.Id);
            return;
        }

        var settings = connectedApp.Settings.AsSettingType<IConnectedAppCalendarSettings>();

        // Push tasks to external calendars if it's the first sync
        if (calendarSubscription.IsInitialSync() && settings.Calendar.Push && connectedApp.CalendarId == calendarSubscription.CalendarId)
        {
            var tasks = await taskRepository.Get(connectedApp.ProviderId.Value, DateTime.UtcNow.AddMonths(-3), DateTime.MaxValue, new[] { person.Id });
            foreach (var task in tasks)
                await calendarService.QueueSyncCalendarJob(connectedApp.ProviderId.Value, task.Id, person.Id, EventType.Task_Updated, platformSource: PlatformSource.QueueWorker);
        }

        var fromStartDate = calendarSubscription.IsInitialSync() ? DateTime.UtcNow.AddMonths(-3) : (DateTime?)null;
        var eventsResult = await connectedCalendarService.GetExternalTasks(connectedApp,
            (SimplePerson)person,
            new GetEventsOptions
            {
                CalendarId = calendarSubscription.CalendarId,
                FromStartDate = fromStartDate,
                SyncToken = calendarSubscription.LastSyncToken,
                ItemsPerPage = MaxItemsPerPage,
                ExpandRecurringTasks = false,
                IncludeDeleted = true
            });

        if (!eventsResult.Items.Any())
        {
            Log.Information("No events found for calendar subscription {CalendarSubscriptionId}", calendarSubscription.Id);
            return;
        }
        
        Log.Information("{eventCount} Google event found starting: {fromStartDate}", 
            eventsResult.Items.Length,
            fromStartDate);

        var events = await AssignTaskIdToEvents(eventsResult.Items,
            connectedApp,
            (SimplePerson)person,
            connectedApp.ProviderId.Value,
            calendarSubscription,
            calendar);

        var personalSettings = await personRepository.GetPersonalSettings(person.Id);

        foreach (var items in events.ToBatches(BatchSize))
        {
            await Sync(items, connectedApp, calendarSubscription, (SimplePerson)person, personalSettings);
            await unitOfWork.SaveUnitOfWork();
            await integrationEventPublisher.TryDispatch();
        }

        if (!string.IsNullOrWhiteSpace(eventsResult.NextSyncToken))
        {
            calendarSubscription.LastSyncToken = eventsResult.NextSyncToken;
            await calendarSubscriptionRepository.SaveCalendarSubscription(calendarSubscription);
            await unitOfWork.SaveUnitOfWork();
        }
    }

    private async Task Sync(ExternalTask[] tasks,
        ConnectedApp connectedApp,
        CalendarSubscription calendarSubscription,
        SimplePerson person,
        PersonalSettings personalSettings)
    {
        List<ExternalTask> externalEvents = new();
        List<ExternalTask> carepatronEvents = new();

        foreach (var task in tasks)
        {
            if (task.Type == TaskType.External)
                externalEvents.Add(task);
            else
                carepatronEvents.Add(task);
        }

        List<SaveTaskModel> tasksToCreate = new();
        List<Guid> tasksToDelete = new();
        List<SaveTaskModel> tasksToUpdate = new();

        if (externalEvents.Any())
        {
            var externalEventsData = await syncGoogleExternalEventsProcessor.GetData(externalEvents, connectedApp, calendarSubscription, person, personalSettings);
            tasksToCreate = externalEventsData.TasksToCreate;
            tasksToDelete = externalEventsData.TasksToDelete;
            tasksToUpdate = externalEventsData.TasksToUpdate;

            if (externalEventsData.ExternalContactsToAdd.Any())
                await contactRepository.BulkCreateExternalContacts(externalEventsData.ExternalContactsToAdd.ToArray());

            foreach (var externalContactMap in externalEventsData.ExternalContactsToUpdate)
                await contactRepository.LinkExternalContact(externalContactMap.Key, externalContactMap.Value);
        }

        if (carepatronEvents.Any())
        {
            if (calendarSubscription.IsInitialSync())
            {
                // we want to delete events in google calendar that are not present in Carepatron
                await syncGoogleInternalEventsProcessor.PublishEventsToBeDeleted(carepatronEvents, connectedApp);
            }
            else
            {
                // ⛔ Warning: We don't want to process internal events if it's INITIAL SYNC as it might contain outdated data.
                var (toCreate, toUpdate) = await syncGoogleInternalEventsProcessor.GetData(carepatronEvents, connectedApp, calendarSubscription, person, personalSettings);
                tasksToCreate.AddRange(toCreate);
                tasksToUpdate.AddRange(toUpdate);
            }
        }

        if (tasksToCreate.Any())
        {
            await taskRepository.BulkCreate(tasksToCreate.ToArray());
        }

        if (tasksToDelete.Any())
        {
            await taskRepository.BulkDelete(tasksToDelete.ToArray());
        }

        foreach (var task in tasksToUpdate)
        {
            await taskRepository.Save(task);
        }
    }

    private async Task<IEnumerable<ExternalTask>> AssignTaskIdToEvents(IEnumerable<ExternalTask> tasks,
        ConnectedApp connectedApp,
        SimplePerson person,
        Guid providerId,
        CalendarSubscription calendarSubscription,
        CalendarResult externalCalendar)
    {
        // Assign task id to events that don't have one. 
        // This is necessary for syncing events between Carepatron and Google Calendar.
        List<ExternalTask> result = new();
        List<ExternalTask> tasksToUpdate = new();
        foreach (var task in tasks)
        {
            if (task.Type == TaskType.External || task.Metadata?.TaskId.HasValue == true || !Guid.TryParse(task.ICalUID, out var icalUid))
                result.Add(task);
            else tasksToUpdate.Add(task);
        }

        if (tasksToUpdate.Any())
        {
            var updatedExternalTasks = await connectedCalendarService.Link(connectedApp,
                person,
                providerId,
                calendarSubscription.CalendarId,
                tasksToUpdate.Select(i => (Guid.Parse(i.ICalUID), i.ExternalId)),
                false,
                externalCalendar.TimeZone,
                true);

            result.AddRange(updatedExternalTasks.Values);
        }

        return result;
    }
}
