using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using carepatron.core.Application.Calendar.Models;
using carepatron.core.Application.ConnectedApps.Models;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Users.Models;
using carepatron.core.Extensions;
using carepatron.core.Models.Common;
using carepatron.core.Models.User;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Utilities;
using Serilog;

namespace carepatron.core.Application.Calendar.EventHandlers;

public class SyncGoogleExternalEventsProcessor(ITaskRepository taskRepository, IContactRepository contactRepository)
{
    public async Task<SaveGoogleExternalEvents> GetData(IEnumerable<ExternalTask> externalTasks,
        ConnectedApp connectedApp,
        CalendarSubscription calendarSubscription,
        SimplePerson person,
        PersonalSettings personalSettings)
    {
        var providerId = connectedApp.ProviderId.Value;
        var existingTasks = await taskRepository.GetExternalTasks(providerId, person.Id, calendarSubscription.CalendarId);

        var allEmailsFromAttendees = externalTasks.SelectMany(t => t.ExternalContacts.Select(c => c.Email)).Distinct().ToArray();

        var existingExternalContacts = await contactRepository.GetExternalContacts(providerId, allEmailsFromAttendees);
        var existingContactsByEmail = existingExternalContacts.GroupBy(x => x.Email).ToDictionary(x => x.Key, x => x.First());
        var newExternalContactsByEmail = GetNewExternalContacts(providerId, externalTasks, existingContactsByEmail);
        
        // Check if there are any existing external contacts that do not have a ContactId linked to them.
        Dictionary<Guid,Guid> externalContactsToUpdate = new(); // key of ExternalContact.Id, value of Contact.Id
        var externalContactWithoutLink = existingExternalContacts.Where(x => !x.ContactId.HasValue).ToList();

        if (externalContactWithoutLink.Any() || newExternalContactsByEmail.Keys.Any())
        {
            Email[] emailsToFind = externalContactWithoutLink
                .Select(x => x.Email)
                .Concat(newExternalContactsByEmail.Keys)
                .Distinct()
                .ToArray();

            var contactEmailsMaps = await GetExistingContactEmailsMap(providerId, emailsToFind);

            // check if we can link existing external contacts to existing contacts
            foreach (var externalContact in externalContactWithoutLink)
            {
                var contactEmailsMap = contactEmailsMaps.FirstOrDefault(kvp => kvp.Value.Contains(externalContact.Email));
                if (contactEmailsMap.Value is null) continue;

                // re-use existing external contact and link it to the existing contact
                externalContactsToUpdate.Add(externalContact.Id, contactEmailsMap.Key);

                // remove if found in new external contacts as we're re-using the existing external contact
                newExternalContactsByEmail.Remove(externalContact.Email);
            }

            // check if we can link new external contacts to existing contacts
            if (newExternalContactsByEmail.Keys.Any())
            {
                foreach (var contactEmailsMap in contactEmailsMaps)
                {
                    var email = newExternalContactsByEmail.Keys.FirstOrDefault(e => contactEmailsMap.Value.Contains(e));
                    if (email is null) continue;

                    if (newExternalContactsByEmail.TryGetValue(email, out var externalContact) && !externalContact.ContactId.HasValue)
                        externalContact.ContactId = contactEmailsMap.Key;
                }
            }
        }

        Dictionary<string, TaskModel> existingTasksByExternalId = new();
        Dictionary<Guid, TaskModel> existingTasksById = new();
        var userTimeZone = personalSettings?.TimeZone;

        foreach (var task in existingTasks)
        {
            if (string.IsNullOrEmpty(task.ExternalId))
            {
                if (existingTasksById.ContainsKey(task.Id))
                    continue;
                existingTasksById.Add(task.Id, task);
            }
            else
            {
                if (existingTasksByExternalId.ContainsKey(task.ExternalId))
                    continue;
                existingTasksByExternalId.Add(task.ExternalId, task);
            }
        }

        /*
            It's possible that an exception instance and its parent recurring event are yet to be created.
            In this case, we need to keep track of the recurring event so that we can update its ex date when the exception instance is created.
        */
        Dictionary<string, SaveTaskModel> tasksToCreate = new();
        List<Guid> tasksToDelete = new();
        Dictionary<Guid, SaveTaskModel> tasksToUpdate = new();

        Dictionary<string, IEnumerable<ExternalTask>> exceptionInstances = new();
        foreach (var task in externalTasks)
        {
            if (!TryGetParentExternalId(task, out var key)) continue;

            if (!exceptionInstances.ContainsKey(key))
                exceptionInstances.Add(key, Array.Empty<ExternalTask>());

            exceptionInstances[key] = exceptionInstances[key].Append(task);
        }

        foreach (var externalTask in externalTasks.OrderBy(x => x.ExternalStatus == "cancelled"))
        {
            var existingTask = FindExistingTask(externalTask, existingTasksByExternalId, existingTasksById);

            Log.ForContext("ExternalTask", new
                {
                    externalTask.Id,
                    externalTask.ExternalId,
                    externalTask.ExternalStatus,
                    externalTask.ParentExternalId,
                    externalTask.StartDate,
                    externalTask.OriginalStartDate,
                    externalTask.AllDay,
                    externalTask.TimeZone,
                    externalTask.RRule,
                    externalTask.ExDate,
                    ExistingTaskId = existingTask?.Id,
                    UserTimeZone = userTimeZone
                }, true)
                .Information($"{nameof(SyncGoogleExternalEventsProcessor)} - Processing external task");

            if (existingTask is not null)
            {
                if (externalTask.ExternalStatus == "cancelled")
                {
                    tasksToDelete.Add(existingTask.Id);
                }
                else
                {
                    var taskToUpdate = BuildTaskToUpdate(externalTask,
                        existingTask,
                        exceptionInstances,
                        existingContactsByEmail,
                        newExternalContactsByEmail,
                        providerId,
                        person);

                    if (string.IsNullOrEmpty(existingTask.ExternalId))
                    {
                        // we need to update the External Id of this task so it's easier to find next time
                        taskToUpdate.ExternalId = externalTask.ExternalId;
                        if (tasksToUpdate.ContainsKey(taskToUpdate.Id))
                            tasksToUpdate[taskToUpdate.Id].ExternalId = externalTask.ExternalId;
                        else
                            tasksToUpdate.Add(taskToUpdate.Id, taskToUpdate);
                    }
                    else if (!taskToUpdate.SameAs(existingTask))
                    {
                        if (tasksToUpdate.ContainsKey(taskToUpdate.Id))
                        {
                            var combinedExDates = $"{tasksToUpdate[taskToUpdate.Id].ExDate},{taskToUpdate.ExDate}";
                            taskToUpdate.ExDate = CalendarUtilities.CleanUpExDates(combinedExDates);
                            tasksToUpdate[taskToUpdate.Id] = taskToUpdate;
                        }
                        else
                        {
                            tasksToUpdate.Add(taskToUpdate.Id, taskToUpdate);
                        }
                    }
                }

                continue;
            }

            // since this cancelled event is not in our database, we should just ignore it if it's not a child of a recurring event
            if (externalTask.ExternalStatus == "cancelled")
            {
                if (string.IsNullOrEmpty(externalTask.ParentExternalId)) continue;

                UpdateParentExDateForCancelledException(externalTask, tasksToCreate, tasksToUpdate, existingTasksByExternalId);
                continue;
            }

            // this is a single or recurring event
            if (string.IsNullOrWhiteSpace(externalTask.ParentExternalId))
            {
                var newTask = BuildTask(externalTask, providerId, person.Id, calendarSubscription.CalendarId, userTimeZone, existingContactsByEmail, newExternalContactsByEmail);
                tasksToCreate.Add(newTask.Key, newTask.Value);
                continue;
            }

            // this is a child (exception) of a recurring event

            Guid? parentId = null;

            var startDate = externalTask.OriginalStartDate ?? externalTask.StartDate;

            // Here we want to make sure we set its parent task's ex date correctly.
            // If it's INITIAL SYNC, it's likely that the parent task is not yet created and still in the tasksToCreate dictionary.
            if (tasksToCreate.TryGetValue(externalTask.ParentExternalId, out var parentEvent))
            {
                Log.ForContext("ParentExternalTask", new
                    {
                        parentEvent.Id,
                        parentEvent.ExternalId,
                        parentEvent.StartDate,
                        parentEvent.TimeZone,
                        parentEvent.ExDate
                    }, true)
                    .Information($"{nameof(SyncGoogleExternalEventsProcessor)} - Processing parent event from tasks to create");

                parentId = parentEvent.Id;
                parentEvent.ExDate = GetNewExDate(parentEvent, externalTask);
            }
            // if it's in the existing tasks, it only means that the parent task is already created from prior syncs.
            else if (existingTasksByExternalId.TryGetValue(externalTask.ParentExternalId, out var existingParentTask))
            {
                parentId = existingParentTask.Id;

                // this parent can also be in the process of being updated and is already in the tasksToUpdate dictionary.
                if (tasksToUpdate.TryGetValue(existingParentTask.Id, out var parentModelToUpdate))
                {
                    Log.ForContext("ParentExternalTask", new
                        {
                            parentModelToUpdate.Id,
                            parentModelToUpdate.ExternalId,
                            parentModelToUpdate.StartDate,
                            parentModelToUpdate.TimeZone,
                            parentModelToUpdate.ExDate
                        }, true)
                        .Information($"{nameof(SyncGoogleExternalEventsProcessor)} - Processing parent event from tasks to update");

                    parentModelToUpdate.ExDate = GetNewExDate(parentModelToUpdate, externalTask);
                }
                else
                {
                    Log.ForContext("ParentExternalTask", new
                        {
                            existingParentTask.Id,
                            existingParentTask.ExternalId,
                            existingParentTask.StartDate,
                            existingParentTask.TimeZone,
                            existingParentTask.ExDate
                        }, true)
                        .Information($"{nameof(SyncGoogleExternalEventsProcessor)} - Processing parent event from existing tasks");

                    if (externalTask.AllDay && DateTime.TryParse(externalTask.OriginalAllDayStartDate, out var parsedDate))
                        startDate = parsedDate.ToUniversalTime(existingParentTask.TimeZone);

                    var exDate = CalendarUtilities.CalculateExDate(existingParentTask.TimeZone, existingParentTask.StartDate, startDate);
                    var formattedExDate = exDate.ToExDateFormat(externalTask.AllDay);

                    var parentModel = existingParentTask.ToSaveTaskModel(providerId, person.Id);
                    parentModel.ExDate = CalendarUtilities.CleanUpExDates(string.IsNullOrWhiteSpace(existingParentTask.ExDate) ? formattedExDate : $"{existingParentTask.ExDate},{formattedExDate}");
                    if (tasksToUpdate.ContainsKey(parentModel.Id))
                    {
                        var combinedExDates = $"{tasksToUpdate[parentModel.Id].ExDate},{parentModel.ExDate}";
                        parentModel.ExDate = CalendarUtilities.CleanUpExDates(combinedExDates);
                        tasksToUpdate[parentModel.Id] = parentModel;
                    }
                    else
                    {
                        tasksToUpdate.Add(parentModel.Id, parentModel);
                    }
                }
            }

            var childModel = externalTask.ToSaveTaskModel(providerId, person.Id, true);
            childModel.Id = Guid.NewGuid();
            childModel.ExternalCalendarId = calendarSubscription.CalendarId;
            childModel.ParentId = parentId;
            childModel.TimeZone = childModel.TimeZone ?? userTimeZone;
            childModel.AdjustDatesForAllDay(childModel.TimeZone);
            childModel.ExternalContacts = GetTaskExternalContacts(childModel, existingContactsByEmail, newExternalContactsByEmail);
            tasksToCreate.Add(externalTask.ExternalId, childModel);
        }

        return new(tasksToCreate.Values.ToList(),
            tasksToDelete,
            tasksToUpdate.Values.ToList(),
            newExternalContactsByEmail.Values.ToList(),
            externalContactsToUpdate);
    }

    private SaveTaskModel BuildTaskToUpdate(ExternalTask externalTask,
        TaskModel existingTask,
        Dictionary<string, IEnumerable<ExternalTask>> allExceptionInstances,
        Dictionary<Email, ExternalContact> existingContactsByEmail, 
        Dictionary<Email, ExternalContact> newContactsByEmail,
        Guid providerId,
        SimplePerson person)
    {
        var model = existingTask.ToSaveTaskModel(providerId, person.Id);
        model = model.MapFromExternal(externalTask, person.Id);

        if (externalTask.AllDay)
            model.TimeZone = existingTask.TimeZone;

        model.AdjustDatesForAllDay(model.TimeZone);

        if (!string.IsNullOrWhiteSpace(externalTask.RRule))
        {
            IEnumerable<ExternalTask> exceptionInstances = Array.Empty<ExternalTask>();
            if (allExceptionInstances.TryGetValue(externalTask.ExternalId, out var instances))
                exceptionInstances = instances;

            var newExDate = string.Empty;
            foreach (var instance in exceptionInstances)
            {
                var instanceStartDate = instance.OriginalStartDate ?? instance.StartDate;
                if (instance.AllDay && DateTime.TryParse(instance.OriginalAllDayStartDate, out var parsedInstanceStartDate))
                    instanceStartDate = parsedInstanceStartDate.ToUniversalTime(model.TimeZone);

                var exDate = CalendarUtilities.CalculateExDate(model.TimeZone, model.StartDate, instanceStartDate);
                var formattedExDate = exDate.ToExDateFormat(instance.AllDay);
                newExDate = string.IsNullOrWhiteSpace(newExDate) ? formattedExDate : $"{newExDate},{formattedExDate}";
            }

            model.ExDate = CalendarUtilities.CleanUpExDates(string.IsNullOrEmpty(existingTask.ExDate) ? newExDate : $"{existingTask.ExDate},{newExDate}");
        }

        model.ExternalContacts = GetTaskExternalContacts(model, existingContactsByEmail, newContactsByEmail);

        return model;
    }

    private KeyValuePair<string, SaveTaskModel> BuildTask(ExternalTask externalTask, Guid providerId, Guid personId, string calendarId, string userTimeZone,
        Dictionary<Email, ExternalContact> existingContactsByEmail, 
        Dictionary<Email, ExternalContact> newContactsByEmail)
    {
        var model = externalTask.ToSaveTaskModel(providerId, personId, true);
        model.Id = Guid.NewGuid();
        model.ExternalCalendarId = calendarId;
        model.TimeZone = model.TimeZone ?? userTimeZone;
        model.AdjustDatesForAllDay(model.TimeZone);
        model.ExternalContacts = GetTaskExternalContacts(model, existingContactsByEmail, newContactsByEmail);
        return new KeyValuePair<string, SaveTaskModel>(externalTask.ExternalId, model);
    }

    // Only used for cancelled exception instances that are not in our database
    private void UpdateParentExDateForCancelledException(ExternalTask externalTask,
        Dictionary<string, SaveTaskModel> tasksToCreate,
        Dictionary<Guid, SaveTaskModel> tasksToUpdate,
        Dictionary<string, TaskModel> existingTasksByExternalId)
    {
        SaveTaskModel parentSaveModel = null;
        if (tasksToCreate.ContainsKey(externalTask.ParentExternalId))
        {
            parentSaveModel = tasksToCreate[externalTask.ParentExternalId];
        }
        else
        {
            if (existingTasksByExternalId.TryGetValue(externalTask.ParentExternalId, out var existingParentTask)
                && tasksToUpdate.ContainsKey(existingParentTask.Id))
            {
                parentSaveModel = tasksToUpdate[existingParentTask.Id];
            }
        }

        if (parentSaveModel is null) return;

        parentSaveModel.ExDate = GetNewExDate(parentSaveModel, externalTask);
    }

    private bool TryGetParentExternalId(ExternalTask externalTask, out string parentExternalId)
    {
        if (!string.IsNullOrWhiteSpace(externalTask.ParentExternalId))
        {
            parentExternalId = externalTask.ParentExternalId;
            return true;
        }

        var idParts = externalTask.ExternalId.Split("_");
        parentExternalId = idParts[0];

        return idParts.Length > 1 && !string.IsNullOrWhiteSpace(parentExternalId);
    }

    private TaskModel FindExistingTask(ExternalTask externalTask, Dictionary<string, TaskModel> existingTasksByExternalId, Dictionary<Guid, TaskModel> existingTasksById)
    {
        TaskModel existingTask = null;

        // try to check if we already have a copy of this event in our database by checking ExternalId
        if (existingTasksByExternalId.ContainsKey(externalTask.ExternalId))
            existingTask = existingTasksByExternalId[externalTask.ExternalId];

        // we can also check this event's Id (Google ICalUid) matches any of our tasks
        // this task, while external, could be from our system that was caused by updating an instance of an external recurring event ("This" or "This and following")
        // if that's the case then its Id (Google ICalUid) should be a Task Id
        if (existingTask is null && existingTasksById.ContainsKey(externalTask.Id))
            existingTask = existingTasksById[externalTask.Id];

        return existingTask;
    }

    private string GetNewExDate(SaveTaskModel parentSaveModel, ExternalTask exceptionTask)
    {
        var startDate = exceptionTask.OriginalStartDate ?? exceptionTask.StartDate;
        if (exceptionTask.AllDay && DateTime.TryParse(exceptionTask.OriginalAllDayStartDate, out var parsedDate))
            startDate = parsedDate.ToUniversalTime(parentSaveModel.TimeZone);

        var exDate = CalendarUtilities.CalculateExDate(parentSaveModel.TimeZone, parentSaveModel.StartDate, startDate);
        var formattedExDate = exDate.ToExDateFormat(exceptionTask.AllDay);
        return CalendarUtilities.CleanUpExDates(string.IsNullOrWhiteSpace(parentSaveModel.ExDate)
            ? formattedExDate
            : $"{parentSaveModel.ExDate},{formattedExDate}");
    }

    private Dictionary<Email, ExternalContact> GetNewExternalContacts(Guid providerId, IEnumerable<ExternalTask> externalTasks, Dictionary<Email, ExternalContact> existingContactDictionary)
    {
        var newEmails = externalTasks
            .SelectMany(x => x.ExternalContacts.Select(c => c.Email))
            .Distinct();

        if (existingContactDictionary.Any())
            newEmails = newEmails.Where(email => !existingContactDictionary.ContainsKey(email)).ToArray();

        var emailsToAdd = newEmails.Select(email => new ExternalContact
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            Email = email
        });

        return emailsToAdd.ToDictionary(x => x.Email, x => x);
    }

    private async Task<Dictionary<Guid, Email[]>> GetExistingContactEmailsMap(Guid providerId, Email[] emails)
    {
        var contacts = await contactRepository.GetClientsByEmails(providerId, emails, true);
        Dictionary<Guid, Email[]> result = new();
        foreach (var contact in contacts)
        {
            List<Email> contactEmails = [contact.Email, ..contact.Emails.Select(x => x.Email).ToArray()];
            result.Add(contact.Id, contactEmails.Distinct().ToArray());
        }

        return result;
    }

    private TaskExternalContact[] GetTaskExternalContacts(SaveTaskModel model,
        Dictionary<Email, ExternalContact> existingContactsByEmail,
        Dictionary<Email, ExternalContact> newContactsByEmail)
    {
        var externalContacts = model.ExternalContacts
            .Select(externalContact =>
            {
                if (existingContactsByEmail.TryGetValue(externalContact.Email, out var existingContact))
                {
                    externalContact.TaskId = model.Id;
                    externalContact.ExternalContactId = existingContact.Id;
                    return externalContact;
                }

                if (newContactsByEmail.TryGetValue(externalContact.Email, out var newContact))
                {
                    externalContact.TaskId = model.Id;
                    externalContact.ExternalContactId = newContact.Id;
                    return externalContact;
                }

                return null;
            })
            .Where(x => x is not null);

        return externalContacts
            .GroupBy(x => new { x.TaskId, x.ExternalContactId })
            .Select(x => x.First())
            .ToArray();
    }
}