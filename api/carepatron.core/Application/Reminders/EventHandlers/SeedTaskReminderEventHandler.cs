﻿using carepatron.core.Abstractions;
using carepatron.core.Application.Reminders.Models;
using carepatron.core.Application.Reminders.Services;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Common;
using carepatron.core.Constants;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Events.Handlers;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Models.Reminders;
using carepatron.core.Repositories.Provider;
using carepatron.core.Repositories.Reminders;
using carepatron.core.Repositories.SQS;
using carepatron.core.Repositories.Tasks;
using Serilog;
using Serilog.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using carepatron.core.Application.Workspace.Providers.Models;

namespace carepatron.core.Application.Reminders.EventHandlers
{
    public class SeedTaskReminderEventHandler : IEventHandler<EventData<SeedTaskRemindersEventData>>
    {
        private const int RecurrenceSearchSpanDays = 40;
        private const int BatchSize = 100;

        private readonly IProviderRepository providerRepository;
        private readonly ITaskRepository taskRepository;
        private readonly IReminderRepository reminderRepository;
        private readonly IReminderJobRepository reminderJobRepository;
        private readonly IReminderService reminderService;
        private readonly ISqsRepository sqsRepository;
        private readonly IDateTimeProvider dateTimeProvider;
        private readonly IUnitOfWork unitOfWork;
        private readonly ILogger logger;

        public SeedTaskReminderEventHandler(IProviderRepository providerRepository,
            ITaskRepository taskRepository,
            IReminderRepository reminderRepository,
            IReminderJobRepository reminderJobRepository,
            IReminderService reminderService,
            ISqsRepository sqsRepository,
            IDateTimeProvider dateTimeProvider,
            IUnitOfWork unitOfWork,
            ILogger logger)
        {
            this.providerRepository = providerRepository;
            this.taskRepository = taskRepository;
            this.reminderRepository = reminderRepository;
            this.reminderJobRepository = reminderJobRepository;
            this.reminderService = reminderService;
            this.sqsRepository = sqsRepository;
            this.dateTimeProvider = dateTimeProvider;
            this.unitOfWork = unitOfWork;
            this.logger = logger;
        }

        public EventType EventType => EventType.SeedTaskReminders;

        public async Task Handle(EventData<SeedTaskRemindersEventData> evt)
        {
            using (LogContext.PushProperty("SeedTaskReminderEventHandlerDebugDetails", new
                   {
                       ProviderIds = string.Join(",", evt.Data?.ProviderIds ?? []),
                       evt.Data?.LastProviderId,
                       evt.Data?.Errors,
                       evt.Data?.TotalProcessed,
                   }))
            {
                logger.Information("Starting task reminder handler");
                LogContext.PushProperty(nameof(CodeOwner), CodeOwner.TasksAndScheduling);
                var data = evt?.Data;
                var errors = data?.Errors ?? new();
                var totalProcessed = data?.TotalProcessed ?? 0;
                var toBeProcessProviderIds = (data?.ProviderIds ?? []).Order().ToArray(); 

                if (data?.HasProcessExceeded ?? false)
                    return;

                Provider[] providers;

                if (toBeProcessProviderIds.Any())
                {
                    int providerIdIndex = 0;

                    if (data?.LastProviderId.HasValue ?? false)
                        providerIdIndex = Array.IndexOf(toBeProcessProviderIds, data.LastProviderId.Value);

                    // exit if cannot find the lastProviderId or it found the lastProviderId
                    if (providerIdIndex == -1 || providerIdIndex == toBeProcessProviderIds.Length - 1)
                    {
                        logger.Information("Last provider found, exiting processing task reminder handler");
                        return;
                    }
                        
                    
                    // get array of provider ids starting from providerIdIndex up to the end then only take the batchSize
                    var batchProviderIds = toBeProcessProviderIds[providerIdIndex..].Take(BatchSize).ToArray();
                    providers = await providerRepository.GetProviders(batchProviderIds.ToArray());
                }
                else 
                    providers = await providerRepository.GetProviders(data?.LastProviderId, BatchSize);

                // exit when no providers left
                if (providers.Length <= 0) return;

                var providerIds = errors.Keys
                    .Concat(providers.Select(x => x.Id))
                    .Order()
                    .ToArray();
                var futureTasks = await taskRepository.GetFutureTasks(providerIds, TaskType.ClientEvent);

                // group tasks by providers
                var providerTasks = futureTasks
                    .GroupBy(x => x.ProviderId);

                unitOfWork.UseUnitOfWork();
                foreach (var providerTask in providerTasks)
                {
                    // process tasks by each provider so there will be one transaction per provider.
                    // when transaction fails, add provider to error list.
                    
                    var providerId = providerTask.Key;
                    logger.Information("Processing provider: {id}", providerId);
                    
                    var tasks = providerTask.ToArray();
                    logger.Information("Processing {taskCount} tasks for provider: {id}", tasks.Length, providerId);
                    
                    if (tasks.Length <= 0) continue;

                    var reminderSettings = await reminderRepository.GetByProviderId(providerId, ReminderEntityType.Task);
                    logger.Information("{reminderSettingsCount} reminder settings found for provider: {id}", reminderSettings.Count, providerId);
                    
                    if (reminderSettings.Count <= 0) continue;

                    using (LogContext.PushProperty("Provider Task", providerId))
                    {
                        try
                        {
                            var reminderSettingsIds = reminderSettings.Select(x => x.Id).ToArray();
                            await reminderService.DeleteReminderJobsByReminderSettingIds(reminderSettingsIds);

                            var currentDateUtc = dateTimeProvider.GetDateTimeUtc();
                            int remindersCreated = 0;
                            foreach (var task in tasks)
                            {
                                foreach (var reminderSetting in reminderSettings)
                                {
                                    var reminderJob = reminderService.BuildNextReminderJob(task, reminderSetting, currentDateUtc);
                                    if (reminderJob is null) continue;

                                    remindersCreated++;
                                    await reminderJobRepository.CreateJob(reminderJob);
                                }
                            }

                            await unitOfWork.SaveUnitOfWork(default);
                            Log.ForContext("Number of Tasks", tasks.Length)
                                .ForContext("Reminder settings", reminderSettings.Count)
                                .ForContext("Reminders created", remindersCreated)
                                .Information("Finished processing task reminders");
                        }
                        catch (Exception ex)
                        {
                            HandleErrorCount(ex, errors, providerId);
                        }
                    }
                }
                
                logger.Information("Finished processing task reminders all providers for this batch");
                var lastProviderId = providerIds[providerIds.Length - 1];
                SeedTaskRemindersEventData seedTaskReminderJob = new(lastProviderId, errors, totalProcessed + BatchSize, data?.MaxToProcess, data?.ProviderIds ?? []);
                logger.Information("Queuing up next batch of task reminders: LastProviderId: {lastProviderId}");
                
                await sqsRepository.SendMessage(QueueType.Task,
                    new EventMessage(EventType.SeedTaskReminders, new EventData<SeedTaskRemindersEventData>(seedTaskReminderJob), Guid.NewGuid().ToString(), null));
                    
            }
        }

        public void HandleErrorCount(Exception ex, Dictionary<Guid, int> providerErrors, Guid providerId)
        {
            if (providerErrors.TryGetValue(providerId, out int retryCount))
            {
                if (providerErrors[providerId] >= 3)
                    providerErrors.Remove(providerId);
                else providerErrors[providerId] += 1;
            }
            else providerErrors.Add(providerId, retryCount + 1);

            Log.Warning(ex, $"Failed to process providerId: {providerId}; Retry count: {retryCount}");
        }
    }
}
