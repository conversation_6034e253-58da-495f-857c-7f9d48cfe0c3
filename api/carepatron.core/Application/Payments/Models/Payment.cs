using System;
using System.Collections.Generic;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Payments.Utils;
using carepatron.core.Application.Trash.Models;
using carepatron.core.Models.Invoices;

namespace carepatron.core.Application.Payments.Models;

public class Payment : ITrash
{
    public Guid Id { get; set; }

    public Guid ProviderId { get; set; }

    public Guid ContactId { get; set; }

    public Guid? InvoiceId { get; set; }

    public DateTime PaymentDate { get; set; }

    public string Type { get; set; }

    public PayerType PayerType { get; set; }

    /// <summary>
    /// WARNING: The representation of the Amount is actually as a whole number
    /// NOT a decimal value.
    /// eg: $120.24 is represented as "12024"
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Amount charged to the customer
    /// </summary>
    public decimal ChargeAmount { get; set; }

    /// <summary>
    /// Amount transferred to the provider
    /// </summary>
    public decimal TransferAmount { get; set; }

    /// <summary>
    /// <PERSON>e's processing fee
    /// </summary>
    public decimal Fee { get; set; }

    /// <summary>
    /// Indicates if the fee is added to the amount due and charged to the customer
    /// </summary>
    public bool IsClientChargedFee { get; set; }

    /// <summary>
    /// Currency of payment
    /// </summary>
    public string CurrencyCode { get; set; }

    /// <summary>
    /// Provider of payment: Manual / Stripe
    /// </summary>
    public string PaymentProvider { get; set; }

    public DateTime? PayoutDateUtc { get; set; }

    public PayoutStatus? PayoutStatus { get; set; }

    /// <summary>
    /// WARNING: This PayoutAmount IS represented as a decimal, NOT as a whole number like above.
    /// eg: $120.24 is represented as "120.24"
    /// </summary>
    public decimal? PayoutAmount { get; set; }

    public string PayoutCurrencyCode { get; set; }

    public Guid? PaymentIntentId { get; set; }

    public Guid? InsurancePayerId { get; set; }

    public string PayerName { get; set; }

    public string Reference { get; set; }

    [Obsolete("To be removed in the future")]
    public bool? IsBillingV2 { get; set; }
    public PaymentRefundStatus? RefundStatus { get; set; }
    public IEnumerable<PaymentAllocation> Allocations { get; set; }
    public IEnumerable<RefundReference> Refunds { get; set; }
    public IEnumerable<ContactCreditAdjustment> IssuedCredits { get; set; }

    public decimal AvailableToAllocate => ChargeAmount - (IsClientChargedFee ? Fee : 0);
    
    public decimal UnallocatedAmount { get; set; }

    /// <summary>
    /// Determines if the payment can be soft deleted.
    /// </summary>
    public bool CanDelete => CanDeletePaymentPolicy.Execute(this).CanDelete;

    public TrashItem GetTrashItem()
    {
        return new TrashItem
        {
            Id = Guid.NewGuid(),
            ProviderId = ProviderId,
            Name = Reference,
            Type = TrashType.Payment,
            EntityId = Id,
            FromContact = new SimpleContact
            {
                Id = ContactId,
            },
            Metadata = new PaymentTrashItemMetadata(PaymentDate, AvailableToAllocate, currencyCode: CurrencyCode, type: Type),
            DeletedDateTimeUtc = DateTime.UtcNow
        };
    }
}
