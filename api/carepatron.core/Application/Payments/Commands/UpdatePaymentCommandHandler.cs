using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Common;
using carepatron.core.Constants;
using carepatron.core.Extensions;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Billables;
using carepatron.core.Repositories.Payments;
using FluentValidation;

namespace carepatron.core.Application.Payments.Commands;

public record UpdatePaymentCommand(
    Guid Id,
    bool IsBillingV2,
    decimal Amount,
    string PayerName,
    string Reference,
    DateTime PaymentDate,
    IEnumerable<PaymentAllocationDetail> Allocations
) : IMediatrCommand<PaymentDetail>;

public class UpdatePaymentCommandValidator : AbstractValidator<UpdatePaymentCommand>
{
    public UpdatePaymentCommandValidator()
    {
        RuleFor(x => x.Id).NotEmpty();
    }
}

public class UpdatePaymentCommandHandler(IPaymentRepository paymentRepository,
    IStripePaymentsRepository stripePaymentsRepository,
    IBillableRepository billableRepository,
    IMapper mapper) : IMediatrCommandHandler<UpdatePaymentCommand, PaymentDetail>
{
    public async Task<ExecutionResult<PaymentDetail>> Handle(UpdatePaymentCommand request, CancellationToken cancellationToken)
    {

        var payment = await paymentRepository.GetPayment(request.Id);
        if (payment == null)
        {
            return ValidationError.NotFound;
        }
        if (payment.IsBillingV2 != true)
        {
            throw new NotSupportedException();
        }

        var availableToAllocate = GetAvailableToAllocate(payment, request);
        var allocationsTotal = request.Allocations.EmptyIfNull().Sum(x => x.Allocated);

        if (availableToAllocate < 0 && allocationsTotal < availableToAllocate)
        {
            return new ValidationError(Errors.InvalidAllocationAmountCode, Errors.InvalidAllocationAmountDetail, ValidationType.BadRequest);
        }
        if (availableToAllocate > 0 && allocationsTotal > availableToAllocate)
        {
            return new ValidationError(Errors.InvalidAllocationAmountCode, Errors.InvalidAllocationAmountDetail, ValidationType.BadRequest);
        }

        var affectedInvoiceIds = request.Allocations.EmptyIfNull()
            .Where(x => x.Invoice?.InvoiceId != null)
            .Select(x => x.Invoice.InvoiceId)
            .ToArray();
        var removedPaymentIntents = await paymentRepository.DeletePaymentIntentsByInvoiceId(affectedInvoiceIds,
            [PaymentIntentStatus.Open, PaymentIntentStatus.Canceled, PaymentIntentStatus.Failed, PaymentIntentStatus.Unknown]);

        foreach (var removedPaymentIntent in removedPaymentIntents.Where(x => x.Status == PaymentIntentStatus.Open))
        {
            await stripePaymentsRepository.CancelStripePaymentIntent(removedPaymentIntent.StripePaymentIntentId);
        }

        var affectedBillableItems = request.Allocations.Select(x => x.BillableItemId).Concat(payment.Allocations.Select(x => x.BillableItemId)).Distinct().ToArray();

        // Update payment details
        if (payment.PayerType == PayerType.Insurance && payment.PaymentProvider != PaymentProviders.Stripe && payment.PaymentProvider != PaymentProviders.ClaimMD)
        {
            var stripeAmount = CurrencyHandler.Get(payment.CurrencyCode).ToStripeAmount(request.Amount);
            payment.PayerName = request.PayerName;
            payment.PaymentDate = request.PaymentDate;
            payment.Reference = request.Reference;

            if (payment.PaymentProvider != PaymentProviders.Stripe && payment.PaymentProvider != PaymentProviders.ClaimMD)
            {
                payment.Amount = stripeAmount;
                payment.ChargeAmount = request.Amount;
                payment.TransferAmount = request.Amount;
            }
            await paymentRepository.UpdatePaymentDetails(payment);
        }

        // Update payment allocations
        payment.Allocations = request.Allocations.EmptyIfNull().Select(mapper.Map<PaymentAllocation>);
        var result = await paymentRepository.Allocate(payment, cancellationToken);
        await billableRepository.RecalculateBillableByItemIds(affectedBillableItems);
        return result;
    }

    private static decimal GetAvailableToAllocate(Payment payment, UpdatePaymentCommand request)
    {
        if (payment.PaymentProvider == PaymentProviders.Insurance)
            return request.Amount;

        if (payment.PaymentProvider == PaymentProviders.Stripe)
            return payment.AvailableToAllocate;


        //todo could add manual payments in here when payment amounts are set from FE. 

        // by default, the payment amount cannot be changed
        return payment.AvailableToAllocate;
    }
}