using Agents.Module;
using Agents.Module.AgentRunners;
using Agents.Module.Agents;
using Agents.Module.Exceptions;
using Agents.Sdk.Types;
using carepatron.core.Abstractions;
using carepatron.core.Application.FeatureModules.Abstractions;
using carepatron.core.Application.FeatureModules.Models;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Transcriptions.Events;
using carepatron.core.Application.Transcriptions.Models;
using carepatron.core.Common;
using carepatron.core.Constants;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Identity;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Media;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Models.Storage;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Notes;
using carepatron.core.Repositories.SQS;
using carepatron.core.Services;
using carepatron.core.Utilities;
using FileUtilities;
using FluentValidation;
using Google;
using Microsoft.AspNetCore.Http;
using Serilog;
using Serilog.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using TranscriptionType = carepatron.core.Application.Transcriptions.Models.TranscriptionType;

namespace carepatron.core.Application.Transcriptions.Commands;

public record CreateTranscriptionPartsCommand(
    Guid TranscriptionId,
    int? PartNumber,
    IFormFile File,
    Guid ProviderId,
    string StartTime,
    string EndTime,
    IIdentityContext IdentityContext,
    Guid? FileId = null,
    string? UploadId = null,
    bool? isLastPart = null) : IMediatrCommand<List<Transcript>>;


public class CreateTranscriptionPartsCommandValidator : AbstractValidator<CreateTranscriptionPartsCommand>
{
    public CreateTranscriptionPartsCommandValidator()
    {
        RuleFor(x => x.File)
            .Must(x => x != null && x.Length > 0)
            .When(x => x.isLastPart != true);

        RuleFor(x => x.PartNumber)
            .Must(x => x >= 0)
            .When(x => x.isLastPart != true);
    }
}

public class CreateTranscriptionPartsCommandHandler(
    IUnitOfWork unitOfWork,
    ITranscriptionRepository transcriptionRepository,
    INoteTranscriptionRepository noteTranscriptionRepository,
    FileStorageServiceFactory fileStorageServiceFactory,
    IAudioProcessingService audioProcessingService,
    IFeatureHeaderService featureHeaderService,
    ISqsRepository sqsRepository,
    IAgentClient agentClient,
    IFeatureService featureService,
    IFeatureModuleService featureModuleService,
    IDateTimeProvider dateTimeProvider) : IMediatrCommandHandler<CreateTranscriptionPartsCommand, List<Transcript>>
{
    private static TranscriptionType[] LiveTranscriptionTypes = { TranscriptionType.LiveTranscription, TranscriptionType.LiveDictation, TranscriptionType.LiveVideoCallTranscription };

    public async Task<ExecutionResult<List<Transcript>>> Handle(CreateTranscriptionPartsCommand command,
        CancellationToken cancellationToken)
    {
        var transcription = await transcriptionRepository.GetById(command.TranscriptionId);

        if (transcription is null)
        {
            Log.Warning("TranscriptionProcess: Transcription with ID {TranscriptionId} not found.");
            return ValidationError.NotFound;
        }

        var deferTranscriptionCompletion = DeferTranscriptionCompletion();

        using (LogContext.PushProperty("TranscriptionId", command.TranscriptionId))
        using (LogContext.PushProperty("ProviderId", command.IdentityContext.ProviderPermissions.ProviderId))
        using (LogContext.PushProperty("PartNumber", command.PartNumber))
        using (LogContext.PushProperty("PersonId", command.IdentityContext.PersonId))
        using (LogContext.PushProperty("TranscriptionType", transcription.Type))
        using (LogContext.PushProperty("DeferTranscriptionCompletion", deferTranscriptionCompletion))
        {
            var noteTranscriptions = await noteTranscriptionRepository.GetByTranscriptionId(transcription.Id);

            if (noteTranscriptions is { Count: > 0 })
            {
                var noteIds = string.Join(", ", noteTranscriptions.Select(nt => nt.NoteId));
                LogContext.PushProperty("NoteIds", noteIds);
            }

            Log.Information("TranscriptionProcess: Processing transcription {TranscriptionId} with part number {PartNumber}");

            if (transcription.Status is TranscriptionStatus.Pending)
            {
                transcription.Status = TranscriptionStatus.InProgress;
                await transcriptionRepository.Update(transcription);
            }

            // ensure there are no long running transactions
            // the transcription service may take a long time to respond
            await unitOfWork.SaveUnitOfWork();

            // handle multipart uploads and return early
            if (transcription.Type == TranscriptionType.RecordedTranscription)
            {
                await HandleMultiPartUploadPart(transcription.StorageProvider, command, transcription.Path);
                return new List<Transcript>();
            }

            var contentType = command.File.ContentType;

            byte[] audioData;

            var fileFormat = MimeTypeUtility.GetFileFormatFromContentType(contentType);

            using (var stream = command.File.OpenReadStream())
            {
                if (!fileFormat.Equals("mp3", StringComparison.InvariantCultureIgnoreCase))
                {
                    audioData = await audioProcessingService.ConvertAudioStream(stream, fileFormat, "mp3");
                    contentType = "audio/mp3";
                }
                else
                {
                    audioData = new byte[stream.Length];
                    await stream.ReadAsync(audioData, 0, (int)stream.Length, cancellationToken);
                }
            }

            var model = await GetAgentModel(command.ProviderId);

            var transcribeTask = agentClient.Run<TranscriptionAgent.TimestampOutputResponse>(
                new TranscriptionAgent(Agents.Module.Agents.TranscriptionType.RecordedTranscription, GetAgentProvider(), model),
                TranscriptionAgent.TimestampOutputSchema,
                new UserInput(Role.User, new TextUserInputPart(TranscriptionAgent.AudioTranscriptionUserPrompt)),
                new UserInput(Role.User, new InlineDataUserInputPart(Convert.ToBase64String(audioData), "audio/mp3")));

            var fileName = $"{command.TranscriptionId}_{command.PartNumber:D4}";
            var uploadTask = UploadAudioFile(fileName, transcription.Path, audioData, contentType,
                transcription.StorageProvider);

            try
            {
                await Task.WhenAll(uploadTask, transcribeTask);

                var totalTokens = transcribeTask.Result.InputTokens + transcribeTask.Result.OutputTokens;

                await featureModuleService.AddUsage(command.ProviderId, FeatureModuleType.AI_Tokens, totalTokens, dateTimeProvider.GetDateTimeUtc());
            }
            catch (Exception e)
            {
                LogContext.PushProperty("TranscriptionId", transcription.Id);
                LogContext.PushProperty("FileName", fileName);
                Log.Error(e, "TranscriptionProcess: Error processing transcription");

                if (e is GoogleApiException googleApiException && googleApiException.HttpStatusCode == System.Net.HttpStatusCode.ServiceUnavailable)
                {
                    throw new ExecutionException(
                    new ValidationError(
                        Errors.VendorServiceNotAvailableCode,
                        Errors.VendorServiceNotAvailableDetail,
                        ValidationType.ServiceUnavailable));
                }

                throw;
            }

            var transcripts = new List<Transcript>();

            var agentResponse = transcribeTask.Result;

            if (LiveTranscriptionTypes.Contains(transcription.Type))
            {
                if (agentResponse.Error is OutputSchemaException)
                {
                    Log.Warning("TranscriptionProcess: Json schema deserialization failed. Retrying using Json agent");
                    agentResponse = await agentClient.Run<TranscriptionAgent.TimestampOutputResponse>(
                        new TranscriptionJsonQualityAssuranceAgent(model),
                        TranscriptionAgent.TimestampOutputSchema,
                        new UserInput(Role.User, new TextUserInputPart(agentResponse.ResponseText)));
                    Log.Information("TranscriptionProcess: Json agent successfully processed the response");
                }

                if (agentResponse.Output != null)
                {
                    var initialTime = TimeSpan.Parse(command.StartTime);
                    transcripts.AddRange(agentResponse.Output.Transcripts
                        .Select(x =>
                        {
                            var isInaudible = x.IsInaudible || string.IsNullOrWhiteSpace(x.Content) ||
                                              Regex.IsMatch(x.Content, @"^\s*[Pp]*\s*$|^([\p{L}\p{M}]+)(?:\1){2,}$");

                            var transcript = new Transcript
                            {
                                Id = Guid.NewGuid(),
                                Content = isInaudible ? "" : x.Content,
                                Speaker = x.Speaker,
                                StartTime = (initialTime + x.StartTime).ToString(@"hh\:mm\:ss"),
                                EndTime = (initialTime + x.EndTime).ToString(@"hh\:mm\:ss"),
                                PartNumber = command.PartNumber.Value,
                                IsInaudible = isInaudible
                            };

                            return transcript;
                        }));
                }
            }

            if (transcripts.Any())
            {
                // if feature flag is enabled and this is the last part, save transcripts directly
                if (deferTranscriptionCompletion && command.isLastPart == true)
                {
                    await SaveTranscripts(transcription.Id, transcripts);
                }
                // if feature flag is disabled, send transcript created event, to sort and save transcripts
                else
                {
                    await sqsRepository.SendMessage(QueueType.Task,
                         new EventMessage(
                             EventType.TranscriptCreated,
                             new EventData<TranscriptCreatedEventData>(new TranscriptCreatedEventData(transcription.Id, transcripts)),
                             transcription.Id.ToString(),
                             command.IdentityContext.PersonId
                         ));
                }
            }

            // if this is the last part of the transcription, update the transcription status
            // and send a message to the transcription queue to complete the transcription
            if (deferTranscriptionCompletion && command.isLastPart == true)
            {
                await transcriptionRepository.UpdateStatus(transcription.Id, TranscriptionStatus.PendingCompletion);
                var noteIds = noteTranscriptions.Select(x => x.NoteId).ToArray();

                await sqsRepository.SendMessage(QueueType.Transcription,
                    new EventMessage(
                        EventType.TranscriptionPendingCompletion,
                        new EventData<TranscriptionPendingCompletionEventData>(
                            new TranscriptionPendingCompletionEventData(transcription.Id, noteIds, transcription.CreatedByPersonId.GetValueOrDefault(), transcription.ProviderId.GetValueOrDefault()))
                    ));
            }

            Log.Information("TranscriptionProcess: Completed processing transcription {TranscriptionId} with part number {PartNumber}");

            return transcripts;
        }
    }

    private async Task SaveTranscripts(Guid transcriptionId, List<Transcript> transcriptsToAdd)
    {
        var transcription = await transcriptionRepository.GetById(transcriptionId);

        var transcripts = new List<Transcript>();

        if (transcription is null || !transcriptsToAdd.Any()) return;

        // add existing transcripts
        if (transcription.Transcripts != null && transcription.Transcripts.Any())
        {
            transcripts.AddRange(transcription.Transcripts);
        }

        // add new transcripts
        transcripts.AddRange(transcriptsToAdd);

        // sort transcripts by part number and start time
        if (transcripts.Count > 1)
        {
            transcripts = transcripts.OrderBy(x => x.PartNumber).ThenBy(x => x.StartTime).ToList();
        }

        // update transcription with new transcripts
        transcription.Transcripts = transcripts;

        if (!transcription.HasInaudibleAudio)
        {
            transcription.HasInaudibleAudio = transcripts.Any(x => x.IsInaudible);
        }

        await transcriptionRepository.Update(transcription);
    }

    private async Task HandleMultiPartUploadPart(StorageProvider storageProvider, CreateTranscriptionPartsCommand command, string folderPath)
    {
        if (!command.FileId.HasValue || string.IsNullOrWhiteSpace(command.UploadId))
        {
            throw new ExecutionException(new ValidationError("Error", "Error", ValidationType.BadRequest));
        }

        await fileStorageServiceFactory(storageProvider).UploadPart(command.FileId.Value, command.UploadId, command.PartNumber.Value, true, command.File.OpenReadStream(), FileLocationType.Transcription, folderPath);
    }

    private async Task<string> GetAgentModel(Guid providerId)
    {
        var useAgentModelGeminiFlash25Preview = await featureService.IsEnabled(FeatureFlags.UseGeminiFlash25PreviewForTranscription, providerId);
        var model = useAgentModelGeminiFlash25Preview ? AgentModels.MultiModal.Google.GeminiFlash25_Preview : AgentModels.MultiModal.Google.GeminiFlash20_001;
        return model;
    }

    private async Task UploadAudioFile(string fileName, string path, byte[] audioBytes, string contentType,
        StorageProvider storageProvider)
    {
        var file = new UploadableFile
        {
            Bytes = audioBytes,
            FileKey = $"{path}/{fileName}",
            FileName = fileName,
            ContentType = contentType
        };

        await fileStorageServiceFactory(storageProvider).Upload(file, FileLocationType.Transcription);
    }

    private AgentProvider GetAgentProvider()
    {
        if (featureHeaderService.IsEnabled(FeatureFlags.AgentProviderGoogleSpeechToText))
        {
            return AgentProvider.GoogleSpeechToText;
        }
        else
        {
            return AgentProvider.Google;
        }
    }

    private bool DeferTranscriptionCompletion()
    {
        return featureHeaderService.IsEnabled(FeatureFlags.DeferredTranscriptionCompletion);
    }
}
