using System.Linq;
using FluentValidation;

namespace carepatron.core.Application.Workspace.ServiceItems.Commands;

public class SaveItemToItemGroupsCommandValidator : AbstractValidator<SaveItemToItemGroupsCommand>
{
    public SaveItemToItemGroupsCommandValidator()
    {
        RuleFor(x => x)
            .Must(x =>
            {
                var createSet = x.ItemGroupsToCreate?.ToHashSet() ?? [];
                var deleteSet = x.ItemGroupsToDelete?.ToHashSet() ?? [];
                return !createSet.Overlaps(deleteSet);
            })
            .WithMessage("An item group ID cannot be present in both ItemGroupsToCreate and ItemGroupsToDelete.");

        RuleFor(x => x)
            .Must(x =>
                (x.ItemGroupsToCreate != null && x.ItemGroupsToCreate.Length > 0) ||
                (x.ItemGroupsToDelete != null && x.ItemGroupsToDelete.Length > 0))
            .WithMessage("At least one of ItemGroupsToCreate or ItemGroupsToDelete must contain a value.");
    }
}