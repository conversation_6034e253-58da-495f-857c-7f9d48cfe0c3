using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Constants;
using carepatron.core.Extensions;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Item;
using Serilog;

namespace carepatron.core.Application.Workspace.ServiceItems.Commands;

public record SaveItemToItemGroupsCommand(Guid ProviderId, Guid ItemId, Guid[] ItemGroupsToCreate, Guid[] ItemGroupsToDelete) : IMediatrCommand<Unit>;

public class SaveItemToItemGroupsCommandHandler(
    IItemRepository itemRepository,
    IProviderItemGroupRepository providerItemGroupRepository) : IMediatrCommandHandler<SaveItemToItemGroupsCommand, Unit>
{
    public async Task<ExecutionResult<Unit>> Handle(SaveItemToItemGroupsCommand request, CancellationToken cancellationToken)
    {
        var item = await itemRepository.Get(request.ProviderId, request.ItemId);
        if (item is null)
            return new ValidationError(Errors.ProviderItemNotFoundCode, Errors.ProviderItemNotFoundDetails, ValidationType.NotFound);

        var itemGroups = await providerItemGroupRepository.Get(request.ProviderId, [..request.ItemGroupsToCreate, ..request.ItemGroupsToDelete]);

        if (itemGroups.IsNullOrEmpty())
        {
            Log.Warning("No item groups found for provider {ProviderId} and item {ItemId}", request.ProviderId, request.ItemId);
            return Unit.Value;
        }

        foreach (var itemGroup in itemGroups)
        {
            if (request.ItemGroupsToCreate.Contains(itemGroup.Id) && !itemGroup.ItemIds.Contains(item.Id))
            {
                await providerItemGroupRepository.AddItemToGroup(item.Id, itemGroup.Id);
            }
            else if (request.ItemGroupsToDelete.Contains(itemGroup.Id) && itemGroup.ItemIds.Contains(item.Id))
            {
                await providerItemGroupRepository.RemoveItemFromGroup(item.Id, itemGroup.Id);
            }
        }

        return Unit.Value;
    }
}