using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Extensions;
using carepatron.core.Models.Common;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Scheduling;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Staff;
using carepatron.core.Repositories.Tasks;
using LinqKit;

namespace carepatron.core.Application.Tasks.Queries;

public record GetSuggestedTimeBlocksQuery(Guid ProviderId, DateTime StartDate, DateTime EndDate, string TimeZone, Guid[] StaffIds) : IMediatrQuery<ConflictTimeBlock[]>;
public class GetSuggestedTimeBlocksQueryHandler(IStaffScheduleRepository staffScheduleRepository,
    ITaskService taskService) : IMediatrQueryHandler<GetSuggestedTimeBlocksQuery, ConflictTimeBlock[]>
{
    public async Task<ExecutionResult<ConflictTimeBlock[]>> Handle(GetSuggestedTimeBlocksQuery request, CancellationToken cancellationToken)
    {
        var duration = (int)(request.EndDate - request.StartDate).TotalMinutes;
        if (request.StaffIds.IsNullOrEmpty() || duration <= 0) return Array.Empty<ConflictTimeBlock>();
        
        var staffAvailabilities = await staffScheduleRepository.GetStaffSchedules(request.ProviderId, request.StaffIds, true);
        if (!staffAvailabilities.Any()) return Array.Empty<ConflictTimeBlock>();
        var staffSchedule = Schedule.FromStaffSchedule(staffAvailabilities);
        
        var localDate = request.StartDate.ToTimeZoneDateTime(request.TimeZone);
        var staffDateRanges = staffSchedule.Select(x => x.Expand(localDate.ToDateOnly(), duration)).ToArray();
        var commonDateRange = DateRangeExtensions.Intersect(staffDateRanges);

        var startOfDay = request.StartDate.Date.ToStartOfDay().AsZonedDateTimeToUtc(request.TimeZone);
        var endOfDay = request.EndDate.Date.ToEndOfDay().AsZonedDateTimeToUtc(request.TimeZone);

        var allTasks = await taskService.GetTasks(request.ProviderId,
            request.StaffIds,
            startOfDay,
            endOfDay,
            new(true, false));
        
        // for external tasks, we need to convert requested date to local time zone date for us to capture staff whole day events 
        var externalTasks = await taskService.GetTasksFromExternalCalendar(request.ProviderId, 
            request.StaffIds, 
            startOfDay,
            endOfDay);
        if (externalTasks.Any())
            allTasks = allTasks.Concat(externalTasks).ToArray();
        
        if (allTasks.IsNullOrEmpty())
        {
            var defaultTimeBlocks = commonDateRange.Select(x => x.ToConflictTimeBlock(request.TimeZone)).ToArray();
            return defaultTimeBlocks;
        }

        foreach (var task in allTasks)
            commonDateRange = commonDateRange.Where(x => !x.IsOverlappingDate(task.StartDate, task.EndDate)).ToList();
        
        var hasRequestedDateRange = false;
        var conflictTimeBlocks = commonDateRange
            .Select(x =>
            {
                if (x.FromDate == request.StartDate && x.ToDate == request.EndDate)
                    hasRequestedDateRange = true;

                return x.ToConflictTimeBlock(request.TimeZone);
            }).ToList();
        
        // need to insert requested date range when removed after filtering to show as conflict
        if (!hasRequestedDateRange)
        {
            var requestedDateRange = new DateRange(request.StartDate, request.EndDate);
            conflictTimeBlocks.Add(requestedDateRange.ToConflictTimeBlock(request.TimeZone, true));
        } 

        return conflictTimeBlocks
            .OrderBy(x => x.Start)
            .ThenBy(x => x.End)
            .ToArray();
    }
}
