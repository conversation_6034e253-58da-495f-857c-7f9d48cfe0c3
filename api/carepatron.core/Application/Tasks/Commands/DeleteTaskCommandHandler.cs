﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Application.Tasks.Events;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Application.Trash.Services;
using carepatron.core.Constants;
using carepatron.core.Models.Permissions;

namespace carepatron.core.Application.Tasks.Commands
{
    public class DeleteTaskCommandHandler(ITaskRepository taskRepository,
        IIntegrationEventPublisher integrationEventPublisher,
        ITrashService trashService,
        ITaskService taskService) : IMediatrCommandHandler<DeleteTaskCommand, Guid>
    {

        public async Task<ExecutionResult<Guid>> Handle(DeleteTaskCommand command, CancellationToken cancellationToken)
        {
            var task = await taskRepository.Get(command.Id);
            if (task is null) return ValidationError.NotFound;
            
            var personId = command.IdentityContext.PersonId;
            
            // must be either
            // - in the meeting with at least own calendar edit permissions.
            // - scheduling edit everything.
            if (false == (
                    command.IdentityContext.ProviderPermissions.SchedulingEdit == SchedulingPermission.OwnCalendar && task.Staff.Any(x => x.Id == personId)
                    || command.IdentityContext.ProviderPermissions.SchedulingEdit == SchedulingPermission.Everything))
            {
                return new ValidationError(Errors.UnauthorisedErrorCode, Errors.UnauthorisedErrorDetails, ValidationType.NotFound);
            }

            if (task.Type != TaskType.External)
            {
                await taskRepository.Delete(command.Id);

                await trashService.SaveTrashItem(task, command.IdentityContext.PersonId);

                //as per discussion, for now when soft deleting a task,
                //do not remove the call if exists. only delete the call on hard delete.

                integrationEventPublisher.Add(new OutgoingEvent<TaskTrashedEvent>(new(task)));
            }
            else//External tasks will not be soft deleted for now
            {
                var isReadOnly = await taskService.IsReadOnlyCalendar(task.ExternalCalendarId, task.ProviderId, task.StaffIds[0]);
                if (isReadOnly)
                {
                    return new ValidationError(Errors.CannotModifyEventFromReadOnlyCalendarCode,
                        Errors.CannotModifyEventFromReadOnlyCalendarDetails,
                        ValidationType.BadRequest);
                }
                
                await taskRepository.HardDelete(command.IdentityContext.ProviderPermissions.ProviderId, command.Id);

                integrationEventPublisher.Add(new OutgoingEvent<ExternalTaskDeletedEvent>(new ExternalTaskDeletedEvent(
                    new ExternalTaskDeletedEventModel(task.ProviderId, task.StaffIds[0], task.ExternalCalendarId, task.ExternalId, task.Id, task.Type, false)
                )));
            }

            return command.Id;
        }
    }
}
