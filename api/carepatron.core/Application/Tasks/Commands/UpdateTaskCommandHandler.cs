﻿using carepatron.core.Abstractions;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Tasks.Events;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Extensions;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Models.Call;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Call;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Services;
using Serilog;
using Serilog.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Application.Tasks.Extensions;
using carepatron.core.Constants;
using carepatron.core.Models.Permissions;

namespace carepatron.core.Application.Tasks.Commands
{
    public class UpdateTaskCommandHandler(
        ITaskRepository taskRepository,
        ICallRepository callRepository,
        IContactRepository contactRepository,
        ICallService callService,
        IUnitOfWork unitOfWork,
        IIntegrationEventPublisher integrationEventPublisher,
        ITaskService taskService,
        ITaskStatusService taskStatusService)
        : IMediatrCommandHandler<UpdateTaskCommand, TaskModel>
    {
        public async Task<ExecutionResult<TaskModel>> Handle(UpdateTaskCommand command, CancellationToken cancellationToken)
        {
            var oldTask = await taskRepository.Get(command.Id);
            
            if (oldTask == null)
                return new ValidationError(Errors.NotFoundErrorCode, Errors.NotFoundErrorDetails, ValidationType.NotFound);
            
            var personId = command.IdentityContext.PersonId;
            
            // must be either
            // - in the meeting with at least own calendar edit permissions.
            // - scheduling edit everything.
            if (false == (
                    (command.IdentityContext.ProviderPermissions.SchedulingEdit == SchedulingPermission.OwnCalendar && oldTask.Staff.Any(x => x.Id == personId) && command.StaffIds.Contains(personId))
                    || command.IdentityContext.ProviderPermissions.SchedulingEdit == SchedulingPermission.Everything))
            {
                return new ValidationError(Errors.UnauthorisedErrorCode, Errors.UnauthorisedErrorDetails, ValidationType.BadRequest);
            }

            if (oldTask.Type == TaskType.External)
            {
                var isReadOnly = await taskService.IsReadOnlyCalendar(oldTask.ExternalCalendarId, oldTask.ProviderId, oldTask.StaffIds[0]);
                if (isReadOnly)
                {
                    return new ValidationError(Errors.CannotModifyEventFromReadOnlyCalendarCode,
                        Errors.CannotModifyEventFromReadOnlyCalendarDetails,
                        ValidationType.BadRequest);
                }
            }
            
            List<SimpleTaskContact> taskContacts = new();
            var contactIds = command.ContactIds ?? [];
            var aboutContacts = command.AboutContacts ?? [];

            if (contactIds.Any())
            {
                var statuses = await taskStatusService.GetStatuses(oldTask.ProviderId, [TaskAttendeeGroupStatus.Accepted]);
                string confirmedStatusId = statuses.GetDefault(TaskAttendeeGroupStatus.Accepted)?.Id;

                var existingContactsMap = (oldTask.Contacts ?? []).ToDictionary(x => x.Id);
                taskContacts = contactIds.Select((contactId) =>
                    {
                        SimpleTaskContact taskContact = new();
                        taskContact.TaskId = oldTask.Id;
                        taskContact.ContactId = contactId;
                        
                        if (existingContactsMap.TryGetValue(contactId, out var existingContact))
                        {
                            taskContact.AboutContact = existingContact.AboutContact;
                            taskContact.TaskContactStatus = existingContact.Status;
                            taskContact.AttendeeStatusId = existingContact.AttendeeStatusId;
                            taskContact.Attending = existingContact.Attending;
                            return taskContact;
                        }
                        
                        taskContact.AboutContact = aboutContacts.Contains(contactId);
                        taskContact.TaskContactStatus = TaskContactStatus.Confirmed;
                        taskContact.AttendeeStatusId = confirmedStatusId;
                        
                        return taskContact;
                    })
                    .ToList();
            }

            var newTask = new SaveTaskModel
            {
                Id = command.Id,
                Type = oldTask.Type,
                ProviderId = oldTask.ProviderId,
                ParentId = command.ParentId,
                Title = command.Title,
                Description = command.Description,
                Location = command.Location,
                TimeZone = oldTask.TimeZone,
                StartDate = command.StartDate,
                EndDate = command.EndDate,
                AllDay = command.AllDay,
                RRule = command.RRule,
                ExDate = command.ExDate,
                OccurrenceEndDate = command.OccurrenceEndDate,
                ContactReminderConfigs = command.ContactReminderConfigs ?? Array.Empty<ContactReminderConfig>(),
                Files = command.Files.EmptyIfNull().ToArray(),
                CallId = oldTask.CallId,
                ContactIds = contactIds,
                AboutContacts = aboutContacts,
                Contacts = taskContacts.ToArray(),
                StaffIds = command.StaffIds ?? Array.Empty<Guid>(),
                Items = command.Items,
                CreatedByPersonId = oldTask.CreatedByPersonId,
                LastUpdatedByPersonId = command.IdentityContext.PersonId,
                CreatedDateTimeUtc = oldTask.CreatedDateTimeUtc,
                LastUpdatedDateTimeUtc = DateTime.UtcNow,
                LocationType = command.LocationType,
                LocationPOSCode = command.LocationPOSCode,
                VirtualLocationProduct = command.VirtualLocationProduct,
                ExternalCalendarId = oldTask.ExternalCalendarId,
                ExternalId = oldTask.ExternalId,
                ExternalEventType = oldTask.ExternalEventType,
                IsFree = oldTask.IsFree,
                IsBillingV2 = oldTask.IsBillingV2,
                LocationsSnapshot = command.LocationsSnapshot,
                DeclineEventType = command.DeclineEventType,
                ExternalContacts = oldTask.ExternalContacts,
            };

            // not great, but feels like the least disruptive place to do this.
            var contacts = await contactRepository.Get(contactIds);
            newTask.AttendeesPersonIds = contacts.Where(x => x.PersonId.HasValue)
                .Select(x => x.PersonId.Value)
                .Union(newTask.StaffIds)
                .Distinct()
                .ToArray();

            //if the old task has a call - check it's still relevant.
            if (oldTask.CallId.HasValue)
            {
                var existingCall = await callRepository.Get(oldTask.CallId.Value);

                // Temporary. Required because the providerId wasn't being set on the call
                if (existingCall.ProviderId is null)
                {
                    Log.Information("Call has no provider");
                    existingCall.ProviderId = oldTask.ProviderId;
                }

                using (LogContext.PushProperty("CallId", existingCall.Id))
                using (LogContext.PushProperty("CallExternal", existingCall.ExternalId))
                using (LogContext.PushProperty("CallCreatedByPersonId", existingCall.CreatedByPersonId))
                {
                    //if the new command has no call, remove the existing one.
                    if (!command.HasCall)
                    {
                        await callService.DeleteCall(existingCall);
                        newTask.CallId = null;
                    }
                    // otherwise update the call with new info
                    else
                    {
                        bool successfullyUpdated = await UpdateExistingCall(command, newTask, existingCall);

                        if (!successfullyUpdated)
                        {
                            var newCall = await callService.CreateCall(command.CallProvider ?? existingCall.CallProvider, newTask, command.MediaRegion);
                            newTask.CallId = newCall?.Id;

                            await callService.DeleteCall(existingCall);
                        }
                    }
                }
            }
            else if (command.HasCall)
            {
                var newCall = await callService.CreateCall(command.CallProvider ?? CallProvider.AWS, newTask, command.MediaRegion);
                newTask.CallId = newCall.Id;
            }

            await taskRepository.Save(newTask);
            //required so fetch and sqs dispatch below happens after changes have been committed.
            await unitOfWork.SaveUnitOfWork(cancellationToken);

            var updatedTask = await taskRepository.Get(newTask.Id);
            integrationEventPublisher.Add(new OutgoingEvent<TaskUpdatedEvent>(new(updatedTask, oldTask)));
            return updatedTask;
        }

        private async Task<bool> UpdateExistingCall(UpdateTaskCommand command, SaveTaskModel newTask, Call existingCall)
        {
            // when updating recurring appointments the commands call provider is null.
            // Fallback to updating the existing call in this scenario.
            if (command.CallProvider is null || existingCall.CallProvider == command.CallProvider)
            {
                var result = await callService.UpdateCall(newTask, existingCall, command.MediaRegion);
                return result != null;
            }

            return false;
        }
    }
}