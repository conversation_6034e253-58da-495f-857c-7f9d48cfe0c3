﻿using System;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Abstractions;
using carepatron.core.Application.Booking.Service;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Application.Tasks.Events;
using carepatron.core.Application.Tasks.Extensions;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Constants;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Tasks;

namespace carepatron.core.Application.Tasks.Commands
{
    public class RespondToTaskCommandHandler(
        ITaskRepository taskRepository,
        IBookingService bookingService,
        IIntegrationEventPublisher integrationEventPublisher,
        ITaskService taskService,
        IUnitOfWork unitOfWork,
        ITaskStatusService taskStatusService) : IMediatrCommandHandler<RespondToTaskCommand, Guid>
    {
        public async Task<ExecutionResult<Guid>> Handle(RespondToTaskCommand request, CancellationToken cancellationToken)
        {
            var task = await taskRepository.Get(request.TaskId);
            if (task is null)
                return new ValidationError(Errors.TaskNotFoundCode, Errors.TaskNotFoundDetail, ValidationType.NotFound);

            Guid currentTaskId = task.Id;

            if (!string.IsNullOrEmpty(task.RRule) && request.OccurrenceDateTimeUtc.HasValue)
            {
                currentTaskId = await taskService.ResolveOccurrenceTaskId(task, request.OccurrenceDateTimeUtc.Value) ?? task.Id;
                await unitOfWork.SaveUnitOfWork();
            }

            if (request.Response == ResponseToTask.Reject)
            {
                var canCancelAppointment = await bookingService.CanCancelAppointment(currentTaskId);
                if (!canCancelAppointment)
                    return new ValidationError(Errors.CannotCancelBeforeMinPeriodCode, Errors.CannotCancelBeforeMinPeriodDetail, ValidationType.BadRequest);
            }

            var taskContact = await taskRepository.GetTaskContact(currentTaskId, request.ContactId);
            if (taskContact is not null)
            {
                var acceptedStatuses = await taskStatusService.GetStatuses(task.ProviderId, [TaskAttendeeGroupStatus.Accepted]);

                var confirmedStatus = acceptedStatuses.GetDefault(TaskAttendeeGroupStatus.Accepted);
                
                var isAttending = request.Response == ResponseToTask.Accept;
                taskContact.Attending = isAttending;

                taskContact.Reason =
                    !string.IsNullOrEmpty(request.Reason)
                        ? $"{taskContact.Reason}\n{request.Reason}".Trim()
                        : taskContact.Reason;

                taskContact.TaskContactStatus = isAttending ? TaskContactStatus.Confirmed : TaskContactStatus.Cancelled;
                
                taskContact.AttendeeStatusId = isAttending ? confirmedStatus?.Id : nameof(TaskContactStatus.Cancelled);
                
                await taskRepository.UpdateTaskContact(taskContact);

                if (taskContact.TaskContactStatus == TaskContactStatus.Cancelled)
                    integrationEventPublisher.Add(new ClientEventCancelledEvent(new(taskContact.TaskId,
                        taskContact.ContactId,
                        false,
                        task.ProviderId,
                        task.Title,
                        task.StaffIds,
                        task.StartDate,
                        task.EndDate,
                        task.RRule,
                        taskContact.Reason,
                        null)));
            }

            return currentTaskId;
        }
    }
}