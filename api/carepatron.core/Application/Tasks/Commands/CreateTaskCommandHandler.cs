﻿using carepatron.core.Abstractions;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Application.Tasks.Events;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Extensions;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Tasks.Extensions;
using carepatron.core.Models.Common;

namespace carepatron.core.Application.Tasks.Commands
{
    public class CreateTaskCommandHandler(
        ITaskRepository taskRepository,
        IContactRepository contactRepository,
        ICallService callService,
        IIntegrationEventPublisher integrationEventPublisher,
        IUnitOfWork unitOf<PERSON>ork,
        ITaskStatusService taskStatusService)
        : IMediatrCommandHandler<CreateTaskCommand, TaskModel>
    {
        public async Task<ExecutionResult<TaskModel>> Handle(CreateTaskCommand command, CancellationToken cancellationToken)
        {
            var parentTask = await CreateTask(
                command,
                command.StartDate,
                command.EndDate,
                command.RRule,
                command.ExDate,
                command.OccurrenceEndDate,
                command.ParentId,
                cancellationToken);

            if (!string.IsNullOrEmpty(command.RRule) && command.Occurrences.Any())
            {
                foreach (var occurrence in command.Occurrences)
                {
                    await CreateTask(
                        command,
                        occurrence.FromDate,
                        occurrence.ToDate,
                        null,
                        null,
                        null,
                        parentTask.Id,
                        cancellationToken);
                }
            }

            await unitOfWork.SaveUnitOfWork();

            var task = await taskRepository.Get(parentTask.Id);

            return task;
        }

        private async Task<SaveTaskModel> CreateTask(CreateTaskCommand command, DateTime startDate, DateTime endDate, RRule rRule, string exDate, DateTime? occurrenceEndDate, Guid? parentId, CancellationToken cancellationToken)
        {
            var taskId = Guid.NewGuid();
            List<SimpleTaskContact> taskContacts = new();
            var contactIds = command.ContactIds ?? [];
            var aboutContacts = command.AboutContacts ?? [];

            if (contactIds.Any())
            {
                var statuses = await taskStatusService.GetStatuses(command.ProviderId, [TaskAttendeeGroupStatus.Accepted]);
                string confirmedStatusId = statuses.GetDefault(TaskAttendeeGroupStatus.Accepted)?.Id;

                taskContacts = contactIds.Select(contactId =>
                        new SimpleTaskContact(
                            taskId,
                            contactId,
                            true,
                            null,
                            aboutContacts.Contains(contactId),
                            TaskContactStatus.Confirmed,
                            confirmedStatusId
                        ))
                    .ToList();
            }

            var model = new SaveTaskModel
            {
                Id = taskId,
                Type = command.Type,
                ProviderId = command.ProviderId,
                ParentId = parentId,
                Title = command.Title,
                Description = command.Description,
                Location = command.Location,
                TimeZone = command.TimeZone,
                StartDate = startDate,
                EndDate = endDate,
                AllDay = command.AllDay,
                RRule = rRule,
                ExDate = exDate,
                OccurrenceEndDate = occurrenceEndDate,
                ContactReminderConfigs = command.ContactReminderConfigs ?? [],
                Files = command.Files.EmptyIfNull().ToArray(),
                CallId = command.CallId,
                ContactIds = contactIds,
                AboutContacts = aboutContacts,
                Contacts = taskContacts.ToArray(),
                StaffIds = command.StaffIds ?? [],
                Items = command.Items,
                CreatedByPersonId = command.IdentityContext.PersonId,
                LastUpdatedByPersonId = command.IdentityContext.PersonId,
                CreatedDateTimeUtc = DateTime.UtcNow,
                LastUpdatedDateTimeUtc = DateTime.UtcNow,
                LocationType = command.LocationType,
                LocationPOSCode = command.LocationPOSCode,
                VirtualLocationProduct = command.VirtualLocationProduct,
                ExternalEventType = command.ExternalEventType,
                ExternalCalendarId = command.ExternalCalendarId,
                IsBillingV2 = command.IsBillingV2,
                LocationsSnapshot = command.LocationsSnapshot,
                DeclineEventType = command.DeclineEventType
            };

            // not great, but feels like the least disruptive place to do this.
            var contacts = await contactRepository.Get(contactIds);
            model.AttendeesPersonIds = contacts.Where(x => x.PersonId.HasValue)
                .Select(x => x.PersonId.Value)
                .Union(model.StaffIds)
                .Distinct()
                .ToArray();

            if (command.HasCall && !command.CallId.HasValue)
            {
                var call = await callService.CreateCall(command.CallProvider, model, command.MediaRegion);
                model.CallId = call.Id;
            }

            await taskRepository.Create(model);

            integrationEventPublisher.Add(new OutgoingEvent<TaskCreatedEvent>(new(model)));

            return model;
        }
    }
}