using System;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.IntegrationEvents.Abstractions;

namespace carepatron.core.Application.Tasks.Events;

public record ExternalTaskDeletedEventModel(
    Guid ProviderId,
    Guid PersonId,
    string ExternalCalendarId,
    string ExternalId,
    Guid? TaskId,
    TaskType TaskType,
    bool SkipUserNotification);

public record ExternalTaskDeletedEvent(ExternalTaskDeletedEventModel Entity)
    : IEntityEvent<ExternalTaskDeletedEventModel>;
