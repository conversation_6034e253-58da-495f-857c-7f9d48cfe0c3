using carepatron.core.Application.Calendar.Models;
using carepatron.core.Application.ConnectedApps.Models;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Users.Models;
using carepatron.core.Constants;
using carepatron.core.Repositories.ConnectedApps;
using carepatron.core.Repositories.Person;
using carepatron.core.Repositories.Tasks;
using Microsoft.Extensions.Logging;
using Serilog.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using carepatron.core.Application.ConnectedApps.Abstractions.Settings;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Application.Calendar.Events;
using carepatron.core.Application.Persons.Abstractions;
using carepatron.core.Repositories.SQS;
using carepatron.core.Events.Events;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Events;
using carepatron.core.Exceptions;
using carepatron.core.Services;

namespace carepatron.core.Application.Tasks.Services
{
    public class ConnectedCalendarService : IConnectedCalendarService
    {
        private static readonly string[] CalendarProductCodes = new[] { ConnectedAppProducts.Google, ConnectedAppProducts.Microsoft };

        private readonly IConnectedAppRepository connectedAppRepository;
        private readonly IPersonRepository personRepository;
        private readonly ICalendarSubscriptionRepository calendarSubscriptionRepository;
        private readonly ILogger<ConnectedCalendarService> logger;
        private readonly IConnectedAppCalendarServiceFactory connectedAppCalendarServiceFactory;
        private readonly ISqsRepository sqsRepository;
        private readonly IDictionary<string, IConnectedCalendarRepository> calendarRepositories;

        public ConnectedCalendarService(
            IConnectedAppRepository connectedAppRepository,
            IPersonRepository personRepository,
            ICalendarSubscriptionRepository calendarSubscriptionRepository,
            IEnumerable<IConnectedCalendarRepository> calendarRepositories,
            ILogger<ConnectedCalendarService> logger,
            IConnectedAppCalendarServiceFactory connectedAppCalendarServiceFactory,
            ISqsRepository sqsRepository)
        {
            this.connectedAppRepository = connectedAppRepository;
            this.personRepository = personRepository;
            this.calendarSubscriptionRepository = calendarSubscriptionRepository;
            this.logger = logger;
            this.connectedAppCalendarServiceFactory = connectedAppCalendarServiceFactory;
            this.sqsRepository = sqsRepository;
            this.calendarRepositories = calendarRepositories.ToDictionary(_ => _.AppProductCode, _ => _);
        }

        public Task<TaskModel[]> GetExternalTasks(Guid personId, Guid? providerId, GetEventsOptions options)
        {
            return GetExternalTasks(new Guid[] { personId }, providerId, options);
        }

        public async Task<TaskModel[]> GetExternalTasks(Guid[] personIds, Guid? providerId, GetEventsOptions options)
        {
            var apps = await connectedAppRepository.GetRestrictedConnectedApps(personIds, providerId, CalendarProductCodes);

            if (!apps.Any())
                return Array.Empty<TaskModel>();

            var personLookup = (await personRepository
                .GetPersons(apps
                    .Where(p => p.PersonId.HasValue)
                    .Select(p => p.PersonId.Value)
                    .ToArray()))
                .ToDictionary(_ => _.Id, p => (SimplePerson)p);

            var fetchTasks = apps.Select(app =>
            {
                var getFromLegacyExternalEvents = app.Settings.IsSettingType<IConnectedAppCalendarSettings>() &&
                                                  !(app.Settings.AsSettingType<IConnectedAppCalendarSettings>().Calendar
                                                      ?.UseTwoWaySync).GetValueOrDefault();

                if (app.DisplayCalendar && getFromLegacyExternalEvents && calendarRepositories.TryGetValue(app.ProductCode, out var repo))
                {
                    return repo.GetEvents(app, personLookup[app.PersonId.Value], options);
                }
                return Task.FromResult(Array.Empty<TaskModel>());
            });

            var externalTasks = (await Task.WhenAll(fetchTasks)).SelectMany(x => x).ToArray();

            return externalTasks;
        }

        public async Task<ExternalTasksResult> GetExternalTasks(ConnectedApp connectedApp, SimplePerson person, GetEventsOptions options)
        {
            using (LogContext.PushProperty("ConnectedApp", new
            {
                connectedApp.ProviderId,
                connectedApp.ProductId,
            }, true))
            {
                if (calendarRepositories.TryGetValue(connectedApp.ProductCode, out var repo))
                {
                    return await repo.GetEventsResult(connectedApp, person, options);
                }

                return ExternalTasksResult.Empty;
            }
        }

        public async Task SyncCreatedTask(TaskModel task, Guid[] staffIds, ICalendarPreferenceProvider calendarPreferenceProvider = null)
        {
            using (LogContext.PushProperty("TaskId", task.Id))
            {
                var connectedApps = await connectedAppRepository.GetRestrictedConnectedApps(staffIds,
                    task.ProviderId,
                    new[] { ConnectedAppProducts.Google, ConnectedAppProducts.Microsoft });

                if (connectedApps.Length <= 0) return;

                try
                {
                    foreach (var app in connectedApps)
                    {
                        if (app.PushToCalendar && calendarRepositories.TryGetValue(app.ProductCode, out var repo))
                        {
                            await repo.CreateEvent(app, task, null, calendarPreferenceProvider);
                        }
                    }
                }
                catch (RateLimitExceededException ex)
                {
                    // log as warning but throw exception to be retried on the next minute
                    logger.LogWarning(ex, "Limit per minute exceeded");
                    throw;
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "Exception occurred when creating an event");
                }
            }
        }

        public async Task SyncUpdatedTask(TaskModel task, Guid[] staffIds, ICalendarPreferenceProvider calendarPreferenceProvider = null)
        {
            using (LogContext.PushProperty("TaskId", task.Id))
            {
                var connectedApps = await connectedAppRepository.GetRestrictedConnectedApps(staffIds,
                task.ProviderId,
                new[] { ConnectedAppProducts.Google, ConnectedAppProducts.Microsoft });

                if (connectedApps.Length <= 0) return;

                try
                {
                    foreach (var app in connectedApps)
                    {
                        if (app.PushToCalendar && calendarRepositories.TryGetValue(app.ProductCode, out var repo))
                        {
                            await repo.UpsertEvent(app, task, null, calendarPreferenceProvider);
                        }
                    }
                }
                catch (RateLimitExceededException ex)
                {
                    // log as warning but throw exception to be retried on the next minute
                    logger.LogWarning(ex, "Limit per minute exceeded");
                    throw;
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "Exception occurred when updating an event");
                }
            }
        }

        public async Task SyncDeletedTask(Guid providerId, Guid taskId, params Guid[] staffIds)
        {
            using (LogContext.PushProperty("TaskId", taskId))
            {
                var connectedApps = await connectedAppRepository.GetRestrictedConnectedApps(staffIds,
                providerId,
                new[] { ConnectedAppProducts.Google, ConnectedAppProducts.Microsoft });

                if (connectedApps.Length <= 0) return;

                try
                {
                    foreach (var app in connectedApps)
                    {
                        if (app.PushToCalendar && calendarRepositories.TryGetValue(app.ProductCode, out var repo))
                        {
                            await repo.DeleteEvent(app, taskId);
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "Exception occurred when deleting an event");
                }
            }
        }

        public async Task<Dictionary<string, ExternalTask>> Link(ConnectedApp connectedApp,
            SimplePerson person,
            Guid providerId,
            string calendarId,
            IEnumerable<(Guid taskId, string eventId)> items,
            bool expanded = false,
            string calendarTimeZone = null,
            bool includeDeleted = false)
        {
            if (calendarRepositories.TryGetValue(connectedApp.ProductCode, out var repo))
            {
                return await repo.Link(connectedApp, person, providerId, calendarId, items, expanded, calendarTimeZone, includeDeleted);
            }
            return null;
        }

        public async Task<CalendarResult> GetCalendar(ConnectedApp connectedApp, string calendarId)
        {
            if (calendarRepositories.TryGetValue(connectedApp.ProductCode, out var repo))
            {
                try
                {
                    return await repo.GetCalendar(connectedApp, calendarId);
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "Exception occurred when getting a calendar");
                }
            }

            return null;
        }

        public async Task<CalendarResult> GetCalendar(Guid connectedAppId, string calendarId)
        {
            var connectedApp = await connectedAppRepository.GetConnectedApp(connectedAppId);
            return await GetCalendar(connectedApp, calendarId);
        }

        public async Task UnsubscribeFromCalendar(Guid connectedAppId, string channelId, string resourceId)
        {
            using (LogContext.PushProperty("UnsubscribeFromCalendarDetails", new { connectedAppId, channelId, resourceId }, true))
            {
                var connectedApp = await connectedAppRepository.GetConnectedApp(connectedAppId);
                if (calendarRepositories.TryGetValue(connectedApp.ProductCode, out var repo))
                {
                    try
                    {
                        await repo.UnsubscribeFromCalendar(connectedApp, channelId, resourceId);
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning(ex, "Exception occurred when unsubscribing from a calendar");
                    }
                }
            }
        }

        public async Task UpdateExternalEvent(TaskModel taskModel, Guid personId, string calendarId)
        {
            using (LogContext.PushProperty("TaskId", taskModel.Id))
            {
                var connectedApp = await calendarSubscriptionRepository.GetConnectedApp(taskModel.ProviderId, personId, calendarId);

                if (connectedApp is null)
                {
                    logger.LogWarning("No connected app found for person {PersonId} and calendar {CalendarId}", personId, calendarId);
                    return;
                }

                var service = connectedAppCalendarServiceFactory.Get(connectedApp.ProductCode);

                await service.UpdateExternalEvent(connectedApp, taskModel, calendarId);
            }
        }

        public async Task CreateExternalEvent(TaskModel taskModel, Guid personId, string calendarId)
        {
            using (LogContext.PushProperty("TaskId", taskModel.Id))
            {
                var connectedApp = await calendarSubscriptionRepository.GetConnectedApp(taskModel.ProviderId, personId, calendarId);

                if (connectedApp is null)
                {
                    logger.LogWarning("No connected app found for person {PersonId} and calendar {CalendarId}", personId, calendarId);
                    return;
                }

                var service = connectedAppCalendarServiceFactory.Get(connectedApp.ProductCode);

                await service.CreateExternalEvent(connectedApp, taskModel, calendarId);
            }
        }

        public async Task DeleteExternalEvent(Guid providerId, Guid personId, string calendarId, string eventId, Guid? taskId)
        {
            var connectedApp = await calendarSubscriptionRepository.GetConnectedApp(providerId, personId, calendarId);

            if (connectedApp is null)
            {
                logger.LogWarning("No connected app found for person {PersonId} and calendar {CalendarId}", personId, calendarId);
                return;
            }

            if (calendarRepositories.TryGetValue(connectedApp.ProductCode, out var repo))
            {
                await repo.DeleteEvent(connectedApp, calendarId, eventId, taskId);
            }
        }

        public async Task SubscribeToCalendar(Guid calendarSubscriptionId)
        {

            var calendarSubscription = await calendarSubscriptionRepository.GetCalendarSubscription(calendarSubscriptionId);

            if (calendarSubscription == null)
                return;

            using (LogContext.PushProperty("SubscribeToCalendarDetails", new
            {
                calendarSubscriptionId,
                calendarSubscription.ChannelId,
                calendarSubscription.ResourceId,
                calendarSubscription.IsActive
            }, true))
            {

                var connectedApp = await connectedAppRepository.GetConnectedApp(calendarSubscription.ConnectedAppId);
                if (calendarRepositories.TryGetValue(connectedApp.ProductCode, out var repo))
                {
                    await SubscribeToCalendar(calendarSubscription, connectedApp, repo);
                }
            }
        }

        public async Task UnsubscribeFromCalendar(Guid calendarSubscriptionId)
        {
            var calendarSubscription = await calendarSubscriptionRepository.GetCalendarSubscription(calendarSubscriptionId);

            if (calendarSubscription == null)
                return;

            using (LogContext.PushProperty("UnsubscribeFromCalendarDetails", new
            {
                calendarSubscriptionId,
                calendarSubscription.ChannelId,
                calendarSubscription.ResourceId,
                calendarSubscription.IsActive
            }, true))
            {
                var connectedApp = await connectedAppRepository.GetConnectedApp(calendarSubscription.ConnectedAppId);

                if (calendarRepositories.TryGetValue(connectedApp.ProductCode, out var repo))
                {
                    try
                    {
                        await repo.UnsubscribeFromCalendar(connectedApp, calendarSubscription.ChannelId, calendarSubscription.ResourceId);

                        var calendarUnSubscribedEvent = new CalendarSubscriptionResourceEvent(calendarSubscription.Id, null, null);
                        await sqsRepository.SendMessage(QueueType.Task, new EventMessage(EventType.CalendarSubscriptionResource,
                            new EventData<CalendarSubscriptionResourceEvent>(calendarUnSubscribedEvent), groupId: calendarSubscription.Id.ToString(), null));
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning(ex, "Exception occurred when unsubscribing from a calendar");
                    }
                }
            }
        }

        public async Task UnsubscribeFromCalendar(string productCode, AccessTokenInfo accessTokenInfo, string channelId, string resourceId)
        {
            using (LogContext.PushProperty("UnsubscribeFromCalendarDetails", new { channelId, resourceId }, true))
            {
                if (calendarRepositories.TryGetValue(productCode, out var repo))
                {
                    try
                    {
                        await repo.UnsubscribeFromCalendar(accessTokenInfo, channelId, resourceId);
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning(ex, "Exception occurred when unsubscribing from a calendar");
                    }
                }
            }
        }

        public async Task<CalendarSubscribeResult> ResubscribeToCalendar(Guid calendarSubscriptionId)
        {
            var calendarSubscription = await calendarSubscriptionRepository.GetCalendarSubscription(calendarSubscriptionId);
            
            using (LogContext.PushProperty("ResubscribeToCalendarDetails", new
                   {
                       Id = calendarSubscriptionId,
                       calendarSubscription?.CalendarId,
                       calendarSubscription?.ChannelId,
                       calendarSubscription?.ResourceId,
                   }, true))
            {
                if (calendarSubscription == null)
                    return null;

                var connectedApp = await connectedAppRepository.GetConnectedApp(calendarSubscription.ConnectedAppId);
                if (calendarRepositories.TryGetValue(connectedApp.ProductCode, out var repo))
                {
                    try
                    {
                        //Unsubscribe first so we can use the same channel id
                        await repo.UnsubscribeFromCalendar(connectedApp, calendarSubscription.ChannelId, calendarSubscription.ResourceId);
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning(ex, "Exception occurred when unsubscribing from a calendar");
                    }

                    return await repo.SubscribeToCalendar(connectedApp, calendarSubscription.CalendarId, calendarSubscription.ChannelId, calendarSubscription.TokenId);

                }

                return null;
                
            }
        }


        private async Task SubscribeToCalendar(CalendarSubscription calendarSubscription, ConnectedApp connectedApp, IConnectedCalendarRepository repo)
        {
            try
            {
                var result = await repo.SubscribeToCalendar(connectedApp, calendarSubscription.CalendarId, calendarSubscription.ChannelId, calendarSubscription.TokenId);

                if (result != null)
                {
                    var calendarSubscribedEvent = new CalendarSubscriptionResourceEvent(calendarSubscription.Id, result.ResourceId, result.ExpirationDateTimeUtc);

                    await sqsRepository.SendMessage(QueueType.Task, new EventMessage(EventType.CalendarSubscriptionResource,
                        new EventData<CalendarSubscriptionResourceEvent>(calendarSubscribedEvent), groupId: calendarSubscription.Id.ToString(), null));
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Exception occurred when subscribing to a calendar");

                await calendarSubscriptionRepository.DeleteCalendarSubscription(calendarSubscription.Id);
            }
        }


        public async Task<CalendarResult[]> GetCalendarList(Guid connectedAppId)
        {
            var connectedApp = await connectedAppRepository.GetConnectedApp(connectedAppId);
            if (calendarRepositories.TryGetValue(connectedApp.ProductCode, out var repo))
            {
                try
                {
                    return await repo.GetCalendarList(connectedApp);
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "Exception occurred when getting a calendar");
                }
            }

            return Array.Empty<CalendarResult>();
        }

    }
}