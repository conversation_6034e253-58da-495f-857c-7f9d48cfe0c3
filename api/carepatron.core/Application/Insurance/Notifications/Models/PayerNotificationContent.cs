using System;
using carepatron.core.Application.Insurance.Models;

namespace carepatron.core.Application.Insurance.Notifications.Models;

public record PayerNotificationContent(string Number, string Name, Guid? PayerId)
{
    public static PayerNotificationContent Create(ProviderInsurancePayer payer)
    {
        ArgumentNullException.ThrowIfNull(payer, nameof(payer));

        return new PayerNotificationContent(payer.PayerId, payer.Name, payer.Id);
    }
}