using System;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Workspace.Billing.Notifications;
using Notifications.Sdk.Client;
using Notifications.Sdk.Client.Abstract;

namespace carepatron.core.Application.Insurance.Notifications.Models;

[NotificationsContent(NotificationCategoryRequest.BILLING_PAYMENT, PaymentsNotificationTypes.InsuranceClaimRejected)]
public class InsuranceClaimRejectedNotificationContent : ClaimResponseNotificationContent
{
    public InsuranceClaimRejectedNotificationContent(Guid providerId, ClaimNotificationContent claim, PayerNotificationContent payer)
        : base(providerId, claim, payer)
    {
        
    }
}

[NotificationsContent(NotificationCategoryRequest.BILLING_PAYMENT, PaymentsNotificationTypes.InsuranceClaimDenied)]
public class InsuranceClaimDeniedNotificationContent : ClaimResponseNotificationContent
{
    public InsuranceClaimDeniedNotificationContent(Guid providerId, ClaimNotificationContent claim, PayerNotificationContent payer)
        : base(providerId, claim, payer)
    {
        
    }
}

public abstract class ClaimResponseNotificationContent : INotificationsContent
{
    // Notification content must be flattened to be compatible
    
    protected ClaimResponseNotificationContent(
        Guid providerId,
        ClaimNotificationContent claim,
        PayerNotificationContent payer)
    {
        if (providerId == Guid.Empty) throw new ArgumentException($"{nameof(providerId)} cannot be empty", nameof(providerId));
        
        ArgumentNullException.ThrowIfNull(claim, nameof(claim));
        ArgumentNullException.ThrowIfNull(payer, nameof(payer));
        
        ProviderId = providerId;
        
        ClaimId = claim.Id;
        ClaimContactId = claim.ContactId;
        ClaimNumber = claim.Number;
        ClaimType = claim.ClaimType;
        
        PayerId = payer.PayerId;
        PayerNumber = payer.Number;
        PayerName = payer.Name;
    }
    
    public Guid ProviderId { get; }
    
    public Guid ClaimId { get; }
    public Guid ClaimContactId { get; }
    public string ClaimNumber { get; }
    public ClaimType ClaimType { get; }
    
    public string PayerNumber { get; }
    public string PayerName { get; }
    public Guid? PayerId { get; }
}