using System;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Workspace.Billing.Notifications;
using Notifications.Sdk.Client;
using Notifications.Sdk.Client.Abstract;

namespace carepatron.core.Application.Insurance.Notifications.Models;

[NotificationsContent(NotificationCategoryRequest.BILLING_PAYMENT, PaymentsNotificationTypes.InsuranceClaimPaid)]
public class InsuranceClaimPaidNotificationContent : ClaimPaymentNotificationContent
{
    public InsuranceClaimPaidNotificationContent(Guid providerId, ClaimNotificationContent claim, PaymentNotificationContent payment, PayerNotificationContent payer)
        : base(providerId, claim, payment, payer)
    {
        
    }
}

[NotificationsContent(NotificationCategoryRequest.BILLING_PAYMENT, PaymentsNotificationTypes.InsuranceClaimPartiallyPaid)]
public class InsuranceClaimPartiallyPaidNotificationContent : ClaimPaymentNotificationContent
{
    public InsuranceClaimPartiallyPaidNotificationContent(Guid providerId, ClaimNotificationContent claim, PaymentNotificationContent payment, PayerNotificationContent payer)
        : base(providerId, claim, payment, payer)
    {
        
    }
}

public abstract class ClaimPaymentNotificationContent : INotificationsContent
{
    // Notification content must be flattened to be compatible
    
    protected ClaimPaymentNotificationContent(
        Guid providerId,
        ClaimNotificationContent claim,
        PaymentNotificationContent payment,
        PayerNotificationContent payer)
    {
        if (providerId == Guid.Empty) throw new ArgumentException($"{nameof(providerId)} cannot be empty", nameof(providerId));
        
        ArgumentNullException.ThrowIfNull(claim, nameof(claim));
        ArgumentNullException.ThrowIfNull(payment, nameof(payment));
        ArgumentNullException.ThrowIfNull(payer, nameof(payer));
        
        ProviderId = providerId;
        
        ClaimId = claim.Id;
        ClaimContactId = claim.ContactId;
        ClaimNumber = claim.Number;
        ClaimType = claim.ClaimType;
        
        PaymentId = payment.Id;
        PaymentAmount = payment.Amount;
        PaymentCurrencyCode = payment.CurrencyCode;
        PaymentReference = payment.Reference;
        
        PayerId = payer.PayerId;
        PayerNumber = payer.Number;
        PayerName = payer.Name;
    }
    
    public Guid ProviderId { get; }
    
    public Guid ClaimId { get; }
    public Guid ClaimContactId { get; }
    public string ClaimNumber { get; }
    public ClaimType ClaimType { get; }
    
    public Guid PaymentId { get; }
    public decimal PaymentAmount { get; }
    public string PaymentCurrencyCode { get; }
    public string PaymentReference { get; }

    public string PayerNumber { get; }
    public string PayerName { get; }
    public Guid? PayerId { get; }
}
