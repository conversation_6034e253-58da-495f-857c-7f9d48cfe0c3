using System;
using carepatron.core.Application.Insurance.Models;

namespace carepatron.core.Application.Insurance.Notifications.Models;

public record ClaimNotificationContent(Guid Id, Guid ContactId, string Number, ClaimType ClaimType)
{
    public static ClaimNotificationContent Create(InsuranceClaimUSProfessional claim)
    {
        ArgumentNullException.ThrowIfNull(claim, nameof(claim));
        
        return new ClaimNotificationContent(
            Id: claim.Id,
            ContactId: claim.ContactId,
            Number: claim.Number,
            ClaimType: ClaimType.USProfessional
        );
    }
}