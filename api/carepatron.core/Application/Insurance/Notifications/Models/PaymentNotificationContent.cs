using System;
using carepatron.core.Application.Payments.Models;

namespace carepatron.core.Application.Insurance.Notifications.Models;

public record PaymentNotificationContent(Guid Id, decimal Amount, string CurrencyCode, string Reference)
{
    public static PaymentNotificationContent Create(Payment payment)
    {
        ArgumentNullException.ThrowIfNull(payment, nameof(payment));

        return new PaymentNotificationContent(payment.Id, payment.TransferAmount, payment.CurrencyCode, payment.Reference);
    }
}