using System;
using System.Collections.Generic;
using System.Linq;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Pdf.Models;
using carepatron.core.Extensions;

namespace carepatron.core.Application.Insurance.Utilities;

public static class ClaimFormUtilities
{
    public static (string AreaCode, string Number) GetPhoneNumberParts(string phoneNumber)
    {
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return (string.Empty, string.Empty);

        var phoneAreaCode = phoneNumber[..3];
        var phoneNumberCentralAndLine = phoneNumber[3..];

        return (phoneAreaCode, phoneNumberCentralAndLine);
    }

    public static string GetDateByTimezone(DateTime date, string timeZoneId)
    {
        if (string.IsNullOrEmpty(timeZoneId))
            return null;

        if (TimeZoneInfo.TryFindSystemTimeZoneById(timeZoneId, out var timeZone))
        {
            return TimeZoneInfo.ConvertTimeFromUtc(date, timeZone).ToString("MM/dd/yyyy");
        }

        return null;
    }

    public static PdfFormField GetPdfFormField(
        string value,
        string fieldName,
        string alignment = "left"
    )
    {
        return new PdfFormField
        {
            FieldName = fieldName,
            Value = value,
            Alignment = alignment
        };
    }

    public static DateOnly? GetDateOnlyValue(DateOnly? date)
    {
        if (date is null)
            return null;
        if (date == DateOnly.MinValue)
            return null;
        return date;
    }

    public static string DxCodeIndexToMarker(int index)
    {
        return index switch
        {
            0 => "A",
            1 => "B",
            2 => "C",
            3 => "D",
            4 => "E",
            5 => "F",
            6 => "G",
            7 => "H",
            8 => "I",
            9 => "J",
            10 => "K",
            11 => "L",
            _ => string.Empty
        };
    }

    public static string GetFullName(
        string firstName,
        string middleName,
        string lastName,
        bool includeCommas = true
    )
    {
        if (string.IsNullOrWhiteSpace(firstName) && string.IsNullOrWhiteSpace(lastName))
            return string.Empty;
        var names = new List<string>();
        if (includeCommas)
        {
            names.AddIfNotNullOrEmpty(lastName);
            names.AddIfNotNullOrEmpty(firstName);
            names.AddIfNotNullOrEmpty(GetMiddleInitial(middleName));
            return string.Join(", ", names).Trim();
        }
        else
        {
            names.AddIfNotNullOrEmpty(firstName);
            names.AddIfNotNullOrEmpty(GetMiddleInitial(middleName));
            names.AddIfNotNullOrEmpty(lastName);
            return string.Join(" ", names).Trim();
        }
    }

    public static string GetMiddleInitial(string middleName)
    {
        if (string.IsNullOrWhiteSpace(middleName))
            return string.Empty;
        return middleName[..1];
    }

    public static string GetPaddedNumber(int number)
    {
        if (number == 0)
            return string.Empty;
        if (number < 10)
            return $"0{number}";
        return number.ToString();
    }

    public static string GetYearString(int number, int length = 4)
    {
        if (number == 0)
            return string.Empty;

        var str = number.ToString();
        if (length == 2 && str.Length >= 3)
            return str[2..];

        return str;
    }

    public static string GetProviderQualifierTypeLabel(
        ClaimProviderQualifier? claimProviderQualifier
    )
    {
        if (claimProviderQualifier is null)
            return string.Empty;
        return claimProviderQualifier switch
        {
            ClaimProviderQualifier.ReferringProvider => "DN",
            ClaimProviderQualifier.OrderingProvider => "DK",
            ClaimProviderQualifier.SupervisingProvider => "DQ",
            _ => "None"
        };
    }

    public static string GetDxCodes(string[] dxCodes, List<ClaimDiagnosticCode> codes)
    {
        if (dxCodes is null || dxCodes.Length == 0)
            return string.Empty;

        var dxCodeString = new List<string>();

        foreach (var code in dxCodes)
        {
            var diagnosticCodeIndex = codes.FindIndex(x => x.Code == code);
            if (diagnosticCodeIndex < -1)
                continue;
            dxCodeString.Add(DxCodeIndexToMarker(diagnosticCodeIndex));
        }

        return string.Join(", ", dxCodeString.Where(x => !string.IsNullOrEmpty(x)));
    }

    public static string TrimCharacters(string value, string charsToRemove, int maxLength)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;
        for (var i = 0; i < charsToRemove.Length; i++)
        {
            value = value.Replace(charsToRemove[i].ToString(), string.Empty);
        }
        return value.Length > maxLength ? value[..maxLength] : value;
    }
}
