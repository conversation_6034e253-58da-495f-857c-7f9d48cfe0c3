using carepatron.core.Application.History.Models;
using carepatron.core.Application.Insurance.Extensions;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Identity;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.History;
using carepatron.core.Repositories.Insurance;
using carepatron.core.Repositories.Person;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Insurance.Commands;

public record UpdateInsuranceClaimStatusCommand(
    Guid Id,
    Guid ProviderId,
    ClaimStatus Status,
    IIdentityContext IdentityContext
) : IMediatrCommand<Unit>;

public class UpdateInsuranceClaimStatusCommandHandler(
    IInsuranceClaimsRepository insuranceClaimsRepository,
    IEntityHistoryRepository entityHistoryRepository,
    IPersonRepository personRepository
) : IMediatrCommandHandler<UpdateInsuranceClaimStatusCommand, Unit>
{
    public async Task<ExecutionResult<Unit>> Handle(UpdateInsuranceClaimStatusCommand request, CancellationToken cancellationToken)
    {
        var claim = await insuranceClaimsRepository.GetById(request.Id, request.ProviderId, cancellationToken);
        if (claim is null) return InsuranceErrors.ClaimNotFound;
        
        var validationResult = ValidateChangeClaimStatus(claim.SubmissionMethod, claim, request.Status, request.IdentityContext);
        if (!validationResult.IsSuccess)
        {
            return validationResult.Error;
        }

        var adjustedStatus = AdjustPaidStatuses(request.Status, claim);

        // Update the claim status and set status reason to null
        // since it's a manual status update
        var result = await insuranceClaimsRepository.UpdateStatus(
            request.Id,
            request.ProviderId,
            adjustedStatus,
            null,
            cancellationToken
        );

        if (result is null) return InsuranceErrors.ClaimFailedToUpdateStatus;

        await SaveHistory(result, request.IdentityContext, cancellationToken);

        return Unit.Value;
    }

    private ValidationResult ValidateChangeClaimStatus(ClaimSubmissionMethod submissionMethod, InsuranceClaim claim, ClaimStatus toStatus, IIdentityContext identityContext)
    {
        var fromStatus = claim.Status;
        if (toStatus == ClaimStatus.Paid || toStatus == ClaimStatus.PartiallyPaid)
        {
            return claim.BalancePaid <= 0 ? ValidationResult.Failure(InsuranceErrors.ClaimHasNoAllocatedPayments) : ValidationResult.Success();
        }
        
        if (submissionMethod is ClaimSubmissionMethod.Electronic)
        {
            if (identityContext.Email.IsInternalEmail)
            {
                return toStatus == ClaimStatus.Draft 
                    ? ValidationResult.Success()
                    : ValidationResult.Failure(InsuranceErrors.ClaimStatusCannotBeChangedForElectronicClaims);
            }

            return (fromStatus == ClaimStatus.Rejected || fromStatus == ClaimStatus.Validated) && toStatus == ClaimStatus.Draft
                ? ValidationResult.Success()
                : ValidationResult.Failure(InsuranceErrors.ClaimStatusCannotBeChangedForElectronicClaims);
        }

        return ValidationResult.Success();
    }

    private ClaimStatus AdjustPaidStatuses(ClaimStatus requestStatus, InsuranceClaim claim)
    {
        if (requestStatus == ClaimStatus.Paid || requestStatus == ClaimStatus.PartiallyPaid)
        {
            if (claim.BalancePaid >= 0)
            {
                if (claim.BalancePaid >= claim.BalanceDue)
                {
                    return ClaimStatus.Paid;
                }

                if (claim.BalancePaid < claim.BalanceDue)
                {
                    return ClaimStatus.PartiallyPaid;
                }
            }
        }
        return requestStatus;
    }

    private async Task SaveHistory(
        InsuranceClaim claim,
        IIdentityContext identityContext,
        CancellationToken cancellationToken
    )
    {
        var person = await personRepository.Get(identityContext.PersonId);

        var history = claim.ToClaimStatusChangedHistory([EntityHistoryActor.Create(person)]);
        await entityHistoryRepository.Create(history, cancellationToken);
    }
}