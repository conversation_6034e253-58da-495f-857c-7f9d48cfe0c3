using carepatron.core.Application.History.Models;
using carepatron.core.Application.Insurance.Extensions;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Insurance.Services;
using carepatron.core.Constants;
using carepatron.core.Identity;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.ClearingHouse;
using carepatron.core.Repositories.History;
using carepatron.core.Repositories.Insurance;
using carepatron.core.Repositories.Person;
using carepatron.core.Utilities;
using FluentValidation;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Insurance.Commands;

public record SubmitUSProfessionalClaimCommand(
    Guid Id,
    Guid ProviderId,
    ClaimSubmissionMethod SubmissionMethod,
    IIdentityContext IdentityContext
) : IMediatrCommand<Unit>;

public class SubmitUSProfessionalClaimCommandValidator
    : AbstractValidator<SubmitUSProfessionalClaimCommand>
{
    public SubmitUSProfessionalClaimCommandValidator()
    {
        RuleFor(x => x.ProviderId)
            .NotEmpty()
            .WithErrorCode(Errors.ProviderIdRequiredCode)
            .WithMessage(Errors.ProviderIdRequiredDetail);
        RuleFor(x => x.Id)
            .NotEmpty()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(SubmitUSProfessionalClaimCommand.Id)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(SubmitUSProfessionalClaimCommand.Id)));
    }
}

public class SubmitUSProfessionalClaimCommandHandler(
    IInsuranceClaimUSProfessionalsRepository usProfessionalClaimsRepository,
    IUSProfessionalClaimService usProfessionalClaimService,
    IClearingHouseIdLookupRepository clearingHouseIdLookupRepository,
    IEntityHistoryRepository entityHistoryRepository,
    IPersonRepository personRepository
) : IMediatrCommandHandler<SubmitUSProfessionalClaimCommand, Unit>
{
    public async Task<ExecutionResult<Unit>> Handle(SubmitUSProfessionalClaimCommand request, CancellationToken cancellationToken)
    {
        var claim = await usProfessionalClaimsRepository.GetById(request.Id, request.ProviderId, cancellationToken);
        if (claim is null) return InsuranceErrors.ClaimNotFound;

        var isElectronic = request.SubmissionMethod == ClaimSubmissionMethod.Electronic;
        if (isElectronic)
        {
            var error = await usProfessionalClaimService.ValidateClaimForElectronicSubmission(claim, cancellationToken);
            if (error is not null) return error;

            // TEMP. Clear/Reset ClientControlNumber if it is invalid.
            if (string.IsNullOrEmpty(claim.ClientControlNumber) || !long.TryParse(claim.ClientControlNumber, out var _))
            {
                claim.ClientControlNumber = (await clearingHouseIdLookupRepository.GetOrAdd(claim.ContactId, cancellationToken)).ToString();
            }
        }

        var submissionResult = await usProfessionalClaimService.Submit(
            claim,
            request.SubmissionMethod,
            cancellationToken
        );
        if (submissionResult.Errors.Length > 0)
        {
            return InsuranceErrors.ClaimFailedToValidate(submissionResult.Errors);
        }

        claim.SetStatus(ClaimStatus.Submitted);
        claim.SetSubmissionMethod(request.SubmissionMethod);

        var updateResult = await usProfessionalClaimsRepository.Update(
            claim,
            cancellationToken
        );

        await SaveHistory(claim, request.IdentityContext, submissionResult.Metadata, cancellationToken);

        return updateResult is null ? InsuranceErrors.ClaimFailedToUpdateStatus : Unit.Value;
    }

    private async Task SaveHistory(
        InsuranceClaimUSProfessional claim,
        IIdentityContext identityContext,
        InsuranceClaimClearingHouseMetadata metadata,
        CancellationToken cancellationToken = default
    )
    {
        var person = await personRepository.Get(identityContext.PersonId);


        var history = claim.SubmissionMethod == ClaimSubmissionMethod.Electronic
            ? claim.ToClaimSubmittedHistory([EntityHistoryActor.Create(person)], metadata)
            : claim.ToClaimStatusChangedHistory([EntityHistoryActor.Create(person)]);
        await entityHistoryRepository.Create(history, cancellationToken);
    }
}
