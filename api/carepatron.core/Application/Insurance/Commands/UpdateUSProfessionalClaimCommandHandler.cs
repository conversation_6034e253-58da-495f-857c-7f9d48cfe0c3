using carepatron.core.Application.Insurance.Events;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Insurance.Services;
using carepatron.core.Identity;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.ClearingHouse;
using carepatron.core.Repositories.Insurance;
using carepatron.core.Utilities;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Insurance.Commands;

public record UpdateUSProfessionalClaimCommand(
    Guid Id,
    Guid ProviderId,
    ClaimStatus Status,
    ClaimSubmissionMethod SubmissionMethod,
    string ResubmissionCode,
    string OriginalReferenceNumber,
    string PatientsAccountNumber,
    string PriorAuthorizationNumber,
    decimal AmountPaid,
    bool Lab,
    decimal? LabCharges,
    string AdditionalClaimInformation,
    ClaimClient Client,
    ClaimIncident Incident,
    ClaimFacility ServiceFacility,
    ClaimServiceLine[] ServiceLines,
    ClaimReferringProvider[] ReferringProviders,
    ClaimRenderingProvider[] RenderingProviders,
    ClaimDiagnosticCode[] DiagnosticCodes,
    ClaimBillingDetail BillingDetail,
    ClaimContactInsurancePolicy ContactInsurancePolicy,
    IIdentityContext IdentityContext
) : IMediatrCommand<InsuranceClaimUSProfessional>;

public class UpdateUSProfessionalClaimCommandHandler(
    IInsuranceClaimUSProfessionalsRepository insuranceClaimUSProfessionalsRepository,
    IUSProfessionalClaimService usProfessionalClaimService,
    IClearingHouseIdLookupRepository clearingHouseIdLookupRepository,
    IInsuranceService insuranceService,
    IIntegrationEventPublisher eventPublisher
) : IMediatrCommandHandler<UpdateUSProfessionalClaimCommand, InsuranceClaimUSProfessional>
{
    public async Task<ExecutionResult<InsuranceClaimUSProfessional>> Handle(UpdateUSProfessionalClaimCommand request, CancellationToken cancellationToken)
    {
        var claim = await insuranceClaimUSProfessionalsRepository.GetById(request.Id, request.ProviderId, cancellationToken);
        if (claim is null) return ValidationError.NotFound;

        var previous = (USProfessionalClaimEventModel)claim;

        // Update claim values
        claim.ResubmissionCode = request.ResubmissionCode;
        claim.OriginalReferenceNumber = request.OriginalReferenceNumber;
        claim.PatientsAccountNumber = request.PatientsAccountNumber;
        claim.PriorAuthorizationNumber = request.PriorAuthorizationNumber;
        claim.ProviderId = request.ProviderId;
        claim.Lab = request.Lab;
        claim.LabCharges = request.LabCharges;
        claim.AdditionalClaimInformation = request.AdditionalClaimInformation;
        claim.Client = request.Client;
        claim.Incident = request.Incident;
        claim.ServiceFacility = request.ServiceFacility;
        claim.ServiceLines = request.ServiceLines;
        claim.ReferringProviders = request.ReferringProviders;
        claim.RenderingProviders = request.RenderingProviders;
        claim.DiagnosticCodes = request.DiagnosticCodes;
        claim.BillingDetail = request.BillingDetail;
        claim.ContactInsurancePolicy = request.ContactInsurancePolicy;
        claim.UpdatedDateTimeUtc = DateTime.UtcNow;
        claim.PrintId = string.Empty;
        claim.ExportId = string.Empty;
        claim.IsPrintValid = false;
        claim.IsExportValid = false;
        claim.SetClaimDates();
        claim.SetAmount();

        // Set client control number if not set
        // This is only set during claim creation
        if (string.IsNullOrEmpty(claim.ClientControlNumber) || !long.TryParse(claim.ClientControlNumber, out var _))
        {
            claim.ClientControlNumber = (await clearingHouseIdLookupRepository.GetOrAdd(claim.ContactId, cancellationToken)).ToString();
        }

        await PopulateTasks(claim, cancellationToken);

        await PopulateAmountPaid(claim, previous, request.AmountPaid);

        // Set submission method
        var submissionMethod = await usProfessionalClaimService.GetClaimSubmissionMethod(
            claim.ProviderId,
            claim.ContactInsurancePolicy?.PayerId,
            cancellationToken
        );
        claim.SetSubmissionMethod(submissionMethod);

        var result = await insuranceClaimUSProfessionalsRepository.Update(claim, cancellationToken);

        eventPublisher.Add(
            new OutgoingEvent<USProfessionalClaimUpdatedEvent>(
                new USProfessionalClaimUpdatedEvent(
                    claim,
                    previous
                ))
        );

        return result;
    }

    private async Task PopulateAmountPaid(InsuranceClaimUSProfessional claim, USProfessionalClaimEventModel previous, decimal requestAmountPaid)
    {
        if (claim.AmountPaid == requestAmountPaid)
        {
            var previousServiceLineIds = (previous.ServiceLines?.Select(x => x.Id) ?? []).Order();
            var currentServiceLineIds = (claim.ServiceLines?.Select(x => x.Id) ?? []).Order();
            if (!previousServiceLineIds.SequenceEqual(currentServiceLineIds))
            {
                // Recalculate the amount paid based on the current service lines
                claim.AmountPaid = await insuranceService.GetAmountPaidFromBillableItems(claim);
                return;
            }
        }

        claim.SetAmountPaid(requestAmountPaid);
    }

    private async Task PopulateTasks(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken)
    {
        var tasks = await insuranceService.GetTasksForBillableItems(claim, cancellationToken);
        // Assign tasks to claim (can potentially just be an empty array)
        claim.Tasks = tasks;
    }
}
