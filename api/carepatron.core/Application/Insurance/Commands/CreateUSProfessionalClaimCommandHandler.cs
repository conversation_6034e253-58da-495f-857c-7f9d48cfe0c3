using carepatron.core.Abstractions;
using carepatron.core.Application.Billing.Models;
using carepatron.core.Application.Diagnoses.Utilities;
using carepatron.core.Application.History.Models;
using carepatron.core.Application.Insurance.Events;
using carepatron.core.Application.Insurance.Extensions;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Insurance.Services;
using carepatron.core.Extensions;
using carepatron.core.Identity;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Pagination;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Billables;
using carepatron.core.Repositories.Billing;
using carepatron.core.Repositories.Diagnoses;
using carepatron.core.Repositories.History;
using carepatron.core.Repositories.Insurance;
using carepatron.core.Repositories.Person;
using carepatron.core.Repositories.Provider;
using carepatron.core.Repositories.Tasks;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Repositories.ClearingHouse;

namespace carepatron.core.Application.Insurance.Commands;

public record CreateUSProfessionalClaimCommand(
    Guid ProviderId,
    ClaimStatus Status,
    ClaimSubmissionMethod SubmissionMethod,
    string ResubmissionCode,
    string OriginalReferenceNumber,
    string PatientsAccountNumber,
    string PriorAuthorizationNumber,
    decimal AmountPaid,
    bool Lab,
    decimal? LabCharges,
    string AdditionalClaimInformation,
    ClaimClient Client,
    ClaimIncident Incident,
    ClaimFacility ServiceFacility,
    ClaimServiceLine[] ServiceLines,
    ClaimReferringProvider[] ReferringProviders,
    ClaimRenderingProvider[] RenderingProviders,
    ClaimDiagnosticCode[] DiagnosticCodes,
    ClaimBillingDetail BillingDetail,
    ClaimContactInsurancePolicy ContactInsurancePolicy,
    IIdentityContext IdentityContext
) : IMediatrCommand<InsuranceClaimUSProfessional>;

public class CreateUSProfessionalClaimCommandHandler(
    IUnitOfWork unitOfWork,
    IInsuranceClaimsRepository insuranceClaimsRepository,
    IClearingHouseIdLookupRepository clearingHouseIdLookupRepository,
    IInsuranceClaimUSProfessionalsRepository insuranceClaimUSProfessionalsRepository,
    IDiagnosisRepository diagnosisRepository,
    IBillableRepository billableRepository,
    ITaskRepository taskRepository,
    IProviderStaffRepository providerStaffRepository,
    IContactInsurancePolicyRepository insurancePolicyRepository,
    IProviderBillingProfilesRepository providerBillingProfilesRepository,
    IUSProfessionalClaimService usProfessionalClaimService,
    IInsuranceService insuranceService,
    IIntegrationEventPublisher eventPublisher,
    IPersonRepository personRepository,
    IEntityHistoryRepository entityHistoryRepository
) : IMediatrCommandHandler<CreateUSProfessionalClaimCommand, InsuranceClaimUSProfessional>
{
    public async Task<ExecutionResult<InsuranceClaimUSProfessional>> Handle(CreateUSProfessionalClaimCommand request, CancellationToken cancellationToken)
    {
        var claim = new InsuranceClaimUSProfessional
        {
            Id = Guid.NewGuid(),
            Status = request.Status,
            Type = ClaimType.USProfessional,
            ProviderId = request.ProviderId,
            ResubmissionCode = request.ResubmissionCode,
            OriginalReferenceNumber = request.OriginalReferenceNumber,
            PatientsAccountNumber = request.PatientsAccountNumber,
            PriorAuthorizationNumber = request.PriorAuthorizationNumber,
            AmountPaid = request.AmountPaid,
            Lab = request.Lab,
            LabCharges = request.LabCharges,
            AdditionalClaimInformation = request.AdditionalClaimInformation,
            ContactId = request.Client.Contact.Id,
            Client = request.Client,
            Incident = request.Incident,
            ServiceFacility = request.ServiceFacility,
            ServiceLines = request.ServiceLines,
            ReferringProviders = request.ReferringProviders,
            RenderingProviders = request.RenderingProviders,
            DiagnosticCodes = request.DiagnosticCodes,
            BillingDetail = request.BillingDetail,
            ContactInsurancePolicy = request.ContactInsurancePolicy
        };

        // Get task ids from billable items
        await PopulateTasks(claim, cancellationToken);

        // Populate claim number
        await PopulateClaimNumber(claim);

        // todo
        // we need to set the fallback date as a local date only
        // currently this can potentially 'drift' when creating a claim at the very start and/or end of a month.
        claim.SetClaimDates(DateTime.UtcNow);
        claim.SetAmount();

        // Populate child properties if needed
        await PopulateContactRelatedInfo(claim, cancellationToken);
        await PopulateDiagnoses(claim, cancellationToken);
        await PopulateRenderingProviders(claim, cancellationToken);
        await PopulateClaimFacility(claim, cancellationToken);
        await PopulateClaimInsurancePolicy(claim, cancellationToken);
        await PopulateAmountPaid(claim, cancellationToken);

        // Make sure we populate billing details after populating the insurance policy and
        // rendering providers since we need the payer id and staff to get the billing profile
        await PopulateBillingDetails(claim, cancellationToken);

        // Populate submission method. Important that this goes last
        // so billing details, payers and contact insurance policy is all set.
        var submissionMethod = await usProfessionalClaimService.GetClaimSubmissionMethod(
            claim.ProviderId,
            claim.ContactInsurancePolicy?.PayerId,
            cancellationToken
        );
        claim.SetSubmissionMethod(submissionMethod);

        var result = await insuranceClaimUSProfessionalsRepository.Create(claim, cancellationToken);

        await SaveHistory(claim, request.IdentityContext, cancellationToken);

        await unitOfWork.SaveUnitOfWork(cancellationToken);

        eventPublisher.Add(
            new OutgoingEvent<USProfessionalClaimCreatedEvent>(new(result))
        );

        return result;
    }

    private async Task PopulateClaimNumber(InsuranceClaimUSProfessional claim)
    {
        var number = await insuranceClaimsRepository.GetNextNumber(claim.ProviderId);
        var numberExists = await insuranceClaimsRepository.DoesNumberExist(claim.ProviderId, number);
        if (numberExists)
        {
            throw InsuranceErrors.ClaimNumberAlreadyExists.AsException();
        }
        claim.Number = number;
    }

    private async Task PopulateTasks(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken)
    {
        var tasks = await insuranceService.GetTasksForBillableItems(claim, cancellationToken);
        // Assign tasks to claim (can potentially just be an empty array)
        claim.Tasks = tasks;
    }

    private async Task PopulateAmountPaid(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken)
    {
        var amountPaid = await insuranceService.GetAmountPaidFromBillableItems(claim);
        claim.SetAmountPaid(amountPaid);
    }

    private async Task PopulateClaimInsurancePolicy(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken = default)
    {
        var activeDate = claim.FromDate ?? DateTime.UtcNow.ToDateOnly();
        var activePolicies = await insurancePolicyRepository.GetActivePolicies(claim.ProviderId, claim.ContactId, activeDate);
        if (activePolicies is null || activePolicies.Length == 0) return;

        var sortedPoliciesByDate = activePolicies
            .OrderByDescending(
                x => x.InsuranceType == InsuranceType.Primary
                    ? 0
                    : x.InsuranceType == InsuranceType.Secondary
                        ? 1 : 2
            )
            .ThenBy(x => x.PolicyStartDate)
            .ToArray();

        var latestPolicy = sortedPoliciesByDate.First();
        claim.ContactInsurancePolicy = ClaimContactInsurancePolicy.Create(claim.ProviderId, latestPolicy);
    }

    private async Task PopulateBillingDetails(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken)
    {
        // We grab the first provider for now
        var staff = claim.RenderingProviders?.FirstOrDefault()?.StaffMember;
        var payerId = claim.ContactInsurancePolicy?.PayerId;

        ProviderBillingProfile billingProfile = null;

        var billingProfiles = await providerBillingProfilesRepository.Get(claim.ProviderId, payerId, staff?.PersonId, new PaginationRequest(10, 0));
        if (billingProfiles?.Items is null || billingProfiles?.Items?.Count == 0)
        {
            return;
        }

        billingProfile = billingProfiles.Items.FirstOrDefault(s => !s.IsDefault) ?? billingProfiles.Items.First();
        claim.BillingDetail = ClaimBillingDetail.Create(claim.ProviderId, billingProfile);
    }

    private async Task PopulateDiagnoses(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken = default)
    {
        var contactId = claim.Client.Contact.Id;

        // If there are no added service line items, return immediately since
        // we have no reference to add diagnostic codes to
        if (claim.ServiceLines is null || claim.ServiceLines.Length == 0) return;

        // If practitioner hasn't added any diagnostic codes, we will add them for them
        // based on the claim service lines they've added
        var fromDate = claim.ServiceLines.Min(x => x.Date).ToDateTime(null, DateTimeKind.Utc);
        var toDate = claim.ServiceLines.Max(x => x.Date).ToDateTime(null, DateTimeKind.Utc);
        var contactDiagnosesItems = await diagnosisRepository.GetByDateRange(
            contactId,
            fromDate.ToStartOfDay(),
            toDate.ToEndOfDay()
        );
        var serviceLineDates = claim.ServiceLines.Select(sl => sl.Date).Distinct().ToArray();

        var (diagnosisItems, diagnosisPointers) = DiagnosisCodeUtilities.GetDiagnosisPointers(serviceLineDates, contactDiagnosesItems);

        // Assigned diagnostic codes to service lines based on dates
        foreach (var serviceLine in claim.ServiceLines)
        {
            if (diagnosisPointers.TryGetValue(serviceLine.Date, out var codes))
            {
                serviceLine.DiagnosticCodeReferences = codes;
            }
        }

        claim.DiagnosticCodes = diagnosisItems.Select(s => ClaimDiagnosticCode.Create(claim.ProviderId, s)).ToArray();
    }

    private async Task PopulateClaimFacility(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken = default)
    {
        if (claim.Tasks is null || claim.Tasks.Length == 0) return;

        var taskId = claim.Tasks.First().TaskId;
        var mappedTask = await taskRepository.Get(claim.ProviderId, taskId);
        if (mappedTask is null) return;

        var location = mappedTask.LocationsSnapshot.FirstOrDefault();
        if (location is null) return;

        claim.ServiceFacility = ClaimFacility.Create(claim.ProviderId, location);
    }

    private async Task PopulateRenderingProviders(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken = default)
    {
        var contactId = claim.Client.Contact.Id;

        // Get billable item ids from service lines
        var billableItemIds = claim.ServiceLines?
            .Where(x => x.BillableItemId.HasValue)
            .Select(x => x.BillableItemId.Value)
            .ToArray() ?? [];
        if (billableItemIds.Length == 0) return;

        // Get task ids from billable items
        var taskIds = await billableRepository.GetTaskIdsByBillableItemId(billableItemIds, cancellationToken);
        if (taskIds.Length == 0) return;

        var tasks = await taskRepository.GetTasks(taskIds);
        if (tasks.Length == 0) return;

        // Get staff assigned on the tasks from the billables
        var staffIds = tasks.SelectMany(x => x.StaffIds).ToArray();
        if (staffIds.Length == 0) return;

        var staff = await providerStaffRepository.GetProviderStaff(claim.ProviderId, staffIds);

        var renderingProviders = new List<ClaimRenderingProvider>();
        var staffOrderIndex = 0;

        foreach (var staffPerson in staff)
        {
            // If the staff member is already in the rendering providers list, skip
            // adding the staff as a rendering provider
            var personIds = renderingProviders.Select(x => x.StaffMember.PersonId).ToList();
            if (!personIds.Contains(staffPerson.PersonId))
            {
                var claimProviderStaff = new ClaimProviderStaff(staffPerson);
                var person = tasks
                    .Where(s => s.StaffIds.Contains(staffPerson.PersonId))
                    .SelectMany(s => s.Staff)
                    .Where(s => s.Id == staffPerson.PersonId)
                    .FirstOrDefault();
                renderingProviders.Add(ClaimRenderingProvider.Create(
                    claim.ProviderId,
                    staffPerson.NationalProviderId,
                    staffOrderIndex++,
                    person,
                    claimProviderStaff
                ));
            }
        }

        claim.RenderingProviders = [.. renderingProviders];
    }

    private async Task PopulateContactRelatedInfo(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken = default)
    {
        claim.ClientControlNumber = (await clearingHouseIdLookupRepository.GetOrAdd(claim.ContactId, cancellationToken)).ToString();
    }

    private async Task SaveHistory(
        InsuranceClaimUSProfessional claim,
        IIdentityContext identityContext,
        CancellationToken cancellationToken
    )
    {
        var person = await personRepository.Get(identityContext.PersonId);

        var history = claim.ToClaimCreatedHistory([EntityHistoryActor.Create(person)]);
        await entityHistoryRepository.Create(history, cancellationToken);
    }
}
