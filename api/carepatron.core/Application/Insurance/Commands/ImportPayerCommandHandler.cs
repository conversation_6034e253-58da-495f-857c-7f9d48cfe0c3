using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Insurance;

namespace carepatron.core.Application.Insurance.Commands;

public record ImportInsurancePayersCommand(Guid ProviderId, ClearingHousePayer[] Payers) : IMediatrCommand<ProviderInsurancePayer[]>;

public class ImportInsurancePayersCommandHandler(IInsurancePayerRepository insurancePayerRepository) : IMediatrCommandHandler<ImportInsurancePayersCommand, ProviderInsurancePayer[]>
{
    public async Task<ExecutionResult<ProviderInsurancePayer[]>> Handle(ImportInsurancePayersCommand request, CancellationToken cancellationToken)
    {
        var payers = await insurancePayerRepository.GetClearingHousePayers(request.ProviderId, request.Payers, cancellationToken);
        var payerIds = payers.Select(x => x.PayerId).ToArray();
        // get exsting payers having no clearing house type to update 
        var existingPayers = await insurancePayerRepository.GetById(request.ProviderId, ClearingHouseType.None, payerIds);
        var providerPayers = payers.Select(x => ProviderInsurancePayer.Create(request.ProviderId, existingPayers, x)).ToArray();
        
        // filter out existing payers that are already in the provider's list
        var createdPayers = providerPayers.Where(x => !existingPayers.ContainsKey(x.PayerId)).ToArray();
        // update existing payers that have no clearing house type
        var updatedPayers = providerPayers.Where(x => existingPayers.ContainsKey(x.PayerId) && existingPayers[x.PayerId].ClearingHouse == ClearingHouseType.None).ToArray();
   
        var createdResult = await insurancePayerRepository.ImportPayers(createdPayers, updatedPayers, cancellationToken);
        
        return createdResult.Concat(updatedPayers).ToArray();
    }
}