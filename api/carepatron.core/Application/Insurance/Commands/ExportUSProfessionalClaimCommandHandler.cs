using carepatron.core.Application.Files.Models;
using carepatron.core.Application.History.Models;
using carepatron.core.Application.Insurance.Extensions;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Insurance.Services;
using carepatron.core.Application.Pdf.Services;
using carepatron.core.Identity;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Files;
using carepatron.core.Repositories.History;
using carepatron.core.Repositories.Insurance;
using carepatron.core.Repositories.Person;
using Serilog.Context;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Insurance.Commands;

public record ExportUSProfessionalClaimCommand(
    Guid Id,
    string FileKey,
    IIdentityContext IdentityContext
) : IMediatrCommand<ExportPrintUSProfessionalClaimResponse>;

public class ExportUSProfessionalClaimCommandHandler(
    IInsuranceClaimUSProfessionalsRepository insuranceClaimUSProfessionalsRepository,
    IFileStorageRepository fileStorageRepository,
    IPdfUtilsService pdfUtilsService,
    IUSProfessionalClaimService usProfessionalClaimService,
    IPersonRepository personRepository,
    IEntityHistoryRepository entityHistoryRepository
) : IMediatrCommandHandler<ExportUSProfessionalClaimCommand, ExportPrintUSProfessionalClaimResponse>
{
    public async Task<ExecutionResult<ExportPrintUSProfessionalClaimResponse>> Handle(ExportUSProfessionalClaimCommand request, CancellationToken cancellationToken)
    {
        using (LogContext.PushProperty(
            nameof(ExportUSProfessionalClaimCommandHandler),
            new { ClaimId = request.Id, request.IdentityContext.ProviderPermissions.ProviderId },
            true
        ))
        {
            var claim = await insuranceClaimUSProfessionalsRepository.GetById(request.Id, request.IdentityContext.ProviderPermissions.ProviderId, cancellationToken);
            if (claim is null) return InsuranceErrors.ClaimNotFound;

            if (!string.IsNullOrEmpty(request.FileKey))
            {
                return GetExportResponse(request.FileKey, ClaimFormConstants.ClaimExportPrintMetadataCMS1500, claim);
            }

            if (!string.IsNullOrEmpty(claim.ExportId) && claim.IsExportValid)
            {
                var existingFile = await usProfessionalClaimService.GetExportOrPrintFileInfo(claim.ExportId);
                if (existingFile is not null)
                {
                    return GetExportResponse(claim.ExportId, ClaimFormConstants.ClaimExportPrintMetadataCMS1500, claim);
                }
            }

            var fillFormRequest = await usProfessionalClaimService.GetClaimFillFormRequest(
                claim,
                ClaimFormConstants.Cms1500SourceFileKey,
                $"{claim.Id}_export_{DateTimeOffset.UtcNow.ToUnixTimeSeconds()}.pdf",
                request.IdentityContext.PersonId
            );

            var fillFormResponse = await pdfUtilsService.FillPdfForm(fillFormRequest, cancellationToken);

            claim.ExportId = fillFormResponse.Key;
            claim.IsExportValid = true;
            await insuranceClaimUSProfessionalsRepository.Update(claim, cancellationToken);
            await SaveHistory(claim, request.IdentityContext, cancellationToken);
            return GetExportResponse(fillFormResponse.Key, ClaimFormConstants.ClaimExportPrintMetadataCMS1500, claim);
        }
    }

    private ExecutionResult<ExportPrintUSProfessionalClaimResponse> GetExportResponse(
        string key,
        string type,
        InsuranceClaimUSProfessional claim
    )
    {
        var signedUrl = fileStorageRepository.GeneratePresignedUrl(
            key,
            FileLocationType.Files,
            usProfessionalClaimService.GenerateExportOrPrintFileName(claim, type, "export"),
            true,
            ClaimFormConstants.ExportedCms1500VersionExpirationMinutes
        );
        if (string.IsNullOrEmpty(signedUrl)) return InsuranceErrors.ClaimFailedToGenerateExportUrl;
        return new ExportPrintUSProfessionalClaimResponse(signedUrl);
    }

    private async Task SaveHistory(
        InsuranceClaimUSProfessional claim,
        IIdentityContext identityContext,
        CancellationToken cancellationToken
    )
    {
        var person = await personRepository.Get(identityContext.PersonId);

        var history = claim.ToClaimExportedHistory([EntityHistoryActor.Create(person)]);
        await entityHistoryRepository.Create(history, cancellationToken);
    }
}
