using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Insurance.Models.ClearingHouse;
using carepatron.core.Application.Insurance.Services.ClearingHouse;
using carepatron.core.Application.Insurance.Utilities;
using carepatron.core.Application.Pdf.Models;
using carepatron.core.Constants;
using carepatron.core.Models.Common;
using carepatron.core.Models.Execution;
using carepatron.core.Repositories.Files;
using carepatron.core.Repositories.Insurance;
using carepatron.core.Repositories.Person;
using carepatron.core.Utilities;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Insurance.Services;

public interface IUSProfessionalClaimService
{
    Task<FillFormRequest> GetClaimFillFormRequest(InsuranceClaimUSProfessional claim, string fileKey, string destinationFileKey, Guid currentPersonId);
    Task<FileInfo> GetExportOrPrintFileInfo(string key);
    Task<FieldValidationError[]> Validate(InsuranceClaimUSProfessional claim, ClaimSubmissionMethod submissionMethod, CancellationToken cancellationToken);
    Task<FieldValidationError[]> ValidateElectronicClaim(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken);
    Task<ClaimSubmissionResult> Submit(InsuranceClaimUSProfessional claim, ClaimSubmissionMethod submissionMethod, CancellationToken cancellationToken);
    Task<ClaimSubmissionResult> SubmitElectronicClaim(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken);
    string GenerateExportOrPrintFileName(InsuranceClaimUSProfessional claim, string type, string suffix = null);
    Task<ValidationError> ValidateClaimForElectronicSubmission(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken);
    Task<ClaimSubmissionMethod> GetClaimSubmissionMethod(Guid providerId, Guid? payerId, CancellationToken cancellationToken = default);
    Task<ValidationError> IsClaimValidForElectronicProcessing(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken);
}

public class USProfessionalClaimService(
    IFileStorageRepository fileStorageRepository,
    IContactInsuranceSettingsRepository contactInsuranceSettingsRepository,
    IInsurancePayerRepository insurancePayerRepository,
    IInsuranceClaimClearingHouseMetadataRepository insuranceClaimClearingHouseMetadataRepository,
    IInsuranceClaimErrorsRepository insuranceClaimErrorsRepository,
    IClearingHouseServiceResolver clearingHouseServiceResolver,
    IPersonRepository personRepository
) : IUSProfessionalClaimService
{
    public async Task<FillFormRequest> GetClaimFillFormRequest(InsuranceClaimUSProfessional claim, string fileKey, string destinationFileKey, Guid currentPersonId)
    {
        if (claim is null) throw new ArgumentException("Claim not provided when generating fill claim form request");
        if (string.IsNullOrEmpty(fileKey)) throw new ArgumentException("File key not provided when generating fill claim form request");

        var exportMetadata = new Dictionary<string, string>
        {
            { "Type", "Claim" },
            { "ProviderId", claim.ProviderId.ToString() },
            { "ClaimId", claim.Id.ToString() },
            { "ContactId", claim.ContactId.ToString() }
        };
        var sourceBucket = fileStorageRepository.GetClientBucketName(FileLocationType.InternalBlobStorage);
        var destinationBucket = fileStorageRepository.GetClientBucketName(FileLocationType.Files);
        var content = new FillFormRequest
        {
            ExportMetadata = exportMetadata,
            FileLocation = new FileLocationDetails
            {
                SourceBucket = sourceBucket,
                SourceKey = fileKey,
                DestinationBucket = destinationBucket,
                DestinationKey = destinationFileKey
            },
            Fields = []
        };
        SetClientInfo(claim.Client, content);
        SetInsuranceDetails(claim.ContactInsurancePolicy, content);
        SetIncidentDetails(claim.Incident, content);
        SetReferringProviderDetails(claim.ReferringProviders, content);
        SetRenderingProviderDetails(claim.RenderingProviders, claim.ServiceLines ?? [], content);
        SetDiagnosticCodeDetails(claim.DiagnosticCodes, content);
        SetServiceLineDetails(claim.ServiceLines, claim.DiagnosticCodes, content);
        SetServiceFacilityDetails(claim.ServiceFacility, content);
        SetBillingProvider(claim.BillingDetail, content);
        await SetMiscDetails(claim, content, currentPersonId);

        return content;
    }

    public async Task<FileInfo> GetExportOrPrintFileInfo(string key)
    {
        try
        {
            var existingFile = await fileStorageRepository.GetFileInfo(key, FileLocationType.Files);
            return existingFile;
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error getting file info for key {Key}", key);
            return null;
        }
    }

    public string GenerateExportOrPrintFileName(InsuranceClaimUSProfessional claim, string type, string suffix = null)
    {
        var fileNameSuffix = string.IsNullOrEmpty(suffix) ? string.Empty : $"_{suffix}";
        var name = $"{type}_{claim.Number}_{DateTime.UtcNow:yyyy-MM-dd}{fileNameSuffix}";
        return $"{name}.pdf";
    }

    private static void SetClientInfo(ClaimClient client, FillFormRequest content)
    {
        if (client is null) return;

        var birthDate = ClaimFormUtilities.GetDateOnlyValue(client.DateOfBirth.GetValueOrDefault());

        if (client.Address is not null)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(client.Address.StreetAddress?.Replace(",", string.Empty), ClaimFormFieldMapping.PatientStreet));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(client.Address.City, ClaimFormFieldMapping.PatientCity));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(AddressUtilities.GetUSState(client.Address.State), ClaimFormFieldMapping.PatientState));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(client.Address.ZipCode, ClaimFormFieldMapping.PatientZip));
        }

        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetFullName(client.FirstName, client.MiddleName, client.LastName), ClaimFormFieldMapping.PatientName));

        if (birthDate is not null)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(birthDate.Value.Day), ClaimFormFieldMapping.BirthDay));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(birthDate.Value.Month), ClaimFormFieldMapping.BirthMonth));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetYearString(birthDate.Value.Year), ClaimFormFieldMapping.BirthYear));
        }

        if (!string.IsNullOrEmpty(client.PhoneNumberDetails?.Number))
        {
            var fullNumber = client.PhoneNumberDetails.GetNumberOnly();
            var (areaCode, number) = ClaimFormUtilities.GetPhoneNumberParts(fullNumber);
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(number, ClaimFormFieldMapping.PatientPhone));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(areaCode, ClaimFormFieldMapping.PatientAreaCode));
        }

        if (client.Sex == Sex.Male)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.SexMale));
        }
        else if (client.Sex == Sex.Female)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.SexFemale));
        }
    }

    private static void SetInsuranceDetails(ClaimContactInsurancePolicy insurancePolicy, FillFormRequest content)
    {
        if (insurancePolicy is null) return;

        var policy = insurancePolicy.ContactInsurancePolicy;
        var payer = policy?.Payer;
        var policyHolderName = ClaimFormUtilities.GetFullName(insurancePolicy.PolicyHolderFirstName, insurancePolicy.PolicyHolderMiddleName, insurancePolicy.PolicyHolderLastName);
        var birthDate = ClaimFormUtilities.GetDateOnlyValue(insurancePolicy.PolicyHolderDateOfBirth);
        var insuranceCityStateZip = insurancePolicy.Address != null
            ? $"{insurancePolicy.Address?.City} {AddressUtilities.GetUSState(insurancePolicy.Address?.State)} {insurancePolicy.Address?.ZipCode}"
            : $"{payer?.Address?.City} {AddressUtilities.GetUSState(payer?.Address?.State)} {payer?.Address?.ZipCode}";
        var policyHolderAddress = insurancePolicy?.PolicyHolderAddress ?? policy?.PolicyHolder?.Contact?.Address;

        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(insurancePolicy?.PayerName ?? payer?.Name, ClaimFormFieldMapping.InsuranceName));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField((insurancePolicy.Address.StreetAddress ?? payer?.Address?.StreetAddress)?.Replace(",", string.Empty), ClaimFormFieldMapping.InsuranceAddress));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(string.Empty, ClaimFormFieldMapping.InsuranceAddress2));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(insuranceCityStateZip, ClaimFormFieldMapping.InsuranceCityStateZip));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(policyHolderName, ClaimFormFieldMapping.InsuredName));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(policyHolderAddress?.StreetAddress?.Replace(",", string.Empty), ClaimFormFieldMapping.InsuredStreet));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(policyHolderAddress?.City, ClaimFormFieldMapping.InsuredCity));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(AddressUtilities.GetUSState(policyHolderAddress?.State), ClaimFormFieldMapping.InsuredState));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(policyHolderAddress?.ZipCode, ClaimFormFieldMapping.InsuredZip));

        if (!string.IsNullOrEmpty(insurancePolicy.PolicyHolderPhoneNumberDetails?.Number))
        {
            var fullNumber = insurancePolicy.PolicyHolderPhoneNumberDetails.GetNumberOnly();
            var (areaCode, number) = ClaimFormUtilities.GetPhoneNumberParts(fullNumber);
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(number, ClaimFormFieldMapping.InsuredPhone));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(areaCode, ClaimFormFieldMapping.InsuredPhoneArea));
        }

        if (birthDate is not null)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(birthDate.Value.Day), ClaimFormFieldMapping.InsuredDOBDay));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(birthDate.Value.Month), ClaimFormFieldMapping.InsuredDOBMonth));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetYearString(birthDate.Value.Year), ClaimFormFieldMapping.InsuredDOBYear));
        }

        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(insurancePolicy.PolicyHolderGroupId ?? policy.GroupId, ClaimFormFieldMapping.InsurancePolicy));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(insurancePolicy.PolicyHolderMemberId ?? policy.MemberId, ClaimFormFieldMapping.InsuranceId));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(insurancePolicy?.PayerName ?? payer?.Name, ClaimFormFieldMapping.InsurancePlanName));

        var sex = insurancePolicy.PolicyHolderSex;
        if (sex == Sex.Male)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.InsuredSexMale));
        }
        else if (sex == Sex.Female)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.InsuredSexFemale));
        }

        var relationship = policy?.PolicyHolder?.Type;
        if (relationship == InsurancePolicyHolderType.Client)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.RelationshipToInsuredSelf));
        }
        else if (relationship == InsurancePolicyHolderType.Spouse)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.RelationshipToInsuredSpouse));
        }
        else if (relationship == InsurancePolicyHolderType.Parent)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.RelationshipToInsuredChild));
        }
        else if (relationship == InsurancePolicyHolderType.Other)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.RelationshipToInsuredOther));
        }

        var coverageType = insurancePolicy.CoverageType;
        if (coverageType == InsuranceCoverageType.Medicare)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.InsuranceTypeMedicare));
        }
        else if (coverageType == InsuranceCoverageType.Medicaid)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.InsuranceTypeMedicaid));
        }
        else if (coverageType == InsuranceCoverageType.CHAMPVA)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.InsuranceTypeChampVA));
        }
        else if (coverageType == InsuranceCoverageType.TRICARE)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.InsuranceTypeTricare));
        }
        else if (coverageType == InsuranceCoverageType.GroupHealthPlan)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.InsuranceTypeGroupHealthPlan));
        }
        else if (coverageType == InsuranceCoverageType.FECABlackLung)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.InsuranceTypeFECA));
        }
        else
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.InsuranceTypeOther));
        }
    }

    private static void SetIncidentDetails(ClaimIncident claimIncident, FillFormRequest content)
    {
        if (claimIncident is null) return;

        if (claimIncident.IsConditionRelatedToEmployment)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.EmploymentYes));
        }
        else
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.EmploymentNo));
        }

        if (claimIncident.IsConditionRelatedToAutoAccident)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.PatientAutoAccidentYes));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claimIncident.AutoAccidentState, ClaimFormFieldMapping.AccidentPlace));
        }
        else
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.PatientAutoAccidentNo));
        }

        if (claimIncident.IsConditionRelatedToOtherAccident)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.OtherAccidentYes));
        }
        else
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.OtherAccidentNo));
        }

        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claimIncident.CurrentIllnessQualifier, ClaimFormFieldMapping.CurrentIllnessQualifier));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claimIncident.OtherAssociatedQualifier, ClaimFormFieldMapping.SimilarIllnessQualifier));

        var currentIllnessDate = ClaimFormUtilities.GetDateOnlyValue(claimIncident.CurrentIllnessDate.GetValueOrDefault());
        if (currentIllnessDate is not null)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(currentIllnessDate.Value.Day), ClaimFormFieldMapping.CurrentIllnessDay));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(currentIllnessDate.Value.Month), ClaimFormFieldMapping.CurrentIllnessMonth));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetYearString(currentIllnessDate.Value.Year), ClaimFormFieldMapping.CurrentIllnessYear));
        }

        var otherAssociatedDate = ClaimFormUtilities.GetDateOnlyValue(claimIncident.OtherAssociatedDate.GetValueOrDefault());
        if (otherAssociatedDate is not null)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(otherAssociatedDate.Value.Day), ClaimFormFieldMapping.SimilarIllnessDay));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(otherAssociatedDate.Value.Month), ClaimFormFieldMapping.SimilarIllnessMonth));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetYearString(otherAssociatedDate.Value.Year), ClaimFormFieldMapping.SimilarIllnessYear));
        }

        var unableToWorkDateFrom = ClaimFormUtilities.GetDateOnlyValue(claimIncident.UnableToWorkFrom.GetValueOrDefault());
        if (unableToWorkDateFrom is not null)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(unableToWorkDateFrom.Value.Day), ClaimFormFieldMapping.WorkFromDay));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(unableToWorkDateFrom.Value.Month), ClaimFormFieldMapping.WorkFromMonth));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetYearString(unableToWorkDateFrom.Value.Year), ClaimFormFieldMapping.WorkFromYear));
        }

        var unableToWorkDateTo = ClaimFormUtilities.GetDateOnlyValue(claimIncident.UnableToWorkTo.GetValueOrDefault());
        if (unableToWorkDateTo is not null)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(unableToWorkDateTo.Value.Day), ClaimFormFieldMapping.WorkEndDay));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(unableToWorkDateTo.Value.Month), ClaimFormFieldMapping.WorkEndMonth));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetYearString(unableToWorkDateTo.Value.Year), ClaimFormFieldMapping.WorkEndYear));
        }

        var hospitalizationDateFrom = ClaimFormUtilities.GetDateOnlyValue(claimIncident.HospitalizationFrom.GetValueOrDefault());
        if (hospitalizationDateFrom is not null)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(hospitalizationDateFrom.Value.Day), ClaimFormFieldMapping.HospitalFromDay));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(hospitalizationDateFrom.Value.Month), ClaimFormFieldMapping.HospitalFromMonth));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetYearString(hospitalizationDateFrom.Value.Year), ClaimFormFieldMapping.HospitalFromYear));
        }

        var hospitalizationDateTo = ClaimFormUtilities.GetDateOnlyValue(claimIncident.HospitalizationTo.GetValueOrDefault());
        if (hospitalizationDateTo is not null)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(hospitalizationDateTo.Value.Day), ClaimFormFieldMapping.HospitalEndDay));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(hospitalizationDateTo.Value.Month), ClaimFormFieldMapping.HospitalEndMonth));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetYearString(hospitalizationDateTo.Value.Year), ClaimFormFieldMapping.HospitalEndYear));
        }
    }

    private static void SetReferringProviderDetails(ClaimReferringProvider[] providers, FillFormRequest content)
    {
        if (providers is null || providers.Length == 0) return;

        var provider = providers.FirstOrDefault(s => s.IncludeReferrerInformation);
        if (provider is null) return;

        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetFullName(provider.FirstName, provider.MiddleName, provider.LastName, false), ClaimFormFieldMapping.ReferringPhysician));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(provider.NationalProviderId, ClaimFormFieldMapping.ReferringNpi));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetProviderQualifierTypeLabel(provider.Qualifier), ClaimFormFieldMapping.ReferringType));
        if (!string.IsNullOrEmpty(provider.OtherId))
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(provider.OtherIdQualifier, ClaimFormFieldMapping.ReferringOtherQualifier));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(provider.OtherId, ClaimFormFieldMapping.ReferringOtherId));
        }
    }

    private static void SetRenderingProviderDetails(ClaimRenderingProvider[] providers, ClaimServiceLine[] serviceLines, FillFormRequest content)
    {
        var isProvidersValid = providers is not null && providers.Length > 0;
        var isServiceLinesValid = serviceLines is not null && serviceLines.Length > 0;
        if (!isProvidersValid || !isServiceLinesValid) return;

        var limitedProviders = providers.Take(serviceLines.Length).ToList();
        // Add first provider to the list if there are less than service lines length
        if (limitedProviders.Count < serviceLines.Length)
        {
            for (var i = limitedProviders.Count; i < serviceLines.Length; i++)
            {
                limitedProviders.Add(providers[0]);
            }
        }

        for (var i = 0; i < limitedProviders.Count; i++)
        {
            var provider = limitedProviders[i];
            var providerIndex = i + 1;
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(provider.NationalProviderId, ClaimFormFieldMapping.GetServiceRenderingProviderNPI(providerIndex)));
            if (!string.IsNullOrEmpty(provider.OtherId))
            {
                content.Fields.Add(ClaimFormUtilities.GetPdfFormField(provider.OtherIdQualifier, ClaimFormFieldMapping.GetServiceRenderingProviderQualifier(providerIndex)));
                content.Fields.Add(ClaimFormUtilities.GetPdfFormField(provider.OtherId, ClaimFormFieldMapping.GetServiceRenderingProviderQualifierId(providerIndex)));
            }
        }
    }

    private static void SetDiagnosticCodeDetails(ClaimDiagnosticCode[] codes, FillFormRequest content)
    {
        if (codes is null || codes.Length == 0) return;

        var limitedCodes = codes.Take(12).ToList();

        for (var i = 0; i < limitedCodes.Count; i++)
        {
            var code = codes[i];
            var codeIndex = i + 1;
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(code.Code.Replace(".", ""), ClaimFormFieldMapping.GetDiagnosis(codeIndex)));
        }
    }

    private static void SetServiceLineDetails(ClaimServiceLine[] serviceLines, ClaimDiagnosticCode[] codes, FillFormRequest content)
    {
        if (serviceLines is null || serviceLines.Length == 0) return;

        var limitedServiceLines = serviceLines.Take(6).ToList();

        var totalCharge = 0.0M;

        for (var i = 0; i < limitedServiceLines.Count; i++)
        {
            var serviceLine = limitedServiceLines[i];
            var serviceLineIndex = i + 1;

            var date = ClaimFormUtilities.GetDateOnlyValue(serviceLine.Date);
            if (date is not null)
            {
                content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(date.Value.Day), ClaimFormFieldMapping.GetServiceFromDay(serviceLineIndex)));
                content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(date.Value.Month), ClaimFormFieldMapping.GetServiceFromMonth(serviceLineIndex)));
                content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetYearString(date.Value.Year, 2), ClaimFormFieldMapping.GetServiceFromYear(serviceLineIndex)));
                content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(date.Value.Day), ClaimFormFieldMapping.GetServiceEndDay(serviceLineIndex)));
                content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetPaddedNumber(date.Value.Month), ClaimFormFieldMapping.GetServiceEndMonth(serviceLineIndex)));
                content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormUtilities.GetYearString(date.Value.Year, 2), ClaimFormFieldMapping.GetServiceEndYear(serviceLineIndex)));
            }

            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(serviceLine.POSCode, ClaimFormFieldMapping.GetPlace(serviceLineIndex)));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(serviceLine.Code, ClaimFormFieldMapping.GetCpt(serviceLineIndex)));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(serviceLine.Units.ToString(), ClaimFormFieldMapping.GetUnits(serviceLineIndex)));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(serviceLine.SupplementalInfo, ClaimFormFieldMapping.GetServiceSupplemental(serviceLineIndex)));

            int chargeWhole = (int)serviceLine.Amount;
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(chargeWhole.ToString(), ClaimFormFieldMapping.GetServiceCharge(serviceLineIndex), "right"));
            int chargeCents = (int)((serviceLine.Amount - chargeWhole) * 100);
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(chargeCents.ToString(), ClaimFormFieldMapping.GetServiceChargeCents(serviceLineIndex)));

            var dxCodes = ClaimFormUtilities.GetDxCodes(serviceLine.DiagnosticCodeReferences, [.. codes]);
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(dxCodes, ClaimFormFieldMapping.GetDiagnosisCode(serviceLineIndex)));

            var modifiers = serviceLine.Modifiers.Take(4).ToList();
            for (var j = 0; j < modifiers.Count; j++)
            {
                var modifier = modifiers[j];
                content.Fields.Add(ClaimFormUtilities.GetPdfFormField(modifier, ClaimFormFieldMapping.GetModifier(serviceLineIndex, j + 1)));
            }

            totalCharge += serviceLine.Amount;

            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(serviceLine.EPSDT.ToString(), ClaimFormFieldMapping.GetEpsdt(serviceLineIndex)));

            if (serviceLine.FamilyPlanningService)
            {
                content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.GetPlan(serviceLineIndex)));
            }
        }

        int totalChargeWhole = (int)totalCharge;
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(totalChargeWhole.ToString(), ClaimFormFieldMapping.TotalCharge, "right"));
        int totalChargeCents = (int)((totalCharge - totalChargeWhole) * 100);
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(totalChargeCents.ToString(), ClaimFormFieldMapping.TotalChargeCents));
    }

    private static void SetServiceFacilityDetails(ClaimFacility claimFacility, FillFormRequest content)
    {
        if (claimFacility is null) return;

        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claimFacility.Name, ClaimFormFieldMapping.FacilityName));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claimFacility.NationalProviderId, ClaimFormFieldMapping.FacilityNpi));

        if (!string.IsNullOrEmpty(claimFacility.OtherId))
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField($"{claimFacility.OtherIdQualifier}{claimFacility.OtherId}", ClaimFormFieldMapping.FacilityOther));
        }

        if (claimFacility.Address is not null)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claimFacility.Address.StreetAddress?.Replace(",", string.Empty), ClaimFormFieldMapping.FacilityStreet));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField($"{claimFacility?.Address?.City} {AddressUtilities.GetUSState(claimFacility?.Address?.State)} {claimFacility?.Address?.ZipCode}", ClaimFormFieldMapping.FacilityLocation));
        }
    }

    private static void SetBillingProvider(ClaimBillingDetail claimBillingDetail, FillFormRequest content)
    {
        if (claimBillingDetail is null) return;

        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claimBillingDetail.Name, ClaimFormFieldMapping.DoctorName));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claimBillingDetail.NationalProviderId, ClaimFormFieldMapping.DoctorNpi));

        if (!string.IsNullOrEmpty(claimBillingDetail.OtherId))
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField($"{claimBillingDetail.OtherIdQualifier}{claimBillingDetail.OtherId}", ClaimFormFieldMapping.DoctorOther));
        }

        if (!string.IsNullOrEmpty(claimBillingDetail.PhoneNumberDetails?.Number))
        {
            var fullNumber = claimBillingDetail.PhoneNumberDetails.GetNumberOnly();
            var (areaCode, number) = ClaimFormUtilities.GetPhoneNumberParts(fullNumber);
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(number, ClaimFormFieldMapping.DoctorPhone));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(areaCode, ClaimFormFieldMapping.DoctorPhoneArea));
        }

        if (claimBillingDetail.Address is not null)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claimBillingDetail.Address.StreetAddress?.Replace(",", string.Empty), ClaimFormFieldMapping.DoctorStreet));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField($"{claimBillingDetail?.Address?.City} {AddressUtilities.GetUSState(claimBillingDetail?.Address?.State)} {claimBillingDetail?.Address?.ZipCode}", ClaimFormFieldMapping.DoctorLocation));
        }

        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claimBillingDetail.TaxNumber, ClaimFormFieldMapping.TaxId));

        if (claimBillingDetail.TaxNumberType?.ToLower() == "ssn")
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.SocialSecurityNumber));
        }
        else
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.EmployerIdentificationNumber));
        }
    }

    private async Task SetMiscDetails(InsuranceClaimUSProfessional claim, FillFormRequest content, Guid currentPersonId)
    {
        if (claim is null) return;

        content.Fields.Add(ClaimFormUtilities.GetPdfFormField("0", ClaimFormFieldMapping.ICDIndicator));

        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claim.ResubmissionCode, ClaimFormFieldMapping.MedicaidResubmission));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claim.OriginalReferenceNumber, ClaimFormFieldMapping.OriginalReference));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claim.PatientsAccountNumber, ClaimFormFieldMapping.PatientAccount));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claim.PriorAuthorizationNumber, ClaimFormFieldMapping.PriorAuthorization));
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(claim.AdditionalClaimInformation, ClaimFormFieldMapping.AdditionalClaimInformation));

        int amtPaidWhole = (int)claim.AmountPaid;
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(amtPaidWhole.ToString(), ClaimFormFieldMapping.AmountPaid, "right"));
        int amtPaidCents = (int)((claim.AmountPaid - amtPaidWhole) * 100);
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(amtPaidCents.ToString(), ClaimFormFieldMapping.AmountPaidCents));

        if (claim.Lab)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.LabYes));
            var labChargesWhole = (int)(claim.LabCharges * 100);
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(labChargesWhole.ToString(), ClaimFormFieldMapping.Charge, "right"));
        }
        else
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.LabNo));
        }

        var personalSettings = await personRepository.GetPersonalSettings(currentPersonId);

        // Physician signature and date
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormConstants.ClaimSignatureOnFileValue, ClaimFormFieldMapping.PhysicianSignature));
        var currentDate = string.Empty;
        if (!string.IsNullOrEmpty(personalSettings?.TimeZone))
        {
            currentDate = ClaimFormUtilities.GetDateByTimezone(
                DateTime.UtcNow,
                personalSettings.TimeZone
            );
        }
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField(currentDate, ClaimFormFieldMapping.PhysicianDate));

        // Accept assignment
        content.Fields.Add(ClaimFormUtilities.GetPdfFormField("X", ClaimFormFieldMapping.AssignmentYes));

        // Signature settings (client and policy holder)
        var contactInsuranceSettings = await GetContactInsuranceSettings(claim.ProviderId, claim.ContactId);
        if (contactInsuranceSettings is null) return;

        if (contactInsuranceSettings.HasConsentFromClient)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormConstants.ClaimSignatureOnFileValue, ClaimFormFieldMapping.PatientSignature));
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(currentDate, ClaimFormFieldMapping.PatientDate));
        }

        if (contactInsuranceSettings.HasConsentFromPolicyHolder)
        {
            content.Fields.Add(ClaimFormUtilities.GetPdfFormField(ClaimFormConstants.ClaimSignatureOnFileValue, ClaimFormFieldMapping.InsuredSignature));
        }
    }

    private async Task<ContactInsuranceSettings> GetContactInsuranceSettings(Guid ProviderId, Guid ContactId)
    {
        var contactInsuranceSettings = await contactInsuranceSettingsRepository.Get(ProviderId, ContactId);
        return contactInsuranceSettings;
    }

    /// <summary>
    /// Validates a claim before submission.
    /// </summary>
    /// <param name="claim"></param>
    /// <param name="submissionMethod"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<FieldValidationError[]> Validate(InsuranceClaimUSProfessional claim, ClaimSubmissionMethod submissionMethod, CancellationToken cancellationToken)
    {
        // When validating, we want to clear any clearing house submissions errors first
        // This reverts the state of the claim to be resubmitted where it can get a new set of submission errors
        await insuranceClaimErrorsRepository.ClearForClaims([claim.Id], claim.ProviderId, cancellationToken);

        var validator = new InsuranceClaimUSProfessionalValidator(submissionMethod);
        var validationResult = await validator.ValidateAsync(claim, cancellationToken);

        if (validationResult.IsValid) return [];

        var errors = validationResult.Errors.Select(x => new FieldValidationError
        {
            FieldName = x.PropertyName,
            ErrorCode = x.ErrorCode,
            ErrorMessage = x.ErrorMessage

        }).ToList();

        if (errors.Count != 0) return [.. errors];

        if (submissionMethod == ClaimSubmissionMethod.Electronic)
        {
            var electronicErrors = await ValidateElectronicClaim(claim, cancellationToken);
            if (electronicErrors.Length != 0) return electronicErrors;
        }

        return [];
    }

    /// <summary>
    /// Validates a claim with the clearing house validation rules.
    /// </summary>
    /// <param name="claim"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<FieldValidationError[]> ValidateElectronicClaim(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken)
    {
        // @TODO: implement for electronic submission
        return await Task.FromResult(Array.Empty<FieldValidationError>());
    }

    /// <summary>
    /// Submits a claim based on the submission method.
    /// </summary>
    /// <param name="claim"></param>
    /// <param name="submissionMethod"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<ClaimSubmissionResult> Submit(InsuranceClaimUSProfessional claim, ClaimSubmissionMethod submissionMethod, CancellationToken cancellationToken)
    {
        // If claim is not yet validated, we have to validate it first
        if (claim.Status is ClaimStatus.Draft or ClaimStatus.Rejected)
        {
            var errors = await Validate(claim, submissionMethod, cancellationToken);
            if (errors.Length != 0) return new ClaimSubmissionResult(errors, null);
        }

        if (submissionMethod != ClaimSubmissionMethod.Electronic) return new ClaimSubmissionResult([], null);

        var electronicResult = await SubmitElectronicClaim(claim, cancellationToken);
        return electronicResult;
    }

    /// <summary>
    /// Submits the claim electronically to the clearing house.
    /// </summary>
    /// <param name="claim"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<ClaimSubmissionResult> SubmitElectronicClaim(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken)
    {
        var clearingHouse = claim.ContactInsurancePolicy?.ContactInsurancePolicy?.Payer?.ClearingHouse;
        if (!clearingHouse.HasValue)
        {
            throw InsuranceErrors.ClaimGeneralError(
                Errors.ClaimMissingClearingHouseCode,
                Errors.ClaimMissingClearingHouseDetails,
                ValidationType.BadRequest
            ).AsException();
        }

        var service = clearingHouseServiceResolver.GetClaimService(clearingHouse.Value);

        var submitClaimsResponse = await service.SubmitClaims(new ClearingHouseSubmitClaimRequest
        {
            Claim = claim
        });

        // If we cannot find the claim from clearing house upload claim response,
        // this will throw an unhandled error (Sequence contains no element(s))
        var submittedClaim = submitClaimsResponse.ClaimUploadResults.Single(s => s.Metadata?.InsuranceClaimId == claim.Id);

        var errorMessages = submittedClaim.Errors;

        // If there are no errors, it means that submission is successful
        // and we can create the metadata
        if (errorMessages is null || errorMessages.Length == 0)
        {
            // Check if there's an existing claim metadata
            var existingMetadata = await insuranceClaimClearingHouseMetadataRepository.GetByClaimId(
                claim.ProviderId,
                claim.Id,
                clearingHouse.Value,
                cancellationToken
            );
            if (existingMetadata is null)
            {
                // If there's no metadata, then we create a new one that came
                // off of the upload claim response
                var newMetadata = submittedClaim.Metadata;
                newMetadata.ProviderId = claim.ProviderId;
                await insuranceClaimClearingHouseMetadataRepository.Create(newMetadata, cancellationToken);
            }
            else
            {
                // If there's an existing one, then update the payer and claim references
                // @TODO: Probably need to revisit this when we implement claim status updates
                // and ERA processing as payer claim ID plays a big role in that process
                existingMetadata.PayerId = submittedClaim.Metadata.PayerId;
                existingMetadata.ClearingHouseClaimId = submittedClaim.Metadata.ClearingHouseClaimId;
                existingMetadata.PayerClaimId = submittedClaim.Metadata.PayerClaimId;
                await insuranceClaimClearingHouseMetadataRepository.Update(existingMetadata, cancellationToken);
            }

            // Return an empty list of errors. Client should be able
            // to evaluate that the electronic submission is successful
            return new ClaimSubmissionResult([], submittedClaim.Metadata);
        }

        // We'll save the claim submission errors so UI can display them
        // Ensure that the errors have the providerId and insuranceClaimId
        await insuranceClaimErrorsRepository.Upsert(claim.Id, claim.ProviderId, errorMessages, cancellationToken);

        // @TODO: Create mapping of clearing house claim field name to insurance claim field names for errors
        var validationErrors = errorMessages.Select(s => new FieldValidationError
        {
            FieldName = s.Field,
            ErrorMessage = s.Message
        }).ToList();

        return new ClaimSubmissionResult([.. validationErrors], null);
    }

    /// <summary>
    /// Checks if the claim is valid for electronic processing
    /// </summary>
    /// <param name="claim">Claim to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>errors encountered when checking the claim can be electronically submitted</returns>
    public async Task<ValidationError> ValidateClaimForElectronicSubmission(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken)
    {
        if (claim is null) throw new ArgumentNullException(nameof(claim));

        var payerId = claim.ContactInsurancePolicy?.PayerId;
        var npi = claim.BillingDetail.NationalProviderId;
        var taxNumber = claim.BillingDetail.TaxNumber;

        // If any of the required fields (NPI, tax number, payer ID) is empty
        // Throw an execution exception
        if (!payerId.HasValue)
        {
            return InsuranceErrors.ClaimGeneralError(
                Errors.PayerRequiredCode,
                Errors.PayerRequiredDetail,
                ValidationType.BadRequest
            );
        }

        if (string.IsNullOrEmpty(npi))
        {
            return InsuranceErrors.ClaimGeneralError(
                Errors.BillingProfilesNationalProviderIdRequiredCode,
                Errors.BillingProfilesNationalProviderIdRequiredDetail,
                ValidationType.BadRequest);

        }

        if (string.IsNullOrEmpty(taxNumber))
        {
            return InsuranceErrors.ClaimGeneralError(
                Errors.BillingProfilesTaxNumberRequiredCode,
                Errors.BillingProfilesTaxNumberRequiredDetail,
                ValidationType.BadRequest
            );
        }

        // Fetch provider insurance payer record to get clearing house reference and
        // get insurance payer by payer Id and clearing house used by claim's payer
        var payer = await insurancePayerRepository.GetClearingHousePayerByProviderPayerId(claim.ProviderId, payerId.Value, cancellationToken);
        if (payer is null)
        {
            return InsuranceErrors.ClaimGeneralError(
                Errors.PayerInvalidCode,
                Errors.PayerInvalidDetail,
                ValidationType.BadRequest
            );
        }

        // If payer does not support electronic claims, return false
        if (payer.Professional == PayerTransactionAvailability.False)
        {
            return InsuranceErrors.ClaimNotValidForElectronic();
        }

        // Ideally we would be checking if there's an active enrollment for the provider
        // when they submit a claim. But since the current clearing house we integrated to
        // can only tell us provider's enrollment status when they submit a claim and got an
        // ERA, then we should just let submissions to go thru. Isn't that fun?!
        // What will happen:
        // - Provider enrolls for claims in the payer
        // - Clearing house will submit enrollment request to payer
        // - At this stage, we just allow providers to submit claims so they CAN get an ERA.
        //   This is important as it will allow the clearing house to know if the provider has
        //   successfully enrolled to the payer
        // - If provider receives an ERA, that means they have successfully enrolled to the payer
        return null;
    }

    /// <summary>
    /// Check the claim submission method based on the claim payers connection to a clearing house
    /// </summary>
    /// <param name="providerId"></param>
    /// <param name="payerId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<ClaimSubmissionMethod> GetClaimSubmissionMethod(
        Guid providerId,
        Guid? payerId,
        CancellationToken cancellationToken = default
    )
    {
        if (!payerId.HasValue)
        {
            return ClaimSubmissionMethod.Manual;
        }

        // Fetch the clearing house payer record
        var payer = await insurancePayerRepository.GetClearingHousePayerByProviderPayerId(providerId, payerId.Value, cancellationToken);

        // If payer does not support electronic claims, return false
        if (payer == null || payer.Professional == PayerTransactionAvailability.False)
        {
            return ClaimSubmissionMethod.Manual;
        }

        return ClaimSubmissionMethod.Electronic;
    }

    /// <summary>
    /// Checks if the claim is valid for electronic processing
    /// </summary>
    /// <param name="claim">Claim to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if the claim is valid for electronic processing, false otherwise</returns>
    public async Task<ValidationError> IsClaimValidForElectronicProcessing(InsuranceClaimUSProfessional claim, CancellationToken cancellationToken)
    {
        if (claim is null) throw new ArgumentNullException(nameof(claim));

        var payerId = claim.ContactInsurancePolicy?.PayerId;
        var npi = claim.BillingDetail.NationalProviderId;
        var taxNumber = claim.BillingDetail.TaxNumber;

        // If any of the required fields (NPI, tax number, payer ID) is empty
        // Throw an execution exception
        if (!payerId.HasValue)
        {
            return InsuranceErrors.ClaimGeneralError(
                Errors.PayerRequiredCode,
                Errors.PayerRequiredDetail,
                ValidationType.BadRequest
            );
        }

        if (string.IsNullOrEmpty(npi))
        {
            return InsuranceErrors.ClaimGeneralError(
                Errors.BillingProfilesNationalProviderIdRequiredCode,
                Errors.BillingProfilesNationalProviderIdRequiredDetail,
                ValidationType.BadRequest
            );
        }

        if (string.IsNullOrEmpty(taxNumber))
        {
            return InsuranceErrors.ClaimGeneralError(
                Errors.BillingProfilesTaxNumberRequiredCode,
                Errors.BillingProfilesTaxNumberRequiredDetail,
                ValidationType.BadRequest
            );
        }

        // Fetch provider insurance payer record to get clearing house reference and
        // get insurance payer by payer Id and clearing house used by claim's payer
        var providerInsurancePayer = await insurancePayerRepository.GetById(claim.ProviderId, payerId.Value);
        if (providerInsurancePayer is null)
        {
            return InsuranceErrors.ClaimGeneralError(
                Errors.PayerInvalidCode,
                Errors.PayerInvalidDetail,
                ValidationType.BadRequest
            );
        }
        var payer = await insurancePayerRepository.GetClearingHousePayer(providerInsurancePayer.ClearingHouse, providerInsurancePayer.PayerId, cancellationToken);
        if (payer is null)
        {
            return InsuranceErrors.ClaimGeneralError(
                Errors.PayerInvalidCode,
                Errors.PayerInvalidDetail,
                ValidationType.BadRequest
            );
        }

        // If payer does not support electronic claims, return false
        if (payer.Professional == PayerTransactionAvailability.False)
        {
            return InsuranceErrors.ClaimNotValidForElectronic();
        }

        // Ideally we would be checking if there's an active enrollment for the provider
        // when they submit a claim. But since the current clearing house we integrated to
        // can only tell us provider's enrollment status when they submit a claim and got an
        // ERA, then we should just let submissions to go thru. Isn't that fun?!
        // What will happen:
        // - Provider enrolls for claims in the payer
        // - Clearing house will submit enrollment request to payer
        // - At this stage, we just allow providers to submit claims so they CAN get an ERA.
        //   This is important as it will allow the clearing house to know if the provider has
        //   successfully enrolled to the payer
        // - If provider receives an ERA, that means they have successfully enrolled to the payer
        return null;
    }
}
