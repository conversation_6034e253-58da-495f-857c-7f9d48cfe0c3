using carepatron.core.Application.Billables.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Extensions;
using carepatron.core.Repositories.Insurance;
using carepatron.core.Repositories.Billables;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Insurance.Notifications.Models;
using carepatron.core.Repositories.Provider;
using Notifications.Sdk.Client.Abstract;
using Serilog;
using Serilog.Context;
using Payment = carepatron.core.Application.Payments.Models.Payment;

namespace carepatron.core.Application.Insurance.Services;

public interface IInsuranceService
{
    Task SetSelfPayForBillables(Guid providerId, Billable[] billables);
    ClaimStatus GetClaimStatusByPayments(
        InsuranceClaim claim,
        PaymentAllocation[] allocations
    );

    Task<decimal> GetAmountPaidFromBillableItems(InsuranceClaim claim);
    Task<ClaimTaskMapping[]> GetTasksForBillableItems(InsuranceClaim claim, CancellationToken cancellationToken);

    Task SendClaimPaymentNotification(
        Guid providerId,
        InsuranceClaimUSProfessional claim,
        Payment payment,
        ProviderInsurancePayer payer);
    
    Task SendClaimPaymentNotification(
        Guid providerId,
        InsuranceClaimUSProfessional claim,
        Payment payment,
        string payerName,
        string payerNumber,
        Guid? payerId = null);
    
    Task SendClaimResponseNotification(
        Guid providerId,
        InsuranceClaimUSProfessional claim,
        ProviderInsurancePayer payer);

    Task SendClaimResponseNotification(
        Guid providerId,
        InsuranceClaimUSProfessional claim,
        string payerName,
        string payerNumber,
        Guid? payerId = null);
}

public class InsuranceService(
    IContactInsurancePolicyRepository contactInsurancePolicyRepository,
    IServiceCoverageRepository serviceCoverageRepository,
    IContactInsuranceSettingsRepository contactInsuranceSettingsRepository,
    IBillableRepository billableRepository,
    IProviderStaffRepository providerStaffRepository,
    INotificationsService notificationsService
) : IInsuranceService
{
    public async Task SetSelfPayForBillables(Guid providerId, Billable[] billables)
    {
        if (billables is null || billables.Length == 0) return;

        var contactIds = billables.Select(x => x.ContactId).Distinct().ToArray();
        var serviceIds = billables.SelectMany(x => x.Items?.Where(s => s.ServiceId.HasValue).Select(s => s.ServiceId.Value))
            .Distinct()
            .ToArray();

        var settings = (await contactInsuranceSettingsRepository.GetForContacts(providerId, contactIds))
            .ToDictionary(x => x.ContactId);

        var policyRequest = billables.Select(
            x => new ContactActivePolicyRequest(x.ContactId, x.Date.ToDateOnly())
        ).ToArray();

        var activePolicies = (await contactInsurancePolicyRepository.GetActivePolicies(providerId, policyRequest))
            .GroupBy(x => x.ContactId)
            .ToDictionary(g => g.Key, g => g.OrderBy(x => x.PolicyStartDate).ToArray());

        var serviceCoverages = (await serviceCoverageRepository.GetServiceCoveragesForContacts(
            providerId,
            contactIds,
            new GetServiceCoveragesListOptions(
                serviceIds,
                activePolicies.Values.SelectMany(p => p.Select(x => x.Id)).ToArray()
            )
        ))
            .GroupBy(x => x.ContactId)
            .ToDictionary(g => g.Key, g => g.ToArray());

        foreach (var billableItem in billables.SelectMany(s => s.Items))
        {
            if (billableItem.IsManuallyUpdated || billableItem.IsInsuranceEnabled) continue;

            // Check if contact has insurance billing method enabled
            if (!settings.TryGetValue(billableItem.ContactId, out var contactSettings) || contactSettings.BillingMethod != InsuranceBillingMethod.Insurance)
            {
                SetDefaultSelfPay(billableItem);
                continue;
            }

            // Check if contact has active insurance policy
            if (!activePolicies.TryGetValue(billableItem.ContactId, out var activePoliciesByContact) || activePoliciesByContact.Length == 0)
            {
                SetDefaultSelfPay(billableItem);
                continue;
            }

            // Get the first active policy for the contact
            var activePolicy = activePoliciesByContact.First();

            // Check service coverages for the contact
            if (!serviceCoverages.TryGetValue(billableItem.ContactId, out var serviceCoveragesByContact) || serviceCoveragesByContact.Length == 0)
            {
                SetDefaultSelfPay(billableItem);
                continue;
            }

            // Calculate self pay amounts based on service coverage
            var serviceCoverage = serviceCoveragesByContact.FirstOrDefault(x => x.ServiceId == billableItem.ServiceId && x.InsurancePolicyId == activePolicy.Id);
            if (serviceCoverage is not null)
            {
                if (serviceCoverage.Coverage is null)
                {
                    SetDefaultSelfPay(billableItem);
                    continue;
                }

                // If service coverage is disabled,
                // it means the service is not covered and will use the original total owed amount
                if (!serviceCoverage.Coverage.Enabled)
                {
                    SetDefaultSelfPay(billableItem);
                }
                else
                {
                    // Use policy's copay/coinsurance only if service coverage has neither
                    var copay = serviceCoverage.Coverage.Copay ?? (serviceCoverage.Coverage.Coinsurance == null ? activePolicy.Copay : null);
                    var coinsurance = serviceCoverage.Coverage.Coinsurance ?? (serviceCoverage.Coverage.Copay == null ? activePolicy.Coinsurance : null);
                    SetInsuranceSelfPayAmount(billableItem, copay, coinsurance);
                }
                continue;
            }

            SetInsuranceSelfPayAmount(billableItem, activePolicy.Copay, activePolicy.Coinsurance);
        }
    }

    private static void SetInsuranceSelfPayAmount(BillableItem billableItem, decimal? copay, decimal? coinsurance)
    {
        if (!copay.HasValue && !coinsurance.HasValue)
        {
            // if both values are null - set default self pay
            SetDefaultSelfPay(billableItem);
        }
        else
        {
            billableItem.IsInsuranceEnabled = true;
            if (copay.HasValue)
            {
                // If copay service coverage is more than the total owed amount,
                // cap the self pay amount to the total owed amount
                var copayValue = copay.Value;
                billableItem.SelfPayAmount = copayValue > billableItem.TotalOwed
                    ? billableItem.TotalOwed
                    : copay.Value;
            }
            else if (coinsurance.HasValue)
            {
                // Probably unlikely to happen unless coinsurance is more than 100%
                var coInsuranceValue = billableItem.TotalOwed * coinsurance.Value;
                billableItem.SelfPayAmount = coInsuranceValue > billableItem.TotalOwed
                    ? billableItem.TotalOwed
                    : billableItem.TotalOwed * coinsurance.Value;
            }
        }
    }

    private static void SetDefaultSelfPay(BillableItem billableItem)
    {
        billableItem.IsInsuranceEnabled = false;
        billableItem.SelfPayAmount = billableItem.TotalOwed;
    }

    /// <summary>
    /// Determines the claim status based on the payment unsaved allocations and existing balancePaid.
    /// </summary>
    public ClaimStatus GetClaimStatusByPayments(
        InsuranceClaim claim,
        PaymentAllocation[] allocations
    )
    {
        ArgumentNullException.ThrowIfNull(claim);

        var existingBalancePaid = claim.BalancePaid;
        var newBalancePaid = allocations?.Sum(x => x.Amount) ?? 0;
        var balancePaid = existingBalancePaid + newBalancePaid;

        if (balancePaid > 0 && balancePaid < claim.BalanceDue)
        {
            return ClaimStatus.PartiallyPaid;
        }
        if (balancePaid >= claim.BalanceDue)
        {
            return ClaimStatus.Paid;
        }

        return claim.Status;
    }

    /// <summary>
    /// Calculates the AmountPaid from billableItems associated with claim service lines.
    /// Sums SelfPayAmount + InsurancePaid for all found billableItems. minus
    /// </summary>
    public async Task<decimal> GetAmountPaidFromBillableItems(InsuranceClaim claim)
    {
        var billableItemIds = claim.ServiceLines?
            .Where(s => s.BillableItemId.HasValue)
            .Select(x => x.BillableItemId.Value)
            .ToArray() ?? [];
        if (billableItemIds.Length == 0)
        {
            return claim.AmountPaid;
        }
        var claimBillableItems = await billableRepository.GetBillableItems(claim.ProviderId, billableItemIds);
        return claimBillableItems.Sum(x => x.SelfPayAmount + x.InsurancePaid) - claim.BalancePaid;
    }

    public async Task<ClaimTaskMapping[]> GetTasksForBillableItems(InsuranceClaim claim, CancellationToken cancellationToken)
    {
        var billableItemIds = claim.ServiceLines?
            .Where(s => s.BillableItemId.HasValue)
            .Select(x => x.BillableItemId.Value)
            .ToArray() ?? [];
        if (billableItemIds.Length > 0)
        {
            var taskIdsResult = await billableRepository.GetTaskIdsByBillableItemId(billableItemIds, cancellationToken);
            var distinctTaskIds = taskIdsResult.Distinct().ToArray();
            return distinctTaskIds.Select(taskId => new ClaimTaskMapping(claim.Id, taskId)).ToArray();
        }
        return [];
    }

    public async Task SendClaimPaymentNotification(
        Guid providerId,
        InsuranceClaimUSProfessional claim,
        Payment payment,
        ProviderInsurancePayer payer)
    {
        ArgumentNullException.ThrowIfNull(payer);
        await SendClaimPaymentNotification(providerId, claim, payment, payerName: payer.Name, payerNumber: payer.PayerId, payer.Id);
    }
    
    public async Task SendClaimPaymentNotification(
        Guid providerId,
        InsuranceClaimUSProfessional claim,
        Payment payment, 
        string payerName,
        string payerNumber,
        Guid? payerId = null)
    {
        using (LogContext.PushProperty(Constants.LogProperties.ClaimId, claim?.Id))
        {
            await SendClaimNotificationToProviderOwner(providerId: providerId, (ownerId) => CreateAndSendClaimPaymentNotificationContent(
                providerId, 
                [ownerId],
                claim, 
                payment,
                payerName: payerName,
                payerNumber: payerNumber, 
                payerId));
        }
    }
    
    public async Task SendClaimResponseNotification(
        Guid providerId,
        InsuranceClaimUSProfessional claim,
        ProviderInsurancePayer payer)
    {
        ArgumentNullException.ThrowIfNull(payer);
        
        await SendClaimResponseNotification(providerId, claim, payerName: payer.Name, payerNumber: payer.PayerId, payer.Id);
    }
    
    public async Task SendClaimResponseNotification(
        Guid providerId,
        InsuranceClaimUSProfessional claim,
        string payerName,
        string payerNumber,
        Guid? payerId = null)
    {
        using (LogContext.PushProperty(Constants.LogProperties.ClaimId, claim?.Id))
        {
            await SendClaimNotificationToProviderOwner(providerId: providerId, (ownerId) => CreateAndSendClaimResponseNotificationContent(
                providerId,
                [ownerId],
                claim,
                payerName: payerName,
                payerNumber: payerNumber,
                payerId));
        }
    }
    
    private async Task SendClaimNotificationToProviderOwner(
        Guid providerId,
        Func<Guid, Task> sendNotificationFunc)
    {
        try
        {
            var owner = await providerStaffRepository.GetProviderOwner(providerId);

            if (owner is null)
            {
                Log.Warning("Unable to send claim payment notification. Workspace owner not found");

                return;
            }

            await sendNotificationFunc(owner.PersonId);
        }
        catch (Exception ex)
        {
            Log.Warning(ex, "Error occurred sending claim notification");
        }
    }
    
    private async Task CreateAndSendClaimPaymentNotificationContent(
        Guid providerId,
        Guid[] recipientPersonIds,
        InsuranceClaimUSProfessional claim,
        Payment payment, 
        string payerName,
        string payerNumber,
        Guid? payerId = null)
    {
        ArgumentNullException.ThrowIfNull(claim);
        ArgumentNullException.ThrowIfNull(payment);

        using (LogContext.PushProperty("ClaimStatus", claim.Status))
        {
            var payerContent = new PayerNotificationContent(Number: payerNumber, Name: payerName, PayerId: payerId);
            var claimContent = ClaimNotificationContent.Create(claim);
            var paymentContent = PaymentNotificationContent.Create(payment);

            if (claim.Status == ClaimStatus.Paid)
            {
                await notificationsService.Send(
                    providerId, 
                    SystemActors.InsuranceJobActorId, 
                    new InsuranceClaimPaidNotificationContent(providerId, claimContent, paymentContent, payerContent), 
                    recipientPersonIds);
                
                return;
            }

            if (claim.Status == ClaimStatus.PartiallyPaid)
            {
                await notificationsService.Send(
                    providerId, 
                    SystemActors.InsuranceJobActorId, 
                    new InsuranceClaimPartiallyPaidNotificationContent(providerId, claimContent, paymentContent, payerContent), 
                    recipientPersonIds);
                
                return;
            }

            Log.Warning("Unable to create claim payment notification content. Invalid claim status");
        }
    }
    
    private async Task CreateAndSendClaimResponseNotificationContent(
        Guid providerId,
        Guid[] recipientPersonIds,
        InsuranceClaimUSProfessional claim,
        string payerName,
        string payerNumber,
        Guid? payerId = null)
    {
        ArgumentNullException.ThrowIfNull(claim);
        
        using (LogContext.PushProperty("ClaimStatus", claim.Status))
        {
            var payerContent = new PayerNotificationContent(Number: payerNumber, Name: payerName, PayerId: payerId);
            var claimContent = ClaimNotificationContent.Create(claim);

            if (claim.Status == ClaimStatus.Rejected)
            {
                await notificationsService.Send(
                    providerId, 
                    SystemActors.InsuranceJobActorId, 
                    new InsuranceClaimRejectedNotificationContent(providerId, claimContent, payerContent),
                    recipientPersonIds);

                return;
            }

            if (claim.Status == ClaimStatus.Denied)
            {
                await notificationsService.Send(
                    providerId, 
                    SystemActors.InsuranceJobActorId, 
                    new InsuranceClaimDeniedNotificationContent(providerId, claimContent, payerContent),
                    recipientPersonIds);
                
                return;
            }

            Log.Warning("Unable to create claim response (rejected / denied) notification content. Invalid claim status");
        }
    }
}
