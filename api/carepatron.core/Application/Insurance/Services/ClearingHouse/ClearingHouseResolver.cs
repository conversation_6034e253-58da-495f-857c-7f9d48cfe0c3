using System;
using carepatron.core.Application.Insurance.Models;
using Microsoft.Extensions.DependencyInjection;

namespace carepatron.core.Application.Insurance.Services.ClearingHouse;

public interface IClearingHouseServiceResolver
{
    IClearingHousePayersService GetPayersService(ClearingHouseType type);
    IClearingHouseEligibilityService GetEligibilityService(ClearingHouseType type);
    IClearingHouseClaimService GetClaimService(ClearingHouseType type);
}

public class ClearingHouseServiceResolver(IServiceProvider serviceProvider) : IClearingHouseServiceResolver
{
    public IClearingHousePayersService GetPayersService(ClearingHouseType type)
    {
        var service = serviceProvider.GetKeyedService<IClearingHousePayersService>(type) ?? throw new NotSupportedException($"Unsupported clearing house type: {type}");
        return service;
    }

    public IClearingHouseEligibilityService GetEligibilityService(ClearingHouseType type)
    {
        var service = serviceProvider.GetKeyedService<IClearingHouseEligibilityService>(type) ?? throw new NotSupportedException($"Unsupported clearing house type: {type}");
        return service;
    }

    public IClearingHouseClaimService GetClaimService(ClearingHouseType type)
    {
        var service = serviceProvider.GetKeyedService<IClearingHouseClaimService>(type) ?? throw new NotSupportedException($"Unsupported clearing house type: {type}");
        return service;
    }
}
