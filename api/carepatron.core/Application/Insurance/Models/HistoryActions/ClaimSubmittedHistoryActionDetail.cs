using System;

namespace carepatron.core.Application.Insurance.Models;

public enum ClaimHistorySenderType
{
    Other = 0,
    ClearingHouse = 1,
    Payer = 2
}

public enum ClaimAttachmentHistoryType
{
    Unknown = 0,
    CMS1500 = 1,
    Text = 2
}

public record ClaimHistoryModel
{
    /// <summary>
    /// The claim id
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// The type of claim (US professional, US institutional, etc.)
    /// </summary>
    public ClaimType Type { get; set; }

    /// <summary>
    /// The internal Carepatron's claim number
    /// </summary>
    public string Number { get; set; }

    /// <summary>
    /// The claim status
    /// </summary>
    public ClaimStatus Status { get; set; }

    /// <summary>
    /// The claim contact id
    /// </summary>
    public Guid ContactId { get; set; }

    /// <summary>
    /// The claim submission method (Electronic or Manual)
    /// </summary>
    public ClaimSubmissionMethod SubmissionMethod { get; set; }

    public static ClaimHistoryModel Create(InsuranceClaim claim)
    {
        return new ClaimHistoryModel
        {
            Id = claim.Id,
            Number = claim.Number,
            Type = claim.Type,
            Status = claim.Status,
            ContactId = claim.ContactId,
            SubmissionMethod = claim.SubmissionMethod,
        };
    }

    public static ClaimHistoryModel CreateRejected(InsuranceClaim claim)
    {
        return new ClaimHistoryModel
        {
            Id = claim.Id,
            Number = claim.Number,
            Type = claim.Type,
            Status = ClaimStatus.Rejected,
            ContactId = claim.ContactId,
            SubmissionMethod = claim.SubmissionMethod,
        };
    }
}

public record ClaimReceivedReferenceHistoryModel
{
    /// <summary>
    /// The external clearing house claim id
    /// </summary>
    public string ClearingHouseClaimId { get; set; }

    /// <summary>
    /// The payer's claim id
    /// </summary>
    public string PayerClaimId { get; set; }

    /// <summary>
    /// Creates a new instance of <see cref="ClaimReceivedReferenceHistoryModel"/> based on the existing metadata and status claim message metadata.
    /// </summary>
    /// <param name="existingMetadata"></param>
    /// <param name="currentMetadata"></param>
    /// <returns></returns>
    public static ClaimReceivedReferenceHistoryModel Create(
        InsuranceClaimClearingHouseMetadata existingMetadata,
        InsuranceClaimClearingHouseMetadata currentMetadata
    )
    {
        if (existingMetadata is null)
        {
            return new ClaimReceivedReferenceHistoryModel
            {
                ClearingHouseClaimId = currentMetadata.ClearingHouseClaimId,
                PayerClaimId = currentMetadata.PayerClaimId
            };
        }

        var clearingHouseClaimId =
            currentMetadata.ClearingHouseClaimId != existingMetadata.ClearingHouseClaimId
                ? currentMetadata.ClearingHouseClaimId
                : null;
        var payerClaimId =
            currentMetadata.PayerClaimId != existingMetadata.PayerClaimId
                ? currentMetadata.PayerClaimId
                : null;
        return new ClaimReceivedReferenceHistoryModel
        {
            ClearingHouseClaimId = clearingHouseClaimId,
            PayerClaimId = payerClaimId
        };
    }
}

public record ClaimPayerHistoryModel
{
    /// <summary>
    /// The payer id number
    /// </summary>
    public string Number { get; set; }

    /// <summary>
    /// The payer name
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// The workspace payer identifier, if configured
    /// </summary>
    public Guid? PayerId { get; set; }

    /// <summary>
    /// The clearing house linked to the payer, if any
    /// </summary>
    public ClearingHouseType? ClearingHouse { get; set; }

    /// <summary>
    /// Creates a new instance of <see cref="ClaimPayerHistoryModel"/> from the given <see cref="InsuranceClaimUSProfessional"/>
    /// </summary>
    /// <param name="claim"></param>
    /// <returns>Instance of <see cref="ClaimPayerHistoryModel"/></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static ClaimPayerHistoryModel Create(InsuranceClaimUSProfessional claim)
    {
        if (claim.ContactInsurancePolicy is null)
            throw new ArgumentNullException(nameof(claim.ContactInsurancePolicy));

        var clearingHouse =
            claim.ContactInsurancePolicy?.ContactInsurancePolicy?.Payer?.ClearingHouse
            ?? ClearingHouseType.None;

        return new ClaimPayerHistoryModel()
        {
            PayerId = claim.ContactInsurancePolicy.PayerId,
            Number = claim.ContactInsurancePolicy.PayerNumber,
            Name = claim.ContactInsurancePolicy.PayerName,
            ClearingHouse = clearingHouse
        };
    }
}

public record PaymentAllocationHistoryReference(decimal Amount, string Code, string Description);

public record ClaimPaymentHistoryModel
{
    /// <summary>
    /// The payment id number
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// The amount paid
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// Currency code of the payment
    /// </summary>
    public string CurrencyCode { get; set; }

    /// <summary>
    /// The payment reference number
    /// </summary>
    public string Reference { get; set; }

    /// <summary>
    ///
    /// </summary>
    public PaymentAllocationHistoryReference[] Allocations { get; set; }
}

public record ClaimAttachmentHistoryModel
{
    /// <summary>
    /// The attachment type
    /// </summary>
    public ClaimAttachmentHistoryType Type { get; set; }

    /// <summary>
    /// The attachment file key
    /// </summary>
    public string Key { get; set; }

    public static ClaimAttachmentHistoryModel Create(ClaimAttachmentHistoryType type, string key)
    {
        return new ClaimAttachmentHistoryModel { Type = type, Key = key };
    }
}

public record InsuranceClaimRemittanceHistoryModel
{
    /// <summary>
    /// The remittance advice id
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// The remittance advice external reference
    /// </summary>
    public string Reference { get; set; }

    /// <summary>
    /// If remittance has any adjustments
    /// </summary>
    public bool IsAdjusted { get; set; }
}

public record ClaimHistoryMetadataReferences(string ClearingHouseClaimId, string PayerClaimId);

public record ClaimStatusChangedHistoryActionDetail(ClaimHistoryModel Claim);

public record ClaimCreatedHistoryActionDetail(ClaimHistoryModel Claim);

public record ClaimRestoredHistoryActionDetail(ClaimHistoryModel Claim);

public record ClaimTrashedHistoryActionDetail(ClaimHistoryModel Claim);

public record ClaimSubmittedHistoryActionDetail(
    ClaimHistoryModel Claim,
    ClaimPayerHistoryModel Payer,
    ClaimHistoryMetadataReferences References = null
);

public record ClaimRejectedHistoryActionDetail(
    ClaimHistoryModel Claim,
    ClaimPayerHistoryModel Payer,
    InsuranceClaimError[] Errors,
    ClaimHistorySenderType Sender
);

public record ClaimDeniedHistoryActionDetail(
    ClaimHistoryModel Claim,
    ClaimPayerHistoryModel Payer,
    InsuranceClaimError[] Errors
);

public record ClaimManualPaymentHistoryActionDetail(
    ClaimHistoryModel Claim,
    ClaimPaymentHistoryModel Payment
);

public record ClaimElectronicPaymentHistoryActionDetail(
    ClaimHistoryModel Claim,
    ClaimPayerHistoryModel Payer,
    ClaimPaymentHistoryModel Payment,
    InsuranceClaimRemittanceHistoryModel Remittance
);

public record ClaimExportedHistoryActionDetail(
    ClaimHistoryModel Claim,
    ClaimAttachmentHistoryModel Attachment
);

public record ClaimReceivedHistoryActionDetail(
    ClaimHistoryModel Claim,
    ClaimPayerHistoryModel Payer,
    ClaimReceivedReferenceHistoryModel References,
    ClaimHistorySenderType Sender,
    string SenderName,
    string Message
);

public record ERAReceivedHistoryActionDetail(
    ClaimHistoryModel Claim,
    ClaimPayerHistoryModel Payer,
    InsuranceClaimRemittanceHistoryModel Remittance
);
