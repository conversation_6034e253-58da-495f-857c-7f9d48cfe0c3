using System;
using System.Collections.Generic;
using carepatron.core.Common.Interfaces;
using carepatron.core.Models.Common;

namespace carepatron.core.Application.Insurance.Models;

public class ProviderInsurancePayer : IHasAddress
{
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public string PayerId { get; set; }
    public string Name { get; set; }
    public Address Address { get; set; }
    public string PhoneNumber { get; set; }
    public InsuranceCoverageType CoverageType { get; set; }
    public string OtherCoverageTypeName { get; set; }
    public ClearingHouseType ClearingHouse { get; set; }

    public static ProviderInsurancePayer Create(Guid providerId, Dictionary<string, ProviderInsurancePayer> existingPayers, InsurancePayer x)
    {
        if (!Enum.TryParse<InsuranceCoverageType>(x.PayerType, true, out var coverageType))
        {
            coverageType = InsuranceCoverageType.Other;
        }

        var existingPayer = existingPayers.GetValueOrDefault(x.PayerId);

        return new ProviderInsurancePayer
        {
            Id = existingPayer?.Id ?? Guid.NewGuid(),
            ProviderId = providerId,
            PayerId = x.PayerId,
            Name = x.Name,
            Address = existingPayer?.Address ?? x.Address,
            PhoneNumber = existingPayer?.PhoneNumber ?? x.PhoneNumber,
            CoverageType = coverageType,
            OtherCoverageTypeName = coverageType == InsuranceCoverageType.Other
                ? x.PayerType
                : null,
            ClearingHouse = x.ClearingHouse,
        };
    }
}

/// <summary>
/// A slim reference record for ProviderInsurancePayer
/// Used on related entities to reduce payload/object graph size.
/// </summary>
public class InsurancePayerReference
{
    public Guid? Id { get; set; }
    public string PayerNumber { get; set; }
    public string Name { get; set; }

    public InsurancePayerReference()
    {
    }

    public InsurancePayerReference(ProviderInsurancePayer? payer)
    {
        if (payer != null)
        {
            Id = payer.Id;
            PayerNumber = payer.PayerId;
            Name = payer.Name;
        }
    }
}