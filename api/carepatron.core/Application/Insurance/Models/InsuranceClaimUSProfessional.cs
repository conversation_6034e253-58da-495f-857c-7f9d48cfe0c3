using System;
using System.Collections.Generic;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Trash.Models;

namespace carepatron.core.Application.Insurance.Models;

/// <summary>
/// Represents a US Professional insurance claim.
/// Ensure claim domain models inherit from IBaseInsuranceClaim to force consistency of general claim fields
/// </summary>
public class InsuranceClaimUSProfessional : InsuranceClaim, ITrash
{
    public string ExportId { get; set; }
    public bool IsExportValid { get; set; }
    public string PrintId { get; set; }
    public bool IsPrintValid { get; set; }
    public string ResubmissionCode { get; set; }
    public string OriginalReferenceNumber { get; set; }
    public string PatientsAccountNumber { get; set; }
    public string PriorAuthorizationNumber { get; set; }
    public bool Lab { get; set; }
    public decimal? LabCharges { get; set; }
    public string AdditionalClaimInformation { get; set; }
    
    [Obsolete("Use BalancePaid property instead.")]
    public decimal AllocatedPaymentAmount => BalancePaid;

    public ClaimClient Client { get; set; }
    public ClaimIncident Incident { get; set; }
    public ClaimFacility ServiceFacility { get; set; }
    public ClaimReferringProvider[] ReferringProviders { get; set; }
    public ClaimRenderingProvider[] RenderingProviders { get; set; }
    public ClaimDiagnosticCode[] DiagnosticCodes { get; set; }
    public ClaimContactInsurancePolicy ContactInsurancePolicy { get; set; }
    public ClaimBillingDetail BillingDetail { get; set; }

    public void SetResubmissionCode(string resubmissionCode, string originalReferenceNumber)
    {
        ResubmissionCode = resubmissionCode;
        OriginalReferenceNumber = originalReferenceNumber;
    }

    public TrashItem GetTrashItem()
    {
        return new TrashItem
        {
            Id = Guid.NewGuid(),
            ProviderId = ProviderId,
            Name = Number,
            Type = TrashType.USProfessionalClaim,
            EntityId = Id,
            DeletedDateTimeUtc = DateTime.UtcNow,
            Metadata = new Dictionary<string, object>
            {
                { "claimId", Id },
                { "hardDelete", SubmissionMethod == ClaimSubmissionMethod.Manual },
            },
            FromContact = new SimpleContact { Id = ContactId },
        };
    }
}
