using System;
using carepatron.core.Constants;
using carepatron.core.Models.Common;
using carepatron.core.Utilities;
using carepatron.core.Validation;
using FluentValidation;

namespace carepatron.core.Application.Insurance.Models;

public class ClaimContactInsurancePolicyValidator : AbstractValidator<ClaimContactInsurancePolicy>
{
    public ClaimContactInsurancePolicyValidator()
    {
        RuleFor(x => x.InsuranceType)
            .IsRequired()
            .WithErrorCode(
                Errors.RequiredFieldCode(nameof(ClaimContactInsurancePolicy.InsuranceType))
            )
            .WithMessage(
                Errors.RequiredFieldDetails(nameof(ClaimContactInsurancePolicy.InsuranceType))
            );
        RuleFor(x => x.CoverageType)
            .IsRequired()
            .WithErrorCode(
                Errors.RequiredFieldCode(nameof(ClaimContactInsurancePolicy.CoverageType))
            )
            .WithMessage(
                Errors.RequiredFieldDetails(nameof(ClaimContactInsurancePolicy.CoverageType))
            );
        RuleFor(x => x.PolicyHolderRelationshipType)
            .IsRequired()
            .WithErrorCode(
                Errors.RequiredFieldCode(
                    nameof(ClaimContactInsurancePolicy.PolicyHolderRelationshipType)
                )
            )
            .WithMessage(
                Errors.RequiredFieldDetails(
                    nameof(ClaimContactInsurancePolicy.PolicyHolderRelationshipType)
                )
            );
        RuleFor(x => x.PolicyHolderFirstName)
            .IsRequired()
            .WithErrorCode(
                Errors.RequiredFieldCode(nameof(ClaimContactInsurancePolicy.PolicyHolderFirstName))
            )
            .WithMessage(
                Errors.RequiredFieldDetails(
                    nameof(ClaimContactInsurancePolicy.PolicyHolderFirstName)
                )
            );
        RuleFor(x => x.PolicyHolderLastName)
            .IsRequired()
            .WithErrorCode(
                Errors.RequiredFieldCode(nameof(ClaimContactInsurancePolicy.PolicyHolderLastName))
            )
            .WithMessage(
                Errors.RequiredFieldDetails(
                    nameof(ClaimContactInsurancePolicy.PolicyHolderLastName)
                )
            );
        RuleFor(x => x.PolicyHolderDateOfBirth)
            .IsRequired()
            .WithErrorCode(
                Errors.RequiredFieldCode(
                    nameof(ClaimContactInsurancePolicy.PolicyHolderDateOfBirth)
                )
            )
            .WithMessage(
                Errors.RequiredFieldDetails(
                    nameof(ClaimContactInsurancePolicy.PolicyHolderDateOfBirth)
                )
            );
        RuleFor(x => x.PolicyHolderSex)
            .IsRequired()
            .WithErrorCode(
                Errors.RequiredFieldCode(nameof(ClaimContactInsurancePolicy.PolicyHolderSex))
            )
            .WithMessage(
                Errors.RequiredFieldDetails(nameof(ClaimContactInsurancePolicy.PolicyHolderSex))
            );
        RuleFor(x => x.PolicyHolderPhoneNumber)
            .IsRequired()
            .WithErrorCode(
                Errors.RequiredFieldCode(
                    nameof(ClaimContactInsurancePolicy.PolicyHolderPhoneNumber)
                )
            )
            .WithMessage(
                Errors.RequiredFieldDetails(
                    nameof(ClaimContactInsurancePolicy.PolicyHolderPhoneNumber)
                )
            );
        RuleFor(x => x.PolicyHolderMemberId)
            .IsRequired()
            .WithErrorCode(
                Errors.RequiredFieldCode(nameof(ClaimContactInsurancePolicy.PolicyHolderMemberId))
            )
            .WithMessage(
                Errors.RequiredFieldDetails(
                    nameof(ClaimContactInsurancePolicy.PolicyHolderMemberId)
                )
            );
    }
}

public class ManualSubmissionClaimContactInsurancePolicyValidator
    : ClaimContactInsurancePolicyValidator
{
    public ManualSubmissionClaimContactInsurancePolicyValidator()
    {
        RuleFor(x => x.Address)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimContactInsurancePolicy.Address)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimContactInsurancePolicy.Address)));
        RuleFor(x => x.Address.StreetAddress)
            .IsRequired()
            .When(s => s.Address != null)
            .WithErrorCode(Errors.RequiredFieldCode(nameof(Address.StreetAddress)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(Address.StreetAddress)));

        RuleFor(x => x.PayerPhoneNumberDetails)
            .IsRequired()
            .WithErrorCode(
                Errors.RequiredFieldCode(
                    nameof(ClaimContactInsurancePolicy.PayerPhoneNumberDetails)
                )
            )
            .WithMessage(
                Errors.RequiredFieldDetails(
                    nameof(ClaimContactInsurancePolicy.PayerPhoneNumberDetails)
                )
            );
        RuleFor(x => x.PayerPhoneNumberDetails.Number)
            .IsRequired()
            .When(s => s.PayerPhoneNumberDetails != null)
            .WithErrorCode(
                Errors.RequiredFieldCode(
                    nameof(ClaimContactInsurancePolicy.PayerPhoneNumberDetails)
                )
            )
            .WithMessage(
                Errors.RequiredFieldDetails(
                    nameof(ClaimContactInsurancePolicy.PayerPhoneNumberDetails)
                )
            );
    }
}

public class ClaimContactInsurancePolicy
{
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public ContactInsurancePolicy ContactInsurancePolicy { get; set; }

    /// <summary>
    /// Internal UUID of the selected payer.
    /// Null if manually entered.
    /// </summary>
    public Guid? PayerId { get; set; }
    public string PayerNumber { get; set; }
    public string PayerName { get; set; }
    public InsuranceType InsuranceType { get; set; }
    public InsuranceCoverageType? CoverageType { get; set; }
    public string OtherCoverageTypeName { get; set; }
    public Address Address { get; set; }
    public InsurancePolicyHolderType PolicyHolderRelationshipType { get; set; }
    public string PolicyHolderFirstName { get; set; }
    public string PolicyHolderMiddleName { get; set; }
    public string PolicyHolderLastName { get; set; }
    public DateOnly? PolicyHolderDateOfBirth { get; set; }
    public Sex? PolicyHolderSex { get; set; }
    public string PolicyHolderMemberId { get; set; }
    public string PolicyHolderGroupId { get; set; }
    public Address PolicyHolderAddress { get; set; }

    [Obsolete("Use PayerPhoneNumberDetails.CountryCode instead.")]
    private string _payerPhoneCountryCode;
    public string PayerPhoneCountryCode
    {
        get => _payerPhoneCountryCode ?? PayerPhoneNumberDetails?.CountryCode;
        set => _payerPhoneCountryCode = value;
    }

    [Obsolete("Use PayerPhoneNumberDetails.Number instead.")]
    private string _payerPhoneNumber;
    public string PayerPhoneNumber
    {
        get => _payerPhoneNumber ?? PayerPhoneNumberDetails?.Number;
        set => _payerPhoneNumber = value;
    }

    private PhoneNumber _payerPhoneNumberDetails;
    public PhoneNumber PayerPhoneNumberDetails
    {
        get
        {
            if (
                _payerPhoneNumberDetails == null
                && (_payerPhoneNumber != null || _payerPhoneCountryCode != null)
            )
            {
                _payerPhoneNumberDetails = new PhoneNumber(
                    _payerPhoneCountryCode,
                    _payerPhoneNumber
                );
            }
            return _payerPhoneNumberDetails;
        }
        set
        {
            _payerPhoneNumberDetails = value;
            _payerPhoneCountryCode = value?.CountryCode;
            _payerPhoneNumber = value?.Number;
        }
    }

    [Obsolete("Use PolicyHolderPhoneNumberDetails.CountryCode instead.")]
    private string _policyHolderPhoneCountryCode;
    public string PolicyHolderPhoneCountryCode
    {
        get => _policyHolderPhoneCountryCode ?? PolicyHolderPhoneNumberDetails?.CountryCode;
        set => _policyHolderPhoneCountryCode = value;
    }

    [Obsolete("Use PolicyHolderPhoneNumberDetails.Number instead.")]
    private string _policyHolderPhoneNumber;
    public string PolicyHolderPhoneNumber
    {
        get => _policyHolderPhoneNumber ?? PolicyHolderPhoneNumberDetails?.Number;
        set => _policyHolderPhoneNumber = value;
    }

    private PhoneNumber _policyHolderPhoneNumberDetails;
    public PhoneNumber PolicyHolderPhoneNumberDetails
    {
        get
        {
            if (
                _policyHolderPhoneNumberDetails == null
                && (_policyHolderPhoneNumber != null || _policyHolderPhoneCountryCode != null)
            )
            {
                _policyHolderPhoneNumberDetails = new PhoneNumber(
                    _policyHolderPhoneCountryCode,
                    _policyHolderPhoneNumber
                );
            }
            return _policyHolderPhoneNumberDetails;
        }
        set
        {
            _policyHolderPhoneNumberDetails = value;
            _policyHolderPhoneCountryCode = value?.CountryCode;
            _policyHolderPhoneNumber = value?.Number;
        }
    }

    public static ClaimContactInsurancePolicy Create(Guid providerId, ContactInsurancePolicy policy)
    {
        return new ClaimContactInsurancePolicy
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            ContactInsurancePolicy = policy,
            InsuranceType = policy.InsuranceType,
            PolicyHolderFirstName = policy.PolicyHolder?.Contact?.FirstName,
            PolicyHolderMiddleName = policy.PolicyHolder?.Contact?.MiddleNames,
            PolicyHolderLastName = policy.PolicyHolder?.Contact?.LastName,
            PolicyHolderDateOfBirth = policy.PolicyHolder?.Contact?.BirthDate,
            PolicyHolderPhoneNumberDetails = policy.PolicyHolder?.Contact?.PhoneNumber,
            PolicyHolderSex = ContactUtilities.GetSexValueFromOptionSetValue(
                policy.PolicyHolder?.Contact?.Sex
            ),
            PayerId = policy.Payer?.Id,
            PayerName = policy.Payer?.Name,
            PayerPhoneNumberDetails = policy.Payer?.PhoneNumber,
            PayerNumber = policy.Payer?.PayerId,
            Address = policy.Payer?.Address,
            CoverageType = policy.Payer?.CoverageType,
            OtherCoverageTypeName = policy.Payer?.OtherCoverageTypeName,
            PolicyHolderRelationshipType =
                policy.PolicyHolder?.Type ?? InsurancePolicyHolderType.Unknown,
            PolicyHolderGroupId = policy.GroupId,
            PolicyHolderMemberId = policy.MemberId,
            PolicyHolderAddress = policy.PolicyHolder?.Contact?.Address
        };
    }
}
