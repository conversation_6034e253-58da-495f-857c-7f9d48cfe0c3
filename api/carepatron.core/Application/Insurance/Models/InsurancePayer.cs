using carepatron.core.Models.Common;

namespace carepatron.core.Application.Insurance.Models;

public class InsurancePayer
{
    public string PayerId { get; set; }
    public ClearingHouseType ClearingHouse { get; set; }
    public string Name { get; set; }
    public string PayerType { get; set; }
    public string[] States { get; set; }
    public string PhoneNumber { get; set; }
    public Address Address { get; set; }
    public PayerTransactionAvailability Eligibility { get; set; }
    public PayerTransactionAvailability Professional { get; set; }
    public PayerTransactionAvailability ERA { get; set; }
    public PayerTransactionAvailability Attachment { get; set; }
}

public class AvailableInsurancePayer : InsurancePayer
{
    public bool AddedToWorkspace { get; set; }
}
