using System;
using System.Security.Claims;
using carepatron.core.Application.Insurance.Interfaces;
using carepatron.core.Application.Users.Models;
using carepatron.core.Constants;
using carepatron.core.Validation;
using FluentValidation;

namespace carepatron.core.Application.Insurance.Models;

public class ClaimRenderingProviderValidator : AbstractValidator<ClaimRenderingProvider>
{
    public ClaimRenderingProviderValidator()
    {
        RuleFor(x => x.OtherId)
            .IsRequired()
            .When(
                x =>
                    string.IsNullOrEmpty(x.NationalProviderId)
                    || !string.IsNullOrEmpty(x.OtherIdQualifier)
            )
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimRenderingProvider.OtherId)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimRenderingProvider.OtherId)));
        RuleFor(x => x.OtherIdQualifier)
            .IsRequired()
            .When(
                x => string.IsNullOrEmpty(x.NationalProviderId) || !string.IsNullOrEmpty(x.OtherId)
            )
            .WithErrorCode(
                Errors.RequiredFieldCode(nameof(ClaimRenderingProvider.OtherIdQualifier))
            )
            .WithMessage(
                Errors.RequiredFieldDetails(nameof(ClaimRenderingProvider.OtherIdQualifier))
            );
        RuleFor(x => x.NationalProviderId)
            .IsRequired()
            .When(x => string.IsNullOrEmpty(x.OtherId) && string.IsNullOrEmpty(x.OtherIdQualifier))
            .WithErrorCode(
                Errors.RequiredFieldCode(nameof(ClaimRenderingProvider.NationalProviderId))
            )
            .WithMessage(
                Errors.RequiredFieldDetails(nameof(ClaimRenderingProvider.NationalProviderId))
            );
        RuleFor(x => x.FirstName)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimRenderingProvider.FirstName)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimRenderingProvider.FirstName)));
        RuleFor(x => x.LastName)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimRenderingProvider.LastName)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimRenderingProvider.LastName)));
    }
}

public class ClaimRenderingProvider : IInsuranceProviderInfo
{
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public ClaimProviderStaff StaffMember { get; set; }
    public string FirstName { get; set; }
    public string MiddleName { get; set; }
    public string LastName { get; set; }
    public string NationalProviderId { get; set; }
    public string OtherIdQualifier { get; set; }
    public string OtherId { get; set; }
    public int OrderIndex { get; set; }

    public static ClaimRenderingProvider Create(
        Guid providerId,
        string npi,
        int orderIndex,
        SimplePerson person,
        ClaimProviderStaff staff
    )
    {
        return new ClaimRenderingProvider
        {
            Id = Guid.NewGuid(),
            StaffMember = staff,
            ProviderId = providerId,
            FirstName = person.FirstName,
            LastName = person.LastName,
            NationalProviderId = npi,
            OrderIndex = orderIndex
        };
    }
}
