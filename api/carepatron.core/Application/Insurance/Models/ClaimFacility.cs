using System;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Workspace.Locations.Models;
using carepatron.core.Constants;
using carepatron.core.Models.Common;
using carepatron.core.Validation;
using FluentValidation;

namespace carepatron.core.Application.Insurance.Models;

public class ClaimFacilityValidator : AbstractValidator<ClaimFacility>
{
    public ClaimFacilityValidator()
    {
        RuleFor(x => x.Name)
            .IsRequired()
            .When(s => s.ProviderLocationId.HasValue)
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimFacility.Name)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimFacility.Name)));
        RuleFor(x => x.Address)
            .IsRequired()
            .When(s => s.ProviderLocationId.HasValue)
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimBillingDetail.Address)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimBillingDetail.Address)));
        RuleFor(x => x.Address.StreetAddress)
            .IsRequired()
            .When(s => s.ProviderLocationId.HasValue && s.Address != null)
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimFacility.Address.StreetAddress)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimFacility.Address.StreetAddress)));
        RuleFor(x => x.OtherId)
            .IsRequired()
            .When(x => !string.IsNullOrEmpty(x.OtherIdQualifier))
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimFacility.OtherId)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimFacility.OtherId)));
        RuleFor(x => x.OtherIdQualifier)
            .IsRequired()
            .When(x => !string.IsNullOrEmpty(x.OtherId))
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimFacility.OtherIdQualifier)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimFacility.OtherIdQualifier)));
    }
}

public class ClaimFacility
{
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public Guid? ProviderLocationId { get; set; }
    public string Name { get; set; }
    public string PlaceOfService { get; set; }
    public Address Address { get; set; }
    public string NationalProviderId { get; set; }
    public string OtherIdQualifier { get; set; }
    public string OtherId { get; set; }

    public static ClaimFacility Create(Guid providerId, TaskLocationSnapshot location)
    {
        var addressDetails = location.AddressDetails;
        return new ClaimFacility
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            Name = location.Name,
            PlaceOfService = location.PosCode,
            ProviderLocationId = location.LocationId,
            Address = addressDetails is null
                ? new Address { StreetAddress = location.Address }
                : addressDetails
        };
    }
}
