using System;
using System.Linq;
using carepatron.core.Application.Insurance.Interfaces;
using carepatron.core.Extensions;

namespace carepatron.core.Application.Insurance.Models;

/// <summary>
/// Represents core insurance claim properties.
/// Additional claim types may inherit from this to capture additional information
/// </summary>
public class InsuranceClaim : IBaseInsuranceClaim
{
    public InsuranceClaim() { }

    public InsuranceClaim(
        Guid id,
        ClaimType type,
        ClaimStatus status,
        ClaimSubmissionMethod submissionMethod,
        Guid contactId,
        string number,
        decimal amount,
        DateOnly? fromDate,
        DateOnly? toDate,
        string currencyCode
    )
    {
        Id = id;
        Type = type;
        Status = status;
        SubmissionMethod = submissionMethod;
        ContactId = contactId;
        Number = number;
        Amount = amount;
        FromDate = fromDate;
        ToDate = toDate;
        CurrencyCode = currencyCode;
    }

    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public string StatusReason { get; set; }
    public Guid ContactId { get; set; }
    public ClaimType Type { get; set; }
    public ClaimStatus Status { get; set; }
    public ClaimSubmissionMethod SubmissionMethod { get; set; }
    public string Number { get; set; }
    
    /// <summary>
    /// Total charges for the claim, calculated from service lines (Tax Inclusive).
    /// </summary>
    public decimal Amount { get; set; }
    
    /// <summary>
    ///  The total amount already paid by the patient (Self pay) or the primary payer
    /// </summary>
    public decimal AmountPaid { get; set; }
    
    /// <summary>
    /// This is the amount due on the claim.
    /// </summary>
    public decimal BalanceDue => Math.Max(Amount - AmountPaid, 0);

    /// <summary>
    /// The total amount that has been allocated to this claim.
    /// </summary>
    public decimal BalancePaid { get; set; }

    public DateOnly? FromDate { get; set; }
    public DateOnly? ToDate { get; set; }
    public string CurrencyCode { get; set; }
    public string ClientControlNumber { get; set; }
    public InsuranceClaimContactReference Contact { get; set; }
    public ClaimServiceLine[] ServiceLines { get; set; }
    public InsurancePayerReference Payer { get; set; }
    public ClaimTaskMapping[] Tasks { get; set; }
    public DateTime? LastSubmittedDateTimeUtc { get; set; }
    public DateTime CreatedDateTimeUtc { get; set; }
    public DateTime UpdatedDateTimeUtc { get; set; }

    public void SetClaimDates(DateTime? fallbackDate = null)
    {
        if (ServiceLines?.Length > 0)
        {
            FromDate = ServiceLines.Min(x => x.Date);
            ToDate = ServiceLines.Max(x => x.Date);
        }
        else if (fallbackDate != null)
        {
            FromDate = fallbackDate.Value.ToFirstDayOfTheMonth().ToDateOnly();
            ToDate = fallbackDate.Value.ToLastDayOfTheMonth().ToDateOnly();
        }
    }

    public void SetAmount()
    {
        Amount = ServiceLines?.Sum(x => x.Amount) ?? 0;
    }

    public void SetAmountPaid(decimal amountPaid)
    {
        AmountPaid = amountPaid;
    }

    public void SetStatus(ClaimStatus status, string statusReason = null)
    {
        if (status == ClaimStatus.Submitted && Status != ClaimStatus.Submitted)
        {
            LastSubmittedDateTimeUtc = DateTime.UtcNow;
        }

        Status = status;
        StatusReason = statusReason;
    }

    /// <summary>
    /// Set the submission method for the claim.
    /// In future, this will consider the payer type and whether its from a clearing house.
    /// For now, it defaults to "Manual"
    /// </summary>
    /// <param name="submissionMethod"></param>
    public void SetSubmissionMethod(ClaimSubmissionMethod submissionMethod)
    {
        SubmissionMethod = submissionMethod == ClaimSubmissionMethod.None
                ? ClaimSubmissionMethod.Manual
                : submissionMethod;
    }
}
