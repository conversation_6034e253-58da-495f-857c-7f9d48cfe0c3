using System;
using carepatron.core.Application.Billables.Models;
using carepatron.core.Common;
using carepatron.core.Constants;
using carepatron.core.Validation;
using FluentValidation;

namespace carepatron.core.Application.Insurance.Models;

public class ClaimServiceLineValidator : AbstractValidator<ClaimServiceLine>
{
    public ClaimServiceLineValidator()
    {
        RuleFor(x => x.Date)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimServiceLine.Date)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimServiceLine.Date)));
        RuleFor(x => x.Amount)
            .IsRequired()
            .GreaterThan(0)
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimServiceLine.Amount)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimServiceLine.Amount)));
        RuleFor(x => x.Units)
            .IsRequired()
            .GreaterThan(0)
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimServiceLine.Units)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimServiceLine.Units)));
    }
}

public enum ClaimServiceLineEPSDT
{
    None = 0,
    AV = 1,
    S2 = 2,
    ST = 3,
    NU = 4
}

public class ClaimServiceLine
{
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public Guid InsuranceClaimId { get; set; }
    public DateOnly Date { get; set; }
    public string Code { get; set; }
    public decimal Units { get; set; }
    public string CurrencyCode { get; set; }

    private decimal _amount;
    public decimal Amount
    {
        get => _amount;
        // round to 4 decimal 
        set => _amount = Math.Round(value, 4);
    }

    public decimal TaxAmount { get; set; }
    public string POSCode { get; set; }
    public string Description { get; set; }
    public string Detail { get; set; }
    public Guid? ServiceId { get; set; }
    public string[] DiagnosticCodeReferences { get; set; }
    public string[] Modifiers { get; set; }
    public ClaimServiceLineEPSDT? EPSDT { get; set; }
    public bool FamilyPlanningService { get; set; }
    public bool Emergency { get; set; }
    public string SupplementalInfo { get; set; }
    public int OrderIndex { get; set; }
    public Guid? BillableItemId { get; set; }

    public void UpdateFromBillableItem(BillableItem item)
    {
        var handler = CurrencyHandler.Get(item.CurrencyCode);
        var amount = item.Price * item.Units;
        var taxAmount = handler.Round(amount * (item.TaxRate / 100));
        var total = handler.Round(amount + taxAmount);
        Amount = total;
        Units = item.Units;
        CurrencyCode = item.CurrencyCode;
        BillableItemId = item.Id;
    }

    public static ClaimServiceLine Create(BillableItem billableItem, Guid claimId)
    {
        var claimServiceLine = new ClaimServiceLine
        {
            Id = Guid.NewGuid(),
            ProviderId = billableItem.ProviderId,
            InsuranceClaimId = claimId,
            Date = billableItem.Date.Value,
            Code = billableItem.Code,
            CurrencyCode = billableItem.CurrencyCode,
            DiagnosticCodeReferences = [],
            Description = billableItem.Description,
            Detail = billableItem.Detail,
            ServiceId = billableItem.ServiceId,
            Modifiers = [],
            POSCode = billableItem.POSCode
        };
        claimServiceLine.UpdateFromBillableItem(billableItem);
        return claimServiceLine;
    }
}
