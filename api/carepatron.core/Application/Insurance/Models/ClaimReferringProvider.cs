using System;
using carepatron.core.Application.Insurance.Interfaces;
using carepatron.core.Constants;
using carepatron.core.Validation;
using FluentValidation;

namespace carepatron.core.Application.Insurance.Models;

public class ClaimReferringProviderValidator : AbstractValidator<ClaimReferringProvider>
{
    public ClaimReferringProviderValidator()
    {
        RuleFor(x => x.Qualifier)
            .IsRequired()
            .When(x => string.IsNullOrEmpty(x.NationalProviderId))
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimReferringProvider.Qualifier)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimReferringProvider.Qualifier)));
        RuleFor(x => x.NationalProviderId)
            .IsRequired()
            .When(
                x =>
                    (string.IsNullOrEmpty(x.OtherId) && string.IsNullOrEmpty(x.OtherIdQualifier))
                    || x.Qualifier == null
            )
            .WithErrorCode(
                Errors.RequiredFieldCode(nameof(ClaimReferringProvider.NationalProviderId))
            )
            .WithMessage(
                Errors.RequiredFieldDetails(nameof(ClaimReferringProvider.NationalProviderId))
            );
        RuleFor(x => x.OtherId)
            .IsRequired()
            .When(
                x =>
                    string.IsNullOrEmpty(x.NationalProviderId)
                    || !string.IsNullOrEmpty(x.OtherIdQualifier)
            )
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimReferringProvider.OtherId)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimReferringProvider.OtherId)));
        RuleFor(x => x.OtherIdQualifier)
            .IsRequired()
            .When(
                x => string.IsNullOrEmpty(x.NationalProviderId) || !string.IsNullOrEmpty(x.OtherId)
            )
            .WithErrorCode(
                Errors.RequiredFieldCode(nameof(ClaimReferringProvider.OtherIdQualifier))
            )
            .WithMessage(
                Errors.RequiredFieldDetails(nameof(ClaimReferringProvider.OtherIdQualifier))
            );
        RuleFor(x => x.FirstName)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimReferringProvider.FirstName)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimReferringProvider.FirstName)));
        RuleFor(x => x.LastName)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimReferringProvider.LastName)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimReferringProvider.LastName)));
    }
}

public class ClaimReferringProvider : IInsuranceProviderInfo
{
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string MiddleName { get; set; }
    public string NationalProviderId { get; set; }
    public ClaimProviderQualifier? Qualifier { get; set; }
    public string OtherIdQualifier { get; set; }
    public string OtherId { get; set; }
    public bool IncludeReferrerInformation { get; set; }
}
