using System;

namespace carepatron.core.Application.Insurance.Models;

public class InsuranceClaimClearingHouseMetadata
{
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }

    /// <summary>
    /// This is the insurance claim ID in the Carepatron system.
    /// </summary>
    public Guid InsuranceClaimId { get; set; }

    /// <summary>
    /// This is the clearing house type that the claim was submitted to.
    /// </summary>
    public ClearingHouseType ClearingHouse { get; set; }

    /// <summary>
    /// Represents actual payer ID from the clearing house. (e.g. 87842 of United Health)
    /// </summary>
    public string PayerId { get; set; }

    /// <summary>
    /// The unique claim identifier assigned by the clearing house for this claim
    /// </summary>
    public string ClearingHouseClaimId { get; set; }

    /// <summary>
    /// The unique claim identifier assigned by the insurance payer for this claim
    /// </summary>
    public string PayerClaimId { get; set; }

    public DateTime CreatedDateTimeUtc { get; set; }

    public InsuranceClaimClearingHouseMetadata Clone()
    {
        return new InsuranceClaimClearingHouseMetadata
        {
            Id = Id,
            ProviderId = ProviderId,
            InsuranceClaimId = InsuranceClaimId,
            ClearingHouse = ClearingHouse,
            PayerId = PayerId,
            ClearingHouseClaimId = ClearingHouseClaimId,
            PayerClaimId = PayerClaimId,
            CreatedDateTimeUtc = DateTime.UtcNow
        };
    }
}
