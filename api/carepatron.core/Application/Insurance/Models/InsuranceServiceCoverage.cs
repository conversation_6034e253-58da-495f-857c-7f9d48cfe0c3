using System;

namespace carepatron.core.Application.Insurance.Models;

public class InsuranceServiceCoverage
{
    public ProviderItemReference Service { get; set; }
    public InsurancePolicyServiceSettings[] PolicySettings { get; set; }
}

public record ContactInsurancePolicyServiceCoverageSetting(
    Guid ContactId,
    Guid? ServiceId,
    Guid InsurancePolicyId,
    InsurancePolicyServiceSettings Coverage
);

public class ContactInsuranceServiceCoverage
{
    public Guid ProviderId { get; set; }
    public Guid ContactId { get; set; }
    public Guid? ServiceId { get; set; }
    public decimal? ServicePrice { get; set; }
    public Guid? PolicyId { get; set; }
    public decimal? Copay { get; set; }
    public decimal? Coinsurance { get; set; }
    public decimal? InsuranceCopay { get; set; }
    public decimal? InsuranceCoinsurance { get; set; }
    public bool CoverageEnabled { get; set; }

    public decimal CalculateSelfPay(decimal? amount)
    {
        var defaultValue = amount ?? ServicePrice ?? 0;

        if (!CoverageEnabled)
            return defaultValue;

        // Use Copay if available
        if (Copay.HasValue)
        {
            return Math.Min(Copay.Value, defaultValue);
        }

        // Use Coinsurance if available
        if (Coinsurance.HasValue)
        {
            var coInsuranceValue = defaultValue * Coinsurance.Value;
            return Math.Min(coInsuranceValue, defaultValue);
        }

        // If Copay & Coinsurance are both missing/null, use InsuranceCopay
        if (InsuranceCopay.HasValue)
        {
            return Math.Min(InsuranceCopay.Value, defaultValue);
        }

        // If still missing, use InsuranceCoinsurance
        if (InsuranceCoinsurance.HasValue)
        {
            var coInsuranceValue = defaultValue * InsuranceCoinsurance.Value;
            return Math.Min(coInsuranceValue, defaultValue);
        }

        // Default case
        return defaultValue;
    }
}
