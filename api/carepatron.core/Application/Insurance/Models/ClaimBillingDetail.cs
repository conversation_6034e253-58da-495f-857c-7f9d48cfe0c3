using System;
using carepatron.core.Application.Billing.Models;
using carepatron.core.Constants;
using carepatron.core.Models.Common;
using carepatron.core.Validation;
using FluentValidation;

namespace carepatron.core.Application.Insurance.Models;

public class ClaimBillingDetailValidator : AbstractValidator<ClaimBillingDetail>
{
    public ClaimBillingDetailValidator()
    {
        RuleFor(x => x.BillingProfile)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimBillingDetail.BillingProfile)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimBillingDetail.BillingProfile)));
        RuleFor(x => x.Type)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimBillingDetail.Type)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimBillingDetail.Type)));
        RuleFor(x => x.TaxNumberType)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimBillingDetail.TaxNumberType)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimBillingDetail.TaxNumberType)));
        RuleFor(x => x.TaxNumber)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimBillingDetail.TaxNumber)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimBillingDetail.TaxNumber)));
        RuleFor(x => x.TaxonomyCode)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimBillingDetail.TaxonomyCode)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimBillingDetail.TaxonomyCode)));
        RuleFor(x => x.NationalProviderId)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimBillingDetail.NationalProviderId)))
            .WithMessage(
                Errors.RequiredFieldDetails(nameof(ClaimBillingDetail.NationalProviderId))
            );
        RuleFor(x => x.OtherId)
            .IsRequired()
            .When(
                x =>
                    string.IsNullOrEmpty(x.NationalProviderId)
                    || !string.IsNullOrEmpty(x.OtherIdQualifier)
            )
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimBillingDetail.OtherId)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimBillingDetail.OtherId)));
        RuleFor(x => x.OtherIdQualifier)
            .IsRequired()
            .When(
                x => string.IsNullOrEmpty(x.NationalProviderId) || !string.IsNullOrEmpty(x.OtherId)
            )
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimBillingDetail.OtherIdQualifier)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimBillingDetail.OtherIdQualifier)));
        RuleFor(x => x.Address)
            .IsRequired()
            .WithErrorCode(Errors.RequiredFieldCode(nameof(ClaimBillingDetail.Address)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(ClaimBillingDetail.Address)));
        RuleFor(x => x.Address.StreetAddress)
            .IsRequired()
            .When(s => s.Address != null)
            .WithErrorCode(Errors.RequiredFieldCode(nameof(Address.StreetAddress)))
            .WithMessage(Errors.RequiredFieldDetails(nameof(Address.StreetAddress)));
    }
}

public class ClaimBillingDetail
{
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public ProviderBillingProfile BillingProfile { get; set; }
    public BillingProfileType Type { get; set; }
    public string Name { get; set; }
    public string TaxNumberType { get; set; }
    public string TaxNumber { get; set; }
    public string NationalProviderId { get; set; }
    public string TaxonomyCode { get; set; }
    public Address Address { get; set; }
    public string OtherIdQualifier { get; set; }
    public string OtherId { get; set; }

    [Obsolete("Use PhoneNumber.CountryCode instead.")]
    private string _phoneCountryCode;
    public string PhoneCountryCode
    {
        get => _phoneCountryCode ?? PhoneNumberDetails?.CountryCode;
        set => _phoneCountryCode = value;
    }

    [Obsolete("Use PhoneNumber.Number instead.")]
    private string _phoneNumber;
    public string PhoneNumber
    {
        get => _phoneNumber ?? PhoneNumberDetails?.Number;
        set => _phoneNumber = value;
    }

    private PhoneNumber _phoneNumberDetails;
    public PhoneNumber PhoneNumberDetails
    {
        get
        {
            if (_phoneNumberDetails == null && (_phoneNumber != null || _phoneCountryCode != null))
            {
                _phoneNumberDetails = new PhoneNumber(_phoneCountryCode, _phoneNumber);
            }
            return _phoneNumberDetails;
        }
        set
        {
            _phoneNumberDetails = value;
            _phoneCountryCode = value?.CountryCode;
            _phoneNumber = value?.Number;
        }
    }

    public static ClaimBillingDetail Create(Guid providerId, ProviderBillingProfile billingProfile)
    {
        return new ClaimBillingDetail
        {
            Id = Guid.NewGuid(),
            ProviderId = providerId,
            Name = billingProfile.Name,
            BillingProfile = billingProfile,
            Type = billingProfile.Type ?? BillingProfileType.Individual,
            TaxNumberType = billingProfile.TaxNumberType,
            TaxNumber = billingProfile.TaxNumber,
            NationalProviderId = billingProfile.NationalProviderId,
            TaxonomyCode = billingProfile.TaxonomyCode,
            Address = billingProfile.Address,
            PhoneNumberDetails = billingProfile.PhoneNumberDetails
        };
    }
}
