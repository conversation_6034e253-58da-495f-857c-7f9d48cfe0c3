using System;
using carepatron.core.Application.Contacts.Models;

namespace carepatron.core.Application.Insurance.Models.ClearingHouse;


public class ClaimUploadResult
{
    public InsuranceClaimError[] Errors { get; set; }
    public InsuranceClaimClearingHouseMetadata Metadata { get; set; }
}

public class ClearingHouseSubmitClaimRequest
{
    public InsuranceClaimUSProfessional Claim { get; set; }
}

public class ClearingHouseSubmitClaimResponse
{
    public ClaimUploadResult[] ClaimUploadResults { get; set; } = [];
}

public class ClearingHouseVerifyClaimRequest
{
}

public class ClearingHouseVerifyClaimResponse
{
}
