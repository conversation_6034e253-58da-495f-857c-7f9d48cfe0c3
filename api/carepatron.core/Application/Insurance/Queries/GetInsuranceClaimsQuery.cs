using carepatron.core.Application.Insurance.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Pagination;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Insurance;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Insurance.Queries;

public record GetInsuranceClaimsQuery(
    Guid ProviderId,
    Guid? ContactId,
    PaginationRequest PaginationRequest,
    ClaimStatus[] Statuses,
    DateTime? FromDate,
    DateTime? ToDate,
    Guid[] TaskIds,
    Guid? AssignedToPersonId = null,
    string SearchQuery = null
) : IMediatrQuery<PaginatedResult<InsuranceClaim>>;

public class GetInsuranceClaimsQueryHandler(
    IInsuranceClaimsRepository insuranceClaimsRepository
) : IMediatrQueryHandler<GetInsuranceClaimsQuery, PaginatedResult<InsuranceClaim>>
{
    public async Task<ExecutionResult<PaginatedResult<InsuranceClaim>>> Handle(GetInsuranceClaimsQuery query, CancellationToken cancellationToken)
    {
        var claims = await insuranceClaimsRepository.Get(
            query.ProviderId,
            query.ContactId,
            query.PaginationRequest,
            query.Statuses,
            query.FromDate,
            query.ToDate,
            query.TaskIds,
            cancellationToken,
            query.AssignedToPersonId,
            query.SearchQuery
        );
        return claims;
    }
}
