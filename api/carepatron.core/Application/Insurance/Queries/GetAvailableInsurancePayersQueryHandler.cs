using System;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Identity;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Pagination;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Insurance;

namespace carepatron.core.Application.Insurance.Queries;
public record GetAvailableInsurancePayersQuery(IIdentityContext IdentityContext, Guid ProviderId, string SearchTerm, string[] State, PaginationRequest Pagination) : IMediatrQuery<PaginatedResult<AvailableInsurancePayer>>;
public class GetAvailableInsurancePayersQueryHandler(IInsurancePayerRepository providerInsurancePayerRepository) : IMediatrQueryHandler<GetAvailableInsurancePayersQuery, PaginatedResult<AvailableInsurancePayer>>
{
    public  async Task<ExecutionResult<PaginatedResult<AvailableInsurancePayer>>> Handle(GetAvailableInsurancePayersQuery request, CancellationToken cancellationToken)
    {
        var result = await providerInsurancePayerRepository.GetAvailableInsurancePayers(request.ProviderId,
            request.SearchTerm,
            request.State,
            request.Pagination
        );
        return result;
    }
}