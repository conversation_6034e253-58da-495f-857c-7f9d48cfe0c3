using System;
using System.Linq;
using System.Text.Json;
using carepatron.core.Application.History.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Payments.Models;
using Newtonsoft.Json;

namespace carepatron.core.Application.Insurance.Extensions;

public static class ClaimHistoryExtensions
{
    public static EntityHistory ToClaimSubmittedHistory(
        this InsuranceClaimUSProfessional claim,
        EntityHistoryActor[] actors,
        InsuranceClaimClearingHouseMetadata metadata = null
    )
    {
        var actionDetail = new ClaimSubmittedHistoryActionDetail(
            ClaimHistoryModel.Create(claim),
            ClaimPayerHistoryModel.Create(claim),
            metadata is null
              ? null
              : new ClaimHistoryMetadataReferences(
                    metadata.ClearingHouseClaimId,
                    metadata.PayerClaimId
                )
        );
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimSubmitted, claim, actors, detail);
        return history;
    }

    public static EntityHistory ToClaimStatusChangedHistory(
        this InsuranceClaim claim,
        EntityHistoryActor[] actors
    )
    {
        var actionDetail = new ClaimStatusChangedHistoryActionDetail(
            ClaimHistoryModel.Create(claim)
        );
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimStatusChanged, claim, actors, detail);
        return history;
    }

    public static EntityHistory ToClaimDeniedHistory(
        this InsuranceClaimUSProfessional claim,
        EntityHistoryActor[] actors,
        InsuranceClaimError[] errors
    )
    {
        var actionDetail = new ClaimDeniedHistoryActionDetail(
            ClaimHistoryModel.Create(claim),
            ClaimPayerHistoryModel.Create(claim),
            errors
        );
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimDenied, claim, actors, detail);
        return history;
    }

    public static EntityHistory ToClaimManualPaymentHistory(
        this InsuranceClaimUSProfessional claim,
        EntityHistoryActor[] actors,
        Guid paymentId,
        ClaimStatus claimStatus,
        decimal amount,
        string currencyCode,
        string reference
    )
    {
        var claimModel = ClaimHistoryModel.Create(claim);
        claimModel.Status = claimStatus;
        var actionDetail = new ClaimManualPaymentHistoryActionDetail(
            claimModel,
            new ClaimPaymentHistoryModel
            {
                Id = paymentId,
                Amount = amount,
                CurrencyCode = currencyCode,
                Reference = reference
            }
        );
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimManualPayment, claim, actors, detail);
        return history;
    }

    public static EntityHistory ToClaimElectronicPaymentHistory(
        this InsuranceClaimUSProfessional claim,
        EntityHistoryActor[] actors,
        Payment payment,
        PaymentAllocationHistoryReference[] allocations,
        InsuranceClaimRemittance insuranceClaimRemittance
    )
    {
        // Check if the remittance has adjustments
        var remittanceHasAdjustments = insuranceClaimRemittance.TotalAdjustmentAmount != 0;
        var actionDetail = new ClaimElectronicPaymentHistoryActionDetail(
            ClaimHistoryModel.Create(claim),
            ClaimPayerHistoryModel.Create(claim),
            new ClaimPaymentHistoryModel
            {
                Id = payment.Id,
                Amount = payment.ChargeAmount,
                CurrencyCode = payment.CurrencyCode,
                Reference = payment.Reference,
                Allocations = allocations
            },
            new InsuranceClaimRemittanceHistoryModel
            {
                Id = insuranceClaimRemittance.Id,
                IsAdjusted = remittanceHasAdjustments,
                Reference = payment.Reference
            }
        );
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimElectronicPayment, claim, actors, detail);
        return history;
    }

    public static EntityHistory ToClaimTrashedHistory(
        this InsuranceClaimUSProfessional claim,
        EntityHistoryActor[] actors
    )
    {
        var actionDetail = new ClaimTrashedHistoryActionDetail(ClaimHistoryModel.Create(claim));
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimTrashed, claim, actors, detail);
        return history;
    }

    public static EntityHistory ToClaimRestoredHistory(
        this InsuranceClaimUSProfessional claim,
        EntityHistoryActor[] actors
    )
    {
        var actionDetail = new ClaimRestoredHistoryActionDetail(ClaimHistoryModel.Create(claim));
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimRestored, claim, actors, detail);
        return history;
    }

    public static EntityHistory ToClaimCreatedHistory(
        this InsuranceClaimUSProfessional claim,
        EntityHistoryActor[] actors
    )
    {
        var actionDetail = new ClaimCreatedHistoryActionDetail(ClaimHistoryModel.Create(claim));
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimCreated, claim, actors, detail);
        return history;
    }

    public static EntityHistory ToClaimExportedHistory(
        this InsuranceClaimUSProfessional claim,
        EntityHistoryActor[] actors
    )
    {
        var actionDetail = new ClaimExportedHistoryActionDetail(
            ClaimHistoryModel.Create(claim),
            ClaimAttachmentHistoryModel.Create(ClaimAttachmentHistoryType.CMS1500, claim.ExportId)
        );
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimExported, claim, actors, detail);
        return history;
    }

    public static EntityHistory ToClaimPrintedHistory(
        this InsuranceClaimUSProfessional claim,
        EntityHistoryActor[] actors
    )
    {
        var actionDetail = new ClaimExportedHistoryActionDetail(
            ClaimHistoryModel.Create(claim),
            ClaimAttachmentHistoryModel.Create(ClaimAttachmentHistoryType.Text, claim.PrintId)
        );
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimExported, claim, actors, detail);
        return history;
    }

    public static EntityHistory ToClaimReceivedHistory(
        this InsuranceClaimUSProfessional claim,
        ClaimReceivedReferenceHistoryModel metadata,
        ClaimHistorySenderType senderType,
        DateTime createdDateTimeUtc,
        string senderName,
        string message
    )
    {
        var actionDetail = new ClaimReceivedHistoryActionDetail(
            ClaimHistoryModel.Create(claim),
            ClaimPayerHistoryModel.Create(claim),
            new ClaimReceivedReferenceHistoryModel
            {
                ClearingHouseClaimId = metadata.ClearingHouseClaimId,
                PayerClaimId = metadata.PayerClaimId
            },
            senderType,
            senderName,
            message
        );
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimReceived, claim, [], detail, createdDateTimeUtc);
        return history;
    }

    public static EntityHistory ToClaimRejectedHistory(
        this InsuranceClaimUSProfessional claim,
        InsuranceClaimError[] errors,
        ClaimHistorySenderType sender,
        DateTime createdDateTimeUtc,
        EntityHistoryActor[] actors = null
    )
    {
        var actionDetail = new ClaimRejectedHistoryActionDetail(
            ClaimHistoryModel.CreateRejected(claim),
            ClaimPayerHistoryModel.Create(claim),
            errors,
            sender
        );
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimRejected, claim, actors, detail, createdDateTimeUtc);
        return history;
    }

    public static EntityHistory ToClaimERAReceivedHistory(
        this InsuranceClaimUSProfessional claim,
        EntityHistoryActor[] actors,
        InsuranceClaimRemittance insuranceClaimRemittance,
        InsuranceRemittanceAdvice insuranceRemittanceAdvice
    )
    {
        // Check if the remittance has adjustments
        var remittanceHasAdjustments = insuranceClaimRemittance.TotalAdjustmentAmount != 0;
        var actionDetail = new ERAReceivedHistoryActionDetail(
            ClaimHistoryModel.Create(claim),
            ClaimPayerHistoryModel.Create(claim),
            new InsuranceClaimRemittanceHistoryModel
            {
                Id = insuranceClaimRemittance.Id,
                IsAdjusted = remittanceHasAdjustments,
                Reference = insuranceRemittanceAdvice.PaymentReference
            }
        );
        var detail = JsonDocument.Parse(JsonConvert.SerializeObject(actionDetail));
        var history = BuildHistory(HistoryAction.ClaimERAReceived, claim, actors, detail);
        return history;
    }

    private static EntityHistory BuildHistory(
        HistoryAction action,
        InsuranceClaim claim,
        EntityHistoryActor[] actors,
        JsonDocument detail,
        DateTime? createdDateTimeUtc = null
    )
    {
        var historyId = Guid.NewGuid();
        // The claim contact can possibly be null (if a contact was deleted)
        // If the claim contact is null, we set the contacts to an empty array
        Guid[] contacts = claim.Contact is null ? [] : [claim.ContactId];

        return new EntityHistory
        {
            Id = historyId,
            EntityId = claim.Id,
            EntityType = EntityType.USProfessionalInsuranceClaim,
            Action = action,
            Details = detail,
            Contacts = contacts,
            CreatedDateTimeUtc = createdDateTimeUtc ?? DateTime.UtcNow,
            ProviderId = claim.ProviderId,
            Actors = actors
        };
    }
}
