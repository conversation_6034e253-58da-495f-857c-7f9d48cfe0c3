﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using carepatron.core.Abstractions;
using carepatron.core.Application.Automation.Abstractions;
using carepatron.core.Application.Automation.Constants;
using carepatron.core.Application.Automation.Models;
using carepatron.core.Application.Automation.Services;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Diagnoses.Utilities;
using carepatron.core.Application.Security.Models;
using carepatron.core.Application.Security.Services;
using carepatron.core.Application.ServiceReceipts.Events;
using carepatron.core.Application.ServiceReceipts.Messages;
using carepatron.core.Application.ServiceReceipts.Models;
using carepatron.core.Application.Staff.Models;
using carepatron.core.Application.Workspace.Billing.Models;
using carepatron.core.Application.Workspace.Providers.Models;
using carepatron.core.Extensions;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Models.Audit;
using carepatron.core.Models.Billing;
using carepatron.core.Models.Common;
using carepatron.core.Models.Invoices;
using carepatron.core.Repositories.Diagnoses;
using carepatron.core.Repositories.Invoices;
using carepatron.core.Repositories.Provider;
using carepatron.core.Repositories.ServiceReceipts;
using carepatron.core.Service;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using Serilog;

namespace carepatron.core.Application.ServiceReceipts.Services
{
    public interface IServiceReceiptService
    {
        ServiceReceipt CreateEmptyReceipt(Guid providerId, Guid contactId, string number, string title, string clientInfo, string staffInfo, string providerInfo, string currencyCode, Guid[] invoiceIds, Guid[] staffPersonIds, DiagnosticCodeReference[] diagnosticCodes, AuditActor creator);

        ServiceReceipt AddLineItems(ServiceReceipt receipt, ServiceReceiptLineItem[] lineItems);

        Task UpdateGenerateReceiptJobsForContact(Guid providerId, Guid contactId, bool enableAutomatedServiceReceipts);

        Task UpdateGenerateReceiptJobsForProvider(Guid providerId, ServiceReceiptAutomationCadence cadence);

        Task EnsureContactHasGenerateReceiptJob(Guid providerId, Guid contactId);

        Task<ScheduledEvent> BuildGenerateReceiptJob(Guid providerId, Guid contactId, DateTime scheduledFor);

        Task GenerateServiceReceipt(Guid contactId, Guid providerId, DateTime fromDateUtc, DateTime toDateUtc);
    }

    public class ServiceReceiptService(
        IScheduledEventService scheduledEventService,
        IContactAutomationSettingRepository contactAutomationSettingRepository,
        IInvoiceRepository invoiceRepository,
        IContactRepository contactRepository,
        IDiagnosisRepository diagnosisRepository,
        IProviderRepository providerRepository,
        IProviderStaffRepository providerStaffRepository,
        IPublicTokenService publicTokenService,
        IEmailService emailService,
        IServiceReceiptRepository serviceReceiptRepository,
        IUnitOfWork unitOfWork,
        IScheduledEventRepository scheduledEventRepository,
        IIntegrationEventPublisher eventPublisher
    ) : IServiceReceiptService
    {
        public ServiceReceipt CreateEmptyReceipt(Guid providerId, Guid contactId, string number, string title, string clientInfo, string staffInfo, string providerInfo, string currencyCode, Guid[] invoiceIds, Guid[] staffPersonIds, DiagnosticCodeReference[] diagnosticCodes, AuditActor creator)
        {
            var now = DateTime.UtcNow;

            return new ServiceReceipt(
                Guid.NewGuid(),
                providerId,
                contactId,
                number,
                title,
                ServiceReceiptStatus.Draft,
                clientInfo,
                staffInfo,
                providerInfo,
                currencyCode,
                invoiceIds ?? new Guid[0],
                staffPersonIds ?? new Guid[0],
                new List<ServiceReceiptLineItem>(),
                diagnosticCodes ?? new DiagnosticCodeReference[0],
                new AuditAction[] { new AuditAction(now, creator, "Created") },
                now,
                now);
        }

        public ServiceReceipt AddLineItems(ServiceReceipt receipt, ServiceReceiptLineItem[] lineItems)
        {
            return receipt with
            {
                LineItems = lineItems
                    .Select(x => x with { ServiceReceiptId = receipt.Id })
                    .ToList()
            };
        }

        public async Task UpdateGenerateReceiptJobsForProvider(Guid providerId, ServiceReceiptAutomationCadence cadence)
        {
            if (cadence == ServiceReceiptAutomationCadence.None)
            {
                //remove all jobs for the provider
                await scheduledEventService.DeleteScheduledEventsByProvider(providerId, MessageTypes.GenerateServiceReceipt);
            }
            else if (cadence == ServiceReceiptAutomationCadence.Monthly)
            {
                //create a job for each contact who is opted in.
                var automationSettings = await contactAutomationSettingRepository.GetWithEnableAutomatedServiceReceipts(providerId);

                //optionally, could also filter this for any contact who has a paid invoice this month.
                var models = new List<ScheduledEvent>();
                foreach (var setting in automationSettings)
                {
                    var model = await BuildGenerateReceiptJob(setting.ProviderId, setting.ContactId, DateTime.UtcNow);
                    models.Add(model);
                }

                await scheduledEventService.SaveScheduledEvents([.. models]);
            }
        }

        public async Task UpdateGenerateReceiptJobsForContact(Guid providerId, Guid contactId, bool enableAutomatedServiceReceipts)
        {
            if (enableAutomatedServiceReceipts)
            {
                var scheduledEvent = await BuildGenerateReceiptJob(providerId, contactId, DateTime.UtcNow);
                await scheduledEventService.SaveScheduledEvents(scheduledEvent);
            }
            else
            {
                await scheduledEventService.DeleteScheduledEventsByResource(contactId, ResourceTypes.Contact, MessageTypes.GenerateServiceReceipt);
            }
        }

        public async Task EnsureContactHasGenerateReceiptJob(Guid providerId, Guid contactId)
        {
            var automationSettings = await contactAutomationSettingRepository.Get(providerId, contactId);
            if (automationSettings?.EnableAutomatedServiceReceipts ?? false)
            {
                await UpdateGenerateReceiptJobsForContact(providerId, contactId, automationSettings.EnableAutomatedServiceReceipts);
            }
        }

        public async Task<ScheduledEvent> BuildGenerateReceiptJob(Guid providerId, Guid contactId, DateTime scheduledFor)
        {
            // Todo
            // We could use this to look up provider/contact timezones to get an appoximate time to
            // run the job
            //var timezones = TzdbDateTimeZoneSource.Default.ZoneLocations
            //    .Where(x => x.CountryCode.Equals("US", StringComparison.OrdinalIgnoreCase))
            //    .OrderBy(x => x.Longitude);

            //var earliestZone = timezones.First();
            //var latestZone = timezones.Last();

            //DateTime fromDateUtc = scheduleFor.ToTimeZoneDateTime(earliestZone.ZoneId).ToFirstDayOfTheMonth();
            //DateTime toDateUtc = scheduleFor.ToTimeZoneDateTime(latestZone.ZoneId).ToLastDayOfTheMonth();

            //var localNow = scheduleFor.ToTimeZoneDateTime(timezone);

            // +/- 13 hours to give a bit of padding around time zones, since we dont have a
            // timezone for the provider/contact.

            var existingEvents = await scheduledEventRepository.GetScheduledEventsByResource(
                contactId,
                ResourceTypes.Contact,
                MessageTypes.GenerateServiceReceipt
            );

            if (existingEvents.Count != 0)
            {
                await scheduledEventRepository.DeleteScheduledEvents(existingEvents.Select(x => x.Id).ToArray());
            }

            DateTime fromDateUtc = scheduledFor.ToFirstDayOfTheMonth().AddHours(-13).ToUniversalTime();
            DateTime toDateUtc = scheduledFor.ToLastDayOfTheMonth().AddHours(+13).ToUniversalTime();

            return new ScheduledEvent()
            {
                Id = Guid.NewGuid(),
                ProviderId = providerId,
                MessageType = MessageTypes.GenerateServiceReceipt,
                ResourceId = contactId,
                ResourceType = ResourceTypes.Contact,
                ScheduledDateTimeUtc = toDateUtc,
                PublishTarget = PublishTarget.TaskQueue,
                GroupId = $"billing.receipts.{providerId}",
                MessageDetail = JObject.FromObject(new GenerateServiceReceiptMessage(providerId, contactId, fromDateUtc, toDateUtc))
            };
        }

        public async Task GenerateServiceReceipt(Guid contactId,
            Guid providerId,
            DateTime fromDateUtc,
            DateTime toDateUtc)
        {
            var invoices = await invoiceRepository.GetContactsPaidInvoicesByDateRange(providerId, contactId, fromDateUtc, toDateUtc);

            if (invoices.Length <= 0) return;

            var invoiceLineItems = invoices
                .SelectMany(x => x.LineItems)
                .OrderBy(x => x.Date)
                .ToArray();

            if (invoiceLineItems.Length == 0) return;

            var contact = await contactRepository.Get(contactId);
            if (contact is null) return;

            var dateRange = GetReceiptDateRange(invoiceLineItems) // this date range is 'unspecified' because it's created from invoice line item dates, which have no time zones.
                ?? new DateRange(fromDateUtc.ToDateOnly(), toDateUtc.ToDateOnly(), DateTimeKind.Utc); // this date range is UTC and is fine to compare to diagnosis times.

            // HACK!
            // We're adding a day either side here due to an issue with invoice line item dates being a "local date"
            // This represents a local time (based on the users time zone when the invoice was created) and has no time zone information associated with it
            // So we have no way of translating this Local Date back to a UTC Instant for accurate comparisons.
            //
            // The impact is we may return slightly more diagnostic notes than needed, but they wont (or shouldnt)
            // match the dates when compared below.
            var diagnosis = (await diagnosisRepository.GetByDateRange(contactId, dateRange.FromDate.AddDays(-1), dateRange.ToDate.AddDays(1)))
                .OrderByDescending(x => x.DiagnosisDateUtc).ToArray();

            var billingSettings = await providerRepository.GetBillingSettings(providerId);

            var staff = invoices.SelectMany(x => x.Staff)
                .DistinctBy(x => x.Id);

            var staffMember = staff.Any()
                ? (await providerStaffRepository.GetStaffMember(providerId, staff.FirstOrDefault().Id))
                : null;

            List<ServiceReceiptLineItem> serviceReceiptLineItems = new();

            var invoiceLineItemDates = invoiceLineItems
                .Where(x => x.Date.HasValue)
                .Select(x => x.Date.Value)
                .Distinct()
                .ToArray();

            var (diagnosisItems, diagnosisPointers) = DiagnosisCodeUtilities.GetDiagnosisPointers(invoiceLineItemDates,
                diagnosis
            );

            foreach (var invoiceLineItem in invoiceLineItems)
            {
                ServiceReceiptLineItem serviceReceiptLineItem = new(
                   Guid.NewGuid(),
                   Guid.NewGuid(), // serviceReceiptId placeholder. will be overridden on serviceReceiptService.AddLineItems
                   invoiceLineItem.Date,
                   invoiceLineItem.Amount + invoiceLineItem.TaxAmount,
                   invoiceLineItem.Amount + invoiceLineItem.TaxAmount,
                   invoiceLineItem.Description,
                   invoiceLineItem.Detail,
                   invoiceLineItem.Code,
                   invoiceLineItem.Date != null && diagnosisPointers.TryGetValue(invoiceLineItem.Date.Value, out var codes) ? codes : null,
                   invoiceLineItem.Id,
                   invoiceLineItem.POSCode,
                   invoiceLineItem.Units);

                serviceReceiptLineItems.Add(serviceReceiptLineItem);
            }


            DiagnosticCodeReference[] diagnosticCodeReferences = diagnosisItems
                .Select((x, index) => new DiagnosticCodeReference(index + 1, x.Code, x.Description))
                .ToArray();

            var serviceReceipt = CreateEmptyReceipt(
                providerId,
                contactId,
                null,
                "Statement of reimbursement",
                FormatServiceReceiptClientInfo(contact),
                FormatServiceReceiptStaffInfo(staffMember),
                FormatServiceReceiptProviderInfo(billingSettings),
                billingSettings.CurrencyCode,
                invoices.Select(x => x.Id).ToArray(),
                staff.Select(x => x.Id).ToArray(),
                diagnosticCodeReferences,
                AuditActor.Automation);

            serviceReceipt = AddLineItems(serviceReceipt, serviceReceiptLineItems.ToArray());

            var serviceReceiptHasDxCode = serviceReceipt.LineItems
                .Any(x => x.DiagnosticCodeReferences.Any());

            var serviceReceiptHasPosCode = serviceReceipt.LineItems
                .All(x => !string.IsNullOrWhiteSpace(x.PosCode));

            if (!string.IsNullOrEmpty(contact?.Email) && serviceReceiptHasDxCode && serviceReceiptHasPosCode)
            {
                serviceReceipt = serviceReceipt with
                {
                    Status = ServiceReceiptStatus.Sent,
                    History = serviceReceipt.History.Append(new AuditAction(DateTime.UtcNow, AuditActor.Automation, "Sent")).ToArray()
                };
            }

            serviceReceipt = await SetNumberAndSave(serviceReceipt, providerId);

            if (serviceReceipt.Status == ServiceReceiptStatus.Sent)
            {
                var provider = await providerRepository.GetProvider(providerId);
                await SendServiceReceiptEmail(provider, serviceReceipt, contact, staffMember, billingSettings);
            }
            else
            {
                await eventPublisher.Publish(new OutgoingEvent<ServiceReceiptRequiresReviewEvent>(new(ServiceReceiptEventModel.Create(serviceReceipt, $"{contact?.FirstName} {contact?.LastName}"))));
            }
        }

        private async Task SendServiceReceiptEmail(Provider provider, ServiceReceipt serviceReceipt, Contact contact, ProviderStaffMember staff, ProviderBillingSettings billingSettings)
        {
            if (contact.Email is null) return;

            var token = await publicTokenService.GenerateToken(serviceReceipt.Id, TokenResource.ServiceReceipt, TokenPermissions.View, contact.PersonId, contact.Email, AuditActor.Automation);
            //ensure token is saved before sending. 
            await unitOfWork.SaveUnitOfWork();

            Uri url = publicTokenService.AppendTokenToUrl($"/ServiceReceipt/{serviceReceipt.Id}", token);
            var replyToEmail = staff?.Email ?? await GetOwnerEmail(billingSettings);

            await emailService.SendServiceReceipt(
                provider,
                new(contact.Email, $"{contact.FirstName} {contact.LastName}"),
                replyToEmail,
                url,
                [.. serviceReceipt.LineItems]
            );
        }

        private string FormatServiceReceiptClientInfo(Contact contact)
        {
            if (contact is null)
                return String.Empty;

            string name = new[] { contact.FirstName, contact.LastName }.TidyJoin(" ");
            string emailPhone = new[] { contact.Email, contact.PhoneNumber }.TidyJoin(", ");
            string birthDate = contact.BirthDate.HasValue
                ? $"Date of birth: {contact.BirthDate.Value.ToShortMonthFormat()}"
                : null;
            string address = contact.Address.ToString();

            return new[] { name, emailPhone, address, birthDate }.TidyJoin($"{Environment.NewLine}");
        }

        private string FormatServiceReceiptStaffInfo(ProviderStaffMember staff)
        {
            if (staff is null)
                return String.Empty;

            string name = new[] { staff.FirstName, staff.LastName }.TidyJoin(" ");
            string npi = !string.IsNullOrWhiteSpace(staff.NationalProviderId) ? $"NPI: {staff.NationalProviderId}" : null;
            string licenseNumber = !string.IsNullOrWhiteSpace(staff.LicenseNumber) ? $"License number: {staff.LicenseNumber}" : null;

            return new[] { name, staff.Email, staff.PhoneNumber, npi, licenseNumber }.TidyJoin($"{Environment.NewLine}");
        }

        private string FormatServiceReceiptProviderInfo(ProviderBillingSettings providerBillingSettings)
        {
            // Only include tax details if both tax name and tax number are set
            string taxDetails = string.IsNullOrEmpty(providerBillingSettings.TaxName) || string.IsNullOrEmpty(providerBillingSettings.TaxNumber)
                ? string.Empty
                : new[] { providerBillingSettings.TaxName, providerBillingSettings.TaxNumber }.TidyJoin(": ");

            string npi = !string.IsNullOrWhiteSpace(providerBillingSettings.NationalProviderId) ? $"NPI: {providerBillingSettings.NationalProviderId}" : null;

            return new[] { providerBillingSettings.ProviderName, taxDetails, npi }.TidyJoin(Environment.NewLine);
        }

        private async Task<Email> GetOwnerEmail(ProviderBillingSettings billingSettings)
        {
            var owner = await providerStaffRepository.GetProviderOwner(billingSettings.ProviderId);
            return owner.Email;
        }

        private DateRange GetReceiptDateRange(InvoiceLineItem[] lineItems)
        {
            var dates = lineItems
                    .Where(x => x.Date.HasValue)
                    .Select(x => x.Date.Value)
                    .OrderBy(x => x)
                    .ToArray();

            if (!dates.Any())
            {
                return null;
            }

            return new DateRange(dates[0], dates[dates.Length - 1]);
        }

        private async Task<ServiceReceipt> SetNumberAndSave(ServiceReceipt serviceReceipt, Guid providerId)
        {
            // Pretty hacky short-term workaround here while we assess the impact...
            // Receipt numbers are sequential across providers and the automation for
            // generating service receipts happens at the same time every month.
            // This means for a single provider we will likely have multiple generation requests
            // each for a different clients all looking to grab the same 'next number' at the same time.

            // We expect this race condition to cause errors, especially as the number of
            // contacts within a provider increases.

            // We need to look at alternative approaches here.
            // - Can the number be set entirely in the DB?
            // - Can we manage the storage and requests for the next number differently?
            // - Can we queue these requests up so they process sequentially per provider?
            for (int i = 0; i < 5; i++)
            {
                var now = DateTime.UtcNow;
                string serviceReceiptNumber = await serviceReceiptRepository.GetNextNumber(providerId, (i + 1) * 5);

                serviceReceipt = serviceReceipt with
                {
                    Number = serviceReceiptNumber,
                    CreatedDateTimeUtc = now,
                    LastUpdatedDateTimeUtc = now,
                };

                try
                {
                    await serviceReceiptRepository.Create(serviceReceipt);

                    //force a save
                    await unitOfWork.SaveUnitOfWork();

                    return serviceReceipt;
                }
                catch (DbUpdateException ex)
                {
                    // could also optionally check for the constraint itself. however this requires referencing
                    // postgresql lib for the more specific inner exception.

                    // pushing the check and retry into the repo means
                    // - has to work for regular creation (which may be a benefit)
                    // - may not work with the unit of work  due to save changes being triggered outside the repo.
                    Log.ForContext("Attempt", i).Warning(ex, "Service Receipt could not be created. Retrying...");
                }

                await Task.Delay(Random.Shared.Next(100, 700));
            }
            throw new ApplicationException("Receipt could not be generated within 5 attempts");
        }
    }
}
