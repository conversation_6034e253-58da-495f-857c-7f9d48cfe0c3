using System.ComponentModel;

namespace carepatron.core.Application.History.Models;

public enum HistoryAction
{
    None = 0,

    [Description("Claim.Submitted")]
    ClaimSubmitted = 1,

    [Description("Claim.StatusChanged")]
    ClaimStatusChanged = 2,

    [Description("Claim.Rejected")]
    ClaimRejected = 3,

    [Description("Claim.Denied")]
    ClaimDenied = 4,

    [Description("Claim.ManualPayment")]
    ClaimManualPayment = 5,

    [Description("Claim.ElectronicPayment")]
    ClaimElectronicPayment = 6,

    [Description("Claim.Trashed")]
    ClaimTrashed = 7,

    [Description("Claim.Restored")]
    ClaimRestored = 8,

    [Description("Claim.Created")]
    ClaimCreated = 9,

    [Description("Claim.Exported")]
    ClaimExported = 10,

    [Description("Claim.Received")]
    ClaimReceived = 11,

    [Description("Claim.ERAReceived")]
    ClaimERAReceived = 12
}
