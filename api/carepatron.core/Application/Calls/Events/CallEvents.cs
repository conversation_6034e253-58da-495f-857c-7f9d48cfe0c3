﻿using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;

namespace carepatron.core.Application.Calls.Events;

public record CallAttendeeAskedToJoinEvent(CallAttendeeJoinAttemptedEventModel Entity, EventInitiator Initiator) : IEntityEvent<CallAttendeeJoinAttemptedEventModel>;

public record CallAttendeeJoinAttemptedEvent(CallAttendeeJoinAttemptedEventModel Entity, EventInitiator Initiator) : IEntityEvent<CallAttendeeJoinAttemptedEventModel>;

public record CallAttendeeAskToJoinAdmittedEvent(CallAttendeeEventModel Entity, EventInitiator Initiator) : IEntityEvent<CallAttendeeEventModel>;

public record CallAttendeeAskToJoinDeniedEvent(CallAttendeeEventModel Entity, EventInitiator Initiator) : IEntityEvent<CallAttendeeEventModel>;