using carepatron.core.Application.Communications.Notifications.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using Notifications.Sdk.Client.Abstract;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Calls.Commands;

public record NotifyAttendeeAskToJoinAdmittedCommand(
    Guid CallId,
    Guid ProviderId,
    Guid PersonId,
    Guid CallAttendeeId,
    string CallAttendeeName,
    DateTime JoinedDateTime) : IMediatrCommand<Unit>;

public class NotifyAttendeeAskToJoinAdmittedHandler(
    INotificationsService notificationsService) : IMediatrCommandHandler<NotifyAttendeeAskToJoinAdmittedCommand, Unit>
{
    public async Task<ExecutionResult<Unit>> Handle(NotifyAttendeeAskToJoinAdmittedCommand request, CancellationToken cancellationToken)
    {
        await notificationsService.Notify(request.ProviderId, [request.PersonId], new CallAttendeeAdmitted(request.CallId, request.CallAttendeeId, request.CallAttendeeName, request.JoinedDateTime));

        return Unit.Value;
    }
}