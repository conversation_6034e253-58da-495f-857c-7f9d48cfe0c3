using carepatron.core.Application.Communications.Notifications.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using Notifications.Sdk.Client.Abstract;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Calls.Commands;

public record NotifyHostCallAttendeeAskedToJoinCommand(
    Guid CallId,
    Guid ProviderId,
    Guid HostPersonId,
    string CallAttendeeName, 
    DateTime JoinedDateTime) : IMediatrCommand<Unit>;

public class NotifyHostCallAttendeeAskedToJoinCommandHandler(
    INotificationsService notificationsService) : IMediatrCommandHandler<NotifyHostCallAttendeeAskedToJoinCommand, Unit>
{
    public async Task<ExecutionResult<Unit>> Handle(NotifyHostCallAttendeeAskedToJoinCommand request, CancellationToken cancellationToken)
    {
        await notificationsService.Notify(request.ProviderId, [request.HostPersonId], new CallAttendeeAskedToJoin(request.CallId, request.CallAttendeeName, request.JoinedDateTime));

        return Unit.Value;
    }
}