﻿using carepatron.core.Application.Calls.Events;
using carepatron.core.Common;
using carepatron.core.Identity;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Call;
using carepatron.core.Repositories.Person;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Calls.Commands;

public record InitiateJoinCallAttemptCommand(IIdentityContext IdentityContext, Guid CallId) : IMediatrCommand<Unit>;

public class InitiateJoinCallAttemptCommandHandler : IMediatrCommandHandler<InitiateJoinCallAttemptCommand, Unit>
{
    private readonly IPersonRepository personRepository;
    private readonly ICallRepository callRepository;
    private readonly IIntegrationEventPublisher eventPublisher;
    private readonly IDateTimeProvider dateTimeProvider;

    public InitiateJoinCallAttemptCommandHandler(
        IPersonRepository personRepository,
        ICallRepository callRepository,
        IIntegrationEventPublisher eventPublisher,
        IDateTimeProvider dateTimeProvider)
    {
        this.personRepository = personRepository;
        this.callRepository = callRepository;
        this.eventPublisher = eventPublisher;
        this.dateTimeProvider = dateTimeProvider;
    }

    public async Task<ExecutionResult<Unit>> Handle(InitiateJoinCallAttemptCommand request, CancellationToken cancellationToken)
    {
        var hasPersonId = request.IdentityContext is not null && request.IdentityContext.PersonId != default(Guid);
        var personId = hasPersonId ? request.IdentityContext.PersonId : (Guid?)null;

        var call = await callRepository.Get(request.CallId);
        var allAttendees = await callRepository.GetAllAttendees(call.Id);
        var activeAttendees = allAttendees.Where(a => a.IsActive).ToArray();
        var expectedAttendees = allAttendees;
        var activeHost = activeAttendees.FirstOrDefault(a => a.PersonId.HasValue && a.PersonId == call.CreatedByPersonId && a.IsActive);

        var attendeeName = hasPersonId ? (await personRepository.Get(request.IdentityContext.PersonId))?.FullName : "Someone";

        // Check if the attendee is already active (link reuse)
        var isLinkReused = activeAttendees.Any(a => a.PersonId == personId);

        // Check if the person trying to join is the host
        var isHostJoining = hasPersonId && personId == call.CreatedByPersonId;

        // If the host is trying to join, don't send any events
        if (isHostJoining)
        {
            return Unit.Value;
        }

        // Determine if we should notify the host that someone is waiting
        bool shouldNotifyHostOfWaitingAttendee = activeHost == null && !isLinkReused;

        if (shouldNotifyHostOfWaitingAttendee)
        {
            // Host is not on call yet: notify host that attendee is waiting
            await eventPublisher.Publish(new OutgoingEvent<CallAttendeeJoinAttemptedEvent>(new(new CallAttendeeJoinAttemptedEventModel(call.Id, call.ProviderId.Value, call.CreatedByPersonId, attendeeName, dateTimeProvider.GetDateTimeUtc()),
                hasPersonId ? new EventInitiator(request.IdentityContext.PersonId) : null)));
        }
        else if (isLinkReused || activeAttendees.Length >= expectedAttendees.Length)
        {
            // Notify host that attendee is waiting to be admitted when:
            // 1. The link is being reused, or
            // 2. The number of active attendees has reached the expected number
            await eventPublisher.Publish(new OutgoingEvent<CallAttendeeAskedToJoinEvent>(new(new CallAttendeeJoinAttemptedEventModel(call.Id, call.ProviderId.Value, call.CreatedByPersonId, attendeeName, dateTimeProvider.GetDateTimeUtc()),
                hasPersonId ? new EventInitiator(request.IdentityContext.PersonId) : null)));
        }

        return Unit.Value;
    }
}