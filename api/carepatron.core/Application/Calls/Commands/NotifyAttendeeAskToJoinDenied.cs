using carepatron.core.Application.Communications.Notifications.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using Notifications.Sdk.Client.Abstract;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Calls.Commands;

public record NotifyAttendeeAskToJoinDeniedCommand(
    Guid CallId,
    Guid ProviderId,
    Guid PersonId,
    Guid CallAttendeeId,
    string CallAttendeeName,
    DateTime JoinedDateTime) : IMediatrCommand<Unit>;

public class NotifyAttendeeAskToJoinDeniedHandler(
    INotificationsService notificationsService) : IMediatrCommandHandler<NotifyAttendeeAskToJoinDeniedCommand, Unit>
{
    public async Task<ExecutionResult<Unit>> Handle(NotifyAttendeeAskToJoinDeniedCommand request, CancellationToken cancellationToken)
    {
        await notificationsService.Notify(request.ProviderId, [request.PersonId], new CallAttendeeDenied(request.CallId, request.CallAttendeeId, request.CallAttendeeName, request.JoinedDateTime));

        return Unit.Value;
    }
}