using carepatron.core.Application.Calls.Events;
using carepatron.core.Constants;
using carepatron.core.Identity;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Models.Call;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Call;
using OneOf.Types;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Calls.Commands;

public record DenyAttendeeCommand(Guid CallId, Guid AttendeeId, IIdentityContext IdentityContext) : IMediatrCommand<CallAttendee>;

public class DenyAttendeeCommandHandler(ICallRepository callRepository, IIntegrationEventPublisher eventPublisher) : IMediatrCommandHandler<DenyAttendeeCommand, CallAttendee>
{
    public async Task<ExecutionResult<CallAttendee>> Handle(DenyAttendeeCommand request, CancellationToken cancellationToken)
    {
        var callAttendee = await callRepository.GetAttendeeByAttendeeId(request.CallId, request.AttendeeId);
        if (callAttendee is null)
            return new ValidationError(Errors.AttendeeNotFoundCode, Errors.AttendeeNotFoundDetails, ValidationType.NotFound);

        var call = await callRepository.Get(request.CallId);
        if (call is null)
            return new ValidationError(Errors.CallNotFoundCode, Errors.CallNotFoundDetails, ValidationType.NotFound);

        // Publish event if attendee has a PersonId
        if (callAttendee.PersonId.HasValue)
        {
            var eventModel = new CallAttendeeEventModel(
                 callAttendee.CallId,
                 call.ProviderId.Value,
                 callAttendee.Id,
                 callAttendee.PersonId.Value,
                 callAttendee.Name,
                 callAttendee.JoinedDateTimeUtc
             );

            await eventPublisher.Publish(new OutgoingEvent<CallAttendeeAskToJoinDeniedEvent>(new(eventModel, new EventInitiator(request.IdentityContext.PersonId))));
        }

        return callAttendee;
    }
}
