using System;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Workspace.Billing.Notifications;
using Notifications.Sdk.Client;
using Notifications.Sdk.Client.Abstract;

namespace carepatron.core.Application.Communications.Notifications.Models;
[NotificationsContent(NotificationCategoryRequest.BILLING_PAYMENT, InsuranceNotificationTypes.InsuranceEnrollmentRejected)]
public class EnrollmentRejectedNotificationContent : INotificationsContent
{
    public Guid PayerId { get; set; }
    public string PayerNumber { get; set; }
    public string PayerName { get; set; }
    public string Reason { get; set; }
    public PayerTransactionType PayerTransactionType { get; set; }
    public Guid ProviderPayerEnrollmentId { get; set; }
    public Guid ProviderPayerEnrollmentTransactionId { get; set; }
}