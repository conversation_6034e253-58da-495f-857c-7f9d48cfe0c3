﻿using carepatron.core.Application.Communications.Inbox.Extensions;
using carepatron.core.Extensions;
using HtmlAgilityPack;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;

namespace carepatron.core.Application.Communications.Notifications.Extensions;

/// <summary>
/// This is an extension method for smart chips for template management
/// </summary>
public static class SmartChipTemplateExtensions
{
    private static readonly string SmartChipIdPrefix = "cp.";
    private static readonly string SmartChipTag = "cp-smart-chip";
    private static readonly string SmartChipIdAttribute = "id";

    /// <summary>
    /// Fetches the smart chips used from an html string
    /// </summary>
    /// <param name="htmlContent"></param>
    /// <returns></returns>
    public static IList<string> GetSmartChipIdsFromHtml(this string htmlContent)
    {
        var htmlDoc = htmlContent.ToHtmlDocument();

        var smartChipNodes = GetSmartChipNodes(htmlDoc);

        if (smartChipNodes.IsNullOrEmpty()) return [];

        var smartChipIds = smartChipNodes
            .Select(el => el.GetAttributeValue(SmartChipIdAttribute))
            .ToArray();

        return [.. smartChipIds];
    }

    /// <summary>
    /// Replaces the smart chips in the html content with its resolved value
    /// </summary>
    /// <param name="htmlContent"></param>
    /// <param name="smartChips"></param>
    /// <returns></returns>
    public static string ReplaceSmartChipsInHtml(string htmlContent, IEnumerable<KeyValuePair<string, object>> smartChips)
    {
        var htmlDoc = htmlContent.ToHtmlDocument();

        var smartChipNodes = GetSmartChipNodes(htmlDoc);

        if (smartChipNodes.IsNullOrEmpty()) return htmlContent;

        foreach (var smartChipNode in smartChipNodes)
        {
            var id = smartChipNode.GetAttributeValue(SmartChipIdAttribute);

            if (id.IsNullOrEmpty()) continue;

            var smartChip = smartChips.FirstOrDefault(x => x.Key.Equals(id, StringComparison.InvariantCultureIgnoreCase));

            // Add logging to identify the smart chip
            if (smartChip.Key == null || smartChip.Value == null)
            {
                Log.Warning(string.Format("Smartchip id: {0} value has not been resolved", id));
            }

            // Fallback to string empty if smartchip value is not resolved
            var textNode = htmlDoc.CreateTextNode(smartChip.Value?.ToString() ?? string.Empty);

            smartChipNode.ParentNode.ReplaceChild(textNode, smartChipNode);
        }

        return htmlDoc.DocumentNode.OuterHtml;
    }

    public static string ToDefaultValue(this string smartChipId) => $"{{{{{smartChipId.Replace(SmartChipIdPrefix, string.Empty)}}}}}";

    private static HtmlNode[] GetSmartChipNodes(HtmlDocument htmlDoc)
    {
        return htmlDoc.GetDescendantsByName(SmartChipTag);
    }
}
