﻿using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Application.Communications.Inbox.Services;
using carepatron.core.Authorization.Constants;
using carepatron.core.Exceptions;
using carepatron.core.Extensions;
using carepatron.core.Models.Execution;
using carepatron.core.Paging.Models;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Utilities;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Communications.Inbox.Queries
{
    public class GetAllConversationsQueryHandler(IInboxService inboxService) : IMediatrQueryHandler<GetAllConversationsQuery, TokenisedPaginatedResult<ConversationResponse>>
    {
        private readonly IInboxService inboxService = inboxService;

        public async Task<ExecutionResult<TokenisedPaginatedResult<ConversationResponse>>> Handle(GetAllConversationsQuery request, CancellationToken cancellationToken)
        {
            var success = EnumExtensions.TryGetEnumValueFromDescription<MessageStatus>(request.FolderName, out var status);
            if (!success) throw new UnknownInboxFolderException(request.FolderName);

            var providerId = request.IdentityContext.ProviderPermissions.ProviderId;
            var personId = request.IdentityContext.ProviderPermissions.PersonId;
            var tokenPaginationRequest = !request.IsCountOnly
                ? TokenPaginationUtilities.CreateTokenisedPaginationRequest(request.Pagination, request.Limit)
                : null;

            var inboxIds = await inboxService.GetAuthorizedInboxIds(providerId, personId, PolicyOperationValues.View);

            return await inboxService.GetInboxConversations(providerId, inboxIds, status, request.SortBy, request.FilterBy, request.SearchTerm, request.IsCountOnly, tokenPaginationRequest);
        }
    }
}
