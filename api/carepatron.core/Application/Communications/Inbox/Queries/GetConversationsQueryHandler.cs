﻿using carepatron.core.Application.Communications.Inbox.Abstractions;
using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Application.Communications.Inbox.Services;
using carepatron.core.Exceptions;
using carepatron.core.Extensions;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Pagination;
using carepatron.core.Paging.Models;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Utilities;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Communications.Inbox.Queries
{
    public class GetConversationsQueryHandler(IInboxRepository inboxRepository,
        IInboxService inboxService) : IMediatrQueryHandler<GetConversationsQuery, TokenisedPaginatedResult<ConversationResponse>>
    {
        private readonly IInboxRepository inboxRepository = inboxRepository;
        private readonly IInboxService inboxService = inboxService;

        public async Task<ExecutionResult<TokenisedPaginatedResult<ConversationResponse>>> Handle(GetConversationsQuery request, CancellationToken cancellationToken)
        {
            var success = EnumExtensions.TryGetEnumValueFromDescription<MessageStatus>(request.FolderName, out var status);
            if (!success) throw new UnknownInboxFolderException(request.FolderName);

            var providerId = request.IdentityContext.ProviderPermissions.ProviderId;

            var tokenPaginationRequest = !request.IsCountOnly
                ? TokenPaginationUtilities.CreateTokenisedPaginationRequest(request.Pagination, request.Limit)
                : null;

            if (request.ContactId.HasValue)
            {
                return await GetConversationsByContactId(providerId, request.ContactId.Value, status, request.SortBy, request.FilterBy, request.SearchTerm, request.IsCountOnly, tokenPaginationRequest);
            }

            return await GetConversationsByInboxId(providerId, request.InboxId.Value, status, request.SortBy, request.FilterBy, request.SearchTerm, request.IsCountOnly, tokenPaginationRequest);
        }

        private async Task<ExecutionResult<TokenisedPaginatedResult<ConversationResponse>>> GetConversationsByInboxId(Guid providerId,
            Guid inboxId,
            MessageStatus status,
            ConversationSorting sortBy,
            ConversationFiltering filterBy,
            string searchTerm,
            bool isCountOnly,
            TokenisedPaginationRequest tokenPaginationRequest
            )
        {
            var inbox = await inboxRepository.Get(inboxId);

            if (inbox == null) return new TokenisedPaginatedResult<ConversationResponse>([]);

            return await inboxService.GetInboxConversations(providerId, [inbox.Id], status, sortBy, filterBy, searchTerm, isCountOnly, tokenPaginationRequest);
        }

        private async Task<ExecutionResult<TokenisedPaginatedResult<ConversationResponse>>> GetConversationsByContactId(Guid providerId,
            Guid contactId,
            MessageStatus status,
            ConversationSorting sortBy,
            ConversationFiltering filterBy,
            string searchTerm,
            bool isCountOnly,
            TokenisedPaginationRequest tokenPaginationRequest
            )
        {
            return await inboxService.GetContactConversations(providerId,
                contactId,
                status,
                sortBy,
                filterBy,
                searchTerm,
                isCountOnly,
                tokenPaginationRequest);
        }
    }
}
