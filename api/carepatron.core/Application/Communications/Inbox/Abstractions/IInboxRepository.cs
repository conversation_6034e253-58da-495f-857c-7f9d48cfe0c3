﻿using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Application.Communications.Inbox.Models.Settings;
using carepatron.core.Models.Pagination;
using carepatron.core.Paging.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace carepatron.core.Application.Communications.Inbox.Abstractions
{
    public interface IInboxRepository
    {
        Task<InboxInfo> Get(Guid providerId, Guid personId);

        Task<InboxInfo> Get(Guid id);

        Task<InboxInfo> SaveInbox(InboxInfo inboxInfo);

        Task CreateInbox(InboxSetting inboxSetting);

        Task<InboxStaff[]> GetInboxStaffsByRole(Guid inboxId, InboxStaffRoleType inboxStaffRoleType);

        Task AddInboxStaffs(Guid inboxId, InboxStaff[] inboxStaffs);

        Task DeleteInboxStaffs(Guid[] inboxStaffIds);

        Task DeleteInbox(Guid id);

        Task<InboxAccount> AddInboxAccount(InboxAccount inboxAccount);
      
        Task<InboxAccount> UpdateInboxAccountLastSync(Guid inboxAccountId, DateTime lastSync, InboxAccountSettings settings = null);

        Task<InboxAccount> UpdateInboxAccountSettings(Guid inboxAccountId, InboxAccountSettings settings);

        Task<InboxAccount[]> GetInboxAccounts(Guid providerId, Guid personId, ExternalSource externalSource);

        Task DeleteInboxAccount(Guid inboxAccountId);

        Task DeleteInboxAccounts(Guid[] inboxAccountIds);

        Task<InboxAccount> GetInboxAccount(Guid inboxId, string accountId, ExternalSource externalSource);

        Task<PaginatedResult<InboxAccount>> GetInboxAccountsWithWebHookSync(PaginationRequest pagination, ExternalSource[] externalSources);
      
        Task<InboxInfo[]> GetInboxInfosByAccountId(string accountId, ExternalSource externalSource);

        Task<InboxAccount> GetInboxAccountById(Guid inboxAccountId);

        Task<InboxAccount[]> GetInboxAccountsByProviderId(Guid providerId);

        Task<Dictionary<Guid, int>> GetAllUnreadConversationsCount(Guid[] inboxIds);

        Task<ConversationResponse[]> GetConversation(Guid providerId,
            Guid inboxId,
            Guid conversationId,
            MessageStatus status);

        Task<InboxMessage> GetMessage(Guid inboxId, Guid inboxMessageId);

        Task<MessageResponse> GetMessageResponse(Guid inboxId, Guid inboxMessageId);

        Task<InboxMessage[]> GetMessages(Guid inboxId, Guid conversationId, MessageStatus status);

        Task<TokenisedPaginatedResult<MessageResponse>> GetMessagesByConversationId(Guid providerId, Guid[] inboxIds, MessageStatus status, Guid conversationId, TokenisedPaginationRequest pagination);

        Task<TokenisedPaginatedResult<MessageResponse>> GetMessagesByContactConversation(Guid providerId, Guid contactId, MessageStatus status, Guid conversationId, TokenisedPaginationRequest pagination);

        Task<Dictionary<Guid, bool>> HasInboxMessages(params Guid[] inboxIds);

        Task<InboxMessage[]> AddInboxMessages(InboxMessage[] inboxMessages, Guid personId, DateTime lastSync);

        Task<MessageUpdateResponse[]> UpdateInboxMessageStatusByConversation(Guid[] inboxIds,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus status,
            MessageStatus[] statusFilter,
            ConversationFiltering filterBy);

        Task UpdateInboxMessageStatusByConversation(Guid inboxId, Guid[] conversationIds, MessageStatus destinationStatus, MessageStatus[] sourceStatuses);

        Task<MessageUpdateResponse[]> UpdateInboxMessageSetReadByConversation(Guid[] inboxIds,
            Guid personId,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus statusFilter);

        Task<MessageUpdateResponse[]> UpdateInboxMessageSetReadByContactConversation(Guid providerId,
            Guid contactId,
            Guid personId,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus statusFilter);

        Task<MessageUpdateResponse[]> UpdateInboxMessageSetUnreadByConversation(Guid[] inboxIds,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus statusFilter);

        Task<MessageUpdateResponse[]> UpdateInboxMessageSetUnreadByContactConversation(Guid providerId,
            Guid contactId,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            MessageStatus statusFilter);

        Task<InboxMessage[]> DeleteInboxMessagesByConversation(Guid[] inboxId,
            Guid[] selectedConversationIds,
            Guid[] excludedConversationIds,
            ConversationFiltering filterBy);

        Task<InboxMessageAttachment[]> AddInboxMessageAttachments(InboxMessageAttachment[] messageAttachments);

        Task<MessageAttachment> AddInboxMessageAttachment(InboxMessageAttachment messageAttachment);

        Task<InboxMessageAttachment> GetInboxMessageAttachment(Guid inboxMessageAttachmentId);

        Task<InboxMessageAttachment[]> GetInboxMessageAttachments(Guid inboxMessageId);

        Task DeleteInboxMessageAttachments(params Guid[] inboxMessageAttachmentId);

        Task<MessageAttachmentStream[]> GetMessageAttachmentStreamByMessage(Guid inboxMessageId);

        Task<MessageAttachmentStream> GetMessageAttachmentStream(Guid attachmentId);

        Task<Guid> AddInboxMessage(InboxMessage message);

        Task AddInboxMessageSeenBy(Guid inboxMessageId,
            Guid seenByPersonId,
            DateTime seenAt);

        Task<InboxMessage> GetInboxMessage(Guid inboxMessageId);

        Task UpdateInboxMessageDraft(InboxMessage draftMessage);

        Task<MessageResponse> UpdateSentInboxMessageDraft(Guid providerId, Guid inboxMessageDraftId, InboxMessage updatedSentDraftMessage, Guid personId, DateTime sentOn);

        Task DeleteInboxMessage(Guid inboxMessageId);

        Task DeleteInboxMessages(Guid[] inboxMessageIds);

        Task<InboxInfo[]> GetMultipleInboxes(PersonId personId);

        Task<Guid[]> GetMultipleInboxIdsOf(PersonId personId);

        Task<Dictionary<string, Guid>> GetInboxConversationsByEmailRfcMessageIds(Guid inboxId, params string[] emailRfcMessageIds);

        Task<Dictionary<string, Guid>> GetInboxConversationsByExternalConversationIds(Guid inboxId, params string[] externalConversationIds);
        
        Task<InboxMessage[]> DeleteInboxDraftMessagesByInboxAccountId(string accountId);

        Task<InboxMessage> GetInboxMessageBySendStatus(Guid inboxId, Guid inboxMessageId, MessageSendStatus sendStatus);

        Task<InboxMessageBulkTransaction> UpsertBulkTransaction(InboxMessageBulkTransaction bulkTransaction);

        Task<InboxMessageBulkTransaction> GetBulkTransaction(Guid bulkTransactionId);

        Task<Recipient[]> GetRecipients(Guid providerId, BulkRecipients bulkRecipients);

        Task<Recipient[]> GetSentRecipientsOfBulkMessage(InboxMessage hostedInboxMessage);

        Task<Guid[]> GetInboxIdsByPerson(Guid providerId, Guid personId, params InboxStaffRoleType[] roleTypes);
    }
}