﻿using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using System;
using System.Threading.Tasks;

namespace carepatron.core.Application.Communications.Inbox.Abstractions;
public interface IInboxMessageRepository
{
    Task<InboxMessage[]> GetHasAttachmentsByInboxId(Guid inboxId);

    Task<InboxMessage[]> GetHasAttachmentsByConversationIds(params Guid[] conversationIds);

    Task<InboxMessage> Get(Guid id);

    Task UpdateStatus(Guid inboxId,
        string[] messageIdsToDelete,
        string[] messageIdsToArchive,
        string[] messageIdsToOpen,
        MessageStatus[] filterStatuses,
        DateTime updatedAt);
}
