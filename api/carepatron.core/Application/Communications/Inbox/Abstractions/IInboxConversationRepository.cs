﻿using carepatron.core.Application.Communications.Inbox.Builders;
using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Paging.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace carepatron.core.Application.Communications.Inbox.Abstractions;
public interface IInboxConversationRepository
{
    Task<InboxConversation> Get(Guid id);

    Task<InboxConversation[]> GetByParticipant(Guid providerId,
            string accountId,
            AccountType accountType);

    Task Add(params InboxConversation[] conversations);

    Task Update(InboxConversation conversation);

    Task Delete(params Guid[] id);

    Task AddParticipants(params InboxConversationParticipant[] participants);

    Task UpdateParticipants(params InboxConversationParticipant[] participants);

    Task DeleteParticipants(params Guid[] participantIds);

    Task AddThreads(params InboxConversationThread[] threads);

    Task UpdateThreads(params InboxConversationThread[] threads);

    Task DeleteThreads(params Guid[] threadIds);

    Task DeleteMessages(params Guid[] conversationIds);

    Task<TokenisedPaginatedResult<ConversationResponse>> GetByParticipantAccount(Guid providerId,
        GetConversationResponseParams getConversationResponseParams,
        params string[] participantAccountIds);

    Task<TokenisedPaginatedResult<ConversationResponse>> GetByInboxIds(Guid providerId,
        GetConversationResponseParams getConversationResponseParams,
        params Guid[] inboxIds);

    Task<Dictionary<string, ConversationSummary>> GetSummary(Guid[] ids);
}
