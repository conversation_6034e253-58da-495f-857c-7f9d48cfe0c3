﻿using carepatron.core.Application.Communications.Inbox.Abstractions;
using carepatron.core.Application.Communications.Inbox.Builders;
using carepatron.core.Application.Communications.Inbox.Events;
using carepatron.core.Application.Communications.Inbox.Extensions;
using carepatron.core.Application.Communications.Inbox.Models;
using carepatron.core.Application.Communications.Inbox.Models.Enums;
using carepatron.core.Application.Communications.Inbox.Processor;
using carepatron.core.Application.Communications.Inbox.RulesEngine;
using carepatron.core.Application.Communications.Inbox.Utilities;
using carepatron.core.Application.ConnectedApps.Extensions;
using carepatron.core.Application.ConnectedApps.Models;
using carepatron.core.Application.ConnectedApps.Services;
using carepatron.core.Common;
using carepatron.core.Configuration;
using carepatron.core.Constants;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Extensions;
using carepatron.core.Models.Media;
using carepatron.core.Models.Pagination;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Paging.Models;
using carepatron.core.Repositories.Files;
using carepatron.core.Repositories.Person;
using carepatron.core.Repositories.Provider;
using carepatron.core.Repositories.SQS;
using carepatron.core.Services;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json.Linq;
using Notifications.Sdk.Client.Abstract;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.core.Application.Communications.Inbox.Services
{
    public interface IInboxService
    {
        Task<RecipientDetail> CreateAccountRecipientDetail(Guid providerId, Guid inboxId, string externalAccount, ExternalSource externalSource);

        Task CloseConversation(Guid inboxId, Guid conversationId);

        Task<InboxConversation> UpsertConversation(InboxMessage inboxMessage, Guid providerId, DateTime createdOn, Guid? draftForInboxMessageId);

        Task<InboxConversation> UpsertConversationThread(Guid inboxConversationId, Guid inboxMessageDraftId, string externalConversationId);

        Task ReOpenConversation(Guid inboxId, Guid conversationId);

        Task<InboxConversation> RollbackConversation(Guid inboxId,
            InboxConversation currentConversation,
            InboxConversation previousConversation,
            MessageStatus currentConversationStatus,
            MessageStatus? previousConversationStatus);

        Task<AttachmentStream[]> GetInboxMessageAttachmentsStream(Guid providerId, Guid inboxId, Guid inboxMessageId, string messageBody);

        Task<InboxMessage> CloneInboxMessage(InboxMessage sourceInboxMessage,
            Recipient recipient,
            Guid createdByPersonId);

        Task RaiseConversationMovedEvent(Guid providerId,
            Guid personId,
            Guid inboxId,
            Guid conversationId,
            MessageStatus fromConversationStatus,
            MessageStatus toConversationStatus);

        Task RaiseConversationMovedEvent(Guid providerId,
            Guid personId,
            (Guid inboxId, Guid conversationId)[] inboxConversations,
            MessageStatus fromConversationStatus,
            MessageStatus toConversationStatus);

        void ProcessInboxReplyFormat(Guid providerId, Guid inboxId, InboxReplyFormat replyFormat);

        Task CleanupInboxReplyFormatAttachments(Guid providerId,
            Guid inboxId,
            Guid personId,
            InboxReplyFormat replyFormat,
            bool isInboxDeleted);

        ReplyFormatAttachment[] GetInboxReplyFormatEmbeddedAttachments(Guid providerId,
            Guid inboxId,
            InboxReplyFormat inboxReplyFormat);

        Task<Guid[]> GetAuthorizedInboxIds(Guid providerId, Guid personId, string policyRequirement);

        Task<InboxMessage[]> SyncInboxMessages(Guid providerId, Models.InboxAccount account, InboxMessage[] newMessages, ConnectedApp connectedApp, Models.InboxAccount[] allInboxAccounts, MovedMessageIds movedMessageIds);

        Task<TokenisedPaginatedResult<ConversationResponse>> GetContactConversations(Guid providerId,
            Guid contactId,
            MessageStatus status,
            ConversationSorting sortBy,
            ConversationFiltering filterBy,
            string searchTerm,
            bool isCountOnly,
            TokenisedPaginationRequest pagination);

        Task<TokenisedPaginatedResult<ConversationResponse>> GetInboxConversations(Guid providerId,
            Guid[] inboxIds,
            MessageStatus status,
            ConversationSorting sortBy,
            ConversationFiltering filterBy,
            string searchTerm,
            bool isCountOnly,
            TokenisedPaginationRequest pagination);
    }

    public class InboxService(IConnectedAppService connectedAppService,
        IPersonRepository personRepository,
        IInboxRepository inboxRepository,
        IFileStorageRepository fileStorageRepository,
        IDateTimeProvider dateTimeProvider,
        ISqsRepository sqsRepository,
        [FromKeyedServices(MessageBodyProcessorType.Rendering)] IMessageBodyProcessor renderingMessageBodyProcessor,
        CommonConfiguration commonConfiguration,
        IMediaRepository mediaRepository,
        IInboxSettingRepository inboxSettingRepository,
        IMessageDeduper messageDeduper,
        ITrackedSenderRepository trackedSenderRepository,
        INotificationsService notificationsService,
        IFeatureService featureService,
        IInboxMessageService inboxMessageService,
        IInboxConversationRepository inboxConversationRepository,
        IInboxContactRepository inboxContactRepository,
        IProviderStaffRepository providerStaffRepository,
        IInboxMessageRepository inboxMessageRepository) : IInboxService
    {
        private readonly IConnectedAppService connectedAppService = connectedAppService;
        private readonly IPersonRepository personRepository = personRepository;
        private readonly IInboxRepository inboxRepository = inboxRepository;
        private readonly IFileStorageRepository fileStorageRepository = fileStorageRepository;
        private readonly IDateTimeProvider dateTimeProvider = dateTimeProvider;
        private readonly ISqsRepository sqsRepository = sqsRepository;
        private readonly IMessageBodyProcessor renderingMessageBodyProcessor = renderingMessageBodyProcessor;
        private readonly CommonConfiguration commonConfiguration = commonConfiguration;
        private readonly IMediaRepository mediaRepository = mediaRepository;
        private readonly IInboxSettingRepository inboxSettingRepository = inboxSettingRepository;
        private readonly INotificationsService notificationsService = notificationsService;
        private readonly IFeatureService featureService = featureService;
        private readonly IInboxMessageService inboxMessageService = inboxMessageService;
        private readonly IInboxConversationRepository inboxConversationRepository = inboxConversationRepository;
        private readonly IInboxContactRepository inboxContactRepository = inboxContactRepository;
        private readonly IProviderStaffRepository providerStaffRepository = providerStaffRepository;
        private readonly IInboxMessageRepository inboxMessageRepository = inboxMessageRepository;

        // This is a constant that is used to determine the batch size for moving conversations
        // This is used to prevent the SQS message size from exceeding the limit
        // ~2,817 items would be the theoretical max
        // Setting this to 1000 as a very conservative limit
        private const int bulkMoveConversationBatchSize = 1000;

        public async Task<RecipientDetail> CreateAccountRecipientDetail(Guid providerId, Guid inboxId, string externalAccount, ExternalSource externalSource)
        {
            var productCd = externalSource.ToDescription();

            var connectedApps = await connectedAppService.GetConnectedAppsWithInboxSettings(providerId, inboxId,
                [externalAccount],
                productCd);

            var connectedAppAccount = connectedApps.FindByInboxAccount(inboxId, externalAccount, productCd);

            if (connectedAppAccount is null) return new RecipientDetail(externalAccount);

            var person = await personRepository.Get(connectedAppAccount.PersonId.Value);

            return new RecipientDetail(person.FullName, externalAccount);
        }

        /// <summary>
        /// This method attempts to optimistically create a conversation record for the given inbox message.
        /// </summary>
        /// <param name="inboxMessage"></param>
        /// <param name="createdOn"></param>
        /// <returns></returns>
        public async Task<InboxConversation> UpsertConversation(InboxMessage inboxMessage, Guid providerId, DateTime createdOn, Guid? draftForInboxMessageId)
        {
            var draftInboxId = inboxMessage.InboxId;

            var inboxMessageConversation = (inboxMessage.Conversation is null)
                ? inboxMessage.CreateConversation(providerId, Guid.NewGuid(), createdOn)
                : inboxMessage.Conversation;

            if (!draftForInboxMessageId.IsNullOrEmpty())
            {
                var replyToInboxMessage = await inboxRepository.GetInboxMessage(draftForInboxMessageId.Value);

                if (draftInboxId == replyToInboxMessage.InboxId && !replyToInboxMessage.ConversationId.IsNullOrEmpty())
                {
                    var existingConversation = await inboxConversationRepository.Get(replyToInboxMessage.ConversationId.Value);

                    // Handle backwards compatibility
                    existingConversation.ConversationThreads ??= [];
                    existingConversation.ConversationParticipants ??= [];

                    var newConversationThreads = inboxMessageConversation.ConversationThreads
                        .Except(existingConversation.ConversationThreads, new InboxConversationThreadEqualityComparer())
                        .Select(x =>
                        {
                            x.ConversationId = existingConversation.Id;
                            return x;
                        })
                        .ToArray();

                    // Optimistically update the conversation threads
                    if (!newConversationThreads.IsNullOrEmpty()) { await inboxConversationRepository.AddThreads(newConversationThreads); }

                    var newConversationParticipants = inboxMessageConversation.ConversationParticipants
                        .Except(existingConversation.ConversationParticipants, new InboxConversationParticipantEqualityComparer())
                        .Select(x =>
                        {
                            x.ConversationId = existingConversation.Id;
                            return x;
                        })
                        .ToArray();

                    // Optimistically update the conversation participants
                    if (!newConversationParticipants.IsNullOrEmpty()) { await inboxConversationRepository.AddParticipants(newConversationParticipants); }

                    // Update the conversation for optimistic update;
                    existingConversation.ConversationParticipants = [.. existingConversation.ConversationParticipants, .. newConversationParticipants];
                    existingConversation.ConversationThreads = [.. existingConversation.ConversationThreads, .. newConversationThreads];

                    // Update the sent message conversation
                    return existingConversation;
                }
            }

            // Optimistically create the conversation record
            await inboxConversationRepository.Add([inboxMessageConversation]);

            return inboxMessageConversation;
        }

        public async Task<AttachmentStream[]> GetInboxMessageAttachmentsStream(Guid providerId,
                Guid inboxId,
                Guid inboxMessageId,
                string messageBody)
        {
            var inboxMessageAttachments = await inboxRepository.GetMessageAttachmentStreamByMessage(inboxMessageId);
            var replyFormatAttachments = await inboxSettingRepository.GetInboxReplyFormatAttachmentsStream(inboxId);

            // This ensures that we are only taking the reply format attachments that are embedded in the message body
            var response = ProcessHtml(providerId, inboxId, messageBody);

            var attachments = (AttachmentStream[])[.. inboxMessageAttachments, .. (replyFormatAttachments.Where(x => response.EmbeddedAttachmentIds.Contains(x.Id)))];

            foreach (var attachment in attachments)
            {
                // Get file stream from S3 Object
                var fileStream = await fileStorageRepository.GetObjectStream(attachment.FilePath);
                attachment.FileStream = fileStream;
            }

            return attachments;
        }

        public async Task<InboxConversation> UpsertConversationThread(Guid inboxConversationId, Guid inboxMessageDraftId, string externalConversationId)
        {
            var conversation = await inboxConversationRepository.Get(inboxConversationId);

            if (conversation is null) return null;

            // Reply Scenario w/ the Same Incoming Thread Id -> No update is necessary since the conversation thread already exists
            if (conversation.ConversationThreads.Any(x => x.ExternalConversationId == externalConversationId)) return conversation;

            var tempConversationThread = conversation.ConversationThreads.FirstOrDefault(x => x.ExternalConversationId == inboxMessageDraftId.ToString());

            // Compose Scenario w/ the Temporary Thread Id -> Update the temporary conversation thread record to the incoming thread data
            if (tempConversationThread is not null)
            {
                tempConversationThread.ExternalConversationId = externalConversationId;

                await inboxConversationRepository.UpdateThreads(tempConversationThread);

                conversation.ConversationThreads = conversation.ConversationThreads
                    .Select(x =>
                    {
                        if (x.Id != tempConversationThread.Id) return x;

                        x.ExternalConversationId = externalConversationId;

                        return x;
                    })
                    .ToArray();
            }
            else
            {
                // Reply Scnario w/ a Different Incoming Thread Id -> Create a new conversation thread record to ensure its threaded to the same conversation
                var newThread = new InboxConversationThread()
                {
                    Id = Guid.NewGuid(),
                    ConversationId = inboxConversationId,
                    ExternalConversationId = externalConversationId
                };

                await inboxConversationRepository.AddThreads([newThread]);

                conversation.ConversationThreads = [.. conversation.ConversationThreads, newThread];
            }

            return conversation;
        }
        
        public async Task ReOpenConversation(Guid inboxId, Guid conversationId)
        {
            await inboxRepository.UpdateInboxMessageStatusByConversation(inboxId, [conversationId], MessageStatus.Default, [MessageStatus.Archived, MessageStatus.Deleted]);
        }

        public async Task CloseConversation(Guid inboxId, Guid conversationId)
        {
            await inboxRepository.UpdateInboxMessageStatusByConversation(inboxId, [conversationId], MessageStatus.Archived, [MessageStatus.Default, MessageStatus.Deleted]);
        }

        public async Task<InboxConversation> RollbackConversation(Guid inboxId,
            InboxConversation currentConversation,
            InboxConversation previousConversation,
            MessageStatus currentConversationStatus,
            MessageStatus? previousConversationStatus)
        {
            var isNewConversation = previousConversation is null || previousConversation.Id != currentConversation.Id;
            var hasConversationStatusChanged = previousConversationStatus.HasValue && currentConversationStatus != previousConversationStatus;

            if (isNewConversation)
            {
                // Delete inbox conversation
                await inboxConversationRepository.Delete(currentConversation.Id);
                
                // Return null to indicate that the conversation has been deleted
                return null;
            }
            else
            {
                // Delete added participants
                var addedParticipants = currentConversation.ConversationParticipants
                    .Except(previousConversation.ConversationParticipants, new InboxConversationParticipantEqualityComparer());

                if (addedParticipants.Any()) await inboxConversationRepository.DeleteParticipants(addedParticipants.Select(x => x.Id).ToArray());

                // Delete added threads
                var addedThreads = currentConversation.ConversationThreads
                    .Except(previousConversation.ConversationThreads, new InboxConversationThreadEqualityComparer());

                if (addedThreads.Any()) await inboxConversationRepository.DeleteThreads(addedThreads.Select(x => x.Id).ToArray());

                // Update conversation status to its original state
                if (hasConversationStatusChanged)
                {
                    await inboxRepository.UpdateInboxMessageStatusByConversation(
                        [inboxId],
                        [currentConversation.Id],
                        [],
                        previousConversationStatus.Value,
                        [currentConversationStatus],
                        ConversationFiltering.All
                    );
                }

                return previousConversation;
            }
        }

        public async Task<InboxMessage> CloneInboxMessage(InboxMessage inboxMessage,
            Recipient recipient,
            Guid createdByPersonId)
        {
            var id = Guid.NewGuid();
            var clonedDraftMessage = new InboxMessage()
            {
                Id = id,
                InboxId = inboxMessage.InboxId,
                ExternalSource = inboxMessage.ExternalSource,
                CreatedAt = inboxMessage.CreatedAt,
                MessageBody = inboxMessage.MessageBody,
                MessageBodyPlainText = inboxMessage.MessageBodyPlainText,
                MessageType = inboxMessage.MessageType,
                MessageSubject = inboxMessage.MessageSubject,
                MessagePreview = inboxMessage.MessagePreview,
                From = inboxMessage.From,
                To = inboxMessage.To,
                Recipients = recipient,
                IsRead = inboxMessage.IsRead,
                Status = inboxMessage.Status,
                StatusChangedAt = inboxMessage.StatusChangedAt,
                DraftForInboxMessageId = inboxMessage.DraftForInboxMessageId,
                EmailRfcMessageId = inboxMessage.EmailRfcMessageId,
                LastSendStatus = inboxMessage.LastSendStatus,
                LastSendStatusAt = inboxMessage.LastSendStatusAt,
                LastSendAttemptByPersonId = inboxMessage.LastSendAttemptByPersonId,
                SendAttemptCount = inboxMessage.SendAttemptCount,
                LastSendAttemptError = inboxMessage.LastSendAttemptError,
                InboxMessageBulkTransactionId = inboxMessage.InboxMessageBulkTransactionId,
                DraftForInboxMessage = inboxMessage.DraftForInboxMessage,
                DraftMessages = inboxMessage.DraftMessages,
                DownloadTimestamp = inboxMessage.DownloadTimestamp,
                ExternalConversationId = id.ToString(),
                ExternalSourceMessageId = id.ToString(),
            };

            var attachments = new List<InboxMessageAttachment>();

            await inboxMessageService.AddMessage(clonedDraftMessage,
                createdByPersonId);

            foreach (var attachment in await inboxRepository.GetInboxMessageAttachments(inboxMessage.Id))
            {
                var messageAttachment = await CreateInboxAttachment(clonedDraftMessage, attachment);

                attachments.Add(messageAttachment);
            }

            clonedDraftMessage.MessageAttachments = attachments.ToArray();

            return clonedDraftMessage;
        }

        public async Task RaiseConversationMovedEvent(Guid providerId, Guid personId, Guid inboxId, Guid conversationId, MessageStatus fromConversationStatus, MessageStatus toConversationStatus) => await RaiseConversationMovedEvent(providerId, personId, [(inboxId, conversationId)], fromConversationStatus, toConversationStatus);

        public async Task RaiseConversationMovedEvent(Guid providerId, Guid personId, (Guid inboxId, Guid conversationId)[] inboxConversations, MessageStatus fromConversationStatus, MessageStatus toConversationStatus)
        {
            if (fromConversationStatus == toConversationStatus) return;

            if (inboxConversations.Length == 1)
            {
                var (inboxId, conversationId) = inboxConversations.Single();

                var evtMessage = new EventMessage(EventType.Message_Moved,
                    new EventData<InboxMessageMovedEventData>(new(providerId, inboxId, conversationId, fromConversationStatus, toConversationStatus)),
                    conversationId.ToString(),
                    personId);

                await sqsRepository.SendMessage(QueueType.Task, evtMessage);
            }
            else
            {
                var groupId = string.Format("{0}-{1}", providerId, personId);

                foreach (var inboxConversationsBatch in inboxConversations.Chunk(bulkMoveConversationBatchSize))
                {
                    var evtMessage = new EventMessage(EventType.BulkMessage_Moved,
                        new EventData<BulkInboxMessageMovedEventData>(new(providerId, personId, inboxConversationsBatch, fromConversationStatus, toConversationStatus)),
                        groupId,
                        personId);

                    await sqsRepository.SendMessage(QueueType.Task, evtMessage);
                }
            }
        }

        public void ProcessInboxReplyFormat(Guid providerId, Guid inboxId, InboxReplyFormat replyFormat)
        {
            if (replyFormat is null) return;
            if (replyFormat.SignatureAttachments.IsNullOrEmpty()) return;

            var response = ProcessHtml(providerId, inboxId, replyFormat.SignatureHtml);

            replyFormat.SignatureHtml = response.MessageBody;
            replyFormat.SignatureJson = ProcessSignatureJson(providerId, inboxId, replyFormat.SignatureJson, replyFormat.SignatureAttachments);
        }

        public async Task CleanupInboxReplyFormatAttachments(Guid providerId,
            Guid inboxId,
            Guid personId,
            InboxReplyFormat replyFormat,
            bool isInboxDeleted)
        {
            if (replyFormat is null) return;
            if (replyFormat.SignatureAttachments.IsNullOrEmpty()) return;

            var mediaIdsToDelete = Array.Empty<Guid>();

            // If inbox is deleted fetch all media attachments for cleanup
            if (isInboxDeleted)
            {
                mediaIdsToDelete = replyFormat.SignatureAttachments
                    .Select(x => x.MediaId)
                    .ToArray();
            }
            else
            {
                // If inbox is not deleted, process the signature html to get the actual embedded attachment ids
                var response = ProcessHtml(providerId, inboxId, replyFormat.SignatureHtml);

                mediaIdsToDelete = replyFormat.SignatureAttachments
                    .Where(x => !response.EmbeddedAttachmentIds.Contains(x.Id))
                    .Select(x => x.MediaId)
                    .ToArray();
            }

            if (mediaIdsToDelete.IsNullOrEmpty()) return;

            var deletedMedias = await mediaRepository.Delete(mediaIdsToDelete);

            await RaiseReplyFormatAttachmentCleanupEvent(inboxId, personId, deletedMedias);
        }


        public ReplyFormatAttachment[] GetInboxReplyFormatEmbeddedAttachments(Guid providerId,
            Guid inboxId,
            InboxReplyFormat inboxReplyFormat)
        {
            if (inboxReplyFormat.SignatureAttachments.IsNullOrEmpty()) return [];

            var response = ProcessHtml(providerId, inboxId, inboxReplyFormat.SignatureHtml);

            return inboxReplyFormat.SignatureAttachments
                .Where(x => response.EmbeddedAttachmentIds.Contains(x.Id))
                .ToArray();
        }

        public async Task<Guid[]> GetAuthorizedInboxIds(Guid providerId, Guid personId, string policyRequirement)
        {
            var inboxStaffRolesWithPolicy = InboxStaffExtensions.GetInboxStaffRolesWithPolicy(policyRequirement);

            return await inboxRepository.GetInboxIdsByPerson(providerId, personId, inboxStaffRolesWithPolicy);
        }

        public async Task<TokenisedPaginatedResult<ConversationResponse>> GetContactConversations(Guid providerId,
            Guid contactId,
            MessageStatus status,
            ConversationSorting sortBy,
            ConversationFiltering filterBy,
            string searchTerm,
            bool isCountOnly,
            TokenisedPaginationRequest pagination)
        {
            var inboxContactsForContact = await inboxContactRepository.GetByContactId(providerId, contactId);

            if (inboxContactsForContact.IsNullOrEmpty()) return TokenisedPaginatedResult<ConversationResponse>.Empty as TokenisedPaginatedResult<ConversationResponse>;

            var inboxAccounts = await inboxRepository.GetInboxAccountsByProviderId(providerId);

            var replyAccounts = (inboxAccounts ?? [])
                .Select(x => x.ExternalAccountId)
                .ToArray();

            var contactAccounts = inboxContactsForContact
                .Where(x => !x.AccountId.IsNullOrEmpty())
                .Select(x => x.AccountId.ToLower())
                .ToArray();

            var conversationParams = new GetConversationResponseParams(status,
                sortBy,
                filterBy,
                searchTerm,
                isCountOnly,
                replyAccounts,
                pagination);

            var contactConversations = await inboxConversationRepository.GetByParticipantAccount(providerId,
                conversationParams,
                contactAccounts);

            if (!contactConversations.Items.IsNullOrEmpty())
            {
                await EnrichConversationItems(contactConversations.Items, providerId);
            }

            return contactConversations;
        }

        public async Task<TokenisedPaginatedResult<ConversationResponse>> GetInboxConversations(Guid providerId,
            Guid[] inboxIds,
            MessageStatus status,
            ConversationSorting sortBy,
            ConversationFiltering filterBy,
            string searchTerm,
            bool isCountOnly,
            TokenisedPaginationRequest pagination)
        {
            var inboxAccounts = await inboxRepository.GetInboxAccountsByProviderId(providerId);

            var replyAccounts = (inboxAccounts ?? [])
                .Select(x => x.ExternalAccountId)
                .ToArray();

            var conversationParams = new GetConversationResponseParams(status,
                sortBy,
                filterBy,
                searchTerm,
                isCountOnly,
                replyAccounts,
                pagination);

            var inboxConversations = await inboxConversationRepository.GetByInboxIds(providerId,
                conversationParams,
                inboxIds);

            if (!inboxConversations.Items.IsNullOrEmpty())
            {
                await EnrichConversationItems(inboxConversations.Items, providerId);
            }

            return inboxConversations;
        }

        private async Task EnrichConversationItems(IList<ConversationResponse> conversations,
            Guid providerId)
        {
            var participantAccountIds = conversations.ExtractParticipantAccountIds();

            var participantInboxContacts = await inboxContactRepository.GetByAccountIds(providerId, participantAccountIds);

            var participantStaffs = await providerStaffRepository.GetProviderStaffMembersByProvider(providerId, participantAccountIds);

            var nonComposedDraftConversationIds = conversations
                .Where(x => !x.IsComposedDraftConversation)
                .Select(x => x.ConversationId.Value)
                .ToArray();

            var conversationSummaries = await inboxConversationRepository.GetSummary(nonComposedDraftConversationIds);

            // Add composed draft conversation summaries
            var composedDraftDict = conversations
                .Where(x => x.IsComposedDraftConversation)
                .Select(x => x.ConversationId.Value.ToString())
                .ToDictionary(id => id, _ => ConversationSummary.Draft);

            // TODO: Use participantInfoEnricher here which was introduced for chats
            conversations = new ConversationResponseBuilder(conversations,
                conversationSummaries.Concat(composedDraftDict).ToDictionary(x => x.Key, x => x.Value),
                participantInboxContacts,
                participantStaffs).Build();
        }

        private async Task<InboxMessageAttachment> CreateInboxAttachment(InboxMessage inboxMessage, InboxMessageAttachment sourceAttachment)
        {
            var attachmentId = Guid.NewGuid();

            var inbox = await inboxRepository.Get(inboxMessage.InboxId.Value);

            var fileKey = new InboxMessageAttachmentS3PathBuilder(inbox.ProviderId,
                inboxMessage.InboxId.Value,
                inboxMessage.Id,
                attachmentId).ToS3Path();

            await fileStorageRepository.CopyS3ObjectAsync(sourceAttachment.FileKey, fileKey);

            var inboxMessageAttachment = new InboxMessageAttachment()
            {
                Id = attachmentId,
                MediaId = Guid.NewGuid(),
                InboxMessageId = inboxMessage.Id,
                ExternalFileId = attachmentId.ToString(),
                FileName = sourceAttachment.FileName,
                FileExtensions = sourceAttachment.FileName.GetAttachmentExtension(),
                FileSize = sourceAttachment.FileSize,
                ContentType = sourceAttachment.ContentType,
                CreatedAt = dateTimeProvider.GetDateTimeUtc(),
                FileKey = fileKey
            };

            var messageAttachments = await inboxRepository.AddInboxMessageAttachments([inboxMessageAttachment]);

            return messageAttachments.First();
        }

        private MessageBodyProcessorResponse ProcessHtml(Guid providerId,
            Guid inboxId,
            string html)
        {
            var builder = new MessageBodyProcessorPayloadBuilder(html)
                .WithProviderId(providerId)
                .WithInboxId(inboxId);

            var response = renderingMessageBodyProcessor.Process(builder.Build());

            return response;
        }

        private JObject ProcessSignatureJson(Guid providerId,
            Guid inboxId,
            JObject signatureJson,
            ReplyFormatAttachment[] replyFormatAttachments)
        {
            var signatureImages = signatureJson.GetSignatureImageTokens();

            if (signatureImages.IsNullOrEmpty()) return signatureJson;

            foreach (var signatureImage in signatureImages)
            {
                var cpFileId = signatureImage.GetTokenCpFileIdentifier();

                if (cpFileId is null) continue;

                var cpFileGuid = Guid.Parse(cpFileId.ToString());
                var attachment = replyFormatAttachments.FirstOrDefault(x => x.Id == cpFileGuid);

                if (attachment is null) continue;

                var srcAttributeValue = new InboxReplyFormatImgSrcPathBuilder(commonConfiguration.CarepatronApiUri, providerId, inboxId, attachment.Id).ToSrcPath();

                signatureImage.SetTokenSrc(srcAttributeValue);
            }

            return signatureJson;
        }

        private async Task RaiseReplyFormatAttachmentCleanupEvent(Guid inboxId, Guid personId, Media[] deletedMedias)
        {
            var fileKeys = deletedMedias
                .Where(x => !x.Url.IsNullOrWhiteSpace())
                .Select(x => x.Url)
                .ToArray();

            // Use the same event model for message attachment deletion to avoid having multiple handlers
            // Might need to refactor this in the future to include where the event originated from
            var evtData = new EventData<InboxMessageAttachmentDeletedEvent>(new(inboxId, fileKeys));
            var groupId = inboxId.ToString();

            var evtMessage = new EventMessage(EventType.Inbox_AttachmentDeleted, evtData, groupId, personId);

            await sqsRepository.SendMessage(QueueType.Task, evtMessage);
        }

        private async Task<InboxMessage[]> TryDedupeMessages(Guid inboxId,
            InboxMessage[] syncedMessages,
            Models.InboxAccount[] allInboxAccounts)
        {
            Log.Information($"Starting deduping messages for inboxId: {inboxId}");

            // Dedup messages based on the equality comparer
            syncedMessages = syncedMessages
                .Distinct(new InboxMessageEqualityComparer())
                .ToArray();

            // Consolidate all email rfc message ids
            var syncedMessageIdentifiers = syncedMessages
                .Select(x => new
                {
                    x.EmailRfcMessageId,
                    x.ExternalConversationId
                })
                .ToArray();

            // Get if any message with the rfc message ids already exists in the database
            var dbConversationsByEmailRfcIdentifiers = await inboxRepository.GetInboxConversationsByEmailRfcMessageIds(inboxId,
                syncedMessageIdentifiers.Select(x => x.EmailRfcMessageId).ToArray());

            // Get threads matching the synced messages threads
            var dbConversationsByExternalConversationIds = await inboxRepository.GetInboxConversationsByExternalConversationIds(inboxId,
                syncedMessageIdentifiers.Select(x => x.ExternalConversationId).ToArray());

            var messageDeduleRulePayload = new MessageDeduperPayload(syncedMessages, allInboxAccounts, dbConversationsByEmailRfcIdentifiers, dbConversationsByExternalConversationIds);
            var messageDedupeRuleResponse = messageDeduper.ApplyRules(messageDeduleRulePayload);

            var syncedMessagesToAdd = messageDedupeRuleResponse
                .Where(x => x.DedupeAction == MessageDedupeAction.Add)
                .Select(x => x.InboxMessage)
                .ToArray();

            if (messageDedupeRuleResponse.Any(x => x.DedupeAction == MessageDedupeAction.DedupeAndAppendThread))
            {
                var conversationThreadsToAdd = new List<InboxConversationThread>();

                var messagesToAppendThreads = messageDedupeRuleResponse
                    .Where(x => x.DedupeAction == MessageDedupeAction.DedupeAndAppendThread)
                    .Select(x => x.InboxMessage)
                    .ToArray();

                foreach (var message in messagesToAppendThreads)
                {
                    var conversationId = dbConversationsByEmailRfcIdentifiers[message.EmailRfcMessageId];

                    var conversationThread = message.Conversation.ConversationThreads
                        .Select(x =>
                        {
                            x.ConversationId = conversationId;

                            return x;
                        })
                        .First();

                    conversationThreadsToAdd.Add(conversationThread);
                }

                await inboxConversationRepository.AddThreads([.. conversationThreadsToAdd]);
            }

            Log.Information($"Completed deduping messages for inboxId: {inboxId} with message count {syncedMessagesToAdd.Length}");

            return [.. syncedMessagesToAdd];
        }

        private async Task<InboxMessage[]> AddInboxMessages(InboxMessage[] messages,
            Guid inboxId,
            Guid providerId,
            Guid inboxAccountId,
            string externalAccountId,
            Guid connectedAppId,
            Guid personId,
            DateTime syncedOn)
        {
            Log.Information($"Starting adding messages for inboxId: {inboxId}");

            await IgnoreMessagesFromIgnoredSenders(providerId, inboxId, messages);

            var addedMessages = await inboxRepository.AddInboxMessages(messages, personId, syncedOn);

            var messagesWithAttachments = addedMessages
                .Where(x => x.MessageAttachments.Any())
                .ToArray();

            if (messagesWithAttachments.Any())
            {
                var attachments = messagesWithAttachments
                    .SelectMany(x => x.MessageAttachments)
                    .ToArray();

                await inboxRepository.AddInboxMessageAttachments(attachments);

                await PublishMessageAttachmentMetadataCreatedEvent(inboxId,
                    providerId,
                    personId,
                    inboxAccountId,
                    externalAccountId,
                    messagesWithAttachments,
                    messages.First().ExternalSource,
                    connectedAppId);
            }

            Log.Information($"Completed adding messages for inboxId: {inboxId} with message count {addedMessages.Length}");

            return addedMessages;
        }

        private async Task IgnoreMessagesFromIgnoredSenders(Guid providerId, Guid inboxId, IEnumerable<InboxMessage> messages)
        {
            var ignoredSenders = await trackedSenderRepository.Get(providerId, inboxId, TrackedSenderStatus.Ignored);

            if (ignoredSenders is null || !ignoredSenders.Any())
                return;

            var ignoredSenderAccounts = ignoredSenders.Select(i => i.Account).ToHashSet();

            foreach (var message in messages)
            {
                if (ignoredSenderAccounts.Contains(message.From.AccountId))
                {
                    message.Status = MessageStatus.Ignored;
                }
            }
        }

        private async Task PublishMessageAttachmentMetadataCreatedEvent(Guid inboxId,
            Guid providerId,
            Guid personId,
            Guid inboxAccountId,
            string externalAccountId,
            InboxMessage[] messagesWithAttachments,
            ExternalSource externalSource,
            Guid connectedAppId)
        {
            var attachmentMetada = messagesWithAttachments
                .SelectMany(
                    message => message.MessageAttachments,
                    (message, attachments) => new AttachmentMetadata(
                        attachments.Id,
                        attachments.MediaId,
                        attachments.InboxMessageId,
                        attachments.FileName,
                    attachments.ContentType,
                        attachments.ExternalFileId,
                        message.ExternalConversationId,
                        message.ExternalSourceMessageId)
            ).ToArray();

            Log.Information($"Publishing attachment metadata event for inboxId: {inboxId} with attachments count {attachmentMetada.Length}");

            await sqsRepository.SendMessage(QueueType.Task,
                new EventMessage(EventType.MessageAttachmentMetadata_Created,
                    new EventData<MessageAttachmentMetadataCreatedEvent>(new(new MessageAttachmentMetadataEventModel(
                    attachmentMetada,
                    inboxId,
                    connectedAppId,
                    providerId,
                    personId,
                    externalAccountId,
                    externalSource
                    )
                )), inboxAccountId.ToString(), personId));

            Log.Information($"Published attachment metadata event for inboxId: {inboxId} with attachments count {attachmentMetada.Length}");
        }

        private async Task ReopenInboxConversations(Guid providerId, Guid personId, Guid inboxId, Guid[] conversationIds, bool isStatesSyncEnabled)
        {
            await inboxRepository.UpdateInboxMessageStatusByConversation(inboxId, conversationIds, MessageStatus.Default, [MessageStatus.Archived, MessageStatus.Deleted]);

            // Raise conversation moved event twice to force gmail to reopen all messages under a conversation
            // In cp when a message is added to a conversation, the whole conversation is reopened
            // This is to force gmail to reopen all messages under a conversation in archive / trash to the inbox
            if (isStatesSyncEnabled)
            {
                foreach (var conversationId in conversationIds)
                {
                    await RaiseConversationMovedEvent(providerId, personId, inboxId, conversationId, MessageStatus.Archived, MessageStatus.Default);
                    await RaiseConversationMovedEvent(providerId, personId, inboxId, conversationId, MessageStatus.Deleted, MessageStatus.Default);
                }
            }
        }

        public async Task<InboxMessage[]> SyncInboxMessages(Guid providerId, Models.InboxAccount account, InboxMessage[]  newMessages, ConnectedApp connectedApp, Models.InboxAccount[] allInboxAccounts, MovedMessageIds movedMessageIds)
        {
            var isStatesSyncEnabled = connectedApp.HasModifyScopePermission();
            var publishedOn = dateTimeProvider.GetDateTimeUtc();

            // Temporary flag for backwards compatibility
            // Refactor this during https://app.clickup.com/t/**********/CP-18097
            if (isStatesSyncEnabled)
            {
                await MoveInboxMessages(account.InboxId, movedMessageIds, publishedOn);
            }

            newMessages = await TryDedupeMessages(account.InboxId,  newMessages, allInboxAccounts);

            if (newMessages.Length > 0)
            {
                var addedMessages = await AddInboxMessages(newMessages,
                    account.InboxId,
                    providerId,
                    account.Id,
                    account.ExternalAccountId,
                    connectedApp.Id,
                    connectedApp.PersonId.Value,
                    publishedOn);

                var conversationIds = addedMessages
                    .Where(x => !x.ConversationId.IsNullOrEmpty())
                    .Select(x => x.ConversationId.Value)
                    .ToArray();

                if (conversationIds.Length > 0)
                {
                    // TODO: Raise another event here to force gmail with cp states whenever a new messages is added in a a conversation
                    // Reopen Inbox Conversations once a message is synced by move all messages under the conversation to the default status
                    await ReopenInboxConversations(providerId, connectedApp.PersonId.Value, account.InboxId, conversationIds, isStatesSyncEnabled);
                }

                if (await featureService.IsEnabled(FeatureFlags.AiSuggestedActions, providerId))
                {
                    await RaiseInboxMessageReceivedEvent(providerId, connectedApp.PersonId.Value, addedMessages);
                }
                
                await NotifyToClient(account.InboxId, addedMessages);
            }

            return newMessages;
        }


        private async Task MoveInboxMessages(Guid inboxId,
            MovedMessageIds movedMessageIds,
            DateTime updatedAt)
        {
            if (!movedMessageIds?.HasAnyMessages() ?? true) return;

            await inboxMessageRepository.UpdateStatus(inboxId,
                movedMessageIds.MessageIdsToDelete,
                movedMessageIds.MessageIdsToClose,
                movedMessageIds.MessageIdsToOpen,
                [MessageStatus.Ignored],
                updatedAt);
        }

        private async Task RaiseInboxMessageReceivedEvent(Guid providerId,
            Guid personId,
            InboxMessage[] addedMessages)
        {
            if (addedMessages.IsNullOrEmpty()) return;

            var inboxConversations = addedMessages.ToInboxConversations();

            foreach (var (InboxId, ConversationId) in inboxConversations)
            {
                // TODO Get the TO as contact email
                var contactEmail = addedMessages.FirstOrDefault(m => m.ConversationId == ConversationId)?.From?.AccountId;
                var evtMessage = new EventMessage(EventType.Message_Received,
                    new EventData<InboxMessageReceivedEventData>(new(providerId,
                    InboxId,
                    ConversationId,
                    contactEmail)),
                    ConversationId.ToString(),
                    personId
                    );

                await sqsRepository.SendMessage(QueueType.Task, evtMessage);
            }
        }

        private async Task NotifyToClient(Guid inboxId, InboxMessage[] updatedMessages)
        {
            if (updatedMessages?.Any() == true)
            {
                var inboxSettings = await this.inboxSettingRepository.Get(inboxId);
                var notification = new NotificationInboxMessagesReceived(inboxId, updatedMessages.Select(m => new InboxMessageId(m.Id, m.ConversationId)).ToArray());

                if (inboxSettings != null)
                {
                    if (inboxSettings.Staffs.Any(i => i.AllMembersHaveAccess()))
                    {
                        await notificationsService.Notify(inboxSettings.ProviderId, notification);
                    }
                    else
                    {
                        var staffIds = inboxSettings.Staffs.Where(i => i.PersonId.HasValue).Select(i => i.PersonId.Value).ToArray();
                        await this.notificationsService.Notify(inboxSettings.ProviderId, staffIds, notification);
                    }
                }
            }
        }
    }
}
