﻿using carepatron.core.Extensions;

namespace carepatron.core.Application.Communications.Inbox.Models
{
    public class GetMessagesResponse(long currentHistoryId, InboxMessage[] addedMessages, MovedMessageIds movedMessages = null)
    {
        public long CurrentHistoryId { get; set; } = currentHistoryId;

        public InboxMessage[] AddedMessages { get; set; } = addedMessages;

        public MovedMessageIds MovedMessages { get; set; } = movedMessages;
    }

    public class MovedMessageIds
    {
        // Message Ids that have been opened in external provider (i.e moved to inbox in gmail)
        public string[] MessageIdsToOpen { get; set; } = [];

        // Message Ids that have been closed in external provider (i.e moved to archive in gmail)
        public string[] MessageIdsToClose { get; set; } = [];

        // Message Ids that have been deleted in external provider (i.e moved to trash in gmail)
        public string[] MessageIdsToDelete { get; set; } = [];

        public bool HasAnyMessages() => !MessageIdsToOpen.IsNullOrEmpty()
            || !MessageIdsToDelete.IsNullOrEmpty()
            || !MessageIdsToClose.IsNullOrEmpty();
    }
}
