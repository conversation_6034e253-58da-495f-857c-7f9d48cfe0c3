using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using carepatron.core.Application.Exports.Abstractions;
using carepatron.core.Application.Exports.Models;
using carepatron.core.Application.Reminders.Services;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Application.Tasks.Extensions;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Tasks.Services;
using carepatron.core.Common;
using carepatron.core.Extensions;
using carepatron.core.Models.Call;
using carepatron.core.Repositories.Call;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Services;
using carepatron.core.Utilities;

namespace carepatron.core.Application.Exports.EventHandlers;

public class AppointmentExportHandler(
    ITaskRepository taskRepository,
    IUriProvider uriProvider,
    ICallRepository callRepository,
    ITaskStatusService taskStatusService) : IExportHandler
{
    private Dictionary<Guid, Call> callDictionary = new();

    private readonly Dictionary<CallProvider, string> callProviders = new()
    {
        { CallProvider.AWS, "Carepatron" },
        { CallProvider.Zoom, "Zoom" }
    };


    public ExportType Type => ExportType.Appointments;

    public async Task<List<Dictionary<string, object>>> GetExportData(ExportItem exportItem)
    {
        if (exportItem.Filter is not CalendarBulkFilter bulkFilter) return [];

        List<TaskModel> allTasks = new();
        HashSet<Guid> callIds = new();

        var tasks = await taskRepository.Get(exportItem.ProviderId, bulkFilter.StartDate, bulkFilter.EndDate, bulkFilter.StaffIds ?? [], null, null, true);
        foreach (var task in tasks)
        {
            if (!string.IsNullOrEmpty(task.RRule))
            {
                var expandedTasks = TaskService.ExpandRecurringInstances(task, bulkFilter.StartDate, bulkFilter.EndDate);
                allTasks.AddRange(expandedTasks);
            }
            // parent recurring tasks could be included from the query result so we need to filter them from the final result
            else if (task.StartDate >= bulkFilter.StartDate && task.EndDate <= bulkFilter.EndDate)
            {
                allTasks.Add(task);
            }

            if (task.CallId.HasValue)
                callIds.Add(task.CallId.Value);
        }

        var calls = await callRepository.GetCalls(callIds.ToArray());
        callDictionary = calls.ToDictionary(x => x.Id);

        var statuses = await taskStatusService.GetStatuses(exportItem.ProviderId);
        var statusMap = statuses.Values.SelectMany(x => x).ToDictionary();

        allTasks = allTasks.OrderBy(x => x.StartDate).ThenBy(x => x.Id).ToList();

        List<AppointmentExportModel> appointmentExports = new();
        foreach (var task in allTasks)
        {
            if (task.Contacts.Any())
                appointmentExports.AddRange(task.Contacts.Select(c => MapFromTaskModel(task, bulkFilter.TimeZone, statusMap, c)));
            else
                appointmentExports.Add(MapFromTaskModel(task, bulkFilter.TimeZone, statusMap));
        }

        var listDictionary = new List<Dictionary<string, object>>();
        foreach (var data in appointmentExports)
        {
            var dict = new Dictionary<string, object>()
            {
                { nameof(data.StartDate), data.StartDate },
                { nameof(data.EndDate), data.EndDate },
                { nameof(data.Title), data.Title },
                { nameof(data.Location), data.Location },
                { nameof(data.VideoCall), data.VideoCall },
                { nameof(data.Description), data.Description },
                { nameof(data.Client), data.Client },
                { nameof(data.ClientIdentificationNumber), data.ClientIdentificationNumber },
                { nameof(data.Status), data.Status },
                { nameof(data.Staff), data.Staff },
                { nameof(data.Services), data.Services },
                { nameof(data.Invoices), data.Invoices },
                { nameof(data.Notes), data.Notes },
                { nameof(data.TimeZone), data.TimeZone },
            };

            listDictionary.Add(dict);
        }

        return listDictionary;
    }

    private AppointmentExportModel MapFromTaskModel(TaskModel task, string timeZone, Dictionary<string, TaskAttendeeStatus> statusMap, TaskContact taskContact = null)
    {
        var staff = !task.Staff.IsNullOrEmpty() ? string.Join(Environment.NewLine, task.Staff.Select(x => x.FullName)) : string.Empty;

        var services = !task.ItemsSnapshot.IsNullOrEmpty()
            ? string.Join(Environment.NewLine, task.ItemsSnapshot.Select(x =>
            {
                string[] details = [x.Name, x.Code, x.Price.ToString()];
                return string.Join(" - ", details.Where(y => !string.IsNullOrEmpty(y)));
            }).ToArray())
            : string.Empty;

        var invoices = taskContact is not null && !task.InvoicesReference.IsNullOrEmpty()
            ? string.Join(Environment.NewLine, task.InvoicesReference.Where(x => x.ContactId == taskContact.Id).Select(x => $"{x.Number} - {x.Status}"))
            : string.Empty;

        var notes = taskContact is not null && !task.NotesReference.IsNullOrEmpty()
            ? string.Join(Environment.NewLine, task.NotesReference.Where(x => x.ContactId == taskContact.Id).Select(x => uriProvider.ComposeNoteUri(x.Id, x.ContactId).ToString()))
            : string.Empty;

        var videoCall = string.Empty;
        if (task.CallId.HasValue
            && callDictionary.TryGetValue(task.CallId.Value, out Call call)
            && callProviders.TryGetValue(call.CallProvider, out var provider))
            videoCall = provider;
        else if (task.VirtualLocationProduct.HasValue)
            videoCall = task.VirtualLocationProduct.Value.ToString();

        var statusName = string.IsNullOrEmpty(taskContact?.AttendeeStatusId)
            ? string.Empty
            : statusMap.TryGetValue(taskContact.AttendeeStatusId, out var status)
                ? status.Name
                : taskContact.AttendeeStatusId;

        return new AppointmentExportModel()
        {
            StartDate = task.StartDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
            EndDate = task.EndDate.ToTimeZoneDateTime(timeZone).ToString("yyyy-MM-dd HH:mm:ss"),
            Title = task.Title,
            Location = task.Location,
            VideoCall = videoCall,
            Description = task.Description,
            Client = taskContact?.FullName,
            Status = statusName,
            Staff = staff,
            Services = services,
            Invoices = invoices,
            Notes = notes,
            ClientIdentificationNumber = taskContact?.IdentificationNumber,
            TimeZone = task.TimeZone
        };
    }
}