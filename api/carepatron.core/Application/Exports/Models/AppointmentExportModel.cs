using System;

namespace carepatron.core.Application.Exports.Models;

public class AppointmentExportModel
{
    public string StartDate { get; set; }
    public string EndDate { get; set; }
    public string Title { get; set; }
    public string Location { get; set; }
    public string VideoCall { get; set; }
    public string Description { get; set; }
    
    /// <summary>
    /// full name of the client
    /// </summary>
    public string Client { get; set; }
    public string ClientIdentificationNumber { get; set; }
    /// <summary>
    /// status of client's attendance
    /// </summary>
    public string Status { get; set; }
    /// <summary>
    /// list of staff members separated with new line
    /// </summary>
    public string Staff { get; set; }
    /// <summary>
    /// list of services formatted '{name} - {code} - {price}' separated with new line
    /// </summary>
    public string Services { get; set; }
    /// <summary>
    /// list of invoices formatted '{invoiceNumber} - {status}' separated with new line
    /// </summary>
    public string Invoices { get; set; }
    /// <summary>
    /// list of notes formatted by URL separated with new line
    /// </summary>
    public string Notes { get; set; }

    public string TimeZone { get; set; }


}