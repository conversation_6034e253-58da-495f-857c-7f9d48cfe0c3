﻿using carepatron.core.Application.Booking.Models;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Insurance.Services;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Schema.Models.Properties;
using carepatron.core.Application.Schema.Services;
using carepatron.core.Application.Tasks.Abstractions;
using carepatron.core.Application.Tasks.Extensions;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Application.Tasks.Services;
using carepatron.core.Application.Users.Models;
using carepatron.core.Application.Workspace.Locations.Models;
using carepatron.core.Application.Workspace.Providers.Models;
using carepatron.core.Application.Workspace.ServiceItems.Models;
using carepatron.core.Common;
using carepatron.core.Constants;
using carepatron.core.Extensions;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Localisation.SharedMessages;
using carepatron.core.Models.Call;
using carepatron.core.Models.Common;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Scheduling;
using carepatron.core.Repositories.Billables;
using carepatron.core.Repositories.Booking;
using carepatron.core.Repositories.ConnectedApps;
using carepatron.core.Repositories.Item;
using carepatron.core.Repositories.Person;
using carepatron.core.Repositories.Provider;
using carepatron.core.Repositories.Staff;
using carepatron.core.Repositories.Tasks;
using carepatron.core.Repositories.TaxRates;
using carepatron.core.Services;
using FuzzySharp;
using Microsoft.Extensions.Localization;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Serilog.Context;

namespace carepatron.core.Application.Booking.Service;

public class BookingService(
    IStaffScheduleRepository staffScheduleRepository,
    IItemRepository itemRepository,
    IConnectedCalendarService connectedCalendarService,
    ITaskRepository taskRepository,
    IProviderRepository providerRepository,
    IPersonRepository personRepository,
    IContactRepository contactRepository,
    IConnectedAppRepository connectedAppRepository,
    IBookingRepository bookingRepository,
    ICallService callService,
    IStaffServicesRepository staffServicesRepository,
    IDateTimeProvider dateTimeProvider,
    IIntegrationEventPublisher eventPublisher,
    ISchemaService schemaService,
    IBillableRepository billableRepository,
    ITaxRateRepository taxRateRepository,
    IStringLocalizer<SharedMessages> localizer,
    Contacts.Abstractions.IContactService contactService,
    ITaskService taskService,
    IInsuranceService insuranceService,
    ICalendarSubscriptionRepository calendarSubscriptionRepository,
    ITaskStatusService taskStatusService
) : IBookingService
{
    public async Task<Dictionary<Guid, List<AvailabilityLocation>>> GetStaffAvailabilities(Guid providerId,
        Guid itemId,
        Guid[] locationIds,
        bool isOnline,
        Guid[] staffIds,
        DateRange dateRange)
    {
        var serviceItem = await itemRepository.Get(providerId, itemId);
        var availabilities = await GetAvailabilities(providerId, locationIds, isOnline, staffIds, dateRange, serviceItem, [itemId]);

        return availabilities;
    }

    public async Task<Dictionary<Guid, List<AvailabilityLocation>>> GetStaffAvailabilities(Guid providerId,
        Guid[] locationIds,
        bool isOnline,
        Guid[] staffIds,
        DateRange dateRange,
        ProviderItem serviceItem)
    {
        return await GetAvailabilities(providerId, locationIds, isOnline, staffIds, dateRange, serviceItem, [serviceItem.Id]);
    }

    /// <summary>
    /// returns an expanded and sorted tasks
    /// </summary>
    /// <param name="providerId"></param>
    /// <param name="dateRange"></param>
    /// <param name="staffIds"></param>
    /// <returns></returns>
    public async Task<TaskModel[]> GetStaffTasks(Guid providerId, DateRange dateRange, params Guid[] staffIds)
    {
        /* How get staff tasks work:
         * Get all tasks related to staff
         * Expand first all recurring tasks based on date range
         * Do the same for external events (this is related to users that uses old sync process or users with MS connected app
         * Update all tasks that has service items (ItemSnapShot) with buff time. This will expand the time of the task to align with buffer time 
         * Exclude all parent tasks with RRule since we already expanded all recurring events
         * Exclude all events tagged as FREE. We want clients to book an appointment that overlaps with FREE event
         */
        
        using (LogContext.PushProperty("GetStaffTasksDebugDetails", new
               {
                   ProviderId = providerId,
                   StaffIds = string.Join(',', staffIds),
                   dateRange.FromDate,
                   dateRange.ToDate
               }))
        {
            var calendarSubscriptions = await calendarSubscriptionRepository.GetTwoWaySyncCalendarSubscriptions(staffIds, providerId, [ConnectedAppProducts.Google]);
            var calendarSubscriptionIds = calendarSubscriptions.Select(x => x.CalendarId).ToArray();
            var parentTasks = await bookingRepository.GetStaffTasks(providerId, dateRange.FromDate, dateRange.ToDate, staffIds, calendarSubscriptionIds);
            
            List<TaskModel> expandedTasks = new();

            foreach (var parentTask in parentTasks)
            {
                if (parentTask.RRule is not null)
                {
                    var expandedTasksFromParent = TaskService.ExpandRecurringInstances(parentTask, dateRange.FromDate, dateRange.ToDate);
                    expandedTasks.AddRange(expandedTasksFromParent);
                }
                else expandedTasks.Add(parentTask);
            }

            // very weird, when trying to call TryFetchExternalTasks asynchronously integration tests fails.
            // but no issue when debugging integration test and calling TryFetchExternalTasks asynchronously :(
            // it would be nice if this runs in parallel.
            var externalTasks = await TryFetchExternalTasks(providerId, staffIds, dateRange);
            foreach (var externalTask in externalTasks)
            {
                if (externalTask.RRule is not null)
                {
                    var expandedTasksFromParent = TaskService.ExpandRecurringInstances(externalTask, dateRange.FromDate, dateRange.ToDate);
                    expandedTasks.AddRange(expandedTasksFromParent);
                }
                else expandedTasks.Add(externalTask);
            }
            
            // since we dont have relationship between task and service items,
            // we need to query service task items to resolve buffer per tasks
            var parentTaskItemIds = parentTasks.SelectMany(x => x.ItemIds)
                .Distinct()
                .ToArray();

            if (parentTaskItemIds.Length > 0)
            {
                var items = await itemRepository.Get(providerId, parentTaskItemIds);

                foreach (var parentTask in parentTasks)
                {
                    if (parentTask.ItemIds.Length <= 0) continue;

                    // instead of sums up buffer times
                    // we get the max buffer instead
                    var taskItemList = items.Where(x => parentTask.ItemIds.Contains(x.Id))
                        .ToList();

                    // if the task is a group event, we don't want to expand the time
                    if(taskItemList.Any(x => x.AllowGroupEvents)) continue;

                    if (taskItemList.Any())
                    {
                        var maxBufferBefore = taskItemList.Max(x => x.BufferTimeBeforeMins);
                        var maxBufferAfter = taskItemList.Max(x => x.BufferTimeAfterMins);

                        parentTask.StartDate = parentTask.StartDate.AddMinutes(maxBufferBefore * -1);
                        parentTask.EndDate = parentTask.EndDate.AddMinutes(maxBufferAfter);
                    }
                }
            }

            var finalTasks = expandedTasks
                // since parent tasks are already expanded, exclude all parent instances in final output
                .Where(x => string.IsNullOrWhiteSpace(x.RRule))
                .Where(t => !t.IsFree)
                .OrderBy(x => x.StartDate)
                .ToArray();

            return finalTasks;
        }
        
        
    }

    public async Task<bool> CanCancelAppointment(Guid taskId)
    {
        var task = await taskRepository.Get(taskId);
        if (task is null) return false;

        var onlineBookingOptions = await providerRepository.GetProviderOnlineBookingOptions(task.ProviderId);

        return CanCancelAppointment(task, onlineBookingOptions);
    }

    public bool CanCancelAppointment(TaskModel task, ProviderOnlineBookingOptions onlineBookingOptions)
    {
        var currentDateUtc = DateTime.UtcNow;
        if (task is null || task.StartDate < currentDateUtc) return false;

        var minCancellation = onlineBookingOptions?.CancellationPolicy?.MinimumCancellationTimeMins;
        if (minCancellation is null or 0) return true;
        if (minCancellation is -1) return false;

        int cancellationMins = onlineBookingOptions.CancellationPolicy.MinimumCancellationTimeMins.GetValueOrDefault();

        DateRange dateRange = new(currentDateUtc, task.StartDate);
        return dateRange.AsTimeSpan().TotalMinutes > cancellationMins;
    }

    public async Task<Contact> GetBookingContact(BookingContactDetail contactDetails, Guid providerId)
    {
        var contacts = await contactRepository.GetByEmail(contactDetails.Email, providerId);
        if ((contacts?.Length ?? 0) == 0) return null;

        // fuzzy match by first name and preferred name, and select the best match
        var (contact, matchScore) = contacts
            .Select(x =>
            (
                contact: x,
                matchScore: Math.Max(
                    Fuzz.PartialRatio(x.FirstName?.Trim().ToLowerInvariant() ?? "", contactDetails.FirstName?.Trim().ToLowerInvariant() ?? ""),
                    Fuzz.PartialRatio(x.PreferredName?.Trim().ToLowerInvariant() ?? "", contactDetails.FirstName?.Trim().ToLowerInvariant() ?? ""))
            ))
            .OrderByDescending(x => x.contact.FirstName?.Trim()?.Equals(contactDetails.FirstName?.Trim(), StringComparison.InvariantCultureIgnoreCase) ?? false)
            .ThenByDescending(x => x.matchScore)
            .FirstOrDefault();

        if (matchScore < 75)
        {
            Log.ForContext("BestMatchedContact", new
                {
                    ContactId = contact.Id,
                    MatchScore = matchScore
                }, true)
                .Warning("Mismatched booking contact details");

            return null;
        }

        return contact;
    }

    public async Task<(Contact, Person)> GetOrCreateBookingContactAndPerson(
        BookingContactDetail contactDetails,
        Guid providerId,
        Guid staffId,
        string stripeCustomerId,
        CultureInfo cultureInfo,
        Person bookingPerson,
        string timeZone,
        bool isClient = true)
    {
        var contact = await GetBookingContact(contactDetails, providerId);
        var person = bookingPerson is not null
            ? bookingPerson
            : (contact?.PersonId.HasValue ?? false)
                ? await personRepository.Get(contact.PersonId.Value)
                : await personRepository.GetByEmail(contactDetails.Email);

        bool mismatchedStripeCustomerId = !string.IsNullOrEmpty(stripeCustomerId)
                                          && !string.IsNullOrEmpty(person?.StripeCustomerId) && person.StripeCustomerId != stripeCustomerId;

        if (mismatchedStripeCustomerId)
        {
            Log.ForContext("BookingStripeCustomer", new
                {
                    BookingStripeCustomerId = stripeCustomerId,
                    ContactId = contact?.Id,
                    PersonId = person?.Id,
                    PersonStripeCustomerId = person?.StripeCustomerId
                }, true)
                .Warning("Mismatched booking Stripe customer id");
        }

        if ((contact != null && contact.Email != contactDetails.Email) || mismatchedStripeCustomerId)
        {
            contact ??= await CreateOrUpdateContact(contact, 
                null,
                contactDetails,
                providerId,
                staffId,
                ContactCoreSchema.StatusGroup.Active,
                timeZone,
                cultureInfo,
                isClient);
            return (contact, person);
        }

        person = await CreateOrUpdatePerson(person, contactDetails, stripeCustomerId, cultureInfo);
        contact = await CreateOrUpdateContact(contact,
            person,
            contactDetails,
            providerId,
            staffId,
            string.IsNullOrEmpty(stripeCustomerId) ? ContactCoreSchema.StatusGroup.Lead : ContactCoreSchema.StatusGroup.Active,
            timeZone,
            cultureInfo,
            isClient);

        return (contact, person);
    }

    private async Task<Person> CreateOrUpdatePerson(Person person,
        BookingContactDetail contactDetails,
        string stripeCustomerId,
        CultureInfo cultureInfo)
    {
        if (person is null)
        {
            person = new Person
            {
                Id = Guid.NewGuid(),
                Email = contactDetails.Email,
                FirstName = contactDetails.FirstName.Trim(),
                LastName = contactDetails.LastName.Trim(),
                PhoneNumber = contactDetails.PhoneNumber,
                IsActive = true,
                IsClient = true,
                PreferredCulture = cultureInfo.ToString(),
                CreatedDateTimeUtc = DateTime.UtcNow,
                UpdatedDateTimeUtc = DateTime.UtcNow,
                StripeCustomerId = stripeCustomerId,
            };

            await personRepository.Create(person);
        }
        else if (!string.IsNullOrEmpty(stripeCustomerId) && string.IsNullOrEmpty(person.StripeCustomerId))
        {
            person.StripeCustomerId = stripeCustomerId;
            await personRepository.Update(person);
        }

        return person;
    }

    private async Task<Contact> CreateOrUpdateContact(Contact contact,
        Person person,
        BookingContactDetail contactDetails,
        Guid providerId,
        Guid staffId,
        string statusGroup,
        string timeZone,
        CultureInfo cultureInfo,
        bool isClient = true)
    {
        var statusOption = await GetDefaultContactStatus(providerId, statusGroup);

        ContactLanguage contactLanguage = new(Guid.NewGuid(), cultureInfo, true);
        if (contact is null)
        {
            contact = new Contact
            {
                Id = Guid.NewGuid(),
                ProviderId = providerId,
                Status = statusOption?.Id,
                IsClient = isClient,
                PersonId = person?.Id,
                FirstName = (contactDetails.FirstName ?? person?.FirstName)?.Trim(),
                LastName = (contactDetails.LastName ?? person?.LastName)?.Trim(),
                Email = contactDetails.Email ?? person?.Email,
                PhoneNumber = contactDetails.PhoneNumber ?? person?.PhoneNumber,
                CreatedDateTimeUtc = DateTime.UtcNow,
                UpdatedDateTimeUtc = DateTime.UtcNow,
                Settings = new ContactSettings { TimeZone = timeZone },
                AssignedStaff = [new SimplePerson() { Id = staffId }],
                Languages = [contactLanguage],
            };

            await contactRepository.Create(contact);

            if (person != null)
            {
                eventPublisher.Add(new OutgoingEvent<ContactPersonChangedEvent>(new(contact.ProviderId, contact, null)));
            }

            eventPublisher.Add(new OutgoingEvent<ContactStaffAssigned>(new(contact.ProviderId, contact, staffId, new EventInitiator(staffId))));
            eventPublisher.Add(new OutgoingEvent<ContactCreatedEvent>(new(contact.ProviderId, contact, new EventInitiator(staffId))));

            return contact;
        }

        var contactUpdated = false;

        if (contact.PersonId is null && person != null)
        {
            contact.PersonId = person.Id;
            contact.Status = statusOption?.Id;

            if (contact.Settings is null)
                contact.Settings = new ContactSettings();
            contact.Settings.TimeZone = !string.IsNullOrEmpty(contact.Settings.TimeZone) ? contact.Settings.TimeZone : timeZone;

            contactUpdated = true;
            eventPublisher.Add(new OutgoingEvent<ContactPersonChangedEvent>(new(contact.ProviderId, contact, null)));
        }


        var languages = contact.Languages ?? Array.Empty<ContactLanguage>();
        var primaryLanguage = languages.FirstOrDefault(x => x.IsPrimary);

        if (!languages.Any())
        {
            contact.Languages = [contactLanguage];
            contactUpdated = true;
        }
        else if (primaryLanguage is null)
        {
            // has languages but there's no primary language,
            var defaultLanguage = languages.FirstOrDefault(x => x.Language == contactLanguage.Language);
            if (defaultLanguage is not null) //set the existing language as primary
            {
                defaultLanguage.IsPrimary = true;
            }
            else //add the default language as primary
            {
                languages = languages.Concat(new[] { contactLanguage }).ToArray();
                contact.Languages = languages;
            }

            contactUpdated = true;
        }

        if (contactUpdated)
        {
            await contactRepository.Update(contact);
            eventPublisher.Add(new OutgoingEvent<ContactUpdatedEvent>(new(contact.ProviderId, contact, Initiator: new EventInitiator(staffId))));
        }

        return contact;
    }

    private async Task<OptionSetValue> GetDefaultContactStatus(Guid providerId, string defaultGroup)
    {
        var dataSchema = await schemaService.GetDataSchema(SchemaTypes.ContactSchema, providerId);
        var statusProperty = dataSchema.GetProperty<OptionSetV2Property>(ContactCoreSchema.PropertyId.Status);
        var defaultOption = statusProperty.GetDefaultGroupOptionSetValue(defaultGroup);

        if (defaultOption is null && defaultGroup == ContactCoreSchema.StatusGroup.Lead)
        {
            defaultOption = statusProperty.GetDefaultGroupOptionSetValue(ContactCoreSchema.StatusGroup.Active);
        }

        return defaultOption;
    }

    private async Task<SaveTaskModel> CreateBookingTask(Contact contact,
        Contact relatedContact,
        bool? isRelatedContactAttending,
        ProviderItem providerItem,
        Guid staffId,
        Guid? locationId,
        bool? isOnline,
        DateRange dateRange,
        string message,
        string timeZone,
        LocationType locationType,
        VirtualLocationProductType? virtualLocationProduct, 
        CancellationToken cancellationToken)
    {
        string title = contact.ToTaskTitle();

        if (providerItem is not null)
            title = $"{title} - {providerItem.Name}";

        var staffServices = await staffServicesRepository.GetByStaffPersonIdAndServiceId(contact.ProviderId, staffId, providerItem.Id);
        providerItem.Price = staffServices?.Price ?? providerItem.Price;

        var billingSettings = await providerRepository.GetBillingSettings(contact.ProviderId);
        // We want to use (snapshot) the provider's current currency code for the task items
        if (!string.IsNullOrEmpty(billingSettings?.CurrencyCode))
        {
            providerItem.CurrencyCode = billingSettings.CurrencyCode;
        }

        var statuses = await taskStatusService.GetStatuses(contact.ProviderId, [TaskAttendeeGroupStatus.Accepted]);
        string confirmedStatusId = statuses.GetDefault(TaskAttendeeGroupStatus.Accepted)?.Id;
        
        Guid taskId = Guid.NewGuid();
        var taskContacts = CreateTaskContacts(taskId, contact, relatedContact, isRelatedContactAttending, message, TaskContactStatus.Confirmed, confirmedStatusId);
        var contactIds = taskContacts.Select(x => x.ContactId).ToArray();

        var taskModel = new SaveTaskModel
        {
            Id = taskId,
            Type = TaskType.ClientEvent,
            ProviderId = contact.ProviderId,
            Title = title,
            Description = message,
            TimeZone = timeZone,
            StartDate = dateRange.FromDate,
            EndDate = dateRange.ToDate,
            ContactIds = contactIds,
            Contacts = taskContacts,
            AboutContacts = [contact.Id],
            StaffIds = [staffId],
            Items = [providerItem],
            AttendeesPersonIds = [staffId, contact.PersonId.GetValueOrDefault()],
            CreatedByPersonId = contact.PersonId ?? staffId,
            LastUpdatedByPersonId = contact.PersonId ?? staffId,
            CreatedDateTimeUtc = DateTime.UtcNow,
            LastUpdatedDateTimeUtc = DateTime.UtcNow,
            LocationType = locationType,
            VirtualLocationProduct = virtualLocationProduct,
            IsBillingV2 = true,
        };

        if (locationId.HasValue)
        {
            var providerLocation = await providerRepository.GetProviderLocationById(locationId.Value);
            taskModel.Location = providerLocation?.Address;
            taskModel.LocationPOSCode = providerLocation?.PosCode;
            if (providerLocation is not null)
                taskModel.LocationsSnapshot = [providerLocation];
        }
        else if (isOnline.GetValueOrDefault())
        {
            var connectedApps = await connectedAppRepository.GetSimpleConnectedApps(staffId, contact.ProviderId, [ConnectedAppProducts.Zoom]);
            var callProvider = connectedApps.Items.Count > 0 ? CallProvider.Zoom : CallProvider.AWS;

            var call = await callService.CreateCall(callProvider, taskModel, MediaRegion.NewZealand);
            taskModel.CallId = call.Id;
        }

        var taxRate = await taxRateRepository.GetDefaultTaxRate(contact.ProviderId, cancellationToken);
        await taskRepository.Create(taskModel);
        var billables = taskModel.AsBillables(taxRate);
        await insuranceService.SetSelfPayForBillables(contact.ProviderId, billables);
        await billableRepository.SaveTaskBillable(taskModel.Id, billables, cancellationToken);
        return taskModel;
    }

    private SimpleTaskContact[] CreateTaskContacts(Guid taskId, Contact contact, Contact relatedContact, bool? isRelatedContactAttending, string message, TaskContactStatus taskContactStatus, string attendeeStatusId = null)
    {
        SimpleTaskContact[] taskContacts;
        if (relatedContact is not null && isRelatedContactAttending.GetValueOrDefault())
        {
            taskContacts =
            [

                new SimpleTaskContact(taskId, contact.Id, true, string.Empty, true, taskContactStatus, attendeeStatusId),
                new SimpleTaskContact(taskId, relatedContact.Id, true, message, false, taskContactStatus, attendeeStatusId),
            ];
        }
        else
        {
            taskContacts =
            [
                new SimpleTaskContact(taskId, contact.Id, true, message, true, taskContactStatus, attendeeStatusId),
            ];
        }

        return taskContacts;
    }

    public async Task<Guid> CreateOrUpdateTaskFromBookingIntent(
        Guid? bookingTaskId,
        Guid providerId,
        Guid staffId,
        Guid? locationId,
        bool? isOnline,
        DateRange date,
        string message,
        string timeZone,
        LocationType locationType,
        VirtualLocationProductType? virtualLocationProduct,
        Contact contact,
        Contact relatedContact,
        bool? isRelatedContactAttending,
        ProviderItem providerItem,
        CancellationToken cancellationToken = default)
    {
        if (bookingTaskId.HasValue && await UpdateIfTaskExisted(bookingTaskId.Value,
                providerId,
                contact,
                relatedContact,
                isRelatedContactAttending.GetValueOrDefault(),
                date,
                message,
                staffId,
                cancellationToken))
        {
            return bookingTaskId.Value;
        }

        var taskModel = await CreateBookingTask(contact,
            relatedContact,
            isRelatedContactAttending,
            providerItem,
            staffId,
            locationId,
            isOnline,
            date,
            message,
            timeZone,
            locationType,
            virtualLocationProduct,
            cancellationToken);
        return taskModel.Id;
    }

    private async Task<bool> UpdateIfTaskExisted(Guid bookingTaskId,
        Guid providerId,
        Contact contact,
        Contact relatedContact,
        bool isRelatedContactAttending,
        DateRange dateRange,
        string message,
        Guid staffId,
        CancellationToken cancellationToken)
    {
        var existingTask = await taskRepository.Get(providerId, bookingTaskId);
        if (existingTask is null)
        {
            return false;
        }

        if (existingTask.Type == TaskType.ClientEvent && existingTask.IsBillingV2)
        {
            var taxRate = await taxRateRepository.GetDefaultTaxRate(providerId, cancellationToken);
            var billables = existingTask.AsBillables(localizer, [taxRate], additionalContacts: [contact.Id]);
            await insuranceService.SetSelfPayForBillables(providerId, billables);
            await billableRepository.SaveTaskBillable(existingTask.Id, billables, cancellationToken);
        }

        if (existingTask.HasContact(contact.Id))
            return true;

        var updatedTaskDescription = existingTask.Description;
        if (!string.IsNullOrWhiteSpace(message))
        {
            updatedTaskDescription = string.IsNullOrEmpty(updatedTaskDescription)
                ? message
                : $"{updatedTaskDescription}{Environment.NewLine}{message}";
        }

        bool isNewInstance = false;

        if (!string.IsNullOrEmpty(existingTask.RRule))
        {
            OccurrenceChildTask childTask = existingTask;
            childTask.StartDate = dateRange.FromDate;
            childTask.EndDate = dateRange.ToDate;
            childTask.ExDate = null;
            childTask.RRule = null;
            childTask.OccurrenceEndDate = null;
            childTask.IsBillingV2 = true;
            childTask.Description = updatedTaskDescription;

            var occurrenceResult = await taskService.ResolveOccurrenceTask(existingTask, childTask.StartDate, childTask);

            if (occurrenceResult.TaskId.HasValue)
            {
                isNewInstance = occurrenceResult.IsNewInstance == true;
                if (isNewInstance)
                {
                    existingTask.Id = occurrenceResult.TaskId.Value;
                }
                else
                {
                    existingTask = await taskRepository.Get(providerId, occurrenceResult.TaskId.Value);

                    if (existingTask is null)
                    {
                        Log.ForContext("TaskId", new { TaskId = bookingTaskId, childTask.StartDate });
                        throw new InvalidOperationException("Failed to resolve occurrence task id");
                    }
                }
            }
        }
        
        var statuses = await taskStatusService.GetStatuses(providerId, [TaskAttendeeGroupStatus.Accepted]);
        string confirmedStatusId = statuses.GetDefault(TaskAttendeeGroupStatus.Accepted)?.Id;

        var newTaskContacts = CreateTaskContacts(existingTask.Id, 
            contact, 
            relatedContact, 
            isRelatedContactAttending, 
            message, 
            TaskContactStatus.Confirmed, 
            confirmedStatusId)
        .Where(x => !existingTask.HasContact(x.ContactId));

        if (isNewInstance)
        {
            // can't taskRepository.Save() here as new instance has not been committed yet
            foreach (var taskContact in newTaskContacts)
                await taskRepository.AddTaskContact(taskContact);
        }
        else
        {
            var saveTaskModel = existingTask.ToSaveTaskModel();
            saveTaskModel.LastUpdatedByPersonId = contact.PersonId ?? staffId;
            saveTaskModel.Contacts = saveTaskModel.Contacts.Union(newTaskContacts).ToArray();
            saveTaskModel.Description = updatedTaskDescription;

            await taskRepository.Save(saveTaskModel);
        }

        return true;
    }

    /// <summary>
    /// Get DateRange based on provider online booking options or default from the current date
    /// eg: online booling options
    /// min time: 1; min unit: month
    /// max time: 6: max unit: month
    /// current date: Apr 25 2023
    /// returns date range: start May 25 2023; end Nov 25 2023
    /// </summary>
    /// <param name="providerId">The ID of the provider.</param>
    /// <param name="currentDateRange"></param>
    /// <returns></returns>
    public async Task<DateRange> GetOnlineBookingPolicyDateRange(Guid providerId, DateRange currentDateRange)
    {
        var onlineBookingOptions = await providerRepository.GetProviderOnlineBookingOptions(providerId);
        var onlineBookingPolicy = onlineBookingOptions?.OnlineBookingPolicy ?? OnlineBookingPolicy.Default();

        var currentDate = dateTimeProvider.GetDateTimeUtc();

        // if there's no minumum booking time, compare currentDateRange.FromDate and currentDate and return who is the "greater" date.
        // eg, currentDateRange.FromDate is June 1 2023, and currentDate is June 19 2023. minBookingDate will be June 19 2023.
        // eg, currentDateRange.FromDate is July 1 2023, and currentDate is June 19 2023. minBookingDate will be July 1 2023.
        var minBookingDate = onlineBookingPolicy.MinimumBookingTime is null
            ? new DateTime(Math.Max(currentDateRange.FromDate.Ticks, currentDate.Ticks))
            : ResolveBookingDate(onlineBookingPolicy.MinimumBookingTimeUnit,
                onlineBookingPolicy.MinimumBookingTime.GetValueOrDefault(),
                currentDate,
                currentDateRange.FromDate);

        var maxBookingDate = onlineBookingPolicy.MaximumBookingTime is null
            ? currentDateRange.ToDate
            : ResolveBookingDate(onlineBookingPolicy.MaximumBookingTimeUnit,
                onlineBookingPolicy.MaximumBookingTime.GetValueOrDefault(),
                currentDate,
                currentDateRange.ToDate);

        if (minBookingDate > maxBookingDate) return null;

        return new DateRange(minBookingDate, maxBookingDate);
    }

    /// <summary>
    /// Checks currentDateRange if within the online booking policy date range; if yes, returns currentDateRange.
    /// otherwise DateRange will vary based on provider online booking policy
    /// eg: online booling options
    /// min time: 1; min unit: month
    /// max time: 6: max unit: month
    /// current date: Apr 25 2023
    /// online booking policy date range will be: start May 25 2023; end Nov 25 2023
    /// currentDateRange: start Nov 22 2023; end Nov 29 2023
    /// returns date range: start Nov 22 2023; end Nov 25 2023
    /// </summary>
    /// <param name="providerId">The ID of the provider.</param>
    /// <param name="currentDateRange"></param>
    /// <returns></returns>
    public async Task<DateRange> TryGetDateRangeFromOnlineBookingPolicy(Guid providerId, DateRange currentDateRange)
    {
        var onlineBookingPolicyDateRange = await GetOnlineBookingPolicyDateRange(providerId, currentDateRange);
        if (onlineBookingPolicyDateRange is null) return null;

        var fromDate = onlineBookingPolicyDateRange.IsWithinDateRange(currentDateRange.FromDate) ? currentDateRange.FromDate : onlineBookingPolicyDateRange.FromDate;
        var toDate = onlineBookingPolicyDateRange.IsWithinDateRange(currentDateRange.ToDate) ? currentDateRange.ToDate : onlineBookingPolicyDateRange.ToDate;

        // making sure its a valid date range
        var min = Math.Min(fromDate.Ticks, toDate.Ticks);
        var max = Math.Max(fromDate.Ticks, toDate.Ticks);

        return new DateRange(new DateTime(min), new DateTime(max));
    }

    public async Task<OnlineBookingPaymentPolicy> GetOnlinePaymentPolicy(Guid providerId, Guid? itemId, CancellationToken cancellationToken)
    {
        var item = itemId.HasValue
            ? await itemRepository.Get(providerId, itemId.Value)
            : null;

        if (item?.OnlineBookingOptions is not null)
        {
            return item.OnlineBookingOptions;
        }

        var providerOnlineBookingOptions = await providerRepository.GetProviderOnlineBookingOptions(providerId);
        return providerOnlineBookingOptions?.PaymentPolicy ?? OnlineBookingPaymentPolicy.Default;
    }

    private async Task<Dictionary<Guid, List<AvailabilityLocation>>> GetAvailabilities(Guid providerId,
        Guid[] locationIds,
        bool isOnline,
        Guid[] staffIds,
        DateRange dateRange,
        ProviderItem providerItem,
        Guid[] itemIds)
    {
        if (!isOnline && locationIds.Length <= 0) return new();

        var itemLocationIds = providerItem.LocationIds;
        
        if (isOnline && !providerItem.IsOnlyOnline && !providerItem.LocationIds.Any())
        {
            var providerLocations = await providerRepository.GetProviderLocations(providerId);
            itemLocationIds = providerLocations.Select(x => x.Id).ToArray();
        }

        var locationIdArray = isOnline ? itemLocationIds : locationIds;
        var staffAvailabilities = await staffScheduleRepository.GetStaffSchedules(providerId, locationIdArray, isOnline, staffIds, itemIds, true, dateRange);

        if (staffAvailabilities.Length <= 0) return new();

        var staffSchedules = Schedule.FromStaffSchedule(staffAvailabilities).ToList();
        Dictionary<Guid, List<AvailabilityLocation>> result = new();
        var groupedStaffSchedules = staffSchedules
            .GroupBy(x => x.PersonId);

        foreach (var groupedStaffSchedule in groupedStaffSchedules)
        {
            var staffSchedule = groupedStaffSchedule.ToArray();
            var availabilities = GetStaffAvailabilityLocation(staffSchedule, dateRange, providerItem);
            result.Add(groupedStaffSchedule.Key, availabilities);
        }

        return result;
    }

    public async Task<Dictionary<Guid, List<AvailabilityLocation>>> GetStaffOverrideAvailabilityLocations(
        Guid providerId,
        Guid itemId,
        Guid[] locationIds,
        bool isOnline,
        Guid[] staffIds,
        DateRange dateRange)
    {
        var serviceItem = await itemRepository.Get(providerId, itemId);

        var staffScheduleOverrides = await staffScheduleRepository.GetStaffScheduleOverrides(providerId, staffIds, dateRange.FromDate, dateRange.ToDate, [itemId], locationIds, isOnline);
        var result = new Dictionary<Guid, List<AvailabilityLocation>>();

        var scheduleOverridesByStaff = staffScheduleOverrides.GroupBy(x => x.PersonId);
        foreach (var overrides in scheduleOverridesByStaff)
        {
            List<AvailabilityLocation> staffAvailabilities = [];
            foreach (var staffOverride in overrides)
            {
                var staffSchedule = Schedule.FromStaffScheduleOverride(staffOverride);

                if (staffSchedule is null) continue;
                var orderedTimeBlocks = staffOverride.TimeBlocks.OrderBy(x => x.FromDate).ThenBy(x => x.ToDate).ToArray();
                var startDate = orderedTimeBlocks.First().FromDate;
                var endDate = orderedTimeBlocks.Last().ToDate;
                var availability = GetStaffAvailabilityLocation([staffSchedule], new DateRange(startDate, endDate), serviceItem);
                staffAvailabilities.AddRange(availability);
            }

            result.Add(overrides.Key, staffAvailabilities);
        }

        return result;
    }

    private List<AvailabilityLocation> GetStaffAvailabilityLocation(Schedule[] staffSchedules, DateRange dateRange, ProviderItem serviceItem)
    {
        List<AvailabilityLocation> intersectedAvailabilityLocations = [];
        var staffAvailabilityLocations = GetAvailabilityLocations(staffSchedules, dateRange, serviceItem);

        var serviceSchedules = Schedule.FromProviderItem(serviceItem);
        if (serviceSchedules.Count > 0)
        {
            var serviceAvailabilityLocations = GetAvailabilityLocations(serviceSchedules.ToArray(), dateRange, serviceItem);
            foreach (var staffAvailabilityLocation in staffAvailabilityLocations)
            {
                // service availability is 10am - 12pm | staff availability is 9am - 5pm then intersected availability is 10am - 12pm
                // service availability is 3.15pm - 7.15pm | staff availability is 3.30pm - 7pm then intersected availability is 3.30pm - 7pm
                // service availability is 3.15pm - 6.30pm | staff availability is 3.30pm - 7pm then intersected availability is 3.30pm - 6.30pm

                // broken timeslots
                // service availability is 10am - 12pm | staff availability is 9am - 12pm and 1pm - 5pm then intersected availability is 10am - 12pm
                // service availability is 10am - 3pm | staff availability is 9am - 12pm and 1pm - 5pm then intersected availability is 10am - 12pm and 1pm - 3pm
                // service availability is 10am - 12pm and 3pm - 5pm | staff availability is 9am - 12pm and 1pm - 5pm then intersected availability 10am - 12pm and 3pm - 5pm

                var overlapping = serviceAvailabilityLocations.Where(x => x.IsOverlappingDate(staffAvailabilityLocation.FromDate, staffAvailabilityLocation.ToDate)).ToArray();

                foreach (var overlap in overlapping)
                {
                    var fromDate = new DateTime(Math.Max(staffAvailabilityLocation.FromDate.Ticks, overlap.FromDate.Ticks), DateTimeKind.Utc);
                    var toDate = new DateTime(Math.Min(staffAvailabilityLocation.ToDate.Ticks, overlap.ToDate.Ticks), DateTimeKind.Utc);
                    var intersect = new AvailabilityLocation(fromDate,
                        toDate,
                        staffAvailabilityLocation.LocationIds,
                        serviceItem.Duration.GetValueOrDefault(),
                        fromDate.AddMinutes(serviceItem.BufferTimeBeforeMins * -1),
                        toDate.AddMinutes(serviceItem.BufferTimeAfterMins)
                    );
                    intersectedAvailabilityLocations.Add(intersect);
                }
            }
        }
        else intersectedAvailabilityLocations = staffAvailabilityLocations;

        List<OptimizedAvailabilityLocation> optimizedStaffAvailabilityLocations = [];
        foreach (var staffAvailabilityLocation in intersectedAvailabilityLocations)
        {
            var overlappingTimeBlock = optimizedStaffAvailabilityLocations
                .FirstOrDefault(x => x.DateRange.FromDate <= staffAvailabilityLocation.ToDate && staffAvailabilityLocation.FromDate <= x.DateRange.ToDate);

            // attach staffAvailabilityLocation from the optimizedStaffAvailabilityLocations for later comparison.
            if (overlappingTimeBlock is not null)
            {
                optimizedStaffAvailabilityLocations.Remove(overlappingTimeBlock);

                var newAvailabilityLocations = overlappingTimeBlock.AvailabilityLocations
                    .Concat(new AvailabilityLocation[] { staffAvailabilityLocation })
                    .ToArray();

                optimizedStaffAvailabilityLocations.Add(new
                (new(overlappingTimeBlock.DateRange.FromDate, staffAvailabilityLocation.ToDate),
                    newAvailabilityLocations));
            }
            else
                optimizedStaffAvailabilityLocations.Add(new
                (new(staffAvailabilityLocation.FromDate, staffAvailabilityLocation.ToDate),
                    [staffAvailabilityLocation]));
        }

        BookingServiceMeasure serviceMeasure = serviceItem;
        List<AvailabilityLocation> finalAvailability = [];

        foreach (var staffAvailability in optimizedStaffAvailabilityLocations)
        {
            if (staffAvailability.DateRange.AsTimeSpan().TotalMinutes < serviceMeasure.OriginalServiceDuration) continue;

            var _dateRange = staffAvailability.DateRange;
            var _availabilityLocations = staffAvailability.AvailabilityLocations;

            int numTimeBlocks = (int)_dateRange.AsTimeSpan().TotalMinutes / serviceMeasure.NumberToSplit;
            var current = _dateRange.FromDate;

            var fromDate = current;
            var durationEndDate = fromDate.AddMinutes(serviceMeasure.ServiceDuration);
            var endDate = fromDate.AddMinutes(serviceMeasure.MinutesToAddAfter);

            DateRange currentDateRange = new(fromDate, durationEndDate);

            // check if availabilityLocation dates is within currentDateRange dates or vise versa.
            // example 1, 2 consecutive 1hour time blocks, 9-10 and 10-11 then optimized availability is 9-11
            // with a 90min duration, currentDateRange dates will be set from 9-10.30
            // then 'currentDateRange.IsWithinDateRange(x.FromDate, x.ToDate)' will capture the 9-10 avaiability
            // and the OPOSITE IsWithinDateRange will not capture any availabilities as the currentDateRange of 9-10.30
            // is not within the 2 1hour timeblocks

            // example 2, time blocks of 9am-5pm will then optimized to the same time.
            // with a 90min duration, currentDateRange dates will be set from 9-10.30
            // then 'currentDateRange.IsWithinDateRange(x.FromDate, x.ToDate)' will not capture any availabilities as 9am-5pm is not within 9-10.30
            // and then OPOSITE IsWithinDateRange will capture 9am-5pm timeblock as 9-10.30 is within 9am-5pm.
            var locationIds = _availabilityLocations
                .Where(x => currentDateRange.IsWithinDateRange(x.FromDate, x.ToDate) ||
                            // oposite IsWithinDateRange. i copied the logic behind 'IsWithinDateRange' instead of creating DateRange object every iteration
                            (x.FromDate <= currentDateRange.FromDate && currentDateRange.ToDate <= x.ToDate))
                .SelectMany(x => x.LocationIds)
                .ToHashSet();

            // only add currentDateRange when currentDateRange.FromDate is future date
            if (currentDateRange.FromDate >= dateRange.FromDate)
                finalAvailability.Add(new(currentDateRange,
                    locationIds, serviceItem.Duration.GetValueOrDefault(),
                    currentDateRange.FromDate.AddMinutes(serviceItem.BufferTimeBeforeMins * -1),
                    currentDateRange.ToDate.AddMinutes(serviceItem.BufferTimeAfterMins)
                ));

            current = endDate;

            for (int i = 1; i < numTimeBlocks; i++)
            {
                fromDate = current.AddMinutes(serviceMeasure.MinutesToAddBefore);
                durationEndDate = fromDate.AddMinutes(serviceMeasure.ServiceDuration);
                endDate = fromDate.AddMinutes(serviceMeasure.MinutesToAddAfter);

                // check if availability end datetime is greater than serviceDurationEndDate
                // this is to make sure that we wont add availability that exceeds staff schedule.
                // 2023-06-19: process record when fromDate is future date
                if (_dateRange.ToDate >= durationEndDate && fromDate >= dateRange.FromDate)
                {
                    currentDateRange = new(fromDate, durationEndDate);

                    // same goes here.
                    locationIds = _availabilityLocations
                        .Where(x => currentDateRange.IsWithinDateRange(x.FromDate, x.ToDate) ||
                                    (x.FromDate <= currentDateRange.FromDate && currentDateRange.ToDate <= x.ToDate))
                        .SelectMany(x => x.LocationIds)
                        .ToHashSet();

                    finalAvailability.Add(new(currentDateRange,
                        locationIds,
                        serviceItem.Duration.GetValueOrDefault(),
                        currentDateRange.FromDate.AddMinutes(serviceItem.BufferTimeBeforeMins * -1),
                        currentDateRange.ToDate.AddMinutes(serviceItem.BufferTimeAfterMins)
                    ));
                }

                current = endDate;
            }
        }

        return finalAvailability;
    }

    private List<AvailabilityLocation> GetAvailabilityLocations(Schedule[] staffSchedules, DateRange dateRange, ProviderItem serviceItem)
    {
        var staffAvailabilityLocations = new List<AvailabilityLocation>();

        foreach (var staffSchedule in staffSchedules)
        {
            if (staffSchedule.TimeBlocks.IsNullOrEmpty())
                continue;

            var scheduleRange = staffSchedule.ResolveScheduleDateRange(dateRange, staffSchedule.TimeZone);
            var startDate = staffSchedule.StartDate ?? dateTimeProvider.GetDateTimeUtc().ToDateOnly();

            // set default to weekly
            RRule rrule = staffSchedule.RRule ?? new RRule("FREQ=WEEKLY");
            foreach (var timeBlock in staffSchedule.TimeBlocks)
            {
                var availabilityLocations = StaffScheduleExtensions.GetTimeBlockAvailabilityLocations(startDate,
                    serviceItem.Duration.GetValueOrDefault(),
                    serviceItem.BufferTimeBeforeMins,
                    serviceItem.BufferTimeAfterMins,
                    timeBlock,
                    rrule,
                    staffSchedule.TimeZone,
                    scheduleRange.FromDate,
                    scheduleRange.ToDate);
                
                staffAvailabilityLocations.AddRange(availabilityLocations);
            }
        }

        return staffAvailabilityLocations
            .OrderBy(x => x.FromDate)
            .ThenBy(x => x.ToDate)
            .ToList();
    }

    private async Task<TaskModel[]> TryFetchExternalTasks(Guid providerId, Guid[] staffIds, DateRange dateRange)
    {
        using (LogContext.PushProperty("TryFetchExternalTasks", new
               {
                   ProviderId = providerId,
                   StaffIds = staffIds,
                   dateRange.FromDate,
                   dateRange.ToDate
               }))
        {
            try
            {
                var options = new GetEventsOptions()
                {
                    FromStartDate = dateRange.FromDate,
                    ToEndDate = dateRange.ToDate,
                };

                var externalEvents = await connectedCalendarService.GetExternalTasks(staffIds, providerId, options);

                // Filtering out all day tasks.
                // Currently these cause issues with availability due to them not having any time zone specific information (only the date)
                // We could probably resolve this by
                // - requesting the time zone from the external calendar
                //      - prob most reliable - requires extra request.
                //      - looks like Microsoft doesnt return time zones in a nice format though: https://github.com/microsoftgraph/microsoft-graph-docs/issues/19332
                var finalExternalEvents = externalEvents.Where(_ => !_.AllDay)
                    .ToArray();
                return finalExternalEvents;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error occurred loading external tasks");
                return [];
            }
        }
    }

    private DateTime ResolveBookingDate(OnlineBookingPolicyUnit unit, int bookingTime, DateTime currentDate, DateTime fallbackDate)
    {
        // yikes
        if (unit is OnlineBookingPolicyUnit.Minutes) return currentDate.AddMinutes(bookingTime);
        else if (unit is OnlineBookingPolicyUnit.Hours) return currentDate.AddHours(bookingTime);
        else if (unit is OnlineBookingPolicyUnit.Days) return currentDate.AddDays(bookingTime);
        else if (unit is OnlineBookingPolicyUnit.Months) return currentDate.AddMonths(bookingTime);
        else return fallbackDate;
    }

    public async Task<List<BookingStaffMemberNextAvailability>> GetStaffMemberNextAvailabilities(Guid providerId,
        ProviderItem providerItem,
        Guid[] locationIds,
        bool isOnline,
        Guid? staffId = null)
    {
        using (LogContext.PushProperty("GetStaffMemberNextAvailabilitiesDebugDetails", new
               {
                   ProviderId = providerId,
                   LocationIds = string.Join(',', locationIds),
                   IsOnline = isOnline,
                   StaffId = staffId,
                   ProviderItemId = providerItem.Id
               }))
        {
            List<BookingStaffMemberNextAvailability> result = [];
            var staffMembers = await bookingRepository.GetStaffMemberByProvider(providerId, staffId);
            if (!staffMembers.Any())
            {
                Log.Information("No staff members found for provider: {providerId}, staffMember: {staffId}", providerId, staffId);
                return result;
            }

            staffMembers = staffMembers.Where(x => !x.IsSupport()).ToArray();

            var currentUtcDate = dateTimeProvider.GetDateTimeUtc();
            var firstMonthRange = new DateRange(currentUtcDate, currentUtcDate.AddMonths(1));
            var bookingPolicyDateRange = await GetOnlineBookingPolicyDateRange(providerId, firstMonthRange);
            if (bookingPolicyDateRange is null)
                throw new ExecutionException(new ValidationError(Errors.BookingPolicyDateRangeExceededCode, Errors.BookingPolicyDateRangeExceededDetail, ValidationType.BadRequest));

            var bookingPolicyDateRanges = bookingPolicyDateRange?.GetDateRangeMonthlyInterval() ?? [];

            var locations = await providerRepository.GetProviderLocationByIds(providerId, locationIds);
            var locationAddresses = locations.Select(x => x.Address).ToArray();
            var providerLocationIds = locations.Select(x => x.Id).ToArray();
            
            var currentStaffMembers = staffMembers;
            foreach (var dateRange in bookingPolicyDateRanges)
            {
                using (LogContext.PushProperty("CurrentBookingPolicyDateRangeDebugDetails", new
                       {
                           dateRange.FromDate,
                           dateRange.ToDate,
                           CurrentDateUtc = currentUtcDate,
                       }))
                {
                    var staffIds = currentStaffMembers.Select(x => x.Id).ToArray();
                    var availabilities = await GetStaffAvailabilities(providerId,
                        locationIds,
                        isOnline,
                        staffIds,
                        dateRange,
                        providerItem);

                    var staffScheduleOverrideAvailabilities = await GetStaffOverrideAvailabilityLocations(providerId, providerItem.Id, locationIds, isOnline, staffIds, bookingPolicyDateRange);
                    if (staffScheduleOverrideAvailabilities.Any())
                    {
                        foreach (var overrideAvailability in staffScheduleOverrideAvailabilities)
                        {
                            if (availabilities.ContainsKey(overrideAvailability.Key))
                            {
                                var staffAvailability = availabilities[overrideAvailability.Key];
                                staffAvailability.AddRange(overrideAvailability.Value);
                            }
                            else
                            {
                                availabilities.Add(overrideAvailability.Key, overrideAvailability.Value);
                            }
                        }
                    }

                    if (!availabilities.Any())
                    {
                        Log.Information("No availabilities generated on staff members: {staffIds}", string.Join(',', staffIds));
                        continue;
                    }

                    var allStaffTasks = await GetStaffTasks(providerId, dateRange, staffIds);
                    var staffTaskDictionary = allStaffTasks.GroupByStaff();

                    foreach (var staff in currentStaffMembers)
                    {
                        if (!availabilities.TryGetValue(staff.Id, out var staffAvailability))
                        {
                            Log.Information("No availability found for staff: {staffId}", staff.Id);
                            continue;
                        }
                        
                        Log.Information("Starting to look for staff member: {staffId} availabilities; Current availability count: {staffAvailabilityCount}", staff.Id, staffAvailability.Count);

                        var staffTasks = staffTaskDictionary.ContainsKey(staff.Id)
                            ? staffTaskDictionary[staff.Id]
                            : [];
                        
                        Log.Information("Found {staffTaskCount} tasks for staffMember {staffId}", staffTasks.Count, staff.Id);

                        if (providerItem.AllowGroupEvents)
                        {
                            List<AvailabilityLocation> finalAvailability = staffAvailability.ToList();
                            foreach (var availability in staffAvailability)
                            {
                                foreach (var task in staffTasks)
                                {
                                    var hasMatchingLocation = (task.LocationsSnapshot?.Any(x => x.LocationId.HasValue && providerLocationIds.Contains(x.LocationId.Value)) ?? false)
                                                              || locationAddresses.Contains(task.Location)
                                                              || (isOnline && task.CallId.HasValue);

                                    if (task.StartDate == availability.FromDate &&
                                        (task.EndDate == availability.ToDate ||
                                         task.EndDate == availability.ActualToDate) &&
                                        hasMatchingLocation)
                                    {
                                        var spotsLeft = task.GetGroupEventRemainingSpots(providerItem.Id);
                                        if (spotsLeft is null || spotsLeft <= 0)
                                            finalAvailability.Remove(availability);
                                        else
                                            //availability and task dates overlap, but still available since there are spots left
                                            continue;
                                    }

                                    if (availability.IsOverlappingDate(task.StartDate, task.EndDate))
                                        finalAvailability.Remove(availability);
                                }
                            }

                            staffAvailability = finalAvailability;
                        }
                        else
                        {
                            foreach (var task in staffTasks)
                            {
                                staffAvailability
                                    .Where(x => x.IsOverlappingDate(task.StartDate, task.EndDate))
                                    .ToList()
                                    .ForEach(x => staffAvailability.Remove(x));
                            }
                        }

                        var firstAvailability = staffAvailability
                            .Where(x => x.FromDate >= bookingPolicyDateRange.FromDate)
                            .OrderBy(x => x.FromDate)
                            .ThenBy(x => x.ToDate)
                            .FirstOrDefault();

                        if (firstAvailability is not null)
                        {
                            BookingStaffMemberNextAvailability memberNextAvailability = staff;
                            memberNextAvailability.NextAvailability = firstAvailability.FromDate;
                            result.Add(memberNextAvailability);
                        }
                        else
                        {
                            Log.Information("No next availability found for staff member for this iteration: {staffId}; HasAvailabilityLeft: {hasAvailabilityLeft}", staff.Id, staffAvailability.Any());
                        }
                    }

                    var staffLookup = currentStaffMembers.Where(x => !result.Any(y => y.Id == x.Id)).ToArray();
                    if (staffLookup.Length <= 0) break;

                    currentStaffMembers = staffLookup;
                }
               
            }

            return result;
        }
    }

    public async Task<Contact> CreateRelatedContactRelationship(Contact primaryContact,
        BookingRelatedContactDetail relatedContactDetail,
        Guid staffId,
        Person bookingPerson,
        CultureInfo preferredCulture)
    {
        var (contact, _) = await GetOrCreateBookingContactAndPerson(relatedContactDetail,
            primaryContact.ProviderId,
            staffId,
            null,
            preferredCulture,
            bookingPerson,
            null,
            false);

        if (primaryContact.Id.Equals(contact?.Id))
        {
            // A bit of an edge case
            // We had cases where equal information was entered into the "related contact" form
            // which caused errors when confirming appointments.
            // Ignoring related contact if this happens, but it does mean some booking context may be lost.
            // Logging to see how frequently this occurs.
            Log.ForContext("BookingContacts", new
                {
                    PrimaryContactId = primaryContact?.Id,
                    RelatedContactId = contact?.Id
                })
                .Warning("Online booking. Related contact equals booking contact");
            return null;
        }

        await contactService.CreateRelationship(primaryContact,
            contact,
            relatedContactDetail.Relationship);

        return contact;
    }
}