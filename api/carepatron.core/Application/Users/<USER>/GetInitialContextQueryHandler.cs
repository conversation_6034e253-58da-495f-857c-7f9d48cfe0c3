﻿using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Staff.Models;
using carepatron.core.Application.Users.Models;
using carepatron.core.Extensions;
using carepatron.core.Mappers;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Onboarding;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Billing;
using carepatron.core.Repositories.ConnectedApps;
using carepatron.core.Repositories.Onboarding;
using carepatron.core.Repositories.Person;
using carepatron.core.Repositories.Provider;
using carepatron.core.Repositories.Registration;

namespace carepatron.core.Application.Users.Queries
{
    public class GetInitialContextQueryHandler : IMediatrQueryHandler<GetInitialContextQuery, InitialContextResult>
    {
        private readonly IPersonRepository personRepository;
        private readonly IProviderStaffRepository providerStaffRepository;
        private readonly IProviderRepository providerRepository;
        private readonly IConnectedAppRepository connectedAppRepository;
        private readonly IBillingRepository billingRepository;
        private readonly IOnboardingRepository onboardingRepository;
        private readonly IContactRepository contactRepository;
        private readonly IRegisteredWorkspaceRepository registeredWorkspaceRepository;

        public GetInitialContextQueryHandler(IPersonRepository personRepository,
            IProviderStaffRepository providerStaffRepository,
            IProviderRepository providerRepository,
            IConnectedAppRepository connectedAppRepository,
            IBillingRepository billingRepository,
            IOnboardingRepository onboardingRepository,
            IContactRepository contactRepository,
            IRegisteredWorkspaceRepository registeredWorkspaceRepository)
        {
            this.personRepository = personRepository;
            this.providerStaffRepository = providerStaffRepository;
            this.providerRepository = providerRepository;
            this.connectedAppRepository = connectedAppRepository;
            this.billingRepository = billingRepository;
            this.onboardingRepository = onboardingRepository;
            this.contactRepository = contactRepository;
            this.registeredWorkspaceRepository = registeredWorkspaceRepository;
        }

        public async Task<ExecutionResult<InitialContextResult>> Handle(GetInitialContextQuery request, CancellationToken cancellationToken)
        {
            var identityContext = request.IdentityContext;
            var personId = identityContext.PersonId;

            var person = await personRepository.Get(personId);
            var providerStaffs = await providerStaffRepository.GetAllProviderStaff(personId);
            var providerId = request.CurrentProviderId.HasValue && providerStaffs.Any(x => x.ProviderId == request.CurrentProviderId)
                ? request.CurrentProviderId
                : providerStaffs.FirstOrDefault()?.ProviderId;

            var connectedApps = await connectedAppRepository.GetSimpleConnectedApps(personId, providerId, null);
            var connectedAppsItems = connectedApps.Items.ToList();
            var personalSettings = await personRepository.GetPersonalSettings(personId);


            if (providerStaffs.Length <= 0 || providerId.IsNullOrEmpty() || (request.IsPortal.GetValueOrDefault() && !providerId.IsNullOrEmpty()))
            {
                //Client Portal User

                List<ProviderStaffDetail> providerDetails = null;
                if (request.IsPortal.GetValueOrDefault() && !providerId.IsNullOrEmpty())
                {
                    var providers = await providerRepository.GetProviders(providerStaffs.Select(x => x.ProviderId).ToArray());
                    providerDetails = ProviderMapper.ToProviderStaffDetails(providers, providerStaffs);
                }

                bool canBookAppointment = await contactRepository.IsClientOfProvider(personId);
                return new InitialContextResult(person, connectedAppsItems, canBookAppointment, personalSettings, providerDetails, providerDetails?.Count > 0);
            }
            else
            {
                //Provider Staff User
                var providers = await providerRepository.GetProviders(providerStaffs.Select(x => x.ProviderId).ToArray());
                var providerDetails = ProviderMapper.ToProviderStaffDetails(providers, providerStaffs);

                // Update LastAccessedUtc for the current provider only if explicitly provided
                if (providerId.HasValue)
                {
                    await providerRepository.UpdateLastAccessedUtc(providerId.Value);
                }

                var permissions = await providerStaffRepository.GetProviderPermissionForStaffMember(providerId.Value, personId);
                var staffMembers = await providerStaffRepository.GetAllProviderStaffMembersByProvider(providerId.Value);
                var billingSettings = await providerRepository.GetBillingSettings(providerId.Value);
                var billingAccount = await billingRepository.GetByProviderId(providerId.Value);
                var personalPreferences = await personRepository.GetApplicablePersonalPreferences(personId, providerId.Value);
                var providerWorkspaceSettings = await providerRepository.GetWorkspacePreferences(providerId.Value);

                var onboarding = await onboardingRepository.Get(person.Id, providerId.Value) ?? core.Models.Onboarding.Onboarding.Default(person.Id, providerId);
                var registeredWorkspace = await registeredWorkspaceRepository.GetByProviderId(providerId.Value);


                return new InitialContextResult(person,
                    connectedAppsItems,
                    providerDetails,
                    providerId.Value,
                    permissions,
                    staffMembers.ToList(),
                    billingSettings,
                    billingAccount,
                    onboarding,
                    personalSettings,
                    personalPreferences,
                    providerWorkspaceSettings,
                    registeredWorkspace,
                    true);
            }
        }
    }
}
