using carepatron.core.Abstractions;
using carepatron.core.Application.AskAI.Models;
using carepatron.core.Application.AskAI.Services;
using carepatron.core.Constants;
using carepatron.core.Identity;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.AskAI;
using carepatron.core.Repositories.Notes;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.AskAI.Commands;
public record CreateAiConversationCommand(IIdentityContext IdentityContext, string Text, IList<AiMessageContextRequest> Contexts, Guid ProviderId, Guid? NoteId) : IMediatrCommand<CreateAiConversationResponse>;

public class CreateAiConversationCommandValidator : AbstractValidator<CreateAiConversationCommand>
{
    public CreateAiConversationCommandValidator()
    {
        RuleFor(x => x)
            .Must(x => x.Contexts
                .Count(y => y.Type == AiMessageContextType.Reference && y.Entity == AiMessageContextEntityType.Attachment) <= 10)
            .WithErrorCode(Errors.MaxFileLimitExceededCode)
            .WithMessage(Errors.MaxFileLimitExceededDetail)
            .When(x => x.Contexts != null);

        RuleFor(command => command.Contexts)
            .ForEach(x => x.SetValidator(new AiMessageContextRequestValidator()))
            .When(x => x.Contexts != null);
    }
}

public class CreateAiConversationCommandHandler
    (IAiConversationRepository aiConversationRepository,
    IAskAiService askAiService,
    IAiMessageRepository aiMessageRepository,
    IUnitOfWork unitOfWork,
    INoteAiConversationRepository noteAiConversationRepository,
    INoteRepository noteRepository) : IMediatrCommandHandler<CreateAiConversationCommand, CreateAiConversationResponse>
{
    public async Task<ExecutionResult<CreateAiConversationResponse>> Handle(CreateAiConversationCommand request, CancellationToken cancellationToken)
    {
        var dateTimeUtcNow = DateTime.UtcNow;
        var conversation = new AiConversation
        {
            Id = Guid.NewGuid(),
            ProviderId = request.ProviderId,
            CreatedByPersonId = request.IdentityContext.PersonId,
            CreatedDateTimeUtc = dateTimeUtcNow,
            LastUpdatedDateTimeUtc = dateTimeUtcNow,
        };

        if (request.Contexts is { Count: > 0 })
        {
            var noteContexts = request.Contexts
                .Where(x => x.Type == AiMessageContextType.Reference && x.Entity == AiMessageContextEntityType.Note)
                .ToList();

            if (noteContexts is { Count: 1 })
            {
                conversation.NoteId = Guid.Parse(noteContexts.First().Value);
                var note = await noteRepository.GetMeta(conversation.NoteId.Value);

                if (note != null)
                {
                    var contactContext = request.Contexts.FirstOrDefault(x =>
                        x.Type == AiMessageContextType.Reference &&
                        x.Entity == AiMessageContextEntityType.Contact &&
                        x.Value == note.ContactId.ToString());

                    if (contactContext == null)
                    {
                        request.Contexts.Add(new AiMessageContextRequest
                        {
                            Type = AiMessageContextType.Reference,
                            Entity = AiMessageContextEntityType.Contact,
                            Value = note.ContactId.ToString()
                        });
                    }

                    await noteAiConversationRepository.Create(new NoteAiConversation
                    {
                        AiConversationId = conversation.Id,
                        NoteId = conversation.NoteId.Value
                    });
                }
            }
        }

        conversation = await aiConversationRepository.Create(conversation);

        var aiMessageResponse = await askAiService.SendMessage(conversation, request.Text, request.Contexts?.ToArray(), request.IdentityContext.PersonId);

        await unitOfWork.SaveUnitOfWork();

        var messages = await aiMessageRepository.GetAiMessagesForAgent(conversation.Id);

        return new CreateAiConversationResponse
        {
            AiConversation = conversation,
            AiMessages = messages
        };
    }
}