﻿using carepatron.core.Application.Trash.Services;
using carepatron.core.Identity;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.AskAI;
using carepatron.core.Repositories.Notes;
using carepatron.core.Validation;
using FluentValidation;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.AskAI.Commands;

public record DeleteNoteAiConversationCommand(IIdentityContext IdentityContext, Guid NoteId, Guid AiConversationId) : IMediatrCommand<Unit>;

public class DeleteNoteAiConversationCommandValidator : AbstractValidator<DeleteNoteAiConversationCommand>
{
    public DeleteNoteAiConversationCommandValidator()
    {
        RuleFor(x => x.NoteId).IsRequired();
        RuleFor(x => x.AiConversationId).IsRequired();
    }
}

public class DeleteNoteAiConversationCommandHandler(
        INoteRepository noteRepository,
        IAiConversationRepository aiConversationRepository,
        INoteAiConversationRepository noteAiConversationRepository,
        ITrashService trashService) : IMediatrCommandHandler<DeleteNoteAiConversationCommand, Unit>
{
    public async Task<ExecutionResult<Unit>> Handle(DeleteNoteAiConversationCommand request, CancellationToken cancellationToken)
    {
        var note = await noteRepository.GetMeta(request.NoteId);
        if (note == null)
        {
            return ValidationError.NotFound;
        }

        var aiConversation = await aiConversationRepository.GetById(request.AiConversationId);

        if (aiConversation == null)
        {
            return ValidationError.NotFound;
        }

        var noteAiConversation = await noteAiConversationRepository.Get(request.NoteId, request.AiConversationId, false);

        if (noteAiConversation == null)
        {
            return ValidationError.NotFound;
        }

        await noteAiConversationRepository.Delete(request.NoteId, request.AiConversationId);
        await trashService.SaveTrashItem(noteAiConversation, request.IdentityContext.PersonId);

        return Unit.Value;
    }
}