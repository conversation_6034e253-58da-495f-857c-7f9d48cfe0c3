using System;
using System.Collections.Generic;

namespace carepatron.core.Application.Templates.Models;

public static class Professions
{
    public static string[] All =
    [
        "Acupuncture",
        "BehavioralHealthTherapy",
        "Chiropractic",
        "Coaching",
        "Counseling",
        "DieteticsOrNutrition",
        "FunctionalMedicineOrNaturopath",
        "HealthCoach",
        "LactationConsulting",
        "MassageTherapy",
        "Medicine",
        "Nursing",
        "OccupationalTherapy",
        "PersonalTraining",
        "PhysicalTherapy",
        "Psychiatry",
        "Psychology",
        "SocialWork",
        "SpeechLanguagePathology",
        "Accountant",
        "Actor",
        "Acupuncturist",
        "AddictionCounselor",
        "AdvertisingManager",
        "AerospaceEngineer",
        "ArtTherapist",
        "Artist",
        "AthleticTrainer",
        "Audiologist",
        "Banker",
        "BehavioralAnalyst",
        "Biller",
        "BiomedicalEngineer",
        "CardiacRehabilitationSpecialist",
        "Cardiologist",
        "Chef",
        "<PERSON>ropractor",
        "CivilEngineer",
        "ClinicalPsychologist",
        "CommunityHealthWorker",
        "ComputerSystemsAnalyst",
        "ConstructionWorker",
        "Consultant",
        "Cosmetologist",
        "Counselor",
        "CustomerServiceRepresentative",
        "DanceTherapist",
        "DentalAssistant",
        "DentalHygienist",
        "Dentist",
        "Dietitian",
        "DramaTherapist",
        "Economist",
        "ElectricalEngineer",
        "ElementarySchoolTeacher",
        "EnvironmentalScientist",
        "Epidemiologist",
        "ExercisePhysiologist",
        "FashionDesigner",
        "FinancialAnalyst",
        "Firefighter",
        "GeneticCounselor",
        "Gerontologist",
        "GraphicDesigner",
        "HairStylist",
        "HealthEducator",
        "HealthInformationTechnician",
        "HealthPolicyExpert",
        "HealthServicesAdministrator",
        "HolisticHealthPractitioner",
        "HomeHealthAide",
        "HumanResourcesManager",
        "Hypnotherapist",
        "InteriorDesigner",
        "Journalist",
        "Lawyer",
        "Librarian",
        "LifeCoach",
        "MarketingManager",
        "MassageTherapist",
        "MechanicalEngineer",
        "MedicalAssistant",
        "MedicalCoder",
        "MedicalDoctor",
        "MedicalIllustrator",
        "MedicalInterpreter",
        "MedicalTechnologist",
        "MultiSpeciality",
        "MusicTherapist",
        "NaturopathicDoctor",
        "Neurologist",
        "NuclearMedicineTechnologist",
        "NurseAnesthetist",
        "NurseEducator",
        "NurseMidwife",
        "NursePractitioner",
        "Nutritionist",
        "ObstetricianOrGynecologist",
        "OccupationalTherapist",
        "Oncologist",
        "Ophthalmologist",
        "Optometrist",
        "Orthodontist",
        "Orthotist",
        "Pathologist",
        "Pediatrician",
        "PersonalTrainer",
        "Pharmacist",
        "PhysicalTherapist",
        "PhysicianAssistant",
        "PoliceOfficer",
        "PrivatePracticeConsultant",
        "Prosthetist",
        "Psychiatrist",
        "Psychoanalyst",
        "Psychologist",
        "Psychometrician",
        "PsychosocialRehabilitationSpecialist",
        "PublicHealthInspector",
        "RadiationTherapist",
        "Radiologist",
        "RealEstateAgent",
        "RegisteredNurse",
        "RehabilitationCounselor",
        "RespiratoryTherapist",
        "SalesRepresentative",
        "SocialWorker",
        "SoftwareDeveloper",
        "SpeechTherapist",
        "SportsMedicinePhysician",
        "Surgeon",
        "SurgicalTechnologist",
        "TeacherAssistant",
        "Therapist",
        "Veterinarian",
        "WebDeveloper",
        "Writer",
        "YogaInstructor",
        "MyofunctionalTherapist",
        "PreventionSpecialist",
        "DogWalker",
        "PsychiatricNursePractitioner"
    ];
}

public class TempTemplateProfession
{
    public string FullName { get; set; }
    public Guid PersonId { get; set; }
    public Guid PublicTemplateId { get; set; }
    public IList<string> Professions { get; set; }
}