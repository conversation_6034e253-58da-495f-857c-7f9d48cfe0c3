using carepatron.core.Application.Templates.Models;
using carepatron.core.Constants;
using carepatron.core.Events;
using carepatron.core.Events.Attributes;
using carepatron.core.Events.Events;
using carepatron.core.Events.Handlers;
using carepatron.core.Extensions;
using carepatron.core.Repositories.Person;
using carepatron.core.Repositories.Templates;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.core.Application.Templates.EventHandlers;

[EventTypeHandler(EventType.ValidatePublicTemplateProfessions)]
public class ValidatePublicTemplateProfessionsEventHandler(
    IPersonRepository personRepository,
    ITempTemplateProfessionRepository tempTemplateProfessionRepository,
    ITempTemplateProfessionCsvRepository tempTemplateProfessionCsvRepository,
    ILogger<ValidatePublicTemplateProfessionsEventHandler> logger) : IEventHandler
{
    public EventType EventType => EventType.ValidatePublicTemplateProfessions;

    public async Task Handle(EventMessage eventMessage)
    {
        LogContext.PushProperty(nameof(CodeOwner), CodeOwner.NotesAndDocuments);
        logger.LogInformation("Starting validation process for public template professions");

        try
        {
            var eventData = eventMessage.As<EventData<ValidatePublicTemplateProfessionsEventData>>();

            var batchSize = 500;

            var expectedCount = eventData.Data.ExpectedCount;

            var templatesToUpdate = await tempTemplateProfessionCsvRepository.GetTempTemplateProfessions(0, expectedCount);

            if (templatesToUpdate.Count != expectedCount)
            {
                logger.LogError("Validation failed with csv items count {0} and expected count {1}", templatesToUpdate.Count, expectedCount);
                return;
            }

            // Validate persons
            var distinctTemplatesToUpdatePersonIds = templatesToUpdate
                .Select(x => x.PersonId)
                .Distinct()
                .ToArray();

            Log.Information("Fetching persons in the csv");
            var persons = await personRepository.GetPersons(distinctTemplatesToUpdatePersonIds);
            Log.Information("Fetched persons in the csv");

            var personIds = persons.Select(x => x.Id).ToArray();

            // Validate templates
            var distinctTemplatesToUpdateTemplateIds = templatesToUpdate
                .Select(x => x.PublicTemplateId)
                .Distinct()
                .ToArray();

            Log.Information("Fetching template ids in the csv");

            var publicTemplatesLookup = new List<(Guid PublicTemplateId, string[] Professions)>();

            foreach (var (idsBatch, batchIndex) in distinctTemplatesToUpdateTemplateIds.Chunk(batchSize).Select((idsBatch, batchIndex) => (idsBatch, batchIndex)))
            {
                Log.Information("Fetching templates for batch {BatchIndex} for ids started: {Ids}", batchIndex, string.Join(", ", idsBatch));

                var publicTemplatesBatch = await tempTemplateProfessionRepository.GetLivePublicTemplatesForBackup(idsBatch);

                Log.Information("Fetching templates for batch {BatchIndex} for ids completed: {Ids}", batchIndex, string.Join(", ", idsBatch));

                publicTemplatesLookup.AddRange(publicTemplatesBatch);
            }

            Log.Information("Fetched template ids in the csv");

            Log.Information("Validating csv records...");

            var publicTemplateIds = publicTemplatesLookup
                .Select(x => x.PublicTemplateId)
                .Distinct()
                .ToArray();

            var validItems = templatesToUpdate
                .Where(x => personIds.Contains(x.PersonId)) // Validate Person existing
                .Where(x => publicTemplateIds.Contains(x.PublicTemplateId)) // Validate Template existing
                .ToArray();

            Log.Information("Validation done..");

            if (validItems.Length == expectedCount)
            {
                logger.LogInformation("Validation success with valid items count {0} and expected count {1}", validItems.Length, expectedCount);

                // Only perform the backup if do backup is set to true and validation is completed
                if (eventData.Data.DoBackup)
                {
                    logger.LogInformation("Executing backup of {expectedCount} items", validItems.Length);

                    await tempTemplateProfessionRepository.PurgeTempPublicTemplateProfessions(publicTemplateIds);

                    foreach (var publicTemplate in publicTemplatesLookup)
                    {
                        if (publicTemplate.Professions.IsNullOrEmpty())
                        {
                            // Log this information
                            logger.LogInformation("Nothing to backup for template: {TemplateId}", publicTemplate.PublicTemplateId);
                        }

                        await tempTemplateProfessionRepository.CreateTempPublicTemplateProfessions(publicTemplate.PublicTemplateId, publicTemplate.Professions);
                    }

                    logger.LogInformation("Backup completed of {expectedCount} items", validItems.Length);
                }
            }
            else
            {
                logger.LogInformation("Validation failed with valid items count {0} and expected count {1}", validItems.Length, expectedCount);

                foreach (var (templateToUpdate, index) in templatesToUpdate.Select((profession, index) => (profession, index)))
                {
                    if (!personIds.Any(x => x == templateToUpdate.PersonId))
                    {
                        logger.LogError("Author not found: {PersonId} at index {Index}", templateToUpdate.PersonId, index);
                    }

                    if (!publicTemplateIds.Any(x => x == templateToUpdate.PublicTemplateId))
                    {
                        logger.LogError("Public template not found: {TemplateId} at index {Index}", templateToUpdate.PublicTemplateId, index);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // Suppress exception but log an error to avoid retry
            logger.LogError(ex, "An error occurred during the validation process for public template professions");
        }

        Log.ForContext("ValidatePublicTemplateProfessionsMigrationEventHandler ", eventMessage.Id)
                .Information("Finished processing");
    }
}