﻿using carepatron.core.Application.Notes.Models;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Events.Handlers;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Repositories.Files;
using carepatron.core.Repositories.SQS;
using carepatron.core.Repositories.Templates;
using Serilog;
using Serilog.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.core.Application.Templates.EventHandlers
{
    public class SecureEmbeddedPublicTemplateFilesEventHandler : IEventHandler<EventData<SecureEmbeddedPublicTemplateFilesEventData>>
    {
        private const int BatchSize = 100;

        private readonly ITemplateRepository templateRepository;
        private readonly ISqsRepository sqsRepository;
        private readonly IFileStorageRepository fileStorageRepository;
        private readonly IPublicTemplateAttachmentRepository publicTemplateAttachmentRepository;

        public SecureEmbeddedPublicTemplateFilesEventHandler(
            ITemplateRepository templateRepository,
            ISqsRepository sqsRepository,
            IFileStorageRepository fileStorageRepository,
            IPublicTemplateAttachmentRepository publicTemplateAttachmentRepository)
        {
            this.templateRepository = templateRepository;
            this.sqsRepository = sqsRepository;
            this.fileStorageRepository = fileStorageRepository;
            this.publicTemplateAttachmentRepository = publicTemplateAttachmentRepository;
        }

        public EventType EventType => EventType.SecureEmbeddedTemplateFiles;

        public async Task Handle(EventData<SecureEmbeddedPublicTemplateFilesEventData> evt)
        {
            Log.Information("Beginning to run SecureEmbeddedPublicTemplateFilesEventHandler");

            var data = evt?.Data;
            var errors = data?.Errors ?? new();
            var totalProcessed = data?.TotalProcessed ?? 0;
            if (data?.HasProcessExceeded ?? false)
                return;

            var publicTemplates = await templateRepository.GetPublicTemplates(data.DateRange, data.PublicTemplateIds, data.LastPublicTemplateId, BatchSize, null);

            if (publicTemplates.Length <= 0) return;

            var publicTemplatesToProcess = new List<PublicTemplateMeta>();

            if (errors.Count > 0)
            {
                var publicTemplatesToRetry = await templateRepository.GetPublicTemplates(null, errors.Keys.ToArray(), null, errors.Count, null);

                publicTemplatesToProcess.AddRange(publicTemplatesToRetry);
            }

            publicTemplatesToProcess.AddRange(publicTemplates);

            foreach (var publicTemplate in publicTemplatesToProcess)
            {
                using (LogContext.PushProperty("PublicTemplate", publicTemplate.Id))
                {
                    try
                    {
                        var publicTemplateAttachments = await publicTemplateAttachmentRepository.GetPublicTemplateAttachments(publicTemplate.Id);
                        foreach (var publicAttachment in publicTemplateAttachments)
                        {
                            using (LogContext.PushProperty("Public Template Attachment", publicAttachment.Id))
                            {
                                if (!await fileStorageRepository.IsFileSecured(publicAttachment.Id.ToString()))
                                {
                                    await fileStorageRepository.SecureFile(publicAttachment.Id.ToString());
                                    Log.ForContext("FileId", publicAttachment.Id)
                                        .Information("File secured");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        HandleErrorCount(ex, errors, publicTemplate.Id);
                    }
                }
            }

            Log.ForContext("SecureEmbeddedPublicTemplateFilesEventHandler", publicTemplates.Length)
              .Information("Finished processing batch");

            var lastPublicTemplateId = publicTemplatesToProcess.Last().Id;
            SecureEmbeddedPublicTemplateFilesEventData secureEmbeddedPublicTemplateFilesEventData = new(data.DateRange, data.PublicTemplateIds, lastPublicTemplateId, errors, totalProcessed + BatchSize, data?.MaxToProcess);
            await sqsRepository.SendMessage(QueueType.Task,
                new EventMessage(EventType.SecureEmbeddedPublicTemplateFiles, new EventData<SecureEmbeddedPublicTemplateFilesEventData>(secureEmbeddedPublicTemplateFilesEventData), Guid.NewGuid().ToString(), null));
        }

        public void HandleErrorCount(Exception ex, Dictionary<Guid, int> publicTemplateErrors, Guid publicTemplateId)
        {
            if (publicTemplateErrors.TryGetValue(publicTemplateId, out int retryCount))
            {
                if (publicTemplateErrors[publicTemplateId] >= 3)
                    publicTemplateErrors.Remove(publicTemplateId);
                else publicTemplateErrors[publicTemplateId] += 1;
            }
            else publicTemplateErrors.Add(publicTemplateId, retryCount + 1);

            Log.Warning(ex, "Failed to process publicTemplateId: {PublicTemplateId}; Retry count: {RetryCount}", publicTemplateId, retryCount);
        }
    }
}