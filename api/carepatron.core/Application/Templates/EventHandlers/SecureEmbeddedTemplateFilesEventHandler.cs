﻿using carepatron.core.Application.Notes.Models;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Events.Handlers;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Repositories.Files;
using carepatron.core.Repositories.SQS;
using carepatron.core.Repositories.Templates;
using Serilog;
using Serilog.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.core.Application.Templates.EventHandlers
{
    public class SecureEmbeddedTemplateFilesEventHandler : IEventHandler<EventData<SecureEmbeddedTemplateFilesEventData>>
    {
        private const int BatchSize = 100;

        private readonly ITemplateRepository templateRepository;
        private readonly ISqsRepository sqsRepository;
        private readonly IFileStorageRepository fileStorageRepository;
        private readonly ITemplateAttachmentRepository templateAttachmentRepository;

        public SecureEmbeddedTemplateFilesEventHandler(
            ITemplateRepository templateRepository,
            ISqsRepository sqsRepository,
            IFileStorageRepository fileStorageRepository,
            ITemplateAttachmentRepository templateAttachmentRepository)
        {
            this.templateRepository = templateRepository;
            this.sqsRepository = sqsRepository;
            this.fileStorageRepository = fileStorageRepository;
            this.templateAttachmentRepository = templateAttachmentRepository;
        }

        public EventType EventType => EventType.SecureEmbeddedNoteFiles;

        public async Task Handle(EventData<SecureEmbeddedTemplateFilesEventData> evt)
        {
            Log.Information("Beginning to run SecureEmbeddedTemplateFilesEventHandler");

            var data = evt?.Data;
            var errors = data?.Errors ?? new();
            var totalProcessed = data?.TotalProcessed ?? 0;
            if (data?.HasProcessExceeded ?? false)
                return;

            var templates = await templateRepository.GetTemplates(data.DateRange, data.TemplateIds, data.LastTemplateId, BatchSize, null);

            if (templates.Length <= 0) return;

            var templatesToProcess = new List<TemplateMeta>();

            if (errors.Count > 0)
            {
                var templatesToRetry = await templateRepository.GetTemplates(null, errors.Keys.ToArray(), null, errors.Count, null);

                templatesToProcess.AddRange(templatesToRetry);
            }

            templatesToProcess.AddRange(templates);

            foreach (var template in templatesToProcess)
            {
                using (LogContext.PushProperty("Template", template.Id))
                {
                    try
                    {
                        var templateAttachments = await templateAttachmentRepository.GetTemplateAttachments(template.ProviderId, template.Id);
                        foreach (var attachment in templateAttachments)
                        {
                            using (LogContext.PushProperty("Template Attachment", attachment.Id))
                            {
                                if (!await fileStorageRepository.IsFileSecured(attachment.Id.ToString()))
                                {
                                    await fileStorageRepository.SecureFile(attachment.Id.ToString());
                                    Log.ForContext("FileId", attachment.Id)
                                        .Information("File secured");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        HandleErrorCount(ex, errors, template.Id);
                    }
                }
            }

            Log.ForContext("SecureEmbeddedTemplateFilesEventHandler", templates.Length)
              .Information("Finished processing batch");

            var lastTemplateId = templatesToProcess.Last().Id;
            SecureEmbeddedTemplateFilesEventData secureEmbeddedTemplateFilesEventData = new(data.DateRange, data.TemplateIds, lastTemplateId, errors, totalProcessed + BatchSize, data?.MaxToProcess);
            await sqsRepository.SendMessage(QueueType.Task,
                new EventMessage(EventType.SecureEmbeddedTemplateFiles, new EventData<SecureEmbeddedTemplateFilesEventData>(secureEmbeddedTemplateFilesEventData), Guid.NewGuid().ToString(), null));
        }

        public void HandleErrorCount(Exception ex, Dictionary<Guid, int> templateErrors, Guid templateId)
        {
            if (templateErrors.TryGetValue(templateId, out int retryCount))
            {
                if (templateErrors[templateId] >= 3)
                    templateErrors.Remove(templateId);
                else templateErrors[templateId] += 1;
            }
            else templateErrors.Add(templateId, retryCount + 1);

            Log.Warning(ex, "Failed to process templateId: {TemplateId}; Retry count: {RetryCount}", templateId, retryCount);
        }
    }
}