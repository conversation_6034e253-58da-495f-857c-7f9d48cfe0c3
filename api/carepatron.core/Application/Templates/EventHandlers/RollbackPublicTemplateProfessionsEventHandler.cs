using System;
using System.Linq;
using carepatron.core.Abstractions;
using carepatron.core.Constants;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Events.Handlers;
using carepatron.core.Repositories.Templates;
using Serilog;
using Serilog.Context;
using System.Threading.Tasks;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Extensions;
using Microsoft.Extensions.Logging;

namespace carepatron.core.Application.Templates.EventHandlers;

public class RollbackPublicTemplateProfessionsEventHandler(
    ILogger<RollbackPublicTemplateProfessionsEventHandler> logger,
    ITempTemplateProfessionRepository tempTemplateProfessionRepository,
    IUnitOfWork unitOfWork) : IEventHandler<EventData<RollbackPublicTemplateProfessionsEventData>>
{
    public EventType EventType => EventType.RollbackPublicTemplateProfessions;


    public async Task Handle(EventData<RollbackPublicTemplateProfessionsEventData> evt)
    {
        LogContext.PushProperty(nameof(CodeOwner), CodeOwner.NotesAndDocuments);
        Log.Information("Starting rollback process for public template professions.");

        var data = evt.Data;

        if (data.PublicTemplateIds.IsNullOrEmpty())
        {
            Log.Warning("PublicTemplateIds is empty.");
            return;
        }

        var publicTemplates = await tempTemplateProfessionRepository.GetLivePublicTemplates(data.PublicTemplateIds);

        if (!publicTemplates.IsNullOrEmpty())
        {
            foreach (var publicTemplate in publicTemplates)
            {
                LogContext.PushProperty("PublicTemplateId", publicTemplate.Id);
                try
                {
                    // fetch backup 
                    var tempTemplateProfessions =
                        await tempTemplateProfessionRepository.GetTempPublicTemplateProfessions(publicTemplate.Id);

                    Log.Information("Rolling back template professions.");

                    var backupProfessions = tempTemplateProfessions.Select(x => x.Profession).ToArray();
                    
                    // set to backup professions
                    await tempTemplateProfessionRepository.UpdatePublicTemplateProfessions(publicTemplate.Id,
                        backupProfessions);

                    if (!data.DryRun)
                    {
                        await unitOfWork.SaveUnitOfWork();
                    }
                }
                catch (Exception e)
                {
                    logger.LogError(e, "Failed processing template professions rollback");
                    unitOfWork.UseUnitOfWork();
                }

                logger.LogInformation("Completed processing template professions rollback");
            }
        }
    }
}