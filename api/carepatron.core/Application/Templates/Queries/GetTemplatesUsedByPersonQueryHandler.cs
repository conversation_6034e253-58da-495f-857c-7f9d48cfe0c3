using carepatron.core.Application.Templates.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Templates;
using carepatron.core.Utilities;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Templates.Queries
{
    public class GetTemplatesUsedByPersonQueryHandler : IMediatrQueryHandler<GetTemplatesUsedByPersonQuery, TemplatesUsedByPersonResponse>
    {
        private readonly ITemplateRepository templateRepository;

        public GetTemplatesUsedByPersonQueryHandler(ITemplateRepository templateRepository)
        {
            this.templateRepository = templateRepository;
        }

        public async Task<ExecutionResult<TemplatesUsedByPersonResponse>> Handle(GetTemplatesUsedByPersonQuery query, CancellationToken cancellationToken)
        {
            var templatesUsedByPerson =
                await templateRepository.GetTemplatesUsedByPerson(query.IdentityContext.PersonId, query.IdentityContext.ProviderPermissions.ProviderId);

            var favs = templatesUsedByPerson?.Favourites ?? Array.Empty<Guid>();
            var recents = templatesUsedByPerson?.RecentlyUsed ?? Array.Empty<Guid>();

            var ids = favs.Union(recents).Distinct().ToArray();

            var templates = await templateRepository.GetMeta(ids, query.IdentityContext.ProviderPermissions.ProviderId);

            var recentlyUsed = templates.Where(x => recents.Contains(x.Id)).ToArray();
            var favourites = templates.Where(x => favs.Contains(x.Id)).ToArray();

            return new TemplatesUsedByPersonResponse
            {
                RecentlyUsed = recentlyUsed,
                Favourites = favourites
            };
        }
    }
}