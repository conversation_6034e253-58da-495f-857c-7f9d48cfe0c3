﻿using carepatron.core.Application.Registrations.Events;
using carepatron.core.Application.Registrations.Models;
using carepatron.core.Application.Users.Models;
using carepatron.core.Extensions;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Person;
using carepatron.core.Repositories.Registration;
using carepatron.core.Repositories.User;
using carepatron.core.Services;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Persons.Services;
using carepatron.core.Application.Workspace.Billing.Models;

namespace carepatron.core.Application.Registrations.Commands
{
    public class RegisterWorkspaceCommandHandler
        : IMediatrCommandHandler<RegisterWorkspaceCommand, PersonRegisteredWorkspace>
    {
        private readonly IRegisteredWorkspaceRepository registerWorkspaceRepository;
        private readonly IPersonRepository personRepository;
        private readonly IProviderProvisioningService providerProvisioningService;
        private readonly IUserRepository userRepository;
        private readonly IIntegrationEventPublisher eventPublisher;
        private readonly IPersonService personService;

        public RegisterWorkspaceCommandHandler(
            IRegisteredWorkspaceRepository registerWorkspaceRepository,
            IPersonRepository personRepository,
            IProviderProvisioningService providerProvisioningService,
            IUserRepository userRepository,
            IIntegrationEventPublisher eventPublisher,
            IPersonService personService
        )
        {
            this.registerWorkspaceRepository = registerWorkspaceRepository;
            this.personRepository = personRepository;
            this.providerProvisioningService = providerProvisioningService;
            this.userRepository = userRepository;
            this.eventPublisher = eventPublisher;
            this.personService = personService;
        }

        public async Task<ExecutionResult<PersonRegisteredWorkspace>> Handle(
            RegisterWorkspaceCommand request,
            CancellationToken cancellationToken
        )
        {
            var users = await userRepository.GetUsers(request.Email);
            if (users.Length <= 0)
                return ValidationError.NotFound;

            var person = await personRepository.GetByEmail(request.Email);
            Guid profileId = await userRepository.UpdateProfileOrNew(request.Email, person?.Id);
            var cognitoUser = users.FirstOrDefault(x => x.Status == "CONFIRMED") ?? users.First();

            if (person is null)
            {
                string firstName = request.FirstName;
                string lastName = request.LastName;

                if (!string.IsNullOrEmpty(request.Name))
                {
                    var name = request.Name.ToName(' ');
                    firstName = name.FirstName;
                    lastName = name.LastName;
                }

                person = new Person
                {
                    Id = profileId,
                    Email = request.Email, // email from token identity,
                    FirstName = firstName,
                    LastName = lastName,
                    PhoneNumber = request.PhoneNumber,
                    Sub = cognitoUser.Sub,
                    IsActive = true,
                    IsClient = false,
                    CreatedDateTimeUtc = DateTime.UtcNow,
                    UpdatedDateTimeUtc = DateTime.UtcNow
                };

                await personRepository.Create(person);
            }

            var businessName = request.BusinessName.ValueOrFallbackIfEmpty(request.Name);
            var billingAddress = request.BillingAddress is null
                ? null
                : new ProviderBillingSettingsAddress
                {
                    StreetAddress = request.BillingAddress.StreetAddress,
                    State = request.BillingAddress.State,
                    City = request.BillingAddress.City,
                    ZipCode = request.BillingAddress.ZipCode,
                    Country = request.BillingAddress.Country,
                    Province = request.BillingAddress.Province,
                };

            var provisioningResult = await providerProvisioningService.ProvisionProvider(
                businessName,
                request.CountryCode,
                person,
                request.TimeZone,
                request.Profession,
                request.PromotionCode,
                request.ReferralCode,
                request.Website,
                billingAddress,
                request.Locations,
                request.Services,
                personService.GetDefaultLocale(request.Locale),
                request.StaffSchedules,
                request.Logo,
                request.PrimaryColorHex,
                request.PartnerKey,
                request.ClickId
            );

            var newWorkspace = new RegisteredWorkspace()
            {
                ProviderId = provisioningResult.Provider.Id,
                ExploreFeatures = request.ExploreFeatures,
                Profession = request.Profession,
                TeamSize = request.TeamSize,
                ToolsUsed = request.ToolsUsed
            };

            await registerWorkspaceRepository.Create(newWorkspace);
            await personService.SavePersonalSetting(person.Id, request.TimeZone, request.Locale);


            eventPublisher.Add(
                new OutgoingEvent<PersonRegisteredEvent>(new(person, new EventInitiator(person.Id)))
            );

            return new PersonRegisteredWorkspace(person.Id, newWorkspace.ProviderId);
        }
    }
}