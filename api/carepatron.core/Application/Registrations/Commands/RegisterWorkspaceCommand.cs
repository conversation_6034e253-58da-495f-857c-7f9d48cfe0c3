﻿using carepatron.core.Application.Registrations.Models;
using carepatron.core.Application.Workspace.Models;
using carepatron.core.Models.Common;
using carepatron.core.Models.Media;
using carepatron.core.Pipeline.Abstractions;

namespace carepatron.core.Application.Registrations.Commands
{
    public record RegisterWorkspaceCommand(
        Email Email,
        string Name,
        string Profession,
        string TeamSize,
        string[] ExploreFeatures,
        string ToolsUsed,
        string CountryCode,
        string TimeZone,
        string PromotionCode,
        string Locale,
        string ReferralCode,
        string FirstName,
        string LastName,
        string BusinessName,
        string Website,
        Address BillingAddress,
        Address[] Locations,
        DefaultService[] Services,
        ProvisioningStaffSchedule[] StaffSchedules,
        ProviderLogo Logo,
        string PrimaryColorHex,
        string PartnerKey,
        string ClickId,
        string PhoneNumber) : IMediatrCommand<PersonRegisteredWorkspace>;
}