﻿using System.Linq;
using carepatron.core.Application.Trash.Services;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Notes;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Application.Notes.Commands
{
    public class DeleteNoteTranscriptionCommandHandler(
        INoteRepository noteRepository, 
        ITranscriptionRepository transcriptionRepository, 
        INoteTranscriptionRepository noteTranscriptionRepository,
        ITrashService trashService) : IMediatrCommandHandler<DeleteNoteTranscriptionCommand, Unit>
    {
        public async Task<ExecutionResult<Unit>> Handle(DeleteNoteTranscriptionCommand request, CancellationToken cancellationToken)
        {
            var note = await noteRepository.Get(request.NoteId);
            if (note == null)
            {
                return ValidationError.NotFound;
            }

            var transcription = await transcriptionRepository.GetById(request.TranscriptionId);

            if (transcription == null)
            {
                return ValidationError.NotFound;
            }

            var noteTranscription = await noteTranscriptionRepository.Get(request.NoteId, request.TranscriptionId, false);

            if (noteTranscription == null)
            {
                return ValidationError.NotFound;
            }

            var validTranscripts = transcription.Transcripts.Where(x => !x.IsInaudible).ToList();

            if (validTranscripts.Any())
            {
                await noteTranscriptionRepository.Delete(request.NoteId, request.TranscriptionId);
                await trashService.SaveTrashItem(noteTranscription, request.IdentityContext.PersonId);
            }
            else
            {
                await transcriptionRepository.Delete(transcription.Id);
                await noteTranscriptionRepository.HardDelete(request.NoteId, request.TranscriptionId);
            }

            return Unit.Value;
        }
    }
}
