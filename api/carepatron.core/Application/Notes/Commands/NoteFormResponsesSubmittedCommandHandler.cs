﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Notes.Commands;
using carepatron.core.Application.Notes.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Schema.Services;
using carepatron.core.Extensions;
using carepatron.core.Models.Common;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.Notes;
using carepatron.core.Repositories.Person;
using carepatron.core.Repositories.Provider;
using carepatron.core.Service;
using carepatron.core.Utilities;
using Newtonsoft.Json.Linq;
using Serilog;

namespace carepatron.core.Application.Notes.EventHandlers;

public class NoteFormResponsesSubmittedCommandHandler : IMediatrCommandHandler<NoteFormResponsesSubmittedCommand, Unit>
{
    private readonly IEmailService emailService;
    private readonly INoteRepository noteRepository;
    private readonly IPersonRepository personRepository;
    private readonly IProviderRepository providerRepository;
    private readonly ISchemaService schemaService;
    private readonly IContactRepository contactRepository;

    public NoteFormResponsesSubmittedCommandHandler(
        IEmailService emailService,
        INoteRepository noteRepository,
        IPersonRepository personRepository,
        IProviderRepository providerRepository,
        ISchemaService schemaService,
        IContactRepository contactRepository)
    {
        this.contactRepository = contactRepository;
        this.schemaService = schemaService;
        this.providerRepository = providerRepository;
        this.personRepository = personRepository;
        this.noteRepository = noteRepository;
        this.emailService = emailService;
    }

    public async Task<ExecutionResult<Unit>> Handle(NoteFormResponsesSubmittedCommand request, CancellationToken cancellationToken)
    {
        var note = await noteRepository.Get(request.NoteId);

        if (note == null)
        {
            Log.Warning("Note with id: {NoteId} not found. ", request.NoteId);
            return Unit.Value;
        }

        var provider = await providerRepository.GetProvider(note.ProviderId);
        if (provider == null)
        {
            Log.Warning("ProviderId with id: {ProviderId} not found. ", note.ProviderId);
            return Unit.Value;
        }

        var clientInfoFormFields = GetClientInfoIdsFromFormFields(note.Form.Fields).ToList();
        if (clientInfoFormFields.Count > 0)
        {
            await UpdateContact(note, clientInfoFormFields);
        }

        // Don't send email if the person who submitted the form is the same as the person who created the note
        if (note.CreatedByPersonId == request.PersonId)
            return Unit.Value;

        var responder = await personRepository.Get(request.PersonId);

        if (responder == null)
        {
            Log.Warning("PersonId with id: {PersonId} not found. ", request.PersonId);
            return Unit.Value;
        }

        await emailService.SendFormResponseSubmitted(provider, responder, note, responder.FullName, note.CreatedByPerson.Email,
            note.CreatedByPerson.FullName);

        return Unit.Value;
    }
    
    private async Task UpdateContact(NoteDetail note, IEnumerable<Guid> clientInfoIds)
    {
        var schema = await schemaService.GetDataSchema(SchemaTypes.ContactSchema, note.ProviderId);
        var propertiesToUpdate = GetPropertiesToUpdate(schema, note, clientInfoIds);

        if (propertiesToUpdate.Count == 0) return;

        var contact = await contactRepository.Get(note.ContactId);

        foreach (var property in propertiesToUpdate)
        {
            if (property.Value == null || property.Value.Type == JTokenType.Null)
            {
                Log.Warning("Property {PropertyKey} has null value for contact {ContactId}. Skipping update.", property.Key, note.ContactId);
                continue;
            }

            if (property.Key.Equals(nameof(contact.PhoneNumber)) &&
                ContactUtilities.TryParsePhoneInfo(property.Value, out var phoneNumberResult))
            {
                contact.PhoneNumbers = ContactUtilities.SetPrimary(contact.PhoneNumbers ?? Enumerable.Empty<IHasPrimary>(), phoneNumberResult.PhoneNumbers.FirstOrDefault())
                    .Select(x => (ContactPhone)x)
                    .ToArray();
                continue;
            }

            if (property.Key.Equals(nameof(contact.Address)) &&
                ContactUtilities.TryParseAddressInfo(property.Value.ToObject<Address>(), out var addressResult))
            {
                contact.Addresses = ContactUtilities.SetPrimary(contact.Addresses ?? Enumerable.Empty<IHasPrimary>(), addressResult.Addresses.FirstOrDefault())
                    .Select(x => (ContactAddress)x)
                    .ToArray();
                continue;
            }
            
            schema.SetPropertyValue(contact, property.Key, property.Value); 
        }

        await contactRepository.Update(contact);
    }

    private Dictionary<string, JToken> GetPropertiesToUpdate(MergedDataSchema schema, NoteDetail note, IEnumerable<Guid> clientInfoIds)
    {
        var propertiesToUpdate = new Dictionary<string, JToken>();

        const string valueKey = "value";
        const string defaultValuesKey = "defaultValues";
        const string clientFieldsKey = "clientFields";
        const string propertyKey = "property";
        
        foreach (var id in clientInfoIds)
        {
            if (!note.Form.Responses.TryGetValue(id, out var formResponses)) continue;
            if (!note.Form.Fields.TryGetValue(id, out var formFields)) continue;
            
            var responses = formResponses
                .OrderByDescending(x => x.CreatedDateTimeUtc)
                .Select(x => x.Response)
                .FirstOrDefault();
            
            var clientFields = formFields.Schema[clientFieldsKey];
            if (clientFields == null) 
                continue;

            var defaultValues = formFields.Schema[defaultValuesKey]!.ToObject<JObject>();
            foreach (var clientField in clientFields)
            {
                var clientFieldProperty = clientField[propertyKey]!.ToString();
                if (schema.GetProperty(clientFieldProperty) is null || string.Equals(clientFieldProperty, nameof(Contact.Email), StringComparison.OrdinalIgnoreCase))
                    continue;

                JToken propertyValue = null;

                var responseValueExists = responses != null && responses.TryGetValue(clientField[propertyKey]!.ToString(), out propertyValue);

                if (responseValueExists)
                {
                    propertiesToUpdate[clientField[propertyKey]!.ToString()] = propertyValue[valueKey];
                }
                else if(defaultValues.TryGetValue(clientField[propertyKey]!.ToString(), out var defaultValue))
                {
                    propertiesToUpdate[clientField[propertyKey]!.ToString()] = defaultValue![valueKey];
                }
            }
        }

        return propertiesToUpdate;
    }

    private static IEnumerable<Guid> GetClientInfoIdsFromFormFields(IDictionary<Guid, NoteFormField> formFields) =>
        formFields
            .Where(x => !x.Value.Deleted && x.Value.Type == FormFieldType.ClientInfo.ToString())
            .Select(x => x.Key);
}