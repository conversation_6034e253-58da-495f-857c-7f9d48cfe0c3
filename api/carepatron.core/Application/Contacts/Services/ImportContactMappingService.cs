using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Agents.Module;
using Agents.Module.AgentRunners;
using Agents.Module.Exceptions;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Agents;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Schema.Models.Properties;
using carepatron.core.Application.Schema.Services;
using carepatron.core.Extensions;
using carepatron.core.Repositories.Schema;
using carepatron.core.Services;
using Serilog;
using Serilog.Context;

namespace carepatron.core.Application.Contacts.Services;

public class ImportContactMappingService(
    IAgentClient agentClient,
    ISchemaService schemaService,
    ISchemaRepository schemaRepository,
    IImportContactsService importContactsService) : IImportContactMappingService
{
    private const string LayoutHeading = "Layout:";
    private const string FieldItemPrefix = "  - ";
    private const string EmptyContainerText = FieldItemPrefix + "Nothing";
    private static readonly IReadOnlyList<string> ExcludedLayoutFields = new[] { nameof(Contact.AssignedStaff) };
    private const int MaxRows = 100;

    public async Task<ImportContactMappingsResponse> GetMappings(Guid providerId, Guid fileId, string fileName)
    {
        var csvData = await importContactsService.ReadCsv(fileId, fileName, MaxRows);

        var distinctColumns = csvData.Columns.Distinct().ToArray();

        var defaultResponse = ImportContactMappingsResponse.Default(distinctColumns);

        if (string.IsNullOrWhiteSpace(csvData.CsvString)) return defaultResponse;

        var formattedLayout = await GetFormattedLayout(providerId);

        var instruction = ImportContactMappingAgent.GetSystemInstruction(formattedLayout);

        var response = await GetResponse(instruction, csvData.CsvString, distinctColumns);
        response.Mappings = response.Mappings.DistinctBy(x => x.CarepatronFieldName).ToArray();
        response.SuggestedCustomFields = response.SuggestedCustomFields.DistinctBy(x => x.ColumnMapping.CarepatronFieldName).ToArray();
        response.SuggestedLayoutContainers = response.SuggestedLayoutContainers.DistinctBy(x => x.Heading).ToArray();
        return response;
    }

    private async Task<string> GetFormattedLayout(Guid providerId)
    {
        var dataSchema = await schemaService.GetDataSchema(SchemaTypes.ContactSchema, providerId);

        var layoutSchema = await schemaRepository.GetLayoutSchema(SchemaTypes.ContactSchemaLayoutView, SchemaTypes.ContactSchema, providerId);

        var result = new StringBuilder();
        result.AppendLine(LayoutHeading);

        var containers = layoutSchema.Elements.OfType<LayoutContainer>();

        foreach (var container in containers)
        {
            result.AppendLine(container.Heading);

            var controls = container.Elements
                .OfType<LayoutControl>()
                .Where(control => !ExcludedLayoutFields.Contains(control.Property))
                .ToList();

            if (controls.IsNullOrEmpty())
            {
                result.AppendLine(EmptyContainerText);
                continue;
            }

            foreach (var control in controls)
            {
                var property = dataSchema.GetProperty(control.Property);
                var formattedProperty = PropertyToString(property);
                result.AppendLine($"{FieldItemPrefix}{control.Property}: {formattedProperty}");
            }
        }

        return result.ToString();
    }

    private string PropertyToString(Property property) => property switch
    {
        AddressProperty p => FormatProperty(p.Type.ToString(),
            FormatAttribute(nameof(p.Multiple), p.Multiple, false)),
        BooleanProperty p => FormatProperty(p.Type.ToString(),
            FormatAttribute(nameof(p.TrueDisplayName), p.TrueDisplayName),
            FormatAttribute(nameof(p.FalseDisplayName), p.FalseDisplayName)),
        DateProperty p => FormatProperty(p.Type.ToString(),
            FormatAttribute(nameof(p.ShowDateDiff), p.ShowDateDiff, false)),
        DateRangeProperty p => FormatProperty(p.Type.ToString(),
            FormatAttribute(nameof(p.ShowDateDiff), p.ShowDateDiff, false)),
        EmailProperty p => FormatProperty(p.Type.ToString(),
            FormatAttribute(nameof(p.Multiple), p.Multiple, false)),
        PhoneNumberProperty p => FormatProperty(p.Type.ToString(),
            FormatAttribute(nameof(p.Multiple), p.Multiple, false)),
        OptionSetV2Property p => FormatProperty(p.Type.ToString(),
            FormatAttribute(nameof(p.FreeSolo), p.FreeSolo, false),
            FormatAttribute(nameof(p.Multiple), p.Multiple, false),
            FormatAttribute(nameof(p.Options), FormatOptionSetValues(p.Options), false)),
        StringProperty p => FormatProperty(PropertyType.String.ToString(),
            FormatAttribute(nameof(p.IsLongText), p.IsLongText, false)),
        // Default case for any other property type not explicitly handled
        _ => FormatProperty(PropertyType.String.ToString(), FormatAttribute(nameof(StringProperty.IsLongText), false, false))
    };

    private string FormatOptionSetValues(IDictionary<string, OptionSetValue> options)
    {
        if (options == null || !options.Any()) return "[]";

        var formattedItems = options.Values
            .OrderBy(x => x.OrderIndex)
            .Select(x => FormatProperty(nameof(OptionSetValue),
                FormatAttribute(nameof(OptionSetValue.Id), x.Id),
                FormatAttribute(nameof(OptionSetValue.DisplayName), x.DisplayName),
                FormatAttribute(nameof(OptionSetValue.OrderIndex), x.OrderIndex, false)))
            .ToArray();

        return $"[{string.Join(",", formattedItems)}]";
    }

    private string FormatAttribute(string attribute, object value, bool withQuotes = true)
    {
        var formattedValue = withQuotes ? $"\"{value}\"" : value.ToString();
        return $"{attribute}={formattedValue}";
    }

    private string FormatProperty(string property, params string[] attributes)
    {
        var formattedAttributes = string.Join(",", attributes);
        return $"{property}({formattedAttributes})";
    }

    private async Task<ImportContactMappingsResponse> GetResponse(string instruction, string csvContent, string[] columns)
    {
        var result = await agentClient.Run(
            new ImportContactMappingAgent(instruction),
            new UserInput(Role.User, new TextUserInputPart(csvContent)));

        if (result.Error is OutputSchemaException)
        {
            Log.Warning("Failed to generate import contacts mapping: {Error}", result.Error.Message);
            return ImportContactMappingsResponse.Default(columns);
        }

        ImportContactMappingsResponse response = null;

        using (LogContext.PushProperty("GetImportContactMappings", new { result.Output }, true))
        {
            try
            {
                response = ImportContactMappingAgent.Parse(result.Output);

                if (response == null)
                {
                    Log.Warning("Null response from import contacts mapping agent");
                }
            }
            catch (Exception e)
            {
                Log.Warning("Failed to parse import contacts mapping: {Error}", e.Message);
            }
        }

        if (response == null) response = ImportContactMappingsResponse.Default(columns);
        else response.ColumnNames = columns;

        return response;
    }
}