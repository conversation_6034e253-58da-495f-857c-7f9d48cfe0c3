﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Schema.Services;
using carepatron.core.Extensions;
using carepatron.core.Models.Media;
using carepatron.core.Models.Permissions;
using carepatron.core.Repositories.Files;
using CsvHelper;
using CsvHelper.Configuration;

namespace carepatron.core.Application.Contacts.Services;

public interface IExportContactService
{
    Task<Guid> Export(Guid providerId,
        Guid personId,
        StandardPermission permission,
        bool isClient,
        BulkContactFilter bulkFilter,
        Guid[] contactIds);
}

public class ExportContactService(IFileStorageRepository fileStorageRepository,
    IContactService contactService,
    ISchemaService schemaService) : IExportContactService
{
    public async Task<Guid> Export(Guid providerId,
        Guid personId,
        StandardPermission permission,
        bool isClient,
        BulkContactFilter bulkFilter,
        Guid[] contactIds)
    {
        var contacts = await GetContacts(providerId, personId, permission, isClient, bulkFilter, contactIds);
        if (contacts is null || !contacts.Any())
            throw new Exception("No contacts found to export");

        var schema = await schemaService.GetDataSchema(SchemaTypes.ContactSchema, providerId);
        var layoutSchema = await schemaService.GetLayoutSchema(SchemaTypes.ContactSchemaLayoutView, SchemaTypes.ContactSchema, providerId);
        var csvItems = MapContactsToCsvItems(contacts, schema, layoutSchema);
        using var content = await WriteCsv(csvItems);
        using var zipped = ZipFiles((content, "carepatron-clients.csv"));
        var fileId = await UploadFile(zipped, "carepatron-clients.zip");
        return fileId;
    }

    private async Task<Contact[]> GetContacts(Guid providerId,
        Guid personId,
        StandardPermission permission,
        bool isClient,
        BulkContactFilter bulkFilter,
        Guid[] contactIds)
    {
        Contact[] contacts = Array.Empty<Contact>();

        if (bulkFilter is not null && bulkFilter.AllClients)
        {
            contacts = await contactService.GetContacts(providerId,
                  bulkFilter.SearchTerm,
                  bulkFilter.Tag,
                  bulkFilter.AssignedStaff,
                  bulkFilter.IsUnassigned,
                  bulkFilter.Statuses,
                  bulkFilter.IsArchived,
                  personId,
                  permission,
                  bulkFilter.ExcludedContactIds,
                  isClient);
        }
        else if (contactIds is not null && contactIds.Any())
        {
            contacts = await contactService.GetContacts(providerId, contactIds);
        }

        return contacts;
    }

    private List<Dictionary<string, object>> MapContactsToCsvItems(Contact[] contacts, MergedDataSchema schema, LayoutSchema layoutSchema)
    {
        var contactSchemaFieldValues = schema.GetSchemaFieldValues(contacts);
        var displayNames = new Dictionary<string, string>();

        foreach (var prop in schema.Properties.Where(p => p.Key != "Fields"))
        {
            var controlProperty = prop.Value as ControlProperty;
            if (controlProperty is null) continue;
            displayNames[prop.Key] = controlProperty.DisplayName;
        }

        foreach (var prop in schema.GetCustomFields())
        {
            var controlProperty = prop.Value as ControlProperty;
            if (controlProperty is null) continue;
            displayNames[prop.Key] = $"{controlProperty.DisplayName}".GenerateUniqueString(displayNames.Values.ToArray());
        }

        var csvItems = new List<Dictionary<string, object>>();
        foreach (var contact in contactSchemaFieldValues)
        {
            var contactEntry = new Dictionary<string, object>();
            contactEntry.Add("Id", contact.Key);

            HashSet<string> includedFields = [];
            var contactValueMap = contact.Value.ToDictionary(x => x.Key, x => x.Value);

            // add fields base on layout schema's order
            foreach (var layoutContainer in layoutSchema.Elements.OfType<LayoutContainer>())
            {
                foreach (var layoutControl in layoutContainer.Elements.OfType<LayoutControl>())
                {
                    if (contactValueMap.TryGetValue(layoutControl.Property, out var schemaFieldValue) &&
                        displayNames.TryGetValue(layoutControl.Property, out var resolvedDisplayName))
                    {
                        contactEntry[resolvedDisplayName] = schemaFieldValue;
                        includedFields.Add(layoutControl.Property);
                    }
                }
            }

            // add fields that are not in the layout schema
            var hiddenFields = contactValueMap.Keys
                .Where(key => !includedFields.Contains(key))
                .ToArray();

            foreach (var fieldKey in hiddenFields)
            {
                if (displayNames.TryGetValue(fieldKey, out var resolvedDisplayName))
                {
                    contactEntry[resolvedDisplayName] = contactValueMap[fieldKey];
                }
            }

            csvItems.Add(contactEntry);
        }

        return csvItems;
    }

    private async Task<MemoryStream> WriteCsv(List<Dictionary<string, object>> values)
    {
        var config = new CsvConfiguration(CultureInfo.InvariantCulture)
        {
            ShouldQuote = (args) => args.FieldType == typeof(string),
            Encoding = System.Text.Encoding.UTF8,
        };

        var memoryStream = new MemoryStream();
        using (var writer = new StreamWriter(memoryStream, Encoding.UTF8, leaveOpen: true))
        using (var csvWriter = new CsvWriter(writer, config))
        {
            if (values.Count > 0)
            {
                foreach (var key in values[0].Keys)
                {
                    csvWriter.WriteField(key);
                }
                csvWriter.NextRecord();

                foreach (var record in values)
                {
                    foreach (var value in record.Values)
                    {
                        csvWriter.WriteField(value);
                    }
                    csvWriter.NextRecord();
                }
            }
        }
        memoryStream.Position = 0;
        return memoryStream;
    }

    private MemoryStream ZipFiles(params (MemoryStream steam, string fileName)[] files)
    {
        var zipMemoryStream = new MemoryStream();
        using (var zip = new ZipArchive(zipMemoryStream, ZipArchiveMode.Create, true))
        {
            foreach (var file in files)
            {
                var zipEntry = zip.CreateEntry(file.fileName);
                using (var entryStream = zipEntry.Open())
                {
                    file.steam.CopyTo(entryStream);
                }
            }
        }
        zipMemoryStream.Position = 0;
        return zipMemoryStream;
    }

    private async Task<Guid> UploadFile(MemoryStream stream, string zipFileName)
    {
        var file = new UploadableFile { Bytes = stream.ToArray(), FileName = zipFileName, ContentType = "application/zip" };
        var result = await fileStorageRepository.Upload(FileLocationType.ClientExport, new[] { file });
        return result[0].Id;
    }
}