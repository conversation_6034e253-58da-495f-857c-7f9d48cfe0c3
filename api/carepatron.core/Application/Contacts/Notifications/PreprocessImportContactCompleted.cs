using System;
using carepatron.core.Application.Contacts.Models;
using Notifications.Sdk.Client;
using Notifications.Sdk.Client.Abstract;

namespace carepatron.core.Application.Contacts.Notifications;

[NotificationsContent(NotificationCategoryRequest.CONTACT_IMPORT, nameof(PreprocessImportContactCompleted))]
public class PreprocessImportContactCompleted : INotificationsContent
{
    public Guid Id { get; set; }
    public Guid ProviderId { get; set; }
    public Guid FileId { get; set; }
    public string FileName { get; set; }
    public long FileSize { get; set; }
    public string FileExtension { get; set; }
    public string Status { get; set; }
    public Guid? SchemaFileId { get; set; }
    public Guid[] LastStatusSeenBy { get; set; }
    public Guid CreatedByPersonId { get; set; }
    public DateTime CreatedDateTimeUtc { get; set; }
    public DateTime? UpdatedDateTime { get; set; }
    public DateTime? CompletedDateTimeUtc { get; set; }

    public static implicit operator PreprocessImportContactCompleted(ContactImportSummary importSummary) => new()
    {
        Id = importSummary.Id,
        ProviderId = importSummary.ProviderId,
        FileId = importSummary.FileId,
        FileName = importSummary.FileName,
        FileSize = importSummary.FileSize,
        FileExtension = importSummary.FileExtension,
        Status = importSummary.Status.ToString(),
        SchemaFileId = importSummary.SchemaFileId,
        LastStatusSeenBy = importSummary.LastStatusSeenBy,
        CreatedByPersonId = importSummary.CreatedByPersonId,
        CreatedDateTimeUtc = importSummary.CreatedDateTimeUtc,
        UpdatedDateTime = importSummary.UpdatedDateTime,
        CompletedDateTimeUtc = importSummary.CompletedDateTimeUtc,
    };
}