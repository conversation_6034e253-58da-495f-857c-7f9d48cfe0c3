using System;
using System.Collections.Generic;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Pipeline.Abstractions;

namespace carepatron.core.Application.Contacts.Commands
{
    public record ContactImportCommand(Guid ProviderId,
        Guid PersonId,
        ExternalContactImportSource ImportSource,
        Guid ImportFileId,
        string FileName,
        string FileExtension,
        long FileSize,
        List<ImportContactsOption> MappedColumns,
        DataSchema DataSchema,
        ImportSummaryStatus? Status,
        ImportType? ImportType,
        bool IsContact
    ) : IMediatrCommand<ContactImportSummary>;
}