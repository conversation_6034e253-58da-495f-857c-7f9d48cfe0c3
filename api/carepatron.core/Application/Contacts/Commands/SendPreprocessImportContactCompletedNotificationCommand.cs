using System;
using System.Threading.Tasks;
using carepatron.core.Application.Communications.Notifications.Abstractions;
using carepatron.core.Application.Communications.Notifications.Models;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Notifications;
using carepatron.core.Pipeline.Abstractions;
using Notifications.Sdk.Client.Abstract;

namespace carepatron.core.Application.Contacts.Commands;

public record SendPreprocessImportContactCompletedNotificationCommand(Guid ContactImportSummaryId) : IMediatrCommand<Unit>;

public class SendPreprocessImportContactCompletedNotificationCommandHandler(
    IContactImportSummaryRepository contactImportSummaryRepository,
    INotificationsService notificationsService) : NotificationsCommandHandler<SendPreprocessImportContactCompletedNotificationCommand, Unit, PreprocessImportContactCompleted>(notificationsService)
{
    protected override async Task<NotificationContext<PreprocessImportContactCompleted>> GetNotificationContext(SendPreprocessImportContactCompletedNotificationCommand command)
    {
        var importSummary = await contactImportSummaryRepository.GetById(command.ContactImportSummaryId);

        if (importSummary is null) return null;

        return new NotificationContext<PreprocessImportContactCompleted>()
        {
            ProviderId = importSummary.ProviderId,
            ActorId = importSummary.CreatedByPersonId.ToString(),
            NotificationsContent = importSummary,
            RecipientPersonIds = [importSummary.CreatedByPersonId]
        };
    }
}