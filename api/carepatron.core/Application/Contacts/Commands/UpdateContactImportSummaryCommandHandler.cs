using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Constants;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Repositories.SQS;

namespace carepatron.core.Application.Contacts.Commands
{
    public class UpdateContactImportSummaryCommandHandler(
        IContactImportSummaryRepository contactImportSummaryRepository,
        IUnitOfWork unitOfWork,
        ISqsRepository sqsRepository) : IMediatrCommandHandler<UpdateContactImportSummaryCommand, ContactImportSummary>
    {
        public async Task<ExecutionResult<ContactImportSummary>> Handle(UpdateContactImportSummaryCommand request, CancellationToken cancellationToken)
        {
            var contactImportSummary = await contactImportSummaryRepository.GetById(request.ContactImportSummaryId);
            if (contactImportSummary is null)
                return new ValidationError(Errors.NotFoundErrorCode, Errors.NotFoundErrorDetails, ValidationType.NotFound);

            contactImportSummary.LastStatusSeenBy = request.LastStatusSeenBy;

            bool shouldStartImport = false;

            if (request.Status.HasValue)
            {
                ImportSummaryStatus[] cancellableStatuses =
                {
                    ImportSummaryStatus.Pending,
                    ImportSummaryStatus.Draft,
                    ImportSummaryStatus.ReadyForMapping
                };
                if (request.Status is ImportSummaryStatus.Cancelled && !cancellableStatuses.Contains(contactImportSummary.Status))
                {
                    return new ValidationError(Errors.UnableToCancelContactImportSummaryCode, string.Format(Errors.UnableToCancelContactImportSummaryDetailsFormat, contactImportSummary.Status), ValidationType.BadRequest);
                }

                shouldStartImport = contactImportSummary.Status is ImportSummaryStatus.ReadyForMapping && request.Status is ImportSummaryStatus.Pending;
                contactImportSummary.Status = request.Status.Value;
            }

            await contactImportSummaryRepository.Update(contactImportSummary);

            await unitOfWork.SaveUnitOfWork();

            if (shouldStartImport)
                await StartImport(contactImportSummary, request);

            return contactImportSummary;
        }

        private async Task StartImport(ContactImportSummary contactImportSummary, UpdateContactImportSummaryCommand request)
        {
            var eventData = new EventData<ImportContactsFromCsvData>(new ImportContactsFromCsvData(
                contactImportSummary.ProviderId,
                contactImportSummary.Id,
                request.PersonId,
                request.MappedColumns,
                null));

            await sqsRepository.SendMessage(QueueType.Task, new EventMessage(EventType.ImportContactsFromAiCsv, eventData, contactImportSummary.Id.ToString(), request.PersonId), true);
        }
    }
}