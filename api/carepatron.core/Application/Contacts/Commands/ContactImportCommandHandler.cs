using System;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Models.Execution;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Repositories.SQS;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Events;

namespace carepatron.core.Application.Contacts.Commands
{
    public class ContactImportCommandHandler : IMediatrCommandHandler<ContactImportCommand, ContactImportSummary>
    {
        private readonly IContactImportSummaryRepository contactImportSummaryRepository;
        private readonly ISqsRepository sqsRepository;
        private readonly IUnitOfWork unitOfWork;

        public ContactImportCommandHandler(IContactImportSummaryRepository contactImportSummaryRepository, ISqsRepository sqsRepository, IUnitOfWork unitOfWork)
        {
            this.contactImportSummaryRepository = contactImportSummaryRepository;
            this.sqsRepository = sqsRepository;
            this.unitOfWork = unitOfWork;
        }

        public async Task<ExecutionResult<ContactImportSummary>> Handle(ContactImportCommand command, CancellationToken cancellationToken)
        {

            var contactImportSummary = new ContactImportSummary
            {
                Id = Guid.NewGuid(),
                ProviderId = command.ProviderId,
                CreatedByPersonId = command.PersonId,
                CreatedDateTimeUtc = DateTime.UtcNow,
                FileId = command.ImportFileId,
                OriginalFileName = command.FileName,
                FileName = command.FileName,
                Status = command.Status ?? ImportSummaryStatus.Pending,
                ImportType = command.ImportType ?? ImportType.Standard,
                FileExtension = command.FileExtension,
                FileSize = command.FileSize,
                IsContact = command.IsContact
            };
            await contactImportSummaryRepository.Create(contactImportSummary);
            await unitOfWork.SaveUnitOfWork();

            var eventMessage = BuildEventMessage(command, contactImportSummary);
            await sqsRepository.SendMessage(QueueType.Task, eventMessage, true);

            return contactImportSummary;
        }

        private EventMessage BuildEventMessage(ContactImportCommand command, ContactImportSummary contactImportSummary)
        {
            switch (command.ImportSource)
            {
                case ExternalContactImportSource.CSV:
                    {
                        var eventData = new EventData<ImportContactsFromCsvData>(new ImportContactsFromCsvData(
                            contactImportSummary.ProviderId,
                            contactImportSummary.Id,
                            command.PersonId,
                            command.MappedColumns,
                            command.DataSchema));

                        return new EventMessage(EventType.ImportContactsFromCsv, eventData, contactImportSummary.Id.ToString(), command.PersonId);
                    }
                case ExternalContactImportSource.AI_CSV:
                {
                    var eventData = new EventData<ImportContactsFromCsvData>(new ImportContactsFromCsvData(
                        contactImportSummary.ProviderId,
                        contactImportSummary.Id,
                        command.PersonId,
                        command.MappedColumns,
                        null));

                    return new EventMessage(EventType.ImportContactsFromAiCsv, eventData, contactImportSummary.Id.ToString(), command.PersonId);
                }
                case ExternalContactImportSource.AI_SimplePractice:
                case ExternalContactImportSource.AI_AthenaHealth:
                {
                    var eventData = new EventData<PreprocessImportContactsEvent>(new PreprocessImportContactsEvent(contactImportSummary.Id, command.ImportSource));
                    
                    return new EventMessage(EventType.PreprocessImportContacts, eventData, contactImportSummary.Id.ToString(), command.PersonId);
                }
                case ExternalContactImportSource.SimplePractice:
                default:
                    {
                        var eventData = new EventData<ImportExternalContactData>(new ImportExternalContactData(
                            contactImportSummary.FileId,
                            contactImportSummary.ProviderId,
                            command.PersonId,
                            command.ImportSource,
                            contactImportSummary.Id));

                        return new EventMessage(EventType.ImportExternalContactData, eventData, contactImportSummary.Id.ToString(), command.PersonId);
                    }
            }
        }
    }
}
