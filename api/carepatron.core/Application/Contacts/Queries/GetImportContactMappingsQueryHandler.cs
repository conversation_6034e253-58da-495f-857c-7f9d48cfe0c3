using System;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Pipeline.Abstractions;

namespace carepatron.core.Application.Contacts.Queries;

public record GetImportContactMappingsQuery(Guid ProviderId, Guid FileId, string FileName) : IMediatrQuery<ImportContactMappingsResponse>;

public class GetImportContactMappingsQueryHandler(IImportContactMappingService importContactMappingService) : IMediatrQueryHandler<GetImportContactMappingsQuery, ImportContactMappingsResponse>
{
    public async Task<ExecutionResult<ImportContactMappingsResponse>> Handle(GetImportContactMappingsQuery request, CancellationToken cancellationToken)
    {
        return await importContactMappingService.GetMappings(request.ProviderId, request.FileId, request.FileName);
    }
}