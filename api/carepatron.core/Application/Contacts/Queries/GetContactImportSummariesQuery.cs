using System;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Models.Pagination;
using carepatron.core.Pipeline.Abstractions;

namespace carepatron.core.Application.Contacts.Queries
{
    public record GetContactImportSummariesQuery(
        Guid ProviderId,
        DateTime? FromDate,
        DateTime? ToDate,
        ImportSummaryStatus[] Statuses,
        bool IsContact) : IMediatrQuery<CollectionResult<ContactImportSummary>>;
}
