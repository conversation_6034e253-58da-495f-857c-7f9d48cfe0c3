using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Pagination;
using carepatron.core.Pipeline.Abstractions;
using carepatron.core.Application.Contacts.Abstractions;

namespace carepatron.core.Application.Contacts.Queries
{
    public class GetContactImportSummariesQueryHandler(IContactImportSummaryRepository importSummaryRepository) : IMediatrQueryHandler<GetContactImportSummariesQuery, CollectionResult<ContactImportSummary>>
    {
        public async Task<ExecutionResult<CollectionResult<ContactImportSummary>>> Handle(GetContactImportSummariesQuery request, CancellationToken cancellationToken)
        {
            var summaries = await importSummaryRepository.Get(request.ProviderId, 
                request.FromDate, 
                request.ToDate, 
                request.Statuses,
                request.IsContact);

            return summaries;
        }
    }
}