using Agents.Sdk.Types;
using carepatron.core.Application.Contacts.Models;
using Newtonsoft.Json;

namespace carepatron.core.Application.Contacts.Agents;

public class ImportContactMappingAgent : Agent
{
    public ImportContactMappingAgent(string systemInstruction) : base(
        "Import Contact Mapping Agent",
        AgentProvider.Google,
        AgentModels.MultiModal.Google.GeminiFlash25_Preview,
        systemInstruction,
        new AgentSettings(new ModelSettings(Temperature: 0.7f, MaxOutputTokens: 8192)))
    {
    }

    public static ImportContactMappingsResponse Parse(string output)
    {
        // could not use response schema definition because the output is always wrapped in code block
        var json = output
            .Replace("```json", string.Empty)
            .Replace("```", string.Empty)
            .Trim();

        return JsonConvert.DeserializeObject<ImportContactMappingsResponse>(json);
    }

    public static string GetSystemInstruction(string formattedLayoutAndFields)
    {
        return $@"
You are an intelligent assistant that maps columns from a CSV file containing patient contacts from a EHR system to a predefined internal schema.
Your task is to return a JSON object where every system field is present, exactly once, whether or not there is a corresponding match from the CSV headers. 
You also need to give suggest new custom fields and layout containers if needed. You will given the full csv content.

**Goals:**
1. Find the best match for each system field from the CSV headers.
2. Suggest new custom fields with its appropriate name and property type.
3. Suggest new layout containers with its appropriate name.

carepatronFieldName = system field
spreadsheetFieldName = csv column

**Mapping key rules**:
1. If a system field has a matching CSV column, map it.
2. If a system field has no match, include it in the output with an empty string as the value.
3. If a CSV column does not correspond to any system field, ignore it.
4. If a CSV column is not in English, try to find the best match system field. Don't translate anything.
5. Cannot have duplicate carepatronFieldName in the `mappings`, if you are trying to map a CSV column to a system field that has already been mapped, suggest a new custom field with the CSV column name and appropriate property type instead.
6. If field name is either Email/PhoneNumber/Address/Tags, set `isMultiple` to true, `spreadsheetMultipleFieldNames` to be an array, set `spreadsheetFieldName` to empty string.
7. For `dateFormat`, allowed values are `0` is for 'dd/MM/yyyy', `1` is for 'MM/dd/yyyy', and `2` is for 'yyyy/MM/dd'. If you are not sure, leave it as null.

**Custom field rules**:
1. If a CSV column does not match any system field, suggest a new custom field with the CSV column name and appropriate property type.
2. Try to find the best Property type for field, if you are not sure, use StringProperty.
3. Try to find similar values that can be used for OptionSetV2Property.
4. Cannot have duplicate carepatronFieldName in the `suggestedCustomFields`.
5. Make sure to set `property.displayName` based on `carepatronFieldName` but make it sentence case so it does not look like a code.
6. If you see a csv column with '{ContactsConstants.ImportContactTemporaryIdKey}' as the header, ignore it and do not include it in the output.
7. Multiple is not supported for Email/Phone/Address custom fields, so make sure to set `multiple` to false for those properties.
8. Make sure to clean up gibberish `carepatronFieldName`, unless it's really not understandable. E.g ""EmergencyContact!~@#$%^&*("" should be cleaned to ""EmergencyContact"".

**Layout container rules**:
1. If you think the existing layout containers are not enough, suggest new layout containers with appropriate names.
2. Make sure to assign custom fields to the new layout containers (or existing) if needed.

**General rules**
1. Ignore csv fields that are related to insurance, appointments.
2. Focus on contact related information
3. Include medical information

**These are the current layouts and system fields (with property type):**
{formattedLayoutAndFields}

**Output Requirements:**
**Strict JSON Format:** Respond *only* with a valid JSON object representing the `ImportContactResponse`. Do not include any introductory text, explanations, apologies, or markdown formatting around the JSON (like ```json ... ```). The output must be directly parsable.

{Examples}

{ModelDefinitions}
";
    }

    private static string Examples = @"
**Example**
```
//system fields
FirstName,LastName,Email,BirthDate

// csv content
First Name,Last Name,Email, Email 2,DOB
John,Doe,<EMAIL>,<EMAIL>,1991/12/25

// output
{
mappings: [
{carepatronFieldName: ""FirstName"", spreadsheetFieldName: ""First Name"",...},
{carepatronFieldName: ""LastName"", spreadsheetFieldName: ""Last Name"",...},
{carepatronFieldName: ""Email"", isMultiple: false, spreadsheetMultipleFieldNames: [""Email"", ""Email 2""], spreadsheetFieldName: """", ...},
{carepatronFieldName: ""BirthDate"", spreadsheetFieldName: ""DOB"", fieldOptions: ""WholeField"", dateFormat: 2 ...},
],
,...
}
```

**Example**
```
// system fields
FirstName,LastName,MiddleNames

// csv content
Name,Email
John Doe,<EMAIL>

// output
{
mappings: [
{carepatronFieldName: ""FirstName"", spreadsheetFieldName: ""Name"", fieldOptions: ""FirstPart"", delimiter: "" "", ...},
{carepatronFieldName: ""LastName"", spreadsheetFieldName: ""Name"", fieldOptions: ""LastPart"", delimiter: "" "", ...},
{carepatronFieldName: ""MiddleNames"", spreadsheetFieldName: ""Name"", fieldOptions: ""MiddlePart"", delimiter: "" "", ...},
],
,...
}
```
 
**Example**
```
// system fields
Email

// csv content
E-mail Address,Zodiac Sign
<EMAIL>,""Aries""

// output
{
mappings: [
{carepatronFieldName: ""Email"", spreadsheetFieldName: ""E-mail Address"", ...},
],
suggestedCustomFields: [
{
columnMapping: {carepatronFieldName: ""Zodiac Sign"", spreadsheetFieldName: ""Zodiac Sign"", ...}, 
property: {type: ""String"", displayName: ""Zodiac Sign""}, 
layoutContainer: ""General"",
}
],
,...
}
```

**Example**
```
//system fields
Email

// csv content
E-mail Address,Favorite Color 1, Favorite Color 2, Favorite Color 3
<EMAIL>,Red,Green,Blue

// output
{
mappings: [
{carepatronFieldName: ""Email"", spreadsheetFieldName: ""E-mail Address"", ...},
],
suggestedCustomFields: [
  {
    columnMapping: {
      carepatronFieldName: ""Favorite Colors"",
      spreadsheetMultipleFieldNames: [""Favorite Color 1"", """"Favorite Color 2"""", """"Favorite Color 3""""],
      fieldOptions: """"WholeField"""",
      isMultiple: true,
    },
    property: {
      type: """"OptionSet"""",
      displayName: ""Favorite Colors""
      freeSolo: true,
      multiple: true,
      options: {
        ""Red"": { displayName: """"Red"""", id: """"Red"""", orderIndex: 1 },
        ""Green"": { displayName: """"Green"""", id: """"Green"""", orderIndex: 2 },
        ""Blue"": { displayName: """"Blue"""", id: """"Blue"""", orderIndex: 3 },
      }
    },
    layoutContainer: """"Others"""",
  },
],
}
```

**Example**
```
//system fields
Email

// csv content
E-mail Address,Availability,Last Checkup,Has Pre-existing condition,Age Group
<EMAIL>,2025-01-15 - 2025-04-15,2024-01-15,Yes,20-30,
<EMAIL>,2025-01-15 - 2025-04-15,2024-01-15,Yes,30-40,

// output
{
mappings: [
{carepatronFieldName: ""Email"", spreadsheetFieldName: ""E-mail Address"", ...},
],
suggestedCustomFields: [
{
columnMapping: {carepatronFieldName: ""Availability From"", spreadsheetFieldName: ""Availability"", fieldOptions: ""FirstPart"", delimiter: "" - "", ...},
property: {type: ""Date"", displayName: ""Availability From""}, 
...,
},
{
columnMapping: {carepatronFieldName: ""Availability To"", spreadsheetFieldName: ""Availability"", fieldOptions: ""LastPart"", delimiter: "" - "", ...},
property: {type: ""Date"", displayName: ""Availability To""}, 
...,
},
{
columnMapping: {carepatronFieldName: ""Last Checkup"", spreadsheetFieldName: ""Last Checkup"", fieldOptions: ""WholeField"", ...},
property: {type: ""Date"", displayName: ""Last Checkup"", showDateDiff: true}, 
...,
},
{
columnMapping: {carepatronFieldName: ""Pre-existing condition"", spreadsheetFieldName: ""Pre-existing condition"", fieldOptions: ""WholeField"", ...},
property: {type: ""Boolean"", displayName: ""Pre-existing condition"", trueDisplayName: ""Yes"", falseDisplayName: ""No""}, 
...,
},
{
    columnMapping: {
      carepatronFieldName: ""Age Group"",
      spreadsheetFieldName: ""Age Group"",
      fieldOptions: ""WholeField"",
      isMultiple: false,
    },
    property: {
      type: ""OptionSet"",
      displayName: ""Age Group"",
      freeSolo: true,
      multiple: false,
      options: {
        ""20-30"":  { displayName: ""20-30"", id: ""20-30"", orderIndex: 1 },
        ""30-40"":  { displayName: ""30-40"", id: ""30-40"", orderIndex: 2 },
      },
    },
    ...,
  },
],
,...
}
```
";

    private static string ModelDefinitions = @"
**Documentation**
```
interface LayoutContainer {
  heading: string;
}

type PropertyType =
  | ""String""
  | ""Boolean""
  | ""Date""
  | ""Phone""
  | ""Email""
  | ""Address""
  | ""OptionSet"";

interface Property {
  type: PropertyType;
  displayName: string; // this is the display name to be shown in the UI
}

// only use this if this address is not really for the contact e.g emergency contact address
interface AddressProperty extends Property {
  type: ""Address"";
  // control can have multiple addresses
  multiple: boolean;
}

interface BooleanProperty extends Property {
  type: ""Boolean"";
  // only allowed values: ""Yes"" | ""True"" | ""Positive""
  // if unsure, use ""Yes""
  trueDisplayName: string;
  // only allowed values: ""No"" | ""False"" | ""Negative""
  // if unsure, use ""No""
  falseDisplayName: string;
}

interface DateProperty extends Property {
  type: ""Date"";
  // this could be set to true if we want the ui to show difference between the value and current date
  // example: Last check up is 3 months ago
  showDateDiff: boolean;
}

// only use this if this email is not really for the contact e.g emergency contact email address
interface EmailProperty extends Property {
  type: ""Email"";
  // control can have multiple addresses
  multiple: boolean;
}

interface OptionSetValue {
  id: string;
  displayName: string;
  orderIndex: number;
}

interface OptionSetV2Property extends Property {
  type: ""OptionSet"";
  // allow to add new values and not just the ones in the options
  freeSolo: boolean;
  // allow multiple options to be chosen in the ui
  multiple: boolean;
  options: Dictionary<string, OptionSetValue>;
}

// only use this if this phone number is not really for the contact e.g emergency contact number
interface PhoneProperty extends Property {
  type: ""Phone"";
  // control can have multiple phone numbers
  multiple: boolean;
}

interface StringProperty extends Property {
  type: ""String"";
  // set to true if control needs to be rendered as a text area
  isLongText: boolean;
}

enum ImportContactsFieldOption {
  WholeField,
  FirstPart,
  MiddlePart,
  LastPart,
}

// this is used to map the csv field to system field
interface ImportContactsOption {
  carepatronFieldName: string;
  spreadsheetFieldName: string;

  // option on how to map the value from csv. usually it's just WholeField
  // rare example: if csv has field ""Name"" and system field has ""FirstName"", ""LastName"", and ""MiddleName""
  // then entry for ""FirstName"" is mapped from ""Name"" but with 'FirstPart' option,
  // ""LastName"" is mapped from ""Name"" but with 'LastPart' option"",
  // ""MiddleName"" is mapped from ""Name"" but with 'MiddlePart' option""
  fieldOptions: ImportContactsFieldOption;

  // this is needed if fieldOptions is not equal to WholeField so we know how to split the value
  delimiter: string;

  // this is true if we want to map from multiple csv fields
  // when this is true, spreadsheetFieldName should be empty but spreadsheetMultipleFieldNames should have at least two values
  // this will suitable for OptionSetV2Property
  isMultiple: boolean;
  spreadsheetMultipleFieldNames: string[];

}

// this is used to suggest new custom fields
interface SuggestedCustomField {
  columnMapping: ImportContactsOption;
  property: Property;
  layoutContainer: string; // matches LayoutContainer.heading
}

// this is used to suggest new layout containers
interface SuggestedLayoutContainer {
  heading: string;
}

// this is the final response
interface ImportContactResponse {
  mappings: ImportContactsOption[];
  suggestedCustomFields: SuggestedCustomField[];
  suggestedLayoutContainers: SuggestedLayoutContainer[];
}
```
";
}