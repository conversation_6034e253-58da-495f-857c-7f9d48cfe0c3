﻿using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Authorization.Models;
using carepatron.core.Models.Common;
using carepatron.core.Models.Pagination;
using carepatron.core.Models.Permissions;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace carepatron.core.Application.Contacts.Abstractions
{
    public interface IContactRepository
    {
        Task<Contact> Create(Contact contact);

        Task<Contact[]> Create(Contact[] contacts);

        Task Delete(Guid id);

        Task<Relationship> CreateRelationship(Relationship relationship);

        Task<Relationship[]> CreateRelationships(Relationship[] relationships);

        Task<SimpleContact> GetReference(Guid id);

        Task<Contact> Get(Guid id);
        Task<Contact> Get(Guid id, bool includeSoftDeleted);
        Task<ContactPerson> GetContactPerson(Guid providerId, Guid contactId);

        Task<Contact[]> Get(Guid[] ids);

        Task<Contact[]> GetByPersonId(Guid personId);
        Task<bool> IsClientOfProvider(Guid personId);

        Task<PaginatedResult<Contact>> GetContacts(Guid providerId,
            string searchTerm,
            bool? isClient,
            Guid[] tags,
            Guid[] assignedStaff,
            bool isUnassigned,
            string[] statuses,
            bool isArchived,
            Guid currentPersonId,
            StandardPermission clientProfilesViewPermission,
            PaginationRequest paginationRequest,
            Sorting sorting);

        Task<Contact[]> GetContacts(
            Guid providerId,
            string searchTerm,
            Guid[] tag,
            Guid[] assignedStaff,
        bool isUnassigned,
            string[] statuses,
            bool isArchived,
            Guid currentPersonId,
            StandardPermission clientProfilesViewPermission,
            Guid[] excludedContactIds,
            bool? isClient);

        Task<Contact[]> GetContacts(Guid providerId, Guid[] contactIds);

        Task<Contact> Update(Contact contact);

        /// <summary>
        /// Updates a contact with the relationship contact details, which is a subset of contact details.
        /// </summary>
        /// <param name="contact"></param>
        /// <returns></returns>
        Task<Contact> Update(RelationshipContactDetails contact);

        Task DeleteRelationship(Guid contactId, Guid toContactId);

        Task<Contact[]> GetByEmail(Email email, Guid? providerId = null);

        Task<Relationship> Save(Relationship relationship);

        /// <summary>
        /// Gets all contacts that have a relationship to this contact
        /// </summary>
        /// <param name="contactId"></param>
        /// <returns></returns>
        Task<Relationship[]> GetRelationshipsToContact(Guid toContactId);

        /// <summary>
        /// Gets all contacts that this contact has relationshpis to
        /// </summary>
        /// <param name="contactId"></param>
        /// <returns></returns>
        Task<Relationship[]> GetRelationshipsForContact(Guid contactId);

        /// <summary>
        /// Gets all contacts that these contacts have relationshpis to
        /// </summary>
        /// <param name="contactId"></param>
        Task<Relationship[]> GetRelationshipsForContacts(Guid[] contactIds);

        Task<Relationship> GetRelationship(Guid contactId, Guid toContactId);

        Task DeleteRelationships((Guid ContactId, Guid ToContactId)[] contactIdPairs);

        Task BulkAssignStaffToContacts(Guid providerId, Guid[] contactIds, Guid[] newStaffIds);

        Task<Guid[]> BulkAssignStaffToContacts(
            Guid providerId,
            string searchTerm,
            Guid[] tag,
            Guid[] assignedStaff,
            bool isUnassigned,
            string[] statuses,
            Guid currentPersonId,
            StandardPermission clientProfilesViewPermission,
            Guid[] excludedContactIds,
            bool? isClient,
            Guid[] newStaffIds);

        Task<Contact[]> BulkUnassignStaffToContacts(Guid[] contactIds, Guid[] staffIds);

        Task BulkAddTagsToContacts(Guid providerId, Guid[] contactIds, Guid[] newTagIds);
        Task BulkAddTagsToContacts(
            Guid providerId,
            string searchTerm,
            bool? isClient,
            Guid[] tag,
            Guid[] assignedStaff,
            bool isUnassigned,
            string[] statuses,
            Guid currentPersonId,
            StandardPermission clientProfilesViewPermission,
            Guid[] excludedContactIds,
            Guid[] newTagIds);
        Task BulkUpdateStatusOfContacts(Guid providerId, Guid[] contactIds, string newStatus);
        Task BulkUpdateStatusOfContacts(
            Guid providerId,
            string searchTerm,
            Guid[] tag,
            Guid[] assignedStaff,
            bool isUnassigned,
            string[] statuses,
            Guid currentPersonId,
            StandardPermission clientProfilesViewPermission,
            Guid[] excludedContactIds,
            bool? isClient,
            string newStatus);

        Task BulkCreate(List<Contact> contactsToCreate);

        Task<Contact[]> BulkDelete(Guid providerId, Guid[] contactIds, bool hardDelete);
        Task<Contact[]> BulkDelete(
            Guid providerId,
            string searchTerm,
            Guid[] tag,
            Guid[] assignedStaff,
            bool isUnassigned,
            string[] statuses,
            Guid currentPersonId,
            StandardPermission clientProfilesViewPermission,
            Guid[] excludedContactIds,
            bool? isClient,
            bool hardDelete);

        Task<Contact> GetByPersonIdAndProviderId(Guid personId, Guid providerId);

        Task<Contact[]> GetByPersonIdAndProviderIds(Guid personId, Guid[] providerIds);

        Task<Guid[]> GetAssignedContactIds(Guid providerId, Guid staffPersonId);

        Task<Dictionary<Guid, Guid[]>> GetAssignedContactIds(Guid providerId, Guid[] staffPersonIds);

        Task<StatusCountArggregate[]> AggregateContactStatusCount(Guid providerId, string[] statuses);

        Task UpdateContactsByStatus(string oldStatus, string newStatus, Guid providerId);

        Task<ResourceName> GetResourceName(Guid id);

        Task<Contact[]> GetByEmailAddressOrPhoneNumber(Guid providerId, Email emailAddress, string phoneNumber);
        Task<Relationship[]> GetRelationshipsAndReverseRelationships(Guid contactId, Guid[] toContactIds);
        Task<Contact[]> Get(Guid providerId, Guid[] ids);
        Task<Dictionary<Guid, Contact>> GetByIds(Guid providerId, params Guid[] contactIds);
        Task Restore(Guid providerId, Guid contactId);
        Task HardDelete(Guid id, Guid contactId);
        Task<ContactSettings> GetContactSettings(Guid id);
        Task<CollectionResult<ContactDuplicate>> GetContactDuplicates(Guid providerId);
        Task Delete(Guid providerId, Guid[] ids);
        Task<DuplicateContact[]> GetPotentialDuplicatesByEmail(Guid providerId, Email email);
        Task<DuplicateContact[]> GetDuplicates(Guid providerId, Guid[] contactIds);
        Task AddDuplicates(DuplicateContact[] newDuplicateContacts);
        Task<DuplicateContact[]> GetDuplicatesByEmail(Guid providerId, Email email);
        Task DeleteDuplicates(Guid providerId, DuplicateContact[] duplicatesToDelete);

        /// <summary>
        /// Gets the dictionary of contact settings by contact id for the given contact ids
        /// </summary>
        /// <param name="contactIds"></param>
        /// <returns></returns>
        Task<Dictionary<Guid, ContactSettings>> GetContactSettings(Guid[] contactIds);
        Task<Contact[]> BulkArchive(Guid providerId, Guid[] contactIds, bool isArchived);

        Task<ExternalContact[]> GetExternalContacts(Guid providerId, Email[] emails);
        Task<ExternalContact[]> GetExternalContactsByContactId(Guid providerId, Guid contactId);
        Task<ExternalContact> GetExternalContact(Guid providerId, Guid id);
        Task BulkCreateExternalContacts(ExternalContact[] externalContacts);
        Task LinkExternalContact(Guid externalContactId, Guid contactId);
        Task BulkUnlinkExternalContacts(Guid providerId, Guid[] externalContactIds);
        
        Task<Contact[]> GetClientsByEmails(Guid providerId, Email[] emails, bool checkOtherEmails = false);
    }
}