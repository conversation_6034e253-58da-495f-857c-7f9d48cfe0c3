using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Models.Pagination;

namespace carepatron.core.Application.Contacts.Abstractions
{
    public interface IContactImportSummaryRepository
    {
        Task<ContactImportSummary> GetById(Guid id);
        Task Create(ContactImportSummary contactImportSummary);
        Task Update(ContactImportSummary contactImportSummary);
        Task<CollectionResult<ContactImportSummary>> Get(Guid providerId,
            DateTime? fromDate,
            DateTime? toDate,
            ImportSummaryStatus[] statuses,
            bool isContact);
    }
}
