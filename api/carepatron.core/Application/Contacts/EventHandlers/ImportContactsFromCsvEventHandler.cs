﻿using System;
using System.Threading.Tasks;
using carepatron.core.Abstractions;
using carepatron.core.Application.Communications.Notifications.Models;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Events.Handlers;
using carepatron.core.Services;
using Microsoft.Extensions.Logging;
using Notifications.Sdk.Client.Abstract;

namespace carepatron.core.Application.Contacts.EventHandlers;

public class ImportContactsFromCsvEventHandler(
    ILogger<ImportContactsFromCsvEventHandler> logger,
    IContactImportSummaryRepository contactImportSummaryRepository,
    IImportContactsService importContactsService,
    IUnitOfWork unitOfWork,
    INotificationsService notificationsService) : IEventHandler<EventData<ImportContactsFromCsvData>>
{
    private const int MaxRows = 100000;

    public EventType EventType => EventType.ImportContactsFromCsv;

    public async Task Handle(EventData<ImportContactsFromCsvData> evt)
    {
        var contactImportSummaryId = evt?.Data?.ContactImportSummaryId;
        if (!contactImportSummaryId.HasValue)
            return;
        try
        {
            unitOfWork.UseUnitOfWork();

            var importSummary = await contactImportSummaryRepository.GetById(
                contactImportSummaryId.Value
            );
            if (importSummary is null)
                return;

            if (importSummary.Status == ImportSummaryStatus.Successful)
                return;

            await UpdateImportSummaryStatus(contactImportSummaryId, ImportSummaryStatus.Running);
            await unitOfWork.SaveUnitOfWork();

            var eventData = evt.Data;
            var (_, rows) = await importContactsService.GetCsvData(importSummary.FileId, importSummary.FileName, MaxRows);

            if (rows is null || rows.Count == 0)
            {
                await UpdateImportSummaryStatus(
                    contactImportSummaryId,
                    ImportSummaryStatus.Successful
                );
                logger.LogWarning(
                    $"No rows found in the file for contact import summary {contactImportSummaryId}"
                );
                return;
            }

            var dataSchema = await importContactsService.UpdateDataSchemaAndLayout(
                eventData.DataSchema,
                eventData.ProviderId);

            var isClient = !importSummary.IsContact;
            var mappedImport = await importContactsService.MapColumnsToContacts(
                rows,
                eventData.ProviderId,
                dataSchema,
                eventData.MappedColumns,
                isClient: isClient
            );
            await importContactsService.SaveMappedContacts(eventData.ProviderId, importSummary, mappedImport, isClient);

            await UpdateImportSummaryStatus(contactImportSummaryId, ImportSummaryStatus.Successful);
        }
        catch (Exception ex)
        {
            await UpdateImportSummaryStatus(contactImportSummaryId, ImportSummaryStatus.Failed);
            logger.LogError(
                ex,
                $"Error occurred when importing data for contact import summary {contactImportSummaryId}"
            );

            // Publish a notification to the user
            await notificationsService.Send(evt.Data.ProviderId, SystemActors.ImportJobActorId, new ImportContactFailedNotificationContent(), [evt.Data.PersonId]);
        }
        finally
        {
            await unitOfWork.SaveUnitOfWork();
        }
    }

    public async Task UpdateImportSummaryStatus(Guid? id, ImportSummaryStatus status)
    {
        if (!id.HasValue)
            return;
        var importSummary = await contactImportSummaryRepository.GetById(id.Value);

        if (importSummary is null)
            return;

        importSummary.Status = status;
        importSummary.UpdatedDateTime = DateTime.UtcNow;
        if (status is ImportSummaryStatus.Successful or ImportSummaryStatus.Failed)
            importSummary.CompletedDateTimeUtc = DateTime.UtcNow;

        await contactImportSummaryRepository.Update(importSummary);
    }
}