using System;
using System.Text;
using System.Threading.Tasks;
using carepatron.core.Abstractions;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Contacts.Notifications;
using carepatron.core.Application.Files.Models;
using carepatron.core.Constants;
using carepatron.core.Events;
using carepatron.core.Events.Attributes;
using carepatron.core.Events.Events;
using carepatron.core.Events.Handlers;
using carepatron.core.IntegrationEvents.Abstractions;
using carepatron.core.IntegrationEvents.Models;
using carepatron.core.Repositories.Files;
using Newtonsoft.Json;
using Notifications.Sdk.Client.Abstract;
using Serilog;
using Serilog.Context;

namespace carepatron.core.Application.Contacts.EventHandlers;

[EventTypeHandler(EventType.PreprocessImportContacts)]
public class PreprocessImportContactsEventHandler(
    IImportContactPreprocessorFactory importContactPreprocessorFactory,
    INotificationsService notificationsService,
    IContactImportSummaryRepository contactImportSummaryRepository,
    IUnitOfWork unitOfWork,
    IImportContactMappingService importContactMappingService,
    IFileStorageRepository fileStorageRepository,
    IIntegrationEventPublisher integrationEventPublisher) : IEventHandler
{
    public EventType EventType => EventType.PreprocessImportContacts;

    public async Task Handle(EventMessage evt)
    {
        LogContext.PushProperty(nameof(CodeOwner), CodeOwner.Clients);

        unitOfWork.UseUnitOfWork();

        var evtMessageData = evt?.As<EventData<PreprocessImportContactsEvent>>()?.Data;
        if (evtMessageData == null)
        {
            Log.Warning("No data found in event");
            return;
        }

        var preprocessor = importContactPreprocessorFactory.Get(evtMessageData.ImportSource);

        var importSummary = await contactImportSummaryRepository.GetById(evtMessageData.ImportSummaryId);
        if (importSummary is null)
        {
            Log.Warning("No import summary found with id {ImportSummaryId}", evtMessageData.ImportSummaryId);
            return;
        }

        if (importSummary.Status is not ImportSummaryStatus.Draft)
        {
            Log.Warning("Import summary with id {ImportSummaryId} is not in draft status", evtMessageData.ImportSummaryId);
            return;
        }

        importSummary.Status = ImportSummaryStatus.Preprocessing;
        await contactImportSummaryRepository.Update(importSummary);
        await unitOfWork.SaveUnitOfWork();
        
        var zipFileId = importSummary.FileId;

        try
        {
            var csvFileId = await preprocessor.Process(evtMessageData.ImportSummaryId, zipFileId);
            var csvFileName = $"{csvFileId}.csv";
            
            var mappingsResponse = await importContactMappingService.GetMappings(
                importSummary.ProviderId,
                csvFileId,
                csvFileName
            );
            
            var schemaFileId = await UploadMappingsAsJsonFile(mappingsResponse);

            // replace zip file with csv file
            importSummary.FileId = csvFileId;
            importSummary.FileName = csvFileName;
            importSummary.FileExtension = "csv";
            importSummary.SchemaFileId = schemaFileId;
            importSummary.Status = ImportSummaryStatus.ReadyForMapping;
        }
        catch (Exception e)
        {
            importSummary.Status = ImportSummaryStatus.Failed;
            Log.Error(e, "Error occurred while preprocessing import summary {ImportSummaryId}", evtMessageData.ImportSummaryId);
        }
        finally
        {
            // if having issues with cleanup when testing locally, comment this out.
            await preprocessor.Cleanup(evtMessageData.ImportSummaryId, zipFileId);

            await contactImportSummaryRepository.Update(importSummary);
            await unitOfWork.SaveUnitOfWork();

            Log.Information("Import summary {ImportSummaryId} has been preprocessed", evtMessageData.ImportSummaryId);
        }

        await notificationsService.Notify(importSummary.ProviderId, [importSummary.CreatedByPersonId], (PreprocessImportContactCompleted)importSummary);

        await integrationEventPublisher.Publish(new OutgoingEvent<PreprocessImportContactCompletedEvent>(new(new(importSummary.Id))));
    }

    private async Task<Guid> UploadMappingsAsJsonFile(ImportContactMappingsResponse mappingsResponse)
    {
        var json = JsonConvert.SerializeObject(mappingsResponse);

        var fileId = Guid.NewGuid();

        await fileStorageRepository.Upload(
            new()
            {
                FileKey = fileId.ToString(),
                Bytes = Encoding.UTF8.GetBytes(json),
                FileName = $"{fileId}-mappings.json",
                ContentType = "application/json"
            },
            FileLocationType.ClientImport);

        return fileId;
    }
}