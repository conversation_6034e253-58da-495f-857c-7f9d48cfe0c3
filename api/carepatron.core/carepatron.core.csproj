﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateDocumentationFile>True</GenerateDocumentationFile>
  </PropertyGroup>
  <PropertyGroup>
	<NoWarn>SKEXP0040;SKEXP0050;SKEXP0070;SKEXP0110</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Application\Communications\Notifications\Models\Sms\**" />
    <EmbeddedResource Remove="Application\Communications\Notifications\Models\Sms\**" />
    <None Remove="Application\Communications\Notifications\Models\Sms\**" />
    <None Update="Application\Templates\Resources\carepatron_public_template_profession_author_mapping.csv">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="30.0.1" />
    <PackageReference Include="DnsClient" Version="1.8.0" />
    <PackageReference Include="FluentValidation" Version="11.2.2" />
    <PackageReference Include="Fluid.Core" Version="2.2.16" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.64" />
    <PackageReference Include="Ical.Net" Version="4.2.0" />
    <PackageReference Include="MediatR" Version="10.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Localization.Abstractions" Version="8.0.1" />
    <PackageReference Include="MimeTypes" Version="2.4.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
    <PackageReference Include="NodaTime" Version="3.1.9" />
    <PackageReference Include="ExcelDataReader" Version="3.6.0" />
    <PackageReference Include="ExcelDataReader.DataSet" Version="3.6.0" />
    <PackageReference Include="OneOf" Version="3.0.271" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="8.0.0" />
    <PackageReference Include="FuzzySharp" Version="2.0.2" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="libphonenumber-csharp" Version="8.12.34" />
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="HtmlSanitizer" Version="8.0.843" />
	  <PackageReference Include="LinqKit" Version="1.3.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="../Documentation/RichTextEditor.Module/RichText.Module.csproj" />
    <ProjectReference Include="..\Agents\Modules\Agents.Module\Agents.Module.csproj" />
    <ProjectReference Include="..\Notifications\Notifications.Sdk.Client\Notifications.Sdk.Client.csproj" />
    <ProjectReference Include="..\Notifications\Notifications.Sdk.Contracts\Notifications.Sdk.Contracts.csproj" />
    <ProjectReference Include="..\Utilities\FileUtilities\FileUtilities.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Localisation\EmailMessages\Emails.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Emails.resx</DependentUpon>
    </Compile>
    <Compile Update="Localisation\ErrorMessages\Errors.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Errors.resx</DependentUpon>
    </Compile>
    <Compile Update="Localisation\SchemaDisplayNames\SchemaDisplayNames.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>SchemaDisplayNames.resx</DependentUpon>
    </Compile>
    <Compile Update="Localisation\SharedMessages\SharedMessages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>SharedMessages.resx</DependentUpon>
    </Compile>
    <Compile Update="Localisation\SmsMessages\SmsMessages.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>SmsMessages.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.fil.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.pt-BR.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.fr.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.de.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.th.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.tr.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.ja.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.ar-SA.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.en-US.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.da.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.fi.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.he.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.hi.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.id.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.it.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.ko.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.ko.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.ms.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.nl.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.no.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.pl.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.sv.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.vi.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.zh-CN.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.zh-Hant.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\EmailMessages\Emails.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Emails.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\ErrorMessages\Errors.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Errors.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\SchemaDisplayNames\SchemaDisplayNames.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SchemaDisplayNames.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Update="Localisation\SharedMessages\SharedMessages.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>SharedMessages.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Update="Application\Communications\Inbox\Resources\DefaultInboxSignatureSettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Application\Communications\Inbox\Resources\DefaultInboxSignatureTemplate.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Application/AiAssistant/Commands/" />
  </ItemGroup>
</Project>