﻿using carepatron.core.Models.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using carepatron.core.Models.Scheduling;

namespace carepatron.core.Extensions
{
    public static class DateRangeExtensions
    {
        public static IEnumerable<DateRange> ToTimeZoneSpecific(this IEnumerable<DateRange> dateRanges, string timeZone, DateTimeKind kind)
        {
            foreach (var dateRange in dateRanges)
                yield return dateRange.ToTimeZoneSpecific(timeZone, kind);
        }

        public static TimeSpan AsTimeSpan(this DateRange dateRange) => dateRange.ToDate - dateRange.FromDate;

        public static List<DateOnly> GetBusinessDays(this DateRange dateRange)
        {
            List<DateOnly> result = new();
            for (int i = 0; i < dateRange.AsTimeSpan().TotalDays + 1; i++)
            {
                var current = dateRange.FromDate.AddDays(i);

                if (current.DayOfWeek != DayOfWeek.Saturday && current.DayOfWeek != DayOfWeek.Sunday)
                    result.Add(current.ToDateOnly());
            }
            return result;
        }

        public static List<DateOnly> GetDaysRange(this DateRange dateRange, string timezone)
        {
            return Enumerable.Range(0, 1 + (int)dateRange.AsTimeSpan().TotalDays)
                .Select(x => dateRange.FromDate.AddDays(x)
                             .ToTimeZoneDateTime(timezone)
                             .ToDateOnly())
                .ToList();
        }

        public static List<DateRange> GetDateRangeIntervals(this DateRange dateRange, int interval)
        {
            List<DateRange> result = new();
            if (interval <= 0) return [];
            
            var current = dateRange.FromDate;

            for (int i = 0; i < (int)dateRange.AsTimeSpan().TotalMinutes / interval; i++)
            {
                result.Add(new DateRange(current, current.AddMinutes(interval)));
                current = current.AddMinutes(interval);
            }

            return result;
        }

        /// <summary>
        /// Get list of DateRange based on monthly interval
        /// eg: Date range of Jan 10 - May 10 will return
        /// Jan 10 - Feb 10; Feb 10 - Mar 10; Mar 10 - Apr 10; Apr 10 - May 10;
        /// </summary>
        /// <param name="dateRange"></param>
        /// <param name="numOfMonth"></param>
        /// <returns></returns>
        public static List<DateRange> GetDateRangeMonthlyInterval(this DateRange dateRange, int numOfMonth = 1)
        {
            if (dateRange is null) return new();

            List<DateRange> result = new();
            var current = dateRange.FromDate;
            var endDate = dateRange.ToDate.ToEndOfDay();

            var firstRange = new DateRange(current, current.AddMonths(numOfMonth).ToEndOfDay());
            result.Add(firstRange);
            current = firstRange.ToDate;

            while (current < endDate)
            {
                var nextMonth = current.AddMonths(numOfMonth).ToEndOfDay();
                result.Add(new(current.ToStartOfDay(), nextMonth));
                current = nextMonth;
            }

            return result;
        }

        public static bool IsOverlappingDate(this DateRange dateRange, DateTime fromDate, DateTime toDate)
        {
            return (dateRange.FromDate < toDate && fromDate < dateRange.ToDate) || IsWithinDateRange(dateRange, fromDate, toDate);
        }

        public static bool IsOverlappingDate(this DateRange dateRange, DateRange lookupDateRange)
        {
            return IsOverlappingDate(dateRange, lookupDateRange.FromDate, lookupDateRange.ToDate);
        }

        public static bool IsWithinDateRange(this DateRange dateRange, DateTime currentDate)
        {
            return dateRange.FromDate <= currentDate && currentDate <= dateRange.ToDate;
        }

        public static bool IsWithinDateRange(this DateRange dateRange, DateTime fromDate, DateTime toDate)
        {
            return dateRange.FromDate <= fromDate && toDate <= dateRange.ToDate;
        }

        public static bool IsWithinDateRange(this DateRange dateRange, DateRange lookupDateRange)
        {
            return IsWithinDateRange(dateRange, lookupDateRange.FromDate, lookupDateRange.ToDate);
        }

        public static DateRange GetNextRange(this DateRange dateRange) =>
            new DateRange(dateRange.ToDate, dateRange.ToDate.AddMilliseconds(dateRange.AsTimeSpan().TotalMilliseconds));
        
        public static ConflictTimeBlock ToConflictTimeBlock(this DateRange dateRange, string timeZone, bool isConflict = false)
        {
            var zonedDateRange = dateRange.ToTimeZoneSpecific(timeZone, DateTimeKind.Unspecified);
            return new ConflictTimeBlock()
            {
                Start = zonedDateRange.FromDate.ToTimeOnly(),
                End = zonedDateRange.ToDate.ToTimeOnly(),
                IsConflict = isConflict
            };
        }
        
        public static List<DateRange> Intersect(params List<DateRange>[] dateRanges)
        {
            if (dateRanges.IsNullOrEmpty()) return [];
            if (dateRanges.Length == 1) return dateRanges[0];
        
            var current = dateRanges[0];
            for (int i = 1; i < dateRanges.Length; i++)
            {
                var instance = dateRanges[i];
                // skip if instance is empty
                if (!instance.Any()) continue;
                
                current = current.Intersect(instance).ToList();
            }
                 

            return current;
        }
    }
}
