﻿using carepatron.core.Models.Common;
using Ganss.Xss;
using PhoneNumbers;
using Serilog;
using System;
using System.Globalization;
using System.Linq;
using System.Net.Mail;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace carepatron.core.Extensions
{
    public static class StringExtensions
    {
        private static Regex removePhoneNumberRegex = new Regex(@"\d+|\+");

        /// <summary>
        /// Includes DATE-AND-OR-TIME formats defined in vCard specification.
        /// See <a href="https://tools.ietf.org/html/rfc6350#section-4.3.4">https://tools.ietf.org/html/rfc6350#section-4.3.4</a>
        /// </summary>
        private readonly static string[] acceptedDateTimeFormats = new[]
        {
            "o",
            "s",
            "yyyyMMddTHHmmss",
            "yyyyMMdd",
            "yyyy-MM-ddTHH:mm:ss",
            "yyyy-MM-dd",
            "yyyy-MM",
            "yyyy"
        };

        public static DateTime? ToDateTime(this string dateTime, DateTimeKind? kind = null)
        {
            if (string.IsNullOrWhiteSpace(dateTime))
                return null;

            if (
                DateTime.TryParseExact(
                    dateTime,
                    acceptedDateTimeFormats,
                    CultureInfo.InvariantCulture,
                    DateTimeStyles.None,
                    out var date
                )
            )
            {
                if (kind.HasValue)
                {
                    date = DateTime.SpecifyKind(date, kind.Value);
                }
                return date;
            }

            return null;
        }

        public static string ToCamelCase(this string input)
        {
            return string.IsNullOrWhiteSpace(input)
              ? input
              : char.ToLowerInvariant(input[0]) + input.Substring(1);
        }

        public static string GetInitial(this string input)
        {
            return string.IsNullOrWhiteSpace(input) ? string.Empty : input[0].ToString();
        }

        public static string ToSnakeCase(this string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                return input;
            }

            var startUnderscores = Regex.Match(input, @"^_+");
            return startUnderscores + Regex.Replace(input, @"([a-z0-9])([A-Z])", "$1_$2").ToLower();
        }

        public static bool IsValidColorHex(this string color)
        {
            if (string.IsNullOrEmpty(color))
            {
                return false;
            }

            if (!Regex.IsMatch(color, "^#[0-9A-Fa-f]{6}$"))
                return false;

            return true;
        }

        /// <summary>
        /// Used to remove a the client name from a api generated title like "Bob - Standard Appointment"
        /// Which becomes "Standard Appointment"
        /// This is only for Client events because the UI displays it in that way
        /// Relies on TaskProfile.GetTitle
        /// </summary>
        /// <param name="title"></param>
        /// <param name="delimiter"></param>
        /// <param name="isClientAppointment"></param>
        /// <returns></returns>
        public static string CleanTitleForClientEvent(
            this string title,
            string delimiter,
            bool isClientAppointment
        )
        {
            if (string.IsNullOrWhiteSpace(title) || !isClientAppointment)
            {
                return title;
            }

            var delimiterIndex = title.IndexOf(delimiter);

            if (delimiterIndex == -1)
            {
                return "Appointment";
            }

            return title.Substring(delimiterIndex + 1).Trim();
        }

        public static string TryCleanPhoneNumber(this string phoneNumber, string countryCode)
        {
            if (!string.IsNullOrWhiteSpace(countryCode))
            {
                try
                {
                    var phoneNumberUtil = PhoneNumberUtil.GetInstance();
                    var cleanedPhoneNumber = phoneNumberUtil.Parse(phoneNumber, countryCode);

                    if (phoneNumberUtil.IsValidNumber(cleanedPhoneNumber))
                    {
                        return $"+{cleanedPhoneNumber.CountryCode}{cleanedPhoneNumber.NationalNumber}";
                    }
                }
                catch (Exception)
                {
                    // do nothing
                }
            }

            return phoneNumber;
        }

        public static string RemoveInvalidCharsFromPhoneNumber(this string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return phoneNumber;

            return string.Join(
                string.Empty,
                removePhoneNumberRegex
                    .Matches(phoneNumber)
                    .OfType<Match>()
                    .Select(m => m.Groups[0].Value)
                    .ToArray()
            );
        }

        public static string TruncateLongString(this string str, int maxLength)
        {
            if (string.IsNullOrEmpty(str))
                return str;

            return str.Substring(0, Math.Min(str.Length, maxLength));
        }

        public static bool IsValidEmail(this string emailaddress)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(emailaddress))
                    return false;

                MailAddress m = new MailAddress(emailaddress);

                var email = Email.ParseOrDefault(emailaddress);
                if (email is null)
                    return false;

                return true;
            }
            catch (FormatException)
            {
                return false;
            }
        }

        public static bool IsNullOrWhiteSpace(this string str)
        {
            return string.IsNullOrWhiteSpace(str);
        }

        public static string ValueOrFallbackIfEmpty(this string str, string fallback)
        {
            return string.IsNullOrEmpty(str) ? fallback : str;
        }

        public static Email ToEmailOrNull(this string emailAddress)
        {
            return Email.ParseOrDefault(emailAddress);
        }

        public static string ToBase64Encode(this string plainText)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(plainText);
            return System.Convert.ToBase64String(plainTextBytes);
        }

        public static string TidyJoin(this string[] strings, string separator)
        {
            return string.Join(separator, strings.Where(s => !string.IsNullOrWhiteSpace(s)));
        }

        /// <summary>
        /// Checks whether the first 12 chars of a guid are valid
        /// </summary>
        /// <param name="guidPart"></param>
        /// <returns></returns>
        public static bool IsStarting12HexCharsGuid(this string guidPart)
        {
            if (string.IsNullOrWhiteSpace(guidPart))
                return false;

            return new Regex("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}$").IsMatch(guidPart);
        }

        /// <summary>
        /// creates a Name object based on a specified separator
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        public static Name ToName(this string name, char separator)
        {
            if (string.IsNullOrEmpty(name))
                return null;

            var nameSplit = name.Split(separator);
            var firstName = nameSplit[0];
            var lastNameArray = nameSplit.Where((x, index) => index > 0).ToArray();
            var lastName =
                lastNameArray.Length > 0 ? string.Join(" ", lastNameArray) : string.Empty;

            return new(firstName, lastName);
        }

        /// <summary>
        /// Creates a GUID from the hashed bytes of a string value.
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static Guid HashToGuid(this string value)
        {
            using (SHA256 sha = SHA256.Create())
            {
                byte[] hash = sha.ComputeHash(Encoding.UTF8.GetBytes(value));
                return new Guid(hash.Take(16).ToArray());
            }
        }

        /// <summary>
        /// Attempts to parse a string as a GUID.
        /// If it is not a GUID, one is created based on the hashed bytes of the string.
        /// </summary>
        /// <param name="uuid"></param>
        /// <returns></returns>
        public static Guid TryParseOrHashToGuid(this string uuid)
        {
            if (Guid.TryParse(uuid, out var guid))
            {
                return guid;
            }

            return uuid.HashToGuid();
        }

        public static string AppendUrlPath(this string baseUrl, params string[] uriPaths)
        {
            if (baseUrl.IsNullOrWhiteSpace() || uriPaths == null || uriPaths.Length == 0)
            {
                return baseUrl;
            }

            var sb = new StringBuilder(baseUrl.Trim('/') + "/");

            foreach (var path in uriPaths.Where(x => !x.IsNullOrWhiteSpace()))
            {
                sb.Append(path.Trim('/') + "/");
            }

            return sb.ToString().TrimEnd('/');
        }

        public static bool IsValidTimeZone(this string timeZone)
        {
            try
            {
                if (string.IsNullOrEmpty(timeZone))
                    return false;

                var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
                return true;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Could not parse timeZone '{timeZone}'", timeZone);
            }

            return false;
        }

        public static bool IsValidLocale(this string locale)
        {
            try
            {
                if (string.IsNullOrEmpty(locale))
                    return false;

                var cultureInfo = System.Globalization.CultureInfo.GetCultureInfo(locale);
                return true;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Could not parse locale '{locale}'", locale);
            }

            return false;
        }

        public static bool NotContainUrl(this string str)
        {
            if (string.IsNullOrWhiteSpace(str))
                return true;
            var urlRegex = new Regex(@"http(s)?://([\w-]+.)+[\w-]+(/[\w- ./?%&=]*)?");
            return !urlRegex.IsMatch(str);
        }

        public static bool NotContainEmail(this string str)
        {
            if (string.IsNullOrWhiteSpace(str))
                return true;
            var emailRegex = new Regex(@"\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*");
            return !emailRegex.IsMatch(str);
        }

        public static bool NotContainAtSign(this string str)
        {
            if (string.IsNullOrWhiteSpace(str))
                return true;
            return !str.Contains("@");
        }

        public static string SanitizeHtml(
            this string str,
            string[] allowedSchemes = null,
            string[] allowedAttributes = null,
            string[] allowedTags = null
        )
        {
            if (str.IsNullOrEmpty())
                return string.Empty;

            var sanitizer = new HtmlSanitizer();

            if (!allowedSchemes.IsNullOrEmpty())
            {
                foreach (var scheme in allowedSchemes)
                {
                    sanitizer.AllowedSchemes.Add(scheme);
                }
            }

            if (!allowedAttributes.IsNullOrEmpty())
            {
                foreach (var attribute in allowedAttributes)
                {
                    sanitizer.AllowedAttributes.Add(attribute);
                }
            }

            if (!allowedTags.IsNullOrEmpty())
            {
                foreach (var tag in allowedTags)
                {
                    sanitizer.AllowedTags.Add(tag);
                }
            }

            return sanitizer.Sanitize(str);
        }

        /// <summary>
        /// A valid workspace name can only contain letters, numbers, and the following special characters:
        ///  &amp; - . , _ '
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static bool IsValidWorkspaceName(this string str)
        {
            if (string.IsNullOrWhiteSpace(str))
                return false;
            var regex = new Regex("^[\\p{L}0-9&-.,_' ]*$");
            var valid = regex.IsMatch(str);
            return valid && str.NotContainUrl();
        }

        public static bool IsValidHexColor(this string str)
        {
            if (string.IsNullOrWhiteSpace(str))
                return false;
            var regex = new Regex("^(?:#[A-Fa-f0-9]{6})?$");
            return regex.IsMatch(str);
        }

        public static string ParseAcceptLanguage(this string str)
        {
            if (string.IsNullOrWhiteSpace(str) || str == "*")
                return string.Empty;

            var values = str.Split(',')
                .Select(
                    (i) =>
                    {
                        var parts = i.Split(';');
                        var lang = parts[0].Trim();
                        var qualityStr = parts.Length > 1 ? parts[1].Split('=')[1].Trim() : "1";
                        var quality = double.TryParse(qualityStr, out var q) ? q : 1;
                        return (lang, quality);
                    }
                )
                .OrderByDescending(i => i.quality);

            var result = values.FirstOrDefault().lang;
            return result == "*" ? string.Empty : result;
        }

        public static string GetValueOrEmpty(this string value)
        {
            return string.IsNullOrWhiteSpace(value) ? string.Empty : value;
        }

        /// <summary>
        /// Collapses empty lines in a string
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string CollapseEmptyLines(this string str)
        {
            if (string.IsNullOrWhiteSpace(str))
                return str?.Trim();
            var lines = str.Split(
                Environment.NewLine,
                StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries
            );
            return string.Join(Environment.NewLine, lines).Trim();
        }

        /// <summary>
        /// Generates a unique string by appending a number to the base string
        /// </summary>
        /// <param name="str"></param>
        /// <param name="values"></param>
        /// <returns>
        /// For example, if the base string is "Test" and the values are ["Test", "Test 1", "Test 2"], the result will be "Test 3"
        /// </returns>
        public static string GenerateUniqueString(this string str, string[] values)
        {
            if (values == null || values.Length == 0)
                return str;

            var index = 1;
            var baseStr = str;
            while (values.Contains(str))
            {
                str = $"{baseStr} {index}";
                index++;
            }

            return str;
        }

        public static string ToMaskedString(
            this string input,
            char mask = '#',
            int shownCharacters = 4
        )
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
            if (input.Length < 4)
            {
                shownCharacters = input.Length;
            }

            var chars = new string(input.Where(char.IsLetterOrDigit).ToArray());
            if (chars.Length < shownCharacters)
            {
                shownCharacters = chars.Length;
            }
            var maskedChars =
                new string(mask, chars.Length - shownCharacters) + chars[^shownCharacters..];
            var charIndex = 0;
            var result = input.Select(c => char.IsLetterOrDigit(c) ? maskedChars[charIndex++] : c);
            return new string(result.ToArray());
        }

        public static string FirstLetterToUpper(this string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return input[0].ToString().ToUpper();
        }

        public static Guid DecompressToGuid(this string asciiString)
        {
            string base64 = asciiString.Replace(".", "+").Replace("-", "/");
            switch (base64.Length % 4)
            {
                case 2:
                    base64 += "==";
                    break;
                case 3:
                    base64 += "=";
                    break;
            }

            byte[] data = Convert.FromBase64String(base64);

            StringBuilder hex = new();
            foreach (var b in data)
            {
                hex.Append(b.ToString("x2"));
            }

            // Create GUID from hex string
            string hexString = hex.ToString();
            Guid result = Guid.ParseExact(hexString, "N"); // "N" = 32 digits, no dashes
            return result;
        }

        public static string CleanString(this string str)
        {
            if (string.IsNullOrWhiteSpace(str))
                return string.Empty;

            // Remove all non-alphanumeric characters except for spaces
            var cleaned = Regex.Replace(str, @"[^a-zA-Z0-9\s]", string.Empty);
            // Remove extra spaces
            cleaned = Regex.Replace(cleaned, @"\s+", " ");
            // Trim leading and trailing spaces
            return cleaned.Trim();
        }
        public static Guid ToGuid(this string noDashGuid)
        {
            if (noDashGuid == null)
                throw new ArgumentNullException(nameof(noDashGuid));

            return new Guid(noDashGuid);
        }

        /// <summary>
        /// Replaces all null bytes in the string with an empty string.
        /// </summary>
        /// <param name="input">The input string.</param>
        /// <returns>The cleaned string without null bytes, or null if input is null.</returns>
        public static string ReplaceNullByte(this string input)
        {
            return input?.Replace("\0", "");
        }

        /// <summary>
        /// Converts a string to an enum value of type TEnum.
        /// </summary>
        /// <typeparam name="TEnum"> where TEnum : struct, Enum
        /// <param name="value">String value to convert.</param>
        /// <returns>Enum value of type TEnum.</returns>
        public static TEnum ToEnum<TEnum>(this string value) where TEnum : struct, Enum
        {
            if (Enum.TryParse<TEnum>(value, out var result))
            {
                return result;
            }

            throw new ArgumentException(
                $"Invalid value '{value}' for enum type '{typeof(TEnum).Name}'"
            );
        }

        public static TEnum ToEnumFromDescription<TEnum>(this string description)
            where TEnum : struct, Enum
        {
            var success = EnumExtensions.TryGetEnumValueFromDescription<TEnum>(
                description,
                out var result
            );
            if (success)
                return result;

            throw new ArgumentException(
                $"Invalid description '{description}' for enum type '{typeof(TEnum).Name}'"
            );
        }

        /// <summary>
        /// Converts a string to a DateOnly object.
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="format"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        public static DateOnly ToDateOnly(this string dateTime, string format = null)
        {
            if (string.IsNullOrWhiteSpace(dateTime))
                throw new ArgumentNullException(nameof(dateTime));

            if (format != null)
            {
                if (DateOnly.TryParseExact(dateTime, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out var dateExact))
                {
                    return dateExact;
                }
            }

            if (DateOnly.TryParse(dateTime, out var date))
            {
                return date;
            }

            if (DateTime.TryParse(dateTime, out var dateTimeValue))
            {
                return DateOnly.FromDateTime(dateTimeValue);
            }

            throw new ArgumentException($"Invalid date string '{dateTime}'");
        }
        
        /// <summary>
        /// Trims and converts string to Pascal case
        /// </summary>
        /// <param name="words"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public static string ToPascalCase(this string words)
        {
            if (string.IsNullOrWhiteSpace(words))
                throw new ArgumentNullException(nameof(words));

            var wordsArray = words.Split(' ', StringSplitOptions.RemoveEmptyEntries);

            if (wordsArray.Length == 1)
                return words;

            var pascalCaseWords = string.Concat(
                wordsArray.Select(word =>
                    char.ToUpperInvariant(word[0]) + word.Substring(1).ToLowerInvariant()
                )
            );

            return pascalCaseWords;
        }
    }
}
