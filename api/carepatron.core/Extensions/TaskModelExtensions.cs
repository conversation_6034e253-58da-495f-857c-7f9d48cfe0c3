﻿using System;
using System.Collections.Generic;
using System.Linq;
using carepatron.core.Application.Tasks.Extensions;
using carepatron.core.Application.Tasks.Models;
using carepatron.core.Models.Common;

namespace carepatron.core.Extensions;

public static class TaskModelExtensions
{
    public static bool HasContact(this TaskModel task, Guid contactId)
    {
        return task?.Contacts?.Any(c => c.Id == contactId) ?? false;
    }

    public static Dictionary<Guid, SortedList<DateOnly, List<TaskModel>>> GroupByStaffOrderedByDate(this TaskModel[] tasks)
    {
        var staffTasksDictionary = new Dictionary<Guid, SortedList<DateOnly, List<TaskModel>>>();
        foreach (var task in tasks)
        {
            if (task.StartDate == DateTime.MinValue) continue;

            var startDate = task.StartDate.ToDateOnly();
            foreach (var staffId in task.StaffIds ?? [])
            {
                if (!staffTasksDictionary.ContainsKey(staffId))
                    staffTasksDictionary[staffId] = new SortedList<DateOnly, List<TaskModel>>();

                if (!staffTasksDictionary[staffId].ContainsKey(startDate))
                    staffTasksDictionary[staffId].Add(startDate, new List<TaskModel>());

                staffTasksDictionary[staffId][startDate].Add(task);
            }
        }
        return staffTasksDictionary;
    }

    public static Dictionary<Guid, List<TaskModel>> GroupByStaff(this TaskModel[] tasks)
    {
        var staffTasksDictionary = new Dictionary<Guid, List<TaskModel>>();
        foreach (var task in tasks)
        {
            if (task.StartDate == DateTime.MinValue) continue;

            var startDate = task.StartDate.ToDateOnly();
            foreach (var staffId in task.StaffIds ?? [])
            {
                if (!staffTasksDictionary.ContainsKey(staffId))
                    staffTasksDictionary[staffId] = new();

                staffTasksDictionary[staffId].Add(task);
            }
        }
        return staffTasksDictionary;
    }

    public static bool IsGroupEvent(this TaskModel task)
    {
        return task.ItemsSnapshot?.Any(i => i.AllowGroupEvents) ?? false;
    }

    public static int? GetGroupEventRemainingSpots(this TaskModel task, Guid selectedProviderItemId)
    {
        var itemSnapshot = task.ItemsSnapshot?.FirstOrDefault(i => i.Id == selectedProviderItemId && i.AllowGroupEvents);
        if (itemSnapshot is null) return null;

        var maxLimit = itemSnapshot.MaximumAttendeeLimit ?? 0;
        var validAttendees = task.Contacts?.Where(c => c.AboutContact && !c.IsCancelled()).ToArray();
        var attendeesCount = validAttendees?.Length ?? 0;
        var remaining = maxLimit - attendeesCount;

        // attendees count could be more than the max limit, so we return 0 if it's negative
        return remaining < 0 ? 0 : remaining;
    }

    public static SaveTaskModel ToSaveTaskModel(this TaskModel task, Guid providerId, Guid personId, bool isNew = false, TaskType taskType = TaskType.External)
    {
        var model = new SaveTaskModel
        {
            Id = task.Id,
            ProviderId = providerId,
            Type = taskType,
            ExternalId = task.ExternalId,
            ExternalEventType = task.ExternalEventType,
            Title = task.Title,
            Description = task.Description,
            StartDate = task.StartDate.AsKind(DateTimeKind.Utc),
            EndDate = task.EndDate.AsKind(DateTimeKind.Utc),
            AllDay = task.AllDay,
            TimeZone = task.TimeZone,
            Location = task.Location,
            LocationType = task.LocationType,
            VirtualLocationProduct = task.VirtualLocationProduct,
            RRule = task.RRule,
            ExDate = task.ExDate,
            OccurrenceEndDate = task.OccurrenceEndDate,
            StaffIds = task.StaffIds,
            LastUpdatedByPersonId = personId,
            ParentId = task.ParentId,
            LastUpdatedDateTimeUtc = DateTime.UtcNow,
            ContactIds = task.Contacts.Select(c => c.Id).ToArray(),
            AboutContacts = task.Contacts.Where(c => c.AboutContact).Select(c => c.Id).ToArray(),
            ContactReminderConfigs = task.ContactReminderConfigs,
            Items = task.ItemsSnapshot,
            LocationPOSCode = task.LocationPOSCode,
            CallId = task.CallId,
            CreatedByPersonId = task.CreatedByPersonId,
            ExternalCalendarId = task.ExternalCalendarId,
            OriginalStartDate = task.OriginalStartDate,
            IsFree = task.IsFree,
            IsBillingV2 = task.IsBillingV2,
            ExternalContacts = task.ExternalContacts
        };

        if (isNew)
        {
            model.CreatedByPersonId = personId;
            model.CreatedDateTimeUtc = DateTime.UtcNow;
        }

        return model;
    }

    public static SaveTaskModel ToSaveTaskModel(this TaskModel task)
    {
        return new SaveTaskModel
        {
            Id = task.Id,
            Type = task.Type,
            ProviderId = task.ProviderId,
            ParentId = task.ParentId,
            Title = task.Title,
            Description = task.Description,

            // date and time
            TimeZone = task.TimeZone,
            StartDate = task.StartDate,
            EndDate = task.EndDate,
            AllDay = task.AllDay,
            RRule = task.RRule,
            ExDate = task.ExDate,
            OccurrenceEndDate = task.OccurrenceEndDate,
            OriginalStartDate = task.OriginalStartDate,
            IsFree = task.IsFree,

            // attendees and services
            Contacts = (task.Contacts ?? []).Select(x => new SimpleTaskContact
            {
                TaskId = task.Id,
                ContactId = x.Id,
                Attending = x.Attending,
                Reason = x.Reason,
                AboutContact = x.AboutContact,
                RecipientStatus = x.RecipientStatus,
                LastMessageId = x.LastMessageId,
                TaskContactStatus = x.Status,
                AttendeeStatusId = x.AttendeeStatusId
            }).ToArray(),
            ContactIds = (task.Contacts ?? []).Select(x => x.Id).ToArray(),
            AboutContacts = (task.Contacts ?? []).Where(x => x.AboutContact).Select(x => x.Id).ToArray(),
            StaffIds = task.StaffIds ?? [],
            Items = task.ItemsSnapshot,

            // locations
            CallId = task.CallId,
            Location = task.Location,
            LocationType = task.LocationType,
            LocationPOSCode = task.LocationPOSCode,
            LocationsSnapshot = task.LocationsSnapshot,
            VirtualLocationProduct = task.VirtualLocationProduct,

            // external event info
            ExternalCalendarId = task.ExternalCalendarId,
            ExternalId = task.ExternalId,
            ExternalEventType = task.ExternalEventType,
            ExternalContacts = task.ExternalContacts,

            // others
            DeclineEventType = task.DeclineEventType,
            ContactReminderConfigs = task.ContactReminderConfigs ?? [],
            Files = [],
            IsBillingV2 = task.IsBillingV2,

            // audit
            CreatedByPersonId = task.CreatedByPersonId,
            CreatedDateTimeUtc = task.CreatedDateTimeUtc,
            LastUpdatedByPersonId = task.LastUpdatedByPersonId,
            LastUpdatedDateTimeUtc = DateTime.UtcNow,
        };
    }
}
