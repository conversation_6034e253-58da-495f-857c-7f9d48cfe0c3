﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace carepatron.core.Extensions
{
    public static class LinqExtensions
    {
        public static IEnumerable<T> EmptyIfNull<T>(this IEnumerable<T> col)
        {
            return col ?? Enumerable.Empty<T>();
        }

        public static TVal GetOrDefault<TKey, TVal>(this IDictionary<TKey, TVal> dict, TKey key)
        {
            if (dict.TryGetValue(key, out var val))
            {
                return val;
            }

            return default;
        }

        public static IEnumerable<T> Distinct<T, TKey>(this IEnumerable<T> col, Func<T, TKey> selector)
        {
            if (col == null)
                return Enumerable.Empty<T>();

            return col.GroupBy(selector).Select(group => group.FirstOrDefault());
        }

        public static IEnumerable<T> PreferExisingItems<T, TKey>(this IEnumerable<T> col, IEnumerable<T> existing, Func<T, TKey> selector)
        {
            if (col == null || !col.Any())
                return Enumerable.Empty<T>();

            if (existing == null || !existing.Any())
                return col;

            var intersectionWithPreference = col.Join(existing, x => selector(x), y => selector(y), (x, y) => y);

            var existingDic = existing.ToDictionary(x => selector(x));

            var leftSetWithoutIntersection = col.Where(x => !existingDic.TryGetValue(selector(x), out _));

            return intersectionWithPreference.Union(leftSetWithoutIntersection);
        }

        public static IEnumerable<T[]> ToBatches<T>(this IEnumerable<T> list, int batchSize = 10)
        {
            int total = 0;
            while (total < list.Count())
            {
                yield return list.Skip(total).Take(batchSize).ToArray();
                total += batchSize;
            }
        }

        public static bool IsNullOrEmpty<T>(this IEnumerable<T> col)
        {
            return col == null || !col.Any();
        }

        public static IEnumerable<T> FilterNulls<T>(this IEnumerable<T> col)
        {
            return col == null ? [] : col.Where(x => x != null);
        }
    }
}
