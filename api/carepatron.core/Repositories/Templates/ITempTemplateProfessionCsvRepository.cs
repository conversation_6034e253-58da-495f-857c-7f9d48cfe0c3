using AngleSharp.Common;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Extensions;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.core.Repositories.Templates;

public interface ITempTemplateProfessionCsvRepository
{
    Task<IList<TempTemplateProfession>> GetTempTemplateProfessions(int offset, int limit);
}

public class TempTemplateProfessionCsvRepository : ITempTemplateProfessionCsvRepository
{
    private const string FileName = "carepatron_public_template_profession_author_mapping.csv";
    
    public async Task<IList<TempTemplateProfession>> GetTempTemplateProfessions(int offset, int limit)
    {
        var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
        var filePath = Path.Combine(baseDirectory, "Application", "Templates", "Resources", FileName);

        var templateProfessions = new List<TempTemplateProfession>();
        if (!File.Exists(filePath))
        {
            Log.Error("CSV file not found at path: {FilePath}", filePath);
            return templateProfessions;
        }
        
        using var reader = new StreamReader(filePath);
        int currentLine = 0;
        int recordsProcessed = 0;

        // Skip lines until we reach the offset
        while (!reader.EndOfStream && currentLine < offset)
        {
            await reader.ReadLineAsync();
            currentLine++;
        }

        while (!reader.EndOfStream && recordsProcessed < limit)
        {
            var row = await reader.ReadLineAsync();

            if (string.IsNullOrWhiteSpace(row))
            {
                currentLine++;
                continue;
            }
            
            var columns = row.Split(",",  StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);

            /*
                0 - Full name
                1 - PersonId
                2 - TemplateId
                3 - Professions
            */
                    
            var fullNameIndex = 0;
            var personIdIndex = 1;
            var templateIdIndex = 2;
            var professionsIndex = 3;
                    
            if (columns.Length < 3)
            {
                Log.Error("Invalid CSV row at line {LineNumber}: insufficient columns", currentLine + 1);
                currentLine++;
                recordsProcessed++;
                continue;
            }

            if (!Guid.TryParse(columns[templateIdIndex], out var publicTemplateId))
            {
                Log.Error("Invalid public template id at line {LineNumber}: {Value}", currentLine + 1, columns[templateIdIndex]);
                currentLine++;
                recordsProcessed++;
                continue;
            }
                    
            if (!Guid.TryParse(columns[personIdIndex], out var personId))
            {
                Log.Error("Invalid person id at line {LineNumber}: {Value}", currentLine + 1, columns[personIdIndex]);
                currentLine++;
                recordsProcessed++;
                continue;
            }
            
            var professions = columns[professionsIndex];
            var professionsList = new List<string>();

            if (!string.IsNullOrWhiteSpace(professions))
            {
                foreach (var profession in professions.Split('|', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries))
                {
                    professionsList.Add(profession.ToPascalCase());
                }
            }

            var invalidProfessions = professionsList.Except(Professions.All).ToList();

            if (!invalidProfessions.IsNullOrEmpty())
            {
                Log.Error("Invalid profession at line {LineNumber}: {Value}", currentLine + 1, professions);
                currentLine++;
                recordsProcessed++;
                continue;
            }

            var tempTemplateProfession = new TempTemplateProfession
            {
                FullName = columns[fullNameIndex],
                PersonId = personId,
                Professions = professionsList,
                PublicTemplateId = publicTemplateId,
            };
            
            templateProfessions.Add(tempTemplateProfession);
            currentLine++;
            recordsProcessed++;
        }
        
        return templateProfessions;
    }
}