﻿using carepatron.core.Application.Templates.Models;
using carepatron.core.Models.Common;
using carepatron.core.Models.Pagination;
using System;
using System.Threading.Tasks;
using carepatron.core.Authorization.Models;

namespace carepatron.core.Repositories.Templates
{
    public interface ITemplateRepository
    {
        // collections

        Task<PaginatedResult<IntakeTemplate>> GetIntakeTemplates(Guid providerId, int limit, int offset);
        Task<DefaultIntakeTemplate> GetDefaultIntakeTemplate(Guid providerId, Guid id);
        Task<DefaultIntakeTemplate> CreateDefaultIntakeTemplate(DefaultIntakeTemplate template);
        Task DeleteDefaultIntakeTemplate(Guid providerId, Guid id);
        Task<TemplateCollection> CreateCollection(TemplateCollection collection);

        Task<TemplateCollection[]> CreateCollections(TemplateCollection[] collection);

        Task<TemplateCollection> UpdateCollection(TemplateCollection collection);

        Task<TemplateCollection> GetCollection(Guid providerId, Guid id);

        Task<TemplateCollection> GetCollectionByName(Guid providerId, string collection);

        Task DeleteCollection(Guid providerId, Guid id);

        Task<PaginatedResult<TemplateCollection>> GetCollections(Guid providerId, int limit, int offset);

        // templates

        Task<Template> CreateTemplate(Template template);

        Task<Template[]> CreateTemplates(Template[] template);

        Task<Template> UpdateTemplate(Template template, bool includeSoftDeleted = false);
        
        Task SetTemplateCollection(Guid id, Guid? templateCollectionId);

        Task<bool> HasAiPrompts(Guid templateId);

        Task DeleteTemplate(Guid providerId, Guid id);

        Task<Template> GetTemplate(Guid id, Guid? providerId = null, bool includeSoftDeleted = false, Guid? currentLoggedInPersonId = null);

        Task<TemplateMeta> GetMeta(Guid templateId, Guid? providerId = null);
        Task<TemplateMeta[]> GetMeta(Guid[] templateIds, Guid? providerId = null);

        Task<PublicTemplateMeta> GetPublicTemplateMeta(Guid id);

        Task<PaginatedResult<Template>> GetTemplates(Guid providerId,
            string searchTerm,
            Guid[] tags,
            string[] collections,
            int limit,
            int offset,
            bool? hasAiPrompts);

        Task<Template[]> GetTemplatesByIds(Guid? providerId, Guid[] ids);

        Task MarkTemplateAsPublished(Guid providerId, Guid templateId);

        Task MarkTemplateAsUnpublished(Guid providerId, Guid templateId);

        // public templates

        Task<PublicTemplate> GetPublicTemplate(Guid id);

        Task<PublicTemplate> CreatePublicTemplate(PublicTemplate template);

        Task<PublicTemplate> UpdatePublicTemplate(PublicTemplate template);
        
        Task DeletePublicTemplate(Guid id);

        Task<PaginatedResult<PublicTemplate>> GetPublicTemplates(string searchTerm,
            string[] collections,
            string[] tags,
            string[] professions,
            int limit,
            int offset,
            bool? hasAiPrompts,
            bool? isRecommended,
            Guid[] recommendedPublicTemplateIds,
            UnifiedSearchSortBy? sortBy);

        // metrics

        Task UpdateTemplateMetrics(Guid templateId, int copyCount, int viewCount, int useCount);

        Task UpdatePublicTemplateMetrics(Guid publicTemplateId, int addCopyCount, int addViewCount, int addUseCount);

        // templates used by person

        Task<TemplatesUsedByPerson> GetTemplatesUsedByPerson(Guid personId, Guid providerId);

        Task<TemplatesUsedByPerson> SaveTemplatesUsedByPerson(TemplatesUsedByPerson templatesUsedByPerson);

        Task UpsertPublicTemplate(PublicTemplate publicTemplate);

        Task UpdatePublicTemplateContentJson(Guid publicTemplateId);

        Task<TemplateMeta[]> GetTemplates(DateRange dateRange, Guid[] templateIds, Guid? lastTemplateId, int limit, bool? hasAiPrompts);

        Task<PublicTemplateMeta[]> GetPublicTemplates(DateRange dateRange, Guid[] publicTemplateIds, Guid? lastPublicTemplateId, int limit, bool? hasAiPrompts);
        Task<ResourceName> GetResourceName(Guid templateId);
        Task<PaginatedResult<SimpleTemplate>> GetSimpleTemplates(Guid providerId, string searchTerm, Guid[] tags, string[] collections, UnifiedSearchSortBy? sortBy, int limit, int offset,
            bool? hasAiPrompts, Guid? templatesFolderId, Guid? favoritesByPersonId, bool favorites);
        Task<PaginatedResult<TemplateMeta>> GetPaginatedTemplatesByFolderId(Guid providerId, Guid folderId, bool deletedOnly, int limit = 20,
            int offset = 0);
        Task<Template[]> GetTemplatesByFolderId(Guid providerId, Guid folderId);

        Task BulkDelete(Guid providerId, Guid[] templateIds, bool isSoftDelete);
        Task Restore(Guid providerId, Guid id);
        Task RestoreByFolderId(Guid providerId, Guid id);
        Task HardDelete(Guid providerId, Guid id);
    }
}