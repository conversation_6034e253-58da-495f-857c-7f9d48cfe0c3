using carepatron.core.Application.Templates.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace carepatron.core.Repositories.Templates;

public interface ITempTemplateProfessionRepository
{
    Task CreateTempPublicTemplateProfessions(Guid publicTemplateId, string[] professions);
    
    Task SetPublicTemplateAuthor(Guid publicTemplateId, Author author);
    
    Task<PublicTemplate> GetLivePublicTemplate(Guid id);

    Task UpdatePublicTemplateProfessions(Guid publicTemplateId, string[] professions);

    Task PurgeTempPublicTemplateProfessions(params Guid[] ids);

    Task<IList<PublicTemplate>> GetLivePublicTemplates(Guid[] dataPublicTemplateIds);

    Task<IList<(Guid PublicTemplateId, string[] Professions)>> GetLivePublicTemplatesForBackup(Guid[] publicTemplateIds);


    Task<IList<TempPublicTemplateProfession>> GetTempPublicTemplateProfessions(Guid publicTemplateId);
}