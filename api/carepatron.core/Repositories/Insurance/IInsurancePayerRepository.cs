using carepatron.core.Application.Insurance.Models;
using carepatron.core.Models.Pagination;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Repositories.Insurance;

public interface IInsurancePayerRepository
{
    /*
    Provider payer methods
    */
    Task<PaginatedResult<ProviderInsurancePayer>> Get(
        Guid providerId,
        string search,
        PaginationRequest paginationRequest
    );
    Task<ProviderInsurancePayer> GetByPayerNumber(
        Guid providerId,
        string payerNumber,
        ClearingHouseType clearingHouse,
        CancellationToken cancellationToken = default
    );
    Task<ProviderInsurancePayer> Create(
        ProviderInsurancePayer providerInsurancePayer,
        CancellationToken cancellationToken = default
    );
    Task<ProviderInsurancePayer> Update(
        ProviderInsurancePayer providerInsurancePayer,
        CancellationToken cancellationToken = default
    );
    Task<ProviderInsurancePayer> GetById(Guid providerId, Guid id);
    Task Delete(Guid providerId, Guid id);
    Task<ProviderInsurancePayer[]> ImportPayers(ProviderInsurancePayer[] createdPayers, ProviderInsurancePayer[] providerPayers,
        CancellationToken cancellationToken = default);

    /*
    Clearing house payer methods
    */
    Task<PaginatedResult<AvailableInsurancePayer>> GetAvailableInsurancePayers(
        Guid providerId,
        string searchTerm,
        string[] states,
        PaginationRequest paginationRequest
    );
    Task<InsurancePayer> GetClearingHousePayer(
        ClearingHouseType clearingHouse,
        string payerId,
        CancellationToken cancellationToken = default
    );
    Task<InsurancePayer> GetClearingHousePayerByProviderPayerId(
        Guid providerId,
        Guid providerPayerId,
        CancellationToken cancellationToken = default
    );
    Task UpdateClearingHousePayer(InsurancePayer clearingHousePayer);
    Task CreateClearingHousePayer(InsurancePayer clearingHousePayer);
    Task<InsurancePayer[]> GetClearingHousePayers(
        Guid providerId,
        ClearingHousePayer[] requestPayers,
        CancellationToken cancellationToken = default
    );
    Task<Dictionary<string, ProviderInsurancePayer>> GetById(Guid requestProviderId, ClearingHouseType none, string[] payerIds);
}
