using carepatron.core.Application.Insurance.Models;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Repositories.Insurance;

public interface IInsuranceRemittancePaymentsRepository
{
    Task<InsuranceRemittancePayment> Create(
        InsuranceRemittancePayment insuranceRemittancePayment,
        CancellationToken cancellationToken = default
    );

    Task<InsuranceRemittancePayment> GetByReference(
        Guid providerId,
        string paymentReference,
        CancellationToken cancellationToken = default
    );
}
