using carepatron.core.Application.Insurance.Models;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Repositories.Insurance;

public interface IInsuranceClaimErrorsRepository
{
    Task Upsert(
        Guid insuranceClaimId,
        Guid providerId,
        InsuranceClaimError[] claimErrors,
        CancellationToken cancellationToken = default
    );
    Task ClearForClaims(
        Guid[] insuranceClaimIds,
        Guid providerId,
        CancellationToken cancellationToken = default
    );
    Task<InsuranceClaimError[]> GetByClaimId(
        Guid insuranceClaimId,
        Guid providerId,
        CancellationToken cancellationToken = default
    );
}
