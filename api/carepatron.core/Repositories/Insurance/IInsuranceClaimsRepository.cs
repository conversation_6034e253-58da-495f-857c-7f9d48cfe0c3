using System;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Models.Pagination;

namespace carepatron.core.Repositories.Insurance;

public interface IInsuranceClaimsRepository
{
    Task<InsuranceClaim> GetById(
        Guid id,
        Guid providerId,
        CancellationToken cancellationToken,
        bool ignoreQueryFilters = false
    );
    Task<string> GetNextNumber(Guid providerId, int limit = 3);
    Task<bool> DoesNumberExist(Guid providerId, string number, bool includeDeleted = true);
    Task<PaginatedResult<InsuranceClaim>> Get(
        Guid providerId,
        Guid? contactId,
        PaginationRequest paginationRequest,
        ClaimStatus[] statuses,
        DateTime? fromDate,
        DateTime? toDate,
        Guid[] taskIds,
        CancellationToken cancellationToken,
        Guid? assignedToPersonId = null,
        string searchTerm = null
    );
    Task<InsuranceClaim> GetById(Guid id, Guid providerId);
    Task<InsuranceClaimReference[]> GetTaskClaims(
        Guid providerId,
        Guid contactId,
        Guid taskId,
        CancellationToken cancellationToken
    );

    Task<InsuranceClaim> UpdateStatus(
        Guid id,
        Guid providerId,
        ClaimStatus status,
        string statusReason,
        CancellationToken cancellationToken = default
    );
    Task<InsuranceClaim[]> UpdateStatus(
        InsuranceClaimStatusUpdateRequest[] claims,
        CancellationToken cancellationToken
    );
    Task UpdateServiceLines(Guid id, Guid providerId, ClaimServiceLine[] claimServiceLines);
    Task HardDelete(Guid providerId, Guid claimId);
    Task<InsuranceClaim[]> GetClaimsByServiceLines(Guid providerId, Guid[] serviceLineIds);

    Task<PaginatedResult<InsuranceClaimListEntry>> GetByProviderId(
        Guid providerId,
        Guid personId,
        Guid[] contactIds,
        Guid[] staffIds,
        string[] payerIds,
        bool isAssignedOnly,
        int offset,
        int limit,
        ClaimStatus[] statuses,
        DateOnly? fromDate,
        DateOnly? toDate,
        string? searchTerm,
        Sorting sorting
    );

    Task<InsuranceClaimExportData[]> ExportByProviderId(
        Guid providerId,
        Guid personId,
        ClaimStatus[] statuses,
        Guid[] staffIds,
        string[] payerIds,
        bool isAssignedOnly,
        DateOnly? fromDate = null,
        DateOnly? toDate = null
    );

    Task CloseDraftClaimsForContact(Guid providerId, Guid contactId);
}
