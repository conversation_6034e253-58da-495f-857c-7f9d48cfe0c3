using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace carepatron.core.Repositories.ClearingHouse;

public interface IClearingHouseIdLookupRepository
{
    /// <summary>
    /// Gets a clearing house identifier for a given entity Id. 
    /// </summary>
    /// <param name="entityId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<long?> Get(Guid entityId, CancellationToken cancellationToken);

    /// <summary>
    /// Resolves the clearing house ID lookup for a given entity ID and generates one if it does not exist.
    /// </summary>
    /// <param name="entityId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<long> GetOrAdd(Guid entityId, CancellationToken cancellationToken);

    /// <summary>
    /// Looks up the entity ID for a given clearing house identifier.
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<Guid?> Lookup(long identifier, CancellationToken cancellationToken);

    /// <summary>
    /// Looks up the entity ID for a given clearing house identifier.
    /// Automatically converts the identifier to a long if it is a string representation of a number.
    /// Returns null if not a valid long or if the identifier does not exist in the lookup table.
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<Guid?> Lookup(string identifier, CancellationToken cancellationToken);

    /// <summary>
    /// Looks up the entity IDs for a given set of clearing house identifiers. 
    /// </summary>
    /// <param name="identifiers"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<Dictionary<long, Guid>> Lookup(long[] identifiers, CancellationToken cancellationToken);
    Task<Dictionary<string, Guid>> Lookup(string[] identifiers, CancellationToken cancellationToken);
}
