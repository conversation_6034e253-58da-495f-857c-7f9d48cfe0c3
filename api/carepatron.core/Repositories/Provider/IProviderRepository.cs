﻿using System;
using System.Threading.Tasks;
using carepatron.core.Application.Workspace.Billing.Models;
using carepatron.core.Application.Workspace.Locations.Models;
using carepatron.core.Application.Workspace.Preferences.Models;
using carepatron.core.Application.Workspace.Providers.Models;
using carepatron.core.Application.Workspace.Schemas.Models;
using carepatron.core.Models.Media;

namespace carepatron.core.Repositories.Provider
{
    public interface IProviderRepository
    {
        Task<Application.Workspace.Providers.Models.Provider> GetProvider(Guid id);

        Task<Application.Workspace.Providers.Models.Provider[]> GetProviders(Guid[] ids);

        Task Create(Application.Workspace.Providers.Models.Provider provider);

        Task UpdateSettings(ProviderSettings provider);

        Task<ProviderSettings> GetProviderSettings(Guid id);

        Task<ProviderLocation[]> GetProviderLocations(Guid providerId);

        Task<ProviderLocation> GetProviderLocationById(Guid id);

        Task<ProviderLocation> CreateProviderLocation(ProviderLocation providerLocation);

        Task<ProviderLocation> UpdateProviderLocation(ProviderLocation providerLocation);

        Task DeleteProviderLocation(Guid id, Guid providerId);

        Task<CountryCurrency> GetCountryCurrency(string countryCode);

        Task SaveStripeAccount(Guid providerId, string stripeAccountId);

        Task<CountryCurrency> GetCountryCurrency(int id);

        Task SaveProviderContactFieldSettings(ProviderContactFieldSettings settings);

        Task<ProviderContactFieldSettings> GetProviderContactFieldSettings(Guid id);

        Task<ProviderContactFieldSettings[]> GetProviderContactFieldSettings(Guid[] ids);

        Task SaveProviderOnlineBookingOptions(ProviderOnlineBookingOptions options);

        Task<ProviderOnlineBookingOptions> GetProviderOnlineBookingOptions(Guid providerId);

        Task Delete(Guid providerId, Guid deleteByPersonId, string deletedReason);

        Task<Application.Workspace.Providers.Models.Provider[]> GetProviders(
            Guid? providerId,
            int limit
        );

        Task<ProviderBillingSettings> GetBillingSettings(Guid providerId);

        Task<ProviderBillingSettings> GetBillingSettingsByStripeId(string stripeAccountId);

        Task SaveBillingSettings(ProviderBillingSettings billingSettings);

        Task SavePaymentProviderDetails(
            Guid providerId,
            PaymentAccountDetails paymentProviderAccountDetails
        );

        Task SaveWorkspacePreference(ProviderWorkspacePreference workspaceSetting);

        Task<ProviderWorkspacePreference[]> GetWorkspacePreferences(Guid providerId);

        Task<ProviderLocation[]> GetProviderLocationByIds(Guid providerId, Guid[] locationIds);

        Task CreateLogo(ProviderLogo logo);

        Task UpdateLastAccessedUtc(Guid providerId);
    }
}
