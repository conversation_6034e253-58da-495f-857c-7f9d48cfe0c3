﻿using carepatron.core.Application.Workspace.ServiceItems.Models;
using carepatron.core.Models.Pagination;
using System;
using System.Threading.Tasks;

namespace carepatron.core.Repositories.Item
{
    public interface IProviderItemGroupRepository
    {
        Task Create(ProviderItemGroup itemGroup);

        Task<ProviderItemGroup> Get(Guid providerId, Guid id);

        Task<ProviderItemGroup[]> Get(Guid providerId, params Guid[] ids);

        Task<PaginatedResult<ProviderItemGroup>> GetByProviderId(Guid providerId, string searchTerm, int limit, int offset);

        Task Update(ProviderItemGroup itemGroup);
        Task Update(ProviderItemGroup[] itemGroups);

        Task Delete(Guid providerId, Guid id);
        
        Task AddItemToGroup(Guid itemId, Guid groupId, int order = 99);
        Task RemoveItemFromGroup(Guid itemId, Guid groupId);
    }
}