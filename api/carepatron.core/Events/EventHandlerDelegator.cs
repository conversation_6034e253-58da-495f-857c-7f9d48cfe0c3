﻿using carepatron.core.Application.AskAI.EventHandlers;
using carepatron.core.Application.AskAI.Events;
using carepatron.core.Application.Authorization.Messaging;
using carepatron.core.Application.Calendar.EventHandlers;
using carepatron.core.Application.Calendar.Events;
using carepatron.core.Application.Calendar.Models;
using carepatron.core.Application.Communications.Inbox.EventHandlers;
using carepatron.core.Application.Communications.Inbox.Events;
using carepatron.core.Application.ConnectedApps.Events;
using carepatron.core.Application.Contacts.EventHandlers;
using carepatron.core.Application.Contacts.Events;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Exports.EventHandlers;
using carepatron.core.Application.Exports.Events;
using carepatron.core.Application.Notes.EventHandlers;
using carepatron.core.Application.Notes.Models;
using carepatron.core.Application.Reminders.EventHandlers;
using carepatron.core.Application.Reminders.Models;
using carepatron.core.Application.Schema.EventHandlers;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Staff.Events;
using carepatron.core.Application.Templates.EventHandlers;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Application.Transcriptions.EventHandlers;
using carepatron.core.Application.Transcriptions.Events;
using carepatron.core.Application.Users.EventHandlers;
using carepatron.core.Application.Users.Models;
using carepatron.core.Events.Events;
using carepatron.core.Events.Handlers;
using carepatron.core.Events.Handlers.ConnectedApps;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;
using Serilog;

namespace carepatron.core.Events;
public interface IEventHandlerDelegator
{
    Task Delegate(EventMessage eventMessage);
}

public class EventHandlerDelegator(
    SyncCreatedTaskEventHandler syncCreatedTaskEventHandler,
    SyncDeletedTaskEventHandler syncDeletedTaskEventHandler,
    SyncUpdatedTaskEventHandler syncUpdatedTaskEventHandler,
    SyncConnectedAppEventHandler syncConnectedAppEventHandler,
    ImportExternalContactDataEventHandler importExternalContactDataEventHandler,
    ImportContactsFromCsvEventHandler importContactsFromCsvEventHandler,
    ExportContactsCompletedEventHandler exportContactsCompletedEventHandler,
    SeedTaskReminderEventHandler seedTaskReminderEventHandler,
    SeedContactDataLayoutSchemaEventHandler seedContactDataLayoutSchemaEventHandler,
    MigrateEmbeddedNoteFilesEventHandler migrateEmbeddedNoteFilesEventHandler,
    MigrateAnnotatedNoteImagesEventHandler migrateAnnotatedNoteImagesEventHandler,
    MigrateAnnotatedTemplateImagesEventHandler migrateAnnotatedTemplateImagesEventHandler,
    SecureEmbeddedNoteFilesEventHandler secureEmbeddedNoteFilesEventHandler,
    UploadFilesToS3OnAttachmentMetadataCreatedEventHandler uploadFilesToS3OnAttachmentMetadataCreatedEventHandler,
    CleanUpS3FilesOnBinEmptiedEventHandler cleanUpS3FilesOnBinEmptiedEventHandler,
    MigrateEmbeddedPublicTemplateFilesEventHandler migrateEmbeddedPublicTemplateFilesEventHandler,
    MigrateEmbeddedTemplateFilesEventHandler migrateEmbeddedTemplateFilesEventHandler,
    SecureEmbeddedTemplateFilesEventHandler secureEmbeddedTemplateFilesEventHandler,
    SecureEmbeddedPublicTemplateFilesEventHandler secureEmbeddedPublicTemplateFilesEventHandler,
    CleanupAttachmentFilesMessageEventHandler cleanupAttachmentFilesMessageEventHandler,
    SeedUserExternalPersonAttributeEventHandler seedUserExternalPersonAttributeEventHandler,
    GmailInboxSyncEventHandler gmailInboxSyncEventHandler,
    UpdateSubscriptionQuantityEventHandler updateSubscriptionQuantityEventHandler,
    InboxMessageQueuedEventHandler inboxMessageQueuedEventHandler,
    ExportContactsEventHandler exportContactsEventHandler,
    BulkInboxMessageQueuedEventHandler bulkInboxMessageQueuedEventHandler,
    DeleteAuthorizationPolicyMessageHandler deleteAuthorizationPolicyMessageHandler,
    TranscriptCreatedEventHandler transcriptCreatedEventHandler,
    InboxMessageMovedEventHandler inboxMessageMovedEventHandler,
    ExportEventHandler exportEventHandler,
    ExportCompletedEventHandler exportCompletedEventHandler,
    ExternalCalendarSyncEventHandler externalCalendarSyncEventHandler,
    SyncInboxContactEventHandler syncInboxContactEventHandler,
    CalendarSubscriptionCreatedEventHandler calendarSubscriptionCreatedEventHandler,
    CalendarSubscriptionUpdatedEventHandler calendarSubscriptionUpdatedEventHandler,
    ReprocessTranscriptionsEventHandler reprocessTranscriptionsEventHandler,
    ReduceCopilotChatHistoryEventHandler reduceCopilotChatHistoryEventHandler,
    MigrateTemplateCollectionsEventHandler migrateTemplateCollectionsEventHandler,
    UpdatePublicTemplateProfessionsEventHandler updatePublicTemplateProfessionsEventHandler,
    RollbackPublicTemplateProfessionsEventHandler rollbackPublicTemplateProfessionsEventHandler,
    IServiceProvider serviceProvider
) : IEventHandlerDelegator
{
    public Task Delegate(EventMessage eventMessage)
    {
        switch (eventMessage.EventType)
        {
            case EventType.Task_Created:
                return syncCreatedTaskEventHandler.Handle(eventMessage.As<EventData<SyncCalendarJob>>());

            case EventType.Task_Deleted:
                return syncDeletedTaskEventHandler.Handle(eventMessage.As<EventData<SyncCalendarJob>>());

            case EventType.Task_Updated:
                return syncUpdatedTaskEventHandler.Handle(eventMessage.As<EventData<SyncCalendarJob>>());

            case EventType.ConnectedApp_Synced:
                return syncConnectedAppEventHandler.Handle(eventMessage.As<EventData<ConnectedAppEventData>>());

            case EventType.ImportExternalContactData:
                return importExternalContactDataEventHandler.Handle(eventMessage.As<EventData<ImportExternalContactData>>());

            case EventType.ImportContactsFromCsv:
                return importContactsFromCsvEventHandler.Handle(eventMessage.As<EventData<ImportContactsFromCsvData>>());

            case EventType.SeedTaskReminders:
                return seedTaskReminderEventHandler.Handle(eventMessage.As<EventData<SeedTaskRemindersEventData>>());

            case EventType.SeedContactDataLayoutSchema:
                return seedContactDataLayoutSchemaEventHandler.Handle(eventMessage.As<EventData<SeedContactDataLayoutSchemaEventData>>());

            case EventType.MigrateEmbeddedNoteFiles:
                return migrateEmbeddedNoteFilesEventHandler.Handle(eventMessage.As<EventData<MigrateEmbeddedNoteFilesEventData>>());

            case EventType.SecureEmbeddedNoteFiles:
                return secureEmbeddedNoteFilesEventHandler.Handle(eventMessage.As<EventData<SecureEmbeddedNoteFilesEventData>>());

            case EventType.MigrateEmbeddedTemplateFiles:
                return migrateEmbeddedTemplateFilesEventHandler.Handle(eventMessage.As<EventData<MigrateEmbeddedTemplateFilesEventData>>());

            case EventType.MigrateEmbeddedPublicTemplateFiles:
                return migrateEmbeddedPublicTemplateFilesEventHandler
                    .Handle(eventMessage.As<EventData<MigrateEmbeddedPublicTemplateFilesEventData>>());

            case EventType.SecureEmbeddedTemplateFiles:
                return secureEmbeddedTemplateFilesEventHandler
                    .Handle(eventMessage.As<EventData<SecureEmbeddedTemplateFilesEventData>>());

            case EventType.SecureEmbeddedPublicTemplateFiles:
                return secureEmbeddedPublicTemplateFilesEventHandler
                    .Handle(eventMessage.As<EventData<SecureEmbeddedPublicTemplateFilesEventData>>());

            case EventType.MessageAttachmentMetadata_Created:
                return uploadFilesToS3OnAttachmentMetadataCreatedEventHandler.Handle(eventMessage.As<EventData<MessageAttachmentMetadataCreatedEvent>>());

            case EventType.Bin_Emptied:
                return cleanUpS3FilesOnBinEmptiedEventHandler.Handle(eventMessage.As<EventData<BinEmptiedEventData>>()); // TODO: Remove this handler as its no longer used

            case EventType.Inbox_AttachmentDeleted:
                return cleanupAttachmentFilesMessageEventHandler.Handle(eventMessage.As<EventData<InboxMessageAttachmentDeletedEvent>>());

            case EventType.GmailMessagePubSubPush_Received:
                return gmailInboxSyncEventHandler.Handle(eventMessage.As<EventData<GmailInboxSyncMessageEventData>>());

            case EventType.Message_Queued:
                return inboxMessageQueuedEventHandler.Handle(eventMessage.As<EventData<InboxMessageQueuedEventData>>());

            case EventType.BulkMessage_Queued:
                return bulkInboxMessageQueuedEventHandler.Handle(eventMessage.As<EventData<BulkInboxMessageQueuedEventData>>());

            case EventType.Message_Moved:
                return inboxMessageMovedEventHandler.Handle(eventMessage.As<EventData<InboxMessageMovedEventData>>());

            case EventType.Sync_InboxContacts:
                return syncInboxContactEventHandler.Handle(eventMessage.As<EventData<SyncInboxContactEventData>>());

            case EventType.SeedUserExternalPersonAttribute:
                return seedUserExternalPersonAttributeEventHandler.Handle(eventMessage
                    .As<EventData<SeedExternalPersonAttributeEventData>>());

            case EventType.UpdateSubscriptionQuantity:
                return updateSubscriptionQuantityEventHandler.Handle(
                    eventMessage.As<EventData<UpdateSubscriptionMessage>>());

            case EventType.ExportContacts:
                return exportContactsEventHandler.Handle(eventMessage.As<EventData<ExportContactsEvent>>());

            case EventType.ExportContactsCompleted:
                return exportContactsCompletedEventHandler.Handle(
                    eventMessage.As<EventData<ExportContactsCompletedEvent>>());

            case EventType.DeleteAuthorizationPolicy:
                return deleteAuthorizationPolicyMessageHandler.Handle(eventMessage.As<EventData<DeleteAuthorizationPolicyMessage>>());

            case EventType.TranscriptCreated:
                return transcriptCreatedEventHandler.Handle(eventMessage.As<EventData<TranscriptCreatedEventData>>());

            case EventType.ExportJob:
                return exportEventHandler.Handle(eventMessage.As<EventData<ExportCreatedJobEvent>>());

            case EventType.ExportCompletedJob:
                return exportCompletedEventHandler.Handle(eventMessage.As<EventData<ExportCompletedJobEvent>>());

            case EventType.ExternalCalendarSync:
                return externalCalendarSyncEventHandler.Handle(eventMessage.As<EventData<ExternalCalendarSyncEventData>>());

            case EventType.CalendarSubscriptionCreated:
                return calendarSubscriptionCreatedEventHandler.Handle(eventMessage.As<EventData<CalendarSubscriptionCreatedEvent>>());

            case EventType.CalendarSubscriptionUpdated:
                return calendarSubscriptionUpdatedEventHandler.Handle(eventMessage.As<EventData<CalendarSubscriptionUpdatedEvent>>());

            case EventType.ReprocessTranscriptions:
                return reprocessTranscriptionsEventHandler.Handle(eventMessage.As<EventData<ReprocessTranscriptionsEventData>>());

            case EventType.ReduceCopilotChatHistory:
                return reduceCopilotChatHistoryEventHandler.Handle(eventMessage.As<EventData<ReduceCopilotChatHistoryEventData>>());

            case EventType.MigrateAnnotatedFiles:
                return migrateAnnotatedNoteImagesEventHandler.Handle(eventMessage.As<EventData<MigrateAnnotatedNoteImagesEventData>>());

            case EventType.MigrateTemplateAnnotatedFiles:
                return migrateAnnotatedTemplateImagesEventHandler.Handle(eventMessage.As<EventData<MigrateAnnotatedTemplateImagesEventData>>());
            
            case EventType.MigrateTemplateCollections:
                return migrateTemplateCollectionsEventHandler.Handle(eventMessage.As<EventData<MigrateTemplateCollectionEventData>>());
            
            case EventType.UpdatePublicTemplateProfessions:
                return updatePublicTemplateProfessionsEventHandler.Handle(eventMessage.As<EventData<UpdatePublicTemplateProfessionsEventData>>());
            
            case EventType.RollbackPublicTemplateProfessions:
                return rollbackPublicTemplateProfessionsEventHandler.Handle(eventMessage.As<EventData<RollbackPublicTemplateProfessionsEventData>>());

            //TODO: Remove after all message is handled and no nwew messages are created
            case EventType.Billing_Account_Updated:
                Log.Warning("Billing_Account_Updated event is deprecated and should not be used.");
                return Task.CompletedTask;
            default:
                return HandleRegisteredEvent(eventMessage);
        }
    }

    private async Task HandleRegisteredEvent(EventMessage eventMessage)
    {
        var handler = serviceProvider.GetKeyedService<IEventHandler>(eventMessage.EventType);
        if (handler is null)
        {
            throw new InvalidOperationException($"Message type {eventMessage.EventType} has no handler");
        }

        await handler.Handle(eventMessage);
    }
}
