﻿using System;

namespace carepatron.core.Events.Events
{
    public enum EventType
    {
        Unknown = 0,

        // tasks
        Task_Created = 1,
        Task_Updated = 2,
        Task_Deleted = 3,

        // authorization
        DeleteAuthorizationPolicy = 5,

        // connected apps
        ConnectedApp_Synced = 10,

        // payments
        Payment_Created = 20,
        GenerateServiceReceipt = 21,

        // referrals
        ProviderReferralActivityCheck = 22,
        ProviderReferralCompleted = 23,
        Transfer_Risk_Assessment = 24,
        Refund_Review_Timeout = 25,

        // billing
        Billing_Account_Updated = 30,
        SavePayout = 31,

        // provider
        //Provider_Provisioned = 40,

        UpdateSubscriptionQuantity = 43,

        // contact imports
        ImportExternalContactData = 71,
        ImportContactsFromCsv = 72,
        ImportContactsFromAiCsv = 73,
        PreprocessImportContacts = 74,

        SeedTaskReminders = 80,
        SeedContactDataLayoutSchema = 81,

        UpdatePublicTemplateProfessions = 88,
        RollbackPublicTemplateProfessions = 89,
        ValidatePublicTemplateProfessions = 90,
        MigrateEmbeddedNoteFiles = 100,
        SecureEmbeddedNoteFiles = 101,
        MigrateEmbeddedTemplateFiles = 102,
        MigrateEmbeddedPublicTemplateFiles = 103,
        SecureEmbeddedTemplateFiles = 104,
        SecureEmbeddedPublicTemplateFiles = 105,
        MigrateTemplateCollections = 107,
        
        // invoice
        Migrate_Invoice_Billable = 106,

        //inbox attachments
        MessageAttachmentMetadata_Created = 200,
        Conversation_Deleted = 201,

        [Obsolete("Remove this enum as its no longer used")]
        Bin_Emptied = 202,
        Inbox_AttachmentDeleted = 203,
        Message_Queued = 204,
        BulkMessage_Queued = 205,
        Message_Moved = 206,
        BulkMessage_Moved = 207,
        Message_Received = 208,
        GmailMessagePubSubPush_Received = 210,
        Sync_InboxContacts = 211,

        OutlookMessageChangeNotification_Received = 220,

        ChatParticipant_Deleted = 230,

        SeedUserExternalPersonAttribute = 300,

        [Obsolete("will move to ExportJob")]
        ExportContacts = 350,
        [Obsolete("will move to ExportCompletedJob")]
        ExportContactsCompleted = 351,

        //calendars
        ExternalCalendarSync = 400,
        [Obsolete("Use CalendarSubscriptionWebHook instead")]
        CalendarSubscriptionCreated = 401,
        [Obsolete("Use CalendarSubscriptionWebHook instead")]
        CalendarSubscriptionUpdated = 402,
        CalendarSubscriptionWebHook = 403,
        CalendarSubscriptionResource = 404,

        TranscriptCreated = 450,
        ReprocessTranscriptions = 455,
        TranscriptionUploadCompleted = 470,
        TranscriptionPendingCompletion = 480,
        TranscribeAudioFiles = 500,
        TemplateImport = 510,

        ExportJob = 600,
        ExportCompletedJob = 601,

        MergeContactForBilling = 700,

        // Trash and Restore
        DeleteTrashItems = 800,
        RestoreTrashItems = 801,

        ReduceCopilotChatHistory = 900,
        MigrateAnnotatedFiles = 910,
        MigrateTemplateAnnotatedFiles = 920,

        // Clearing house events
        ProcessClaimRejected = 1000,
        ProcessClaimAcknowledged = 1001
    }
}
