using System;
using System.Linq;
using System.Numerics;
using System.Text;
using Serilog;

namespace carepatron.core.Utilities
{
    /// <summary>
    /// Provides methods to encode and decode GUIDs using Base36 encoding (case-insensitive alphanumeric)
    /// </summary>
    public static class Base36GuidEncoder
    {
        private static readonly char[] Base36Chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ".ToCharArray();
        private static readonly int[] CharToValue = new int[256]; // ASCII lookup table
        private const int Base36Length = 25; // Fixed length for consistent GUID encoding

        static Base36GuidEncoder()
        {
            // Initialize lookup table for faster character to value conversion
            for (int i = 0; i < CharToValue.Length; i++)
                CharToValue[i] = -1; // Invalid by default

            for (int i = 0; i < Base36Chars.Length; i++)
            {
                CharToValue[Base36Chars[i]] = i;
                // Handle lowercase as well
                if (Base36Chars[i] >= 'A' && Base36Chars[i] <= 'Z')
                    CharToValue[Base36Chars[i] + 32] = i; // lowercase equivalent
            }
        }
        /// <summary>
        /// Encodes a GUID to a Base36 string
        /// </summary>
        /// <param name="guid">The GUID to encode</param>
        /// <returns>A 25-character Base36 encoded string</returns>
        public static string Encode(Guid guid)
        {
            if (guid == Guid.Empty)
                return new string('0', Base36Length);

            // Convert GUID to byte array and then to BigInteger
            Span<byte> bytes = stackalloc byte[17]; // 16 bytes + 1 zero byte
            guid.TryWriteBytes(bytes);
            bytes[16] = 0; // Ensure positive BigInteger

            var value = new BigInteger(bytes);

            if (value == 0)
                return new string('0', Base36Length);

            // Pre-allocate result buffer
            Span<char> result = stackalloc char[Base36Length];
            int pos = Base36Length - 1;

            while (value > 0)
            {
                var remainder = (int)(value % 36);
                result[pos--] = Base36Chars[remainder];
                value = value / 36;
            }

            // Fill remaining positions with zeros
            while (pos >= 0)
                result[pos--] = '0';

            return new string(result);
        }

        /// <summary>
        /// Decodes a Base36 string back to a GUID
        /// </summary>
        /// <param name="base36String">The Base36 encoded string</param>
        /// <returns>The decoded GUID</returns>
        /// <exception cref="ArgumentException">Thrown when the input string is invalid</exception>
        public static Guid Decode(string base36String)
        {
            if (string.IsNullOrEmpty(base36String))
                throw new ArgumentException("Base36 string cannot be null or empty", nameof(base36String));

            if (base36String.Length > Base36Length)
                throw new ArgumentException($"Base36 string cannot be longer than {Base36Length} characters", nameof(base36String));

            // Check for all zeros quickly
            if (IsAllZeros(base36String))
                return Guid.Empty;

            var value = BigInteger.Zero;
            var baseValue = new BigInteger(36);

            // Use lookup table for faster character conversion
            for (int i = 0; i < base36String.Length; i++)
            {
                char c = base36String[i];
                if (c >= 256)
                    throw new ArgumentException("Base36 string contains invalid characters", nameof(base36String));

                int charValue = CharToValue[c];
                if (charValue == -1)
                    throw new ArgumentException("Base36 string contains invalid characters", nameof(base36String));

                value = value * baseValue + charValue;
            }

            // Convert back to byte array using span
            Span<byte> bytes = stackalloc byte[17];
            if (!value.TryWriteBytes(bytes, out int bytesWritten))
                throw new ArgumentException("Decoded value is too large for a GUID", nameof(base36String));

            // Handle the case where we have 17 bytes (with trailing zero)
            if (bytesWritten == 17 && bytes[16] == 0)
                bytesWritten = 16;
            else if (bytesWritten > 16)
                throw new ArgumentException("Decoded value is too large for a GUID", nameof(base36String));

            // Create GUID from the first 16 bytes
            return new Guid(bytes.Slice(0, 16));
        }

        /// <summary>
        /// Validates if a string is a valid Base36 encoded GUID
        /// </summary>
        /// <param name="base36String">The string to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidBase36Guid(string base36String)
        {
            if (string.IsNullOrEmpty(base36String))
                return false;

            if (base36String.Length > Base36Length)
                return false;

            var normalized = base36String.ToUpperInvariant();
            return normalized.All(c => Base36Chars.Contains(c));
        }

        public static bool TryParse(string base36String, out Guid guid)
        {
            try
            {
                guid = Decode(base36String);
                return true;
            }
            catch
            {
                Log.Warning("Failed to decode Base36 Guid string: {Base36String}", base36String);
                guid = Guid.Empty;
                return false;
            }
        }
        /// <summary>
        /// Fast check if string contains only zeros
        /// </summary>
        private static bool IsAllZeros(ReadOnlySpan<char> span)
        {
            for (int i = 0; i < span.Length; i++)
            {
                if (span[i] != '0')
                    return false;
            }
            return true;
        }
    }

}