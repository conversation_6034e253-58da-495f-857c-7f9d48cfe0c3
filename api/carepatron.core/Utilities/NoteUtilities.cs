﻿using carepatron.core.Application.ClientPortal.Models;
using carepatron.core.Application.Communications.Inbox.Extensions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Notes.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Schema.Models.Properties;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Application.Users.Models;
using carepatron.core.Application.Workspace.Providers.Models;
using carepatron.core.Constants;
using carepatron.core.Extensions;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Media;
using carepatron.core.Models.Notes;
using carepatron.core.Repositories.Files;
using carepatron.core.Repositories.Notes;
using carepatron.core.Repositories.Tasks;
using Microsoft.AspNetCore.Http.Internal;
using Newtonsoft.Json.Linq;
using Serilog.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;

namespace carepatron.core.Utilities
{
    public class NoteUtilities
    {
        public static void UnionAttachments(NoteDetail note, IFileStorageRepository fileStorageRepository)
        {
            // Ensure the collections are not null
            note.Attachments ??= new List<File>();
            note.FileAttachments ??= new List<NoteAttachment>();

            // Use dictionaries for efficient lookup and update
            var attachmentsById = note.Attachments.ToDictionary(a => a.Id);
            var fileAttachmentsById = note.FileAttachments.ToDictionary(fa => fa.Id);

            foreach (var attachment in note.Attachments)
            {
                if (!fileAttachmentsById.ContainsKey(attachment.Id))
                {
                    var newFileAttachment = new NoteAttachment
                    {
                        Id = attachment.Id,
                        NoteId = note.Id,
                        ProviderId = note.ProviderId,
                        FileName = attachment.FileName,
                        FileExtension = attachment.FileExtension,
                        FileSize = attachment.FileSize,
                        ContentType = attachment.ContentType,
                        IsEmbedded = attachment.IsEmbedded,
                        Url = attachment.Url,
                    };

                    note.FileAttachments.Add(newFileAttachment);
                }
            }

            // For backwards compatibility, add any attachments that are not in the note.Attachments collection
            foreach (var fileAttachment in note.FileAttachments)
            {
                if (!fileAttachment.IsEmbedded && !attachmentsById.ContainsKey(fileAttachment.Id))
                {
                    var newAttachment = new File
                    {
                        Id = fileAttachment.Id,
                        FileName = fileAttachment.FileName,
                        FileExtension = fileAttachment.FileExtension,
                        FileSize = fileAttachment.FileSize,
                        ContentType = fileAttachment.ContentType,
                        IsEmbedded = fileAttachment.IsEmbedded,
                        MediaType = fileAttachment.MediaType,
                        Url = fileAttachment.Url
                    };

                    note.Attachments.Add(newAttachment);
                }
            }

            foreach (var fileAttachment in note.FileAttachments)
            {
                // inline url
                fileAttachment.Url = fileStorageRepository.GeneratePresignedUrl(fileAttachment.Id, FileLocationType.Files, download: false);

                // download url
                fileAttachment.DownloadUrl = fileStorageRepository.GeneratePresignedUrl(fileAttachment.Id, FileLocationType.Files,
                    fileName: fileAttachment.FileName, download: true);
            }
        }

        public static void UnionAttachments(ClientPortalNote note, IFileStorageRepository fileStorageRepository)
        {
            // Ensure the collections are not null
            note.Attachments ??= new List<FileReference>();
            note.FileAttachments ??= new List<NoteAttachment>();

            // Use dictionaries for efficient lookup and update
            var attachmentsById = note.Attachments.ToDictionary(a => a.Id);
            var fileAttachmentsById = note.FileAttachments.ToDictionary(fa => fa.Id);

            foreach (var attachment in note.Attachments)
            {
                if (!fileAttachmentsById.ContainsKey(attachment.Id))
                {
                    var newFileAttachment = new NoteAttachment
                    {
                        Id = attachment.Id,
                        NoteId = note.Id,
                        ProviderId = note.Provider.Id,
                        FileName = attachment.FileName,
                        FileExtension = attachment.FileExtension,
                        FileSize = attachment.FileSize,
                        ContentType = attachment.ContentType,
                        IsEmbedded = attachment.IsEmbedded,
                        Url = attachment.Url,
                    };

                    note.FileAttachments.Add(newFileAttachment);
                }
            }

            // For backwards compatibility, add any attachments that are not in the note.Attachments collection
            foreach (var fileAttachment in note.FileAttachments)
            {
                if (!fileAttachment.IsEmbedded && !attachmentsById.ContainsKey(fileAttachment.Id))
                {
                    var newAttachment = new FileReference
                    {
                        Id = fileAttachment.Id,
                        FileName = fileAttachment.FileName,
                        FileExtension = fileAttachment.FileExtension,
                        FileSize = fileAttachment.FileSize,
                        ContentType = fileAttachment.ContentType,
                        IsEmbedded = fileAttachment.IsEmbedded,
                        Url = fileAttachment.Url
                    };

                    note.Attachments.Add(newAttachment);
                }
            }

            foreach (var fileAttachment in note.FileAttachments)
            {
                // inline url
                fileAttachment.Url = fileStorageRepository.GeneratePresignedUrl(fileAttachment.Id, FileLocationType.Files, download: false);

                // download url
                fileAttachment.DownloadUrl = fileStorageRepository.GeneratePresignedUrl(fileAttachment.Id, FileLocationType.Files,
                    fileName: fileAttachment.FileName, download: true);
            }
        }

        public static Note CreateOrUpdateNoteFromTemplate(Template template, Guid contactId, Guid providerId, Guid createdByPersonId, DateTime? occurenceDate, ApplyTemplateOption applyTemplateOption, Note targetNote = null)
        {
            var dateNow = DateTime.UtcNow;

            if (targetNote is null)
            {
                targetNote = new Note
                {
                    Id = Guid.NewGuid(),
                    ContactId = contactId,
                    ProviderId = providerId,
                    CreatedByPersonId = createdByPersonId,
                    LastUpdatedByPersonId = createdByPersonId,
                    Title = template.Title,
                    Content = template.Content,
                    CreatedDateTimeUtc = dateNow,
                    LastUpdatedDateTimeUtc = dateNow,
                    OccurrenceDateTimeUtc = occurenceDate ?? dateNow,
                    Status = NoteStatus.Published,
                    ContentJson = template.ContentJson,
                    RolesAccessibleBy = new List<RelationshipAccessType>
                    {
                        RelationshipAccessType.Careprovider_Admin
                    }
                };
            }
            else
            {
                targetNote.Title = !string.IsNullOrEmpty(targetNote.Title) ? targetNote.Title : template.Title;
                targetNote.Content = template.Content;
                targetNote.ContentJson = applyTemplateOption == ApplyTemplateOption.Append ? targetNote.ContentJson.MergeContents(template.ContentJson) : template.ContentJson;
                targetNote.LastUpdatedByPersonId = createdByPersonId;
                targetNote.LastUpdatedDateTimeUtc = dateNow;
                targetNote.OccurrenceDateTimeUtc = occurenceDate ?? dateNow;
            }
            
            return targetNote;
        }

        public static Note CreateOrUpdateNoteFromPublicTemplate(PublicTemplate publicTemplate, Guid contactId, Guid providerId, Guid createdByPersonId, DateTime? occurenceDate, ApplyTemplateOption applyTemplateOption, Note targetNote = null)
        {
            var dateNow = DateTime.UtcNow;
            
            if (targetNote is null)
            {
                targetNote = new Note
                {
                    Id = Guid.NewGuid(),
                    ContactId = contactId,
                    ProviderId = providerId,
                    CreatedByPersonId = createdByPersonId,
                    LastUpdatedByPersonId = createdByPersonId,
                    Title = publicTemplate.Title,
                    Content = publicTemplate.Content,
                    CreatedDateTimeUtc = dateNow,
                    LastUpdatedDateTimeUtc = dateNow,
                    OccurrenceDateTimeUtc = occurenceDate ?? dateNow,
                    Status = NoteStatus.Published,
                    ContentJson = publicTemplate.ContentJson,
                    RolesAccessibleBy = new List<RelationshipAccessType>
                    {
                        RelationshipAccessType.Careprovider_Admin
                    }
                };
            }
            else
            {
                targetNote.Title = !string.IsNullOrEmpty(targetNote.Title) ? targetNote.Title : publicTemplate.Title;
                targetNote.Content = publicTemplate.Content;
                targetNote.ContentJson = applyTemplateOption == ApplyTemplateOption.Append ? targetNote.ContentJson.MergeContents(publicTemplate.ContentJson) : publicTemplate.ContentJson;
                targetNote.LastUpdatedByPersonId = createdByPersonId;
                targetNote.LastUpdatedDateTimeUtc = dateNow;
                targetNote.OccurrenceDateTimeUtc = occurenceDate ?? dateNow;
            }

            return targetNote;
        }

        public static IList<NoteAttachment> CreateNoteAttachments(IEnumerable<CreateNoteAttachmentRequest> requests) => requests?.Select(CreateNoteAttachment).ToList() ?? Array.Empty<NoteAttachment>().ToList();

        public static NoteAttachment CreateNoteAttachment(CreateNoteAttachmentRequest request)
        {
            if (request == null)
            {
                return null;
            }

            return new NoteAttachment
            {
                Id = request.FileId,
                FileName = request.FileName,
                ContentType = request.ContentType,
                FileExtension = request.FileName.Split(".").DefaultIfEmpty("").LastOrDefault().ToLower(),
                FileSize = request.FileSize,
                Url = request.Url,
                IsEmbedded = request.IsEmbedded,
                MediaType = request.MediaType,
            };
        }

        public static IList<NoteFormField> CreateNoteFormFields(Template template, Note note)
        {
            var noteFormFields = new List<NoteFormField>();

            // Extract form field IDs from contentJson
            var contentJsonFormFields = template.ContentJson.GetObjectsByType("formField")
                .Select(x => new
                {
                    Id = x["attrs"]!["id"]!.ToString(),
                    Version = x["attrs"]!.Value<string>("version") ?? "1.0.0",
                    Type = x["attrs"]!.Value<string>("type") ?? "unknown"
                })
                .ToList();

            if (!contentJsonFormFields.Any()) return noteFormFields;

            // Get template form fields for lookup
            var templateFormFields = template.Form?.Fields?
                .Where(x => !x.Value.Deleted)
                .ToDictionary(x => x.Key.ToString(), x => x.Value) ?? new Dictionary<string, TemplateFormField>();

            foreach (var contentField in contentJsonFormFields)
            {
                NoteFormField noteFormField;

                if (templateFormFields.TryGetValue(contentField.Id, out var templateFormField))
                {
                    // Use values from the template form field
                    noteFormField = new NoteFormField
                    {
                        Id = Guid.NewGuid(),
                        NoteId = note.Id,
                        ProviderId = note.ProviderId,
                        Schema = templateFormField.Schema,
                        Version = templateFormField.Version,
                        Type = templateFormField.Type
                    };
                }
                else
                {
                    // Create a new form field using contentJson attributes and add default options
                    noteFormField = new NoteFormField
                    {
                        Id = Guid.NewGuid(),
                        NoteId = note.Id,
                        ProviderId = note.ProviderId,
                        Schema = GetDefaultSchema(contentField.Type),
                        Version = contentField.Version,
                        Type = contentField.Type
                    };
                }

                // Update contentJson with the new generated ID
                note.ContentJson.UpdateObjectsByType("formField", "id", contentField.Id, noteFormField.Id.ToString());
                noteFormFields.Add(noteFormField);
            }

            return noteFormFields;
        }

        public static IList<NoteFormField> CreateNoteFormFields(PublicTemplate publicTemplate, Note note)
        {
            var noteFormFields = new List<NoteFormField>();

            // Extract form field IDs from contentJson
            var contentJsonFormFields = publicTemplate.ContentJson.GetObjectsByType("formField")
                .Select(x => new
                {
                    Id = x["attrs"]!["id"]!.ToString(),
                    Version = x["attrs"]!.Value<string>("version") ?? "1.0.0",
                    Type = x["attrs"]!.Value<string>("type") ?? "unknown"
                })
                .ToList();

            if (!contentJsonFormFields.Any()) return noteFormFields;

            // Get template form fields for lookup
            var publicTemplateFormFields = publicTemplate.Form?.Fields?
                .Where(x => !x.Value.Deleted)
                .ToDictionary(x => x.Key.ToString(), x => x.Value) ?? new Dictionary<string, PublicTemplateFormField>();

            foreach (var contentField in contentJsonFormFields)
            {
                NoteFormField noteFormField;

                if (publicTemplateFormFields.TryGetValue(contentField.Id, out var templateFormField))
                {
                    // Use values from the template form field
                    noteFormField = new NoteFormField
                    {
                        Id = Guid.NewGuid(),
                        NoteId = note.Id,
                        ProviderId = note.ProviderId,
                        Schema = templateFormField.Schema,
                        Version = templateFormField.Version,
                        Type = templateFormField.Type
                    };
                }
                else
                {
                    // Create a new form field using contentJson attributes and add default options
                    noteFormField = new NoteFormField
                    {
                        Id = Guid.NewGuid(),
                        NoteId = note.Id,
                        ProviderId = note.ProviderId,
                        Schema = GetDefaultSchema(contentField.Type),
                        Version = contentField.Version,
                        Type = contentField.Type
                    };
                }

                // Update contentJson with the new generated ID
                note.ContentJson.UpdateObjectsByType("formField", "id", contentField.Id, noteFormField.Id.ToString());
                noteFormFields.Add(noteFormField);
            }

            return noteFormFields;
        }

        public static IList<NoteFormResponse> CreateClientInformationResponses(Note note, Contact contact, MergedDataSchema contactSchema, Guid providerId, Guid createdByPersonId,
            DateTime dateNow, IEnumerable<NoteFormField> clientInfoFormFields, string locale, string timeZone)
        {
            var formResponses = new List<NoteFormResponse>();

            var customFields = contactSchema.Properties[nameof(Contact.Fields)] as ObjectProperty;

            foreach (var formField in clientInfoFormFields)
            {
                // Skip processing if formField.Schema["clientFields"] does not exist
                if (!formField.Schema.ContainsKey("clientFields"))
                    continue;

                var formFieldResponse = new NoteFormResponse
                {
                    Id = Guid.NewGuid(),
                    FormFieldId = formField.Id,
                    NoteId = note.Id,
                    ProviderId = providerId,
                    CreatedByPersonId = createdByPersonId,
                    CreatedDateTimeUtc = dateNow,
                    UpdatedByPersonId = createdByPersonId,
                    UpdatedDateTimeUtc = dateNow,
                };

                var clientFields = formField.Schema["clientFields"]!.ToObject<JArray>();

                var defaultValues = formField.Schema.ContainsKey("defaultValues") ?
                    formField.Schema["defaultValues"]!.ToObject<JObject>() : null;

                var responseSchema = new JObject();

                foreach (var clientField in clientFields)
                {
                    var clientFieldProperty = clientField["property"]!.ToObject<string>();

                    if (!contactSchema.Properties.ContainsKey(clientFieldProperty) &&
                        !customFields!.Properties.ContainsKey(clientFieldProperty) ||
                        string.Equals(clientFieldProperty, "Email", StringComparison.OrdinalIgnoreCase))
                        continue;

                    var responseField = new JObject();
                    var responseFieldValue =
                        contactSchema.GetPropertyValue(contact, clientFieldProperty) ?? string.Empty;
                    responseField.Add("type", clientField["fieldType"]);

                    var responseValue = JToken.FromObject(responseFieldValue);

                    // Set default value from template if client data is empty
                    if (string.IsNullOrEmpty(responseValue?.ToString()) &&
                        defaultValues != null && defaultValues.ContainsKey(clientFieldProperty))
                    {
                        responseField.Add("value", formField.Schema["defaultValues"]![clientFieldProperty]!["value"]);
                    }
                    else
                    {
                        responseField.Add("value", responseValue);
                    }

                    responseSchema[clientFieldProperty] = responseField;
                }

                formFieldResponse.Response = responseSchema;

                formResponses.Add(formFieldResponse);
            }

            return formResponses;
        }

        public static async Task<List<NoteAttachment>> CreateNoteAttachments(
            Note note,
            TemplateAttachment[] templateAttachments,
            SimplePerson createdByPerson,
            DateTime dateNow,
            IFileStorageRepository fileStorageRepository)
        {
            var contentJsonSb = new StringBuilder(note.ContentJson.ToString());
            var contentSb = new StringBuilder(note.Content);
            var newNoteAttachments = new List<NoteAttachment>();

            foreach (var templateAttachment in templateAttachments)
            {
                var newFileId = Guid.NewGuid();

                var result =
                    await fileStorageRepository.CopyS3ObjectAsync(templateAttachment.Id.ToString(), newFileId.ToString());

                if (!result)
                {
                    throw new ExecutionException(new ValidationError(Errors.CopyS3ObjectFailedCode,
                        Errors.CopyS3ObjectFailedDetail, ValidationType.InternalServer));
                }

                var newNoteAttachment = new NoteAttachment
                {
                    Id = newFileId,
                    NoteId = note.Id,
                    ProviderId = note.ProviderId,
                    FileName = templateAttachment.FileName,
                    FileExtension = templateAttachment.FileExtension,
                    FileSize = templateAttachment.FileSize,
                    ContentType = templateAttachment.ContentType,
                    MediaType = templateAttachment.MediaType,
                    IsEmbedded = templateAttachment.IsEmbedded,
                    CreatedByPersonId = createdByPerson.Id,
                    CreatedByPerson = createdByPerson,
                    CreatedDateTimeUtc = dateNow,
                    Url = templateAttachment.Url.Replace(templateAttachment.Id.ToString(), newFileId.ToString()),
                };

                if (newNoteAttachment.IsEmbedded)
                {
                    contentJsonSb = contentJsonSb
                        .Replace(templateAttachment.Id.ToString(), newNoteAttachment.Id.ToString());

                    contentSb = contentSb
                        .Replace(templateAttachment.Id.ToString(), newNoteAttachment.Id.ToString());
                }

                newNoteAttachments.Add(newNoteAttachment);
            }

            note.ContentJson = JObject.Parse(contentJsonSb.ToString());
            note.Content = contentSb.ToString();

            return newNoteAttachments;
        }

        public static async Task<List<NoteAttachment>> CreateNoteAttachments(
            Note note,
            PublicTemplateAttachment[] publicTemplateAttachments,
            SimplePerson createdByPerson,
            DateTime dateNow,
            IFileStorageRepository fileStorageRepository)
        {
            var contentJsonSb = new StringBuilder(note.ContentJson.ToString());
            var contentSb = new StringBuilder(note.Content);
            var newNoteAttachments = new List<NoteAttachment>();

            foreach (var publicTemplateAttachment in publicTemplateAttachments)
            {
                var newFileId = Guid.NewGuid();

                var result =
                    await fileStorageRepository.CopyS3ObjectAsync(publicTemplateAttachment.Id.ToString(), newFileId.ToString());

                if (!result)
                {
                    throw new ExecutionException(new ValidationError(Errors.CopyS3ObjectFailedCode,
                        Errors.CopyS3ObjectFailedDetail, ValidationType.InternalServer));
                }

                var newNoteAttachment = new NoteAttachment
                {
                    Id = newFileId,
                    NoteId = note.Id,
                    ProviderId = note.ProviderId,
                    FileName = publicTemplateAttachment.FileName,
                    FileExtension = publicTemplateAttachment.FileExtension,
                    FileSize = publicTemplateAttachment.FileSize,
                    ContentType = publicTemplateAttachment.ContentType,
                    MediaType = publicTemplateAttachment.MediaType,
                    IsEmbedded = publicTemplateAttachment.IsEmbedded,
                    CreatedByPersonId = createdByPerson.Id,
                    CreatedByPerson = createdByPerson,
                    CreatedDateTimeUtc = dateNow,
                    Url = publicTemplateAttachment.Url.Replace(publicTemplateAttachment.Id.ToString(), newFileId.ToString()),
                };

                if (newNoteAttachment.IsEmbedded)
                {
                    contentJsonSb = contentJsonSb
                        .Replace(publicTemplateAttachment.Id.ToString(), newNoteAttachment.Id.ToString());

                    contentSb = contentSb
                        .Replace(publicTemplateAttachment.Id.ToString(), newNoteAttachment.Id.ToString());
                }

                newNoteAttachments.Add(newNoteAttachment);
            }

            note.ContentJson = JObject.Parse(contentJsonSb.ToString());
            note.Content = contentSb.ToString();

            return newNoteAttachments;
        }

        public static void RemoveAiPromptObjects(Note note)
        {
            var aiPrompts = note.ContentJson.GetObjectsByType("aiSmartPrompt").ToList();

            if (!aiPrompts.Any())
                return;

            var aiPromptIds = aiPrompts.Select(x => x["attrs"]["id"]!.ToObject<Guid>()).ToList();
            foreach (var aiPromptId in aiPromptIds)
            {
                note.ContentJson.RemoveObjectsByType("aiSmartPrompt", "id", aiPromptId.ToString());
            }
        }

        public static (List<NoteFormField>, List<NoteFormResponse>) CreateFormFieldsAndResponsesCopy(NoteDetail sourceNote, Note targetNote)
        {
            var sourceNoteFormFields = sourceNote.Form.Fields.Values.Where(x => !x.Deleted).ToList();
            var noteFormFields = new List<NoteFormField>();
            var noteFormResponses = new List<NoteFormResponse>();
            foreach (var targetNoteFormFieldObject in targetNote.ContentJson.GetObjectsByType("formField"))
            {
                if (!Guid.TryParse(targetNoteFormFieldObject["attrs"]!["id"]!.ToString(), out var id)) continue;
                
                sourceNote.Form.Responses.TryGetValue(id, out var responses);
                
                var sourceNoteFormField = sourceNoteFormFields.FirstOrDefault(x => x.Id == id);

                if (sourceNoteFormField is null)
                {
                    continue;
                }

                var (formField, response) = CreateFormFieldAndResponseCopy(targetNoteFormFieldObject, sourceNoteFormField, responses?.DefaultIfEmpty().MaxBy(x => x?.CreatedDateTimeUtc) , sourceNote, targetNote);
                
                noteFormFields.Add(formField);

                if (response is not null)
                {
                    noteFormResponses.Add(response);
                }
            }

            return (noteFormFields, noteFormResponses);
        }

        private static (NoteFormField, NoteFormResponse) CreateFormFieldAndResponseCopy(JToken targetNoteFormFieldObject, NoteFormField sourceNoteFormField, NoteFormResponse sourceNoteFormResponse, NoteDetail note, Note noteCopy)
        {
            var targetNoteFormFieldType = targetNoteFormFieldObject["attrs"]!["type"]!.ToString();
            var targetNoteFormFieldVersion = targetNoteFormFieldObject["attrs"]!["version"]!.ToString();

            if (targetNoteFormFieldType == FormFieldType.ClientInfo.ToString())
            {
                UpdateClientInfoFormFieldSchema(note, sourceNoteFormField, sourceNoteFormField.Schema);
            }

            sourceNoteFormField.Schema["id"] = Guid.NewGuid();
                
            var formField = new NoteFormField
            {
                Id = Guid.NewGuid(),
                NoteId = noteCopy.Id,
                ProviderId = noteCopy.ProviderId,
                Schema = sourceNoteFormField.Schema,
                Version = targetNoteFormFieldVersion,
                Type = targetNoteFormFieldType
            };
            
            //not every field has a required response, filter out signature response
            NoteFormResponse formResponse = null;
            if (sourceNoteFormResponse is not null && sourceNoteFormField.Type != FormFieldType.Signature.ToString())
            {
                formResponse = new NoteFormResponse
                {
                    Id = Guid.NewGuid(),
                    NoteId = noteCopy.Id,
                    ProviderId = noteCopy.ProviderId,
                    CreatedByPersonId = noteCopy.CreatedByPersonId,
                    UpdatedByPersonId = noteCopy.CreatedByPersonId,
                    CreatedDateTimeUtc = DateTime.UtcNow,
                    UpdatedDateTimeUtc = DateTime.UtcNow,
                    FormFieldId = formField.Id,
                    Response = sourceNoteFormResponse.Response
                };
            }

            targetNoteFormFieldObject["attrs"]!["id"] = formField.Id;

            return (formField, formResponse);
        }

        private static void UpdateClientInfoFormFieldSchema(NoteDetail note, NoteFormField formField, JObject schema)
        {
            if (!note.Form.Responses.TryGetValue(formField.Id, out var formResponses) ||
                formResponses?.LastOrDefault() is null) return;

            var latestResponse = formResponses.Last();
            
            if (schema["defaultValues"] is not JObject defaultValues)
            {
                return;
            }
            
            foreach (var response in latestResponse.Response)
            {
                var responseValue = response.Value["value"];
                if (defaultValues.ContainsKey(response.Key))
                {
                    schema["defaultValues"]![response.Key]!["value"] = responseValue;
                }
                else
                {
                    var newProperty = new JObject(new JProperty("value", responseValue));
                    defaultValues.Add(response.Key, newProperty);
                }
            }
        }

        public static async Task<Tuple<List<NoteAttachment>, List<NoteSignature>>> CloneNoteAttachments(Note noteCopy, Note originalNote, NoteAttachment[] noteAttachments, NoteSignature[] noteSignatures, SimplePerson createdByPerson, DateTime dateNow, IFileStorageRepository fileStorageRepository)
        {
            var contentJsonSb = new StringBuilder(noteCopy.ContentJson.ToString());
            var contentSb = new StringBuilder(noteCopy.Content);
            var newNoteAttachments = new List<NoteAttachment>();
            var newNoteSignatures = new List<NoteSignature>();

            foreach (var attachment in noteAttachments)
            {
                var newFileId = Guid.NewGuid();

                var result =
                    await fileStorageRepository.CopyS3ObjectAsync(attachment.Id.ToString(), newFileId.ToString());

                if (!result)
                {
                    throw new ExecutionException(new ValidationError(Errors.CopyS3ObjectFailedCode,
                        Errors.CopyS3ObjectFailedDetail, ValidationType.InternalServer));
                }

                var newNoteAttachment = new NoteAttachment
                {
                    Id = newFileId,
                    NoteId = noteCopy.Id,
                    ProviderId = noteCopy.ProviderId,
                    FileName = attachment.FileName,
                    FileExtension = attachment.FileExtension,
                    FileSize = attachment.FileSize,
                    ContentType = attachment.ContentType,
                    MediaType = attachment.MediaType,
                    IsEmbedded = attachment.IsEmbedded,
                    CreatedByPersonId = createdByPerson.Id,
                    CreatedByPerson = createdByPerson,
                    CreatedDateTimeUtc = dateNow,
                    Url = attachment.Url.Replace(attachment.Id.ToString(), newFileId.ToString()),
                };

                var signature = noteSignatures.FirstOrDefault(x => x.FileId == attachment.Id);
        
                if (signature is not null)
                {
                    var newSignature = new NoteSignature
                    {
                        Id = Guid.NewGuid(),
                        Remarks = signature.Remarks,
                        Timestamp = signature.Timestamp,
                        FileId = newFileId,
                        NoteId = noteCopy.Id,
                        ProviderId = noteCopy.ProviderId,
                        SignedByPersonId = signature.SignedByPersonId
                    };
                    
                    newNoteSignatures.Add(newSignature);

                    contentJsonSb = contentJsonSb
                        .Replace(signature.Id.ToString(), newSignature.Id.ToString());
                    
                    contentSb = contentSb
                        .Replace(signature.Id.ToString(), newSignature.Id.ToString());
                }

                if (newNoteAttachment.IsEmbedded)
                {
                    contentJsonSb = contentJsonSb
                        .Replace(attachment.Id.ToString(), newNoteAttachment.Id.ToString())
                        .Replace(originalNote.Id.ToString(), noteCopy.Id.ToString());
                    
                    contentSb = contentSb
                        .Replace(originalNote.Id.ToString(), noteCopy.Id.ToString())
                        .Replace(attachment.Id.ToString(), newNoteAttachment.Id.ToString());
                }
                
                newNoteAttachments.Add(newNoteAttachment);
            }

            noteCopy.ContentJson = JObject.Parse(contentJsonSb.ToString());
            noteCopy.Content = contentSb.ToString();

            return new Tuple<List<NoteAttachment>, List<NoteSignature>>(newNoteAttachments, newNoteSignatures);
        }

        public static IEnumerable<NoteFormField> FilterNoteFormFields(JObject contentJson, IList<NoteFormField> formFields)
        {
            if (contentJson.HasValues == false) return Array.Empty<NoteFormField>();

            var formFieldIds = contentJson.GetObjectsByType("formField")
                .Select(x => x["attrs"]!["id"]!.ToString())
                .ToHashSet();

            return formFields.Where(x => formFieldIds.Contains(x.Id.ToString()));
        }

        private static JObject GetDefaultSchema(string fieldType)
        {
            var schema = new JObject
            {
                { "id", Guid.NewGuid().ToString() },
                { "title", "" },
                { "description", "" },
                { "validation", new JObject() },
                { "defaultValues", new JObject() },
                { "required", false }
            };

            switch (fieldType)
            {
                case nameof(FormFieldType.LinearScale):
                    schema["min"] = 1;
                    schema["max"] = 5;
                    schema["options"] = new JArray
                    {
                        new JObject { { "value", 1 }, { "label", "" } },
                        new JObject { { "value", 2 }, { "label", "" } },
                        new JObject { { "value", 3 }, { "label", "" } },
                        new JObject { { "value", 4 }, { "label", "" } },
                        new JObject { { "value", 5 }, { "label", "" } }
                    };
                    break;

                case nameof(FormFieldType.SingleChoice):
                case nameof(FormFieldType.MultipleChoice):
                    schema["options"] = new JArray
                    {
                        new JObject { { "value", "option-1" }, { "label", "" } },
                        new JObject { { "value", "option-2" }, { "label", "" } }
                    };
                    break;
            }

            return schema;
        }
    }
}