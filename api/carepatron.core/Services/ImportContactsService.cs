﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Threading.Tasks;
using carepatron.core.Abstractions;
using carepatron.core.Application.Contacts;
using carepatron.core.Application.Contacts.Abstractions;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Contacts.Utilities;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Schema.Models.Properties;
using carepatron.core.Application.Schema.Services;
using carepatron.core.Extensions;
using carepatron.core.Models.Common;
using carepatron.core.Models.Media;
using carepatron.core.Models.Tags;
using carepatron.core.Repositories.Files;
using carepatron.core.Repositories.Schema;
using carepatron.core.Repositories.Tags;
using carepatron.core.Utilities;
using Newtonsoft.Json;
using Serilog;

namespace carepatron.core.Services;

public interface IImportContactsService
{
    Task<DataSchema> UpdateDataSchemaAndLayout(
        DataSchema customDataSchema,
        Guid providerId
    );

    Task<(DataSchema, LayoutSchema)> UpdateDataSchemaAndLayout(
        Guid providerId,
        DataSchema customDataSchema,
        LayoutSchema customLayoutSchema
    );

    Task<MappedImportContact> MapColumnsToContacts(
        List<Dictionary<string, string>> rows,
        Guid providerId,
        DataSchema customDataSchema,
        List<ImportContactsOption> mappedColumns,
        bool supportCustomType = false,
        bool isClient = true
    );

    Task SaveMappedContacts(Guid providerId, ContactImportSummary importSummary, MappedImportContact import, bool isClient = true);

    Task<(List<string> Columns, List<Dictionary<string,string>> Rows, string CsvString)> ReadCsv(Guid fileId, string fileName, int maxRows);

    Task<(List<string> Columns, List<Dictionary<string, string>> Rows)> GetCsvData(Guid fileId, string fileName, int maxRows);
}

public class ImportContactsService(
    IContactRepository contactRepository,
    ITagRepository tagRepository,
    ISchemaService schemaService,
    ISchemaRepository schemaRepository,
    IFileStorageRepository fileStorageRepository,
    IFileRepository fileRepository,
    IUnitOfWork unitOfWork) : IImportContactsService
{
    private const int BatchSize = 300;

    public async Task<MappedImportContact> MapColumnsToContacts(
        List<Dictionary<string, string>> rows,
        Guid providerId,
        DataSchema dataSchema,
        List<ImportContactsOption> mappedColumns,
        bool supportCustomType = false,
        bool isClient = true
    )
    {
        var now = DateTime.UtcNow;
        var contactsToCreate = new List<Contact>();
        var spreadSheetColumnToTagMap = new Dictionary<string, Tag>();

        // temporary fix
        var schema = dataSchema is not null
            ? dataSchema.CloneAndMergeToCoreProperties(SchemaTypes.ContactSchema, providerId)
            : await schemaService.GetDataSchema(SchemaTypes.ContactSchema, providerId);

        var statusProperty = schema.GetProperty<OptionSetV2Property>(
            ContactCoreSchema.PropertyId.Status
        );
        var defaultContactStatus = statusProperty
            .GetDefaultGroupOptionSetValue(ContactCoreSchema.StatusGroup.Active)
            ?.Id;
        var preferredLanguageProperty = schema.GetProperty<OptionSetV2Property>(
            ContactCoreSchema.PropertyId.PreferredLanguage
        );
        var sexProperty = schema.GetProperty<OptionSetV2Property>(
            ContactCoreSchema.PropertyId.Sex
        );
        var excludedFields = new[] { "Fields", "AssignedStaff", "Tags" };
        var schemeCoreFields = schema.Properties.Where(x => !excludedFields.Contains(x.Key)).ToDictionary();
        var schemaCustomFields = schema.GetCustomFields();

        var existingStatuses = statusProperty
            .Options.GroupBy(x => x.Value.DisplayName, x => x.Value.Id)
            .ToDictionary(x => x.Key, x => x.First());

        var statusesToImport = new Dictionary<string, OptionSetValue>();

        PropertyType[] allowedPropertyTypesForMultiple =
        [
            PropertyType.Phone,
            PropertyType.Email,
            PropertyType.Address,
            PropertyType.OptionSet,
        ];
        string[] notSupportedFieldsForMultiple = [nameof(Contact.Tags).ToLowerInvariant(), nameof(Contact.Status).ToLowerInvariant()];

        foreach (var row in rows)
        {
            Guid? contactId = null;
            if (row.ContainsKey(ContactsConstants.ImportContactTemporaryIdKey))
            {
                var tempId = row[ContactsConstants.ImportContactTemporaryIdKey];
                if (!string.IsNullOrEmpty(tempId) && Guid.TryParse(tempId, out var id))
                {
                    contactId = id;
                }
            }
            
            var mapCarepatronFieldToSpreadsheetValue = new Dictionary<string, dynamic>();
            var customFields = new Dictionary<string, dynamic>();

            // loop through known mapped columns
            foreach (var columnMap in mappedColumns)
            {
                var isCoreField = schemeCoreFields.Any(x => x.Key.Equals(columnMap.CarepatronFieldName, StringComparison.OrdinalIgnoreCase));
                var isCustomField = schemaCustomFields.Keys.Contains(columnMap.CarepatronFieldName, StringComparer.OrdinalIgnoreCase);
                var property = ImportContactsUtilities.GetFieldProperty(schemeCoreFields, schemaCustomFields, columnMap);

                // if the column can have multiple values i.e. tags
                if (columnMap.IsMultiple)
                {
                    var useCustomProperty = supportCustomType
                                            && property is not null
                                            && allowedPropertyTypesForMultiple.Contains(property.Type)
                                            && !notSupportedFieldsForMultiple.Contains(columnMap.CarepatronFieldName.ToLowerInvariant())
                                            && (isCoreField || isCustomField);

                    if (useCustomProperty)
                    {
                        HandleFieldWithCustomPropertyMultipleValues(
                            row,
                            mapCarepatronFieldToSpreadsheetValue,
                            customFields,
                            columnMap,
                            property,
                            isCoreField
                        );
                    }
                    else
                    {
                        HandleFieldWithMultipleValues(
                            row,
                            mapCarepatronFieldToSpreadsheetValue,
                            columnMap,
                            spreadSheetColumnToTagMap,
                            providerId
                        );
                    }
                }
                // single mapping i.e FirstName
                // check there's column in the spreadsheet that matches this mapped column
                else if (row.ContainsKey(columnMap.SpreadsheetFieldName))
                {
                    // make sure the field is a core field
                    if (isCoreField)
                    {
                        HandleCoreFields(
                            columnMap,
                            row,
                            mapCarepatronFieldToSpreadsheetValue,
                            statusesToImport,
                            existingStatuses,
                            defaultContactStatus,
                            preferredLanguageProperty,
                            sexProperty
                        );
                    }
                    // otherwise check if it's a custom field
                    else if (isCustomField)
                    {
                        if (supportCustomType && property is not null)
                        {
                            HandleCustomField(row, customFields, columnMap, property);
                        }
                        else
                        {
                            HandleCustomField(row, customFields, columnMap, schema);
                        }
                    }
                }
            }

            var json = JsonConvert.SerializeObject(mapCarepatronFieldToSpreadsheetValue);
            var contact = JsonConvert.DeserializeObject<Contact>(
                json,
                new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore }
            );

            contact.Id = contactId ?? Guid.NewGuid();
            contact.FirstName = contact.FirstName.GetValueOrEmpty();
            contact.LastName = contact.LastName.GetValueOrEmpty();
            contact.CreatedDateTimeUtc = now;
            contact.UpdatedDateTimeUtc = now;
            contact.ProviderId = providerId;
            contact.IsClient = isClient;
            contact.IsArchived = false;
            contact.Fields = customFields;

            if (string.IsNullOrEmpty(contact.Status))
                contact.Status = defaultContactStatus;

            contactsToCreate.Add(contact);
        }

        var orderIndex = statusProperty
            .Options.Where(o => o.Value.GroupPath == ContactCoreSchema.StatusGroup.Active)
            .Max(o => o.Value.OrderIndex);
        foreach (var statusKvp in statusesToImport)
        {
            var statusOption = statusKvp.Value;
            statusOption.OrderIndex = ++orderIndex;
            statusProperty.Options.Add(statusOption.Id, statusOption);
        }

        var mappedTags = await GetMappedTags(providerId, spreadSheetColumnToTagMap.Values.ToArray());
        Tag[] allTags = [..mappedTags.TagsToAdd, ..mappedTags.TagsToReactivate, ..mappedTags.ExistingTags];
        contactsToCreate.ForEach(x => x.Tags = SanitizeContactTags(x, allTags));

        return new(contactsToCreate, mappedTags, schema.Id, ContactCoreSchema.PropertyId.Status, statusProperty);
    }

    public async Task<DataSchema> UpdateDataSchemaAndLayout(
        DataSchema customDataSchema,
        Guid providerId
    )
    {
        // update data schema if there's any new fields
        DataSchema dataSchemaResult = null;
        if (customDataSchema?.Properties is not null && customDataSchema.Properties.Count > 0)
        {
            var dataLayoutSchema = await schemaRepository.GetDataLayoutsSchema(
                SchemaTypes.ContactSchema,
                providerId
            );
            dataSchemaResult = dataLayoutSchema.DataSchema;

            var requestDataSchema = customDataSchema;
            List<LayoutControl> layoutControlList = new();
            foreach (var requestDataSchemaProperty in requestDataSchema.Properties ?? new())
            {
                if (dataSchemaResult.DoesFieldExists(requestDataSchemaProperty.Key))
                    continue;

                dataSchemaResult.Properties.Add(
                    requestDataSchemaProperty.Key,
                    requestDataSchemaProperty.Value
                );

                layoutControlList.Add(
                    new LayoutControl { Property = requestDataSchemaProperty.Key, Width = 4 }
                );
            }

            await schemaRepository.SaveDataSchema(dataSchemaResult);

            var layoutSchemas = dataLayoutSchema.LayoutSchemas;
            var layoutControlArray = layoutControlList.ToArray();
            await InsertCustomFieldsToLayout(layoutControlArray, layoutSchemas);
        }

        return dataSchemaResult;
    }


    public async Task<(DataSchema, LayoutSchema)> UpdateDataSchemaAndLayout(
        Guid providerId,
        DataSchema customDataSchema,
        LayoutSchema customLayoutSchema
    )
    {
        if (customDataSchema is null)
            return (null, null);
        var dataLayoutSchema = await schemaRepository.GetDataLayoutsSchema(
            SchemaTypes.ContactSchema,
            providerId
        );
        if (dataLayoutSchema?.DataSchema is null)
            return (null, null);

        // update data schema if there's any new fields  
        var dataSchemaResult = dataLayoutSchema.DataSchema;

        if (customDataSchema?.Properties?.Any() ?? false)
        {
            var addedNewFields = await CreateNewCustomFields(dataSchemaResult, customDataSchema.Properties);

            // insert all new fields to the 'Other' layout container if no custom layout schema is provided  
            if (customLayoutSchema is null)
            {
                var layoutControls = addedNewFields.Select(x => new LayoutControl { Property = x.Key, Width = 4 }).ToArray();
                await InsertCustomFieldsToLayout(layoutControls, dataLayoutSchema.LayoutSchemas);
                return (dataSchemaResult, customLayoutSchema);
            }
        }

        await SaveLayoutSchema(providerId, dataSchemaResult, customLayoutSchema);

        return (dataSchemaResult, customLayoutSchema);
    }

    private async Task<Dictionary<string, Property>> CreateNewCustomFields(DataSchema existingSchema, Dictionary<string, Property> newFields)
    {
        Dictionary<string, Property> addedFields = new();
        foreach (var field in newFields)
        {
            if (existingSchema.DoesFieldExists(field.Key))
                continue;

            existingSchema.Properties.Add(field.Key, field.Value);
            addedFields.Add(field.Key, field.Value);
        }

        await schemaRepository.SaveDataSchema(existingSchema);

        return addedFields;
    }

    private async Task InsertCustomFieldsToLayout(LayoutControl[] layoutControls, LayoutSchema[] layoutSchemas, string layoutHeading = "Other")
    {
        foreach (var layoutSchema in layoutSchemas)
        {
            var otherLayoutContainer =
                layoutSchema
                    .Elements.Where(x =>
                        x is LayoutContainer layoutContainer
                        && layoutContainer.Heading == layoutHeading
                    )
                    .FirstOrDefault() as LayoutContainer;

            // create a new layout (e.g. 'Other') container and add the custom fields to it
            // otherwise append to existing 'Other' layout container elements
            if (otherLayoutContainer is null)
            {
                otherLayoutContainer = new LayoutContainer(layoutHeading, layoutControls, 6);
                layoutSchema.Elements = layoutSchema
                    .Elements.Concat(new LayoutElement[] { otherLayoutContainer })
                    .ToArray();
            }
            else
                otherLayoutContainer.Elements = otherLayoutContainer
                    .Elements.Concat(layoutControls)
                    .ToArray();

            await schemaRepository.SaveLayoutSchema(layoutSchema);
        }
    }

    // removes any layout controls that are not in the data schema before saving
    private async Task SaveLayoutSchema(Guid providerId, DataSchema dataSchema, LayoutSchema customLayoutSchema)
    {
        var mergedDataSchema = dataSchema.CloneAndMergeToCoreProperties(SchemaTypes.ContactSchema, providerId);

        foreach (var container in customLayoutSchema.Elements.OfType<LayoutContainer>())
        {
            var containerElements = container.Elements.ToList();
            foreach (var control in container.Elements)
            {
                var layoutControl = control as LayoutControl;
                if (layoutControl is null) continue;

                var property = mergedDataSchema.GetProperty(layoutControl.Property);
                if (property is not null) continue;

                containerElements.Remove(control);
            }

            container.Elements = containerElements.ToArray();
        }

        await schemaRepository.SaveLayoutSchema(customLayoutSchema);
    }

    public async Task SaveMappedContacts(Guid providerId, ContactImportSummary importSummary, MappedImportContact import, bool isClient = true)
    {
        await schemaRepository.UpdateDataSchemaProperty(
            import.StatusPropertyKey,
            import.StatusProperty,
            import.DataSchemaId,
            providerId
        );

        if (isClient)
        {
            await tagRepository.BulkCreate(import.MappedTags.TagsToAdd);
            await tagRepository.BulkReactivate(providerId, import.MappedTags.TagsToReactivate.Select(x => x.Id).ToArray());
        }

        var groupedImportContactsByEmail = isClient ? import.Contacts.Where(x => x.Email is not null).GroupBy(x => x.Email) : [];
        HashSet<Guid> duplicateContactIds = new();
        HashSet<Email> emailsToCheck = new();

        foreach (var group in groupedImportContactsByEmail)
        {
            var email = group.Key;
            var contacts = group.ToList();

            if (contacts.Count > 1) duplicateContactIds.UnionWith(contacts.Select(x => x.Id));
            else emailsToCheck.Add(email);
        }

        var existingContacts = await contactRepository.GetClientsByEmails(providerId, emailsToCheck.ToArray());

        var groupedExistingContactsByEmail = existingContacts
            .GroupBy(x => x.Email)
            .ToDictionary(x => x.Key, x => x.ToArray());

        Dictionary<Guid, HashSet<string>> filesMap = new();
        if (isClient && importSummary?.ImportType is ImportType.Advanced)
        {
            var files = await GetAllFiles(importSummary.Id);
            foreach (var file in files)
            {
                var fileSegments = file.Split('/');

                if (fileSegments.Length < 3) continue;

                var temporaryId = fileSegments[1];

                if (!Guid.TryParse(temporaryId, out var temporaryIdValue)) continue;

                if (!filesMap.ContainsKey(temporaryIdValue)) filesMap.Add(temporaryIdValue, []);

                filesMap[temporaryIdValue].Add(file);
            }
        }

        var contactsToImport = import.Contacts;
        var numberOfBatches = contactsToImport.Count / BatchSize;
        if (numberOfBatches == 0) numberOfBatches = 1;
        else if ((contactsToImport.Count % BatchSize) != 0) numberOfBatches += 1;

        for (int i = 0; i < numberOfBatches; i++)
        {
            var recordsToSkip = i * BatchSize;

            var batchContacts = contactsToImport
                .Skip(recordsToSkip)
                .Take(BatchSize)
                .ToList();

            var duplicates =  GetDuplicateContacts(
                batchContacts,
                duplicateContactIds,
                groupedExistingContactsByEmail
            );

            if (duplicates.Any())
                await contactRepository.AddDuplicates(duplicates.ToArray());

            if (filesMap.Any())
            {
                var contactFiles = await GetMappedContactFiles(
                    providerId,
                    importSummary.CreatedByPersonId,
                    batchContacts,
                    filesMap
                );

                if (contactFiles.Any())
                    await fileRepository.Create(contactFiles);
            }

            await contactRepository.BulkCreate(batchContacts);
            await unitOfWork.SaveUnitOfWork();
        }
    }

    private List<DuplicateContact> GetDuplicateContacts(
        List<Contact> contactsToImport,
        HashSet<Guid> duplicateContactIds,
        Dictionary<Email, Contact[]> groupedExistingContactsByEmail)
    {
        if (duplicateContactIds.Count == 0 && groupedExistingContactsByEmail.Count == 0) return [];

        List<DuplicateContact> duplicatesFromBatch = new();
        HashSet<Email> emailsFromExistingContacts = new();

        foreach (var contact in contactsToImport)
        {
            if (contact.Email is null) continue;

            if (duplicateContactIds.Contains(contact.Id))
            {
                duplicatesFromBatch.Add(CreateDuplicateContact(contact));
                continue;
            }

            if (!groupedExistingContactsByEmail.TryGetValue(contact.Email, out var existingContacts))
                continue;

            duplicatesFromBatch.Add(CreateDuplicateContact(contact));

            if (emailsFromExistingContacts.Contains(contact.Email))
                continue;

            foreach (var existingContact in existingContacts)
                duplicatesFromBatch.Add(CreateDuplicateContact(existingContact));

            emailsFromExistingContacts.Add(contact.Email);
        }

        return duplicatesFromBatch;
    }

    private DuplicateContact CreateDuplicateContact(Contact contact)
    {
        return new DuplicateContact
        {
            ProviderId = contact.ProviderId,
            ContactId = contact.Id
        };
    }

    private async Task<ContactFile[]> GetMappedContactFiles(Guid providerId, Guid createdByPersonId, List<Contact> contacts, Dictionary<Guid, HashSet<string>> filesMap)
    {
        List<ContactFile> contactFiles = new();
        foreach (var contact in contacts)
        {
            if (!filesMap.TryGetValue(contact.Id, out var files)) continue;

            foreach (var file in files)
            {
                var contactFile = await CreateContactFile(providerId, createdByPersonId, contact.Id, file);
                if (contactFile is null) continue;
                contactFiles.Add(contactFile);
            }
        }

        return contactFiles.ToArray();
    }

    private async Task<ContactFile> CreateContactFile(Guid providerId, Guid createdByPersonId, Guid contactId, string fileKey)
    {
        var newFileId = Guid.NewGuid();

        try
        {
            if (await fileStorageRepository.MoveObject(fileKey, newFileId.ToString(), FileLocationType.ClientImport, FileLocationType.Files))
            {
                var fileName = Path.GetFileName(fileKey);
                var mimeType = MimeTypes.GetMimeType(fileName);
                var fileExtension = Path.GetExtension(fileName);
                fileExtension = fileExtension.StartsWith(".") ? fileExtension[1..] : fileExtension;

                return new ContactFile
                {
                    Id = newFileId,
                    FileName = fileName,
                    FileExtension = fileExtension,
                    ContentType = mimeType,
                    ContactId = contactId,
                    ProviderId = providerId,
                    RolesAccessibleBy = [RelationshipAccessType.Careprovider_Admin],
                    CreatedByPersonId = createdByPersonId,
                    CreatedDateTimeUtc = DateTime.UtcNow,
                    LastUpdatedDateTimeUtc = DateTime.UtcNow,
                    LastUpdatedByPersonId = createdByPersonId
                };
            }

        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error creating contact file for contact {ContactId} with file key {FileKey}", contactId, fileKey);
        }

        return null;
    }

    private void HandleCoreFields(ImportContactsOption columnMap,
        Dictionary<string, string> row,
        Dictionary<string, dynamic> mapCarepatronFieldToSpreadsheetValue,
        Dictionary<string, OptionSetValue> statusesToImport,
        Dictionary<string, string> existingStatuses,
        string defaultContactStatus,
        OptionSetV2Property preferredLanguageProperty,
        OptionSetV2Property sexProperty)
    {
        if (columnMap.FieldOptions != ImportContactsFieldOption.WholeField && !string.IsNullOrEmpty(columnMap.Delimiter))
        {
            HandleFieldWithDelimiter(
                row,
                mapCarepatronFieldToSpreadsheetValue,
                columnMap
            );
        }
        else if (columnMap.CarepatronFieldName.ToLowerInvariant() == nameof(Contact.Ethnicity).ToLowerInvariant())
        {
            HandleEthnicityField(row, mapCarepatronFieldToSpreadsheetValue, columnMap);
        }
        else if (columnMap.CarepatronFieldName.ToLowerInvariant() == nameof(Contact.BirthDate).ToLowerInvariant())
        {
            HandleBirthDateField(row, mapCarepatronFieldToSpreadsheetValue, columnMap);
        }
        else if (columnMap.CarepatronFieldName.ToLowerInvariant() == nameof(Contact.PhoneNumber).ToLowerInvariant())
        {
            HandlePhoneNumberField(
                row,
                mapCarepatronFieldToSpreadsheetValue,
                columnMap
            );
        }
        else if (columnMap.CarepatronFieldName.ToLowerInvariant() == nameof(Contact.Status).ToLowerInvariant())
        {
            HandleStatusField(
                row,
                mapCarepatronFieldToSpreadsheetValue,
                columnMap,
                statusesToImport,
                existingStatuses,
                defaultContactStatus
            );
        }
        else if (columnMap.CarepatronFieldName.ToLowerInvariant() == nameof(Contact.Email).ToLowerInvariant())
        {
            HandleEmailField(row, mapCarepatronFieldToSpreadsheetValue, columnMap);
        }
        else if (columnMap.CarepatronFieldName.ToLowerInvariant() == nameof(Contact.PreferredLanguage).ToLowerInvariant())
        {
            HandleOptionProperty(row,
                mapCarepatronFieldToSpreadsheetValue,
                columnMap,
                preferredLanguageProperty);
        }
        else if (columnMap.CarepatronFieldName.ToLowerInvariant() == nameof(Contact.Sex).ToLowerInvariant())
        {
            HandleOptionProperty(row,
                mapCarepatronFieldToSpreadsheetValue,
                columnMap,
                sexProperty);
        }
        else
        {
            HandleSystemField(row, mapCarepatronFieldToSpreadsheetValue, columnMap);
        }
    }

    private void HandleFieldWithCustomPropertyMultipleValues(
        Dictionary<string, string> row,
        Dictionary<string, dynamic> mapCarepatronFieldToSpreadsheetValue,
        Dictionary<string, dynamic> customFields,
        ImportContactsOption columnMap,
        Property property,
        bool isCoreField
    )
    {
        var currentValues = (isCoreField ? mapCarepatronFieldToSpreadsheetValue : customFields);

        var rowValues = columnMap.SpreadsheetMultipleFieldNames.Select(x => ValueOrNull(row.GetValueOrDefault(x)))
            .Where(x => !string.IsNullOrEmpty(x))
            .Distinct()
            .ToList();

        var defaultSingleValue = rowValues.FirstOrDefault();

        if (property.Type == PropertyType.Phone && property is PhoneNumberProperty phoneProperty)
        {
            if (columnMap.CarepatronFieldName.ToLowerInvariant().Equals(nameof(Contact.PhoneNumber).ToLowerInvariant()))
            {
                mapCarepatronFieldToSpreadsheetValue.TryAdd(nameof(Contact.PhoneNumber), defaultSingleValue);

                var listValue = rowValues.Select(x => new ContactPhone
                    {
                        Id = Guid.NewGuid(),
                        PhoneNumber = x,
                        Type = PhoneNumberType.Home,
                        IsPrimary = x.Equals(defaultSingleValue)
                    })
                    .ToArray();

                mapCarepatronFieldToSpreadsheetValue.TryAdd(nameof(Contact.PhoneNumbers), listValue);
                return;
            }

            currentValues.TryAdd(columnMap.CarepatronFieldName, phoneProperty.Multiple ? rowValues : defaultSingleValue);
            return;
        }

        if (property.Type == PropertyType.Email && property is EmailProperty emailProperty)
        {
            var validEmails = rowValues.Where(x => IsValidEmail(x)).Select(x => new Email(x)).ToArray();
            var defaultEmail = validEmails.FirstOrDefault();
            
            if (columnMap.CarepatronFieldName.ToLowerInvariant().Equals(nameof(Contact.Email).ToLowerInvariant()))
            {
                mapCarepatronFieldToSpreadsheetValue.TryAdd(nameof(Contact.Email), defaultEmail);

                var listValue = validEmails
                    .Select(x => new ContactEmail
                    {
                        Id = Guid.NewGuid(),
                        Email = x,
                        Type = EmailType.Personal,
                        IsPrimary = x.Equals(defaultEmail)
                    })
                    .ToArray();

                mapCarepatronFieldToSpreadsheetValue.TryAdd(nameof(Contact.Emails), listValue);
                return;
            }

            currentValues.TryAdd(columnMap.CarepatronFieldName, emailProperty.Multiple ? validEmails : defaultEmail);
            return;
        }

        if (property.Type == PropertyType.Address && property is AddressProperty addressProperty)
        {
            Address addressValue = defaultSingleValue;
            Address[] addressValues = rowValues.Select(x => new Address { StreetAddress = x, }).ToArray();

            if (columnMap.CarepatronFieldName.ToLowerInvariant().Equals(nameof(Contact.Address).ToLowerInvariant()))
            {
                mapCarepatronFieldToSpreadsheetValue.TryAdd(nameof(Contact.Address), addressValue);

                var listValue = addressValues.Select(x => new ContactAddress()
                    {
                        Id = Guid.NewGuid(),
                        AddressDetails = new Address { StreetAddress = x },
                        Type = AddressType.Residential,
                        IsPrimary = x.StreetAddress.Equals(defaultSingleValue)
                    })
                    .ToArray();

                mapCarepatronFieldToSpreadsheetValue.TryAdd(nameof(Contact.Addresses), listValue);
                return;
            }

            currentValues.TryAdd(columnMap.CarepatronFieldName, addressProperty.Multiple ? addressValues : addressValue);
            return;
        }

        if (property.Type == PropertyType.OptionSet && property is OptionSetV2Property optionProperty)
        {
            List<OptionSetValue> selectedOptions = new();
            foreach (var value in rowValues)
            {
                var option = ImportContactsUtilities.GetOptionByIdOrName(optionProperty, value);
                if (option is null) continue;
                selectedOptions.Add(option);
            }

            currentValues.TryAdd(columnMap.CarepatronFieldName, optionProperty.Multiple ? selectedOptions : selectedOptions.FirstOrDefault());
        }
    }

    private void HandleFieldWithMultipleValues(
        Dictionary<string, string> row,
        Dictionary<string, dynamic> mapCarepatronFieldToSpreadsheetValue,
        ImportContactsOption columnMap,
        Dictionary<string, Tag> spreadSheetColumnToTagMap,
        Guid providerId
    )
    {
        foreach (var field in columnMap.SpreadsheetMultipleFieldNames)
        {
            // if the carepatron field hasn't been created yet - create it so we can just add to it
            if (!mapCarepatronFieldToSpreadsheetValue.ContainsKey(columnMap.CarepatronFieldName))
            {
                if (columnMap.CarepatronFieldName.ToLowerInvariant() == "tags")
                {
                    mapCarepatronFieldToSpreadsheetValue.Add(columnMap.CarepatronFieldName, new List<Tag>());
                }
                else
                {
                    mapCarepatronFieldToSpreadsheetValue.Add(columnMap.CarepatronFieldName, new List<string>());
                }
            }

            // if the spreadsheet row has a value for this field (some table cells will be empty)
            if (row.ContainsKey(field) && !string.IsNullOrWhiteSpace(row[field]))
            {
                var cellTableValue = row[field];

                if (columnMap.CarepatronFieldName.ToLowerInvariant() == "tags")
                {
                    if (!spreadSheetColumnToTagMap.ContainsKey(cellTableValue))
                    {
                        // add existing tag to this contact
                        var tag = new Tag(
                            Guid.NewGuid(),
                            providerId,
                            cellTableValue,
                            TagType.Client
                        );

                        spreadSheetColumnToTagMap.Add(tag.Title, tag);
                    }

                    var tags = mapCarepatronFieldToSpreadsheetValue[columnMap.CarepatronFieldName] as List<Tag>;
                    if (!tags.Contains(spreadSheetColumnToTagMap[cellTableValue]))
                    {
                        tags.Add(spreadSheetColumnToTagMap[cellTableValue]);
                    }
                }
                else
                {
                    (mapCarepatronFieldToSpreadsheetValue[columnMap.CarepatronFieldName] as List<string>).Add(cellTableValue);
                }
            }
        }
    }

    private void HandleFieldWithDelimiter(
        Dictionary<string, string> row,
        Dictionary<string, dynamic> mapCarepatronFieldToSpreadsheetValue,
        ImportContactsOption columnMap
    )
    {
        var value = ImportContactsUtilities.ExtractPartOfString(row[columnMap.SpreadsheetFieldName], columnMap);
        mapCarepatronFieldToSpreadsheetValue.TryAdd(columnMap.CarepatronFieldName, value);
    }

    private void HandleEthnicityField(
        Dictionary<string, string> row,
        Dictionary<string, dynamic> mapCarepatronFieldToSpreadsheetValue,
        ImportContactsOption columnMap
    )
    {
        var value = row[columnMap.SpreadsheetFieldName];

        // if cell value is empty then set empty array
        // if there's a delimiter then use it to split the cell (and trim)
        // otherwise if no delimiter then just use that value
        var ethnicities = !string.IsNullOrWhiteSpace(value)
            ? !string.IsNullOrWhiteSpace(columnMap.Delimiter)
                ? value.Split(columnMap.Delimiter).Select(x => x.Trim()).ToArray()
                : new[] { value }
            : Array.Empty<string>();

        // try to add the spreadsheet's column value using our Carepatron field name i.e FirstName
        mapCarepatronFieldToSpreadsheetValue.TryAdd(columnMap.CarepatronFieldName, ethnicities);
    }

    private void HandleBirthDateField(
        Dictionary<string, string> row,
        Dictionary<string, dynamic> mapCarepatronFieldToSpreadsheetValue,
        ImportContactsOption columnMap
    )
    {
        var birthDateStr = ValueOrNull(row[columnMap.SpreadsheetFieldName]);

        if (birthDateStr != null)
        {
            mapCarepatronFieldToSpreadsheetValue.TryAdd(
                columnMap.CarepatronFieldName,
                birthDateStr.TryDateOnlyParseWithFormat(
                    columnMap.DateFormat ?? DateFormatType.YYYYMMDD
                )
            );
        }
    }

    private void HandlePhoneNumberField(
        Dictionary<string, string> row,
        Dictionary<string, dynamic> mapCarepatronFieldToSpreadsheetValue,
        ImportContactsOption columnMap
    )
    {
        var phoneStr = ValueOrNull(row[columnMap.SpreadsheetFieldName]);

        if (!string.IsNullOrWhiteSpace(phoneStr))
        {
            mapCarepatronFieldToSpreadsheetValue.TryAdd(
                columnMap.CarepatronFieldName,
                phoneStr.TryCleanPhoneNumber(columnMap.CountryCode)
            );
        }
    }

    private void HandleStatusField(
        Dictionary<string, string> row,
        Dictionary<string, dynamic> mapCarepatronFieldToSpreadsheetValue,
        ImportContactsOption columnMap,
        Dictionary<string, OptionSetValue> statusesToImport,
        Dictionary<string, string> existingStatuses,
        string defaultContactStatus
    )
    {
        var status = ValueOrNull(row[columnMap.SpreadsheetFieldName]);

        if (!string.IsNullOrWhiteSpace(status))
        {
            if (existingStatuses.TryGetValue(status, out var existingStatusId))
            {
                status = existingStatusId;
            }
            else if (statusesToImport.TryGetValue(status, out var optionSetValue))
            {
                status = optionSetValue.Id;
            }
            else if (statusesToImport.Count < 10)
            {
                var statusId = Guid.NewGuid().ToString();
                statusesToImport.Add(
                    status,
                    new OptionSetValue()
                    {
                        ColorHex = "#4CAF50",
                        Id = statusId,
                        DisplayName = status,
                        GroupPath = ContactCoreSchema.StatusGroup.Active
                    }
                );
                status = statusId;
            }
            else
            {
                status = defaultContactStatus;
            }
        }
        else
        {
            status = defaultContactStatus;
        }

        mapCarepatronFieldToSpreadsheetValue.TryAdd(columnMap.CarepatronFieldName, status);
    }

    private void HandleOptionProperty(Dictionary<string, string> row,
        Dictionary<string, dynamic> mapCarepatronFieldToSpreadsheetValue,
        ImportContactsOption columnMap,
        OptionSetV2Property optionProperty)
    {
        var value = ValueOrNull(row[columnMap.SpreadsheetFieldName]);

        if (string.IsNullOrWhiteSpace(value)) return;

        var option = optionProperty.Options.Values
            .FirstOrDefault(x => x.Id.Equals(value.Trim(), StringComparison.OrdinalIgnoreCase)
                                 || x.DisplayName.Equals(value.Trim(), StringComparison.OrdinalIgnoreCase));

        if (option is null) return;

        mapCarepatronFieldToSpreadsheetValue.TryAdd(columnMap.CarepatronFieldName, option);
    }

    private void HandleEmailField(Dictionary<string, string> row,
        Dictionary<string, dynamic> mapCarepatronFieldToSpreadsheetValue,
        ImportContactsOption columnMap)
    {
        var email = row[columnMap.SpreadsheetFieldName]?.Trim();

        // only add valid emails
        if (IsValidEmail(email))
        {
            mapCarepatronFieldToSpreadsheetValue.TryAdd(
                columnMap.CarepatronFieldName,
                ValueOrNull(email)
            );
        }
    }

    private void HandleSystemField(
        Dictionary<string, string> row,
        Dictionary<string, dynamic> mapCarepatronFieldToSpreadsheetValue,
        ImportContactsOption columnMap
    )
    {
        // try to add the spreadsheet's column value using our Carepatron field name i.e FirstName
        mapCarepatronFieldToSpreadsheetValue.TryAdd(
            columnMap.CarepatronFieldName,
            ValueOrNull(row[columnMap.SpreadsheetFieldName])
        );
    }

    private void HandleCustomField(
        Dictionary<string, string> row,
        Dictionary<string, dynamic> customFields,
        ImportContactsOption columnMap,
        MergedDataSchema schema
    )
    {
        // for backwards compatibility, if the field name is not found in the visible fields, check on schema v2
        // for this update, we will drop the api call for schema v1 to sync to schema v2, instead we will make the update directly to schema v2
        var actualFieldName = schema.GetProperty(columnMap.CarepatronFieldName) is not null
            ? columnMap.CarepatronFieldName
            : null;

        if (string.IsNullOrWhiteSpace(actualFieldName))
            return;

        customFields.TryAdd(actualFieldName, ValueOrNull(row[columnMap.SpreadsheetFieldName]));
    }

    private void HandleCustomField(Dictionary<string, string> row,
        Dictionary<string, dynamic> customFields,
        ImportContactsOption columnMap,
        Property property)
    {
        var rowValue = ValueOrNull(row[columnMap.SpreadsheetFieldName]);

        if (rowValue is null)
        {
            customFields.TryAdd(columnMap.CarepatronFieldName, null);
            return;
        }
        
        switch (property.Type)
        {
            case PropertyType.String:
            case PropertyType.Phone:
                customFields.TryAdd(columnMap.CarepatronFieldName, rowValue);
                break;
            case PropertyType.Number:
                if (int.TryParse(rowValue, out var number))
                    customFields.TryAdd(columnMap.CarepatronFieldName, number);
                else
                    customFields.TryAdd(columnMap.CarepatronFieldName, null);
                break;
            case PropertyType.Boolean:
                var booleanProperty = property as BooleanProperty;
                customFields.TryAdd(columnMap.CarepatronFieldName, ImportContactsUtilities.ParseBooleanValue(booleanProperty, rowValue));
                break;
            case PropertyType.Date:
                var valueAsDate = rowValue.TryDateOnlyParseWithFormat(columnMap.DateFormat ?? DateFormatType.YYYYMMDD);
                valueAsDate ??= (DateTime.TryParse(rowValue, out var date) ? date : null);
                customFields.TryAdd(columnMap.CarepatronFieldName, valueAsDate);
                break;
            case PropertyType.Email:
                var valueAsEmail = IsValidEmail(rowValue) ? new Email(rowValue) : null;
                customFields.TryAdd(columnMap.CarepatronFieldName, valueAsEmail);
                break;
            case PropertyType.Address:
                var valueAddress = string.IsNullOrEmpty(rowValue) ? null : new Address { StreetAddress = rowValue };
                customFields.TryAdd(columnMap.CarepatronFieldName, valueAddress);
                break;
            case PropertyType.OptionSet:
                var optionSetProperty = property as OptionSetV2Property;
                var valueAsOption = ImportContactsUtilities.GetOptionByIdOrName(optionSetProperty, rowValue);
                customFields.TryAdd(columnMap.CarepatronFieldName, valueAsOption);
                break;
        }
    }

    private async Task<(Tag[] TagsToAdd, Tag[] TagsToReactivate, Tag[] ExistingTags)> GetMappedTags(Guid providerId, Tag[] tags)
    {
        if (!tags.Any()) return ([], [], []);

        List<Tag> tagsToAdd = new();
        List<Tag> tagsReactivate = new();
        List<Tag> existingTags = new();

        var providerTags = await tagRepository.Get(providerId);

        if (!providerTags.Any()) return (tags, [], []);

        var tagsDictionary = providerTags.GroupBy(x => x.Title.ToLower()).ToDictionary(x => x.Key, x => x.ToList());
        foreach (var tag in tags)
        {
            if (tagsDictionary.TryGetValue(tag.Title.ToLower(), out var existingTagsList))
            {
                foreach (var existingTag in existingTagsList)
                {
                    if (existingTag.IsActive)
                    {
                        existingTags.Add(existingTag);
                    }
                    else
                    {
                        tagsReactivate.Add(existingTag);
                    }
                }
            }
            else
            {
                tagsToAdd.Add(tag);
            }
        }

        return (tagsToAdd.ToArray(), tagsReactivate.ToArray(), existingTags.ToArray());
    }

    public Tag[] SanitizeContactTags(Contact contact, Tag[] validTags)
    {
        if (contact.Tags.IsNullOrEmpty()) return [];

        var contactTags = contact.Tags.Select(x => x.Title.ToLower()).ToArray();

        return validTags.Where(x => contactTags.Contains(x.Title.ToLower()))
            .Distinct(x => x.Title.ToLower())
            .ToArray();
    }

    private static string ValueOrNull(string value)
    {
        return string.IsNullOrWhiteSpace(value?.Trim()) ? null : value.Trim();
    }

    private static bool IsValidEmail(string emailaddress)
    {
        try
        {
            MailAddress m = new MailAddress(emailaddress);

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<(List<string> Columns, List<Dictionary<string, string>> Rows, string CsvString)> ReadCsv(Guid fileId, string fileName, int maxRows)
    {
        List<string> columnsResult = new();
        List<Dictionary<string, string>> rowsResult = new();
        string csvContent = string.Empty;
        try
        {
            await using (var fileStream = await GetFileStream(fileId))
            {
                var (columns, rows) = SpreadsheetUtilities.ParseToContactImportRows(fileStream, fileName, maxRows);
                csvContent = SpreadsheetUtilities.BuildCsv(columns, rows);
                columnsResult = columns;
                rowsResult = rows;
            }

            if (string.IsNullOrEmpty(csvContent))
                Log.Warning("File content is empty");
        }
        catch (Exception e)
        {
            Log.Warning("Failed to read file content: {Error}", e.Message);
        }

        return (columnsResult, rowsResult, csvContent);
    }

    public async Task<(List<string> Columns, List<Dictionary<string, string>> Rows)> GetCsvData(Guid fileId, string fileName, int maxRows)
    {
        using var fileStream = await GetFileStream(fileId);
        return SpreadsheetUtilities.ParseToContactImportRows(fileStream, fileName, maxRows);
    }

    private async Task<MemoryStream> GetFileStream(Guid fileId)
    {
        await using var fileStream = await fileStorageRepository.GetObjectStream(fileId.ToString(), FileLocationType.ClientImport);

        if (fileStream is null)
        {
            Log.Warning("File stream is null");
            return null;
        }

        var memoryStream = new MemoryStream();
        await fileStream.CopyToAsync(memoryStream);
        memoryStream.Seek(0, SeekOrigin.Begin);

        return memoryStream;
    }
    
    private async Task<List<string>> GetAllFiles(Guid importSummaryId)
    {
        var allDirectories = new List<string>();
        string lastDirectory = null;

        do
        {
            var directories = await fileStorageRepository.ListPathFiles(importSummaryId.ToString(),
                1000,
                lastDirectory,
                FileLocationType.ClientImport,
                false);

            if (directories == null || directories.Length == 0)
            {
                break; // no more directories to fetch
            }

            allDirectories.AddRange(directories);

            // Update lastDirectory for next page
            lastDirectory = directories.Last();

        } while (true);

        return allDirectories;
    }
}